# curefit_x

A new flutter module project.

## Getting Started

For help getting started with Flutter, view our online
[documentation](https://flutter.dev/).

For instructions integrating Flutter modules to your existing applications,
see the [add-to-app documentation](https://flutter.dev/docs/development/add-to-app).


## Setting up flutter packages
- Install python3 and pyyaml package. Python is installed in macOS by default. Use your package manager to install pyyaml. Eg. brew install pyyaml if you use homebrew.
- Clone `flutter-packages` repository parallel to curefit-mobile directory
- To use packages locally, run ` python3 update_pubspecs.py local`
- To use packages from git, run ` python3 update_pubspecs.py git`
- To sync all the published modules (master only) directly from local, run `python3 update_pubspecs.py sync`
- Update the versions in the file `flutter_package_versions.yaml`


## Fixing "No module named 'yaml'" Error
- Run ` source virtual_env.sh` to activate the virtual environment.
- Run ` pip3 install pyyaml`.
- Run ` python3 update_pubspecs.py local` again.
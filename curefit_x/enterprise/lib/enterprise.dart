library enterprise;

import 'package:common/network/client.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:enterprise/UI/activation_benefit/screens/activation_benefit_screen.dart';
import 'package:enterprise/UI/clp/screens/clp.dart';
import 'package:enterprise/blocs/activation_benefit/activation_benefit_bloc.dart';
import 'package:enterprise/blocs/activation_benefit/state.dart';
import 'package:enterprise/blocs/clp/clp_bloc.dart';
import 'package:enterprise/blocs/clp/state.dart';
import 'package:enterprise/network/activation_benefit_repository.dart';
import 'package:enterprise/network/zoom_meeting_deeplink_repository.dart';
import 'factory/widget_builder.dart' as enterprise_widget_builder;
import 'constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:common/network/clp/clp_repository.dart';

/// A Calculator.
class Calculator {
  /// Returns [value] plus 1.
  int addOne(int value) => value + 1;
}

class Enterprise {
  IWidgetBuilder get widgetBuilder {
    return enterprise_widget_builder.WidgetBuilder();
  }

  String _routeName(RouteNames routeName) {
    return '/${EnumToString.convertToString(routeName)}';
  }

  Map<String, WidgetBuilder> getRoutes(NetworkClient networkClient,
      Function onClose) {
    CLPClientRepository enterpriseClientRepository = CLPClientRepository(
        networkClient);

    ZoomMeetingDeepLinkRepository zoomMeetingDeepLinkRepository = ZoomMeetingDeepLinkRepository(networkClient);

    ActivationBenefitRepository activationBenefitRepository = ActivationBenefitRepository(networkClient);

    return {
      _routeName(RouteNames.enterpriseclp): (_) => BlocProvider(
        create: (context) => EnterpriseCLPBloc(EnterpriseCLPIdle(), enterpriseClientRepository, zoomMeetingDeepLinkRepository),
        child: const EnterpriseCLP(),
      ),
      _routeName(RouteNames.activationBenefit): (_) => BlocProvider(
        create: (context) => ActivationBenefitBloc(ActivationBenefitIdle(), activationBenefitRepository),
        child: const ActivationBenefitScreen(),
      )
    };
  }
}

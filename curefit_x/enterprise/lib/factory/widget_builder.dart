import 'package:common/ui/widgets/card/challenge_leaderboard_widget.dart';
import 'package:common/ui/widgets/catalog_classcard_widget.dart';
import 'package:enterprise/UI/clp/widgets/corp_explore_banner_widget.dart';
import 'package:enterprise/UI/clp/widgets/enterprise_interactive_class_widget.dart';
import 'package:flutter/material.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:common/ui/widgets/card/trial_card_widget.dart';
import 'package:common/ui/widgets/card/sku_card_widget.dart';
import 'package:common/ui/widgets/goal_based_challenge_widget.dart';

class WidgetBuilder extends IWidgetBuilder {
  @override
  Widget? buildWidget(payload, {BuilderInfo? builderInfo}) {
    if (payload['widgetType'] == null) return null;
    WidgetTypes? widgetType =
        EnumToString.fromString(WidgetTypes.values, payload['widgetType']);

    if (widgetType != null) {
      WidgetInfo widgetInfo = payload['widgetMetric'] != null
          ? WidgetInfo.fromJson(payload, widgetType)
          : WidgetInfo(
              widgetType: widgetType,
              widgetMetric: WidgetMetric(widgetName: "", widgetId: ""));

      switch (widgetType) {
        case WidgetTypes.TRIAL_CARD_WIDGET_V2:
          return TrialCardWidget(
              widgetData: TrialCardWidgetData.fromJson(
                  payload, widgetInfo, widgetType));
        case WidgetTypes.SKU_CARD_WIDGET_V2:
          return SkuCardWidget(
            widgetData:
                SkuCardWidgetData.fromJson(payload, widgetInfo, widgetType),
          );
        case WidgetTypes.CHALLENGE_LEADERBOARD_WIDGET:
          return ChallengeLeaderboardWidget(
            widgetData: ChallengeLeaderboardWidgetData.fromJson(
                payload, widgetInfo, widgetType),
          );
        case WidgetTypes.CORP_EXPLORE_BANNER_WIDGET:
          return CorpExploreBannerWidget(
              widgetData: CorpExploreBannerWidgetData.fromJson(
                  payload, widgetInfo, widgetType));
        case WidgetTypes.ENTERPRISE_DOCTOR_WIDGET:
          return CatalogClassCardCarouselWrapperWidget(
              widgetData: CatalogClassCardCarouselWrapperWidgetData.fromJson(
                  payload, widgetInfo, widgetType));
        case WidgetTypes.ENTERPRISE_INTERACTIVE_CLASS_WIDGET:
          return EnterpriseInteractiveClassWidget(
              widgetData: EnterpriseInteractiveClassWidgetData.fromJson(
                  payload, widgetInfo, widgetType));
        case WidgetTypes.GOAL_BASED_CHALLENGE_WIDGET:
          return GoalBasedChallengeWidget(
              widgetData: GoalBasedChallengeWidgetData.fromJson(
                  payload, widgetInfo, widgetType));
        default:
          return null;
      }
    }
    return null;
  }
}

import 'package:common/action/action_handler.dart';
import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/ui/atoms/floating_button.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/page/basic_page_container.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/widget_factory.dart';
import 'package:common/util/action_util.dart';
import 'package:enterprise/blocs/activation_benefit/activation_benefit_bloc.dart';
import 'package:enterprise/blocs/activation_benefit/events.dart';
import 'package:enterprise/blocs/activation_benefit/models.dart';
import 'package:enterprise/blocs/activation_benefit/state.dart';
import 'package:enterprise/constants/constants.dart';
import 'package:flutter/material.dart' hide Action;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';

class ActivationBenefitScreen extends StatefulWidget {
  const ActivationBenefitScreen({Key? key}) : super(key: key);

  @override
  ActivationBenefitScreenState createState() => ActivationBenefitScreenState();
}

class ActivationBenefitScreenState extends State<ActivationBenefitScreen>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  logPageClose() {
    AnalyticsRepository analyticsRepository =
        RepositoryProvider.of<AnalyticsRepository>(context);
    analyticsRepository.logPageViewEvent(
        pageId: EnumToString.convertToString(RouteNames.activationBenefit),
        eventInfo: {});
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    switch (state) {
      case AppLifecycleState.resumed:
        refresh(context: context);
        break;
      case AppLifecycleState.inactive:
        break;
      case AppLifecycleState.paused:
        break;
      case AppLifecycleState.detached:
        break;
    }
  }

  refresh({required BuildContext context, bool showLoader = false}) {
    final activationBenefitBloc =
        BlocProvider.of<ActivationBenefitBloc>(context);
    activationBenefitBloc
        .add(LoadActivationBenefitEvent(showLoader: showLoader, params: {}));
  }

  ActivationBenefitScreenArguments? getScreenArguments() {
    final ScreenArguments? args =
        ModalRoute.of(context)!.settings.arguments != null
            ? ModalRoute.of(context)!.settings.arguments as ScreenArguments
            : null;
    if (args != null) {
      return ActivationBenefitScreenArguments(args.params);
    }
    return null;
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      AuroraThemeData themeData = AuroraTheme.of(context);
      themeData.canvasTheme = CanvasTheme.CLASSIC;
      themeData.backgroundTheme = themeColor;
      ActivationBenefitScreenArguments? benefitScreenArguments =
          getScreenArguments();
      if (benefitScreenArguments != null &&
          benefitScreenArguments.activationBenefitData != null) {
        final activationBenefitBloc =
            BlocProvider.of<ActivationBenefitBloc>(context);
        activationBenefitBloc.add(LoadActivationBenefitFromRouteEvent(
            benefitScreenArguments.activationBenefitData!));
      } else {
        refresh(context: context, showLoader: true);
      }
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  Widget buildWidgets() {
    return BlocBuilder<ActivationBenefitBloc, ActivationBenefitState>(
      builder: (context, state) {
        ActivationBenefitData? activationBenefitData;
        if (state is ActivationBenefitLoaded) {
          activationBenefitData = state.activationBenefit;
        } else if (state is ActivationBenefitLoading) {
          activationBenefitData = state.activationBenefit;
        }
        return Scaffold(
          extendBodyBehindAppBar: true,
          body: Stack(
            children: [
              Positioned.fill(
                  child: BasicPageContainer(
                    canvasTheme: CanvasTheme.CLASSIC,
                footerPadding: activationBenefitData?.action != null
                    ? const EdgeInsets.only(bottom: 100, top: 50)
                    : EdgeInsets.zero,
                requiresVideoCleanup: false,
                enableFloatingCTAAnimation: false,
                floatingCTA: activationBenefitData?.action != null
                    ? FloatingButton(
                        onPress: () {
                          Navigator.pop(context);
                          clickActionWithAnalytics(
                              Action.fromJson(activationBenefitData?.action),
                              context,
                              null, {});
                        },
                        buttonText: activationBenefitData?.action['title'],
                        titleText: activationBenefitData?.action['subTitle'],
                      )
                    : null,
                showTitleBar: false,
                showLoader: state is ActivationBenefitLoading,
                widgetData: activationBenefitData?.widgets ?? [],
              ))
            ],
          ),
        );
      },
    );
  }

  Widget buildContent(context) {
    return BlocBuilder<ActivationBenefitBloc, ActivationBenefitState>(
      builder: (context, state) {
        ActivationBenefitData? activationBenefitData;
        if (state is ActivationBenefitLoaded) {
          activationBenefitData = state.activationBenefit;
        } else if (state is ActivationBenefitLoading) {
          activationBenefitData = state.activationBenefit;
        }
        WidgetFactory widgetFactory =
            RepositoryProvider.of<WidgetFactory>(context);
        ListView listView = ListView.builder(
            padding: activationBenefitData?.action != null
                ? const EdgeInsets.only(bottom: 100, top: 70)
                : EdgeInsets.zero,
            itemCount: activationBenefitData?.widgets.length ?? 0,
            itemBuilder: (context, index) => widgetFactory
                .createWidget(activationBenefitData?.widgets[index]));
        return Positioned.fill(child: AnimationLimiter(child: listView));
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        child: Scaffold(
          backgroundColor: Colors.black,
          appBar: AppBar(
            leading: Container(),
            actions: [
              CloseButton(
                color: Colors.white,
                onPressed: () {
                  if (Navigator.canPop(context)) {
                    Navigator.popUntil(context, (route) {
                      return route.settings.name ==
                          '/${EnumToString.convertToString(RouteNames.enterpriseclp)}';
                    });
                  } else {
                    ActionBloc actionBloc =
                        BlocProvider.of<ActionBloc>(context);
                    actionBloc.add(CloseApplicationEvent());
                  }
                },
              )
            ],
          ),
          extendBodyBehindAppBar: true,
          body: SafeArea(
            top: false,
            left: false,
            right: false,
            child: buildWidgets(),
          ),
        ),
        onWillPop: () async {
          logPageClose();
          return true;
        });
  }
}

class ActivationBenefitScreenArguments {
  Map<String, dynamic>? activationBenefitData;

  ActivationBenefitScreenArguments(Map<String, dynamic> params) {
    if (params['activationBenefitData'] != null) {
      activationBenefitData = params['activationBenefitData'];
    } else {
      activationBenefitData = null;
    }
  }
}

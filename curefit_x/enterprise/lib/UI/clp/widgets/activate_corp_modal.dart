import 'package:common/action/action_handler.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/blocs/city/update_city_bloc.dart';
import 'package:common/constants/action_constants.dart';
import 'package:common/font/cf_icons.dart';
import 'package:common/model/city/model.dart';
import 'package:common/network/client.dart';
import 'package:common/ui/alert_view.dart';
import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/screens/web_view.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/user/user_repository.dart';
import 'package:common/util/color.dart';
import 'package:common/util/util.dart';
import 'package:enterprise/blocs/corp_activation/corp_activation_bloc.dart';
import 'package:enterprise/blocs/corp_activation/events.dart';
import 'package:enterprise/blocs/corp_activation/models.dart';
import 'package:enterprise/blocs/corp_activation/state.dart';
import 'package:enterprise/constants/constants.dart';
import 'package:flutter/material.dart' hide Action;
import 'package:pin_code_fields/pin_code_fields.dart';
import 'package:flutter_svg/svg.dart';
import 'package:common/ui/atoms/tertiary_button.dart';
import 'package:enterprise/network/corp_activation_repository.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:enterprise/network/activation_benefit_repository.dart';

enum ActivateCORPViewType {
  EMAIL_VOUCHER_FORM_VIEW,
  OTP_VERIFY_VIEW,
  MESSAGE_VIEW,
}

class ActivateCorpModal extends StatefulWidget {
  final String title;

  const ActivateCorpModal({Key? key, this.title = ""}) : super(key: key);

  @override
  _ActivateCorpModalState createState() => _ActivateCorpModalState();
}

class _ActivateCorpModalState extends State<ActivateCorpModal> {
  late TextEditingController _emailController;
  late TextEditingController _phoneController;
  late String _txtEmail = "";
  String? _txtEmailError;
  late String _txtOTPCode = "";
  late String _txtPhone = "";
  String? _txtPhoneError;
  num _resendOTPTimeout = 10;
  late bool _isMobileNoVerified = false;
  City? selectedCity;

  ActivateCORPViewType viewType = ActivateCORPViewType.EMAIL_VOUCHER_FORM_VIEW;
  String otpVerifyType = "";
  Future? _futureDelay;

  @override
  void initState() {
    super.initState();
    _emailController = TextEditingController();
    _phoneController = TextEditingController();
    checkMobileNoVerified();
  }

  @override
  void dispose() {
    _emailController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  void checkMobileNoVerified() {
    final UserInfo? userInfo =
    RepositoryProvider.of<UserRepository>(context).getUserInfo();
    if (userInfo != null && userInfo.phoneNumber != null) {
      setState(() {
        _isMobileNoVerified = true;
      });
    }
  }

  void _checkResendTimeout() {
    if (_resendOTPTimeout > 1) {
      _futureDelay = Future.delayed(const Duration(milliseconds: 1000), () {
        if (mounted) {
          setState(() {
            _resendOTPTimeout = _resendOTPTimeout - 1;
          });
          _checkResendTimeout();
        }
      });
    }
  }

  String _formatResendTime() {
    if (_resendOTPTimeout > 9) {
      return "00:$_resendOTPTimeout";
    }
    return "00:0$_resendOTPTimeout";
  }

  void _showToastMsg(String message, BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(SnackBar(
      content: Text(message, style: AuroraTheme.of(context).textStyle(TypescaleValues.P1),textAlign: TextAlign.center,),
    ));
  }

  void _btnActivePress(context) {
    FocusManager.instance.primaryFocus?.unfocus();
    if (_txtEmail.isNotEmpty && _txtPhone.isNotEmpty) {
      showErrorAlert(
          context: context,
          title:
              "Please enter either phone number or email / voucher code for activate cultpass corp");
    } else if (_txtEmail.isNotEmpty) {
      if (!isValidEmail(_txtEmail)) {
        final corpActivationBloc = BlocProvider.of<CorpActivationBloc>(context);
        final RedeemVoucherCodeReqParam params =
        RedeemVoucherCodeReqParam(_txtEmail, selectedCity?.cityId);
        corpActivationBloc
            .add(RedeemVoucherCodeEvent(params: params, showLoader: true));
      } else {
        final corpActivationBloc = BlocProvider.of<CorpActivationBloc>(context);
        final SetEmailSendOTPReqParam params =
            SetEmailSendOTPReqParam(_txtEmail, "WORK");
        corpActivationBloc.add(CheckTNCEnabledForWorkEmailEvent(email: _txtEmail.substring(_txtEmail.indexOf("@") + 1)));
        // corpActivationBloc
        //     .add(SetEmailSendOTPEvent(params: params, showLoader: true));
      }
    } else if (_txtPhone.isNotEmpty) {
      if(!isValidPhone(_txtPhone)) {
        setState(() {
          _txtPhoneError = 'Please enter a valid phone number';
        });
        return;
      } else {
        final corpActivationBloc = BlocProvider.of<CorpActivationBloc>(context);
        final SetPhoneSendOTPReqParam params = SetPhoneSendOTPReqParam(_txtPhone, "sms", "+91");
        corpActivationBloc.add(SetPhoneSendOTPEvent(params: params, showLoader: true));
      }
    }
  }

  void _btnGoBackPress(context) {
    final corpActivationBloc = BlocProvider.of<CorpActivationBloc>(context);
    corpActivationBloc.add(ResetCorpActivationEvent());
  }

  void _verifyOTPAction(context) {
    final corpActivationBloc = BlocProvider.of<CorpActivationBloc>(context);
    if (otpVerifyType == "phone") {
      corpActivationBloc.add(VerifyPhoneThroughOTPEvent(params: VerifyPhoneThroughOTPReqParam(_txtOTPCode)));
    } else {
      corpActivationBloc.add(VerifyEmailThroughOTPEvent(
          params: VerifyEmailThroughOTPReqParam(_txtEmail, "WORK", _txtOTPCode, selectedCity?.cityId)));
    }
  }

  Widget _buildTextField(
      context, controller, title, hintText, errorMsg, onChanged, onTap,
      {keyboardType = TextInputType.text, maxLength}) {
    return Container(
      decoration: const BoxDecoration(color: Colors.white12),
      child: Padding(
        padding: const EdgeInsets.only(
            left: Spacings.x3,
            right: Spacings.x3,
            top: Spacings.x2,
            bottom: Spacings.x1),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextField(
              style: AuroraTheme.of(context)
                  .textStyle(TypescaleValues.P5, color: Colors.white),
              controller: controller,
              cursorColor: Colors.white,
              keyboardType: keyboardType,
              onChanged: onChanged,
              onTap: onTap,
              maxLength: maxLength,
              decoration: InputDecoration(
                focusColor: Colors.white,
                hintStyle: AuroraTheme.of(context).textStyle(TypescaleValues.P5,
                    color: Colors.white.withOpacity(0.6)),
                border: InputBorder.none,
                hintText: hintText,
                floatingLabelBehavior: FloatingLabelBehavior.always,
                labelText: title,
                errorText: errorMsg,
                labelStyle: AuroraTheme.of(context).textStyle(
                    TypescaleValues.P5,
                    color: Colors.white.withOpacity(0.40)),
                alignLabelWithHint: true,
                contentPadding: EdgeInsets.zero,
                counterText: ""
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _buildResendOTP(context) {
    if (_resendOTPTimeout > 1) {
      return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            "Didn’t receive OTP?",
            style: AuroraTheme.of(context).textStyle(TypescaleValues.P8, color: Colors.white60),
          ),
          const SizedBox(width: Spacings.x2),
          Text(
            "Resend OTP in ${_formatResendTime()}",
            style: AuroraTheme.of(context).textStyle(TypescaleValues.P6),
          )
        ],
      );
    }
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text("Resend OTP via",
            style: AuroraTheme.of(context)
                .textStyle(TypescaleValues.P8, color: Colors.white60)),
        const SizedBox(width: Spacings.x1),
        TertiaryButton(
          () {
            final corpActivationBloc =
                BlocProvider.of<CorpActivationBloc>(context);

            if(otpVerifyType == "phone") {
              final corpActivationBloc = BlocProvider.of<CorpActivationBloc>(context);
              final SetPhoneSendOTPReqParam params = SetPhoneSendOTPReqParam(_txtPhone, "sms", "+91");
              corpActivationBloc.add(SetPhoneSendOTPEvent(params: params, showLoader: true, isResend: true));
            } else {
              final SetEmailSendOTPReqParam params =
              SetEmailSendOTPReqParam(_txtEmail, "WORK");
              corpActivationBloc.add(SetEmailSendOTPEvent(
                  params: params, showLoader: true, isResend: true));
            }
          },
          otpVerifyType == "phone" ? "SMS" : "EMAIL",
          expanded: false,
          buttonType: TertiaryButtonType.SMALL,
          iconData: CFIcons.message,
        ),
      ],
    );
  }

  Widget _buildOTPField(context, haveError) {
    const normalColor = Colors.white10;
    Color borderErrorColor = HexColor.fromHex("#FF5942");
    return PinCodeTextField(
      appContext: context,
      length: 6,
      animationType: AnimationType.fade,
      onChanged: (value) {
        setState(() {
          _txtOTPCode = value;
        });
      },
      onCompleted: (value) {
        setState(() {
          _txtOTPCode = value;
        });
        _verifyOTPAction(context);
      },
      cursorColor: Colors.white,
      enableActiveFill: true,
      pinTheme: PinTheme(
          fieldWidth: 44,
          fieldHeight: 44,
          shape: PinCodeFieldShape.box,
          activeFillColor: normalColor,
          activeColor: haveError ? borderErrorColor : Colors.transparent,
          inactiveColor: haveError ? borderErrorColor : Colors.transparent,
          inactiveFillColor: normalColor,
          selectedColor: Colors.transparent,
          borderWidth: 1,
          borderRadius: const BorderRadius.all(Radius.circular(Spacings.x1)),
          selectedFillColor: normalColor),
      onTap: () {
        final corpActivationBloc = BlocProvider.of<CorpActivationBloc>(context);
        corpActivationBloc.add(ResetCorpActivationOTPErrorEvent());
      },
      textStyle: AuroraTheme.of(context).textStyle(TypescaleValues.P2),
      keyboardType: TextInputType.number,
    );
  }

  Widget _buildMessageView(context, message) {
    return Column(
      children: [
        Padding(
            padding: const EdgeInsets.symmetric(vertical: Spacings.x4),
            child: Transform.translate(
                offset: const Offset(0, 0),
                child: SvgPicture.asset(
                  'assets/cross_red.svg',
                  width: 55,
                  height: 55,
                  fit: BoxFit.fitWidth,
                ))),
        Text(
          message,
          textAlign: TextAlign.center,
          style: AuroraTheme.of(context)
              .textStyle(TypescaleValues.P2, color: Colors.white60),
        ),
        Padding(
          padding: const EdgeInsets.only(top: Spacings.x8, bottom: Spacings.x4),
          child: PrimaryButton(
            () => _btnGoBackPress(context),
            "GO BACK",
            enabled: true,
          ),
        )
      ],
    );
  }

  Widget _buildOTPVerificationForm(context, wrongOTPError) {
    return Column(
      children: [
        Text(
          "Enter the code sent to",
          style: AuroraTheme.of(context).textStyle(TypescaleValues.P5),
        ),
        Text(
          otpVerifyType == "phone" ? "+91 $_txtPhone" : _txtEmail,
          style: AuroraTheme.of(context).textStyle(TypescaleValues.H2),
        ),
        if (wrongOTPError != null)
          Padding(
              padding: const EdgeInsets.only(top: Spacings.x8),
              child: Text(wrongOTPError,
                  style: AuroraTheme.of(context).textStyle(TypescaleValues.P8,
                      color: const Color(0XFFFF5942)))),
        Padding(
            padding: EdgeInsets.only(
                top: wrongOTPError != null ? Spacings.x2 : Spacings.x8),
            child: _buildOTPField(context, wrongOTPError != null)),
        _buildResendOTP(context),
        Padding(
          padding: const EdgeInsets.only(top: Spacings.x8, bottom: Spacings.x4),
          child: PrimaryButton(
            () {
              _verifyOTPAction(context);
            },
            "CONTINUE",
            enabled: _txtOTPCode.isNotEmpty && _txtOTPCode.length == 6,
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(bottom: Spacings.x4),
          child: TertiaryButton(
            () {
              Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (BuildContext context) => TFWebView(
                          "https://support.cure.fit/support/solutions/articles/25000025141-i-m-having-trouble-logging-into-the-app-")));
            },
            "Trouble logging in?".toUpperCase(),
            expanded: false,
          ),
        )
      ],
    );
  }

  Widget _buildEmailVoucherForm(context) {
    var enableButton = (_txtEmail.isNotEmpty || _txtPhone.isNotEmpty) && selectedCity != null;
    return Column(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Text(
          "Activate cultpass CORP",
          style: AuroraTheme.of(context).textStyle(TypescaleValues.H9),
        ),
        const SizedBox(
          height: Spacings.x8,
        ),
        if (!_isMobileNoVerified)
          _buildTextField(context, _phoneController, "Phone Number",
              "Enter Phone Number", _txtPhoneError, (value) {
            setState(() {
              _txtPhone = value;
              _txtPhoneError = null;
            });
          }, () {
            setState(() {
              _txtPhoneError = null;
            });
          }, keyboardType: TextInputType.phone, maxLength: 10),
        if(!_isMobileNoVerified)
          Padding(
            padding: const EdgeInsets.symmetric(vertical: Spacings.x2),
            child: Text("Or",
                style: AuroraTheme.of(context).textStyle(TypescaleValues.P1)),
          ),
        _buildTextField(context, _emailController, "Company Email / Voucher code",
            "Enter Email / Voucher", _txtEmailError, (value) {
              setState(() {
                _txtEmail = value;
                _txtEmailError = null;
              });
            }, () {
              setState(() {
                _txtEmailError = null;
              });
            }, keyboardType: TextInputType.text),
        const SizedBox(height: Spacings.x8),
        _buildCitySelectionView(context, "Select your city", "City"),
        const SizedBox(height: Spacings.x8),
        PrimaryButton(
          () => _btnActivePress(context),
          "ACTIVATE NOW",
          enabled: enableButton,
        ),
        const SizedBox(height: Spacings.x4),
      ],
    );
  }

  Widget _buildCitySelectionView(context, hintText, title) {
    ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
    Action showChangeCityAction = Action(type: ActionTypes.SHOW_CHANGE_CITY);
    return Container(
      decoration: const BoxDecoration(color: Colors.white12),
      child: Padding(
        padding: const EdgeInsets.only(
            left: Spacings.x3,
            right: Spacings.x3,
            top: Spacings.x2,
            bottom: Spacings.x1),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
              TextField(
                style: AuroraTheme.of(context)
                    .textStyle(TypescaleValues.P5, color: Colors.white),
                cursorColor: Colors.white,
                readOnly: true,
                onTap: () {
                  actionBloc.add(PerformActionEvent(showChangeCityAction));
                },
                decoration: InputDecoration(
                  focusColor: Colors.white,
                  hintStyle: AuroraTheme.of(context).textStyle(TypescaleValues.P5,
                      color: Colors.white.withOpacity(0.6)),
                  border: InputBorder.none,
                  hintText: selectedCity?.name ?? hintText,
                  floatingLabelBehavior: FloatingLabelBehavior.always,
                  labelText: title,
                  labelStyle: AuroraTheme.of(context).textStyle(
                      TypescaleValues.P5,
                      color: Colors.white.withOpacity(0.40)),
                  alignLabelWithHint: true,
                  contentPadding: EdgeInsets.zero,
                  counterText: ""
                )
              )],
        ),
      ),
    );
  }

  Widget _buildView(context, state) {
    switch (viewType) {
      case ActivateCORPViewType.MESSAGE_VIEW:
        return _buildMessageView(context, state.message);
      case ActivateCORPViewType.EMAIL_VOUCHER_FORM_VIEW:
        return _buildEmailVoucherForm(context);
      case ActivateCORPViewType.OTP_VERIFY_VIEW:
        return _buildOTPVerificationForm(context,
            state is CorpActivationOTPError ? state.wrongOTPError : null);
      default:
        return Container();
    }
  }

  Widget _blocBuilder(context, state) {
    return Padding(
      padding: const EdgeInsets.only(
          left: Spacings.x4,
          right: Spacings.x4,
          bottom: Spacings.x4,
          top: Spacings.x2),
      child: Stack(
        alignment: Alignment.center,
        children: [
          Column(
            children: [
              getDashView(Colors.white60, context),
              const SizedBox(height: Spacings.x5),
              _buildView(context, state),
            ],
          ),
          if (state is CorpActivationLoading)
            const FancyLoadingIndicator()
        ],
      ),
    );
  }

  void _blocListener(context,state) {
    if (state is CorpActivationIdle) {
      setState(() {
        viewType = ActivateCORPViewType.EMAIL_VOUCHER_FORM_VIEW;
      });
    }

    if (state is CorpActivationShowOTPVerify) {
      setState(() {
        viewType = ActivateCORPViewType.OTP_VERIFY_VIEW;
        _resendOTPTimeout = 10;
        otpVerifyType = state.verifyFor;
      });
      _checkResendTimeout();
    }

    if(state is CorpActivationResendOTPSuccess) {
      setState(() {
        _resendOTPTimeout = 10;
      });
      _checkResendTimeout();
    }

    if (state is CorpActivationShowMessage) {
      setState(() {
        viewType = ActivateCORPViewType.MESSAGE_VIEW;
      });
    }

    if (state is CorpActivationError && state.errorMsg != null) {
      _showToastMsg(state.errorMsg!, context);
    }

    if (state is CorpActivationOTPVerifySuccess) {
      final corpActivationBloc =
      BlocProvider.of<CorpActivationBloc>(context);
      corpActivationBloc.add(CheckCorpBenefitAssociatedEvent(
          activationByType: otpVerifyType == "phone" ? CorpActivationByType.PHONE : CorpActivationByType.EMAIL));
    }

    if (state is CorpVoucherRedeemSuccess) {
      final corpActivationBloc =
      BlocProvider.of<CorpActivationBloc>(context);
      corpActivationBloc.add(CheckCorpBenefitAssociatedEvent(
          activationByType: CorpActivationByType.VOUCHER_CODE));
    }

    if (state is CorpBenefitAssociatedSuccess) {
      Future.delayed(const Duration(milliseconds: 250), () {
        if (Navigator.canPop(context)) {
          Navigator.pop(context);
        }
        Navigator.pushNamed(context,
            '/${EnumToString.convertToString(RouteNames.activationBenefit)}',
            arguments: ScreenArguments(
                {"activationBenefitData": state.activationBenefitData}));
      });
    }

    if (state is ShowEnterpriseTNC) {
      Action action = Action(type: ActionTypes.SET_EMAIL_SEND_OTP, meta: {
        "email": _txtEmail,
      });
      BlocProvider.of<ActionBloc>(context).add(
          PerformActionEvent(Action(type: ActionTypes.NAVIGATION, url: "curefit://enterprise_tnc", meta: {
            "email": _txtEmail.substring(_txtEmail.indexOf("@") + 1),
            "action": action,
          })));
    }

    if (state is SetEmailOTPState) {
      final corpActivationBloc = BlocProvider.of<CorpActivationBloc>(context);
      final SetEmailSendOTPReqParam params = SetEmailSendOTPReqParam(_txtEmail, "WORK");
      corpActivationBloc
          .add(SetEmailSendOTPEvent(params: params, showLoader: true));
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<UpdateCityBloc, UpdateCityState> (
      listener: (context, state) {
        if (state is UpdateCitySuccessful) {
          setState(() {
            selectedCity = state.city;
          });
        }
      },
      child: BlocConsumer<CorpActivationBloc, CorpActivationState>(
        builder: _blocBuilder,
        listener: _blocListener,
      ),
    );
  }
}

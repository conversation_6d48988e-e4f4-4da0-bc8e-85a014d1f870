import 'package:common/action/action_handler.dart';
import 'package:common/ui/header/card_header.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:common/util/action_util.dart';
import 'package:flutter/cupertino.dart' hide Action;
import 'package:common/ui/molecules/upcoming_class_card.dart';
import 'package:enum_to_string/enum_to_string.dart';

class EnterpriseInteractiveClassWidgetData implements IWidgetData {
  @override
  WidgetInfo? widgetInfo;
  @override
  WidgetTypes widgetType;
  WidgetHeaderData? header;
  List<UpcomingClassData> data;

  EnterpriseInteractiveClassWidgetData(
      {this.widgetInfo,
      required this.widgetType,
      required this.data,
      this.header});

  factory EnterpriseInteractiveClassWidgetData.fromJson(
      dynamic widgetData, WidgetInfo widgetInfo, WidgetTypes widgetType) {
    List<UpcomingClassData> cards = [];
    if (widgetData["data"] != null) {
      List<dynamic> dataList = widgetData["data"];
      if (dataList.isNotEmpty) {
        cards = List.generate(dataList.length, (index) {
          dynamic item = dataList[index];
          UpcomingClassStatus? status = EnumToString.fromString(
              UpcomingClassStatus.values, item["meetingStatus"]);

          return UpcomingClassData(
              title: item["title"],
              description: item["description"],
              status: status,
              classTime: item["classStartTime"],
              rightImage: item["rightImage"],
              rightAction: item["rightAction"] != null
                  ? Action.fromJson(item["rightAction"])
                  : null,
              note: item["corporateCaption"]);
        });
      }
    }
    return EnterpriseInteractiveClassWidgetData(
        widgetType: widgetType,
        data: cards,
        header: widgetData["header"] != null
            ? WidgetHeaderData.fromJson(widgetData['header'])
            : null);
  }
}

class EnterpriseInteractiveClassWidget extends StatelessWidget {
  final EnterpriseInteractiveClassWidgetData widgetData;

  const EnterpriseInteractiveClassWidget({required this.widgetData});

  void performClickAction(Action action, BuildContext context) {
    clickActionWithAnalytics(action, context, widgetData.widgetInfo, {});
  }

  @override
  Widget build(BuildContext context) {
    return Column(children: [
      if (widgetData.header != null)
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: Spacings.x4),
          child: WidgetHeader(
            cardHeaderData: widgetData.header!,
            onActionPress: (Action action) {
              performClickAction(action, context);
            },
          ),
        ),
      SizedBox(height: widgetData.header != null ? Spacings.x1 : 0),
      SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: Spacings.x4),
          child: Column(
            children: widgetData.data
                .map((e) => Padding(
                      padding: const EdgeInsets.only(top: Spacings.x3),
                      child: UpcomingClassCard(
                        data: e,
                        onPress: (Action action) {
                          performClickAction(action, context);
                        },
                      ),
                    ))
                .toList(),
          ),
        ),
      )
    ]);
  }
}

import 'package:common/action/action_handler.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/atoms/secondary_button.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:common/util/action_util.dart';
import 'package:common/util/color.dart';
import 'package:flutter/material.dart' hide Action;

class CorpExploreBannerWidgetData implements IWidgetData {
  @override
  WidgetInfo? widgetInfo;
  @override
  WidgetTypes widgetType;
  String? title;
  String? subTitle;
  String? companyLogo;
  String? companyName;
  String leftImage;
  String? backgroundColor;
  String? ctaCaption;
  Action? action;

  CorpExploreBannerWidgetData(
      {this.widgetInfo,
        required this.widgetType,
        this.title,
        this.subTitle,
        this.companyLogo,
        this.companyName,
        required this.leftImage,
        this.backgroundColor,
        this.ctaCaption,
        this.action});

  factory CorpExploreBannerWidgetData.fromJson(
      dynamic widgetData, WidgetInfo widgetInfo, WidgetTypes widgetType) {
    return CorpExploreBannerWidgetData(
        widgetType: widgetType,
        widgetInfo: widgetInfo,
        leftImage: widgetData['leftImage'],
        title: widgetData['title'],
        subTitle: widgetData['subTitle'],
        companyLogo: widgetData['companyLogo'],
        companyName: widgetData['companyName'],
        backgroundColor: widgetData['backgroundColor'],
        ctaCaption: widgetData['ctaCaption'],
        action: widgetData['action'] != null
            ? Action.fromJson(widgetData['action'])
            : null);
  }
}

class CorpExploreBannerWidget extends StatelessWidget {
  final CorpExploreBannerWidgetData widgetData;

  const CorpExploreBannerWidget({Key? key, required this.widgetData})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    final cardWidth = scale(context, 335);
    final cardMinHeight = cardWidth / 1.9;
    final companyLogoWidth = scale(context, 80);
    final leftImageWidth = cardWidth * (135 / 335);
    final leftImageHeight = leftImageWidth * (171 / 135);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: Spacings.x4),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          enableFeedback: widgetData.action != null,
          borderRadius: const BorderRadius.all(Radius.circular(Spacings.x2)),
          onTap: () {
            if(widgetData.action != null){
              clickActionWithAnalytics(widgetData.action!, context, widgetData.widgetInfo, {});
            }
          },
          child: Container(
            constraints: BoxConstraints(minHeight: cardMinHeight),
            clipBehavior: Clip.hardEdge,
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.all(Radius.circular(Spacings.x2)),
              color: HexColor.fromHex(widgetData.backgroundColor ?? "#266630"),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                CFNetworkImage(
                  fit: BoxFit.cover,
                  width: leftImageWidth,
                  height: leftImageHeight,
                  imageUrl: getImageUrl(
                    context,
                    imagePath: widgetData.leftImage,
                    width: leftImageWidth.toInt(),
                  ),
                ),
                Container(
                  width: cardWidth - leftImageWidth,
                  constraints: BoxConstraints(minHeight: cardMinHeight),
                  padding: const EdgeInsets.symmetric(horizontal: Spacings.x1),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      const SizedBox(height: Spacings.x4),
                      if(widgetData.companyLogo != null && widgetData.companyLogo!.isNotEmpty)
                        CFNetworkImage(
                          fit: BoxFit.cover,
                          width: companyLogoWidth,
                          height: companyLogoWidth * 0.23,
                          imageUrl: getImageUrl(
                            context,
                            imagePath: widgetData.companyLogo,
                            width: companyLogoWidth.toInt(),
                          ),
                        )
                      else if (widgetData.companyName != null)
                        Text(widgetData.companyName!,
                            style: AuroraTheme.of(context)
                                .textStyle(TypescaleValues.H4),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,),
                      const SizedBox(height: Spacings.x4),
                      Text(
                        widgetData.title!,
                        style:
                        AuroraTheme.of(context).textStyle(TypescaleValues.H2),
                      ),
                      if (widgetData.subTitle != null)
                        Padding(
                            padding: const EdgeInsets.only(top: Spacings.x1),
                            child: Text(
                              widgetData.subTitle!,
                              maxLines: 3,
                              style: AuroraTheme.of(context)
                                  .textStyle(TypescaleValues.P8),
                            )),
                      if (widgetData.ctaCaption != null)
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: Spacings.x2),
                          child: SecondaryButton(
                                () {
                              if(widgetData.action != null){
                                clickActionWithAnalytics(widgetData.action!, context, widgetData.widgetInfo, {});
                              }
                            },
                            widgetData.ctaCaption!,
                            expanded: false,
                            buttonType: SecondaryButtonType.SMALL,
                            enabled: false,
                          ),
                        )
                    ],
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}

import 'package:common/action/action_handler.dart';
import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/blocs/navigation/navigation_bloc.dart';
import 'package:common/blocs/navigation/state.dart';
import 'package:common/constants/action_constants.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/alert_view.dart';
import 'package:common/ui/atoms/floating_button.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/page/basic_page_container.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:enterprise/blocs/clp/clp_bloc.dart';
import 'package:enterprise/blocs/clp/events.dart';
import 'package:enterprise/blocs/clp/models.dart';
import 'package:enterprise/blocs/clp/state.dart';
import 'package:enterprise/constants/constants.dart';
import 'package:flutter/material.dart' hide Action;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'dart:io';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:navigation_history_observer/navigation_history_observer.dart';
import 'package:scroll_to_index/scroll_to_index.dart';

class EnterpriseCLP extends StatefulWidget {
  const EnterpriseCLP({Key? key}) : super(key: key);

  @override
  _EnterpriseCLPState createState() => _EnterpriseCLPState();
}

class _EnterpriseCLPState extends State<EnterpriseCLP>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  EnterpriseCLPScreenArguments? enterpriseCLPScreenArguments;
  bool showingError = false;
  bool scrollToWidgetPerformed = false;
  bool zoomMeetingStartPerformed = false;
  late bool visible;

  logPageClose() {
    AnalyticsRepository analyticsRepository =
        RepositoryProvider.of<AnalyticsRepository>(context);
    analyticsRepository.logPageViewEvent(
        pageId: EnumToString.convertToString(RouteNames.enterpriseclp),
        eventInfo: {});
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (!visible) return;
    switch (state) {
      case AppLifecycleState.resumed:
        refresh(context: context);
        break;
      case AppLifecycleState.inactive:
        break;
      case AppLifecycleState.paused:
        break;
      case AppLifecycleState.detached:
        break;
    }
  }

  scrollToWidget() {
    ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
    Action action = Action(
        type: ActionTypes.SCROLL_TO_WIDGET,
        meta: {"widgetId": enterpriseCLPScreenArguments?.widgetId});
    actionBloc.add(PerformActionEvent(action));
  }

  refresh({required BuildContext context, bool showLoader = false}) {
    final enterpriseCLPBloc = BlocProvider.of<EnterpriseCLPBloc>(context);
    enterpriseCLPBloc.add(LoadEnterpriseCLPEvent(
        showLoader: showLoader, params: enterpriseCLPScreenArguments));
  }

  EnterpriseCLPScreenArguments? getScreenArguments() {
    final ScreenArguments? args =
        ModalRoute.of(context)!.settings.arguments as ScreenArguments?;
    if (args != null) {
      return EnterpriseCLPScreenArguments(args.params);
    }
    return null;
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    visible = true;
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      AuroraThemeData themeData = AuroraTheme.of(context);
      themeData.canvasTheme = CanvasTheme.CLASSIC;
      themeData.backgroundTheme = themeColor;
      enterpriseCLPScreenArguments = getScreenArguments();
      refresh(context: context, showLoader: true);
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  void blocListener(context, state) {
    if (state is EnterpriseCLPNotLoaded && state.error != null) {
      showingError = true;
      if (state.exception != null &&
          ((state.exception!.title ?? "").isNotEmpty ||
              (state.exception!.subTitle ?? "").isNotEmpty)) {
        showNetworkExceptionAlert(
            context: context,
            networkException: state.exception!,
            onPress: (Map<String, dynamic>? action) {
              if (action != null) {
                performAlertButtonAction(context, action);
              }
            });
      } else {
        showErrorAlert(context: context, title: state.error);
      }
    }

    if (state is EnterpriseCLPLoaded) {
      if (enterpriseCLPScreenArguments?.widgetId != null && scrollToWidgetPerformed == false) {
        scrollToWidgetPerformed = true;
        scrollToWidget();
      }
      EnterpriseCLPData? enterpriseCLPData = state.enterpriseCLP;
      if (zoomMeetingStartPerformed == false &&
          enterpriseCLPData.meetingDeepLinkAction != null) {
        zoomMeetingStartPerformed = true;
        if (enterpriseCLPData.meetingDeepLinkAction?["actionType"] ==
            "JOIN_ZOOM_MEETING") {
          ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
          PerformActionEvent event = PerformActionEvent(
              Action.fromJson(enterpriseCLPData.meetingDeepLinkAction));
          actionBloc.add(event);
        }
      }
    }
  }

  Widget blocBuilder(context, state) {
    EnterpriseCLPData? enterpriseClpData;
    if (state is EnterpriseCLPLoaded) {
      enterpriseClpData = state.enterpriseCLP;
      showingError = false;
    } else if (state is EnterpriseCLPLoading) {
      enterpriseClpData = state.enterpriseCLP;
    }
    return Scaffold(
      extendBodyBehindAppBar: true,
      body: Stack(
        children: [
          Positioned.fill(
              child: BasicPageContainer(
            canvasTheme: CanvasTheme.CLASSIC,
            footerPadding: enterpriseClpData?.action != null
                ? const EdgeInsets.only(bottom: 100)
                : EdgeInsets.zero,
            requiresVideoCleanup: true,
            enableFloatingCTAAnimation: false,
            autoScrollPosition: AutoScrollPosition.middle,
            floatingCTA: enterpriseClpData?.action != null
                ? Padding(
                    padding: EdgeInsets.only(
                        bottom: MediaQuery.of(context).viewPadding.bottom),
                    child: FloatingButton(
                      onPress: () {
                        ActionBloc actionBloc =
                            BlocProvider.of<ActionBloc>(context);
                        PerformActionEvent event =
                            PerformActionEvent(enterpriseClpData!.action);
                        actionBloc.add(event);
                      },
                      buttonText: enterpriseClpData?.action.title,
                      titleText: enterpriseClpData?.action?.meta?['subTitle'],
                    ),
                  )
                : null,
            onBackPressed: () {
              if (Navigator.canPop(context)) {
                Navigator.pop(context);
              } else if (!Navigator.canPop(context)) {
                ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
                actionBloc.add(CloseApplicationEvent(shouldReset: false));
              }
            },
            title: enterpriseClpData?.pageName ?? "",
            titleBarRightActions: [
              if (enterpriseClpData?.corpLogo != null &&
                  enterpriseClpData?.corpLogo != "")
                corpLogo(context, enterpriseClpData?.corpLogo)
              else if (enterpriseClpData?.corpName != null)
                corpName(context, enterpriseClpData?.corpName)
            ],
            showLoader: state is EnterpriseCLPLoading && state.showLoader,
            widgetData: enterpriseClpData?.widgets ?? [],
          ))
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        child: BlocListener<NavigationBloc, NavigationState>(
          listener: (context, state) {
            if (!showingError &&
                state is NavigationStackUpdated &&
                state.action == NavigationStackAction.pop &&
                state.route?.settings.name ==
                    '/${EnumToString.convertToString(RouteNames.enterpriseclp)}') {
              visible = true;
              refresh(context: context, showLoader: false);
            } else {
              visible = false;
            }
          },
          child: BlocConsumer<EnterpriseCLPBloc, EnterpriseCLPState>(
            builder: blocBuilder,
            listener: blocListener,
          ),
        ),
        onWillPop: () async {
          logPageClose();
          return true;
        });
  }

  Widget corpLogo(BuildContext context, String? corpLogo) {
    final topPadding = MediaQuery.of(context).viewPadding.top;
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(
              right: Spacings.x2,
              top: Platform.isIOS
                  ? topPadding <= 20
                      ? Spacings.x1
                      : 0
                  : Spacings.x3 + 2),
          child: SizedBox(
            height: scaleHeight(context, 20),
            width: scale(context, 90),
            child: CachedNetworkImage(
              fit: BoxFit.contain,
              imageUrl: getImageUrl(context,
                  imagePath: corpLogo ?? "", width: scale(context, 90).toInt()),
            ),
          ),
        )
      ],
    );
  }

  Widget corpName(BuildContext context, String? corpName) {
    final topPadding = MediaQuery.of(context).viewPadding.top;
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Container(
          padding: EdgeInsets.only(
              right: Spacings.x2,
              top: Platform.isIOS
                  ? topPadding <= 20
                      ? Spacings.x1 + 2
                      : Spacings.x1
                  : Spacings.x3 + 2),
          constraints: BoxConstraints(maxWidth: scale(context, 120)),
          child: Text(
            corpName!,
            style: AuroraTheme.of(context).textStyle(TypescaleValues.P3),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  void performAlertButtonAction(context, action) {
    ActionTypes? type = action['actionType'] != null
        ? EnumToString.fromString(ActionTypes.values, action['actionType'])
        : null;
    ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
    if (type != null) {
      // Action supported in flutter
      PerformActionEvent event = PerformActionEvent(Action.fromJson(action));
      actionBloc.add(event);
    } else if (action['actionType'] != "HIDE_ALERT_MODAL") {
      // Alert is showing in flutter so no need to send event in react native when actionType is HIDE_ALERT_MODAL
      actionBloc.add(UnsupportedActionEvent(Action.fromJson(action), false));
    }
  }
}

class EnterpriseCLPScreenArguments {
  String? pageName;
  String? pageId;
  String? widgetId;
  bool? isEnterpriseMeetingLink;
  String? meetingJoinUrl;
  String? externalMeetingId;
  String? meetingId;

  EnterpriseCLPScreenArguments(Map<String, dynamic> params) {
    pageName = params['name'];
    pageId = params['pageId'];
    widgetId = params['widgetId'];
    isEnterpriseMeetingLink = params['isEnterpriseMeetingLink'] == "true" ? true : false;
    meetingJoinUrl = params['meetingJoinUrl'];
    externalMeetingId = params['externalMeetingId'];
    meetingId = params['meetingId'];
  }
}

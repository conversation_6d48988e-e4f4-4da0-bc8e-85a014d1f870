import 'package:enterprise/blocs/activation_benefit/models.dart';

abstract class ActivationBenefitEvent {
  ActivationBenefitEvent() : super();
}

class LoadActivationBenefitEvent extends ActivationBenefitEvent {
  final bool showLoader;
  final String? pageId;
  final Map<String, String>? params;
  LoadActivationBenefitEvent(
      {this.showLoader = true, this.pageId = "benefits", this.params})
      : super();

  @override
  String toString() => 'LoadActivationBenefitEvent';
}

class LoadActivationBenefitFromRouteEvent extends ActivationBenefitEvent {
  final Map<String, dynamic> activationBenefitData;
  LoadActivationBenefitFromRouteEvent(this.activationBenefitData) : super();

  @override
  String toString() => 'LoadActivationBenefitFromRouteEvent';
}

class AddLoaderActivationBenefitEvent extends ActivationBenefitEvent {
  AddLoaderActivationBenefitEvent() : super();

  @override
  String toString() => 'AddLoaderActivationBenefitEvent';
}

class ResetActivationBenefitEvent extends ActivationBenefitEvent {
  @override
  String toString() => 'ResetActivationBenefitEvent';
}

class ScrollCLPBottomEvent extends ActivationBenefitEvent {
  final double scrollFactor;
  ScrollCLPBottomEvent({this.scrollFactor = 2.5});
  @override
  String toString() => 'ScrollToBottomEvent';
}

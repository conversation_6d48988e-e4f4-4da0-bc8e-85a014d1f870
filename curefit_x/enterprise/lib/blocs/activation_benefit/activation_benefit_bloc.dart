import 'dart:convert';

import 'package:common/network/client.dart';
import 'package:enterprise/blocs/activation_benefit/events.dart';
import 'package:enterprise/blocs/activation_benefit/models.dart';
import 'package:enterprise/network/activation_benefit_repository.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:enterprise/blocs/activation_benefit/state.dart';

class ActivationBenefitBloc
    extends Bloc<ActivationBenefitEvent, ActivationBenefitState> {
  final ActivationBenefitRepository repository;
  ActivationBenefitData activationBenefit =
      ActivationBenefitData(widgets: []);

  ActivationBenefitBloc(ActivationBenefitState initialState, this.repository)
      : super(initialState) {

    on<ResetActivationBenefitEvent>((event, emit) {
      activationBenefit = ActivationBenefitData(widgets: []);
      emit(ActivationBenefitIdle());
    });

    on<LoadActivationBenefitEvent>((event, emit) async {
      await _mapActivationBenefitToState(event, emit);
    });

    on<ScrollCLPBottomEvent>((event, emit) async {
      emit(ActivationBenefitLoaded(activationBenefit: activationBenefit,
          scrollToEnd: false,
          scrollFactor: event.scrollFactor));
    });

    on<AddLoaderActivationBenefitEvent>((event, emit) async {
      emit(ActivationBenefitLoading(activationBenefit: null));
    });

    on<LoadActivationBenefitFromRouteEvent>((event, emit) {
      ActivationBenefitData activationBenefitData = ActivationBenefitData(
          widgets: event.activationBenefitData['widgets'], action: event.activationBenefitData['action']);
      emit(ActivationBenefitLoaded(activationBenefit: activationBenefitData));
    });
  }

  loadMockJson(String fileName) async {
    String data = await rootBundle.loadString('assets/mocks/$fileName');
    return json.decode(data);
  }

  Future<void> _mapActivationBenefitToState(
      LoadActivationBenefitEvent event, Emitter<ActivationBenefitState> emit) async {
    try {
      emit(ActivationBenefitLoading(
          activationBenefit: activationBenefit, showLoader: event.showLoader));
      final response = await repository.getPageData(event.params ?? {});

      if (response == null) {
        emit(ActivationBenefitNotLoaded(throw Error()));
      }
      dynamic params = response; // await loadMockJson('enterprise_benefit.json');
      activationBenefit = ActivationBenefitData(widgets: params['widgets'],
          pageName: params['name'],
          action: params['action']);
      emit(ActivationBenefitLoaded(activationBenefit: activationBenefit));
    } on NetworkException catch (exception) {
      emit(ActivationBenefitNotLoaded(exception.subTitle));
    } on FormatException catch (exception) {
      emit(ActivationBenefitNotLoaded("Something went wrong"));
    }
    catch (e) {
      emit(ActivationBenefitNotLoaded("Something went wrong"));
    }
  }
}

import 'package:enterprise/blocs/activation_benefit/models.dart';

abstract class ActivationBenefitState {
  ActivationBenefitState() : super();
}

class ActivationBenefitIdle extends ActivationBenefitState {
  @override
  String toString() => 'IdleState';
}

class ActivationBenefitLoading extends ActivationBenefitState {
  final ActivationBenefitData? activationBenefit;
  final bool showLoader;

  ActivationBenefitLoading({this.activationBenefit, this.showLoader = true});

  @override
  String toString() => 'ActivationBenefitLoading';
}

class ActivationBenefitLoaded extends ActivationBenefitState {
  final bool scrollToEnd;
  final double scrollFactor;
  final ActivationBenefitData activationBenefit;

  ActivationBenefitLoaded(
      {required this.activationBenefit,
        this.scrollToEnd = false,
        this.scrollFactor = 2.5})
      : super();

  @override
  String toString() => 'ActivationBenefitLoaded';

}

class ActivationBenefitNotLoaded extends ActivationBenefitState {
  final String? error;

  ActivationBenefitNotLoaded([this.error]) : super();

  @override
  String toString() => 'ActivationBenefitNotLoaded';
}

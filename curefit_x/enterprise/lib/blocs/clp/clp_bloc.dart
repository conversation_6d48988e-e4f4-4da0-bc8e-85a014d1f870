import 'dart:convert';

import 'package:common/network/client.dart';
import 'package:enterprise/blocs/clp/events.dart';
import 'package:enterprise/blocs/clp/models.dart';
import 'package:enterprise/blocs/clp/state.dart';
import 'package:enterprise/network/zoom_meeting_deeplink_repository.dart';
import 'package:flutter/services.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:common/network/clp/clp_repository.dart';

class EnterpriseCLPBloc extends HydratedBloc<EnterpriseCLPEvent, EnterpriseCLPState> {
  final CLPClientRepository repository;
  final ZoomMeetingDeepLinkRepository zoomMeetingDeepLinkRepository;

  EnterpriseCLPData enterpriseCLP = EnterpriseCLPData(widgets: []);

  EnterpriseCLPBloc(EnterpriseCLPState initialState, this.repository, this.zoomMeetingDeepLinkRepository)
      : super(initialState) {

    on<ResetEnterpriseCLPEvent>((event, emit) async {
      enterpriseCLP = EnterpriseCLPData(widgets: []);
      emit(EnterpriseCLPIdle());
    });

    on<LoadEnterpriseCLPEvent>((event, emit) async {
      await _mapEnterpriseCLPToState(event, emit);
    });

    on<ScrollCLPBottomEvent>((event, emit) async {
      emit(EnterpriseCLPLoaded(
          enterpriseCLP: enterpriseCLP,
          scrollToEnd: false,
          scrollFactor: event.scrollFactor));
    });

    on<AddLoaderEnterpriseCLPEvent>((event, emit) async {
      emit(EnterpriseCLPLoading(enterpriseCLP: null));
    });
  }

  loadMockJson(String fileName) async {
    String data = await rootBundle.loadString('assets/mocks/$fileName');
    return json.decode(data);
  }

  Future<void> _mapEnterpriseCLPToState(
      LoadEnterpriseCLPEvent event, Emitter<EnterpriseCLPState> emit) async {
    try {
      if (state is EnterpriseCLPLoaded) {
        emit(EnterpriseCLPLoaded(enterpriseCLP: (state as EnterpriseCLPLoaded).enterpriseCLP));
      }
      else {
        emit(EnterpriseCLPLoading(
            enterpriseCLP: enterpriseCLP, showLoader: event.showLoader));
      }
      final response = await repository
          .getPageData('v2/page/${event.params?.pageId ?? event.pageId}', {});

      if (response == null && state is !EnterpriseCLPLoaded) {
        emit(EnterpriseCLPNotLoaded(error: throw Error()));
      }

      dynamic zoomDeepLinkResponse;
      if(event.params?.isEnterpriseMeetingLink == true) {
        zoomDeepLinkResponse = await zoomMeetingDeepLinkRepository.getLinkData({
          "externalMeetingId" : event.params?.externalMeetingId ?? "",
          "joinUrl" : event.params?.meetingJoinUrl ?? "",
          "meetingId" : event.params?.meetingId ?? ""
        });
      }
      dynamic params = response; // await loadMockJson('enterprise.json');
      enterpriseCLP = EnterpriseCLPData(
          widgets: params['body'],
          pageName: params['name'],
          actions: params['actions'],
          corpLogo: params['corpLogo'],
          corpName: params['corpName'],
          meetingDeepLinkAction: zoomDeepLinkResponse?["action"]);
      emit(EnterpriseCLPLoaded(enterpriseCLP: enterpriseCLP));
    } on NetworkException catch (exception) {
      if (state is EnterpriseCLPLoaded) {
        emit(EnterpriseCLPLoaded(enterpriseCLP: (state as EnterpriseCLPLoaded).enterpriseCLP));
      }
      else {
        emit(EnterpriseCLPNotLoaded(
            error: exception.subTitle, exception: exception));
      }
    } catch (e) {
      if (state is EnterpriseCLPLoaded) {
        emit(EnterpriseCLPLoaded(enterpriseCLP: (state as EnterpriseCLPLoaded).enterpriseCLP));
      }
      else {
        emit(EnterpriseCLPNotLoaded(error: "Something went wrong"));
      }
    }
  }

  @override
  EnterpriseCLPState? fromJson(Map<String, dynamic> json) {
    try {
      if (json['enterpriseCLP'] != null) {
        return EnterpriseCLPLoaded(
            enterpriseCLP: EnterpriseCLPData.fromJson(json['enterpriseCLP']));
      }
    } catch(e){}
    return state;
  }

  @override
  Map<String, dynamic>? toJson(EnterpriseCLPState state) {
    Map<String, dynamic> jsonObject = {};

    if (state is EnterpriseCLPLoaded) {
      jsonObject['enterpriseCLP'] = state.enterpriseCLP.toJson();
    }

    return jsonObject;
  }
}

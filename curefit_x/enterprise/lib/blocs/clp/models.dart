import 'package:common/action/action_handler.dart';

class EnterpriseCLPData {
  final String? pageName;
  final String? corpLogo;
  final String? corpName;
  final List widgets;
  final Map<String, dynamic>? meetingDeepLinkAction;
  dynamic action;

  EnterpriseCLPData({this.pageName, required this.widgets,this.corpLogo, this.corpName, this.meetingDeepLinkAction, actions}) {
    if(actions != null) {
      dynamic navigationAction = actions.firstWhere((i) {
        if(i['actionType'].contains('NAVIGATION') || i['actionType'].contains('SHOW_ACTIVATE_CORP_MODAL')) {
          return true;
        }
        return false;
      }, orElse: () => null);

      if(navigationAction != null) {
        navigationAction = Action.fromJson(navigationAction);
        action = navigationAction;
      }
    }
  }

  factory EnterpriseCLPData.fromJson(Map<String, dynamic> json) {
    List widgetList = (json['widgets'] as List)
        .map((item) => item)
        .toList();

    EnterpriseCLPData clpData = EnterpriseCLPData(
      pageName: json['pageName'],
      corpLogo: json['corpLogo'],
      corpName: json['corpName'],
      widgets: widgetList,
    );
    if (json['action'] != null) {
      clpData.action = Action.fromJson(json['action']);
    }

    return clpData;
  }

  Map<String, dynamic> toJson() {
    return {
      'pageName': pageName,
      'corpLogo': corpLogo,
      'corpName': corpName,
      'widgets': widgets,
      'action': action,
    };
  }
}

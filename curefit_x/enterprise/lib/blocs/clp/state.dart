import 'package:common/network/client.dart';
import 'package:enterprise/blocs/clp/models.dart';

abstract class EnterpriseCLPState {
  EnterpriseCLPState() : super();
}

class EnterpriseCLPIdle extends EnterpriseCLPState {
  @override
  String toString() => 'IdleState';
}

class EnterpriseCLPLoading extends EnterpriseCLPState {
  final EnterpriseCLPData? enterpriseCLP;
  final bool showLoader;

  EnterpriseCLPLoading({this.enterpriseCLP, this.showLoader = true});

  @override
  String toString() => 'EnterpriseCLPLoading';
}

class EnterpriseCLPLoaded extends EnterpriseCLPState {
  final bool scrollToEnd;
  final double scrollFactor;
  final EnterpriseCLPData enterpriseCLP;

  EnterpriseCLPLoaded(
      {required this.enterpriseCLP,
      this.scrollToEnd = false,
      this.scrollFactor = 2.5})
      : super();

  @override
  String toString() => 'EnterpriseCLPLoaded';

}

class EnterpriseCLPNotLoaded extends EnterpriseCLPState {
  final String? error;
  final NetworkException? exception;

  EnterpriseCLPNotLoaded({this.error, this.exception}) : super();

  @override
  String toString() => 'EnterpriseCLPNotLoaded';
}

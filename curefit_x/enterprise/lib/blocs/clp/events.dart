import 'package:enterprise/UI/clp/screens/clp.dart';

abstract class EnterpriseCLPEvent {
  EnterpriseCLPEvent() : super();
}

class LoadEnterpriseCLPEvent extends EnterpriseCLPEvent {
  final bool showLoader;
  final String? pageId;
  final EnterpriseCLPScreenArguments? params;
  LoadEnterpriseCLPEvent(
      {this.showLoader = true, this.pageId = "enterpriseclp", this.params})
      : super();

  @override
  String toString() => 'LoadEnterpriseCLPEvent';
}

class AddLoaderEnterpriseCLPEvent extends EnterpriseCLPEvent {
  AddLoaderEnterpriseCLPEvent() : super();

  @override
  String toString() => 'AddLoaderEnterpriseCLPEvent';
}

class ResetEnterpriseCLPEvent extends EnterpriseCLPEvent {
  @override
  String toString() => 'ResetEnterpriseCLPEvent';
}

class ScrollCLPBottomEvent extends EnterpriseCLPEvent {
  final double scrollFactor;
  ScrollCLPBottomEvent({this.scrollFactor = 2.5});
  @override
  String toString() => 'ScrollToBottomEvent';
}

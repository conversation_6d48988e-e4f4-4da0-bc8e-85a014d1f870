import 'dart:async';

import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/network/client.dart';
import 'package:enterprise/blocs/corp_activation/events.dart';
import 'package:enterprise/blocs/corp_activation/state.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:enterprise/network/corp_activation_repository.dart';
import 'package:enterprise/network/activation_benefit_repository.dart';

class CorpActivationBloc
    extends Bloc<CorpActivationEvent, CorpActivationState> {
  final CorpActivationRepository repository;
  final ActivationBenefitRepository activationBenefitRepository;

  final String somethingWrongMsg = "Something went wrong";
  final int corpActivateCheckDelaySecond = 5;

  CorpActivationBloc(
      {required this.repository, required this.activationBenefitRepository})
      : super(CorpActivationIdle()) {
    on<ResetCorpActivationEvent>((event, emit) {
      emit(CorpActivationIdle());
    });

    on<ResetCorpActivationOTPErrorEvent>((event, emit) {
      emit(ResetCorpActivationOTPError());
    });

    on<SetEmailSendOTPEvent>((event, emit) async {
      await _mapSetEmailSendOTPToState(event, emit);
    });

    on<SetPhoneSendOTPEvent>((event, emit) async {
      await _mapSetPhoneSendOTPToState(event, emit);
    });

    on<RedeemVoucherCodeEvent>((event, emit) async {
      await _mapRedeemVoucherToState(event, emit);
    });

    on<VerifyEmailThroughOTPEvent>((event, emit) async {
      await _mapVerifyEmailOTPToState(event, emit);
    });

    on<VerifyPhoneThroughOTPEvent>((event, emit) async {
      await _mapVerifyPhoneOTPToState(event, emit);
    });

    on<CheckCorpBenefitAssociatedEvent>((event, emit) async {
      await _mapCheckCorpBenefitAssociatedToState(event, emit);
    });

    on<CheckTNCEnabledForWorkEmailEvent>((event, emit) async {
      await _mapCheckTNCEnabledToState(event, emit);
    });
  }

  Future<void> _mapCheckTNCEnabledToState(
      CheckTNCEnabledForWorkEmailEvent event, Emitter<CorpActivationState> emit) async {
    try {
      final response = await repository.isTNCEnabled(event.email);
      if (response == null) {
        emit(CorpActivationError(somethingWrongMsg));
      } else {
        if (response['isTNCEnabled']) {
          emit(ShowEnterpriseTNC());
        } else {
          emit(SetEmailOTPState());
        }
      }
    } on NetworkException catch (exception) {
      emit(CorpActivationError(exception.subTitle ?? somethingWrongMsg));
    } catch (e) {
      emit(CorpActivationError(somethingWrongMsg));
    }
  }

  Future<void> _mapSetEmailSendOTPToState(
      SetEmailSendOTPEvent event, Emitter<CorpActivationState> emit) async {
    try {
      emit(CorpActivationLoading(showLoader: event.showLoader));
      final response = await repository.setEmailSendOtp(
          {"email": event.params?.email, "emailType": event.params?.emailType});
      if (response == null) {
        emit(CorpActivationError(somethingWrongMsg));
      } else {
        if (event.isResend == true) {
          emit(CorpActivationError("You will receive OTP on Email shortly"));
          emit(CorpActivationResendOTPSuccess());
        } else {
          emit(CorpActivationShowOTPVerify("email"));
        }
      }
    } on NetworkException catch (exception) {
      emit(CorpActivationError(exception.subTitle ?? ""));
    } catch (e) {
      emit(CorpActivationError(somethingWrongMsg));
    }
  }

  Future<void> _mapSetPhoneSendOTPToState(SetPhoneSendOTPEvent event, Emitter<CorpActivationState> emit) async {
    try {
      emit(CorpActivationLoading(showLoader: event.showLoader));
      final response = await repository.setPhoneNumberSendOtp({"phoneNumber": event.params?.phoneNumber, "countryCallingCode": event.params?.countryCallingCode, "medium": event.params?.medium});
      if (response == null) {
        emit(CorpActivationError(somethingWrongMsg));
      } else {
        if (event.isResend == true) {
          emit(CorpActivationError("You will receive OTP on Phone Number shortly"));
          emit(CorpActivationResendOTPSuccess());
        } else {
          emit(CorpActivationShowOTPVerify("phone"));
        }
      }
    } on NetworkException catch (exception) {
      emit(CorpActivationError(exception.subTitle ?? ""));
    } catch (e) {
      emit(CorpActivationError(somethingWrongMsg));
    }
  }

  Future<void> _mapRedeemVoucherToState(
      RedeemVoucherCodeEvent event, Emitter<CorpActivationState> emit) async {
    try {
      emit(CorpActivationLoading(showLoader: event.showLoader));
      final response = await repository
          .activeCouponCode({"couponCode": event.params!.couponCode, "meta": { "cityId": event.params!.cityId ?? ""} });
      if (response == null) {
        emit(CorpActivationError(somethingWrongMsg));
      } else {
        await Future.delayed(Duration(seconds: corpActivateCheckDelaySecond), () {
          emit(CorpVoucherRedeemSuccess());
        });
      }
    } on NetworkException catch (exception) {
      emit(CorpActivationShowMessage(exception.subTitle ?? somethingWrongMsg));
    } catch (e) {
      emit(CorpActivationShowMessage(somethingWrongMsg));
    }
  }

  Future<void> _mapVerifyEmailOTPToState(
      VerifyEmailThroughOTPEvent event, Emitter<CorpActivationState> emit) async {
    try {
      emit(CorpActivationLoading(showLoader: event.showLoader));
      final response = await repository.verifyEmailThroughOtp({
        "otp": event.params!.otp,
        "email": event.params!.email,
        "emailType": event.params!.emailType,
        "cityId": event.params!.cityId ?? ""
      });
      if (response == null) {
        emit(CorpActivationError(somethingWrongMsg));
      } else {
        await Future.delayed(Duration(seconds: corpActivateCheckDelaySecond), () {
          emit(CorpActivationOTPVerifySuccess());
        });
      }
    } on NetworkException catch (exception) {
      emit(CorpActivationError(exception.subTitle));
      emit(CorpActivationOTPError(
          wrongOTPError: "Oops! Looks like you’ve entered the wrong OTP"));
    } catch (e) {
      emit(CorpActivationError(somethingWrongMsg));
    }
  }

  Future<void> _mapVerifyPhoneOTPToState(
      VerifyPhoneThroughOTPEvent event, Emitter<CorpActivationState> emit) async {
    try {
      emit(CorpActivationLoading(showLoader: event.showLoader));
      final response = await repository.verifyPhoneNumberThroughOtp({
        "otp": event.params!.otp
      });
      if (response == null) {
        emit(CorpActivationError(somethingWrongMsg));
      } else {
        await Future.delayed(Duration(seconds: corpActivateCheckDelaySecond), () {
          emit(CorpActivationOTPVerifySuccess());
        });
      }
    } on NetworkException catch (exception) {
      emit(CorpActivationError(exception.subTitle));
      emit(CorpActivationOTPError(
          wrongOTPError: "Oops! Looks like you’ve entered the wrong OTP"));
    } catch (e) {
      emit(CorpActivationError(somethingWrongMsg));
    }
  }

  Future<void> _mapCheckCorpBenefitAssociatedToState(
      CheckCorpBenefitAssociatedEvent event, Emitter<CorpActivationState> emit) async {
    try {
      emit(CorpActivationLoading(showLoader: event.showLoader));
      final response = await activationBenefitRepository.getPageData({});
      if (response == null) {
        emit(CorpActivationError(somethingWrongMsg));
      } else {
        if(response['widgets'] != null) {
          emit(CorpBenefitAssociatedSuccess(response));
        }
        else{
          emit(CorpActivationShowMessage(
              "Sorry, there are no cultpass CORP benefits associated with this ${event.activationByType == CorpActivationByType.EMAIL ? "email id" : event.activationByType == CorpActivationByType.PHONE ? "phone number" : "voucher code"}"));
        }
      }
    } on NetworkException catch (exception) {
      emit(CorpActivationError(exception.subTitle));
    } catch (e) {
      emit(CorpActivationError(somethingWrongMsg));
    }
  }
}

import 'package:enterprise/blocs/activation_benefit/models.dart';

enum CorpActivationByType {
  EMAIL,
  PHONE,
  VOUCHER_CODE
}
abstract class CorpActivationState {
  CorpActivationState() : super();
}

class CorpActivationIdle extends CorpActivationState {
  @override
  String toString() => 'IdleState';
}

class CorpActivationLoading extends CorpActivationState {
  final bool showLoader;
  CorpActivationLoading({this.showLoader = true});

  @override
  String toString() => "CorpActivationLoading";
}

class CorpActivationError extends CorpActivationState {
  final String? errorMsg;
  CorpActivationError(this.errorMsg);
  @override
  String toString() => "CorpActivationError";
}

class CheckCorpBenefitAssociated extends CorpActivationState {
  final bool showLoader;
  final CorpActivationByType activationByType;
  CheckCorpBenefitAssociated(this.showLoader, this.activationByType);
  @override
  String toString() => "CheckCorpBenefitAssociated";
}

class CorpBenefitAssociatedSuccess extends CorpActivationState {
  final Map<String, dynamic> activationBenefitData;
  CorpBenefitAssociatedSuccess(this.activationBenefitData);
  @override
  String toString() => "CorpBenefitAssociatedSuccess";
}

class CorpActivationShowOTPVerify extends CorpActivationState {
  final String verifyFor;
  CorpActivationShowOTPVerify(this.verifyFor);

  @override
  String toString() => 'CorpActivationShowOTPVerify';
}

class CorpActivationResendOTPSuccess extends CorpActivationState {
  @override
  String toString() => 'CorpActivationResendOTPSuccess';
}

class CorpActivationOTPError extends CorpActivationState {
  final String? wrongOTPError;
  CorpActivationOTPError({this.wrongOTPError});
  @override
  String toString() => 'CorpActivationOTPError';
}

class CorpActivationOTPVerifySuccess extends CorpActivationState {
  @override
  String toString() => "CorpActivationOTPVerifySuccess";
}

class CorpVoucherRedeemSuccess extends CorpActivationState {
  @override
  String toString() => "CorpVoucherRedeemSuccess";
}

class ResetCorpActivationOTPError extends CorpActivationState {
  @override
  String toString() => 'ResetCorpActivationOTPError';
}

class CorpActivationShowMessage extends CorpActivationState {
  final String message;
  CorpActivationShowMessage(this.message);

  @override
  String toString() => 'CorpActivationShowMessage';
}

class ShowEnterpriseTNC extends CorpActivationState {
  ShowEnterpriseTNC(): super();

  @override
  String toString() => 'ShowEnterpriseTNC';
}

class SetEmailOTPState extends CorpActivationState {
  SetEmailOTPState(): super();

  @override
  String toString() => 'SetEmailOTPState';
}


import 'package:enterprise/blocs/corp_activation/models.dart';
import 'package:enterprise/blocs/corp_activation/state.dart';

abstract class CorpActivationEvent {
  CorpActivationEvent() : super();
}

class SetEmailSendOTPEvent extends CorpActivationEvent {
  final bool showLoader;
  final SetEmailSendOTPReqParam? params;
  final bool? isResend;

  SetEmailSendOTPEvent({this.showLoader = true, this.params, this.isResend = false}) : super();

  @override
  String toString() => 'SetEmailSendOTPEvent';
}

class RedeemVoucherCodeEvent extends CorpActivationEvent {
  final bool showLoader;
  final RedeemVoucherCodeReqParam? params;
  RedeemVoucherCodeEvent({this.showLoader = true, this.params}) : super();
}

class CheckCorpBenefitAssociatedEvent extends CorpActivationEvent {
  final bool showLoader;
  final CorpActivationByType activationByType;
  CheckCorpBenefitAssociatedEvent({this.showLoader = true, required this.activationByType}) : super();
}

class VerifyEmailThroughOTPEvent extends CorpActivationEvent {
  final bool showLoader;
  final VerifyEmailThroughOTPReqParam? params;
  VerifyEmailThroughOTPEvent({this.showLoader = true,this.params}) : super();

  @override
  String toString() => 'VerifyEmailThroughOTPEvent';
}

class ResetCorpActivationOTPErrorEvent extends CorpActivationEvent {
  @override
  String toString() => "ResetCorpActivationOTPErrorEvent";
}

class AddLoaderCorpActivationEvent extends CorpActivationEvent {
  AddLoaderCorpActivationEvent() : super();

  @override
  String toString() => 'AddLoaderCorpActivationEvent';
}

class ResetCorpActivationEvent extends CorpActivationEvent {
  @override
  String toString() => 'ResetCorpActivationEvent';
}


class SetPhoneSendOTPEvent extends CorpActivationEvent {
  final bool showLoader;
  final SetPhoneSendOTPReqParam? params;
  final bool? isResend;

  SetPhoneSendOTPEvent({this.showLoader = true, this.params, this.isResend = false}) : super();

  @override
  String toString() => 'SetPhoneSendOTPEvent';
}

class VerifyPhoneThroughOTPEvent extends CorpActivationEvent {
  final bool showLoader;
  final VerifyPhoneThroughOTPReqParam? params;
  VerifyPhoneThroughOTPEvent({this.showLoader = true,this.params}) : super();

  @override
  String toString() => 'VerifyPhoneThroughOTPEvent';
}

class CheckTNCEnabledForWorkEmailEvent extends CorpActivationEvent {
  final String email;
  final bool showLoader;
  final SetEmailSendOTPReqParam? params;
  final bool? isResend;

  CheckTNCEnabledForWorkEmailEvent({required this.email, this.showLoader = true, this.params, this.isResend = false}) : super();


  @override
  String toString() => 'CheckTNCEnabledForWorkEmailEvent';
}

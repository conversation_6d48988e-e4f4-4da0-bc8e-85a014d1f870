import 'package:common/action/action_handler.dart' as ActionHandler;
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/constants/action_constants.dart';
import 'package:common/network/clp/clp_repository.dart';
import 'package:common/ui/molecules/bottom_tray.dart';
import 'package:enterprise/UI/clp/widgets/activate_corp_modal.dart';
import 'package:enterprise/blocs/corp_activation/corp_activation_bloc.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../blocs/corp_activation/events.dart';
import '../blocs/corp_activation/models.dart';

class EnterpriseActionHandler extends ActionHandler.IActionHandler {
  late final CLPClientRepository clpClientRepository;
  final GlobalKey<NavigatorState> navigatorKey;

  EnterpriseActionHandler(clpClientRepository, {required this.navigatorKey}) {
    this.clpClientRepository = CLPClientRepository(clpClientRepository);
  }

  @override
  bool handleAction(ActionHandler.Action action, ActionBloc actionBloc) {
    BuildContext? context = navigatorKey.currentContext;
    if (context == null) {
      return false;
    }
    switch (action.type) {
      case ActionTypes.SHOW_ACTIVATE_CORP_MODAL:
        {
          showBottomTray(context: context, child: const ActivateCorpModal());
          return true;
        }
      case ActionTypes.SET_EMAIL_SEND_OTP:
        {
          CorpActivationBloc corpActivationBloc = BlocProvider.of<CorpActivationBloc>(context);
          final SetEmailSendOTPReqParam params = SetEmailSendOTPReqParam(action.meta!["email"], "WORK");
          corpActivationBloc.add(SetEmailSendOTPEvent(params: params, showLoader: true));
          return true;
        }
    }
    return false;
  }
}

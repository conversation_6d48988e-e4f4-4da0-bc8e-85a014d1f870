
import 'package:common/network/client.dart';

class CorpActivationRepository {
  final NetworkClient client;
  CorpActivationRepository(this.client);

  Future<dynamic> setEmailSendOtp(Map<String, dynamic> params, {String? baseUrl}) async {
    final response = await this.client.post('/user/setEmailSendOtp',params, baseUrl);
    return response;
  }

  Future<dynamic> verifyEmailThroughOtp(Map<String, dynamic> params, {String? baseUrl}) async {
    final response = await this.client.post("/user/verifyEmailThroughOtp", params, baseUrl);
    return response;
  }

  Future<dynamic> activeCouponCode(Map<String, dynamic> params, {String? baseUrl}) async {
    final response = await this.client.post("/fitnessPass/activate", params, baseUrl);
    return response;
  }

  Future<dynamic> setPhoneNumberSendOtp(Map<String, dynamic> params, {String? baseUrl}) async {
    final response = await this.client.post('/user/setPhoneNumber', params, baseUrl);
    return response;
  }

  Future<dynamic> verifyPhoneNumberThroughOtp(Map<String, dynamic> params, {String? baseUrl}) async {
    final response = await this.client.post('/user/verifyPhoneNumber', params, baseUrl);
    return response;
  }

  Future<dynamic> isTNCEnabled(String email) async {
    final response = await client.get("/v2/enterprise/isTNCEnabled?email=" + email);
    return response;
  }
}

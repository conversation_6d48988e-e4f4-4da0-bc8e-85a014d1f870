library community;

import 'package:common/network/client.dart';
import 'package:common/network/feedback_form_repository.dart';
import 'package:common/network/highlights_repository.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:common/user/user_repository.dart';
import 'package:community/UI/Screens/community_landing/screens/community_landing_page.dart';
import 'package:community/UI/Screens/cult_buddy/screens/cult_buddy.dart';
import 'package:community/UI/Screens/group_landing/screens/search_members.dart';
import 'package:community/UI/Screens/highlights/highlights_page.dart';
import 'package:community/UI/Screens/karma_points/Karma_points_Screen.dart';
import 'package:community/UI/Screens/moments/moments_page.dart';
import 'package:community/UI/Screens/post_creation/post_creation_page.dart';
import 'package:community/UI/Screens/post_detail/screens/post_detail_page.dart';
import 'package:community/UI/Screens/recommendations/recommendations_page.dart';
import 'package:community/UI/Screens/tagged_posts_landing/screens/tagged_posts.dart';
import 'package:community/blocs/about_group/aboutgroup_bloc.dart';
import 'package:community/blocs/comment_bloc/comment_bloc.dart';
import 'package:community/blocs/community_landing/community_landing_bloc.dart';
import 'package:community/blocs/feed_groups/feed_groups_bloc.dart';
import 'package:community/blocs/ftu_landing/ftu_landing_bloc.dart';
import 'package:community/blocs/group_landing/group_landing_bloc.dart';
import 'package:community/blocs/mentions_bloc/mention_bloc.dart';
import 'package:community/blocs/other_profile/other_profile_bloc.dart';
import 'package:community/blocs/post_creation/post_creation_bloc.dart';
import 'package:community/blocs/post_detail/post_detail_bloc.dart';
import 'package:community/blocs/posts_bloc/posts_page_bloc.dart';
import 'package:community/blocs/reaction_bloc/post_reaction_bloc.dart';
import 'package:community/blocs/update_profile/update_profile_bloc.dart';
import 'package:community/blocs/user_media/user_media_bloc.dart';
import 'package:community/network/community_landing_repository.dart';
import 'package:community/network/cult_buddy_repository.dart';
import 'package:community/network/edit_profile_repository.dart';
import 'package:community/network/feed_groups_repository.dart';
import 'package:community/network/ftu_landing_repository.dart';
import 'package:community/network/group_landing_repository.dart';
import 'package:community/network/post_page_repository.dart';
import 'package:community/network/recommendations_repository.dart';
import 'package:community/network/social_profile_repository.dart';
import 'package:community/network/social_user_media_repository.dart';
import 'package:core/network/notif_center_repository.dart';
import 'package:core/network/squads_manage_repository.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import './constants/constants.dart';
import 'UI/Screens/edit_profile/screens/edit_profile.dart';
import 'UI/Screens/ftu_landing/screens/ftu_landing.dart';
import 'UI/Screens/group_landing/screens/group_landing_page.dart';
import 'UI/Screens/social_user_media/screens/social_user_media.dart';
import 'UI/Screens/social_user_profile/screens/social_user_profile.dart';
import 'blocs/cult_buddy/cult_buddy_bloc.dart';
import 'blocs/edit_profile/edit_profile_bloc.dart';
import 'factory/widget_builder.dart' as CommunityWidgetBuilder;

class Community {
  IWidgetBuilder get widgetBuilder {
    return CommunityWidgetBuilder.WidgetBuilder();
  }

  String _routeName(RouteNames routeName) {
    return '/${EnumToString.convertToString(routeName)}';
  }

  Map<String, WidgetBuilder> tabRoute(NetworkClient networkClient) {
    return {
      _routeName(RouteNames.community_landing): (_) =>
          _clpRoute(networkClient, TabMode.EMBEDDED),
    };
  }

  Widget _clpRoute(NetworkClient networkClient, TabMode tabMode) {
    final PostPageRepository postPageRepository =
        PostPageRepository(client: networkClient);
    final CommunityLandingRepository communityLandingRepository =
        CommunityLandingRepository(client: networkClient);
    final FeedGroupsRepository feedGroupsRepository =
        FeedGroupsRepository(client: networkClient);
    return MultiBlocProvider(providers: [
      BlocProvider(create: (context) => PostReactionBloc(postPageRepository)),
      BlocProvider(
          create: (context) => FeedGroupsBloc(context, feedGroupsRepository)),
      BlocProvider(
          create: (context) => PostDetailBloc(context, postPageRepository)),
      BlocProvider(
          create: (context) => PostPageBloc(
              context, postPageRepository, PostsFeedType.MAIN_FEED)),
      BlocProvider(
          create: (context) => CommunityLandingBloc(communityLandingRepository))
    ], child: const CommunityLandingPage());
  }

  Map<String, WidgetBuilder> getRoutes(
      NetworkClient networkClient, Function onClose) {
    final FTULandingRepository ftuLandingRepository =
        FTULandingRepository(networkClient);
    final GroupLandingRepository groupLandingRepository =
        GroupLandingRepository(client: networkClient);
    final PostPageRepository postPageRepository =
        PostPageRepository(client: networkClient);
    final NotifCenterRepository notifCenterRepository =
        NotifCenterRepository(client: networkClient);
    final RecommendationsRepository recommendationRepository =
        RecommendationsRepository(client: networkClient);
    final HighlightsRepository highlightsRepository =
        HighlightsRepository(client: networkClient);
    final SquadsRepository squadsRepository =
        SquadsRepository(client: networkClient);
    final SocialProfileRepository socialProfileRepository =
        SocialProfileRepository(client: networkClient);
    final CommunityLandingRepository communityLandingRepository =
        CommunityLandingRepository(client: networkClient);
    final FeedGroupsRepository feedGroupsRepository =
        FeedGroupsRepository(client: networkClient);
    final SocialUserMediaRepository socialUserMediaRepository =
        SocialUserMediaRepository(client: networkClient);
    final EditProfileRepository editProfileRepository =
        EditProfileRepository(client: networkClient);
    final FeedbackPageRepository feedbackPageRepository =
        FeedbackPageRepository(client: networkClient);
    final CultBuddyRepository cultBuddyRepository =
        CultBuddyRepository(client: networkClient);

    return {
      _routeName(RouteNames.karma_points): (_) => KarmaScreen(),
      _routeName(RouteNames.ftu_landing_page): (_) => BlocProvider(
            create: (context) => FTULandingBloc(ftuLandingRepository),
            child: const FTULandingPage(),
          ),
      _routeName(RouteNames.group_landing_page): (_) => MultiBlocProvider(
            providers: [
              BlocProvider(
                  create: (context) => PostReactionBloc(postPageRepository)),
              BlocProvider(
                  create: (context) =>
                      GroupLandingBloc(context, groupLandingRepository)),
              BlocProvider(
                  create: (context) => FTULandingBloc(ftuLandingRepository)),
              BlocProvider(
                  create: (context) =>
                      AboutGroupPageBloc(groupLandingRepository)),
              BlocProvider(
                  create: (context) =>
                      PostDetailBloc(context, postPageRepository)),
            ],
            child: GroupLandingPage(ModalRoute.of(_)!.settings.arguments),
          ),
      _routeName(RouteNames.searched_posts_page): (_) => MultiBlocProvider(
            providers: [
              BlocProvider(
                  create: (context) => PostPageBloc(
                      context, postPageRepository, PostsFeedType.TAGGED_FEED)),
              BlocProvider(
                  create: (context) => PostReactionBloc(postPageRepository)),
              BlocProvider(
                  create: (context) => FTULandingBloc(ftuLandingRepository)),
            ],
            child: SearchedPostsPage(ModalRoute.of(_)!.settings.arguments),
          ),
      _routeName(RouteNames.moments_posts_page): (_) => MultiBlocProvider(
            providers: [
              BlocProvider(
                  create: (context) => PostPageBloc(
                      context, postPageRepository, PostsFeedType.TAGGED_FEED)),
              BlocProvider(
                  create: (context) => PostReactionBloc(postPageRepository)),
              BlocProvider(
                  create: (context) => FTULandingBloc(ftuLandingRepository)),
            ],
            child: MomentsPostsPage(ModalRoute.of(_)!.settings.arguments),
          ),
      _routeName(RouteNames.recommendations_page): (_) =>
          RecommendationsPage(args: ModalRoute.of(_)!.settings.arguments),
      _routeName(RouteNames.highlights_page): (_) =>
          HighlightsPage(ModalRoute.of(_)!.settings.arguments),
      _routeName(RouteNames.post_detail_page): (_) => MultiBlocProvider(
            providers: [
              BlocProvider(
                  create: (context) => PostReactionBloc(postPageRepository)),
              BlocProvider(
                  create: (context) =>
                      PostDetailBloc(context, postPageRepository)),
              BlocProvider(
                  create: (context) =>
                      CommentBloc(context, postPageRepository)),
              BlocProvider(create: (context) => MentionBloc(postPageRepository))
            ],
            child: PostDetailPage(ModalRoute.of(_)!.settings.arguments),
          ),
      _routeName(RouteNames.social_user_profile): (_) => MultiBlocProvider(
            providers: [
              BlocProvider(
                  create: (context) => PostReactionBloc(postPageRepository)),
              BlocProvider(
                  create: (context) =>
                      PostDetailBloc(context, postPageRepository)),
              BlocProvider(
                create: (context) => OtherProfileBloc(
                  context,
                  socialProfileRepository,
                  socialUserId: ModalRoute.of(_)!.settings.arguments != null
                      ? (ModalRoute.of(_)!.settings.arguments as dynamic)
                          .params["socialUserId"]
                      : null,
                  cultUserId: ModalRoute.of(_)!.settings.arguments != null
                      ? (ModalRoute.of(_)!.settings.arguments as dynamic)
                          .params["cultUserId"]
                      : null,
                  phone: ModalRoute.of(_)!.settings.arguments != null
                      ? (ModalRoute.of(_)!.settings.arguments as dynamic)
                          .params["phone"]
                      : null,
                ),
              ),
            ],
            child: const SocialUserProfile(),
          ),
      _routeName(RouteNames.post_creation_page): (_) => MultiBlocProvider(
            providers: [
              BlocProvider(
                  create: (context) => PostCreationBloc(postPageRepository)),
              BlocProvider(
                  create: (context) => MentionBloc(postPageRepository)),
              BlocProvider(
                  create: (context) =>
                      GroupLandingBloc(context, groupLandingRepository)),
              BlocProvider(create: (context) => GroupsBloc(postPageRepository))
            ],
            child: PostCreationPage(ModalRoute.of(_)!.settings.arguments),
          ),
      _routeName(RouteNames.search_members_page): (_) => MultiBlocProvider(
            providers: [
              BlocProvider(
                  create: (context) => MentionBloc(postPageRepository)),
            ],
            child: SearchMembers(ModalRoute.of(_)!.settings.arguments),
          ),
      _routeName(RouteNames.community_landing): (_) =>
          MultiBlocProvider(providers: [
            BlocProvider(
                create: (context) => PostReactionBloc(postPageRepository)),
            BlocProvider(
                create: (context) =>
                    FeedGroupsBloc(context, feedGroupsRepository)),
            BlocProvider(
                create: (context) =>
                    PostDetailBloc(context, postPageRepository)),
            BlocProvider(
                create: (context) => PostPageBloc(
                    context, postPageRepository, PostsFeedType.MAIN_FEED)),
            BlocProvider(
                create: (context) =>
                    CommunityLandingBloc(communityLandingRepository))
          ], child: const CommunityLandingPage()),
      _routeName(RouteNames.social_user_media): (_) => MultiBlocProvider(
            providers: [
              BlocProvider(
                  create: (context) => UserMediaBloc(
                      context,
                      socialUserMediaRepository,
                      (ModalRoute.of(_)!.settings.arguments as dynamic)
                          .params["socialUserId"])),
            ],
            child: const SocialUserMedia(),
          ),
      _routeName(RouteNames.edit_profile): (_) => MultiBlocProvider(
            providers: [
              BlocProvider(
                  create: (context) => EditProfileBloc(editProfileRepository)),
              BlocProvider(
                  create: (context) =>
                      UpdateProfileBloc(editProfileRepository)),
            ],
            child: EditProfile(ModalRoute.of(_)!.settings.arguments),
          ),
      _routeName(RouteNames.community_comingsoon): (_) => MultiBlocProvider(
            providers: [
              BlocProvider(
                  create: (context) => CultBuddyBloc(cultBuddyRepository)),
            ],
            child: const CultBuddy(),
          ),
    };
  }
}

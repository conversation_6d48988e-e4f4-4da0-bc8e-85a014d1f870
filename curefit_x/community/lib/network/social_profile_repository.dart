import 'package:common/network/client.dart';
import 'package:community/action/internal_action_handler.dart';
import 'package:flutter/material.dart';

class SocialProfileRepository {
  NetworkClient client;

  SocialProfileRepository({required this.client});

  Future<dynamic> getOthersProfile(BuildContext context, String? profileId,
      String? cultUserId, String? nextCursor) async {
    String cb = getCbToken(context);
    final response = await client.get('/v2/community/profile', {
      if (profileId != null) "socialUserId": profileId,
      if (cultUserId != null) "cultUserId": cultUserId,
      if (nextCursor != null) "nextCursor": nextCursor,
      if (cb.isNotEmpty) "cb": cb,
    });
    return response;
  }

  Future<dynamic> changePrivacyToPublic() async {
    final response = await client
        .post('/v2/community/updatevisibility', {"visibility": "PUBLIC"});
    return response;
  }
}

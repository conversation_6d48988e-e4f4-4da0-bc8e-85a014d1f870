import 'package:common/network/client.dart';
import 'package:community/action/internal_action_handler.dart';
import 'package:flutter/material.dart';

class RecommendationsRepository {
  NetworkClient client;

  RecommendationsRepository({required this.client});

  Future<dynamic> getRecommendations(BuildContext context, String? classId, String? bookingId, String? nextKey) async {
    final response = await client.get('/v2/community/recommendations', {
      if (nextKey != null) "nextCursor": nextKey,
      if (classId != null) "classId": classId,
      if (bookingId != null) "bookingId": bookingId,
    });
    return response;
  }
}

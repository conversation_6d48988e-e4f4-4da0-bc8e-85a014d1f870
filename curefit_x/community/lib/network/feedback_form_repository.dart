import 'package:common/network/client.dart';
import 'package:common/blocs/feedback/feedback_models.dart';
import 'package:flutter/cupertino.dart';

class FeedbackPageRepository {
  NetworkClient client;

  FeedbackPageRepository({required this.client});

  Future<dynamic> getFeedbackForm(String feedbackId) async {
    final response = await client.get('/feedback/getWidgets/$feedbackId');
    return response;
  }

  Future<dynamic> postFeedback(
      BuildContext context, SubmitFeedbackData submitFeedbackData) async {
    final response = await client
        .post('/feedback/submit/v2', {...submitFeedbackData.toJson()});
    return response;
  }
}

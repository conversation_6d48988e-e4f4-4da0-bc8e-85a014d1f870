import 'dart:io';

import 'package:common/network/client.dart';
import 'package:community/blocs/edit_profile/edit_profile_models.dart';
import 'package:enum_to_string/enum_to_string.dart';

class EditProfileRepository {
  NetworkClient client;

  EditProfileRepository({required this.client});

  Future<dynamic> getProfilePicSignedUrl(String fileName) async {
    final _fileName = fileName.replaceAll("image_cropper_", "");
    final response = await client
        .get('/user/profilePicture/signedUrl', {"fileName": _fileName});
    return response;
  }

  Future<dynamic> editProfile(isCreateProfile) async {
    final response = await client
        .get('/v2/community/editprofile', {'isCreateProfile': isCreateProfile});
    return response;
  }

  Future<dynamic> uploadProfilePicToS3(dynamic s3Info, File file) async {
    final response = await client.uploadFile(s3Info['url'], file, s3Info);
    return response;
  }

  Future<dynamic> uploadProfilePic(String fileName) async {
    final response =
        await client.post('/user/uploadProfilePicture', {"fileName": fileName});
    return response;
  }

  Future<dynamic> uploadProfileInfo(EditProfileData details) async {
    dynamic data = getEditProfileJsonData(details);
    final response = await client.post('/v2/community/updateprofile', data);
    return response;
  }
}

import 'package:common/network/client.dart';
import 'package:flutter/material.dart';

class SocialUserMediaRepository {
  NetworkClient client;

  SocialUserMediaRepository({required this.client});

  Future<dynamic> getUserMedia(
      BuildContext context, String profileId, String? nextCursor) async {
    final response = await client.get('/v2/community/media', {
      "socialUserId": profileId,
      if (nextCursor != null) "nextCursor": nextCursor
    });
    return response;
  }
}

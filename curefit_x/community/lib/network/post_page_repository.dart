import 'package:common/network/client.dart';
import 'package:community/action/internal_action_handler.dart';
import 'package:flutter/material.dart';

enum PostsFeedType { MAIN_FEED, GROUP_FEED, FEATURED_FEED, TAGGED_FEED, MOMENTS_FEED }

class PostPageRepository {
  NetworkClient client;

  PostPageRepository({required this.client});

  Future<dynamic> reportPost(String activityId) async {
    final response = await client
        .post('/v2/community/reportactivity', {"activityId": activityId});
    return response;
  }

  Future<dynamic> postReaction(String activityId, bool status) async {
    final response = await client.post('/v2/community/activityreaction',
        {"likeStatus": status ? "True" : "False", "activityId": activityId});
    return response;
  }

  Future<dynamic> postComment(BuildContext context, String postId,
      String comment, String? commentId, String? parentCommentId) async {
    String cb = getCbToken(context);
    final response = await client.post('/v2/community/postcomment', {
      "comment": comment,
      "activityId": postId,
      if (cb.isNotEmpty) "cb": cb
    });
    return response;
  }

  Future<dynamic> getUserMentions(String triggerWord, String? groupId) async {
    final response =
        await client.get('/v2/community/searchmembers', {"query": triggerWord, "groupId": groupId.toString()});
    return response;
  }

  Future<dynamic> getSharablePostUrl(String postId) async {
    final response =
        await client.get('/v2/community/sharepost', {"postId": postId});
    return response;
  }

  Future<dynamic> getPosts(BuildContext context, PostsFeedType feedType,
      String? groupId, String? tag, String? nextCursor) async {
    String cb = getCbToken(context);
    switch (feedType) {
      case PostsFeedType.MAIN_FEED:
        final response = await client.get('/v2/community/userfeed', {
          if (nextCursor != null) "nextCursor": nextCursor,
          if (cb.isNotEmpty) "cb": cb
        });
        return response;
      case PostsFeedType.GROUP_FEED:
        final response = await client.get('/v2/community/groupfeed', {
          "groupId": groupId!,
          if (nextCursor != null) "nextCursor": nextCursor,
          if (cb.isNotEmpty) "cb": cb
        });
        return response;
      case PostsFeedType.TAGGED_FEED:
        final response = await client.get('/v2/community/search', {
          "tag": tag.toString(),
          if (nextCursor != null) "nextCursor": nextCursor,
          if (cb.isNotEmpty) "cb": cb
        });
        return response;
      case PostsFeedType.MOMENTS_FEED:
        final response = await client.get('/v2/community/momentsFeed', {
          "groupId": groupId!,
        });
        return response;
    }
  }

  Future<dynamic> getPostDetailedPage(
      BuildContext context, String postId, String? nextCursor) async {
    String cb = getCbToken(context);
    final response = await client.get('/v2/community/postdetail', {
      "activityId": postId,
      if (nextCursor != null) "nextCursor": nextCursor,
      if (cb.isNotEmpty) "cb": cb
    });
    return response;
  }

  Future<dynamic> getPostingUserAndGroup(String? groupId) async {
    final response = await client.get('/v2/community/authenticate',
        {if (groupId != null) "groupId": groupId});
    return response;
  }

  Future<dynamic> getGroups() async {
    final response = await client.get('/v2/community/listgroup');
    return response;
  }

  Future<dynamic> getCommunityLanding(String? groupId) async {
    final response = await client.get('/v2/community/landing');
    return response;
  }

  Future<dynamic> getFeedGroups(String? groupId) async {
    final response = await client.get('/v2/community/feedgroups');
    return response;
  }

  Future<dynamic> postCreated(BuildContext context, String activityId) async {
    String cb = getCbToken(context);
    final response = await client
        .post('/v2/community/postcreated', {"activityId": activityId});
    return response;
  }
}

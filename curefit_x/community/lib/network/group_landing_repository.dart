import 'package:common/network/client.dart';
import 'package:community/action/internal_action_handler.dart';
import 'package:community/blocs/social_cache/social_cache_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class GroupLandingRepository {
  final NetworkClient client;

  GroupLandingRepository({required this.client});

  Future<dynamic> leaveGroup(String groupId) async {
    final response =
        await client.post('/v2/community/leavegroup?groupId=$groupId');
    return response;
  }

  Future<dynamic> joinGroup(String groupId) async {
    final response =
        await client.post('/v2/community/joingroup?groupId=$groupId');
    return response;
  }

  Future<dynamic> getMembersList(String groupId, String? nextCursor) async {
    final response = await client.get('/v2/community/memberslist',
        {"groupId": groupId, if (nextCursor != null) "nextCursor": nextCursor});
    print(response);
    return response;
  }

  Future<dynamic> getAboutGroup(String groupId) async {
    final response =
        await client.get('/v2/community/groupabout', {"groupId": groupId});
    return response;
  }

  Future<dynamic> getGroupDetails(BuildContext context, String groupId) async {
    SocialCacheBloc cacheBloc = BlocProvider.of<SocialCacheBloc>(context);
    String cb = getCbToken(context);
    final response = await client.get('/v2/community/group',
        {"groupId": groupId, if (cb.isNotEmpty) "cb": cb});
    return response;
  }
}

import 'package:common/network/client.dart';

class KarmaPointsRepository {
  final NetworkClient client;
  KarmaPointsRepository({required this.client});

  Future<dynamic> giveKudosToUser(payload) async {
    final dynamic response =
        await client.post('/v2/kudos/userReview/create', payload);
    return response;
  }

  Future<dynamic> fetchKarmaScreenData(String userType) async {
    final dynamic response = await client
        .get('/v2/kudos/userReview/summary/forUser', {"userType": userType});
    return response;
  }
}

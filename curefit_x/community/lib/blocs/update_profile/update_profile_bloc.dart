import 'dart:io';

import 'package:community/UI/Screens/edit_profile/widgets/squad_visibility.dart';
import 'package:community/blocs/edit_profile/edit_profile_models.dart';
import 'package:community/network/edit_profile_repository.dart';
import 'package:community/blocs/update_profile/update_profile_events.dart';
import 'package:community/blocs/update_profile/update_profile_states.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class UpdateProfileBloc extends Bloc<UpdateProfileEvent, UpdateProfileState> {
  EditProfileRepository repository;

  UpdateProfileBloc(this.repository) : super(InitialUpdateProfileState()) {
    on<UploadProfileDetails>((event, emit) async {
      if (state is InitialUpdateProfileState ||
          state is UploadedProfileDetails ||
          state is NotUploadedProfileDetails) {
        emit(UploadingProfileDetails());
        try {
          EditProfileData finalData = event.data.data;
          if (event.name != null) {
            finalData.name = event.name!;
          }
          if (event.dob != null) {
            finalData.dob = event.dob!;
          }
          if (event.gender != null) {
            finalData.gender = event.gender!;
          }
          if (event.height != null) {
            finalData.height = event.height!;
          }
          if (event.weight != null) {
            finalData.weight = event.weight!;
          }
          if (event.instaHandle != null) {
            finalData.instagramHandle = event.instaHandle;
          }
          if (event.privacy != null) {
            finalData.privacy = event.privacy!;
          }
          if (event.squadVisibility != null) {
            finalData.squadVisibilityData?.squadVisibility =
                event.squadVisibility!;
          }
          if (event.bio != null) {
            finalData.bio = event.bio!;
          }
          if (event.tags.isNotEmpty) {
            List<ProfileTag> newTags = finalData.tags;
            for (int i = 0; i < newTags.length; i++) {
              if (event.tags.contains(newTags[i].code)) {
                newTags[i].mapped = true;
              } else {
                newTags[i].mapped = false;
              }
            }
            finalData.tags = newTags;
          }
          if (event.profilePicture != null) {
            // To do
            String fileName = event.profilePicture!.split("/").last;
            var s3Info = await repository.getProfilePicSignedUrl(fileName);
            var uploadRes = await repository.uploadProfilePicToS3(
                s3Info, File(event.profilePicture!));
            var userResponse =
                await repository.uploadProfilePic(s3Info['fileName']);
            finalData.profilePictureUrl = userResponse['profilePictureUrl'];
          }
          dynamic res = await repository.uploadProfileInfo(finalData);
          emit(UploadedProfileDetails(res?["status"]));
        } catch (error) {
          emit(NotUploadedProfileDetails());
        }
      }
    });
  }
}

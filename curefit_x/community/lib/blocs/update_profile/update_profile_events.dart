import 'package:core/model/squad_visibility_type.dart';

abstract class UpdateProfileEvent {
  UpdateProfileEvent() : super();
}

class UploadProfileDetails extends UpdateProfileEvent {
  String? name;
  String? dob;
  String? gender;
  String? height;
  String? weight;
  String? bio;
  String? instaHandle;
  String? privacy;
  SquadVisibilityType? squadVisibility;
  List<String> tags = [];
  String? profilePicture;
  dynamic data;
  UploadProfileDetails(
      this.data,
      this.name,
      this.dob,
      this.gender,
      this.height,
      this.weight,
      this.bio,
      this.instaHandle,
      this.privacy,
      this.squadVisibility,
      this.tags,
      this.profilePicture)
      : super();
}

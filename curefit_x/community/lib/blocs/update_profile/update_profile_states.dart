
abstract class UpdateProfileState{
  UpdateProfileState(): super();
}

class InitialUpdateProfileState extends UpdateProfileState {
  InitialUpdateProfileState(): super();
}

class UploadingProfileDetails extends UpdateProfileState {
  UploadingProfileDetails(): super();
}

class UploadedProfileDetails extends UpdateProfileState {
  bool status;
  UploadedProfileDetails(this.status): super();
}

class NotUploadedProfileDetails extends UpdateProfileState {
  NotUploadedProfileDetails(): super();
}

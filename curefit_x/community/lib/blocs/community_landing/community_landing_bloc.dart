import 'package:common/action/action_handler.dart';
import 'package:common/network/client.dart';
import 'package:community/network/community_landing_repository.dart';
import 'package:community/blocs/community_landing/community_landing_events.dart';
import 'package:community/blocs/community_landing/community_landing_models.dart';
import 'package:community/blocs/community_landing/community_landing_state.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class CommunityLandingBloc
    extends Bloc<CommunityLandingEvent, CommunityLandingState> {
  CommunityLandingRepository communityLandingRepository;

  CommunityLandingBloc(this.communityLandingRepository)
      : super(IdleCommunityLandingState()) {
    on<LoadCommunityLanding>((event, emit) async {
      if (!event.isRefresh) {
        emit(LoadingCommunityLandingState());
      }
      try {
        final response = await communityLandingRepository.getCommunityLanding();
        Action? shareAction = response['shareAction'] != null
            ? Action.fromJson(response['shareAction'])
            : null;
        Action? groupJoinToShareAction = response['groupJoinToShareAction'] != null
            ? Action.fromJson(response['groupJoinToShareAction'])
            : null;
        CommunityLandingData data = CommunityLandingData(
            response["userProfilePic"], response["userName"], shareAction ?? groupJoinToShareAction);
        emit(LoadedCommunityLandingState(data));
      } on NetworkException catch (exception) {
        emit(NotLoadedCommunityLandingState(exception.title));
      }
    });
  }
}

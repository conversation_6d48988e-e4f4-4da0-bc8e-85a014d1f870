

import 'package:community/blocs/community_landing/community_landing_models.dart';

abstract class CommunityLandingState {
  CommunityLandingState();
}

class IdleCommunityLandingState extends CommunityLandingState {
  IdleCommunityLandingState();
}

class LoadingCommunityLandingState extends CommunityLandingState {
  LoadingCommunityLandingState();
}

class LoadedCommunityLandingState extends CommunityLandingState {
  CommunityLandingData data;
  LoadedCommunityLandingState(this.data);
}

class NotLoadedCommunityLandingState extends CommunityLandingState {
  final String? error;
  NotLoadedCommunityLandingState(this.error);
}

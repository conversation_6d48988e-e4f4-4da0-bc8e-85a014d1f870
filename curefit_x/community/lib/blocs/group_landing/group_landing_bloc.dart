import 'package:common/model/group_landing_models.dart';
import 'package:common/network/client.dart';
import 'package:community/network/group_landing_repository.dart';
import 'package:community/blocs/group_landing/group_landing_events.dart';
import 'package:community/blocs/group_landing/group_landing_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:common/action/action_handler.dart' as action_handler;

class GroupLandingBloc extends Bloc<GroupLandingEvent, GroupLandingState> {
  GroupLandingRepository groupLandingRepository;
  BuildContext context;
  GroupLandingBloc(this.context, this.groupLandingRepository)
      : super(IdleGroupLandingState()) {
    on<LoadGroupLanding>((event, emit) async {
      emit(LoadingGroupLandingState());
      try {
        final response = await groupLandingRepository.getGroupDetails(
            context, event.groupId);
        List<GroupMenuOption> options = response['options']
            .map<GroupMenuOption>((e) => GroupMenuOption.fromJson(e))
            .toList();
        action_handler.Action? addPost = response['addPost'] != null
            ? action_handler.Action.fromJson(response['addPost'])
            : null;
        GroupLandingData data = GroupLandingData(response['groupName'],
            response['showJoinCta'], options, response['shareUrl'],
            initialIndex: response['initialIndex'], addPost: addPost);
        emit(LoadedGroupLandingState(data));
      } on NetworkException catch (exception) {
        emit(NotLoadedGroupLandingState(exception.title));
      }
    });
    on<JoinGroup>((event, emit) async {
      final res = await groupLandingRepository.joinGroup(event.groupId);
      emit(LoadingGroupLandingState());
      try {
        final response = await groupLandingRepository.getGroupDetails(
            context, event.groupId);
        List<GroupMenuOption> options = response['options']
            .map<GroupMenuOption>((e) => GroupMenuOption.fromJson(e))
            .toList();
        action_handler.Action? addPost = response['addPost'] != null
            ? action_handler.Action.fromJson(response['addPost'])
            : null;
        GroupLandingData data = GroupLandingData(response['groupName'],
            response['showJoinCta'], options, response['shareUrl'],
            initialIndex: response['initialIndex'], addPost: addPost);
        emit(LoadedGroupLandingState(data));
      } on NetworkException catch (exception) {
        emit(NotLoadedGroupLandingState(exception.title));
      }
    });
  }
}

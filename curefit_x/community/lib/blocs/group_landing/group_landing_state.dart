import 'package:common/model/group_landing_models.dart';

abstract class GroupLandingState {
  GroupLandingState();
}

class IdleGroupLandingState extends GroupLandingState {
  IdleGroupLandingState();
}

class LoadingGroupLandingState extends GroupLandingState {
  LoadingGroupLandingState();
}

class LoadedGroupLandingState extends GroupLandingState {
  GroupLandingData data;
  LoadedGroupLandingState(this.data);
}

class NotLoadedGroupLandingState extends GroupLandingState {
  final String? error;
  NotLoadedGroupLandingState(this.error);
}

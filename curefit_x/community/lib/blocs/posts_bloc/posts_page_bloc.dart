import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:community/network/post_page_repository.dart';
import 'posts_page_events.dart';
import 'posts_page_state.dart';

class PostPageBloc extends Bloc<PostPageEvent, PostPageState> {
  PostPageRepository repository;
  PostsFeedType postsFeedType;
  String? groupId;
  String? tag;
  BuildContext context;
  PostPageBloc(this.context, this.repository, this.postsFeedType,
      {this.groupId, this.tag})
      : super(InitialPostPageState()) {
    on<FetchPosts>((event, emit) async {
      if (state is InitialPostPageState || event.isRefresh) {
        try {
          var res =
              await repository.getPosts(context, postsFeedType, groupId, tag, null);
          final List<dynamic> posts = res["widgets"];
          String? nextCursor = res["nextCursor"];
          int ttl = res['ttl'];
          DateTime lastLoadedTime = DateTime.now();
          emit(PostPageLoadedState(
              posts, nextCursor == null, nextCursor, ttl, lastLoadedTime));
        } on Exception {
          emit(PostPageLoadedState([], false, null, 300, DateTime.now()));
        }
      } else if (state is PostPageLoadedState) {
        try {
          var res = await repository.getPosts(context, postsFeedType, groupId, tag,
              (state as PostPageLoadedState).nextCursor);
          final List<dynamic> posts = res["widgets"];
          String? nextCursor = res["nextCursor"];
          emit((state as PostPageLoadedState)
              .copyFrom(posts, nextCursor == null, nextCursor));
        } on Exception {
          emit((state as PostPageLoadedState).copyFrom([], true, null));
        }
      }
    });
    on<PollInPostPage>((event, emit) async {
      PostPageLoadedState postsList = (state as PostPageLoadedState);
      String postId = event.postId;
      for (int i = 0; i < postsList.posts.length; i++) {
        if (postsList.posts[i]['data']['id'] == postId) {
          postsList.posts[i]['data']['poll']['vote_count'] = event.pollData.voteCount;
          for(int i = 0; i < event.pollData.options.length; i++) {
            postsList.posts[i]['data']['poll']['options'][i]['vote_count'] =
                event.pollData.options[i].voteCount;
            postsList.posts[i]['data']['poll']['options'][i]['voted_by_me'] =
                event.pollData.options[i].votedByMe;
          }
        }
      }
      emit(postsList);
    });
    on<ReactionPost>((event, emit) async {
      PostPageLoadedState postsList = (state as PostPageLoadedState);
      String postId = event.postId;
      for (int i = 0; i < postsList.posts.length; i++) {
        if (postsList.posts[i]['data']['id'] == postId) {
          if (event.isLiked) {
            // it shouldnt be null, so making it "Liked"
            postsList.posts[i]['data']['my_reactions'] = "Liked";
          } else {
            postsList.posts[i]['data']['my_reactions'] = null;
          }
          if (postsList.posts[i]['data']['reactions_count'] != null) {
            int likes = int.parse(
                postsList.posts[i]['data']['reactions_count']['like']);
            postsList.posts[i]['data']['reactions_count']['like'] =
                (likes + (event.isLiked ? 1 : -1)).toString();
          } else {
            postsList.posts[i]['data']
                ['reactions_count'] = {"like": event.isLiked ? "1" : "0"};
          }
        }
      }
      emit(postsList);
    });
    on<DeletePost>((event, emit) async {
      PostPageLoadedState postsList = (state as PostPageLoadedState);
      String postId = event.postId;
      for (int i = 0; i < postsList.posts.length; i++) {
        if (postsList.posts[i]['data']['id'] == postId) {
          postsList.posts.removeAt(i);
          emit(PostPageLoadedState(postsList.posts, postsList.limitReached,
              postsList.nextCursor, postsList.ttl, postsList.lastLoadedTime));
        }
      }
    });
  }
}

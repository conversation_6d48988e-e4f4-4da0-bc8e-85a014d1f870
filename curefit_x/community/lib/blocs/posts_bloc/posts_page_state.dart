abstract class PostPageState {
  PostPageState() : super();
}

class InitialPostPageState extends PostPageState {
  InitialPostPageState() : super();
}

class PostPageLoadedState extends PostPageState {
  List<dynamic> posts;
  bool limitReached;
  String? nextCursor;
  int ttl;
  DateTime lastLoadedTime;

  PostPageLoadedState(this.posts, this.limitReached, this.nextCursor, this.ttl,
      this.lastLoadedTime)
      : super();

  PostPageLoadedState copyFrom(
      List<dynamic> posts, bool limitReached, String? nextCursor) {
    this.posts.addAll(posts);
    return PostPageLoadedState(
        this.posts, limitReached, nextCursor, ttl, lastLoadedTime);
  }
}

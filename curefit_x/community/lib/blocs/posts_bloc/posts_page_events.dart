import 'package:community/UI/CommonWidgets/poll_creation_widget.dart';

abstract class PostPageEvent {
  PostPageEvent() : super();
}

class FetchPosts extends PostPageEvent {
  bool isRefresh;
  FetchPosts({this.isRefresh = false}) : super();
}

class ReactionPost extends PostPageEvent {
  String postId;
  bool isLiked;
  ReactionPost(this.postId, this.isLiked) : super();
}

class PollInPostPage extends PostPageEvent {
  String postId;
  PollData pollData;
  PollInPostPage(this.postId, this.pollData) : super();
}

class DeletePost extends PostPageEvent {
  String postId;
  DeletePost(this.postId) : super();
}

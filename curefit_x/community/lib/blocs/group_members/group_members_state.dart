
import 'package:community/blocs/group_members/group_members_models.dart';

abstract class GroupMembersState {
  GroupMembersState(): super();
}

class IdleGroupMembersState extends GroupMembersState{
  IdleGroupMembersState() : super();
}


class LoadingGroupMembersState extends GroupMembersState{
  LoadingGroupMembersState() : super();
}

class LoadedGroupMembersState extends GroupMembersState{
  GroupMembersData membersData;
  bool limitReached;
  LoadedGroupMembersState(this.membersData, this.limitReached) : super();

  LoadedGroupMembersState copyFrom(GroupMembersData membersData, bool limitReached) {
    this.membersData.members.addAll(membersData.members);
    this.membersData.experts.addAll(membersData.experts);
    // GroupMembersData data = new GroupMembersData(members: this.membersData.members, experts: this.membersData.experts, nextCursor: membersData.nextCursor);
    this.membersData.nextCursor = membersData.nextCursor;
    return LoadedGroupMembersState(this.membersData, limitReached);
  }

}

class NotLoadedGroupMembersState extends GroupMembersState{
  final String? errorMessage;

  NotLoadedGroupMembersState(this.errorMessage) : super();

}


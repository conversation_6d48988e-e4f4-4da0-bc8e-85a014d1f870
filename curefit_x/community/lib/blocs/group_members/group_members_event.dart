
abstract class GroupMembersEvent{
  GroupMembersEvent(): super();
}

class LoadingGroupMembers extends GroupMembersEvent{
  LoadingGroupMembers(): super();

  // @override
  // String toString() => 'LoadingGroupMembers';
}

// class LoadedGroupMembers extends GroupMembersEvent{
//   String groupId;
//   LoadedGroupMembers(this.groupId): super();
//
//   @override
//   String toString() => 'LoadedGroupMembers';
// }

// class FetchNextWidgets extends GroupMembersEvent {
//   String groupId;
//   FetchNextWidgets(this.groupId): super();
// }

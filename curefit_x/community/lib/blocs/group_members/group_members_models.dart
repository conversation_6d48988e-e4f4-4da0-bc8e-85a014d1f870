import 'package:common/action/action_handler.dart' as action_handler;
import 'package:common/constants/action_constants.dart';

class GroupMembersData {
  final List<Member> members;
  final List<Member> experts;
  String? nextCursor;

  GroupMembersData(
      {required this.members, required this.experts, this.nextCursor});
}

class Member {
  String name;
  String? tag;
  String? imageUrl;
  String? socialUserId;
  action_handler.Action action;

  Member(this.name, this.imageUrl, this.tag, this.action, this.socialUserId);

  factory Member.fromJson(dynamic memberData) {
    action_handler.Action profileAction = action_handler.Action(
        type: ActionTypes.NAVIGATION,
        url:
            "curefit://social_user_profile?cultUserId=${memberData['socialUserId']}");
    return Member(memberData['name'], memberData['avatarUrl'],
        memberData['expertType'], profileAction, memberData['socialUserId']);
  }
}

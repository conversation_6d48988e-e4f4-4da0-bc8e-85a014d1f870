import 'package:community/blocs/group_members/group_members_event.dart';
import 'package:community/blocs/group_members/group_members_models.dart';
import 'package:community/blocs/group_members/group_members_state.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:community/network/group_landing_repository.dart';

class GroupMembersBloc extends Bloc<GroupMembersEvent, GroupMembersState> {
  GroupLandingRepository groupLandingRepository;
  String groupId;

  GroupMembersBloc(this.groupLandingRepository, this.groupId)
      : super(IdleGroupMembersState()) {
    on<LoadingGroupMembers>((event, emit) async {
      if (state is IdleGroupMembersState) {
        try {
          emit(LoadingGroupMembersState());
          final response =
              await groupLandingRepository.getMembersList(groupId, null);
          if (response == null) {
            emit(NotLoadedGroupMembersState("Null Response"));
          }
          List<Member> members = response['members'] != null
              ? response['members']
                  .map<Member>((e) => Member.fromJson(e))
                  .toList()
              : [];
          List<Member> experts = response['experts'] != null
              ? response['experts']
                  .map<Member>((e) => Member.fromJson(e))
                  .toList()
              : [];
          String? nextCursor = response["nextCursor"];
          GroupMembersData groupMembersData = GroupMembersData(
              members: members, experts: experts, nextCursor: nextCursor);
          emit(LoadedGroupMembersState(groupMembersData, nextCursor == null));
        } on Exception {
          GroupMembersData blankData =
              GroupMembersData(members: [], experts: [], nextCursor: null);
          emit(LoadedGroupMembersState(blankData, false));
        }
      } else if (state is LoadedGroupMembersState) {
        try {
          if ((state as LoadedGroupMembersState).membersData.nextCursor ==
              null) {
            emit(state);
            return;
          }
          final response = await groupLandingRepository.getMembersList(groupId,
              (state as LoadedGroupMembersState).membersData.nextCursor);
          if (response == null) {
            emit(NotLoadedGroupMembersState("Null Response"));
          }
          List<Member> members = response['members'] != null
              ? response['members']
                  .map<Member>((e) => Member.fromJson(e))
                  .toList()
              : [];
          List<Member> experts = response['experts'] != null
              ? response['experts']
                  .map<Member>((e) => Member.fromJson(e))
                  .toList()
              : [];
          String? nextCursor = response["nextCursor"];
          GroupMembersData groupMembersData = GroupMembersData(
              members: members, experts: experts, nextCursor: nextCursor);
          emit((state as LoadedGroupMembersState)
              .copyFrom(groupMembersData, nextCursor == null));
        } on Exception {
          GroupMembersData blankData =
              GroupMembersData(members: [], experts: [], nextCursor: null);
          emit((state as LoadedGroupMembersState).copyFrom(blankData, true));
        }
      }
    });
  }

  // @override
  // Stream<GroupMembersState> mapEventToState(GroupMembersEvent event) async* {
  //   if(event is LoadingGroupMembers) {
  //     yield* getMembersData();
  //   }
  // }

  // Stream<GroupMembersState> getMembersData() async* {
  //   yield LoadingGroupMembersState();
  //   try {
  //     final response = await groupLandingRepository.getMembersList();
  //     if (response == null) {
  //       yield NotLoadedGroupMembersState("Null Response");
  //     }
  //     List<Member> members = response['members'].map<Member>((e) => Member.fromJson(e)).toList();
  //     List<Member> experts = response['experts'].map<Member>((e) => Member.fromJson(e)).toList();
  //     GroupMembersData groupMembersData = GroupMembersData(members: members, experts: experts);
  //     yield LoadedGroupMembersState(groupMembersData);
  //   } on NetworkException catch (exception){
  //     yield NotLoadedGroupMembersState(exception.subTitle);
  //   }
  // }

}

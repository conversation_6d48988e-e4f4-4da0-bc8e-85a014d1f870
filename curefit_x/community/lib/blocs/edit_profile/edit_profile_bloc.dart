import 'package:community/blocs/edit_profile/edit_profile_events.dart';
import 'package:community/blocs/edit_profile/edit_profile_state.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:community/network/edit_profile_repository.dart';

import 'edit_profile_models.dart';
import 'package:enum_to_string/enum_to_string.dart';

class EditProfileBloc extends Bloc<EditProfilePageEvent, EditProfilePageState> {
  EditProfileRepository repository;

  EditProfileBloc(this.repository) : super(InitialEditProfilePageState()) {
    on<FetchEditProfileData>((event, emit) async {
      try {
        bool isCreateProfile = event.isCreateProfile;
        var res = await repository.editProfile(isCreateProfile.toString());
        EditProfileData data = EditProfileData.fromJson(res);
        emit(EditProfilePageLoadedState(data));
      } on Exception {
        emit(EditProfilePageLoadError());
      }
    });

    on<UpdateSquadData>((event, emit) async {
      if (state is EditProfilePageLoadedState) {
        try {
          EditProfileData profileData =
              (state as EditProfilePageLoadedState).data;
          profileData.squadVisibilityData = event.data;
          emit(EditProfilePageLoadedState(profileData));
        } on Exception {
          //TODO: report firebase error
        }
      }
    });

    on<UpdateProfileVisibility>((event, emit) async {
      if (state is EditProfilePageLoadedState) {
        try {
          EditProfileData profileData =
              (state as EditProfilePageLoadedState).data;
          profileData.privacy = EnumToString.convertToString(event.visibility);
          emit(EditProfilePageLoadedState(profileData));
        } on Exception {
          //TODO: report firebase error
        }
      }
    });
  }
}

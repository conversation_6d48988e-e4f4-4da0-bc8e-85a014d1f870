
import 'edit_profile_models.dart';

abstract class EditProfilePageState{
  EditProfilePageState(): super();
}

class InitialEditProfilePageState extends EditProfilePageState {
  InitialEditProfilePageState(): super();
}

class EditProfilePageLoadingState extends EditProfilePageState {
  EditProfilePageLoadingState(): super();
}

class EditProfilePageLoadedState extends EditProfilePageState {
  EditProfileData data;
  EditProfilePageLoadedState(this.data) : super();
}

class EditProfilePageLoadError extends EditProfilePageState{
  EditProfilePageLoadError(): super();
}

import 'package:community/blocs/edit_profile/edit_profile_models.dart';
import 'package:core/model/profile_visibility_enum.dart';

abstract class EditProfilePageEvent {
  EditProfilePageEvent() : super();
}

class FetchEditProfileData extends EditProfilePageEvent {
  bool isCreateProfile;
  FetchEditProfileData(this.isCreateProfile) : super();
}

class UpdateSquadData extends EditProfilePageEvent {
  ProfileSquadData data;
  UpdateSquadData(this.data) : super();
}

class UpdateProfileVisibility extends EditProfilePageEvent {
  ProfileVisibilityType visibility;
  UpdateProfileVisibility(this.visibility) : super();
}

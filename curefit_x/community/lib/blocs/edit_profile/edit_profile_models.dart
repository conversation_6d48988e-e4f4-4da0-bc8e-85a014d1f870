import 'package:core/model/squad_visibility_type.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:support/action/action_handler.dart';

class EditProfileData {
  bool isCreateProfile;
  String name;
  String? dob;
  String? gender;
  String? height;
  String? weight;
  String? bio;
  int userProfileId;
  String userId;
  bool nameEditable;
  String? instagramHandle;
  String? profilePictureUrl;
  String privacy;
  List<ProfileTag> tags;
  String? publicProfileMessage;
  String? privateProfileMessage;
  ProfileSquadData? squadVisibilityData;
  ProfileVisibilityInfoModel? profileVisibilityInfoModel;
  IssueDetailAction? minorAgeRaiseTicketAction;

  EditProfileData({
    required this.isCreateProfile,
    required this.name,
    required this.dob,
    required this.gender,
    required this.height,
    required this.weight,
    required this.bio,
    required this.userProfileId,
    required this.userId,
    required this.nameEditable,
    this.instagramHandle,
    this.profilePictureUrl,
    required this.privacy,
    required this.tags,
    this.publicProfileMessage,
    this.privateProfileMessage,
    this.squadVisibilityData,
    this.minorAgeRaiseTicketAction,
    this.profileVisibilityInfoModel,
  });

  factory EditProfileData.fromJson(dynamic data) {
    return EditProfileData(
        isCreateProfile: data["isCreateProfile"],
        name: data?["name"],
        dob: data?["dob"],
        gender: data?["gender"],
        height: data?["height"],
        weight: data?["weight"],
        bio: data?["userProfile"]?["bio"] ?? "",
        userProfileId: data?["userProfile"]?["id"],
        userId: data?["userProfile"]?["userId"],
        nameEditable: data?["nameEditable"],
        instagramHandle: data?["instagramHandle"],
        profilePictureUrl: data?["profilePictureUrl"],
        privacy: data?["userProfile"]?["visibility"],
        tags: data?["userProfile"]?["tags"]
            ?.map<ProfileTag>((entry) => ProfileTag.fromJson(entry))
            .toList(),
        publicProfileMessage: data?["publicProfileMessage"],
        privateProfileMessage: data?["privateProfileMessage"],
        squadVisibilityData: data?["squadData"] != null
            ? ProfileSquadData.fromJson(data?["squadData"])
            : null,
        profileVisibilityInfoModel: data?["profileVisibilityInfo"] != null
            ? ProfileVisibilityInfoModel.fromJson(
                data?["profileVisibilityInfo"])
            : null,
        minorAgeRaiseTicketAction: data?['minorAgeRaiseTicketAction'] != null
            ? IssueDetailAction.fromJson(data?['minorAgeRaiseTicketAction'])
            : null);
  }
}

Map getEditProfileJsonData(EditProfileData data) {
  List<ProfileTag> tags = data.tags;
  dynamic tagsJson = [];
  for (var tag in tags) {
    Map<String, dynamic> tagJson = {
      "id": tag.id,
      "code": tag.code,
      "mapped": tag.mapped,
      "displayName": tag.displayName
    };
    tagsJson.add(tagJson);
  }

  Map<String, dynamic> profileJsonData = {
    "id": data.userProfileId,
    "userId": data.userId,
    "visibility": data.privacy,
    "whoCanInvite": EnumToString.convertToString(
        data.squadVisibilityData?.squadVisibility ?? SquadVisibilityType.ANY),
    "tags": tagsJson,
    "bio": data.bio
  };

  Map<String, dynamic> jsonData = {
    "name": data.name,
    "instagramHandle": data.instagramHandle,
    "profilePictureUrl": data.profilePictureUrl,
    "userProfile": profileJsonData,
    "dob": data.dob,
    "gender": data.gender,
    "height": data.height,
    "weight": data.weight,
  };

  return jsonData;
}

class ProfileTag {
  int id;
  String code;
  String displayName;
  bool mapped;

  ProfileTag(
      {required this.id,
      required this.code,
      required this.displayName,
      required this.mapped});

  factory ProfileTag.fromJson(dynamic data) {
    return ProfileTag(
      id: data["id"],
      code: data["code"],
      displayName: data["displayName"],
      mapped: data["mapped"],
    );
  }
}

class ProfileSquadData {
  SquadVisibilityType squadVisibility;
  String? anySquadState;
  String? phoneSquadState;
  String? squadVisibiltyTitle;
  String anySquadTitle;
  String phoneSquadTitle;
  String? anySquadSubText;
  String? phoneSquadSubText;

  ProfileSquadData({
    required this.squadVisibility,
    this.anySquadState,
    this.phoneSquadState,
    this.squadVisibiltyTitle,
    required this.anySquadTitle,
    required this.phoneSquadTitle,
    this.anySquadSubText,
    this.phoneSquadSubText,
  });

  factory ProfileSquadData.fromJson(dynamic data) {
    return ProfileSquadData(
      squadVisibility: EnumToString.fromString(
              SquadVisibilityType.values, data?['squadVisibility'] ?? "ANY") ??
          SquadVisibilityType.ANY,
      anySquadState: data?["anySquadState"],
      phoneSquadState: data?["phoneSquadState"],
      squadVisibiltyTitle: data?["squadVisibiltyTitle"] ?? "Squad Visibility",
      anySquadTitle: data?["anySquadTitle"] ?? "Any cult member can invite me",
      phoneSquadTitle: data?["phoneSquadTitle"] ?? "Only with phone number",
      anySquadSubText: data?["anySquadSubText"],
      phoneSquadSubText: data?["phoneSquadSubText"],
    );
  }
}

class ProfileVisibilityInfoModel {
  String? headingText;
  String? headingSubText;
  List<ProfileVisibilityFiltersModel> profileVisibilityFiltersList;

  ProfileVisibilityInfoModel({
    this.headingText,
    this.headingSubText,
    required this.profileVisibilityFiltersList,
  });

  factory ProfileVisibilityInfoModel.fromJson(Map<String, dynamic> json) =>
      ProfileVisibilityInfoModel(
        headingText: json["headingText"],
        headingSubText: json["headingSubText"],
        profileVisibilityFiltersList:
            json["profileVisibilityFiltersList"] == null
                ? []
                : List<ProfileVisibilityFiltersModel>.from(
                    json["profileVisibilityFiltersList"]!
                        .map((x) => ProfileVisibilityFiltersModel.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "headingText": headingText,
        "headingSubText": headingSubText,
        "profileVisibilityFiltersList": profileVisibilityFiltersList == null
            ? []
            : List<dynamic>.from(
                profileVisibilityFiltersList.map((x) => x.toJson())),
      };
}

class ProfileVisibilityFiltersModel {
  String text;
  String subText;
  String profileVisibility;

  ProfileVisibilityFiltersModel({
    required this.text,
    required this.subText,
    required this.profileVisibility,
  });

  factory ProfileVisibilityFiltersModel.fromJson(Map<String, dynamic> json) =>
      ProfileVisibilityFiltersModel(
        text: json["text"],
        subText: json["subText"],
        profileVisibility: json["profileVisibility"],
      );

  Map<String, dynamic> toJson() => {
        "text": text,
        "subText": subText,
        "profileVisibility": profileVisibility,
      };
}

import 'package:flutter_bloc/flutter_bloc.dart';

class PNTokenBloc extends Bloc<PNTokenEvent, PNTokenState> {
  String? token;

  PNTokenBloc() : super(InitialPNTokenState()) {
    on<UpdatePNTokenEvent>(((event, emit) {
      token = event.token;
      emit(UpdatePNTokenState(event.token));
    }));
  }
}

abstract class PNTokenEvent {
  PNTokenEvent() : super();
}

abstract class PNTokenState {
  PNTokenState() : super();
}

class InitialPNTokenState extends PNTokenState {
  InitialPNTokenState();
}

class UpdatePNTokenState extends PNTokenState {
  String token;
  UpdatePNTokenState(this.token);
}

class UpdatePNTokenEvent extends PNTokenEvent {
  String token;
  UpdatePNTokenEvent(this.token);
}

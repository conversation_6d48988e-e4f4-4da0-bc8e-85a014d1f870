abstract class AboutGroupPageEvent{
  AboutGroupPageEvent() : super();
}

class LoadingAboutGroupPage extends AboutGroupPageEvent{
  String groupId;
  LoadingAboutGroupPage(this.groupId): super();

  @override
  String toString() => 'LoadingAboutGroupPage';
}

class LoadedAboutGroupPage extends AboutGroupPageEvent{
  LoadedAboutGroupPage(): super();

  @override
  String toString() => 'LoadedAboutGroupPage';
}


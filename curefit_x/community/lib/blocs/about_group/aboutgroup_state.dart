
import 'package:community/blocs/about_group/aboutgroup_models.dart';

abstract class AboutGroupPageState{
  AboutGroupPageState();
}

class IdleAboutGroupPageState extends AboutGroupPageState {
  @override
  String toString() => 'IdleAboutGroupPage';
}

class LoadingAboutGroupPageState extends AboutGroupPageState{
  final bool showLoader;

  LoadingAboutGroupPageState(this.showLoader);

  @override
  String toString() => 'LoadingAboutGroupPage';
}

class LoadedAboutGroupPageState extends AboutGroupPageState{
  final AboutGroupData data;

  LoadedAboutGroupPageState(this.data);

  @override
  String toString() => 'LoadedAboutGroupPage';

}

class NotLoadedAboutGroupPageState extends AboutGroupPageState{
  final String? error;

  NotLoadedAboutGroupPageState(this.error);

  @override
  String toString() => 'NotLoadedAboutGroupPage';
}

import 'package:common/network/client.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:community/network/group_landing_repository.dart';
import 'package:community/blocs/about_group/aboutgroup_models.dart';
import 'package:community/blocs/about_group/aboutgroup_events.dart';
import 'package:community/blocs/about_group/aboutgroup_state.dart';

class AboutGroupPageBloc
    extends Bloc<AboutGroupPageEvent, AboutGroupPageState> {
  final GroupLandingRepository groupLandingRepository;

  AboutGroupPageBloc(this.groupLandingRepository)
      : super(IdleAboutGroupPageState()) {
    on<LoadingAboutGroupPage>((event, emit) async {
      emit(LoadingAboutGroupPageState(true));
      try {
        final response =
            await groupLandingRepository.getAboutGroup((event).groupId);
        if (response == null) {
          emit(NotLoadedAboutGroupPageState("Null Response"));
        }
        AboutGroupData aboutGroupPageData =
            AboutGroupData(widgets: response['widgets']);
        emit(LoadedAboutGroupPageState(aboutGroupPageData));
      } on NetworkException catch (exception) {
        emit(NotLoadedAboutGroupPageState(exception.subTitle));
      }
      // fetchData(event, emit);
    });
  }

  // @override
  // Stream<AboutGroupPageState> mapEventToState(AboutGroupPageEvent event) async* {
  //   if(event is LoadingAboutGroupPage ){
  //     yield* fetchData(event);
  //   }
  // }

  // Stream<AboutGroupPageState> fetchData(AboutGroupPageEvent event, Emitter<AboutGroupPageState> emit) async* {
  //   emit(LoadingAboutGroupPageState(true));
  //   try {
  //     final response = await groupLandingRepository.getAboutGroup((event as LoadingAboutGroupPage).groupId);
  //     if (response == null) {
  //       emit(NotLoadedAboutGroupPageState("Null Response"));
  //     }
  //     AboutGroupData aboutGroupPageData = AboutGroupData(widgets: response['widgets']);
  //     emit(LoadedAboutGroupPageState(aboutGroupPageData));
  //   } on NetworkException catch (exception){
  //     emit(NotLoadedAboutGroupPageState(exception.subTitle));
  //   }
  // }
}

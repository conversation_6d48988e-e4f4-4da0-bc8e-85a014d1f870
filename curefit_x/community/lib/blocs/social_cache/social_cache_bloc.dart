import 'package:community/blocs/social_cache/social_cache_events.dart';
import 'package:community/blocs/social_cache/social_cache_state.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class SocialCacheBloc extends Bloc<SocialCacheEvent, SocialCacheState> {
  SocialCacheBloc() : super(DisabledSocialCacheState()) {
    on<EnableSocialCacheEvent>((event, emit) async {
      emit(EnabledSocialCacheState(true, event.lastActivityTime));
    });

    on<DisableSocialCacheEvent>((event, emit) async {
      emit(DisabledSocialCacheState());
    });
  }
}

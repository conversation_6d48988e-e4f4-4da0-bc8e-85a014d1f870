
abstract class UserMediaPageState{
  UserMediaPageState(): super();

}

class InitialUserMediaPageState extends UserMediaPageState {
  InitialUserMediaPageState(): super();
}

class UserMediaPageLoadedState extends UserMediaPageState {
  bool limitReached;
  String? nextCursor;
  List<dynamic> media;
  UserMediaPageLoadedState(this.media, this.limitReached, this.nextCursor) : super();

  UserMediaPageLoadedState copyFrom(List<dynamic> media, bool limitReached, String? nextCursor) {
    this.media.addAll(media);
    return UserMediaPageLoadedState(this.media, limitReached, nextCursor);
  }

}

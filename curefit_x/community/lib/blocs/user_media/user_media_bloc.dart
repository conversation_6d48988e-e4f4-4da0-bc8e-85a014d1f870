import 'package:community/blocs/user_media/user_media_events.dart';
import 'package:community/blocs/user_media/user_media_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:community/network/social_user_media_repository.dart';

class UserMediaBloc extends Bloc<UserMediaPageEvent, UserMediaPageState> {
  SocialUserMediaRepository repository;
  String socialUserId;
  BuildContext context;

  UserMediaBloc(this.context, this.repository, this.socialUserId)
      : super(InitialUserMediaPageState()) {
    on<FetchNextWidgets>((event, emit) async {
      if (state is InitialUserMediaPageState) {
        try {
          var res = await repository.getUserMedia(context, socialUserId, null);
          final List<dynamic> media = res["multiMediaList"];
          String? nextCursor = res["nextCursor"];
          emit(UserMediaPageLoadedState(media, nextCursor == null, nextCursor));
        } on Exception {
          emit(UserMediaPageLoadedState([], true, null));
        }
      } else if (state is UserMediaPageLoadedState) {
        try {
          var res = await repository.getUserMedia(context, socialUserId,
              (state as UserMediaPageLoadedState).nextCursor);
          final List<dynamic> media = res["multiMediaList"];
          String? nextCursor = res["nextCursor"];
          emit((state as UserMediaPageLoadedState)
              .copyFrom(media, nextCursor == null, nextCursor));
        } on Exception {
          emit((state as UserMediaPageLoadedState).copyFrom([], true, null));
        }
      }
    });
  }
}

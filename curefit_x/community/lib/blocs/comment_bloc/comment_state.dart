import 'package:community/UI/Screens/post_detail/widgets/comment_list_widget.dart';

abstract class CommentState {
  CommentState() : super();
}

class InitialCommentState extends CommentState {
  InitialCommentState() : super();
}

class ReplyCommentState extends CommentState {
  String? commentId;
  String? parentCommentId;
  String postId;
  String replyTo;
  String authorId;

  ReplyCommentState(this.postId,
      {this.commentId,
      this.parentCommentId,
      required this.replyTo,
      required this.authorId})
      : super();
}

class CommentPostingState extends CommentState {
  String? commentId;
  String? parentCommentId;
  String postId;

  CommentPostingState(this.postId, {this.commentId, this.parentCommentId})
      : super();
}

class CommentPostedState extends CommentState {
  String commentId;
  String? parentCommentId;
  String postId;
  Comment comment;
  dynamic rawComment;

  CommentPostedState(this.postId, this.comment,
      {required this.commentId, required this.parentCommentId, this.rawComment})
      : super();
}

class NotPostedCommentState extends CommentState {
  NotPostedCommentState() : super();
}

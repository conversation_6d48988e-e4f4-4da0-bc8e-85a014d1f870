import 'dart:developer';

import 'package:common/network/client.dart';
import 'package:community/network/post_page_repository.dart';
import 'package:community/UI/Screens/post_detail/widgets/comment_list_widget.dart';
import 'package:community/blocs/comment_bloc/comment_events.dart';
import 'package:community/blocs/comment_bloc/comment_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class CommentBloc extends Bloc<CommentEvent, CommentState> {
  final PostPageRepository postPageRepository;
  BuildContext context;
  CommentBloc(this.context, this.postPageRepository)
      : super(InitialCommentState()) {
    on<ResetReply>((event, emit) async {
      emit(InitialCommentState());
    });
    on<ReplyToComment>((event, emit) async {
      emit(ReplyCommentState(event.postId,
          commentId: event.commentId,
          parentCommentId: event.parentCommentId,
          replyTo: event.replyTo,
          authorId: event.authorId));
    });
    on<CommentPostingEvent>((event, emit) async {
      emit(CommentPostingState(event.postId,
          commentId: event.commentId, parentCommentId: event.parentCommentId));
      try {
        print("finalComment:${event.comment}");
        dynamic response = await postPageRepository.postComment(
            context,
            event.postId,
            event.comment,
            event.commentId,
            event.parentCommentId);
        final Comment comment = Comment.fromJson(response);
        emit(CommentPostedState(event.postId, comment,
            commentId: comment.commentId,
            parentCommentId: comment.parentCommentId,
            rawComment: response));
      } on NetworkException catch (exception) {
        emit(NotPostedCommentState());
        log(exception.subTitle!);
      }
    });
  }
}

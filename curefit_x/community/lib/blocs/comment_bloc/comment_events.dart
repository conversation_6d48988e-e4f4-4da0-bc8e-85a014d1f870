import 'package:community/UI/Screens/post_detail/widgets/comment_list_widget.dart';

abstract class CommentEvent {
  CommentEvent() : super();
}

class ResetReply extends CommentEvent {
  ResetReply() : super();
}

class ReplyToComment extends CommentEvent {
  String commentId;
  String? parentCommentId;
  String postId;
  String replyTo;
  String authorId;

  ReplyToComment(this.postId,
      {required this.commentId,
      required this.parentCommentId,
      required this.replyTo,
      required this.authorId})
      : super();
}

class CommentPostingEvent extends CommentEvent {
  String? commentId;
  String? parentCommentId;
  String postId;
  String comment;

  CommentPostingEvent(this.postId,
      {this.commentId, this.parentCommentId, required this.comment})
      : super();
}

class CommentPostedEvent extends CommentEvent {
  String? commentId;
  String? parentCommentId;
  String postId;
  Comment comment;

  CommentPostedEvent(this.postId,
      {this.commentId, this.parentCommentId, required this.comment})
      : super();
}

import 'package:community/network/post_page_repository.dart';
import 'package:community/blocs/mentions_bloc/mention_events.dart';
import 'package:community/blocs/mentions_bloc/mention_models.dart';
import 'package:community/blocs/mentions_bloc/mention_states.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class MentionBloc extends Bloc<MentionEvent, MentionsState> {
  final PostPageRepository postPageRepository;

  MentionBloc(this.postPageRepository) : super(EmptyMentionsState()) {
    on<EmptyTrigger>((event, emit) async {
      emit(EmptyMentionsState());
    });

    on<SearchMentions>((event, emit) async {
      emit(LoadingMentionsState());
      dynamic response =
          await postPageRepository.getUserMentions((event).triggerWord, (event).groupId);
      List<UserMention> mentions = response['members']
          .map<UserMention>((e) => UserMention.fromJson(e))
          .toList();
      if (mentions.isNotEmpty) {
        emit(LoadedMentionsState(mentions));
      } else {
        emit(EmptyMentionsState());
      }
    });
  }
}

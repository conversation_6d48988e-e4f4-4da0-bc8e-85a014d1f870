import 'package:community/blocs/mentions_bloc/mention_models.dart';

abstract class MentionsState {
  MentionsState(): super();
}

class LoadingMentionsState extends MentionsState {
  LoadingMentionsState(): super();
}

class LoadedMentionsState extends MentionsState {
  List<UserMention> mentions;

  LoadedMentionsState(this.mentions): super();
}

class EmptyMentionsState extends MentionsState {
  EmptyMentionsState(): super();
}
import 'package:community/UI/CommonWidgets/poll_creation_widget.dart';

abstract class PostDetailEvent {
  PostDetailEvent() : super();
}

class FetchWidgets extends PostDetailEvent {
  String postId;
  bool isRefresh;
  FetchWidgets(this.postId, {this.isRefresh = false}) : super();

  @override
  String toString() => 'FetchWidgets';
}

class LoadedPostDetailEvent extends PostDetailEvent {
  LoadedPostDetailEvent() : super();

  @override
  String toString() => 'LoadedPostDetailEvent';
}

class ReportPost extends PostDetailEvent {
  String postId;
  ReportPost(this.postId);
}

class ReactionOnComment extends PostDetailEvent {
  String commentId;
  bool isLiked;
  ReactionOnComment(this.commentId, this.isLiked) : super();
}

class ReactionPostInPostDetail extends PostDetailEvent {
  String postId;
  bool isLiked;
  ReactionPostInPostDetail(this.postId, this.isLiked) : super();
}

class PollInPostDetail extends PostDetailEvent {
  String postId;
  PollData pollData;
  PollInPostDetail(this.postId, this.pollData) : super();
}

class DeleteComment extends PostDetailEvent {
  String commentId;
  DeleteComment(this.commentId) : super();
}

class AddComment extends PostDetailEvent {
  dynamic comment;
  String? parentCommentId;
  AddComment(this.comment, {this.parentCommentId}) : super();
}

import 'package:common/network/client.dart';
import 'package:community/network/post_page_repository.dart';
import 'package:community/blocs/post_detail/post_detail_events.dart';
import 'package:community/blocs/post_detail/post_detail_models.dart';
import 'package:community/blocs/post_detail/post_detail_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class PostDetailBloc extends Bloc<PostDetailEvent, PostDetailState> {
  final PostPageRepository postPageRepository;
  BuildContext context;
  PostDetailBloc(this.context, this.postPageRepository)
      : super(IdlePostDetailState()) {
    on<FetchWidgets>((event, emit) async {
      if (state is IdlePostDetailState || event.isRefresh) {
        emit(LoadingPostDetailState());
        try {
          final response = await postPageRepository.getPostDetailedPage(
              context, (event).postId, null);
          if (response == null) {
            emit(NotLoadedPostDetailState("Null Response"));
            return;
          }
          String? nextCursor = response['nextCursor'];
          PostDetailData postDetailData = PostDetailData(
              response['widgets'],
              response['actions'],
              (event).postId,
              nextCursor == null,
              nextCursor);
          emit(LoadedPostDetailState(postDetailData));
        } on NetworkException catch (exception) {
          emit(NotLoadedPostDetailState(exception.subTitle));
        }
      } else if (state is LoadedPostDetailState) {
        try {
          final response = await postPageRepository.getPostDetailedPage(context,
              (event).postId, (state as LoadedPostDetailState).data.nextCursor);
          String? nextCursor = response['nextCursor'];
          PostDetailData newPostDetailData = (state as LoadedPostDetailState)
              .data
              .copyFrom(response['widgets'], response['actions'],
                  (event).postId, nextCursor == null, nextCursor);
          emit(LoadedPostDetailState(newPostDetailData));
        } on NetworkException catch (exception) {
          emit(NotLoadedPostDetailState(exception.subTitle));
        }
      }
    });
    on<ReportPost>((event, emit) async {
      final response = await postPageRepository.reportPost(event.postId);
      emit(state);
    });
    on<ReactionPostInPostDetail>((event, emit) async {
      PostDetailData postDetailList = (state as LoadedPostDetailState).data;
      if(postDetailList.widgets[0]['widgetType'] == 'COMMUNITY_POST' &&
          postDetailList.widgets[0]['data']['id'] == event.postId) {
        if(event.isLiked) {
          postDetailList.widgets[0]['data']['my_reactions'] = "Liked";
        } else {
          postDetailList.widgets[0]['data']['my_reactions'] = null;
        }
        if (postDetailList.widgets[0]['data']['reactions_count'] != null) {
          int likes = int.parse(
              postDetailList.widgets[0]['data']['reactions_count']['like']);
          postDetailList.widgets[0]['data']['reactions_count']['like'] =
              (likes + (event.isLiked ? 1 : -1)).toString();
        } else {
          postDetailList.widgets[0]['data']
          ['reactions_count'] = {"like": event.isLiked ? "1" : "0"};
        }
      }
      emit(LoadedPostDetailState(postDetailList));
    });
    on<PollInPostDetail>((event, emit) async {
      PostDetailData postDetailList = (state as LoadedPostDetailState).data;
      if( postDetailList.widgets[0]['widgetType'] == 'COMMUNITY_POST' &&
          postDetailList.widgets[0]['data']['id'] == event.postId ) {
        postDetailList.widgets[0]['data']['poll']['vote_count'] = event.pollData.voteCount;
        for(int i = 0; i < event.pollData.options.length; i++) {
          postDetailList.widgets[0]['data']['poll']['options'][i]['vote_count'] =
              event.pollData.options[i].voteCount;
          postDetailList.widgets[0]['data']['poll']['options'][i]['voted_by_me'] =
              event.pollData.options[i].votedByMe;
        }
      }
      emit(LoadedPostDetailState(postDetailList));
    });
    on<ReactionOnComment>((event, emit) async {
      PostDetailData commentsList = (state as LoadedPostDetailState).data;
      String postId = event.commentId;
      for (int i = 0; i < commentsList.widgets.length; i++) {
        if (commentsList.widgets[i]['data']['id'] == postId) {
          if (event.isLiked) {
            // it shouldnt be null, so making it "Liked"
            commentsList.widgets[i]['data']['my_reactions'] = "Liked";
          } else {
            commentsList.widgets[i]['data']['my_reactions'] = null;
          }
          if (commentsList.widgets[i]['data']['reactions_count'] != null) {
            int likes = int.parse(
                commentsList.widgets[i]['data']['reactions_count']['like']);
            commentsList.widgets[i]['data']['reactions_count']['like'] =
                (likes + (event.isLiked ? 1 : -1)).toString();
          } else {
            commentsList.widgets[i]['data']
                ['reactions_count'] = {"like": event.isLiked ? "1" : "0"};
          }
        }
      }
      emit(LoadedPostDetailState(commentsList));
    });
    on<DeleteComment>((event, emit) async {
      PostDetailData commentsList = (state as LoadedPostDetailState).data;
      String postId = event.commentId;
      for (int i = 0; i < commentsList.widgets.length; i++) {
        if (commentsList.widgets[i]['data']['id'] == postId) {
          commentsList.widgets.removeAt(i);
          emit(LoadedPostDetailState(commentsList));
        }
      }
    });
    on<AddComment>((event, emit) async {
      PostDetailData commentsList = (state as LoadedPostDetailState).data;
      commentsList.widgets.insert(1, event.comment);
      emit(LoadedPostDetailState(commentsList));
    });
  }
}

import 'package:community/blocs/post_detail/post_detail_models.dart';

abstract class PostDetailState {
  PostDetailState() : super();
}

class IdlePostDetailState extends PostDetailState {
  @override
  String toString() => 'IdlePostDetailPage';
}

class LoadingPostDetailState extends PostDetailState {
  LoadingPostDetailState();

  @override
  String toString() => 'LoadingPostDetailPage';
}

class LoadedPostDetailState extends PostDetailState {
  PostDetailData data;

  LoadedPostDetailState(this.data);

  @override
  String toString() => 'LoadedPostDetailPage';
}

class NotLoadedPostDetailState extends PostDetailState {
  final String? error;

  NotLoadedPostDetailState(this.error);

  @override
  String toString() => 'LoadedPostDetailPage';
}

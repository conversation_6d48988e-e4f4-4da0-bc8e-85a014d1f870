class PostDetailData {
  String postId;
  final List widgets;
  final List actions;
  String? nextCursor;
  bool limitReached;

  PostDetailData(this.widgets, this.actions, this.postId, this.limitReached, this.nextCursor);

  PostDetailData copyFrom(List<dynamic> widgets, List<dynamic> actions, String postId,
      bool limitReached, String? nextCursor) {
    this.widgets.addAll(widgets);
    return PostDetailData(this.widgets, actions, postId, limitReached, nextCursor);
  }
}


abstract class CultBuddyPageState{
  CultBuddyPageState(): super();

}

class InitialCultBuddyPageState extends CultBuddyPageState {
  InitialCultBuddyPageState(): super();
}

class CultBuddyPageLoadedState extends CultBuddyPageState {
  final List<dynamic> media;
  CultBuddyPageLoadedState(this.media) : super();

  CultBuddyPageLoadedState copyFrom(List<dynamic> media) {
    this.media.addAll(media);

    return CultBuddyPageLoadedState(this.media);
  }

}

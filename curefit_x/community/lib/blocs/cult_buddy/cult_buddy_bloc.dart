import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:community/network/cult_buddy_repository.dart';

import 'cult_buddy_events.dart';
import 'cult_buddy_state.dart';

class CultBuddyBloc extends Bloc<CultBuddyPageEvent, CultBuddyPageState> {
  CultBuddyRepository repository;

  CultBuddyBloc(this.repository) : super(InitialCultBuddyPageState()) {
    on<FetchNextWidgets>((event, emit) async {
      if (state is InitialCultBuddyPageState) {
        try {
          var res = await repository.cultBuddy();

          final List<dynamic> media = res["imageUrls"];
          emit(CultBuddyPageLoadedState(media));
        } on Exception {
          emit(CultBuddyPageLoadedState([]));
        }
      } else if (state is CultBuddyPageLoadedState) {
        try {
          var res = await repository.cultBuddy();
          final List<dynamic> media = res["imageUrls"];
          emit((state as CultBuddyPageLoadedState).copyFrom(media));
        } on Exception {
          emit((state as CultBuddyPageLoadedState).copyFrom([]));
        }
      }
    });
  }
}

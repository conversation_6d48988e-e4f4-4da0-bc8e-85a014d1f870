import 'package:common/network/client.dart';
import 'package:community/blocs/ftu_landing/ftu_landing_models.dart';
import 'package:community/blocs/ftu_landing/ftu_landing_events.dart';
import 'package:community/blocs/ftu_landing/ftu_landing_state.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:community/network/ftu_landing_repository.dart';

class FTULandingBloc extends Bloc<FTULandingEvent, FTULandingState> {
  final FTULandingRepository ftuLandingRepository;

  FTULandingBloc(this.ftuLandingRepository) : super(IdleFTULandingState()) {
    on<LoadingFTULanding>((event, emit) async {
      emit(LoadingFTULandingState(true));
      try {
        final response = await ftuLandingRepository.getPageData();
        if (response == null) {
          emit(NotLoadedFTULandingState("Null Response"));
        }
        FTULandingData ftuLandingData =
            FTULandingData(widgets: response['widgets']);
        emit(LoadedFTULandingState(ftuLandingData));
      } on NetworkException catch (exception) {
        emit(NotLoadedFTULandingState(exception.subTitle));
      }
    });
  }
}

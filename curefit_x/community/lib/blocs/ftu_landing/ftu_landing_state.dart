import 'package:community/blocs/ftu_landing/ftu_landing_models.dart';

abstract class FTULandingState{
  FTULandingState();
}

class IdleFTULandingState extends FTULandingState {
  @override
  String toString() => 'IdleFTULanding';
}

class LoadingFTULandingState extends FTULandingState{
  final bool showLoader;

  LoadingFTULandingState(this.showLoader);

  @override
  String toString() => 'LoadingFTULanding';
}

class LoadedFTULandingState extends FTULandingState{
  final FTULandingData data;

  LoadedFTULandingState(this.data);

  @override
  String toString() => 'LoadedFTULanding';

}

class NotLoadedFTULandingState extends FTULandingState{
  final String? error;

  NotLoadedFTULandingState(this.error);

  @override
  String toString() => 'LoadedFTULanding';
}

import 'package:community/network/post_page_repository.dart';
import 'package:community/blocs/reaction_bloc/post_reaction_event.dart';
import 'package:community/blocs/reaction_bloc/post_reaction_state.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class PostReactionBloc extends Bloc<PostReactionEvent, PostReactionState> {
  final PostPageRepository postPageRepository;

  PostReactionBloc(this.postPageRepository)
      : super(InitialPostReactionState()) {
    on<SendReaction>((event, emit) async {
      emit(PostingReactionState(event.activityId));
      try {
        var response = await postPageRepository.postReaction(
            event.activityId, event.status);
        emit(PostedReactionState(event.activityId));
      } catch (error) {
        emit(NotPostedReactionState(event.activityId));
      }
    });
  }
}

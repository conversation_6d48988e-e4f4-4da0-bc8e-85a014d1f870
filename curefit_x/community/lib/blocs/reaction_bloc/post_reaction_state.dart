abstract class PostReactionState {
  PostReactionState() : super();
}

class InitialPostReactionState extends PostReactionState {
  InitialPostReactionState() : super();
}

class PostingReactionState extends PostReactionState {
  String activityId;
  PostingReactionState(this.activityId) : super();
}

class PostedReactionState extends PostReactionState {
  String activityId;
  PostedReactionState(this.activityId) : super();
}

class NotPostedReactionState extends PostReactionState {
  String activityId;
  NotPostedReactionState(this.activityId) : super();
}

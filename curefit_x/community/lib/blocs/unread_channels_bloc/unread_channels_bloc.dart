// States
import 'package:flutter_bloc/flutter_bloc.dart';

abstract class UnreadChanelsState {
  UnreadChanelsState() : super();
}

class UpdatedUnreadChanelsState extends UnreadChanelsState {
  int unreadChannels;
  UpdatedUnreadChanelsState(this.unreadChannels) : super();
}

// Events
abstract class UnreadChanelsEvent {
  UnreadChanelsEvent() : super();
}

class UpdateUnreadChannels extends UnreadChanelsEvent {
  int unreadChannels;
  UpdateUnreadChannels(this.unreadChannels) : super();
}

class UnreadChanelsBloc extends Bloc<UnreadChanelsEvent, UnreadChanelsState> {
  UnreadChanelsBloc() : super(UpdatedUnreadChanelsState(0)) {
    on<UpdateUnreadChannels>((event, emit) async {
      emit(UpdatedUnreadChanelsState(event.unreadChannels));
    });
  }
}

import 'package:common/model/group_landing_models.dart';
import 'package:community/blocs/other_profile/other_profile_events.dart';
import 'package:community/blocs/other_profile/other_profile_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:community/network/social_profile_repository.dart';

class OtherProfileBloc
    extends Bloc<OthersProfilePageEvent, OtherProfilePageState> {
  SocialProfileRepository repository;
  String? socialUserId;
  String? cultUserId;
  BuildContext context;
  String? phone;
  // bool isFindAFriend = false;
  OtherProfileBloc(
    this.context,
    this.repository, {
    this.socialUserId,
    this.cultUserId,
    this.phone,
    // this.isFindAFriend = false,
  }) : super(InitialOtherProfilePageState()) {
    on<ChangePrivacy>((event, emit) async {
      var res = await repository.changePrivacyToPublic();
      emit(PrivacyChanged());
    });
    on<FetchNextWidgets>((event, emit) async {
      try {
        var res = await repository.getOthersProfile(
            context, socialUserId, cultUserId, null);
        if (res["userVisibilityInfo"] != null) {
          emit(ShowPrivacyModal(
              res["userVisibilityInfo"]['message'].toString(),
              res["userVisibilityInfo"]['cta'].toString(),
              res["userVisibilityInfo"]['title'].toString()));
          return;
        }
        final List<dynamic> widgets = res["widgets"];
        String? nextCursor = res["nextCursor"];
        List<GroupMenuOption> options = res["options"]
            .map<GroupMenuOption>((e) => GroupMenuOption.fromJson(e))
            .toList();
        emit(OtherProfilePageLoadedState(
            widgets, nextCursor == null, nextCursor, options));
      } on Exception {
        emit(OtherProfilePageFailedState());
      }
    });
    on<PollInProfile>((event, emit) async {
      OtherProfilePageLoadedState postsList =
          (state as OtherProfilePageLoadedState);
      for (int i = 0; i < postsList.widgets.length; i++) {
        if (postsList.widgets[i]['widgetType'] == "COMMUNITY_POST" &&
            postsList.widgets[i]['data']['id'] == event.postId) {
          postsList.widgets[0]['data']['poll']['vote_count'] =
              event.pollData.voteCount;
          for (int i = 0; i < event.pollData.options.length; i++) {
            postsList.widgets[0]['data']['poll']['options'][i]['vote_count'] =
                event.pollData.options[i].voteCount;
            postsList.widgets[0]['data']['poll']['options'][i]['voted_by_me'] =
                event.pollData.options[i].votedByMe;
          }
        }
      }
      emit(postsList);
    });
    on<ReactionPostInProfile>((event, emit) async {
      OtherProfilePageLoadedState postsList =
          (state as OtherProfilePageLoadedState);
      String postId = event.postId;
      for (int i = 0; i < postsList.widgets.length; i++) {
        if (postsList.widgets[i]['widgetType'] == "COMMUNITY_POST" &&
            postsList.widgets[i]['data']['id'] == postId) {
          if (event.isLiked) {
            // it shouldnt be null, so making it "Liked"
            postsList.widgets[i]['data']['my_reactions'] = "Liked";
          } else {
            postsList.widgets[i]['data']['my_reactions'] = null;
          }
          if (postsList.widgets[i]['data']['reactions_count'] != null) {
            int likes = int.parse(
                postsList.widgets[i]['data']['reactions_count']['like']);
            postsList.widgets[i]['data']['reactions_count']['like'] =
                (likes + (event.isLiked ? 1 : -1)).toString();
          } else {
            postsList.widgets[i]['data']
                ['reactions_count'] = {"like": event.isLiked ? "1" : "0"};
          }
        }
      }
      emit(postsList);
    });
    on<DeletePostInProfile>((event, emit) async {
      OtherProfilePageLoadedState postsList =
          (state as OtherProfilePageLoadedState);
      String postId = event.postId;
      for (int i = 0; i < postsList.widgets.length; i++) {
        if (postsList.widgets[i]['widgetType'] == "COMMUNITY_POST" &&
            postsList.widgets[i]['data']['id'] == postId) {
          postsList.widgets.removeAt(i);
          emit(OtherProfilePageLoadedState(postsList.widgets,
              postsList.limitReached, postsList.nextCursor, postsList.options));
        }
      }
    });
  }
}

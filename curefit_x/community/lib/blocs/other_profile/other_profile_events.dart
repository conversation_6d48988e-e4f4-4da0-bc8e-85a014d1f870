import 'package:community/UI/CommonWidgets/poll_creation_widget.dart';

abstract class OthersProfilePageEvent {
  OthersProfilePageEvent() : super();
}

class ChangePrivacy extends OthersProfilePageEvent {
  ChangePrivacy() : super();
}

class FetchNextWidgets extends OthersProfilePageEvent {
  bool isRefresh;
  FetchNextWidgets({this.isRefresh = false}) : super();
}

class PollInProfile extends OthersProfilePageEvent {
  String postId;
  PollData pollData;
  PollInProfile(this.postId, this.pollData) : super();
}

class ReactionPostInProfile extends OthersProfilePageEvent {
  String postId;
  bool isLiked;
  ReactionPostInProfile(this.postId, this.isLiked) : super();
}

class DeletePostInProfile extends OthersProfilePageEvent {
  String postId;
  DeletePostInProfile(this.postId) : super();
}

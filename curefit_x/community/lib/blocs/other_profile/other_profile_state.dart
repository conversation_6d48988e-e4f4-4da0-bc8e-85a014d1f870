import 'package:common/model/group_landing_models.dart';

abstract class OtherProfilePageState {
  OtherProfilePageState() : super();
}

class InitialOtherProfilePageState extends OtherProfilePageState {
  InitialOtherProfilePageState() : super();
}

class PrivacyChanged extends OtherProfilePageState {
  PrivacyChanged() : super();
}

class ShowPrivacyModal extends OtherProfilePageState {
  String message;
  String ctaTitle;
  String title;
  ShowPrivacyModal(this.message, this.ctaTitle, this.title) : super();
}

class OtherProfilePageLoadedState extends OtherProfilePageState {
  List<dynamic> widgets;
  bool limitReached;
  String? nextCursor;
  List<GroupMenuOption> options;

  OtherProfilePageLoadedState(
      this.widgets, this.limitReached, this.nextCursor, this.options)
      : super();

  OtherProfilePageLoadedState copyFrom(
      List<dynamic> widgets, bool limitReached, String? nextCursor) {
    this.widgets.addAll(widgets);
    return OtherProfilePageLoadedState(
        this.widgets, limitReached, nextCursor, options);
  }
}

class OtherProfilePageFailedState extends OtherProfilePageState {
  OtherProfilePageFailedState()
      : super();
}

abstract class SocialChatAuthState {
  SocialChatAuthState() : super();
}

class InitialSocialChatAuthState extends SocialChatAuthState {
  InitialSocialChatAuthState() : super();
}

class UpdatingSocialChatAuthState extends SocialChatAuthState {
  UpdatingSocialChatAuthState() : super();
}

class UpdatedSocialChatAuthState extends SocialChatAuthState {
  String name;
  String? avatarUrl;
  String token;
  String cultUserId;
  UpdatedSocialChatAuthState(
      this.name, this.avatarUrl, this.token, this.cultUserId);
}

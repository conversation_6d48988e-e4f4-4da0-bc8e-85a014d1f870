import 'package:community/blocs/social_chat_auth/social_chat_auth_events.dart';
import 'package:community/blocs/social_chat_auth/social_chat_auth_states.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class SocialChatAuthBloc
    extends Bloc<SocialChatAuthEvent, SocialChatAuthState> {
  SocialChatAuthBloc() : super(InitialSocialChatAuthState()) {
    on<UpdateSocialChatAuth>((event, emit) async {
      emit(UpdatingSocialChatAuthState());
      emit(UpdatedSocialChatAuthState(
          event.name, event.avatarUrl, event.token, event.cultUserId));
    });
  }
}

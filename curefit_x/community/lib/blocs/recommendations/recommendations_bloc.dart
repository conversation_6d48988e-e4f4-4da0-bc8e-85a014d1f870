import 'package:community/blocs/recommendations/recommendations_events.dart';
import 'package:community/blocs/recommendations/recommendations_state.dart';
import 'package:community/network/recommendations_repository.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class RecommendationsBloc extends Bloc<RecommendationsEvent, RecommendationsState> {
  RecommendationsRepository repository;
  BuildContext context;
  RecommendationsBloc(this.context, this.repository)
      : super(InitialRecommendationsState()) {
    on<FetchWidgets>((event, emit) async {
      if (state is InitialRecommendationsState) {
        try {
          var res =
          await repository.getRecommendations(context, null, null, null);
          final List<dynamic> widgets = res["widgets"][0]["data"]["data"];
          String? nextKey = res["nextCursor"];
          emit(LoadedRecommendationsState(
              widgets, nextKey == null, nextKey));
        } on Exception {
          emit(LoadedRecommendationsState([], false, null));
        }
      } else if (state is LoadedRecommendationsState) {
        try {
          var res = await repository.getRecommendations(context,
              (state as LoadedRecommendationsState).nextKey, null, null);
          final List<dynamic> widgets = res["widgets"][0]["data"]["data"];
          String? nextKey = res["nextCursor"];
          emit((state as LoadedRecommendationsState)
              .copyFrom(widgets, nextKey == null, nextKey));
        } on Exception {
          emit((state as LoadedRecommendationsState).copyFrom([], true, null));
        }
      }
    });
  }
}

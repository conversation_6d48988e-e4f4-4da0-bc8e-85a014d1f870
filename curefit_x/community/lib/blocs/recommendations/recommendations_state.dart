abstract class RecommendationsState {
  RecommendationsState() : super();
}

class InitialRecommendationsState extends RecommendationsState {
  InitialRecommendationsState() : super();
}

class LoadedRecommendationsState extends RecommendationsState {
  List<dynamic> widgets;
  bool limitReached;
  String? nextKey;

  LoadedRecommendationsState(this.widgets, this.limitReached, this.nextKey)
      : super();

  LoadedRecommendationsState copyFrom(
      List<dynamic> widgets, bool limitReached, String? nextKey) {
    this.widgets.addAll(widgets);
    return LoadedRecommendationsState(
        this.widgets, limitReached, nextKey);
  }
}

import 'package:community/blocs/feed_groups/feed_groups_models.dart';

abstract class FeedGroupsState{
  FeedGroupsState();
}

class IdleFeedGroupsState extends FeedGroupsState {
  @override
  String toString() => 'IdleFeedGroups';
}

class LoadingFeedGroupsState extends FeedGroupsState{
  final bool showLoader;

  LoadingFeedGroupsState(this.showLoader);

  @override
  String toString() => 'LoadingFeedGroups';
}

class LoadedFeedGroupsState extends FeedGroupsState{
  final FeedGroupsData data;

  LoadedFeedGroupsState(this.data);

  @override
  String toString() => 'LoadedFeedGroups';

}

class NotLoadedFeedGroupsState extends FeedGroupsState{
  final String? error;

  NotLoadedFeedGroupsState(this.error);

  @override
  String toString() => 'LoadedFeedGroups';
}

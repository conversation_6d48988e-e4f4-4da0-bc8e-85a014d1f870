import 'package:common/network/client.dart';
import 'package:community/blocs/feed_groups/feed_groups_events.dart';
import 'package:community/blocs/feed_groups/feed_groups_models.dart';
import 'package:community/blocs/feed_groups/feed_groups_state.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:community/network/feed_groups_repository.dart';

class FeedGroupsBloc extends Bloc<FeedGroupsEvent, FeedGroupsState> {
  final FeedGroupsRepository feedGroupsRepository;
  BuildContext context;
  FeedGroupsBloc(this.context, this.feedGroupsRepository)
      : super(IdleFeedGroupsState()) {
    on<LoadingFeedGroups>((event, emit) async {
      emit(LoadingFeedGroupsState(true));
      try {
        final response = await feedGroupsRepository.getFeedGroups();
        if (response == null) {
          emit(NotLoadedFeedGroupsState("Null Response"));
        }
        FeedGroupsData feedGroupsData =
            FeedGroupsData(widgets: response['widgets']);
        emit(LoadedFeedGroupsState(feedGroupsData));
      } on NetworkException catch (exception) {
        emit(NotLoadedFeedGroupsState(exception.subTitle));
      }
    });
  }
}

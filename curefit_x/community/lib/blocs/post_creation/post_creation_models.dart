
class GetSocialUser {
  String displayName;
  String userIdHashToken;
  String getSocialUserId;
  String cultUserId;
  String? avatarUrl;

  GetSocialUser(this.displayName, this.avatarUrl, this.cultUserId, this.getSocialUserId, this.userIdHashToken);

  factory GetSocialUser.fromJson(dynamic userDetails) {
    String displayName = userDetails["user"]["display_name"];
    String userIdHashToken = userDetails["userIdHashToken"];
    String getSocialUserId = userDetails["user"]["id"];
    String cultUserId = userDetails["user"]["auth_identities"]["cult_user_id"];
    String? avatarUrl = userDetails["user"]["avatar_url"];
    
    return GetSocialUser(displayName, avatarUrl, cultUserId, getSocialUserId, userIdHashToken);
  }
}

class GetSocialGroupDetails {
  String socialGroupId;
  String title;
  String? avatarUrl;
  // String? location;
  GetSocialGroupDetails(this.title, this.socialGroupId, this.avatarUrl);

  factory GetSocialGroupDetails.fromJson(dynamic groupData) {
    return GetSocialGroupDetails(
      groupData['groupId'], groupData['imageUrl'], groupData['title'],
    );
  }
}

class PollDuration {
  String displayText;
  int numDays;
  PollDuration(this.displayText, this.numDays);
  
  factory PollDuration.fromJson(dynamic pollDurationData) {
    return PollDuration(pollDurationData['displayText'], pollDurationData['numDays']);
  }
}

class PollsTemplateDetails {
  bool showPoll;
  String titleHint;
  String pollLengthDesc;
  int maxOptions;
  String? hintQuestion;
  List<PollDuration> pollDurationList;
  PollsTemplateDetails(this.showPoll, this.titleHint, this.pollLengthDesc,
  this.maxOptions, this.hintQuestion, this.pollDurationList);

  factory PollsTemplateDetails.fromJson(dynamic pollsTemplateData) {
    return PollsTemplateDetails(pollsTemplateData['showPoll'], pollsTemplateData['titleHint'],
        pollsTemplateData['pollLengthDesc'], pollsTemplateData['maxOptions'],
        pollsTemplateData['hintQuestion'], pollsTemplateData['pollLength']
            .map<PollDuration>((e) => PollDuration.fromJson(e))
            .toList());
  }
}

class PostingUserAndGroupData {
  GetSocialUser user;
  GetSocialGroupDetails? groupDetails;
  PollsTemplateDetails? pollsTemplateDetails;
  PostingUserAndGroupData(this.user, this.groupDetails, this.pollsTemplateDetails);
}

class GroupsData {
  String groupId;
  String? imageUrl;
  String title;
  String? location;
  GroupsData(this.groupId, this.imageUrl, this.title, this.location);

  factory GroupsData.fromJson(dynamic memberData) {
    return GroupsData(
      memberData['groupId'], memberData['imageUrl'], memberData['title'], memberData['location'],
    );
  }
}

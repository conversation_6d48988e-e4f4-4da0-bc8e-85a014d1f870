import 'package:community/blocs/post_creation/post_creation_models.dart';

abstract class PostCreationState{
  PostCreationState();
}

class IdlePostCreationState extends PostCreationState {
  @override
  String toString() => 'IdlePostCreation';
}

class LoadingPostCreationState extends PostCreationState{

  LoadingPostCreationState();

  @override
  String toString() => 'LoadingPostCreation';
}

class LoadedPostCreationState extends PostCreationState{
  final PostingUserAndGroupData data;

  LoadedPostCreationState(this.data);

  @override
  String toString() => 'LoadedPostCreation';

}

class NotLoadedPostCreationState extends PostCreationState{
  final String? error;

  NotLoadedPostCreationState(this.error);

  @override
  String toString() => 'LoadedPostCreation';
}


// Group loadinf state
abstract class GroupsState{
  GroupsState();
}

class IdleGroupsState extends GroupsState {
  @override
  String toString() => 'IdleGroupsState';
}

class LoadingGroupsState extends GroupsState{

  LoadingGroupsState();

  @override
  String toString() => 'LoadingGroupsState';
}

class LoadedGroupsState extends GroupsState{
  final List<GroupsData> data;
  LoadedGroupsState(this.data);

  @override
  String toString() => 'LoadedGroupsState';

}

class NotLoadedGroupsState extends GroupsState{
  final String? error;

  NotLoadedGroupsState(this.error);

  @override
  String toString() => 'LoadedGroupsState';
}

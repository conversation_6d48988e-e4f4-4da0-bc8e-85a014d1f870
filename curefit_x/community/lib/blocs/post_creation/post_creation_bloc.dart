import 'package:common/network/client.dart';
import 'package:community/network/post_page_repository.dart';
import 'package:community/blocs/post_creation/post_creation_events.dart';
import 'package:community/blocs/post_creation/post_creation_models.dart';
import 'package:community/blocs/post_creation/post_creation_state.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class PostCreationBloc extends Bloc<PostCreationEvent, PostCreationState> {
  PostPageRepository postPageRepository;

  PostCreationBloc(this.postPageRepository) : super(IdlePostCreationState()) {
    on<LoadingPostCreation>(((event, emit) async {
      emit(LoadingPostCreationState());
      try {
        final response =
            await postPageRepository.getPostingUserAndGroup(event.groupId);
        GetSocialUser user =
            GetSocialUser.fromJson(response["authenticateUserResponse"]);
        GetSocialGroupDetails? groupDetails;
        if (response["groupResponse"] != null) {
          groupDetails = GetSocialGroupDetails(
              response["groupResponse"]["title"],
              response["groupResponse"]["socialGroupId"],
              response["groupResponse"]["avatarUrl"]);
        }
        PollsTemplateDetails? pollsTemplateDetails;
        if ( response["publicProperties"] != null) {
          if( response['publicProperties']['poll'] != null) {
            pollsTemplateDetails = PollsTemplateDetails.fromJson(response['publicProperties']['poll']);
          }
        }
        PostingUserAndGroupData data =
            PostingUserAndGroupData(user, groupDetails, pollsTemplateDetails);
        emit(LoadedPostCreationState(data));
      } on NetworkException catch (exception) {
        emit(NotLoadedPostCreationState(exception.subTitle));
      }
    }));
  }
}

class GroupsBloc extends Bloc<GroupsEvent, GroupsState> {
  PostPageRepository postPageRepository;

  GroupsBloc(this.postPageRepository) : super(IdleGroupsState()) {
    on<LoadingGroups>(((event, emit) async {
      emit(LoadingGroupsState());
      try {
        final response = await postPageRepository.getFeedGroups(null);
        List<GroupsData> media = response["widgets"][0]['groupList']
            .map<GroupsData>((e) => GroupsData.fromJson(e))
            .toList();
        emit(LoadedGroupsState(media));
      } on NetworkException catch (exception) {
        emit(NotLoadedGroupsState(exception.subTitle));
      }
    }));
  }
}

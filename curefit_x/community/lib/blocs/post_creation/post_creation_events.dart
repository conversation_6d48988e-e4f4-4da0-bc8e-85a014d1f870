abstract class PostCreationEvent{
  PostCreationEvent() : super();
}

class LoadingPostCreation extends PostCreationEvent{
  String? groupId;
  LoadingPostCreation({this.groupId}): super();

  @override
  String toString() => 'LoadingPostCreation';
}

class LoadedPostCreation extends PostCreationEvent{
  LoadedPostCreation(): super();

  @override
  String toString() => 'LoadedPostCreation';
}


abstract class GroupsEvent{
  GroupsEvent() : super();
}

class LoadingGroups extends GroupsEvent{
  LoadingGroups(): super();

  @override
  String toString() => 'LoadingPostCreation';
}

// class LoadedGroups extends LoadingGroups{
//   LoadedGroups(): super();
//
//   @override
//   String toString() => 'LoadedPostCreation';
// }


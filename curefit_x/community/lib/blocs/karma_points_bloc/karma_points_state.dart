import 'package:core/model/karma_points_models.dart';
import 'package:core/model/users_list_modal.dart';

abstract class KarmaPointsState {
  KarmaPointsState() : super();
}

class InitialState extends KarmaPointsState {
  InitialState() : super();
}

class GivingKudosToUser extends KarmaPointsState {
  GivingKudosToUser() : super();
}

class KudosGivenToUser extends KarmaPointsState {
  UserEventReviewCount? userEventReviewCount;
  UserListData? highlightPeopleData;
  KudosGivenToUser({this.highlightPeopleData, this.userEventReviewCount})
      : super();
}

class KudosNotGivenToUser extends KarmaPointsState {
  KudosNotGivenToUser() : super();
}

class DismissBottomTray extends KarmaPointsState {
  DismissBottomTray() : super();
}

class KarmaPointsScreenLoaded extends KarmaPointsState {
  KarmaPointsScreenData? karmaPointsScreenData;
  KarmaPointsScreenLoaded({this.karmaPointsScreenData}) : super();
}

class KarmaPointsScreenLoading extends KarmaPointsState {
  KarmaPointsScreenLoading() : super();
}

class KarmaPointsScreenNotLoaded extends KarmaPointsState {
  KarmaPointsScreenNotLoaded() : super();
}

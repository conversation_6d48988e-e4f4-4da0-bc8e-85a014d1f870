import 'dart:convert';

import 'package:community/blocs/karma_points_bloc/karma_points_events.dart';
import 'package:community/blocs/karma_points_bloc/karma_points_state.dart';
import 'package:core/model/karma_points_models.dart';
import 'package:core/model/users_list_modal.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../network/karma_points_repository.dart';

class KarmaPointsBloc extends Bloc<KarmaPointsEvents, KarmaPointsState> {
  KarmaPointsRepository karmaPointsRepository;

  loadMockJson(String fileName) async {
    String data = await rootBundle.loadString('assets/mocks/$fileName');
    return json.decode(data);
  }

  KarmaPointsBloc({required this.karmaPointsRepository})
      : super(InitialState()) {
    on<FetchKarmaPointsScreenData>((event, emit) async {
      try {
        emit(KarmaPointsScreenLoading());
        //KarmaPointsScreenData karmaPointsScreenData = KarmaPointsScreenData();
        // var res = await loadMockJson("karma_points_mock.json");
        var res = await karmaPointsRepository.fetchKarmaScreenData(
            EnumToString.convertToString(UserType.CUSTOMER));
        if (res != null) {
          KarmaPointsScreenData karmaPointsScreenData =
              KarmaPointsScreenData.fromJson(res);
          emit(KarmaPointsScreenLoaded(
              karmaPointsScreenData: karmaPointsScreenData));
        }
      } on Exception catch (e) {
        emit(KarmaPointsScreenNotLoaded());
      }
    });

    on<GiveKarmaPoints>((event, emit) async {
      try {
        emit(GivingKudosToUser());
        Map<String, dynamic> data = event.userReviewEntry.toJson();
        var res = await karmaPointsRepository.giveKudosToUser(data);
        if (res != null) {
          UserListData? oldData = event.highlightPeopleData;

          if (oldData != null) {
            List<UserWidgetData> squadMembers = oldData.cardData.squadMembers;
            List<UserWidgetData> inviterMembers =
                oldData.cardData.inviterMembers;
            List<UserWidgetData> otherMembers = oldData.cardData.otherMembers;

            for (UserWidgetData peopleWidgetData in squadMembers) {
              if (peopleWidgetData.userEventReviewCount?.userId ==
                  event.userEventReviewCount?.userId) {
                peopleWidgetData.userEventReviewCount =
                    event.userEventReviewCount;
                break;
              }
            }

            for (UserWidgetData peopleWidgetData in inviterMembers) {
              if (peopleWidgetData.userEventReviewCount?.userId ==
                  event.userEventReviewCount?.userId) {
                peopleWidgetData.userEventReviewCount =
                    event.userEventReviewCount;
                break;
              }
            }

            for (UserWidgetData peopleWidgetData in otherMembers) {
              if (peopleWidgetData.userEventReviewCount?.userId ==
                  event.userEventReviewCount?.userId) {
                peopleWidgetData.userEventReviewCount =
                    event.userEventReviewCount;
                break;
              }
            }

            oldData.cardData.otherMembers = otherMembers;
            oldData.cardData.inviterMembers = inviterMembers;
            oldData.cardData.squadMembers = squadMembers;
            emit(KudosGivenToUser(
                highlightPeopleData: oldData,
                userEventReviewCount: event.userEventReviewCount));
            await Future.delayed(Duration(seconds: 3), () {
              emit(DismissBottomTray());
            });
          }
        } else {
          emit(KudosNotGivenToUser());
        }
      } on Exception catch (e) {
        emit(KudosNotGivenToUser());
      }
    });
  }
}

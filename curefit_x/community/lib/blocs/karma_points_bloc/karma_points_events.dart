import 'package:core/model/karma_points_models.dart';
import 'package:core/model/users_list_modal.dart';
import 'package:flutter/cupertino.dart';

abstract class KarmaPointsEvents {
  KarmaPointsEvents() : super();
}

class FetchKarmaPointsScreenData extends KarmaPointsEvents {
  FetchKarmaPointsScreenData() : super();
}

class GiveKarmaPoints extends KarmaPointsEvents {
  UserReviewEntry userReviewEntry;
  BuildContext context;
  UserEventReviewCount? userEventReviewCount;
  UserListData? highlightPeopleData;
  GiveKarmaPoints(
      {required this.userReviewEntry,
      required this.context,
      this.userEventReviewCount,
      this.highlightPeopleData})
      : super();
}

import 'package:common/ui/widget_builder.dart';
import 'package:community/blocs/highlight_peoples_bloc/highlights_people_events.dart';
import 'package:community/blocs/highlight_peoples_bloc/highlights_people_state.dart';
import 'package:core/model/users_list_modal.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../network/karma_points_repository.dart';

class HighlightsPeopleBloc
    extends Bloc<HighlightsPeopleEvents, HighlightsPeopleState> {
  KarmaPointsRepository karmaPointsRepository;

  HighlightsPeopleBloc({required this.karmaPointsRepository})
      : super(InitialState()) {
    on<FetchHighlightsPeople>((event, emit) {
      try {
        if (event.widgets != null) {
          UserListData? widgetData;
          for (dynamic widget in event.widgets!) {
            if (widget["widgetType"] == "HIGHLIGHT_PEOPLE") {
              widgetData =
                  UserListData.fromJson(widget, WidgetTypes.HIGHLIGHT_PEOPLE);
              break;
            }
          }
          if (widgetData != null) {
            emit(LoadedHighlightPeople(widgetData: widgetData));
          }
        }
      } on Exception catch (e) {
        emit(NotLoadedHighlightPeople());
      }
    });
  }
}

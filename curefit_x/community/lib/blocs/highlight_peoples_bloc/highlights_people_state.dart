import 'package:core/model/users_list_modal.dart';

abstract class HighlightsPeopleState {
  HighlightsPeopleState();
}

class InitialState extends HighlightsPeopleState {
  InitialState() : super();
}

class LoadedHighlightPeople extends HighlightsPeopleState {
  UserListData? widgetData;
  LoadedHighlightPeople({this.widgetData}) : super();
}

class LoadingHighlightPeople extends HighlightsPeopleState {
  UserListData? widgetData;
  LoadingHighlightPeople({this.widgetData}) : super();
}

class NotLoadedHighlightPeople extends HighlightsPeopleState {
  NotLoadedHighlightPeople() : super();
}

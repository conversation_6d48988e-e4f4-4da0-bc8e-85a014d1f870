import 'package:common/analytics/analytics_repository.dart';
import 'package:common/constants/action_constants.dart';
import 'package:common/network/client.dart';
import 'package:community/blocs/social_cache/social_cache_bloc.dart';
import 'package:community/blocs/social_cache/social_cache_events.dart';
import 'package:community/blocs/social_cache/social_cache_state.dart';
import 'package:community/blocs/unread_channels_bloc/unread_channels_bloc.dart';
import 'package:community/network/post_page_repository.dart';
import 'package:common/ui/molecules/bottom_tray.dart';
import 'package:community/UI/CommonWidgets/report_post.dart';
import 'package:flutter/material.dart';
import 'package:common/action/action_handler.dart' as action_handler;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:share_plus/share_plus.dart';

enableSocialCache(BuildContext context) {
  SocialCacheBloc cacheBloc = BlocProvider.of<SocialCacheBloc>(context);
  cacheBloc.add(EnableSocialCacheEvent(DateTime.now()));
}

updateUnreadChannels(BuildContext context, int unreadChannels) {
  final unreadChannelsBloc = BlocProvider.of<UnreadChanelsBloc>(context);
  unreadChannelsBloc.add(UpdateUnreadChannels(unreadChannels));
}

String getCbToken(BuildContext context) {
  SocialCacheBloc cacheBloc = BlocProvider.of<SocialCacheBloc>(context);
  String cb = "";
  if (cacheBloc.state is EnabledSocialCacheState) {
    cb = (cacheBloc.state as EnabledSocialCacheState)
        .lastActivityTime
        .millisecondsSinceEpoch
        .toString();
  }
  return cb;
}

Function getActionFunction(BuildContext context, action_handler.Action action,
    {Function? onSuccess, Map<String, dynamic>? analyticsInfo}) {
  switch (action.type) {
    case ActionTypes.SHARE_POST_DETAILS:
      {
        onTap() async {
          String? shareUrl = action.meta!['url'];
          if (shareUrl!.isEmpty) {
            PostPageRepository postPageRepository = PostPageRepository(
                client: RepositoryProvider.of<NetworkClient>(context));
            var response = await postPageRepository
                .getSharablePostUrl(action.meta!['postId']);
            shareUrl = response['url'];
          }
          RepositoryProvider.of<AnalyticsRepository>(context)
              .logButtonClickEvent(extraInfo: analyticsInfo!);
          Share.share(shareUrl.toString());
          Navigator.pop(context);
        }

        return onTap;
      }
    case ActionTypes.DELETE_POST:
      {
        onTap() async {
          RepositoryProvider.of<AnalyticsRepository>(context)
              .logButtonClickEvent(extraInfo: analyticsInfo!);
          // getSocial - delete social post
          Navigator.pop(context);
        }

        return onTap;
      }
    case ActionTypes.DELETE_COMMENT:
      {
        onTap() async {
          RepositoryProvider.of<AnalyticsRepository>(context)
              .logButtonClickEvent(extraInfo: analyticsInfo!);
          // getSocial - delete social comment
          Navigator.pop(context);
        }

        return onTap;
      }
    case ActionTypes.REPORT_POST:
      {
        onTap() async {
          Navigator.pop(context);

          onReport(String message) async {
            RepositoryProvider.of<AnalyticsRepository>(context)
                .logButtonClickEvent(extraInfo: {"reportAPost": message});
            // getSocial - report post
            Navigator.pop(context);
          }

          String header = "Tell us why you are reporting this post?";
          String actionTitle = "SUBMIT";
          showBottomTray(
              context: context,
              child: ReportPostBottomSheet(onReport, header, actionTitle));
        }

        return onTap;
      }
    case ActionTypes.REPORT_COMMENT:
      {
        onTap() async {
          Navigator.pop(context);

          onReport(String message) async {
            RepositoryProvider.of<AnalyticsRepository>(context)
                .logButtonClickEvent(extraInfo: {"reportAComment": message});
            // getSocial - report comment
            Navigator.pop(context);
          }

          String header = "Tell us why you are reporting this comment?";
          String actionTitle = "REPORT COMMENT";
          showBottomTray(
              context: context,
              child: ReportPostBottomSheet(onReport, header, actionTitle));
        }

        return onTap;
      }
    default:
      {
        return () {};
      }
  }
}

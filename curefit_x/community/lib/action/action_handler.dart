import 'package:common/action/action_handler.dart' as ActionHandler;
import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/constants/action_constants.dart';
import 'package:common/network/client.dart';
import 'package:common/ui/molecules/bottom_tray.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/toast.dart';
import 'package:community/UI/Screens/group_landing/widgets/group_leave_questions.dart';
import 'package:community/blocs/group_landing/group_landing_bloc.dart';
import 'package:community/blocs/post_detail/post_detail_bloc.dart';
import 'package:community/blocs/post_detail/post_detail_events.dart';
import 'package:community/network/group_landing_repository.dart';
import 'package:community/network/post_page_repository.dart';
import 'package:core/blocs/squads_action/bloc.dart';
import 'package:core/blocs/squads_action/events.dart';
import 'package:core/model/karma_points_models.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:share_plus/share_plus.dart';

import '../UI/Screens/karma_points/Karma_points_bottom_sheet.dart';
import '../UI/Screens/karma_points/karma_points_intro_sheet.dart';

class CommunityActionHandler extends ActionHandler.IActionHandler {
  final GlobalKey<NavigatorState> navigatorKey;
  late GroupLandingRepository groupLandingRepository;
  late PostPageRepository postPageRepository;

  CommunityActionHandler(NetworkClient client, this.navigatorKey) {
    groupLandingRepository = GroupLandingRepository(client: client);
    postPageRepository = PostPageRepository(client: client);
  }

  @override
  bool handleAction(ActionHandler.Action action, ActionBloc actionBloc) {
    BuildContext? context = navigatorKey.currentContext;
    if (context == null) {
      return false;
    }
    switch (action.type) {
      case ActionTypes.LEAVE_GROUP:
        {
          GroupLandingBloc groupLandingBloc = action.bloc as GroupLandingBloc;
          // groupLandingBloc.add(LoadGroupLanding(action.meta!['groupId']));
          // AnalyticsRepository analyticsRepo = RepositoryProvider.of<AnalyticsRepository>(context);
          showBottomTray(
              context: context,
              child: GroupLeaveQuestions(
                  groupLandingBloc, action.meta!, groupLandingRepository));
          return true;
        }
      case ActionTypes.SHARE_POST_DETAILS:
        {
          Share.share(action.meta!['url']);
          return true;
        }
      case ActionTypes.SHARE_GROUP_DETAILS:
        {
          Share.share(action.meta!['url']);
          return true;
        }
      case ActionTypes.SHARE_PROFILE:
        {
          Share.share(action.meta!['url']);
          return true;
        }
      case ActionTypes.REPORT_POST:
        {
          PostDetailBloc bloc = action.bloc as PostDetailBloc;
          bloc.add(ReportPost(action.meta!['postId']));
          Toast.show("Reported Post", context,
              duration: Toast.lengthShort,
              gravity: Toast.top,
              textStyle: AuroraTheme.of(context)
                  .textStyle(TypescaleValues.P3, color: Colors.black),
              backgroundColor: Colors.white);
          return true;
        }
      case ActionTypes.REPORT_COMMENT:
        {
          PostDetailBloc bloc = action.bloc as PostDetailBloc;
          bloc.add(ReportPost(action.meta!['commentId']));
          Toast.show("Reported Comment", context,
              duration: Toast.lengthShort,
              gravity: Toast.top,
              textStyle: AuroraTheme.of(context)
                  .textStyle(TypescaleValues.P3, color: Colors.black),
              backgroundColor: Colors.white);
          return true;
        }
      case ActionTypes.REJECT_SQUAD_INVITE:
        {
          if (action.meta != null &&
              action.meta?["communityMappingId"] != null &&
              action.meta?["userId"] != null) {
            RepositoryProvider.of<AnalyticsRepository>(context)
                .logButtonClickEvent(extraInfo: {
              "modal": "REJECT_SQUAD_INVITE",
              "source": "highlights"
            });
            final squadsBloc = BlocProvider.of<SquadsBloc>(context);
            squadsBloc.add(ChangeInviteStateEvent(
                action.meta?["communityMappingId"],
                action.meta?["userId"],
                "REJECTED",
                "CF_USER"));
          }
          return true;
        }
      case ActionTypes.ACCEPT_SQUAD_INVITE:
        {
          if (action.meta != null &&
              action.meta?["communityMappingId"] != null &&
              action.meta?["userId"] != null) {
            RepositoryProvider.of<AnalyticsRepository>(context)
                .logButtonClickEvent(extraInfo: {
              "modal": "ACCEPT_SQUAD_INVITE",
              "source": "notification_centre"
            });
            final squadsBloc = BlocProvider.of<SquadsBloc>(context);
            squadsBloc.add(ChangeInviteStateEvent(
                action.meta?["communityMappingId"],
                action.meta?["userId"],
                "CONFIRMED",
                "CF_USER"));
          }
          return true;
        }
      case ActionTypes.SHOW_KARMA_POINTS_BOTTOM_SHEET:
        {
          showBottomTray(
              context: context,
              blurEnabled: true,
              child: KarmaPointsBottomSheet(
                karmaPointsBottomSheetData:
                    KarmaPointsBottomSheetData.fromJson(action.meta),
              ),
              showTopNotch: true);
          return true;
        }
      case ActionTypes.SHOW_KARMA_POINTS_INTRO:
        {
          showBottomTray(
              context: context,
              blurEnabled: true,
              child: KarmaPointsIntroSheet(
                  karmaPointsIntroSheetData:
                      KarmaPointsIntroSheetData.fromJson(action.meta)),
              showTopNotch: true);
          return true;
        }
    }
    return false;
  }
}

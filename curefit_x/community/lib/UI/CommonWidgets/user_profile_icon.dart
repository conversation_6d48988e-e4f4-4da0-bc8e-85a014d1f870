import 'package:cached_network_image/cached_network_image.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:flutter/material.dart';

String defaultProfileImageUrl =
    "https://cdn-media.cure.fit/image/community/default_male_thumbnail.png";

class UserProfileIcon extends StatelessWidget {
  final String profileDisplayText;
  final String? profileImageUrl;
  double profileIconRadius;
  double borderWidth;

  UserProfileIcon(
      {Key? key,
      required this.profileDisplayText,
      this.profileImageUrl,
      this.profileIconRadius = 20.0,
      this.borderWidth = 0})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Material(
      elevation: 10,
      shape: const CircleBorder(),
      child: Container(
          height: profileIconRadius * 2,
          width: profileIconRadius * 2,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(color: Colors.white, width: borderWidth),
            color: getBGColor(profileDisplayText),
          ),
          child: ClipOval(
            child: FadeInImage.assetNetwork(
              image: getImageUrl(context,
                  imagePath: profileImageUrl != null
                      ? profileImageUrl.toString()
                      : defaultProfileImageUrl),
              placeholder: "assets/default_male_thumbnail.png",
              fit: BoxFit.cover,
              imageErrorBuilder: (context, url, error) =>
                  CachedNetworkImage(imageUrl: defaultProfileImageUrl),
            ),
          )),
    );
  }
}

Color getBGColor(String profileDisplayText) {
  int profileHashCode = profileDisplayText.hashCode;
  switch (profileHashCode % 10) {
    case 0:
      return const Color(0xFFFF3278);
    case 1:
      return const Color(0xFF00BEFF);
    case 2:
      return const Color(0xFFFFDC18);
    case 3:
      return const Color(0xFF0FE498);
    case 4:
      return const Color(0xFFF7C744);
    case 5:
      return const Color(0xFFFF5942);
    case 6:
      return const Color(0xFFFFD579);
    case 7:
      return const Color(0xFFF2994A);
    case 8:
      return const Color(0xFF307AFF);
    case 9:
      return const Color(0xFFFF75A4);
  }
  return Colors.black87;
}

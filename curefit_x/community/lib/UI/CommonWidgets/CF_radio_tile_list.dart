import 'dart:async';

import 'package:common/font/cf_icons.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:community/UI/CommonWidgets/photo_text_post.dart';
import 'package:community/UI/CommonWidgets/poll_creation_widget.dart';
import 'package:community/blocs/other_profile/other_profile_bloc.dart';
import 'package:community/blocs/other_profile/other_profile_events.dart';
import 'package:community/blocs/post_detail/post_detail_bloc.dart';
import 'package:community/blocs/post_detail/post_detail_events.dart';
import 'package:community/blocs/posts_bloc/posts_page_bloc.dart';
import 'package:community/blocs/posts_bloc/posts_page_events.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:percent_indicator/linear_percent_indicator.dart';


class CFRadioTileList extends StatefulWidget {
  PhotoTextPostWidgetData postData;
  CFRadioTileList(this.postData, {Key? key}) : super(key: key);


  @override
  State<CFRadioTileList> createState() => _CFRadioTileListState();
}

class _CFRadioTileListState extends State<CFRadioTileList> {
  late Timer _timer;
  late Duration _duration = Duration.zero;

  @override
  void initState() {
    if(widget.postData.pollData?.endsAt != null ) {
      DateTime endTime = DateTime.fromMillisecondsSinceEpoch(
          widget.postData.pollData?.endsAt ?? 0 * 1000);
      _duration = endTime.difference(DateTime.now());
      _timer = Timer.periodic(const Duration(seconds: 1), (Timer timer) {
        setState(() {
          _duration = endTime.difference(DateTime.now());
        });
      });
      super.initState();
    }
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  String _formatDuration(Duration duration) {
    int days = duration.inDays.remainder(30);
    String hours = duration.inHours.remainder(24).toString().padLeft(2, '0');
    String minutes = duration.inMinutes.remainder(60).toString().padLeft(2, '0');
    String seconds = duration.inSeconds.remainder(60).toString().padLeft(2, '0');
    if(days > 0) return "$days ${days == 1 ? "day" : "days"}";
    return "$hours:$minutes:$seconds";
  }

  @override
  Widget build(BuildContext context) {
    int totalVoteCount = widget.postData.pollData?.voteCount ?? 0;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ListProgressIndicator(widget.postData, _duration.isNegative),
        const SizedBox(height: Spacings.x4),
        if( widget.postData.pollData?.endsAt != null )
          Text("${widget.postData.pollData?.voteCount.toString()} "
              "${totalVoteCount <= 1 ? "vote" : "votes"}"
              " • ${_duration.isNegative
              ? "poll ended"
              : "Ends in ${_formatDuration(_duration)}"}",
              style: AuroraTheme.of(context).textStyle(
                  TypescaleValues.P8, color: Colors.white60)),
      ],
    );
  }
}

class ListProgressIndicator extends StatefulWidget {
  PhotoTextPostWidgetData postData;
  bool isPollEnded;
  ListProgressIndicator(this.postData, this.isPollEnded, {Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _ListProgressIndicatorState();
}

class _ListProgressIndicatorState extends State<ListProgressIndicator> {
  int currentVote = -1;
  bool isVoted = false;
  bool isMyPoll = false;

  @override
  void initState() {
    setState(() {
      int numOptions = widget.postData.pollData?.options.length ?? 0;
      for(int option=0; option < numOptions; option++) {
        if(widget.postData.pollData?.options[option].votedByMe == true) {
          currentVote = option;
          isVoted = true;
        }
      }
    });
    if(widget.isPollEnded)  isVoted = true;
    if(widget.postData.isMyPost ?? false) isMyPoll = true;
    super.initState();
  }

  void handlePollChanges(PollData pollData) {
    if (widget.postData.isPostDetailPage){
      BlocProvider.of<PostDetailBloc>(context).add(
          PollInPostDetail(
              widget.postData.activityId, pollData));
      return;
    }
    if (widget.postData.pageType == "profilePage") {
      BlocProvider.of<OtherProfileBloc>(context).add(
          PollInProfile(
              widget.postData.activityId, pollData));
      return;
    }
    if (!widget.postData.isPostDetailPage) {
      BlocProvider.of<PostPageBloc>(context)
          .add(PollInPostPage(widget.postData.activityId, pollData));
      return;
    }
    return;
  }

  void onTapPoll(int index) {
    if(index == currentVote) {
      if(!widget.isPollEnded) {
        setState(() {
          // getSocial - remove poll vote from social sdk
          PollData newPollData = widget.postData.pollData!;
          currentVote = -1;
          newPollData.options[index].voteCount--;
          newPollData.voteCount--;
          isVoted = !isVoted;
          newPollData.options[index].votedByMe = false;
          handlePollChanges(newPollData);
        });
      }
    } else {
      if(!widget.isPollEnded) {
        setState(() {
          // getSocial - add poll vote to social sdk
          PollData newPollData = widget.postData.pollData!;
          if(isVoted) {
            newPollData.options[currentVote].voteCount--;
            newPollData.voteCount--;
            newPollData.options[currentVote].votedByMe = false;
          }
          currentVote = index;
          newPollData.options[index].voteCount++;
          newPollData.voteCount++;
          isVoted = true;
          newPollData.options[index].votedByMe = true;

          handlePollChanges(newPollData);
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    int totalVotes = widget.postData.pollData?.voteCount ?? 1;
    return ListView.builder(
      itemCount: widget.postData.pollData?.options.length,
      physics: const NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemBuilder: (context, index) {
        int numOption = widget.postData.pollData?.options.length ?? 0;
        int optionVotes = widget.postData.pollData?.options[index].voteCount ?? 0;
        double votePercent = optionVotes / totalVotes;
        return Padding(
          padding: EdgeInsets.only(top: index == 0 ? 0 : Spacings.x2,
              bottom: index == numOption - 1 ? 0 : Spacings.x2),
          child: InkWell(
            onTap: () => onTapPoll(index),
            child: Row(
              children: [
                index == currentVote
                    ? const Icon(CFIcons.check_circle, color: Colors.white, size: 20)
                    : const Icon(CFIcons.black_circle, color: Colors.white10, size: 20),
                const SizedBox(width: 15),
                Expanded(
                  child: Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(widget.postData.pollData?.options[index].displayText ?? "",
                              style: AuroraTheme.of(context).textStyle(TypescaleValues.P2,
                                  color: index == currentVote ? Colors.white : Colors.white60)),
                          if(isVoted || isMyPoll)
                            Text("${optionVotes.toString()} ${optionVotes < 2 ? "vote" : "votes"}",
                              style: AuroraTheme.of(context).textStyle(TypescaleValues.P8, color: Colors.white60)),
                        ],
                      ),
                      const SizedBox(height: Spacings.x1),
                      LinearPercentIndicator(
                        width: MediaQuery.of(context).size.width - 120,
                        animation: true,
                        backgroundColor: Colors.white10,
                        animationDuration: 500,
                        percent: isVoted || isMyPoll ? votePercent : 0,
                        linearStrokeCap: LinearStrokeCap.roundAll,
                        progressColor: Colors.white,
                        padding: const EdgeInsets.only(left: 4),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

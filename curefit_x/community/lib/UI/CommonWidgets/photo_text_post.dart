import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:ui';

import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/constants/action_constants.dart';
import 'package:common/font/cf_icons.dart';
import 'package:common/model/group_landing_models.dart';
import 'package:common/network/client.dart';
import 'package:common/ui/atoms/secondary_button.dart';
import 'package:common/ui/cards/enclosed_catalog_card.dart';
import 'package:common/ui/components/cf_photo_view.dart';
import 'package:community/UI/CommonWidgets/poll_creation_widget.dart';
import 'package:community/UI/Screens/moments/moments.dart';
import 'package:community/blocs/other_profile/other_profile_bloc.dart';
import 'package:community/blocs/other_profile/other_profile_events.dart';
import 'package:community/blocs/post_detail/post_detail_bloc.dart';
import 'package:community/blocs/post_detail/post_detail_events.dart';
import 'package:community/blocs/posts_bloc/posts_page_bloc.dart';
import 'package:community/blocs/posts_bloc/posts_page_events.dart';
import 'package:community/network/post_page_repository.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/molecules/bottom_tray.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/toast.dart';
import 'package:common/ui/video/video_view.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:common/video/video_player_registry.dart';
import 'package:community/UI/CommonWidgets/add_comment/comment_text_controller.dart';
import 'package:common/ui/widgets/dropdown_options.dart';
import 'package:community/UI/CommonWidgets/heart_animation_widget.dart';
import 'package:common/ui/user_profile_icon.dart';
import 'package:community/action/internal_action_handler.dart';
import 'package:community/blocs/reaction_bloc/post_reaction_bloc.dart';
import 'package:community/blocs/reaction_bloc/post_reaction_event.dart';
import 'package:community/blocs/reaction_bloc/post_reaction_state.dart';
import 'package:flutter/material.dart';
import 'package:common/ui/widgets/rich_text_read_more_widget.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'dart:ui' as ui;
import 'package:common/action/action_handler.dart' as action_handler;
import 'package:lottie/lottie.dart';
import 'package:linkify/linkify.dart';
import 'package:share_plus/share_plus.dart';

import '../Screens/post_detail/widgets/like_users_widget.dart';
import 'CF_radio_tile_list.dart';
import 'CFUrlLinkifier.dart';
import 'auto_post_shape.dart';
import 'badge_view_widget.dart';

class PhotoTextPostWidget extends StatefulWidget {
  final PhotoTextPostWidgetData widgetData;
  const PhotoTextPostWidget({required this.widgetData, Key? key})
      : super(key: key);

  @override
  _PhotoTextPostWidgetState createState() => _PhotoTextPostWidgetState();
}

class _PhotoTextPostWidgetState extends State<PhotoTextPostWidget>
    with TickerProviderStateMixin {
  late bool isLiked = false;
  bool isAnimating = false;
  double aspectRatio = 1;

  @override
  void initState() {
    isLiked = widget.widgetData.isLiked;
    // _calculateImageDimension().then((size) => {
    //   print("height:${size.height} -- width:${size.width}"),
    //   aspectRatio = size.width/size.height
    // });
    super.initState();
  }

  @override
  void didUpdateWidget(PhotoTextPostWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    bool captionNotExists = widget.widgetData.caption.length == 1 &&
        widget.widgetData.caption[0].text.isEmpty;
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          renderHeader(),
          renderCaption(captionNotExists),
          widget.widgetData.properties.showMoments == 'true'
              ? renderMoments()
              : renderContent(),
          if (widget.widgetData.properties.ctaNavAction != null)
            if (widget.widgetData.properties.expiryDate == null ||
                (widget.widgetData.properties.expiryDate != null &&
                    DateTime.parse(
                            widget.widgetData.properties.expiryDate.toString())
                        .isAfter(DateTime.now())))
              renderJoinClassCTA(),
          renderLikesAndComments(),
          // render liked users
          if (getCurrentLikes() > 0 || widget.widgetData.comments > 0)
            renderLikesUserList(),
          // render divider in postDetailPage
          if (widget.widgetData.isPostDetailPage &&
              widget.widgetData.comments > 0)
            Padding(
                child: Container(height: 1, color: Colors.white10),
                padding: const EdgeInsets.only(
                    top: Spacings.x3, bottom: Spacings.x3)),
        ],
      ),
    );
  }

  Widget renderJoinClassCTA() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      child: SecondaryButton(
        () {
          RepositoryProvider.of<AnalyticsRepository>(context)
              .logButtonClickEvent(extraInfo: {
            "clickedOnPostCTA": true,
            "ctaURL": widget.widgetData.properties.ctaNavAction,
            "postId": widget.widgetData.activityId,
            "groupId": widget.widgetData.groupId,
            "postAuthorId": widget.widgetData.authorId,
            "postAuthorType": widget.widgetData.tag,
            "caption": widget.widgetData.caption,
            "authorCultUserId": widget.widgetData.properties.authorCultUserId,
            "post_created_by": widget.widgetData.properties.post_created_by
          });

          action_handler.Action action = action_handler.Action(
              type: ActionTypes.NAVIGATION,
              url: widget.widgetData.properties.ctaNavAction);
          ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
          PerformActionEvent event = PerformActionEvent(action);
          actionBloc.add(event);
        },
        widget.widgetData.properties.ctaTitle.toString(),
      ),
    );
  }

  Widget renderContent() {
    if (widget.widgetData.postType == "ONLY_TEXT") {
      return Container();
    }
    if (widget.widgetData.postType == "ONLY_VIDEO") {
      return Padding(
        padding: const EdgeInsets.fromLTRB(Spacings.x4, 0, Spacings.x4, 0),
        child: renderOnlyVideo(),
      );
    }
    if (widget.widgetData.postType == "ONLY_IMAGE") {
      return Padding(
        padding: const EdgeInsets.fromLTRB(Spacings.x4, 0, Spacings.x4, 0),
        child: widget.widgetData.aspectRatio != null
            ? renderOnlyPhotoAspRatio()
            : renderOnlyPhoto(),
      );
    }
    if (widget.widgetData.postType == "IMAGE_CAROUSEL") {
      return Padding(
        padding: const EdgeInsets.fromLTRB(Spacings.x4, 0, 0, 0),
        child: widget.widgetData.aspectRatio != null
            ? renderImageCarouseAspRatio()
            : renderImageCarousel(),
      );
    }
    if (widget.widgetData.postType == "VIDEO_CAROUSEL") {
      return Padding(
        padding: const EdgeInsets.fromLTRB(Spacings.x4, 0, 0, 0),
        child: renderVideoCarousel(),
      );
    }
    if (widget.widgetData.postType == "CELEBRATING_CLASSES" ||
        widget.widgetData.postType == "CELEBRATING_DAY_STREAK") {
      return renderCelebrationPost();
    }
    if (widget.widgetData.postType == "WELCOME_POST") {
      return renderNewbies();
    }
    if (widget.widgetData.postType == "POLL") {
      return renderPolls();
    }
    return Container();
  }

  Widget renderNewbies() {
    return Stack(
      alignment: Alignment.center,
      children: [
        const Padding(
          padding: EdgeInsets.symmetric(horizontal: 20),
          child: Image(
            image: AssetImage('assets/welcome_newbies.png'),
          ),
        ),
        const Positioned(
          top: 25,
          right: 20,
          child: Image(
            image: AssetImage('assets/mirage-welcome.png'),
          ),
        ),
        Padding(
          padding: const EdgeInsets.fromLTRB(70, 0, 70, 0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Text('Hiya newbies!',
                  style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.w400,
                      fontSize: 20,
                      fontFamily: "BethEllen")),
              const SizedBox(height: 25),
              Row(mainAxisAlignment: MainAxisAlignment.spaceEvenly, children: [
                for (int i = 0;
                    i <
                        ((widget.widgetData.properties.membersData?.length ??
                                    0) +
                                1) ~/
                            2;
                    i++)
                  renderNewbiesView(
                      widget.widgetData.properties.membersData![i]['userName'],
                      widget.widgetData.properties.membersData![i]['userImage'])
              ]),
              const SizedBox(height: 15),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  for (int i =
                          ((widget.widgetData.properties.membersData?.length ??
                                      0) +
                                  1) ~/
                              2;
                      i <
                          (widget.widgetData.properties.membersData?.length ??
                              0);
                      i++)
                    renderNewbiesView(
                        widget.widgetData.properties.membersData![i]
                            ['userName'],
                        widget.widgetData.properties.membersData![i]
                            ['userImage'])
                ],
              ),
              const SizedBox(height: 15),
              Column(
                children: const [
                  Text('WELCOME',
                      style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.w400,
                          fontSize: 35,
                          fontFamily: "BebasNeue")),
                  Text('TO THE CLAN!',
                      style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.w400,
                          fontSize: 26,
                          fontFamily: "BebasNeue")),
                ],
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget renderNewbiesView(String name, String? imageUrl) {
    return Column(
      children: [
        UserProfileIcon(profileDisplayText: name, profileImageUrl: imageUrl),
        const SizedBox(height: Spacings.x1),
        Text(name,
            style: const TextStyle(
                color: Colors.white, fontSize: 8, fontWeight: FontWeight.w400)),
      ],
    );
  }

  Widget renderCelebrationPost() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Stack(
        alignment: Alignment.center,
        children: [
          Container(height: 335),
          Lottie.asset(
            'assets/celebration.json',
            width: double.infinity,
          ),
          Positioned(
              left: 50,
              top: 85,
              right: 50,
              bottom: 50,
              child: Container(
                clipBehavior: Clip.hardEdge,
                decoration: BoxDecoration(
                  border: Border.all(
                    color: Colors.white38,
                    width: 1,
                  ),
                  borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(30),
                      topRight: Radius.circular(30),
                      bottomLeft: Radius.circular(10),
                      bottomRight: Radius.circular(10)),
                ),
                child: BackdropFilter(
                    blendMode: BlendMode.src,
                    filter: ImageFilter.blur(
                      sigmaX: 10,
                      sigmaY: 10,
                    ),
                    child: CustomPaint(
                      painter: HolePainter(
                          width: MediaQuery.of(context).size.width - 140,
                          height: MediaQuery.of(context).size.height - 135),
                    )),
              )),
          Positioned(
              top: 45,
              child: InkWell(
                onTap: () {
                  RepositoryProvider.of<AnalyticsRepository>(context)
                      .logButtonClickEvent(extraInfo: {
                    "clickOthersSocialProfileCta": "likedUsersList",
                    "socialUserId": widget.widgetData.properties.socialUserId
                  });
                  action_handler.Action action = action_handler.Action(
                      type: ActionTypes.NAVIGATION,
                      url:
                          'curefit://social_user_profile?cultUserId=${widget.widgetData.properties.socialUserId}');
                  ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
                  PerformActionEvent event = PerformActionEvent(action);
                  actionBloc.add(event);
                },
                child: UserProfileIcon(
                    profileDisplayText:
                        widget.widgetData.properties.userName.toString(),
                    profileImageUrl:
                        widget.widgetData.properties.imageUrl.toString(),
                    borderWidth: 1,
                    profileIconRadius: 40),
              )),
          Positioned(
            top:
                widget.widgetData.postType == 'CELEBRATING_CLASSES' ? 140 : 160,
            child: RotationTransition(
              turns: const AlwaysStoppedAnimation(-3 / 360),
              child: Column(
                children: [
                  Text(widget.widgetData.properties.celebrationData![0],
                      style: AuroraTheme.of(context)
                          .textStyle(TypescaleValues.P9, color: Colors.white)),
                  Text(widget.widgetData.properties.celebrationData![1],
                      style: TextStyle(
                          fontSize: widget.widgetData.postType ==
                                  'CELEBRATING_CLASSES'
                              ? 56
                              : 38,
                          fontWeight: FontWeight.w800,
                          fontStyle: FontStyle.italic,
                          color: Colors.white)),
                  Text(widget.widgetData.properties.celebrationData![2],
                      style: const TextStyle(
                          fontSize: 22,
                          fontWeight: FontWeight.w500,
                          fontStyle: FontStyle.italic,
                          color: Colors.white)),
                  if (widget.widgetData.postType == 'CELEBRATING_CLASSES')
                    Text(widget.widgetData.properties.celebrationData![3],
                        style: const TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w400,
                            fontStyle: FontStyle.italic,
                            color: Colors.white)),
                ],
              ),
            ),
          ),
          const Positioned(
              bottom: 10,
              child: Icon(CFIcons.logo_onBlack, color: Colors.white, size: 18)),
        ],
      ),
    );
  }

  Widget renderOnlyVideo() {
    return Stack(
      children: [
        ClipRRect(
          borderRadius: widget.widgetData.properties.showMoments == 'true'
              ? const BorderRadius.vertical(top: Radius.circular(Spacings.x1))
              : BorderRadius.circular(Spacings.x1),
          child: SizedBox(
            height: 0.75 * MediaQuery.of(context).size.width,
            width: MediaQuery.of(context).size.width,
            child: VideoView(
                showControls: true,
                useCache: false,
                showThumbnail: true,
                canShowVideo: false,
                thumbnailUrl: widget.widgetData.attachments[0].thumbnail,
                shouldLoop: true,
                showPlayButton: true,
                videoPlayerRegistry:
                    RepositoryProvider.of<VideoPlayerRegistry>(context),
                videoUrl: widget.widgetData.attachments[0].url),
          ),
        ),
        showMentionIcon()
      ],
    );
  }

  Widget renderOnlyPhotoAspRatio() {
    double aspectRatio = widget.widgetData.aspectRatio!;
    double sWidth = MediaQuery.of(context).size.width - 20;
    double height = sWidth / aspectRatio;
    return Stack(
      children: [
        ClipRRect(
            borderRadius: widget.widgetData.properties.showMoments == 'true'
                ? const BorderRadius.vertical(top: Radius.circular(Spacings.x1))
                : BorderRadius.circular(Spacings.x1),
            child: SizedBox(
              width: sWidth,
              height: height,
              child: GestureDetector(
                onTap: () {
                  showDialog(
                      context: context,
                      builder: (context) {
                        return CFPhotoView(
                          widget.widgetData.attachments[0].url,
                          aspectRatio,
                        );
                      });
                },
                child: CachedNetworkImage(
                  fit: BoxFit.cover,
                  width: sWidth,
                  height: height,
                  imageUrl: widget.widgetData.attachments[0].url,
                ),
              ),
            )),
        showMentionIcon()
      ],
    );
  }

  Widget renderOnlyPhoto() {
    Image image = Image.network(widget.widgetData.attachments[0].url);
    Completer<ui.Image> completer = Completer<ui.Image>();
    image.image.resolve(const ImageConfiguration()).addListener(
        ImageStreamListener((ImageInfo info, bool synchronousCall) {
      completer.complete(info.image);
    }));

    return Stack(
      children: [
        FutureBuilder(
            future: completer.future,
            builder: (BuildContext context, AsyncSnapshot<ui.Image> snapshot) {
              if (snapshot.hasData) {
                double aspectRatio =
                    snapshot.data!.width / snapshot.data!.height;
                double sWidth = 0.95 * (MediaQuery.of(context).size.width - 20);
                double height = sWidth / aspectRatio;
                return ClipRRect(
                    borderRadius:
                        widget.widgetData.properties.showMoments == 'true'
                            ? const BorderRadius.vertical(
                                top: Radius.circular(Spacings.x1))
                            : BorderRadius.circular(Spacings.x1),
                    child: SizedBox(
                      width: sWidth,
                      height: height,
                      child: GestureDetector(
                        onTap: () {
                          showDialog(
                              context: context,
                              builder: (context) {
                                return CFPhotoView(
                                  widget.widgetData.attachments[0].url,
                                  aspectRatio,
                                );
                              });
                        },
                        child: CachedNetworkImage(
                          fit: BoxFit.cover,
                          width: sWidth,
                          height: height,
                          imageUrl: widget.widgetData.attachments[0].url,
                        ),
                      ),
                    ));
              } else {
                return const FancyLoadingIndicator();
              }
            }),
        showMentionIcon()
      ],
    );
  }

  Widget showMentionIcon() {
    if (widget.widgetData.properties.taggedUsers == null ||
        widget.widgetData.properties.taggedUsers?.isEmpty == true) {
      return Container();
    }
    return Positioned(
        top: Spacings.x2,
        left: Spacings.x2,
        child: CircleAvatar(
          radius: 12,
          child: Padding(
            padding: const EdgeInsets.all(3),
            child: GestureDetector(
                onTap: () {
                  showBottomTray(
                      context: context,
                      child: MentionModalSheet(
                          title:
                              "User Mentions (${widget.widgetData.properties.taggedUsers?.length})",
                          mentions:
                              widget.widgetData.properties.taggedUsers ?? []));
                },
                child: const Icon(CFIcons.user, color: Colors.white, size: 15)),
          ),
          backgroundColor: Colors.black26,
        ));
  }

  int getFirstImageIndex() {
    for (int i = 0; i < widget.widgetData.attachments.length; i++) {
      if (!widget.widgetData.attachments[i].isVideo) {
        return i;
      }
    }
    return -1;
  }

  List<Widget> getImageItems(double height, double width) {
    List<Widget> res = [];
    int length = widget.widgetData.attachments.length;
    for (int i = 0; i < length; i++) {
      if (widget.widgetData.attachments[i].isVideo) {
        res.add(Padding(
          padding: const EdgeInsets.only(right: 10.0),
          child: renderImageCarouselVideoItem(i, length, height, width),
        ));
      } else {
        res.add(Padding(
          padding: const EdgeInsets.only(right: 10.0),
          child: renderImageCarouselImage(i, length, height, width),
        ));
      }
    }
    return res;
  }

  Widget renderImageCarouselImage(
      int index, int total, double height, double width) {
    return GestureDetector(
      onTap: () {},
      child: Stack(
          // alignment: Alignment.center,
          children: [
            ClipRRect(
                borderRadius: widget.widgetData.properties.showMoments == 'true'
                    ? const BorderRadius.vertical(
                        top: Radius.circular(Spacings.x1))
                    : BorderRadius.circular(Spacings.x1),
                child: SizedBox(
                  width: width,
                  height: height,
                  child: GestureDetector(
                    onTap: () {
                      showDialog(
                          context: context,
                          builder: (context) {
                            return CFPhotoView(
                              widget.widgetData.attachments[index].url,
                              width / height,
                            );
                          });
                    },
                    child: CachedNetworkImage(
                      fit: BoxFit.cover,
                      width: width,
                      height: height,
                      imageUrl: widget.widgetData.attachments[index].url,
                    ),
                  ),
                )),
            Positioned(
              top: 15,
              right: 15,
              child: Container(
                decoration: BoxDecoration(
                    borderRadius: const BorderRadius.all(Radius.circular(15)),
                    color: Colors.black.withOpacity(0.5)),
                padding: const EdgeInsets.fromLTRB(10, 5, 10, 5),
                child: Text(
                  '${index + 1}/$total',
                  style: const TextStyle(color: Colors.white),
                ),
              ),
            ),
            showMentionIcon()
          ]),
    );
  }

  Widget renderImageCarouselVideoItem(
      int index, int total, double height, double width) {
    return Stack(
      children: [
        ClipRRect(
          borderRadius: widget.widgetData.properties.showMoments == 'true'
              ? const BorderRadius.vertical(top: Radius.circular(Spacings.x1))
              : BorderRadius.circular(Spacings.x1),
          child: SizedBox(
            height: height,
            width: width,
            child: VideoView(
                showControls: true,
                useCache: false,
                showThumbnail: true,
                canShowVideo: false,
                thumbnailUrl: widget.widgetData.attachments[index].thumbnail,
                shouldLoop: true,
                showPlayButton: true,
                videoPlayerRegistry:
                    RepositoryProvider.of<VideoPlayerRegistry>(context),
                videoUrl: widget.widgetData.attachments[index].url),
          ),
        ),
        Positioned(
          top: 15,
          right: 15,
          child: Container(
            decoration: BoxDecoration(
                borderRadius: const BorderRadius.all(Radius.circular(15)),
                color: Colors.black.withOpacity(0.5)),
            padding: const EdgeInsets.fromLTRB(10, 5, 10, 5),
            child: Text(
              '${index + 1}/$total',
              style: const TextStyle(color: Colors.white),
            ),
          ),
        ),
        showMentionIcon()
      ],
    );
  }

  Widget renderImageCarouseAspRatio() {
    double aspectRatio = widget.widgetData.aspectRatio!;
    double sWidth = 0.95 * (MediaQuery.of(context).size.width - 20);
    double height = sWidth / aspectRatio;
    return Stack(
      children: [
        CarouselSlider(
          items: getImageItems(height, sWidth),
          options: CarouselOptions(
            // initialPage: 1,
            padEnds: false,
            viewportFraction: 0.95,
            // aspectRatio: ,
            height: height,
            enableInfiniteScroll: false,
          ),
        ),
        showMentionIcon()
      ],
    );
  }

  Widget renderImageCarousel() {
    int firstImageIndex = getFirstImageIndex();
    Image image =
        Image.network(widget.widgetData.attachments[firstImageIndex].url);
    Completer<ui.Image> completer = Completer<ui.Image>();
    image.image.resolve(const ImageConfiguration()).addListener(
        ImageStreamListener((ImageInfo info, bool synchronousCall) {
      completer.complete(info.image);
    }));

    return FutureBuilder(
        future: completer.future,
        builder: (BuildContext context, AsyncSnapshot<ui.Image> snapshot) {
          if (snapshot.hasData) {
            double aspectRatio = snapshot.data!.width / snapshot.data!.height;
            double sWidth = 0.95 * (MediaQuery.of(context).size.width - 20);
            double height = sWidth / aspectRatio;
            return Stack(
              children: [
                CarouselSlider(
                  items: getImageItems(height, sWidth),
                  options: CarouselOptions(
                    // initialPage: 1,
                    padEnds: false,
                    viewportFraction: 0.95,
                    // aspectRatio: ,
                    height: height,
                    enableInfiniteScroll: false,
                  ),
                ),
                showMentionIcon()
              ],
            );
          } else {
            return const FancyLoadingIndicator();
          }
        });
  }

  Widget renderVideoCarousel() {
    double width = 0.95 * (MediaQuery.of(context).size.width - 20);
    double height = 0.75 * width;
    return Stack(
      children: [
        CarouselSlider(
          items: getImageItems(height, width),
          options: CarouselOptions(
            padEnds: false,
            viewportFraction: 0.95,
            height: height,
            enableInfiniteScroll: false,
          ),
        ),
        showMentionIcon()
      ],
    );
  }

  Widget streakCard() {
    return Padding(
      padding: const EdgeInsets.only(right: 20),
      child: Container(
        // height: 220,
        // width: 150,
        child: BlurView(
          borderRadius: 10,
          opacity: 0.1,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Padding(
                padding: const EdgeInsets.only(left: 30, right: 30, top: 15),
                child: UserProfileIcon(
                  profileDisplayText: "card.name.toString()",
                  // profileImageUrl: card.buddyDisplayPicture,
                  profileIconRadius: 45,
                ),
              ),
              const SizedBox(
                height: 15,
              ),
              Text(
                "card.active",
                style: AuroraTheme.of(context)
                    .textStyle(TypescaleValues.H4, color: Colors.white),
              ),
              const SizedBox(height: 5),
              Text(
                "gfjhjh",
                style: AuroraTheme.of(context)
                    .textStyle(TypescaleValues.P8, color: Colors.white70),
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget renderStreakCards(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                "Top performers near you",
                style: AuroraTheme.of(context)
                    .textStyle(TypescaleValues.H4, color: Colors.white),
              ),
              Row(
                children: [
                  Text(
                    "See All",
                    style: AuroraTheme.of(context)
                        .textStyle(TypescaleValues.P5, color: Colors.white70),
                  ),
                  const Padding(
                    padding:
                        EdgeInsets.only(left: Spacings.x1, right: Spacings.x4),
                    child: Icon(CFIcons.right_arrow,
                        color: Colors.white, size: 12),
                  )
                ],
              ),
            ],
          ),
          const SizedBox(height: Spacings.x3),
          SizedBox(
            height: 173,
            child: ListView.builder(
                physics: const ClampingScrollPhysics(),
                scrollDirection: Axis.horizontal,
                shrinkWrap: true,
                itemBuilder: (BuildContext context, int index) {
                  return streakCard();
                },
                itemCount: 5),
          ),
        ],
      ),
    );
  }

  renderMoments() {
    return Padding(
      padding: const EdgeInsets.fromLTRB(Spacings.x4, 0, Spacings.x4, 0),
      child: Moments(
        attachments: widget.widgetData.attachments,
        heading: widget.widgetData.properties.momentTitle.toString(),
        title: "${widget.widgetData.properties.classLocation ?? ""}"
            "${widget.widgetData.properties.clasDate != null ? ' • ' : ""}"
            "${widget.widgetData.properties.clasDate ?? ""}"
            "${widget.widgetData.properties.classTimings != null ? ' • ' : ""}"
            "${widget.widgetData.properties.classTimings ?? ""}",
        cardType: EnclosedCatalogCardType.SMALL,
        mentions: widget.widgetData.properties.taggedUsers,
      ),
    );
  }

  renderPolls() {
    return Padding(
      padding: const EdgeInsets.fromLTRB(Spacings.x4, 0, Spacings.x4, 0),
      child: BlurView(
        blurType: BlurType.HIGH,
        borderRadius: 10,
        child: Container(
            padding: const EdgeInsets.all(Spacings.x4),
            child: CFRadioTileList(widget.widgetData)),
      ),
    );
  }

  String getPostedTime() {
    var postedTime = DateTime.now().difference(widget.widgetData.postedTime);
    int minutesDifference = postedTime.inMinutes;
    if (minutesDifference == 0) {
      return "1m";
    }
    if (minutesDifference < 60) {
      return "${minutesDifference}m";
    }
    if (minutesDifference >= 60 && minutesDifference < 1440) {
      return "${minutesDifference ~/ 60}h";
    }
    if (minutesDifference >= 1440 && minutesDifference <= 43200) {
      return "${minutesDifference ~/ 1440}d";
    }
    if (minutesDifference >= 43200 && minutesDifference <= 15768000) {
      return "${minutesDifference ~/ 43200}m";
    }
    if (minutesDifference >= 15768000) {
      return "${minutesDifference ~/ 15768000}y";
    }
    return minutesDifference.toString();
  }

  Widget renderInitial(String name) {
    return Container();
  }

  Widget renderHeader() {
    String postedTime = getPostedTime();
    return Padding(
      padding:
          const EdgeInsets.fromLTRB(Spacings.x4, 0, Spacings.x4, Spacings.x3),
      child: IntrinsicHeight(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          mainAxisSize: MainAxisSize.max,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Flexible(
              flex: 17,
              child: Row(
                children: [
                  GestureDetector(
                      onTap: () {
                        if (widget.widgetData.authorId != null) {
                          RepositoryProvider.of<AnalyticsRepository>(context)
                              .logButtonClickEvent(extraInfo: {
                            "clickOthersSocialProfileCta": "postWidgetHeader",
                            "socialUserId": widget.widgetData.authorId
                          });
                          ActionBloc actionBloc =
                              BlocProvider.of<ActionBloc>(context);
                          PerformActionEvent event = PerformActionEvent(
                              widget.widgetData.userProfileAction);
                          actionBloc.add(event);
                        }
                      },
                      child: UserProfileIcon(
                          profileDisplayText: widget.widgetData.userName,
                          profileImageUrl: widget.widgetData.userImage)),
                  const SizedBox(width: Spacings.x2),
                  Flexible(
                    child: Container(
                      child: Column(
                        mainAxisSize: MainAxisSize.max,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          GestureDetector(
                            onTap: () {
                              if (widget.widgetData.authorId != null) {
                                ActionBloc actionBloc =
                                    BlocProvider.of<ActionBloc>(context);
                                PerformActionEvent event = PerformActionEvent(
                                    widget.widgetData.userProfileAction);
                                actionBloc.add(event);
                              }
                              print("On Profile Clicked");
                            },
                            child: Row(
                              children: [
                                Text(
                                  widget.widgetData.userName,
                                  style: AuroraTheme.of(context).textStyle(
                                      TypescaleValues.P3,
                                      color: Colors.white),
                                ),
                                const SizedBox(
                                  width: Spacings.x1,
                                ),
                                if (widget.widgetData.tag != null)
                                  BlurView(
                                    child: Padding(
                                      padding: const EdgeInsets.fromLTRB(
                                          10, 2, 10, 2),
                                      child: Text(
                                        widget.widgetData.tag!,
                                        style: AuroraTheme.of(context)
                                            .textStyle(TypescaleValues.P7,
                                                color: Colors.white),
                                      ),
                                    ),
                                  )
                              ],
                            ),
                          ),
                          GestureDetector(
                            onTap: () {
                              ActionBloc actionBloc =
                                  BlocProvider.of<ActionBloc>(context);
                              PerformActionEvent event = PerformActionEvent(
                                  widget.widgetData.groupAction);
                              actionBloc.add(event);
                            },
                            child: Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Flexible(
                                    child: Text(
                                      widget.widgetData.groupName,
                                      style: AuroraTheme.of(context).textStyle(
                                          TypescaleValues.P8,
                                          color: const Color(0xffE0E0E0)),
                                    ),
                                  ),
                                  Text(
                                    ' · ',
                                    style: AuroraTheme.of(context).textStyle(
                                        TypescaleValues.P8,
                                        color: Colors.white60),
                                  ),
                                  Text(postedTime,
                                      style: AuroraTheme.of(context).textStyle(
                                          TypescaleValues.P8,
                                          color: const Color(0xff828282)))
                                ]),
                          )
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Flexible(
              flex: 2,
              child: GestureDetector(
                  onTap: () =>
                      {showDropDownOptions(context, widget.widgetData.options)},
                  child: const Icon(Icons.more_horiz,
                      color: Colors.white60, size: 24)),
            )
          ],
        ),
      ),
    );
  }

  showDropDownOptions(BuildContext context, List<GroupMenuOption> options) {
    List<DropdownOption> listOptions = options.map((e) {
      action_handler.Action newAction = e.action;
      Function onTap;
      if (newAction.type == ActionTypes.DELETE_POST) {
        onSuccess() {
          if (widget.widgetData.pageType == "profilePage") {
            BlocProvider.of<OtherProfileBloc>(context)
                .add(DeletePostInProfile(widget.widgetData.activityId));
            return;
          }
          if (!widget.widgetData.isPostDetailPage) {
            BlocProvider.of<PostPageBloc>(context)
                .add(DeletePost(widget.widgetData.activityId));
            return;
          }
          if (Navigator.canPop(context)) {
            Navigator.pop(context);
          } else if (!Navigator.canPop(context)) {
            ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
            actionBloc.add(CloseApplicationEvent(shouldReset: false));
          }
        }

        Map<String, dynamic> analyticsInfo = {
          "deleteAPost": "true",
          "postId": widget.widgetData.activityId,
          "groupId": widget.widgetData.groupId,
          "postAuthorId": widget.widgetData.authorId,
          "postAuthorType": widget.widgetData.tag
        };

        onTap = getActionFunction(context, e.action,
            onSuccess: onSuccess, analyticsInfo: analyticsInfo);
      } else {
        Map<String, dynamic> analyticsInfo = {
          "sharePostDetails": "true",
          "postId": widget.widgetData.activityId,
          "groupId": widget.widgetData.groupId,
          "postAuthorId": widget.widgetData.authorId,
          "postAuthorType": widget.widgetData.tag
        };
        onTap =
            getActionFunction(context, e.action, analyticsInfo: analyticsInfo);
      }

      return DropdownOption(e, onTap);
    }).toList();

    showBottomTray(context: context, child: DropdownOptionsList(listOptions));
  }

  Widget renderCaption(bool captionNotExists) {
    if (!captionNotExists) {
      return Padding(
        padding:
            const EdgeInsets.fromLTRB(Spacings.x4, 0, Spacings.x4, Spacings.x3),
        child: RichTextReadMoreWidget(
            widget.widgetData.caption,
            AuroraTheme.of(context)
                .textStyle(TypescaleValues.P5, color: Colors.white60),
            3),
      );
    }
    return Container();
  }

  Icon getShareIcon() {
    if (Platform.isIOS) {
      return const Icon(CFIcons.share, color: Colors.white, size: 18);
    }
    return const Icon(CFIcons.share_2, color: Colors.white, size: 18);
  }

  Widget renderLikesAndComments() {
    return Padding(
      padding:
          const EdgeInsets.fromLTRB(Spacings.x4, 0, Spacings.x4, Spacings.x1),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          renderLikes(),
          const SizedBox(width: 10),
          renderComments(),
          const SizedBox(width: 10),
          GestureDetector(
            onTap: () {
              String? shareUrl;
              widget.widgetData.options.map((e) async {
                action_handler.Action newAction = e.action;
                if (newAction.type == ActionTypes.SHARE_POST_DETAILS) {
                  shareUrl = e.action.meta!['url'];
                  if (shareUrl!.isEmpty) {
                    PostPageRepository postPageRepository = PostPageRepository(
                        client: RepositoryProvider.of<NetworkClient>(context));
                    var response = await postPageRepository
                        .getSharablePostUrl(e.action.meta!['postId']);
                    shareUrl = response['url'];
                  }
                  RepositoryProvider.of<AnalyticsRepository>(context)
                      .logButtonClickEvent(extraInfo: {
                    "sharePostDetails": "true",
                    "postId": widget.widgetData.activityId,
                    "groupId": widget.widgetData.groupId,
                    "postAuthorId": widget.widgetData.authorId,
                    "postAuthorType": widget.widgetData.tag,
                    "caption": widget.widgetData.caption,
                    "authorCultUserId":
                        widget.widgetData.properties.authorCultUserId,
                    "post_created_by":
                        widget.widgetData.properties.post_created_by
                  });
                  Share.share(shareUrl.toString());
                }
              }).toList();
            },
            child: getShareIcon(),
          )
        ],
      ),
    );
  }

  Widget renderLikesUserList() {
    return Padding(
      padding:
          const EdgeInsets.fromLTRB(Spacings.x4, 0, Spacings.x4, Spacings.x3),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          InkWell(
            onTap: () {
              showBottomTray(
                context: context,
                child: LikeUsersModal(
                    activityId: widget.widgetData.activityId,
                    title: 'Likes (${getCurrentLikes().toString()})'),
              );
            },
            child: RichText(
                text: TextSpan(
              children: [
                if (getCurrentLikes() > 0)
                  TextSpan(
                      text: getCurrentLikes().toString(),
                      style: AuroraTheme.of(context).textStyle(
                          TypescaleValues.P8,
                          color: Colors.white60)),
                if (getCurrentLikes() > 0)
                  TextSpan(
                      text: getCurrentLikes() > 1 ? " likes" : " like",
                      style: AuroraTheme.of(context)
                          .textStyle(TypescaleValues.P8, color: Colors.white60))
              ],
            )),
          ),
          InkWell(
            onTap: () {
              if (!widget.widgetData.isPostDetailPage) {
                RepositoryProvider.of<AnalyticsRepository>(context)
                    .logButtonClickEvent(extraInfo: {
                  "postDetailPageCta": "true",
                  "postId": widget.widgetData.activityId,
                  "groupId": widget.widgetData.groupId,
                  "postAuthorId": widget.widgetData.authorId,
                  "postAuthorType": widget.widgetData.tag,
                  "caption": widget.widgetData.caption,
                  "authorCultUserId":
                      widget.widgetData.properties.authorCultUserId,
                  "post_created_by":
                      widget.widgetData.properties.post_created_by
                });
                ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
                PerformActionEvent event =
                    PerformActionEvent(widget.widgetData.postDetailsAction);
                actionBloc.add(event);
              }
            },
            child: RichText(
                text: TextSpan(
              children: [
                if (widget.widgetData.comments > 0)
                  TextSpan(
                    text: displayCount(widget.widgetData.comments),
                    style: AuroraTheme.of(context)
                        .textStyle(TypescaleValues.P8, color: Colors.white60),
                  ),
                if (widget.widgetData.comments > 0)
                  TextSpan(
                      text: widget.widgetData.comments > 1
                          ? " comments"
                          : " comment",
                      style: AuroraTheme.of(context)
                          .textStyle(TypescaleValues.P8, color: Colors.white60))
              ],
            )),
          )
        ],
      ),
    );
  }

  Widget renderLikes() {
    var likeIcon = isLiked ? Icons.favorite : Icons.favorite_border;
    var likeColor = isLiked ? Colors.pink : Colors.white;
    return BlocListener<PostReactionBloc, PostReactionState>(
      listenWhen: (previous, current) {
        return previous is PostingReactionState &&
            current is NotPostedReactionState &&
            current.activityId == widget.widgetData.activityId;
      },
      listener: (context, state) {
        if (state is NotPostedReactionState) {
          setState(() {
            isLiked = !isLiked;
          });
          Toast.show("Network Failed", context,
              duration: Toast.lengthLong,
              gravity: Toast.top,
              textStyle: AuroraTheme.of(context)
                  .textStyle(TypescaleValues.P3, color: Colors.black),
              backgroundColor: Colors.white);
        }
      },
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          InkWell(
            onTap: () async {
              RepositoryProvider.of<AnalyticsRepository>(context)
                  .logButtonClickEvent(extraInfo: {
                "likedAPost": (!isLiked).toString(),
                "postId": widget.widgetData.activityId,
                "groupId": widget.widgetData.groupId,
                "postAuthorId": widget.widgetData.authorId,
                "postAuthorType": widget.widgetData.tag,
                "caption": widget.widgetData.caption,
                "authorCultUserId":
                    widget.widgetData.properties.authorCultUserId,
                "post_created_by": widget.widgetData.properties.post_created_by
              });
              setState(() {
                isLiked = !isLiked;
              });

              final reactionBloc = BlocProvider.of<PostReactionBloc>(context);
              reactionBloc
                  .add(SendReaction(widget.widgetData.activityId, isLiked));
              if (widget.widgetData.pageType == "profilePage") {
                BlocProvider.of<OtherProfileBloc>(context).add(
                    ReactionPostInProfile(
                        widget.widgetData.activityId, isLiked));
                return;
              }
              if (widget.widgetData.isPostDetailPage) {
                BlocProvider.of<PostDetailBloc>(context).add(
                    ReactionPostInPostDetail(
                        widget.widgetData.activityId, isLiked));
                return;
              }
              if (!widget.widgetData.isPostDetailPage) {
                BlocProvider.of<PostPageBloc>(context)
                    .add(ReactionPost(widget.widgetData.activityId, isLiked));
              }
            },
            child: HeartAnimationWidget(
                alwaysShowAnimation: true,
                child: Icon(likeIcon, color: likeColor, size: 24),
                isAnimating: isLiked,
                onEnd: () {}),
          ),
        ],
      ),
    );
  }

  int getCurrentLikes() {
    if (isLiked != widget.widgetData.isLiked) {
      if (isLiked) {
        return widget.widgetData.likes + 1;
      } else {
        return widget.widgetData.likes - 1;
      }
    }
    return widget.widgetData.likes;
  }

  String displayCount(int value) {
    if (value > 999) {
      return "${value ~/ 1000}.${(value % 1000) ~/ 100}K";
    }
    return value.toString();
  }

  Widget renderComments() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        InkWell(
          onTap: () async {
            if (!widget.widgetData.isPostDetailPage) {
              RepositoryProvider.of<AnalyticsRepository>(context)
                  .logButtonClickEvent(extraInfo: {
                "postDetailPageCta": "true",
                "postId": widget.widgetData.activityId,
                "groupId": widget.widgetData.groupId,
                "postAuthorId": widget.widgetData.authorId,
                "postAuthorType": widget.widgetData.tag,
                "caption": widget.widgetData.caption,
                "authorCultUserId":
                    widget.widgetData.properties.authorCultUserId,
                "post_created_by": widget.widgetData.properties.post_created_by
              });
              ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
              action_handler.Action action = action_handler.Action(
                  type: ActionTypes.NAVIGATION,
                  url:
                      "curefit://post_detail_page?postId=${widget.widgetData.activityId}&showFocus=true");
              PerformActionEvent event = PerformActionEvent(action);
              actionBloc.add(event);
            }
          },
          child: Padding(
            padding: const EdgeInsets.fromLTRB(
                Spacings.x2, 10, Spacings.x2, Spacings.x2),
            child: Row(
              children: const [
                Padding(
                  padding: EdgeInsets.only(top: 2.0),
                  child: Icon(Icons.mode_comment_outlined,
                      color: Colors.white, size: 22),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

class PostAttachment {
  String url;
  bool isVideo;
  String? thumbnail;
  PostAttachment(this.url, this.isVideo, this.thumbnail);
}

class Properties {
  String? postType;
  List<dynamic>? celebrationData;
  String? userName;
  String? imageUrl;
  String? ctaNavAction;
  String? expiryDate;
  String? ctaTitle;
  String? className;
  String? classLocation;
  String? classTimings;
  String? clasDate;
  String? momentTitle;
  String? showMoments;
  String? socialUserId;
  List<dynamic>? membersData;
  String? momentsCaption;
  List<dynamic>? taggedUsers;
  String? aspectRatio;
  String? authorCultUserId;
  String? post_created_by;

  Properties(
      this.postType,
      this.celebrationData,
      this.userName,
      this.imageUrl,
      this.ctaNavAction,
      this.expiryDate,
      this.ctaTitle,
      this.className,
      this.classLocation,
      this.classTimings,
      this.clasDate,
      this.momentTitle,
      this.showMoments,
      this.socialUserId,
      this.membersData,
      this.momentsCaption,
      this.taggedUsers,
      this.aspectRatio,
      this.authorCultUserId,
      this.post_created_by);
}

List<RichTextReadMoreItem> getRichTextReadMoreItem(
    String caption, int start, int end) {
  List<RichTextReadMoreItem> items = [];
  if (end - start > 20) {
    int i = start;
    for (i = start; ((i < end) && (i + 20 < end));) {
      items.add(RichTextReadMoreItem(caption.substring(i, i + 20)));
      i = i + 20;
    }
    items.add(RichTextReadMoreItem(caption.substring(i, end)));
  } else {
    items.add(RichTextReadMoreItem(caption.substring(start, end)));
  }
  return items;
}

class PhotoTextPostWidgetData implements IWidgetData {
  @override
  WidgetInfo? widgetInfo;
  @override
  WidgetTypes widgetType;
  String? pageType;
  String? userImage;
  String userName;
  String? tag;
  String groupName;
  DateTime postedTime;
  List<RichTextReadMoreItem> caption;
  List<PostAttachment> attachments;
  String postType;
  int likes;
  int comments;
  bool isLiked;
  String activityId;
  action_handler.Action userProfileAction;
  action_handler.Action groupAction;
  action_handler.Action postDetailsAction;
  List<GroupMenuOption> options;
  bool isPostDetailPage;
  double? aspectRatio;
  Properties properties;
  String groupId;
  String? authorId;
  List<Mention>? mentions;
  PollData? pollData;
  bool? isMyPost;

  PhotoTextPostWidgetData(
      {required this.widgetType,
      required this.pageType,
      required this.userImage,
      required this.postedTime,
      required this.userName,
      this.tag,
      required this.groupName,
      required this.caption,
      required this.postType,
      required this.attachments,
      required this.likes,
      required this.comments,
      required this.isLiked,
      required this.activityId,
      required this.userProfileAction,
      required this.authorId,
      required this.postDetailsAction,
      required this.groupId,
      required this.groupAction,
      required this.options,
      required this.isPostDetailPage,
      this.aspectRatio,
      required this.properties,
      this.mentions,
      this.pollData,
      this.isMyPost});

  factory PhotoTextPostWidgetData.fromJson(
      dynamic _widgetData, WidgetInfo widgetInfo, WidgetTypes widgetType) {
    dynamic widgetData = _widgetData["data"];
    bool isPostDetailPage = _widgetData['actionButtonDisabled'];
    // List<RichTextReadMoreItem> caption = widgetData['caption'].map<RichTextReadMoreItem>((e) => RichTextReadMoreItem.fromJson(e)).toList();
    // List<String> imageUrls = widgetData['imageUrls'].map<String>((e) => e.toString()).toList();
    DateTime postedTime =
        DateTime.fromMillisecondsSinceEpoch(widgetData['created_at'] * 1000);

    bool getIsLiked(dynamic myReactions) {
      if (myReactions == null) {
        return false;
      }
      return true;
    }

    List<Mention> getMentions(String caption, dynamic mentions) {
      List<Mention> list = [];
      if (mentions == null) {
        return list;
      }
      list = mentions['en']
          .map<Mention>((e) => Mention(
              caption.substring(e["start_index"] + 1, e["end_index"]),
              e["user_id"],
              e["start_index"],
              e["end_index"]))
          .toList();
      return list;
    }

    List<HashTag> getHashTag(String caption) {
      List<HashTag> tags = [];
      int start = 0;
      while (start < caption.length) {
        if (caption[start] == '#' &&
            (start > 0
                ? caption[start - 1] == ' ' ||
                    caption[start - 1] == '\n' ||
                    (tags.isEmpty ? false : tags.last.end == start)
                : true)) {
          int end = start + 1;
          while (end < caption.length &&
              caption[end].contains(RegExp(r'^[a-zA-Z0-9_]+$'))) {
            end++;
          }
          if ((end >= caption.length ||
                  caption[end].contains(RegExp(r'^[#\s]+$')) ||
                  caption[end] == '\n') &&
              end - start > 1) {
            HashTag tag =
                HashTag(caption.substring(start + 1, end), start, end);
            tags.add(tag);
          }
          start = end - 1;
        }
        start++;
      }
      return tags;
    }

    List<HyperLink> getHyperlinks(String caption) {
      List<HyperLink> links = [];
      List<LinkifyElement> listUrls =
          linkify(caption, linkifiers: [const CFUrlLinkifier()]);
      int index = 0;
      for (LinkifyElement url in listUrls) {
        int start = caption.indexOf(url.text, index);
        if (start > 0 &&
            caption[start - 1] != ' ' &&
            caption[start - 1] != '\n') {
          continue;
        }
        index = start + url.text.length;
        HyperLink hyperLink = HyperLink(url.text, start, index);
        links.add(hyperLink);
      }
      return links;
    }

    List<RichTextReadMoreItem> getCaption(
        String? caption, dynamic mentions, String? isMoment) {
      List<RichTextReadMoreItem> items = [];
      if (caption == null) {
        items.add(RichTextReadMoreItem(caption ?? ""));
      } else {
        int start = 0;
        List<Mention> listMentions =
            isMoment == null ? getMentions(caption, mentions) : [];
        List<HashTag> listHashTags = getHashTag(caption);
        List<HyperLink> listLinks = getHyperlinks(caption);

        List<dynamic> listItems = [];
        listItems.addAll(listHashTags);
        listItems.addAll(listMentions);
        listItems.addAll(listLinks);
        listItems.sort((a, b) => a.start < b.start ? -1 : 1);

        for (int i = 0; i < listItems.length; i++) {
          items.addAll(
              getRichTextReadMoreItem(caption, start, listItems[i].start));
          switch (listItems[i]!.type) {
            case 'MENTION':
              items.add(RichTextReadMoreItem(
                  caption.substring(listItems[i].start, listItems[i].end),
                  isClickable: true,
                  textStyle: RichTextReadMoreStyle.BOLD,
                  action: action_handler.Action(
                      type: ActionTypes.NAVIGATION,
                      url:
                          "curefit://social_user_profile?cultUserId=${listItems[i]?.userId}")));
              break;
            case 'HASHTAG':
              items.add(RichTextReadMoreItem(
                  caption.substring(listItems[i].start, listItems[i].end),
                  isClickable: true,
                  textStyle: RichTextReadMoreStyle.TAGS,
                  action: action_handler.Action(
                      type: ActionTypes.NAVIGATION,
                      url:
                          "curefit://searched_posts_page?tag=${listItems[i]?.hashTag}")));
              break;
            case 'HYPERLINK':
              items.add(RichTextReadMoreItem(
                listItems[i].url,
                isClickable: true,
                textStyle: RichTextReadMoreStyle.HYPERLINK,
                isHyperlink: true,
              ));
              break;
          }
          start = listItems[i].end;
        }
        items.addAll(getRichTextReadMoreItem(caption, start, caption.length));
      }
      return items;
    }

    List<PostAttachment> getAttachments(dynamic attachments) {
      List<PostAttachment> list = [];
      if (attachments != null) {
        attachments.forEach((e) {
          if (e != null) {
            if (e["video"] != null) {
              list.add(PostAttachment(e["video"], true, e["image"]));
            } else {
              list.add(PostAttachment(e["image"], false, null));
            }
          }
        });
        // list = attachments.map<PostAttachment>((e) {
        //   // if(e == null) {
        //   //   return null;
        //   // }
        // if(e["video"] != null) {
        //   return PostAttachment(e["video"], true, e["image"]);
        // } else {
        //   return PostAttachment(e["image"], false, null);
        // }
        // }).toList();
      }
      return list;
    }

    List<PostAttachment> attachments =
        getAttachments(widgetData["content"][0]["attachments"]);

    String getPostType(
        List<PostAttachment> attachments, Properties properties) {
      if (widgetData['poll'] != null) {
        return "POLL";
      }
      if (widgetData['properties'] != null &&
          widgetData['properties']['POST_TYPE'] != null) {
        return widgetData['properties']['POST_TYPE'];
      }
      if (attachments.isEmpty) {
        return "ONLY_TEXT";
      }
      if (attachments.length == 1) {
        if (attachments[0].isVideo) {
          return "ONLY_VIDEO";
        }
        return "ONLY_IMAGE";
      }
      for (int i = 0; i < attachments.length; i++) {
        if (!attachments[i].isVideo) {
          return "IMAGE_CAROUSEL";
        }
      }
      return "VIDEO_CAROUSEL";
    }

    int getLikesCount(dynamic reactions) {
      if (reactions == null) return 0;
      return int.parse(reactions['like']);
    }

    List<GroupMenuOption> options = _widgetData['options']
        .map<GroupMenuOption>((e) => GroupMenuOption.fromJson(e))
        .toList();

    List<dynamic>? getCelebrationData() {
      if (widgetData['properties']?['CELEBRATION_DATA'] != null) {
        List<dynamic> ret =
            json.decode(widgetData['properties']['CELEBRATION_DATA'])['data'];
        return ret;
      }
      return null;
    }

    List<dynamic>? getMembersData() {
      if (widgetData['properties']?['MEMBERS_DATA'] != null) {
        List<dynamic> ret =
            json.decode(widgetData['properties']['MEMBERS_DATA'])['data'];
        return ret;
      }
      return null;
    }

    List<dynamic>? getTaggedUsers() {
      if (widgetData['properties']?['TAGGED_USERS'] != null) {
        List<dynamic> ret =
            json.decode(widgetData['properties']['TAGGED_USERS']);
        return ret;
      }
      return null;
    }

    Properties properties = Properties(
      widgetData['properties'] != null
          ? widgetData['properties']['POST_TYPE']
          : null,
      getCelebrationData(),
      widgetData['properties'] != null
          ? widgetData['properties']['USER_NAME']
          : null,
      widgetData['properties'] != null
          ? widgetData['properties']['IMAGE_URL']
          : null,
      widgetData['properties'] != null
          ? widgetData['properties']['CTA_NAV_ACTION']
          : null,
      widgetData['properties'] != null
          ? widgetData['properties']['EXPIRY_DATE']
          : null,
      widgetData['properties'] != null
          ? widgetData['properties']['CTA_TITLE']
          : null,
      widgetData['properties'] != null
          ? widgetData['properties']['CLASS_NAME']
          : null,
      widgetData['properties'] != null
          ? widgetData['properties']['CLASS_LOCATION']
          : null,
      widgetData['properties'] != null
          ? widgetData['properties']['CLASS_TIMIMGS']
          : null,
      widgetData['properties'] != null
          ? widgetData['properties']['CLASS_DATE']
          : null,
      widgetData['properties'] != null
          ? widgetData['properties']['MOMENT_TITLE']
          : null,
      widgetData['properties'] != null
          ? widgetData['properties']['SHOW_MOMENTS']
          : null,
      widgetData['properties'] != null
          ? widgetData['properties']['socialUserId']
          : null,
      getMembersData(),
      widgetData['properties'] != null
          ? widgetData['properties']['MOMENTS_CAPTION']
          : null,
      getTaggedUsers(),
      widgetData['properties'] != null
          ? widgetData['properties']['aspectRatio']
          : null,
      widgetData['properties'] != null
          ? widgetData['properties']['authorCultUserId']
          : null,
      widgetData['properties'] != null
          ? widgetData['properties']['post_created_by']
          : null,
    );

    return PhotoTextPostWidgetData(
        widgetType: widgetType,
        pageType: _widgetData['pageType'],
        userImage: widgetData['author']['user']['avatar_url'],
        postedTime: postedTime,
        userName: widgetData['author']['user']['display_name'],
        tag: widgetData['author']['user']!['public_properties']?['TAG'],
        // To_Do Group Name from backend
        groupName: widgetData['groupName'] ??
            widgetData["source"]["title"]?["en"] ??
            "",
        caption: getCaption(
            widgetData["properties"]?["MOMENTS_CAPTION"] ??
                widgetData["content"][0]["text"],
            widgetData["mentions"],
            widgetData["properties"]?["MOMENTS_CAPTION"]),
        postType: getPostType(attachments, properties),
        attachments: attachments,
        likes: getLikesCount(widgetData['reactions_count']),
        comments: widgetData['comments_count'],
        isLiked: getIsLiked(widgetData['my_reactions']),
        activityId: widgetData['id'],
        userProfileAction: action_handler.Action(
            type: ActionTypes.NAVIGATION,
            url:
                "curefit://social_user_profile?cultUserId=${widgetData['author']['user']['id']}"),
        authorId: widgetData['author']['user']['id'],
        postDetailsAction: action_handler.Action(
            type: ActionTypes.NAVIGATION,
            url: "curefit://post_detail_page?postId=${widgetData['id']}"),
        groupId: widgetData['source']['id']['id'],
        groupAction: action_handler.Action(
            type: ActionTypes.NAVIGATION,
            url:
                "curefit://group_landing_page?groupId=${widgetData['source']['id']['id']}"),
        options: options,
        aspectRatio: widgetData['properties'] != null
            ? double.parse(widgetData['properties']['aspectRatio'])
            : null,
        properties: properties,
        isPostDetailPage: isPostDetailPage,
        pollData: widgetData['poll'] != null
            ? PollData.fromJson(widgetData['poll'])
            : null,
        isMyPost: widgetData['isMyPost'] ?? false);
  }
}

import 'package:flutter/material.dart';

class HolePainter extends CustomPainter {
  final double height;
  final double width;
  final Color fillColor;
  final double radius;

  HolePainter({
    this.height = 200.0,
    this.width = 230.0,
    this.fillColor = Colors.white,
    this.radius = 50.0,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint();

    paint.color = Colors.white.withOpacity(0.1);
    canvas.drawPath(
      Path.combine(
        PathOperation.difference,
        Path()..addRRect(RRect.fromLTRBAndCorners(0, 0, width, height,
            topLeft: const Radius.circular(30),
            topRight: const Radius.circular(30),
            bottomLeft: const Radius.circular(10),
            bottomRight: const Radius.circular(10))),
        Path()
          ..addOval(Rect.fromCircle(center: Offset(width/2, 0), radius: radius))
          ..close(),
      ),
      paint,
    );
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) {
    return false;
  }

}

import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:flutter/material.dart';
import 'package:flutter_linkify/flutter_linkify.dart';

class Mention {
  String? type;
  String userName;
  int start;
  int end;
  String userId;

  Mention(this.userName, this.userId, this.start, this.end, {this.type = 'MENTION'});

  @override String toString() {
    return "$userName $start $end";
  }
}

class HashTag {
  String? type;
  String hashTag;
  int start;
  int end;

  HashTag(this.hashTag, this.start, this.end, {this.type = 'HASHTAG'});

  @override String toString() {
    // TODO: implement toString
    return "$hashTag $start $end";
  }
}

class HyperLink {
  String? type;
  String url;
  int start;
  int end;

  HyperLink(this.url, this.start, this.end, {this.type = 'HYPERLINK'});

  @override String toString() {
    // TODO: implement toString
    return "$url $start $end";
  }
}

class CommentTextController extends TextEditingController {

  CommentTextController() : super();
  
  List<Mention> _mentions = [];
  
  set mentions(List<Mention> list) {
    list.sort((a,b) => a.start < b.start ? -1 : 1);
    _mentions = list;
  }

  List<Mention> get mentions => _mentions;

  @override 
  TextSpan buildTextSpan({required BuildContext context, TextStyle? style, required bool withComposing}) {
    String currentText = text;
    if(mentions.isNotEmpty) {
      List<TextSpan> children = [];
      int currStart = 0;
      mentions.forEach((element) {
        if(currStart <= element.start && element.start < currentText.length && element.end < currentText.length){
          children.add(TextSpan(text: currentText.substring(currStart, element.start), style: AuroraTheme.of(context).textStyle(TypescaleValues.P5, color: Colors.white)));
          children.add(TextSpan(text: currentText.substring(element.start, element.end+1), style: AuroraTheme.of(context).textStyle(TypescaleValues.P3, color: Colors.white)));
          currStart = element.end+1;
        }
      });
      if(currStart != text.length) {
        children.add(TextSpan(text: currentText.substring(currStart, text.length)));
      }
      return TextSpan(children: children);
    }
    return TextSpan(text: currentText);
  }
}

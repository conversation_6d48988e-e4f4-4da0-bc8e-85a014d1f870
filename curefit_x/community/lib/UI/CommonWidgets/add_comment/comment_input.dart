import 'package:common/ui/blur_view.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:community/UI/CommonWidgets/add_comment/comment_text_controller.dart';
import 'package:community/blocs/mentions_bloc/mention_bloc.dart';
import 'package:community/blocs/mentions_bloc/mention_events.dart';
import 'package:community/blocs/mentions_bloc/mention_models.dart';
import 'package:community/blocs/mentions_bloc/mention_states.dart';
import 'package:flutter/material.dart';
import 'package:easy_debounce/easy_debounce.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:common/ui/user_profile_icon.dart';

int triggerLetterLimit = 3;

class Selection {
  int start;
  int end;
  Selection(this.start, this.end);

  @override
  String toString() {
    return '$start - $end';
  }
}

class CommentInput extends StatefulWidget {
  Widget? onPostComment;
  CommentTextController textEditingController;
  FocusNode? focusNode;
  bool mentionsOnTop;
  int maxLines;
  String? suggestionText;

  CommentInput(this.textEditingController,
      {this.focusNode,
      this.onPostComment,
      this.mentionsOnTop = true,
      this.maxLines = 3,
      this.suggestionText,
      Key? key})
      : super(key: key);

  @override
  _CommentInputState createState() => _CommentInputState();
}

class _CommentInputState extends State<CommentInput> {
  String triggerWord = "";
  late String prevText;

  @override
  void initState() {
    super.initState();
    prevText = widget.textEditingController.text;

    // Start listening to changes.
    widget.textEditingController.addListener(textListener);
  }

  bool mentionAtIndex(int index, List<Mention> mentions) {
    bool res = false;
    for (var i = 0; i < mentions.length; i++) {
      if (mentions[i].start <= index && mentions[i].end >= index) {
        res = true;
        break;
      }
    }
    return res;
  }

  int getMentionWord(String text, int index, List<Mention> mentions) {
    int triggerIndex;
    for (int i = index; i >= 0; i--) {
      if (mentionAtIndex(i, mentions)) {
        break;
      }
      if (text[i] == '@') {
        triggerIndex = i;
        return triggerIndex;
      }
    }
    return -1;
  }

  getTriggerWord(String text, int cursorIndex, List<Mention> mentions) {
    int triggerIndex = getMentionWord(text, cursorIndex - 1, mentions);
    String _triggerWord = "";
    if (triggerIndex != -1) {
      _triggerWord = text.substring(triggerIndex, cursorIndex);
      setState(() {
        triggerWord = _triggerWord;
      });
    } else {
      setState(() {
        triggerWord = "";
      });
    }

    EasyDebounce.debounce("mentions_search", const Duration(milliseconds: 500),
        () {
      if (triggerWord.length <= triggerLetterLimit) {
        BlocProvider.of<MentionBloc>(context).add(EmptyTrigger());
      }
      if (triggerWord.length > triggerLetterLimit) {
        BlocProvider.of<MentionBloc>(context)
            .add(SearchMentions(triggerWord.substring(1)));
      }
    });

    return triggerWord;
  }

  List<Mention> updateMentions(
      int start, int end, int charCount, bool added, List<Mention> mentions) {
    List<Mention> newList = [];
    int charAdded = added ? charCount : 0 - charCount;
    for (var element in mentions) {
      if (element.start >= start && element.end <= end) {
        Mention newMention = Mention(element.userName, element.userId,
            element.start + charAdded, element.end + charAdded);
        newList.add(newMention);
      } else {
        newList.add(element);
      }
    }
    return newList;
  }

  List<Mention> removeMentionsBetween(
      int start, int end, List<Mention> mentions) {
    List<Mention> newList = [];
    for (var element in mentions) {
      if ((element.start >= start && element.end <= end) ||
          (element.start <= start &&
              element.end <= end &&
              element.end >= start) ||
          (element.start >= start &&
              element.end >= end &&
              element.start <= end) ||
          (element.start <= start && element.end >= end)) {
      } else {
        newList.add(element);
      }
    }
    return newList;
  }

  List<Mention> removeMentionAtIndex(int index, List<Mention> mentions) {
    List<Mention> newList = [];
    for (var element in mentions) {
      if (element.start <= index && element.end >= index) {
        // DO Nothing
      } else {
        newList.add(element);
      }
    }
    return newList;
  }

  textListener() {
    int start = widget.textEditingController.selection.start;
    int end = widget.textEditingController.selection.end;
    String value = widget.textEditingController.text;

    if (prevText.length < value.length) {
      int charAdded = value.length - prevText.length;
      List<Mention> newList = [];
      newList = updateMentions(start - charAdded, prevText.length, charAdded,
          true, widget.textEditingController.mentions);
      if (charAdded == 1) {
        newList = removeMentionAtIndex(start - 1, newList);
      } else {
        newList = removeMentionsBetween(start - charAdded, start - 1, newList);
      }

      widget.textEditingController.mentions = newList;
    }

    if (prevText.length > value.length) {
      int charRemoved = prevText.length - value.length;
      List<Mention> newList = [];
      if (charRemoved == 1) {
        newList =
            removeMentionAtIndex(start, widget.textEditingController.mentions);
      } else {
        newList = removeMentionsBetween(start, start + charRemoved - 1,
            widget.textEditingController.mentions);
      }
      newList = updateMentions(
          start + charRemoved, prevText.length, charRemoved, false, newList);

      widget.textEditingController.mentions = newList;
    }

    String triggerWord = "";
    if (start == end) {
      triggerWord =
          getTriggerWord(value, start, widget.textEditingController.mentions);
    }

    List<Mention> finalList = [];
    for (var e in widget.textEditingController.mentions) {
      String mentionText =
          widget.textEditingController.text.substring(e.start, e.end + 1);
      if (mentionText == e.userName) {
        finalList.add(e);
      }
    }
    widget.textEditingController.mentions = finalList;

    setState(() {
      prevText = widget.textEditingController.text;
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  Widget renderImageAndTitle(
    UserMention item,
  ) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        UserProfileIcon(
            profileDisplayText: item.userName, profileImageUrl: item.imageUrl),
        const SizedBox(width: Spacings.x2),
        Flexible(
            child: Text(
          item.userName,
          style: AuroraTheme.of(context)
              .textStyle(TypescaleValues.P3, color: Colors.white),
        )),
        const SizedBox(
          width: Spacings.x1,
        ),
        if (item.tag != null)
          BlurView(
            child: Padding(
              padding: const EdgeInsets.fromLTRB(10, 2, 10, 2),
              child: Text(
                item.tag!,
                style: AuroraTheme.of(context)
                    .textStyle(TypescaleValues.P7, color: Colors.white),
              ),
            ),
          )
      ],
    );
  }

  Widget renderMentionItem(UserMention mention) {
    return Padding(
      padding: const EdgeInsets.symmetric(
          horizontal: Spacings.x4, vertical: Spacings.x1),
      child: InkWell(
        child: renderImageAndTitle(mention),
        onTap: () {
          int tWLength = triggerWord.length;
          int mentionLength = mention.userName.length;
          String value = widget.textEditingController.text;
          int start = widget.textEditingController.selection.baseOffset;
          value = value.replaceRange(
              start - tWLength, start, mention.userName + " ");
          List<Mention> newList = updateMentions(
              start - tWLength,
              value.length,
              mentionLength - tWLength + 1,
              true,
              widget.textEditingController.mentions);
          newList.add(Mention(mention.userName, mention.userId,
              start - tWLength, start - tWLength + mentionLength - 1));
          widget.textEditingController.mentions = newList;
          setState(() {
            prevText = value;
          });
          widget.textEditingController.value = TextEditingValue(
              text: value,
              selection: TextSelection(
                  baseOffset: start - tWLength + mentionLength + 1,
                  extentOffset: start - tWLength + mentionLength + 1));
        },
      ),
    );
  }

  List<Widget> renderMentions(List<UserMention> mentions) {
    List<Widget> res = [];
    for (var i = 0; i < mentions.length; i++) {
      res.add(renderMentionItem(mentions[i]));
    }
    return res;
  }

  Widget showSuggestions(List<UserMention> mentions) {
    return SizedBox(
      height: 300,
      child: SingleChildScrollView(
        child: Column(children: [
          const SizedBox(
            height: Spacings.x3,
          ),
          ...renderMentions(mentions)
        ]),
      ),
    );
  }

  Widget renderSuggestions() {
    return BlocBuilder<MentionBloc, MentionsState>(builder: (context, state) {
      if (state is LoadedMentionsState &&
          triggerWord.length > triggerLetterLimit) {
        return showSuggestions(state.mentions);
      }
      if (state is LoadingMentionsState) {
        return const SizedBox(height: 300, child: FancyLoadingIndicator());
      }

      return Container();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (widget.mentionsOnTop) renderSuggestions(),
        TextField(
          autocorrect: false,
          maxLines: widget.maxLines,
          minLines: 1,
          focusNode: widget.focusNode,
          controller: widget.textEditingController,
          // onChanged: onChanged,
          decoration: InputDecoration(
              contentPadding: const EdgeInsets.symmetric(
                  horizontal: Spacings.x3, vertical: Spacings.x3),
              suffixIcon: widget.onPostComment,
              border: InputBorder.none,
              hintStyle: AuroraTheme.of(context).textStyle(TypescaleValues.P5,
                  color: Colors.white.withOpacity(0.6)),
              hintText: widget.suggestionText ?? "Add a comment"),
          style: const TextStyle(color: Colors.white),
        ),
        if (!widget.mentionsOnTop) renderSuggestions(),
      ],
    );
  }
}

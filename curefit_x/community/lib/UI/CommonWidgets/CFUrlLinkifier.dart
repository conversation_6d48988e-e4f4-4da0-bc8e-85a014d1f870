import 'package:flutter_linkify/flutter_linkify.dart';

final _urlRegex = RegExp(
  r'^(.*?)((?:https?:\/\/|www\.)[^\s/$.?#].[^\s]*)',
  caseSensitive: false,
  dotAll: true,
);

final _looseUrlRegex = RegExp(
  r'^(.*?)((https?:\/\/)?(www\.)?[-a-zA-Z0-9@:%._\+~#=]{2,256}\.[a-z]{2,4}\b([-a-zA-Z0-9@:%_\+.~#?&//=]*))',
  caseSensitive: false,
  dotAll: true,
);

final _protocolIdentifierRegex = RegExp(
  r'^(https?:\/\/)',
  caseSensitive: false,
);

List<String> whitelistedUrls = [
  'cult.fit',
  'cultsport.com',
  'sugarfit.com',
  'eatfit.in'
];


class CFUrlLinkifier extends Linkifier {
  const CFUrlLinkifier();

  @override
  List<LinkifyElement> parse(elements, options) {
    final list = <LinkifyElement>[];

    elements.forEach((element) {
      if (element is TextElement) {
        var match = options.looseUrl
            ? _looseUrlRegex.firstMatch(element.text)
            : _urlRegex.firstMatch(element.text);

        if (match != null) {
          final text = element.text.replaceFirst(match.group(0)!, '');

          if (match.group(2)?.isNotEmpty == true) {
            var originalUrl = match.group(2)!;
            String? end;

            if ((options.excludeLastPeriod) &&
                originalUrl[originalUrl.length - 1] == ".") {
              end = ".";
              originalUrl = originalUrl.substring(0, originalUrl.length - 1);
            }

            for(String link in whitelistedUrls) {
              if(originalUrl.contains(link)) {

                if (!originalUrl.startsWith(_protocolIdentifierRegex)) {
                  list.add(UrlElement((options.defaultToHttps ? "https://" : "http://") +
                      originalUrl,originalUrl));
                }
                else {
                  list.add(UrlElement(originalUrl));
                }
              }
            }
          }
          if (text.isNotEmpty) {
            list.addAll(parse([TextElement(text)], options));
          }
        }
      } else {
        list.add(element);
      }
    });

    return list;
  }
}

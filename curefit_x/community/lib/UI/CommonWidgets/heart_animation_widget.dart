import 'package:flutter/material.dart';


class HeartAnimationWidget extends StatefulWidget {
  Widget child;
  bool isAnimating;
  Duration duration;
  VoidCallback onEnd;
  bool? alwaysShowAnimation;

  HeartAnimationWidget({required this.child, required this.isAnimating, this.alwaysShowAnimation=false, this.duration = const Duration(milliseconds: 100), required this.onEnd, Key? key}) : super(key: key);

  @override
  _HeartAnimationWidgetState createState() => _HeartAnimationWidgetState();
}

class _HeartAnimationWidgetState extends State<HeartAnimationWidget> with TickerProviderStateMixin {
  late AnimationController animationController;
  late Animation<double> scale;

  @override
  void initState() {
    super.initState();
    animationController = AnimationController(vsync: this, duration: Duration(milliseconds: widget.duration.inMilliseconds));
    scale = Tween<double>(begin: 1.0, end: 1.4).animate(animationController);
  }

  @override
  void didUpdateWidget(HeartAnimationWidget oldWidget) {
    if(widget.isAnimating != oldWidget.isAnimating) {
      startAnimation();
    }
  }

  startAnimation() async {
    if(widget.isAnimating || widget.alwaysShowAnimation!) {
      await animationController.forward();
      await animationController.reverse();
      await Future.delayed(Duration(milliseconds: 300));
      widget.onEnd();
    }
  }

  @override
  void dispose() {
    super.dispose();
    animationController.dispose();
  }


  @override
  Widget build(BuildContext context) {
    return ScaleTransition(
      scale: scale,
      child: widget.child,
    );
  }
}

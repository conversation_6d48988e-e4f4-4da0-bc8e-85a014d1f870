import 'dart:ui' as ui;

import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/font/cf_icons.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:community/blocs/post_creation/post_creation_models.dart';
import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:common/action/action_handler.dart' as action_handler;
import 'package:flutter_bloc/flutter_bloc.dart';

class PollCreationWidget extends StatefulWidget {
  Function handleChanges;
  PollsTemplateDetails? pollTemplateDetails;

  PollCreationWidget(this.handleChanges, this.pollTemplateDetails, {Key? key}) : super(key: key);

  @override
  _PollCreationWidgetState createState() => _PollCreationWidgetState();
}

class _PollCreationWidgetState extends State<PollCreationWidget>
    with AutomaticKeepAliveClientMixin<PollCreationWidget> {
  List<TextEditingController> textEditingControllerList = [];
  late PollDuration selectedDuration;

  @override
  void initState() {
    selectedDuration = widget.pollTemplateDetails?.pollDurationList.first
        ?? PollDuration("1 Day", 1);
    textEditingControllerList = [
      TextEditingController(),
      TextEditingController(),
    ];
    for (TextEditingController textEditingController in textEditingControllerList) {
      textEditingController.addListener(() {
        widget.handleChanges(
            textEditingControllerList.map((e) => e.text).toList(), 0);
      });
    }
    super.initState();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    return renderPoll(context);
  }

  List<DropdownMenuItem<PollDuration>> _addDividersAfterItems(List<PollDuration> items) {
    List<DropdownMenuItem<PollDuration>> _menuItems = [];
    for (var item in items) {
      _menuItems.addAll(
        [
          DropdownMenuItem<PollDuration>(
            value: item,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
              child: Text(
                  item.displayText,
                  style: AuroraTheme.of(context).textStyle(
                      TypescaleValues.P8, color: Colors.white)
              ),
            ),
          ),

          if (item != items.last)
            const DropdownMenuItem<PollDuration>(
              enabled: false,
              child: Divider(
                color: Colors.white60,
              ),
            ),
        ],
      );
    }
    return _menuItems;
  }

  List<double> _getCustomItemsHeights(List<PollDuration> items) {
    List<double> _itemsHeights = [];
    for (var i = 0; i < (items.length * 2) - 1; i++) {
      if (i.isEven) {
        _itemsHeights.add(40);
      }
      if (i.isOdd) {
        _itemsHeights.add(4);
      }
    }
    return _itemsHeights;
  }

  Widget renderPoll(BuildContext context) {
    return BlurView(
      blurType: BlurType.HIGH,
      borderRadius: 10,
      child: Container(
        padding: const EdgeInsets.only(left: Spacings.x4, right: Spacings.x4),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            MediaQuery.removePadding(
              removeTop: true,
              removeBottom: true,
              context: context,
              child: ListView.builder(
                  itemCount: textEditingControllerList.length,
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemBuilder: (BuildContext context, int index) {
                    return Padding(
                      padding: const EdgeInsets.only(top: 10, bottom: 10),
                      child: TextFormField(
                          controller: textEditingControllerList[index],
                          onChanged: (value) {
                            setState(() {
                              if (index ==
                                  textEditingControllerList.length - 1 &&
                                  textEditingControllerList.length < 5) {
                                textEditingControllerList.add(
                                    TextEditingController());
                              }
                              widget.handleChanges(
                                  textEditingControllerList.map((e) => e.text).toList(), 0);
                            });
                          },
                          style: AuroraTheme.of(context)
                              .textStyle(
                              TypescaleValues.P2, color: Colors.white),
                          decoration: InputDecoration(
                              suffixIcon: GestureDetector(
                                  onTap: () {
                                    if(textEditingControllerList.length > 2) {
                                      setState(() {
                                      textEditingControllerList.removeAt(index);
                                      widget.handleChanges(
                                          textEditingControllerList.map((e) => e.text).toList(), 0);
                                    });
                                    }
                                  },
                                  child: textEditingControllerList.length > 2
                                      ? const Icon(CFIcons.cancel, color: Colors.white)
                                      : const Icon(CFIcons.cancel, color: Colors.white60)),
                              hintText: "Option ${index + 1}",
                              enabledBorder: const UnderlineInputBorder(
                                borderSide: BorderSide(
                                    color: Colors.white60, width: 1),
                              ),
                              focusedBorder: const UnderlineInputBorder(
                                borderSide: BorderSide(
                                    color: Colors.white60, width: 1),
                              ),
                              hintStyle: AuroraTheme.of(context)
                                  .textStyle(TypescaleValues.P2, color: Colors
                                  .white60))),
                    );
                  }),
            ),
            const SizedBox(height: Spacings.x2),
            if( widget.pollTemplateDetails?.pollDurationList != null )
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                    widget.pollTemplateDetails?.pollLengthDesc ?? 'Poll ends in',
                    style: AuroraTheme.of(context).textStyle(
                        TypescaleValues.P5, color: Colors.white)),
              ],
            ),
            const SizedBox(height: Spacings.x2),
          ],
        ),
      ),
    );
  }

  Widget renderCloseButton() {
    return InkWell(
      child: const Icon(
        CFIcons.cancel,
        color: Colors.white,
        size: 30,
      ),
      onTap: () {
        if (Navigator.canPop(context)) {
          Navigator.pop(context);
        } else if (!Navigator.canPop(context)) {
          ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
          actionBloc.add(CloseApplicationEvent(shouldReset: false));
        }
      },
    );
  }
}

class PollOptionData {
  String id;
  String displayText;
  int voteCount;
  bool votedByMe;

  PollOptionData({
    required this.id,
    required this.displayText,
    required this.voteCount,
    required this.votedByMe});

  factory PollOptionData.fromJson(dynamic pollOptionData) {
    return PollOptionData(
      id: pollOptionData["id"],
      displayText: pollOptionData["content"][0]["text"],
      voteCount: pollOptionData["vote_count"] ?? 0,
      votedByMe: pollOptionData["voted_by_me"] ?? false,
    );
  }

  @override String toString() {
    // TODO: implement toString
    return "$displayText $voteCount $votedByMe";
  }
}

class PollData {
  bool allowMultipleVotes;
  List<PollOptionData> options;
  int? endsAt;
  int voteCount;

  PollData({
    required this.options,
    required this.allowMultipleVotes,
    this.endsAt,
    required this.voteCount});

  factory PollData.fromJson(dynamic pollData) {

    List<PollOptionData> pollOptions = pollData['options']
        .map<PollOptionData>((e) => PollOptionData.fromJson(e))
        .toList();

    return PollData(
      options: pollOptions,
      allowMultipleVotes: pollData["allow_multi_votes"] ?? false,
      endsAt: pollData["ends_at"],
      voteCount: pollData["vote_count"] ?? 0,
    );
  }
}

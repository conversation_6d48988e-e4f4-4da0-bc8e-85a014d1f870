import 'package:common/font/cf_icons.dart';
import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/toast.dart';
import 'package:common/util/util.dart';
import 'package:flutter/material.dart';

class ReasonOfReport {
  String displayText;
  String code;
  ReasonOfReport(this.displayText, this.code);
}

List<ReasonOfReport> reasonsList = [
  ReasonOfReport("Not interested", "NOT_INTERESTED"),
  ReasonOfReport("User is spamming", "USER_SPAM"),
  ReasonOfReport("Privacy breach", "PRIVACY_BREACH"),
  ReasonOfReport("Inappropriate content", "INAPPROPRIATE_CONTENT"),
  ReasonOfReport("Offensive behavior", "OFFENSIVE_BEHAVIOUR"),
  ReasonOfReport("Others", "OTHERS"),
];

class ReportPostBottomSheet extends StatefulWidget {
  Function onReport;
  String header;
  String actionTitle;
  ReportPostBottomSheet(this.onReport, this.header, this.actionTitle,
      {Key? key})
      : super(key: key);

  @override
  State<ReportPostBottomSheet> createState() => _ReportPostBottomSheetState();
}

class _ReportPostBottomSheetState extends State<ReportPostBottomSheet> {
  String? selectedReason;
  TextEditingController textEditingController = TextEditingController();

  Widget renderReasonItem(ReasonOfReport item) {
    BoxDecoration selectedTagDecoration = BoxDecoration(
        boxShadow: const [BoxShadow(color: Color(0x1AFFFFFF))],
        borderRadius: BorderRadius.circular(100.0),
        border: Border.all(color: Colors.white));

    BoxDecoration unSelectedTagDecoration = BoxDecoration(
        boxShadow: const [BoxShadow(color: Color(0x1AFFFFFF))],
        borderRadius: BorderRadius.circular(100.0));
    bool selected = selectedReason == item.code;
    return GestureDetector(
      onTap: () => {
        setState(() {
          selectedReason = item.code;
        })
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 10.0, right: 7),
        padding:
            const EdgeInsets.symmetric(vertical: 8, horizontal: Spacings.x3),
        decoration: selected ? selectedTagDecoration : unSelectedTagDecoration,
        child: Text(item.displayText,
            style: AuroraTheme.of(context)
                .textStyle(TypescaleValues.P5, color: Colors.white)),
      ),
    );
  }

  Widget renderReasons() {
    return Wrap(
      children: [for (var element in reasonsList) renderReasonItem(element)],
    );
  }

  Widget renderFeedbackInput() {
    return Padding(
      padding: const EdgeInsets.only(top: Spacings.x2),
      child: Container(
        decoration: const BoxDecoration(
            color: Colors.white10,
            borderRadius: BorderRadius.all(Radius.circular(Spacings.x1))),
        child: TextField(
          maxLines: 3,
          keyboardType: TextInputType.multiline,
          controller: textEditingController,
          decoration: InputDecoration(
              // fillColor: Colors.white10,
              // filled: true,

              contentPadding: const EdgeInsets.symmetric(
                  horizontal: Spacings.x3, vertical: Spacings.x2),
              border: InputBorder.none,
              hintStyle: AuroraTheme.of(context).textStyle(TypescaleValues.P5,
                  color: Colors.white.withOpacity(0.8)),
              hintText: "Write your feedback here..."),
          style: const TextStyle(color: Colors.white),
        ),
      ),
    );
  }

  Widget renderHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Flexible(
            flex: 1,
            child: Text(
              widget.header,
              style: AuroraTheme.of(context)
                  .textStyle(TypescaleValues.H2, color: Colors.white),
            )),
        InkWell(
          child: const Icon(
            CFIcons.cancel,
            color: Colors.white,
            size: 35,
          ),
          onTap: () {
            Navigator.pop(context);
          },
        )
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: Spacings.x4),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 10.0, bottom: 20),
            child: renderModalNotch(Colors.white60, context),
          ),
          renderHeader(),
          const SizedBox(height: Spacings.x4),
          Align(alignment: Alignment.centerLeft, child: renderReasons()),
          if (selectedReason == "OTHERS") renderFeedbackInput(),
          // if(selectedReason != null)
          const SizedBox(height: Spacings.x4),
          // if(selectedReason != null)
          PrimaryButton(
            () {
              if (selectedReason == null || selectedReason!.isEmpty) {
                Toast.show("Select a reason to report", context,
                    duration: Toast.lengthShort,
                    gravity: Toast.top,
                    textStyle: AuroraTheme.of(context)
                        .textStyle(TypescaleValues.P3, color: Colors.black),
                    backgroundColor: Colors.white);
                return;
              }
              if (selectedReason == "OTHERS") {
                widget.onReport("Others: ${textEditingController.text}");
              } else {
                String reason = reasonsList
                    .firstWhere((element) => element.code == selectedReason)
                    .displayText;
                widget.onReport(reason);
              }
            },
            widget.actionTitle,
            enabled: selectedReason!=null,
            buttonType: PrimaryButtonType.SMALL,
          ),
          const SizedBox(height: 30),
        ],
      ),
    );
  }
}

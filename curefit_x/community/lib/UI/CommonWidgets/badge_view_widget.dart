import 'package:common/ui/aurora.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/header/card_header.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:common/action/action_handler.dart' as action_handler;
import 'package:common/image/image_url_generator.dart';
import 'package:flutter_svg/flutter_svg.dart';

class BadgeWidget extends StatelessWidget {
  BadgeViewData badge;
  BuildContext ctx;

  BadgeWidget(this.badge, this.ctx, {Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return renderBadgeWidget(context);
  }

  Widget renderBadgeWidget(BuildContext context) {
    return Container(
      height: 335,
      color: Colors.black,
      child: Stack(
        alignment: Alignment.topCenter,
        children: [
          Positioned(
              top: -37.5,
              child: Image.asset("assets/badge_background.png",
                  width: 335,
                  height: 335)),
          Padding(
            padding: const EdgeInsets.only(left: Spacings.x4, right: Spacings.x4),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Padding(
                  padding: const EdgeInsets.only(top: 60, bottom: 30),
                  child: CFNetworkImage(imageUrl: getImageUrl(ctx, imagePath: badge.imageUrl), height: 140, width: 140,),
                ),
                Text(badge.badgeTitle, style: AuroraTheme.of(context)
                    .textStyle(TypescaleValues.H2, color: Colors.white)),
                const SizedBox(height: Spacings.x1),
                Text(badge.description, style: AuroraTheme.of(context)
                    .textStyle(TypescaleValues.P5, color: Colors.white70), textAlign: TextAlign.center),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class HlPeopleWidgetData {
  String displayName;
  String? avatarUrl;
  String? socialUserId;
  String? cultUserId;
  String? tag;
  String? secondaryData;

  HlPeopleWidgetData({
    required this.displayName,
    this.avatarUrl,
    this.socialUserId,
    this.cultUserId,
    this.tag,
    this.secondaryData});

  @override String toString() {
    return displayName;
  }
}

class BadgeViewData {
  String userName;
  String badgeTitle;
  String description;
  String imageUrl;

  BadgeViewData({required this.userName, required this.badgeTitle,
    required this.description, required this.imageUrl});
}

import 'package:common/analytics/analytics_repository.dart';
import 'package:common/font/cf_icons.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:common/ui/user_profile_icon.dart';
import 'package:community/blocs/group_members/group_members_bloc.dart';
import 'package:community/blocs/group_members/group_members_event.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:common/ui/components/stacked_items_widget.dart';
import 'package:common/action/action_handler.dart' as ActionHandler;
import 'members_list_modal.dart';
import 'package:flutter_svg/svg.dart';

class GroupMembersListWidget extends StatelessWidget {
  final GroupMembersListWidgetData widgetData;
  const GroupMembersListWidget({Key? key, required this.widgetData})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
          horizontal: AuroraTheme.of(context).verticalWidgetSpacing),
      child: InkWell(
        onTap: () {
          RepositoryProvider.of<AnalyticsRepository>(context)
              .logButtonClickEvent(
              extraInfo: {"seeAllMembersOnAboutGroupPage": "true"});
          BlocProvider.of<GroupMembersBloc>(context)
              .add(LoadingGroupMembers());
          showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          builder: (_) {
            return BlocProvider.value(
              value: BlocProvider.of<GroupMembersBloc>(context),
              child: MembersListModal(widgetData.groupId),
            );
          });
        },
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
              Text(
                widgetData.title,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                style: AuroraTheme.of(context).textStyle(TypescaleValues.H2,
                    color: Colors.white,
                    deviceWidth: MediaQuery.of(context).size.width),
              ),
              Row(
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: Spacings.x1),
                    child: Text('See All',
                        style: AuroraTheme.of(context).textStyle(
                            TypescaleValues.P5,
                            color: Colors.white.withOpacity(.8))),
                  ),
                  Transform.translate(
                    offset: const Offset(0, 2),
                    child: SvgPicture.asset(
                      'assets/chevron_right_arrow.svg',
                      width: scale(context, 30),
                      height: scale(context, 30),
                      fit: BoxFit.contain,
                    ),
                  ),
                ],
              ),
            ]),
            const SizedBox(
              height: 20,
            ),
            StackedItemsWidget(
              items: widgetData.imageUrls
                  .map((e) => renderImage(e, context))
                  .toList(),
              size: 60,
              xShift: 20,
              direction: TextDirection.rtl,
            )
          ],
        ),
      ),
    );
  }

  Widget renderImage(dynamic member, BuildContext context) {
    String? imageUrl = member['avatarUrl'] == null
        ? null
        : getImageUrl(context, imagePath: member['avatarUrl']);
    return UserProfileIcon(
        profileDisplayText: member['name'], profileImageUrl: imageUrl);
  }
}

class RenderIconWidget extends StatelessWidget {
  final GroupMembersListWidgetData widgetData;
  const RenderIconWidget(this.widgetData) : super();

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
          child: const Icon(
            CFIcons.right_arrow,
            color: Colors.white,
            size: 18,
          ),
          onTap: () => {
                BlocProvider.of<GroupMembersBloc>(context)
                    .add(LoadingGroupMembers()),
                showModalBottomSheet(
                    context: context,
                    isScrollControlled: true,
                    builder: (_) {
                      return BlocProvider.value(
                        value: BlocProvider.of<GroupMembersBloc>(context),
                        child: MembersListModal(widgetData.groupId),
                      );
                    })
              }),
    );
  }
}

class GroupMembersListWidgetData implements IWidgetData {
  @override
  WidgetInfo? widgetInfo;
  @override
  WidgetTypes widgetType;
  List<dynamic> imageUrls;
  String title;
  String groupId;

  GroupMembersListWidgetData(
      {this.widgetInfo,
      required this.widgetType,
      required this.imageUrls,
      required this.title,
      required this.groupId});

  factory GroupMembersListWidgetData.fromJson(
      dynamic widgetData, WidgetInfo widgetInfo, WidgetTypes widgetType) {
    List<dynamic> imageIds = [];
    for (dynamic item in widgetData['members']) {
      if (item != null) {
        imageIds.add(item);
      }
    }
    return GroupMembersListWidgetData(
      widgetType: widgetType,
      // imageUrls: widgetData['members'].map<String>((e) => e.toString()).toList(),
      imageUrls: imageIds,
      title: widgetData['title'],
      groupId: widgetData['groupId'],
    );
  }
}

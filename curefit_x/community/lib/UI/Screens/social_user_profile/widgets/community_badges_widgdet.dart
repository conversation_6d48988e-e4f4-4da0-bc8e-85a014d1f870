import 'dart:typed_data';

import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/constants/action_constants.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/atoms/secondary_button.dart';
import 'package:common/ui/molecules/bottom_tray.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:common/ui/header/card_header.dart';
import 'package:common/util/util.dart';
import 'package:community/UI/CommonWidgets/badge_view_widget.dart';
import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:share_plus/share_plus.dart';
import 'package:http/http.dart' as http;
import 'package:path_provider/path_provider.dart';
import 'dart:io';
import 'dart:math';
import 'package:common/action/action_handler.dart' as action_handler;
import 'package:screenshot/screenshot.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';

class CommunityBadgesWidget extends StatelessWidget {
  CommunityBadgesWidgetData widgetData;

  CommunityBadgesWidget({required this.widgetData, Key? key}) : super(key: key);


  final ScreenshotController screenshotController = ScreenshotController();

  Widget renderHeader() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: Spacings.x4),
      child: WidgetHeader(
        cardHeaderData: WidgetHeaderData(title: "Achievements"),
      ),
    );
  }

  Future<File> urlToFile(String imageUrl) async {
// generate random number.
    var rng = Random();
// get temporary directory of device.
    Directory tempDir = await getTemporaryDirectory();
// get temporary path from temporary directory.
    String tempPath = tempDir.path;
// create a new file in temporary path with random file name.
    File file = File(tempPath + (rng.nextInt(100)).toString() + '.png');
// call http.get method and pass imageUrl into it to get response.
    var uri = Uri.parse(imageUrl);
    http.Response response = await http.get(uri);
// write bodyBytes received in response to file.
    await file.writeAsBytes(response.bodyBytes);
// now return the file which is created with random name in
// temporary directory and image bytes from response is written to // that file.
    return file;
  }

  Widget renderBadgeDetailBottomSheet(Badge item, String userName, BuildContext context) {
    String imageUrl = getImageUrl(context, imagePath: item.imageUrl);
    return Container(
      decoration: BoxDecoration(
        borderRadius:
            BorderRadius.circular(AuroraTheme.of(context).cornerRadiusTray),
        color: Colors.black.withOpacity(0.8),
      ),
      child: Padding(
          padding: const EdgeInsets.only(
              top: Spacings.x2,
              bottom: Spacings.x4,
              right: Spacings.x4,
              left: Spacings.x4),
          child: Column(children: [
            renderModalNotch(Colors.white60, context),
            const SizedBox(
              height: Spacings.x4,
            ),
            CachedNetworkImage(
              imageUrl: imageUrl,
              fit: BoxFit.contain,
              width: 170,
              height: 170,
              errorWidget: (context, url, error) => const Icon(Icons.error),
            ),
            const SizedBox(
              height: Spacings.x6,
            ),
            Text(
              item.name,
              style: AuroraTheme.of(context)
                  .textStyle(TypescaleValues.H2, color: Colors.white),
            ),
            const SizedBox(height: Spacings.x2),
            Text(
              item.description,
              style: AuroraTheme.of(context)
                  .textStyle(TypescaleValues.P5, color: Colors.white),
              textAlign: TextAlign.center,
            ),
            if (widgetData.share) const SizedBox(height: Spacings.x6),
            if (widgetData.share)
              Row(
                children: [
                  Expanded(
                    child: SizedBox(
                      child: SecondaryButton(() async {
                        RepositoryProvider.of<AnalyticsRepository>(context)
                            .logButtonClickEvent(
                                extraInfo: {"shareSelfBadgeDetails": "true"});
                        // File file = await urlToFile(imageUrl);
                        // Share.shareFiles([file.path], text: item.description);
                        shareBadge(item, userName, true, context);
                      }, "Share",
                        height: 50,),
                    ),
                  ),
                  if (widgetData.isCommunityUser)
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.only(left: Spacings.x2),
                        child: SizedBox(
                          height: 50,
                          child: PrimaryButton(() async {
                            shareBadge(item, userName, false, context);
                          }, "Create a Post"),
                        ),
                      ),
                    ),
                ],
              ),
            const SizedBox(height: Spacings.x8),
          ])),
    );
  }

  Widget renderBadge(Badge item, String userName, BuildContext context) {
    return GestureDetector(
      onTap: () {
        RepositoryProvider.of<AnalyticsRepository>(context)
            .logButtonClickEvent(extraInfo: {"clickOnBadge": true});
        showBottomTray(
            context: context,
            child: renderBadgeDetailBottomSheet(item, userName, context));
      },
      child: Padding(
        padding: const EdgeInsets.only(right: Spacings.x5),
        child: Column(
          children: [
            CachedNetworkImage(
              imageUrl: getImageUrl(context, imagePath: item.imageUrl),
              fit: BoxFit.contain,
              width: 60,
              height: 60,
              errorWidget: (context, url, error) => const Icon(Icons.error),
            ),
            const SizedBox(
              height: Spacings.x1,
            ),
            Text(
              getBadgeName(item.badgeCategory),
              style: AuroraTheme.of(context).textStyle(
                TypescaleValues.P8,
                color: Colors.white60,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void shareBadge(Badge item, String userName, bool share, BuildContext context) async {
    await DefaultCacheManager().downloadFile(getImageUrl(context, imagePath: item.imageUrl));
    await screenshotController.captureFromWidget(
      BadgeWidget(BadgeViewData(
        badgeTitle: item.name,
        userName: userName,
        imageUrl: item.imageUrl,
        description: item.shareDescription ?? getBadgeData(item.name, userName, item.badgeCategory, userName, false),
      ), context),
      delay: const Duration(
          milliseconds: 20),
    ).then((Uint8List? image) async {
      if (image != null) {
        Directory appDocDir =
        await getApplicationDocumentsDirectory();
        String appDocPath =
            appDocDir.path;
        var millis = DateTime.now()
            .millisecondsSinceEpoch;
        File imagePath = await File(
            '$appDocPath/$millis.png')
            .create();
        imagePath
            .writeAsBytesSync(image);

        if(share) {
          await Share.shareFiles(
              [imagePath.path], text: item.shareDescription ?? getBadgeData(
              item.name, userName, item.badgeCategory, "I",
              widgetData.share)).then((response) =>
          {
            RepositoryProvider.of<AnalyticsRepository>(context).logShareEvent(
                extraInfo: {"subType": "share_screenshot",
                })
          });
        } else {
          RepositoryProvider.of<AnalyticsRepository>(context)
              .logButtonClickEvent(
              extraInfo: {"postSelfBadgeDetails": "true"});
          action_handler.Action navAction = action_handler.Action(
              type: ActionTypes.NAVIGATION,
              url: "curefit://post_creation_page?urls=${imagePath.path}"
                  "&CAPTION=${item.shareDescription ?? getBadgeData(
                  item.name, userName, item.badgeCategory, "I",
                  widgetData.share)}");
          ActionBloc actionBloc =
          BlocProvider.of<ActionBloc>(context);
          PerformActionEvent event = PerformActionEvent(navAction);
          actionBloc.add(event);
        }
      }
    });
  }

  String getBadgeName(String badgeType) {
    switch (badgeType) {
      case 'Activity':
        return 'Classes';
      case 'Streak':
        return 'Streak';
      case 'Workout Minutes':
        return 'Workout';
      case 'Workout Calorie Burn':
        return 'Calorie burn';
      case 'Workout Energy Score':
        return 'Energy Score';
      case 'Workout Booster':
        return 'Boosters';
    }
    return badgeType;
  }

  String getBadgeData(String name, String userName, String badgeType, String? self, bool share) {
    if (share) userName = self ?? 'You';
    String verb = share ? 'have' : 'has';
    switch (badgeType) {
      case 'Activity':
        return "$userName $verb completed more than $name";
      case 'Streak':
        return "$userName $verb achieved a maximum of $name";
      case 'Workout Minutes':
        return "$userName $verb completed more than $name in LIVE classes";
      case 'Workout Calorie Burn':
        return "$userName $verb achieved more than $name in LIVE classes";
      case 'Workout Energy Score':
        return "$userName $verb achieved more than $name in LIVE classes";
      case 'Workout Booster':
        return "$userName $verb received more than $name in LIVE classes";
      case 'Cult Ninja':
        return "$userName $verb completed 3 workouts per week for 4 weeks.";
    }
    return badgeType;
  }

  Widget renderBadgesList(BuildContext context) {
    List<Widget> badges =
        widgetData.badges.map<Widget>((e) => renderBadge(e, widgetData.userName ?? 'User', context)).toList();
    return Padding(
        padding: const EdgeInsets.only(left: Spacings.x4),
        child: SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [...badges],
          ),
        ));
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(
        children: [
          renderHeader(),
          const SizedBox(height: Spacings.x4),
          renderBadgesList(context)
        ],
      ),
    );
  }
}

class CommunityBadgesWidgetData implements IWidgetData {
  @override
  WidgetInfo? widgetInfo;
  @override
  WidgetTypes widgetType;
  List<Badge> badges;
  bool share;
  bool isCommunityUser;
  String? userName;

  CommunityBadgesWidgetData(this.widgetType, this.badges, this.share, this.isCommunityUser, this.userName);

  factory CommunityBadgesWidgetData.fromJson(
      dynamic widgetData, WidgetInfo widgetInfo, WidgetTypes widgetType) {
    List<Badge> badges =
    widgetData["badges"].map<Badge>((e) => Badge.fromJson(e)).toList();
    bool share = widgetData['share'] ?? false;
    bool isCommunityUser = widgetData['communityUser'] ?? false;
    String? userName = widgetData['userName'];

    return CommunityBadgesWidgetData(widgetType, badges, share, isCommunityUser, userName);
  }
}

class Badge {
  String type;
  String? subType;
  String name;
  String badgeCategory;
  String description;
  String? shareDescription;
  String imageUrl;

  Badge(this.type, this.subType, this.name, this.badgeCategory,
      this.description,this.shareDescription, this.imageUrl);

  factory Badge.fromJson(dynamic data) {
    return Badge(data['type'], data['subType'], data['name'],
        data['badgeCategory'], data['description'],data['shareDescription'], data['imageUrl']);
  }
}

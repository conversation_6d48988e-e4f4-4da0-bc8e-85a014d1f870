import 'package:common/font/cf_icons.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:community/UI/Screens/edit_profile/screens/edit_profile_components.dart';
import 'package:community/blocs/edit_profile/edit_profile_bloc.dart';
import 'package:community/blocs/edit_profile/edit_profile_events.dart';
import 'package:community/blocs/edit_profile/edit_profile_models.dart';
import 'package:core/model/squad_visibility_type.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class SquadVisibility extends StatefulWidget {
  ProfileSquadData widgetData;
  Function handleChanges;
  bool? squadExpanded;
  SquadVisibility(this.widgetData, this.handleChanges,
      {this.squadExpanded = false, Key? key})
      : super(key: key);

  @override
  _SquadVisibilityState createState() => _SquadVisibilityState();
}

class _SquadVisibilityState extends State<SquadVisibility> {
  late String? visibilityText;
  late SquadVisibilityType visibilityState;
  late String visibilityTitle;
  bool expanded = false;

  @override
  void initState() {
    super.initState();
    expanded = widget.squadExpanded ?? false;
    visibilityState = widget.widgetData.squadVisibility;
    visibilityTitle =
        widget.widgetData.squadVisibility == SquadVisibilityType.ANY
            ? widget.widgetData.anySquadTitle
            : widget.widgetData.phoneSquadTitle;
    visibilityText =
        widget.widgetData.squadVisibility == SquadVisibilityType.PHONE_NUMBER
            ? widget.widgetData.anySquadSubText
            : widget.widgetData.phoneSquadSubText;
  }

  void setPrivacyText(String privacyState) {
    setState(() {
      visibilityTitle =
          (widget.widgetData.squadVisibility == SquadVisibilityType.ANY
              ? widget.widgetData.anySquadTitle
              : widget.widgetData.phoneSquadTitle)!;
      visibilityText =
          (widget.widgetData.squadVisibility == SquadVisibilityType.PHONE_NUMBER
              ? widget.widgetData.anySquadSubText
              : widget.widgetData.phoneSquadSubText)!;
    });
  }

  void changeSquadVisibility(SquadVisibilityType newVisibility) {
    widget.widgetData.squadVisibility = newVisibility;
    widget.handleChanges(EnumToString.convertToString(newVisibility),
        HandleChangeType.SQUAD_VISIBILITY);
    final bloc = BlocProvider.of<EditProfileBloc>(context);
    bloc.add(UpdateSquadData(widget.widgetData));
  }

  Widget checkListItem(
      String title, String? subText, SquadVisibilityType visibilityType) {
    return Padding(
      padding: const EdgeInsets.only(top: Spacings.x1),
      child: InkWell(
        onTap: () {
          setState(() {
            changeSquadVisibility(visibilityType);
          });
        },
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Radio<String?>(
              fillColor: MaterialStateProperty.all(Colors.white),
              materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
              groupValue: widget.widgetData.squadVisibility.toString(),
              value: visibilityType.toString(),
              onChanged: (value) {
                setState(() {
                  changeSquadVisibility(visibilityType);
                });
              },
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.only(top: Spacings.x1),
                  child: Text(title,
                      style: AuroraTheme.of(context).textStyle(
                          widget.widgetData.squadVisibility == visibilityType
                              ? TypescaleValues.H4
                              : TypescaleValues.P5,
                          color: Colors.white),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 2),
                ),
                if (subText != null)
                  Padding(
                      padding: const EdgeInsets.only(top: Spacings.x1),
                      child: Text(subText,
                          style: AuroraTheme.of(context).textStyle(
                              TypescaleValues.P5,
                              color: Colors.white60),
                          overflow: TextOverflow.ellipsis,
                          maxLines: 2)),
              ],
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    ProfileSquadData widgetData = widget.widgetData;
    return GestureDetector(
      onTap: () {
        setState(() {
          expanded = !expanded;
        });
      },
      child: BlurView(
        blurType: BlurType.HIGH,
        borderRadius: 10.0,
        child: Padding(
          padding: const EdgeInsets.symmetric(
              horizontal: Spacings.x4, vertical: Spacings.x2),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  if (widgetData.squadVisibiltyTitle != null)
                    Text(
                      widgetData.squadVisibiltyTitle!,
                      style: AuroraTheme.of(context)
                          .textStyle(TypescaleValues.P4, color: Colors.white),
                    ),
                  const Icon(CFIcons.chevron_down,
                      size: 10, color: Colors.white)
                ],
              ),
              if (widgetData.anySquadState != null &&
                  widgetData.phoneSquadState != null)
                Padding(
                  padding: const EdgeInsets.only(top: Spacings.x1),
                  child: Text(
                    widget.widgetData.squadVisibility == SquadVisibilityType.ANY
                        ? widgetData.anySquadState!
                        : widgetData.phoneSquadState!,
                    style: AuroraTheme.of(context)
                        .textStyle(TypescaleValues.P8, color: Colors.white54),
                  ),
                ),
              AnimatedContainer(
                duration: const Duration(milliseconds: 500),
                height: expanded ? null : 0,
                curve: expanded ? Curves.easeIn : Curves.easeOut,
                child: expanded
                    ? ListView(
                        physics: const NeverScrollableScrollPhysics(),
                        shrinkWrap: true,
                        children: [
                          checkListItem(
                              widgetData.anySquadTitle!,
                              widgetData.anySquadSubText,
                              SquadVisibilityType.ANY),
                          checkListItem(
                              widgetData.phoneSquadTitle!,
                              widgetData.phoneSquadSubText,
                              SquadVisibilityType.PHONE_NUMBER),
                          // Add more items as needed
                        ],
                      )
                    : null,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

import 'package:common/blocs/center_level_challenge/centre_level_challenge_models.dart';
import 'package:common/blocs/feedback/feedback_models.dart';
import 'package:common/ui/center_level_challenge/center_challenge_widget.dart';
import 'package:common/ui/visibility_detector.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:community/UI/CommonWidgets/photo_text_post.dart';
import 'package:community/UI/Screens/feedback/feedback.dart';
import 'package:community/UI/Screens/ftu_landing/widgets/ftu_banner_widget.dart';
import 'package:community/UI/Screens/ftu_landing/widgets/group_join_widget.dart';
import 'package:community/UI/Screens/group_landing/widgets/group_description_widget.dart';
import 'package:community/UI/Screens/group_landing/widgets/group_members_list_widget.dart';
import 'package:community/UI/Screens/group_landing/widgets/group_rules_widget.dart';
import 'package:community/UI/Screens/highlights/highlight_people.dart';
import 'package:community/UI/Screens/highlights/highlights_card.dart';
import 'package:community/UI/Screens/highlights/weekly_class_highlight_carousel_widget.dart';
import 'package:community/UI/Screens/moments/clubbed_moments.dart';
import 'package:community/UI/Screens/non_membership_widget/non_membership_widget.dart';
import 'package:community/UI/Screens/post_detail/widgets/comment_list_widget.dart';
import 'package:community/UI/Screens/recommendations/recommendations.dart';
import 'package:community/UI/Screens/social_user_profile/widgets/community_badges_widgdet.dart';
import 'package:community/UI/Screens/social_user_profile/widgets/groups_listing_widget.dart';
import 'package:community/UI/Screens/social_user_profile/widgets/multimedia_list_widget.dart';
import 'package:community/UI/Screens/social_user_profile/widgets/profile_info_widget.dart';
import 'package:community/UI/Screens/squads/Add_to_squad_button.dart';
import 'package:community/UI/Screens/squads/squadInviteResponseButtons.dart';
import 'package:community/UI/Screens/squads/squad_community_feed.dart';
import 'package:community/UI/Screens/squads/squad_highlights.dart';
import 'package:core/model/Squads/squad_feed_modal.dart';
import 'package:core/module/squad_goals/presentation/widgets/goal_summary_widget/goal_summary_widget.dart';
import 'package:core/module/squad_goals/presentation/widgets/goal_summary_widget/model/goal_summary_widget_model.dart';
import 'package:core/module/squad_goals/presentation/widgets/ongoing_squad_banner_widget/model/ongoing_squad_banner_widget_model.dart';
import 'package:core/module/squad_goals/presentation/widgets/squad_goal_bg_image_widget/model/squad_goal_bg_image_widget_model.dart';
import 'package:core/module/squad_goals/presentation/widgets/squad_goal_bg_image_widget/squad_goal_bg_image_widget.dart';
import 'package:core/module/squad_goals/presentation/widgets/squad_goal_highlights_widget/model/squad_goal_highlights_widget_model.dart';
import 'package:core/module/squad_goals/presentation/widgets/squad_goal_highlights_widget/squad_goal_highlights_widget.dart';
import 'package:core/module/squad_goals/presentation/widgets/squad_goal_leaderboard_header_widget/model/squad_goal_leaderboard_header_widget_model.dart';
import 'package:core/module/squad_goals/presentation/widgets/squad_goal_leaderboard_header_widget/squad_goal_leaderboard_header_widget.dart';
import 'package:core/module/squad_goals/presentation/widgets/squad_goal_progress_trail_widget/model/squad_goal_progress_trail_widget_model.dart';
import 'package:core/module/squad_goals/presentation/widgets/squad_goal_progress_trail_widget/squad_goal_progress_trail_widget.dart';
import 'package:core/module/squad_goals/presentation/widgets/squad_goal_reward_widget/model/squad_goal_reward_widget_model.dart';
import 'package:core/module/squad_goals/presentation/widgets/squad_goal_reward_widget/squad_goal_reward_widget.dart';
import 'package:core/module/squad_goals/presentation/widgets/squad_goal_week_trail_widget/model/squad_goal_week_trail_widget_model.dart';
import 'package:core/module/squad_goals/presentation/widgets/squad_goal_week_trail_widget/squad_goal_week_trail_widget.dart';
import 'package:core/module/squad_goals/presentation/widgets/squad_goals_date_widget/model/squad_goal_date_widget_model.dart';
import 'package:core/module/squad_goals/presentation/widgets/squad_goals_date_widget/squad_goal_date_widget.dart';
import 'package:core/module/squad_goals/presentation/widgets/squad_goals_detail_footer_widget/model/squad_goal_detail_footer_widget_model.dart';
import 'package:core/module/squad_goals/presentation/widgets/squad_goals_details_upcoming_footer_widget/squad_goals_details_upcoming_footer_widget.dart';
import 'package:core/module/squad_goals/presentation/widgets/squad_goals_details_upcoming_info_widget/model/squad_goals_details_upcoming_info_widget_model.dart';
import 'package:core/module/squad_goals/presentation/widgets/squad_goals_details_upcoming_info_widget/squad_goals_details_upcoming_info_widget.dart';
import 'package:core/module/squad_goals/presentation/widgets/squad_goals_intro_widget/model/squad_goals_intro_widget_model.dart';
import 'package:core/module/squad_goals/presentation/widgets/squad_goals_detail_footer_widget/squad_goals_detail_footer_widget.dart';
import 'package:core/module/squad_goals/presentation/widgets/squad_goals_details_upcoming_footer_widget/model/squad_goals_details_upcoming_footer_widget_model.dart';
import 'package:core/module/squad_goals/presentation/widgets/squad_goals_intro_widget/squad_goals_intro_widget.dart';
import 'package:core/module/squad_goals/presentation/widgets/ongoing_squad_banner_widget/ongoing_squad_banner_widget.dart';

import 'package:core/ui/Notif_center/notifications.dart';
import 'package:core/ui/squads/ProfileViewActionItem.dart';
import 'package:core/ui/squads/add_to_squad.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:flutter/material.dart';

class WidgetBuilder extends IWidgetBuilder {
  @override
  Widget? buildWidget(widgetData, {BuilderInfo? builderInfo}) {
    if (widgetData['widgetType'] == null) return null;
    WidgetTypes? widgetType =
        EnumToString.fromString(WidgetTypes.values, widgetData['widgetType']);

    if (widgetType != null) {
      WidgetInfo widgetInfo = widgetData['widgetMetric'] != null
          ? WidgetInfo.fromJson(widgetData['widgetMetric'], widgetType)
          : WidgetInfo(
              widgetType: widgetType,
              widgetMetric: WidgetMetric(widgetName: "", widgetId: ""));
      switch (widgetType) {
        case WidgetTypes.COMMUNITY_FTU_BANNER:
          return FTUBannerWidget(
              widgetData: FTUBannerWidgetData.fromJson(
                  widgetData, widgetInfo, widgetType));
        case WidgetTypes.COMMUNITY_GROUP_JOIN:
          return GroupJoinWidget(
              widgetData: GroupJoinWidgetData.fromJson(
                  widgetData, widgetInfo, widgetType));
        case WidgetTypes.COMMUNITY_GROUP_DESCRIPTION:
          return GroupDescriptionWidget(
              widgetData: GroupDescriptionWidgetData.fromJson(
                  widgetData, widgetInfo, widgetType));
        case WidgetTypes.COMMUNITY_GROUP_MEMBERS_LIST:
          return GroupMembersListWidget(
              widgetData: GroupMembersListWidgetData.fromJson(
                  widgetData, widgetInfo, widgetType));
        case WidgetTypes.COMMUNITY_GROUP_RULES_WIDGET:
          return GroupRulesWidget(
              widgetData: GroupRulesWidgetData.fromJson(
                  widgetData, widgetInfo, widgetType));
        case WidgetTypes.COMMUNITY_NOTIFICATION_ITEM:
          return NotificationWidget(
              widgetData: NotificationData.fromJson(
                  widgetData, widgetInfo, widgetType));
        case WidgetTypes.SQUAD_COMMUNITY_FEED:
          return SquadCommunityFeedWidget(
              widgetData: SquadCommunityFeedData.fromJson(
                  widgetData, widgetInfo, widgetType));
        case WidgetTypes.SQUAD_HIGHLIGHTS:
          return SquadHighlightsWidget(
              widgetData: SquadHighlightsWidgetData.fromJson(
                  widgetData, widgetInfo, widgetType));
        case WidgetTypes.SQUAD_GOAL_INTRO_WIDGET:
          return SquadGoalBanner(
            isUpcoming: false,
            widgetData: SquadGoalsIntroWidgetModel.fromJson(
                widgetData, widgetInfo, widgetType),
          );
        case WidgetTypes.SQUAD_GOAL_UPCOMING_WIDGET:
          return SquadGoalBanner(
            isUpcoming: true,
            widgetData: SquadGoalsIntroWidgetModel.fromJson(
                widgetData, widgetInfo, widgetType),
          );
        case WidgetTypes.SQUAD_GOAL_DETAILS_DATE_WIDGET:
          return SquadGoalsDateWidget(
            widgetData: SquadGoalsDateWidgetModel.fromJson(
                widgetData, widgetInfo, widgetType),
          );
        case WidgetTypes.SQUAD_GOAL_DETAILS_FOOTER_WIDGET:
          return SquadGoalDetailFooterWidget(
            widgetData: SquadGoalsDetailFooterWidgetModel.fromJson(
                widgetData, widgetInfo, widgetType),
          );
        case WidgetTypes.SQUAD_GOAL_DETAILS_UPCOMING_INFO_WIDGET:
          return SquadGoalsDetailsUpcomingInfoWidget(
            widgetData: SquadGoalsDetailUpcomingInfoWidgetModel.fromJson(
                widgetData, widgetInfo, widgetType),
          );
        case WidgetTypes.SQUAD_GOAL_DETAILS_UPCOMING_FOOTER_WIDGET:
          return SquadGoalsDetailsUpcomingFooterWidget(
            widgetData: SquadGoalsDetailsUpcomingFooterWidgetModel.fromJson(
                widgetData, widgetInfo, widgetType),
          );
        case WidgetTypes.ONGOING_SQUADS_BANNER_WIDGET:
          return OngoingSquadBannerWidget(
            widgetData: OngoingSquadBannerWidgetModel.fromJson(
                widgetData, widgetInfo, widgetType),
          );
        case WidgetTypes.GOAL_SUMMARY_WIDGET:
          return GoalSummaryWidget(
            widgetData: GoalSummaryWidgetModel.fromJson(
                widgetData, widgetInfo, widgetType),
          );
        case WidgetTypes.SQUAD_GOAL_WEEK_TRAIL_WIDGET:
          return SquadGoalWeekTrailWidget(
            widgetData: SquadGoalWeekTrailWidgetModel.fromJson(
                widgetData, widgetInfo, widgetType),
          );
        case WidgetTypes.SQUAD_GOAL_PROGRESS_TRAIL_WIDGET:
          return SquadGoalsProgressTrailWidget(
            widgetData: SquadGoalProgressTrailWidgetModel.fromJson(
                widgetData, widgetInfo, widgetType),
          );
        case WidgetTypes.SQUAD_GOAL_BG_IMAGE_WIDGET:
          return SquadGoalBgImageWidget(
            widgetData: SquadGoalBgImageWidgetModel.fromJson(
                widgetData, widgetInfo, widgetType),
          );
        case WidgetTypes.SQUAD_GOAL_HIGHLIGHTS_WIDGET:
          return SquadGoalHighlightsWidget(
            widgetData: SquadGoalHighlightsWidgetModel.fromJson(
                widgetData, widgetInfo, widgetType),
          );
        case WidgetTypes.SQUAD_GOAL_REWARD_WIDGET:
          return SquadGoalRewardWidget(
            widgetData: SquadGoalRewardWidgetModel.fromJson(
                widgetData, widgetInfo, widgetType),
          );
        case WidgetTypes.SQUAD_GOAL_LEADERBOARD_HEADER_WIDGET:
          return SquadGoalLeaderboardHeaderWidget(
            widgetData: SquadGoalLeaderboardHeaderWidgetModel.fromJson(
                widgetData, widgetInfo, widgetType),
          );
        case WidgetTypes.HIGHLIGHT_CARD:
          return HighlightsCard(
              widgetData: HighlightsCardData.fromJson(
                  widgetData, widgetInfo, widgetType));
        case WidgetTypes.HIGHLIGHT_PEOPLE:
          return const HighlightPeople();
        case WidgetTypes.CULT_NOTIFICATION_ITEM:
          return NotificationWidget(
              widgetData: NotificationData.fromJson(
                  widgetData, widgetInfo, widgetType));
        case WidgetTypes.PROFILE_VIEW_ACTION_ITEM:
          return ProfileViewActionWidget(
              widgetData: ProfileViewActionWidgetData.fromJson(widgetData));
        case WidgetTypes.SQUAD_INVITE_RESPONSE_BUTTONS:
          return SquadInviteResponseButtons(
              widgetData: SquadInviteButtonsModal.fromJson(
                  widgetData, widgetInfo, widgetType));
        case WidgetTypes.CENTER_LEVEL_CHALLENGE:
          return ChallengeCard(
              widgetData: CenterChallengeData.fromJson(widgetData));
        case WidgetTypes.FEEDBACK_INPUT_WIDGET:
          return FeedBackInputWidget(
              widgetData:
                  FeedbackInputWidgetData.fromJson(widgetData, widgetType));
        case WidgetTypes.COMMUNITY_POST:
          return CustomVisibilityDetector(
              widgetCreator: ((isVisible) => PhotoTextPostWidget(
                  widgetData: PhotoTextPostWidgetData.fromJson(
                      widgetData, widgetInfo, widgetType))),
              widgetData: widgetData,
              widgetInfo: WidgetInfo(
                  widgetType: widgetType,
                  widgetMetric: WidgetMetric(
                      isSampledWidgetImpressionRequired: true,
                      isWidgetImpressionRequired: true,
                      widgetName: "COMMUNITY_POST",
                      widgetId:
                          "${widgetData['data']['id']}_${DateTime.now().millisecondsSinceEpoch}")),
              widgetType: widgetType);
        case WidgetTypes.COMMUNITY_CLUBBED_MOMENTS:
          return ClubbedMoments(
              widgetData: ClubbedMomentsData.fromJson(
                  widgetData, widgetInfo, widgetType));
        case WidgetTypes.COMMUNITY_RECOMMENDATIONS:
          return Recommendations(
              widgetData: RecommendationsData.fromJson(
                  widgetData, widgetInfo, widgetType));
        case WidgetTypes.COMMUNITY_COMMENT:
          return CommentsListWidget(
              widgetData: CommentsListWidgetData.fromJson(
                  widgetData, widgetInfo, widgetType));
        case WidgetTypes.COMMUNITY_PROFILE_INFO:
          return ProfileInfoWidget(
              widgetData: ProfileInfoWidgetData.fromJson(
                  widgetData, widgetInfo, widgetType));
        case WidgetTypes.COMMUNITY_BADGES:
          return CommunityBadgesWidget(
              widgetData: CommunityBadgesWidgetData.fromJson(
                  widgetData, widgetInfo, widgetType));
        case WidgetTypes.COMMUNITY_GROUPS:
          return GroupsListingWidget(
              widgetData: GroupsListingWidgetData.fromJson(
                  widgetData, widgetInfo, widgetType));
        case WidgetTypes.COMMUNITY_MULTIMEDIA:
          return MultimediaListWidget(
              widgetData: MultimediaListWidgetData.fromJson(
                  widgetData, widgetInfo, widgetType));
        case WidgetTypes.COMMUNITY_NON_MEMBER:
          return NonMembershipWidget(
              widgetData: NonMembershipWidgetData.fromJson(
                  widgetData, widgetInfo, widgetType));
        case WidgetTypes.WEEKLY_CLASS_HIGHLIGHT_CAROUSEL_WIDGET:
          return WeeklyClassHighlightCarouselWidget(
              widgetData: WeeklyClassHighlightCarouselWidgetData.fromJson(
                  widgetData, widgetInfo, widgetType));
        case WidgetTypes.ADD_TO_SQUAD_BUTTON:
          return AddToSquadButton(
            widgetData:
                SquadInviteModal.fromJson(widgetData, widgetInfo, widgetType),
          );
      }
    }
    return null;
  }
}

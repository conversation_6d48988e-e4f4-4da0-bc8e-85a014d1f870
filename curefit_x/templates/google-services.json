{"project_info": {"project_number": "710378156821", "firebase_url": "https://cult-155917.firebaseio.com", "project_id": "cult-155917", "storage_bucket": "cult-155917.appspot.com"}, "client": [{"client_info": {"mobilesdk_app_id": "1:710378156821:android:36f534ca8e233356", "android_client_info": {"package_name": "fit.cure.android"}}, "oauth_client": [{"client_id": "710378156821-ioapfn0ep36f8g8lmr9gkhpp2ict6n22.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "fit.cure.android", "certificate_hash": "79285cfbcefe28977e3be5a44d593f960a840238"}}, {"client_id": "710378156821-dcu78sb9a1suhgrgvp5phk54bm5ovpam.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "fit.cure.android", "certificate_hash": "9c3acd72ee56cae2c45a88b8099d82d845eb24fb"}}, {"client_id": "710378156821-eshep548b2ajhqu17fe2js2sqvt7087b.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "fit.cure.android", "certificate_hash": "5f73749ce127d5dbb704e34a7356e5f9d3a25212"}}, {"client_id": "710378156821-7svpfrlg8b2fk50ntlf9ubctkhuloe76.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "fit.cure.android", "certificate_hash": "a7801fb224d556e24a625d0037bca780ad6b1a20"}}, {"client_id": "710378156821-cg5pjm99d9bmn22q6f82qvkgdqgr8svj.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "fit.cure.android", "certificate_hash": "544dbdbfb47fe3d1b8fb08357913dc0a0e827e04"}}, {"client_id": "710378156821-r24vveq9q67u4l88045kt724fe5nobll.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "fit.cure.android", "certificate_hash": "69a268a03bffa6c08956d93151bda49d89fb0c32"}}, {"client_id": "710378156821-lsq2icaebbldc8r53qv2pq4lcqconaho.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "fit.cure.android", "certificate_hash": "957857be16a324a6561da6c932c0c31ef3c14c35"}}, {"client_id": "710378156821-9tbtldvfierbjsnhha7smrfemnv6fdqc.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "fit.cure.android", "certificate_hash": "dda7720fb8113d57752ca863fb7521edb9f33242"}}, {"client_id": "710378156821-qd1nd35n1qh81bon4qkodabif1t0g0gn.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDayU_8G-n_3w-wLzou--Ly1G-tozsYBeo"}, {"current_key": "AIzaSyBGt18zjqQ-54YaU6ZN4E7Q_D9Dty9nyUo"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "710378156821-76t9djbge1hm98qcecs1fp9jm1mom50a.apps.googleusercontent.com", "client_type": 3}, {"client_id": "710378156821-mrb4unq5b3cudd6hh9b21mp24qmp64ea.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "fit.cure.ios.beta"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:710378156821:android:5bffa0ee42ca5584", "android_client_info": {"package_name": "com.flutter.curefitx.host"}}, "oauth_client": [{"client_id": "710378156821-jvng7p493rdc797urji7b5jb8t5n6n85.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "com.flutter.curefitx.host", "certificate_hash": "79285cfbcefe28977e3be5a44d593f960a840238"}}, {"client_id": "710378156821-qd1nd35n1qh81bon4qkodabif1t0g0gn.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyDayU_8G-n_3w-wLzou--Ly1G-tozsYBeo"}, {"current_key": "AIzaSyBGt18zjqQ-54YaU6ZN4E7Q_D9Dty9nyUo"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "710378156821-76t9djbge1hm98qcecs1fp9jm1mom50a.apps.googleusercontent.com", "client_type": 3}, {"client_id": "710378156821-mrb4unq5b3cudd6hh9b21mp24qmp64ea.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "fit.cure.ios.beta"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:785551392770:android:49e28a0844e1fd40ada71b", "android_client_info": {"package_name": "fit.cure.intl.android.debug"}}, "oauth_client": [{"client_id": "785551392770-do3aurbhihggvl7su5kasuio07tr6euu.apps.googleusercontent.com", "client_type": 1, "android_info": {"package_name": "fit.cure.intl.android.debug", "certificate_hash": "5cb2aad3828cb955a8f34652361d8350070f1ad4"}}, {"client_id": "785551392770-bmtu8h3o194unvvl9290gpqiso1u379a.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyAx1yOcx6BVFjBjrFQ_GgbmFhs9rjGbn4Q"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "785551392770-bmtu8h3o194unvvl9290gpqiso1u379a.apps.googleusercontent.com", "client_type": 3}, {"client_id": "785551392770-6f70e5ga330oi3k3mf6e2sqcamhmv3vv.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "fit.cure.intl.ios.beta"}}]}}}], "configuration_version": "1"}
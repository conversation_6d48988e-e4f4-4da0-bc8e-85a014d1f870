#!/bin/bash

# Define the virtual environment directory name
VENV_DIR="../../my_virtual_env"

# Create the virtual environment if it doesn't exist
if [ ! -d "$VENV_DIR" ]; then
    python3 -m venv $VENV_DIR
    echo "Virtual environment created at $VENV_DIR"
else
    echo "Virtual environment already exists at $VENV_DIR"
fi

# Activate the virtual environment
if [[ "$SHELL" == *"zsh"* ]]; then
    source $VENV_DIR/bin/activate
elif [[ "$SHELL" == *"bash"* ]]; then
    source $VENV_DIR/bin/activate
else
    echo "Unsupported shell. Activate manually: source $VENV_DIR/bin/activate"
fi

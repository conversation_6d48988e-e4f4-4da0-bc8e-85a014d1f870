name: curefit_x
description: A new flutter module project.

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
#
# This version is used _only_ for the Runner app, which is used if you just do
# a `flutter run` or a `flutter make-host-app-editable`. It has no impact
# on any other native host app that you embed your Flutter project into.
version: 1.0.0+1

environment:
  sdk: ">=2.17.0 <3.0.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_bloc: ^8.0.1
  transform:
    path: ./transform
  therapy:
    path: ./therapy
  enterprise:
    path: ./enterprise
  wellness:
    path: ./wellness
  community:
    path: ./community
  core:
    path: ./core
  store:
    path: ./store
  home:
    path: ./home
  fitness:
    path: ./fitness
  aitrainer:
    path: ./ai_trainer
  gympt:
    path: ./gympt
  support:
    path: ./support
  equipment:
    path: ./equipment
  account:
    path: ./account
  core_authentication:
    path: ./core_authentication
  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.3
  webview_flutter: ^4.7.0
  firebase_crashlytics: ^3.3.2
  simple_observable: ^2.0.0
  # advance_pdf_viewer_fork: ^2.0.0
  connectivity_plus: ^2.2.1
  cronet_http: ^1.3.2
  super_banners: ^0.0.1
  archive: ^3.4.9
  firebase_messaging: ^14.7.10
  firebase_core: ^2.24.2
  flutter_contacts: ^1.1.9+2
  loader_overlay: ^4.0.0
  workmanager:
    git:
      ref: f3e717b0d875296e8bec619965dd73283a54c6e5
      url: **************:curefit/flutter_workmanager_2.git
  patch_package: ^0.0.8

dev_dependencies:
  pigeon: ^22.5.0
  flutter_test:
    sdk: flutter

dependency_overrides:
  flutter_exif_rotation: 0.5.1
  shimmer: 2.0.0
  permission_handler_apple:
    git:
      url: **************:curefit/permission_handler_apple.git
  permission_handler_platform_interface: ^3.11.0
  win32: 4.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true
  fonts:
    - family: Inter
      fonts:
        - asset: fonts/Inter-Italic.otf
          style: italic
        - asset: fonts/Inter-Regular.ttf
        - asset: fonts/Inter-Thin.ttf
          weight: 100
        - asset: fonts/Inter-ExtraLight.ttf
          weight: 200
        - asset: fonts/Inter-Light.ttf
          weight: 300
        - asset: fonts/Inter-Medium.ttf
          weight: 500
        - asset: fonts/Inter-SemiBold.ttf
          weight: 600
        - asset: fonts/Inter-Bold.ttf
          weight: 700
        - asset: fonts/Inter-ExtraBold.ttf
          weight: 800
        - asset: fonts/Inter-Black.ttf
          weight: 900
    - family: BebasNeue
      fonts:
        - asset: fonts/BebasNeue-Regular.ttf
    - family: Mongoose
      fonts:
        - asset: fonts/mongooseRegular.ttf
    - family: League Gothic
      fonts: 
        - asset: fonts/LeagueGothic-Regular.ttf
    - family: CFIcons
      fonts:
        - asset: fonts/CFIcons.ttf
    - family: DangerOnTheMotorway
      fonts:
        - asset: fonts/Danger-on-the-Motorway.ttf
    - family: BethEllen
      fonts:
        - asset: fonts/Beth-Ellen.ttf

  # To add Flutter specific assets to your application, add an assets section,
  # like this:
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add Flutter specific custom fonts to your application, add a fonts
  # section here, in this "flutter" section. Each entry in this list should
  # have a "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages

  # This section identifies your Flutter project as a module meant for
  # embedding in a native host app.  These identifiers should _not_ ordinarily
  # be changed after generation - they are used to ensure that the tooling can
  # maintain consistency when adding or modifying assets and plugins.
  # They also do not have any bearing on your native host application's
  # identifiers, which may be completely independent or the same as these.
  module:
    androidX: true
    androidPackage: com.flutter.curefitx
    iosBundleIdentifier: com.flutter.curefitx

  assets:
    - assets/
    - assets/animations/
    - assets/equipment/
    - assets/mocks/
    - assets/activity_streak/
    - mocks/
    - shorebird.yaml

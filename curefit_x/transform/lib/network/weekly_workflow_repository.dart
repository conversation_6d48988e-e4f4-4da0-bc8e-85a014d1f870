import 'package:common/network/client.dart';

class WeeklyWorkflowRepository {
  final NetworkClient client;

  WeeklyWorkflowRepository(this.client);

  Future<dynamic> getWorkflowList(
      {String epoch = "", String? subCategoryCode}) async {
    final response = await this.client.get("/v2/transform/workflowList",
        {"currentEpoch": epoch, "subCategoryCode": subCategoryCode ?? "TRANSFORM"});
    return response;
  }
}

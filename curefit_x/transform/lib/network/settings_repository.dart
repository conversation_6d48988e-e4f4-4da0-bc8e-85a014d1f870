import 'package:common/network/client.dart';

class SettingsRepository {
  final NetworkClient client;

  SettingsRepository(this.client);

  Future<dynamic> getPageData() async {
    final response = await this.client.get("/v2/transform/whatsappSettings");
    return response;
  }

  Future<dynamic> submitSettings({required bool preference}) async {
    final response = await this.client.post("/v2/transform/whatsappSettings", {
      "preferenceValue" : preference.toString(),
    });
    return response;
  }
}

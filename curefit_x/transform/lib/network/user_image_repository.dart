import 'dart:io';

import 'package:common/network/client.dart';

class UserImageRepository {
  final NetworkClient client;

  UserImageRepository(this.client);

  Future<dynamic> getSignedUrl(String fileName) async {
    final response =
        await client.get('/transform/s3/getSignedUrl', {"fileName": fileName});
    return response;
  }

  Future<dynamic> uploadUserPicToS3(dynamic s3Info, File file) async {
    final response = await client.uploadFile(s3Info['url'], file, s3Info);
    return response;
  }

  Future<dynamic> getDestinationImageUrl(String imageUrl) async {
    final response = await client
        .get('/transform/s3/getValidImageUrl', {"imageUrl": imageUrl});
    return response;
  }

  Future<dynamic> uploadUserImageUrl(
      String imageUrl, bool replacePic, String imageTag) async {
    final response = await client.post('/v2/transform/uploadUserImageUrl',
        {"imageUrl": imageUrl, "replacePic": replacePic, "imageTag": imageTag});
    return response;
  }

  Future<dynamic> getUserImageConfirmationData(
      {String? subCategoryCode, String? pictureTag}) async {
    final response = await client.get('/v2/transform/userImageConfirmation', {
      "subCategoryCode": subCategoryCode ?? "",
      "pictureTag": pictureTag ?? ""
    });

    return response;
  }
}

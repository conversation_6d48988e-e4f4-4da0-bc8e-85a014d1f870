import 'package:common/network/client.dart';
import 'package:transform/blocs/cult_habit_building/models.dart';


class HabitBuildingRepository {
  final NetworkClient client;

  HabitBuildingRepository(this.client);

  Future<dynamic> getHabitBuildingData(Map<String, String> params, {String? baseUrl}) async {
    final response1 = await client.get("/v2/fitness/getHabits");
    print(response1);
    return response1;
  }

  Future<dynamic> setHabit(HabitResponse habitResponse) async {
    final response = await client.post("/v2/fitness/postHabit",{"response": habitResponse.response,"habitID":habitResponse.habitId,"isSelected":habitResponse.isSelected});
    if(response != null) {
      return response;
    }
    return false;
  }

  Future<dynamic> updateHabit(EditHabitResponse habitResponse) async {
    final response = await client.post("/v2/fitness/patchUserAction",{"id":habitResponse.id,"current": habitResponse.response});
    if(response != null) {
      return response;
    }
    return null;
  }
}

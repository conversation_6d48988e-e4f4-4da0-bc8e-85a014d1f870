import 'dart:convert';

import 'package:common/network/client.dart';
import 'package:flutter/services.dart';
import 'package:transform/blocs/content/models.dart';

class ContentRepository {
  final NetworkClient client;

  ContentRepository(this.client);

  Future<dynamic> getChapter(String chapterId, String courseId) async {
    final response = await this.client.get("/v2/transform/chapter",
        {"chapterId": chapterId, "courseId": courseId});
    // final response = await loadMockJson("content-try.json");
    return response;
  }

  Future<dynamic> getChapterForReview(String chapterId) async {
    final response = await this
        .client
        .get("/v2/transform/chapter-review", {"chapterId": chapterId});
    // final response = await loadMockJson("content.json");
    return response;
  }

  loadMockJson(String fileName) async {
    String data = await rootBundle.loadString('assets/mocks/$fileName');
    return json.decode(data);
  }

  Future<dynamic> getCourse(String courseId, String subCategoryCode) async {
    final response = await this.client.get("/v2/transform/content",
        {"courseId": courseId, "subCategoryCode": subCategoryCode});
    return response;
  }

  Future<dynamic> getUserCourseProgress(String courseId,
      {String? subCategoryCode}) async {
    final response = await this.client.get("/v2/transform/contentProgress",
        {"courseId": courseId, "subCategoryCode": subCategoryCode ?? ""});
    return response;
  }

  Future<dynamic> updateChapter(String chapterId, String courseId,
      Map<String, QuestionResponse> answers) async {
    final response = await this.client.post("/v2/transform/updateChapter", {
      "courseId": courseId,
      "chapterId": chapterId,
      "response": answers
          .map<String, dynamic>((key, value) => MapEntry(key, value.toJson()))
    });
    return response;
  }

  Future<dynamic> getCoachContentHistory(
      {String? courseId, String? subCategoryCode}) async {
    final response = await this.client.get("/v2/transform/contentHistory",
        {"courseId": courseId ?? "", "subCategoryCode": subCategoryCode ?? ""});
    return response;
  }

  Future<dynamic> getFitnessPlan({String? subCategoryCode}) async {
    final response = await this.client.get("/v2/transform/fitnessPlan",
        {"subCategoryCode": subCategoryCode ?? ""});
    return response;
  }

  Future<dynamic> getFitnessPlanV2({String? subCategoryCode}) async {
    final response = await this.client.get("/v2/transform/fitnessPlan/V2",
        {"subCategoryCode": subCategoryCode ?? ""});
    return response;
  }

  Future<dynamic> swapWorkoutFitnessPlan(Map<String, String> answers) async {
    final response = await this.client.post("/v2/transform/fitnessPlan/swapWorkout", {
      "payload": answers,
    });
    return response;
  }


  // Future<dynamic> updateFitnessPlanSportWorkout(String fitnessPlanId,
  //     Map<String, String> answers) async {
  //   final response = await this.client.post("/v2/transform/fitnessPlan/V2", {
  //     "fitnessPlanId": fitnessPlanId,
  //     "response": answers,
  //   });
  //   return response;
  // }

}

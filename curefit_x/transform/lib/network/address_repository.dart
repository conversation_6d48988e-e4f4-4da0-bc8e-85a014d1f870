import 'package:common/network/client.dart';

class AddressRepository {
  final NetworkClient client;

  AddressRepository(this.client);

  Future<dynamic> getAddressPageData(Map<String, String> params,
      {String? baseUrl}) async {
    final response = await this.client.get('/transform/addressForm', params, baseUrl);
    return response;
  }

  Future<dynamic> addAddress(Map<String, dynamic> params,
      {String? baseUrl}) async {
    final response = await client.post('/user/address/', params, baseUrl);
    return response;
  }

}

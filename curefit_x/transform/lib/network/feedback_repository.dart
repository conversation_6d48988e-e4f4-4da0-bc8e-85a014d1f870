import 'package:common/network/client.dart';

class FeedbackRepository {
  final NetworkClient client;

  FeedbackRepository(this.client);

  Future<dynamic> getPageData(
      {required String feedbackId, String? subCategoryCode}) async {
    final response = await this.client.get("/feedback/getWidgets/${feedbackId}",
        {"subCategoryCode": subCategoryCode ?? ""});
    return response;
  }

  Future<dynamic> submitFeedback(
      {required String? feedbackId,
      required String? rating,
      required String? review,
      required List<dynamic>? selectedTags}) async {
    final response = await this.client.post("/feedback/submit/v2", {
      "feedbackId": feedbackId,
      "rating": rating,
      "review": review,
      "selectedTags": selectedTags,
    });
    return response;
  }
}

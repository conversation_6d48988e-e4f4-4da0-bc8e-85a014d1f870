import 'package:common/network/client.dart';

class HabitRepository {
  final NetworkClient client;

  HabitRepository(this.client);

  Future<dynamic> updateHabit(String habitId, String status, {String? channel}) async {
    final response = await this.client.post("/v2/transform/updateHabit",
        {"userHabitId": habitId, "status": status, "channel": channel});
    return response;
  }

  Future<dynamic> getHabitsList({String epoch = "",String? tenant}) async {
    final response = await this.client.get("/v2/transform/habitsList",{
      "epoch": epoch,"tenant":tenant ?? "TRANSFORM"});
    return response;
  }

}

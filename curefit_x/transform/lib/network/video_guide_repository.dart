import 'package:common/network/client.dart';

class VideoGuideRepository {
  final NetworkClient client;

  VideoGuideRepository(this.client);

  Future<void> sendUserFulfillmentDetails(String? contentId, int? duration) async {
    try {
      if (contentId != null) {
        final response = await this
            .client
            .post("/transform/pushVideoGuideMetric",
            {"contentId": contentId, "duration": duration});
      }
    }
    catch (e){
    }
  }


}

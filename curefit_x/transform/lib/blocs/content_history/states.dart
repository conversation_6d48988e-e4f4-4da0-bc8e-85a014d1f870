part of 'content_history_bloc.dart';

@immutable
abstract class ContentH<PERSON>oryBlocState {}

class ContentHistoryBlocInitial extends ContentHistoryBlocState {
  @override
  String toString() {
    return "ContentHistoryBlocInitial";
  }
}

class ContentHistoryLoadingState extends ContentHistoryBlocState {
  @override
  String toString() {
    return "ContentHistoryLoadingState";
  }
}

class ContentHistoryLoadedState extends ContentHistoryBlocState {
  final ContentHistoryScreen screenData;

  ContentHistoryLoadedState({required this.screenData}) : super();
  @override
  String toString() {
    return "ContentHistoryLoadedState";
  }
}

class ContentHistoryErrorState extends ContentHistoryBlocState {
  final NetworkException? exception;
  ContentHistoryErrorState(this.exception) : super();
  @override
  String toString() {
    return "ContentHistoryErrorState";
  }
}

import 'package:bloc/bloc.dart';
import 'package:common/network/client.dart';
import 'package:meta/meta.dart';
import 'package:transform/blocs/content_history/models.dart';
import 'package:transform/network/content_repository.dart';
import 'events.dart';

part 'states.dart';

class ContentHistoryBloc
    extends Bloc<ContentHistoryBlocEvent, ContentHistoryBlocState> {
  final ContentRepository repository;

  ContentHistoryBloc(this.repository) : super(ContentHistoryBlocInitial()) {
    on<LoadContentHistoryEvent>((event, emit) async {
      await _mapLoadOnboarding(event, emit);
    });
  }

  Future<void> _mapLoadOnboarding(LoadContentHistoryEvent event,
      Emitter<ContentHistoryBlocState> emit) async {
    emit(ContentHistoryLoadingState());

    try {
      String? courseId = event.courseId;
      final response = await this.repository.getCoachContentHistory(
          courseId: courseId, subCategoryCode: event.subCategoryCode);

      if (response == null) {
        emit(ContentHistoryErrorState(
            throw new Exception("Something went wrong")));
      }
      ContentHistoryScreen contentHistoryScreen = new ContentHistoryScreen(
          response['widgets'],
          title: response['title'],
          subtitle: response['subtitle']);
      emit(ContentHistoryLoadedState(screenData: contentHistoryScreen));
    } on NetworkException catch (exception) {
      emit(ContentHistoryErrorState(exception));
    } catch (e) {
      emit(ContentHistoryErrorState(
          throw new Exception("Something went wrong")));
    }
  }
}

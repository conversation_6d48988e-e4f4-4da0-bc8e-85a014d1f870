abstract class ContentHistoryBlocEvent {}

class LoadContentHistoryEvent extends ContentHistoryBlocEvent {
  final String? courseId;
  final String? subCategoryCode;

  LoadContentHistoryEvent({this.courseId, this.subCategoryCode}) : super();

  @override
  String toString() {
    return "LoadContentHistoryEvent";
  }
}

class MockContentHistoryEvent extends ContentHistoryBlocEvent {
  @override
  String toString() {
    return "MockContentHistoryEvent";
  }
}

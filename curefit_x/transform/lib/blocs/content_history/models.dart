import 'package:common/action/action_handler.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:transform/blocs/content/models.dart';

class ContentHistoryScreen {
  ContentHistoryScreen(this.widgets, {this.title, this.subtitle});

  final List widgets;
  final String? title;
  final String? subtitle;
}

class ChapterTileInfo extends ChapterTileData implements IWidgetData {
  bool isLastUnread;
  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  ChapterTileInfo(this.widgetType,
      {String? title,
      this.widgetInfo,
      String? subTitle,
      String? imageUrl,
      String? completedDate,
      bool? isLocked,
      UserChapterStatus? status,
      Action? action,
      this.isLastUnread = false})
      : super(
            title: title,
            subTitle: subTitle,
            imageUrl: imageUrl,
            completedDate: completedDate,
            isLocked: isLocked,
            status: status,
            action: action);
}

class CoachArchiveModuleModel implements IWidgetData {
  List<ChapterTileInfo>? chapters;
  late String title;
  String? subTitle;
  UserChapterStatus? status;
  bool? isCurrentWeek;

  CoachArchiveModuleModel(this.widgetType, this.chapters, this.title,
      this.subTitle, this.status, this.isCurrentWeek,
      {this.widgetInfo});

  factory CoachArchiveModuleModel.fromJson(WidgetTypes widgetType,
      Map<String, dynamic> json, WidgetInfo widgetInfo) {
    String title = json['title'] ?? "";
    String subTitle = json['subtitle'];
    UserChapterStatus? status =
        (json['status'] != null && json['status'] is String)
            ? EnumToString.fromString(UserChapterStatus.values, json["status"])
            : UserChapterStatus.ASSIGNED;
    List<ChapterTileInfo> chapters = [];
    if (json['chapters'] != null) {
      chapters = json["chapters"].map<ChapterTileInfo>((chapter) {
        return ChapterTileInfo(widgetType,
            title: chapter["title"],
            subTitle: chapter["subTitle"],
            imageUrl: chapter["imageUrl"],
            completedDate: chapter["completedDate"],
            isLocked: chapter["isLocked"],
            status: EnumToString.fromString(
                UserChapterStatus.values, chapter["status"]),
            action: new Action.fromJson(chapter["action"]));
      }).toList();
      if (chapters.isNotEmpty) {
        int lastUnreadIndex = chapters.lastIndexWhere(
            (element) => element.status != UserChapterStatus.COMPLETED);
        if (lastUnreadIndex >= 0 && lastUnreadIndex < chapters.length) {
          chapters[lastUnreadIndex].isLastUnread = true;
        }
      }
    }

    return new CoachArchiveModuleModel(
        widgetType, chapters, title, subTitle, status, false,
        widgetInfo: widgetInfo);
  }

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;
}

import 'package:flutter/material.dart';
import 'package:transform/blocs/calendar/models.dart';
// import 'package:common/model/habit_building_model.dart';
import 'package:transform/blocs/cult_habit_building/models.dart';

@immutable
abstract class ActionCalendarState {
  ActionCalendarState() : super();
}

class ActionCalendarIdleState extends ActionCalendarState {
  @override
  String toString() => 'ActionCalendarIdleState';
}

class ActionCalendarLoadingState extends ActionCalendarState {
  @override
  String toString() => 'ActionCalendarLoadingState';
}

class ActionCalendarLoadedState extends ActionCalendarState {
  final ActionCalendarScreenData actionCalendarScreenData;
  String? toastText;

  ActionCalendarLoadedState({required this.actionCalendarScreenData,this.toastText})
      : super();

  @override
  String toString() => 'ActionCalendarLoadedState';
}

class LoadHabitEditPageState extends ActionCalendarState {
  final Pages page;
  LoadHabitEditPageState({required this.page}): super();
  @override
  String toString() => 'LoadHabitEditPageState';
}

class SaveEditHabitResponseState extends ActionCalendarState {

  final SaveDataPage savePage;

  SaveEditHabitResponseState({required this.savePage}): super();
  @override
  String toString() => 'SaveEditHabitResponseState';
}

class ActionCalendarFailedState extends ActionCalendarState {
  final String? error;

  ActionCalendarFailedState([this.error]) : super();

  @override
  String toString() => 'ActionCalendarFailedState';
}

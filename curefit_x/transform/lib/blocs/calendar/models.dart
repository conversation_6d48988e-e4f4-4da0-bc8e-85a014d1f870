import 'package:common/ui/widget_builder.dart';
// import 'package:common/model/habit_building_model.dart';
import 'package:transform/blocs/cult_habit_building/models.dart';

class ActionCalendarScreenData {
  ActionCalendarScreenData(
      {this.title,
      this.subtitle,
      this.scoreValue,
      this.consistencyText,
      this.widget,
      this.days,
      this.daysDescription,
      this.streak,
      this.streakDescription,
      this.editPage,
      this.editAction,
      this.deleteAction,
        this.deleteTexts
      });

  final String? title;
  final String? subtitle;
  final double? scoreValue;
  final String? consistencyText;
  final dynamic widget;
  final String? days;
  final String? daysDescription;
  final String? streak;
  final String? streakDescription;
  final Pages? editPage;
  final String? editAction;
  final String? deleteAction;
  final List<String>? deleteTexts;


  static ActionCalendarScreenData fromJson(widget) {
    return ActionCalendarScreenData(
      title: widget['title'],
      subtitle: widget['subtitle'],
      scoreValue: widget['scoreValue'],
      consistencyText: widget['consistencyText'],
      widget: widget['calendarWidget'][0],
      days: widget['days'],
      daysDescription: widget['daysDescription'],
      streak: widget['streak'],
      streakDescription: widget['streakDescription'],
      editPage: widget['page'] != null ? Pages.fromJson(widget['page']) : null,
      editAction: widget['editAction'],
      deleteAction: widget['deleteAction'],
      deleteTexts: widget["deleteText"] != null ?
                  widget["deleteText"].map<String>((dynamic obj) =>  obj.toString()).toList()
                    : []
    );
  }
}

class ActionCalendarData implements IWidgetData {
  ActionCalendarData(this.widgetType,
      {this.actionCalendarData,
      this.latestMonth,
      this.latestYear,
      this.startingMonth,
      this.startingYear});

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  final actionCalendarData;
  final int? latestMonth;
  final int? latestYear;
  final int? startingMonth;
  final int? startingYear;

  static ActionCalendarData fromJson(widget, WidgetTypes widgetType) {
    var calendarData = {};
    for (var data in widget['actionCalendarData']) {
      calendarData[data[0]] = data[1];
    }
    if(widget['latestMonth'] != null && widget['latestMonth']==0){
      widget['latestMonth'] = DateTime.now().month;
    }
    if(widget['latestYear'] != null && widget['latestYear']==0){
      widget['latestYear'] = DateTime.now().year;
    }
    return ActionCalendarData(
      widgetType,
      actionCalendarData: calendarData,
      latestMonth: widget['latestMonth'],
      latestYear: widget['latestYear'],
      startingMonth: widget['startingMonth'],
      startingYear: widget['startingYear'],
    );
  }
}

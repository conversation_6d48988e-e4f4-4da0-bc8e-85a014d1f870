// import 'package:common/model/habit_building_model.dart';
import 'package:common/network/client.dart';
import 'package:common/network/client_repository.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/calendar/events.dart';
import 'package:transform/blocs/calendar/models.dart';
import 'package:transform/blocs/calendar/state.dart';
import 'package:transform/blocs/cult_habit_building/models.dart';
import 'package:transform/network/cult_habit_building_repository.dart';

class ActionCalendarBloc
    extends Bloc<ActionCalendarEvent, ActionCalendarState> {

  final CoachClientRepository repository;
  final HabitBuildingRepository habitRepository;

  ActionCalendarBloc(ActionCalendarIdleState initialState, this.repository,this.habitRepository)
      : super(initialState) {
    on<ActionCalendarLoadEvent>((event, emit) async {
      await _mapLoadCalendarToState(event, emit);
    });

    on<ActionCalendarDateChangeEvent>((event, emit) async {
      await _mapCalendarToState(event, emit);
    });

    on<ShowEditHabitPageEvent>((event,emit) async {
      print("reached edit event");
      emit(LoadHabitEditPageState(page:event.page));
    });
    
    //Add api call here
    on<SaveEditTimerResponseEvent> ((event,emit) async {
      emit(ActionCalendarLoadingState());
      print(event.habitResponse);
      EditHabitResponse editHabitResponse = new EditHabitResponse(response: event.habitResponse.response, id: event.habitResponse.actionID!);
      print(editHabitResponse);
      final response = await habitRepository.updateHabit(editHabitResponse);
      if(response != null) {
        add(ActionCalendarLoadEvent(actionId: event.actionId,subCategoryCode: event.subCategoryCode,toastText: "Habit Updated. Reminders will be updated accordingly."));
        // emit(SaveEditHabitResponseState(savePage:SaveDataPage.fromJson(response)));
      } else {
        emit(ActionCalendarFailedState("Something went wrong"));
      }
    });

    on<DeleteHabitEvent> ((event,emit) async {
      print(event.habitResponse);
      emit(ActionCalendarLoadingState());
      final response = await habitRepository.setHabit(event.habitResponse);
      if(response != null) {
        // add(ActionCalendarLoadEvent(actionId: event.actionId,subCategoryCode: event.subCategoryCode));
        emit(SaveEditHabitResponseState(savePage:SaveDataPage.fromJson(response)));
      } else {
        ActionCalendarFailedState("Something went wrong");
      }
    });
  }

  Future<void> _mapLoadCalendarToState(
      ActionCalendarLoadEvent event, Emitter<ActionCalendarState> emit) async {
    try {
      emit(ActionCalendarLoadingState());
      final response =
          await this.repository.getPageData('v2/transform/actionCalendar', {
        "actionId": event.actionId!,
        "subCategoryCode": event.subCategoryCode ?? "",
      });
      print("response for progress ${response}");
      if (response != null) {
        final detail = response;

        emit(ActionCalendarLoadedState(
            actionCalendarScreenData:
                ActionCalendarScreenData.fromJson(detail), toastText: event.toastText));
      } else {
        emit(ActionCalendarFailedState());
      }
    } on NetworkException catch (exception) {
      emit(ActionCalendarFailedState(exception.subTitle));
    } catch (e) {
      emit(ActionCalendarFailedState("Something went wrong"));
    }
  }

  Future<void> _mapCalendarToState(ActionCalendarDateChangeEvent event,
      Emitter<ActionCalendarState> emit) async {
    try {
      emit(ActionCalendarLoadingState());
      final response =
          await this.repository.getPageData('v2/transform/actionCalendar', {
        "actionId": event.actionId!,
        "subCategoryCode": event.subCategoryCode ?? "",
      });
      if (response != null) {
        final detail = response;
        emit(ActionCalendarLoadedState(
            actionCalendarScreenData:
                ActionCalendarScreenData.fromJson(detail)));
      } else {
        emit(ActionCalendarFailedState());
      }
    } on NetworkException catch (exception) {
      emit(ActionCalendarFailedState(exception.subTitle));
    } catch (e) {
      emit(ActionCalendarFailedState("Something went wrong"));
    }
  }
}

// import 'package:common/model/habit_building_model.dart';
import 'package:transform/blocs/cult_habit_building/models.dart';

abstract class ActionCalendarEvent {
  ActionCalendarEvent() : super();
}

class ActionCalendarDateChangeEvent extends ActionCalendarEvent {
  final String? actionId;
  final String? value;
  final String? subCategoryCode;
  ActionCalendarDateChangeEvent({required this.actionId, required this.value, this.subCategoryCode}) : super();

  @override
  String toString() => 'ActionCalendarDateChangeEvent';
}

class ActionCalendarLoadEvent extends ActionCalendarEvent {
  final String? actionId;
  final String? subCategoryCode;
  final String? toastText;
  ActionCalendarLoadEvent({required this.actionId, this.subCategoryCode,this.toastText}) : super();

  @override
  String toString() => 'ActionCalendarLoadEvent';
}

class ShowEditHabitPageEvent extends ActionCalendarEvent {
  final Pages page;
  ShowEditHabitPageEvent({required this.page}): super();
  @override
  String toString() => 'ShowEditHabitPageEvent';
}

class SaveEditTimerResponseEvent extends ActionCalendarEvent {
  final HabitResponse habitResponse;
  String? actionId;
  String? subCategoryCode;
  SaveEditTimerResponseEvent({required this.habitResponse,this.actionId,this.subCategoryCode}): super();
  @override
  String toString() => 'SaveEditTimerResponseEvent';
}

class MoveBackToActionCalendarEvent extends ActionCalendarEvent {
  MoveBackToActionCalendarEvent(): super();
  @override
  String toString() => 'MoveBackToActionCalendarEvent';
}

class DeleteHabitEvent extends ActionCalendarEvent {
  HabitResponse habitResponse;
  String? actionId;
  String? subCategoryCode;
  DeleteHabitEvent({required this.habitResponse,this.actionId,this.subCategoryCode}): super();
  @override
  String toString() => 'DeleteHabitEvent';
}

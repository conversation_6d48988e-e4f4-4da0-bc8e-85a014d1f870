import 'dart:io';

import 'package:bloc/bloc.dart';
import 'package:common/model/error_prop_model.dart';
import 'package:common/network/client.dart';
import 'package:common/network/client_repository.dart';
import 'package:transform/blocs/lift_clp/events.dart';
import 'package:transform/blocs/lift_clp/models.dart';
import 'package:transform/blocs/lift_clp/state.dart';

class LiftClpBloc extends Bloc<LiftClpEvent, LiftClpState> {
  final String pageId = "lift_post";
  final CoachClientRepository repository;
  LiftClpData screenData = LiftClpData(widgets: []);

  LiftClpBloc(this.repository) : super(LiftClpIdleState()) {
    on<LoadLiftClpEvent>((event, emit) async {
      await _mapLoadLiftClp(event, emit);
    });
    on<ResetLiftClpEvent>((event, emit) async {
      screenData = LiftClpData(widgets: []);
      emit(LiftClpLoadedState(screenData: screenData));
    });
  }

  void refresh() {
    this.add(LoadLiftClpEvent());
  }

  Future<void> _mapLoadLiftClp(
      LoadLiftClpEvent event, Emitter<LiftClpState> emit) async {
    emit(LiftClpLoadingState(
        screenData: screenData, showLoader: event.showLoader));
    try {
      final response = await this.repository.getPageData('v2/page/$pageId', {});
      if (response != null && response['body'] != null) {
        screenData = LiftClpData.fromJson(response);
        emit(LiftClpLoadedState(screenData: screenData));
      } else {
        emit(LiftClpErrorState(UnknownError(
            failed: true, responseCode: "404", callbackFunction: refresh)));
      }
    } on NetworkException catch (exception) {
      emit(LiftClpErrorState(NetworkError(
          failed: true,
          responseCode: '${(exception).statusCode}',
          callbackFunction: refresh)));
    } on SocketException {
      emit(LiftClpErrorState(
          NoInternetError(failed: true, callbackFunction: refresh)));
    } catch (e) {
      emit(LiftClpErrorState(
          UnknownError(failed: true, callbackFunction: refresh)));
    }
  }
}

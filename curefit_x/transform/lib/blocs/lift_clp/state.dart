import 'package:common/model/error_prop_model.dart';

import 'models.dart';

abstract class LiftClpState {
  LiftClpState() : super();
}

class LiftClpIdleState extends LiftClpState {
  @override
  String toString() => 'LiftClpIdleState';
}

class LiftClpLoadingState extends LiftClpState {
  final LiftClpData? screenData;
  final bool showLoader;

  LiftClpLoadingState({this.screenData, this.showLoader = true});

  @override
  String toString() => 'LiftClpLoadingState';
}

class LiftClpLoadedState extends LiftClpState {
  final LiftClpData screenData;

  LiftClpLoadedState(
      {required this.screenData})
      : super();

  @override
  String toString() => 'LiftClpLoadedState';
}

class LiftClpErrorState extends LiftClpState {
  final ErrorInfo? errorInfo;

  LiftClpErrorState([this.errorInfo]) : super();

  @override
  String toString() => 'LiftClpErrorState';
}

import 'package:common/action/action_handler.dart';
import 'package:common/ui/header/card_header.dart';
import 'package:common/ui/widget_builder.dart';

class LiftClpData {
  LiftClpData({
    required this.widgets,
    this.title,
  });

  final String? title;
  final List<dynamic>? widgets;

  static LiftClpData fromJson(json) {
    return LiftClpData(
      title: json["name"],
      widgets: json['body'],
    );
  }
}

class MetricInputWidgetData implements IWidgetData {
  MetricInputWidgetData(
    this.widgetType, {
    this.title,
    this.subtitle,
    this.value,
    this.metricId,
    this.unit,
    this.initialState,
    this.editAction,
    this.updateAction,
    this.subCategoryCode,
    this.precision,
    this.widgetInfo,
        this.usePadding = true,
  }) : super();

  final String? title;
  final String? subtitle;
  final num? value;
  final String? unit;
  final int? metricId;
  final int? precision;
  final String? initialState;
  final String? subCategoryCode;
  final Action? editAction;
  final Action? updateAction;
  final bool usePadding;

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  factory MetricInputWidgetData.fromJson(
      WidgetTypes widgetType, dynamic json, WidgetInfo? widgetInfo) {
    return MetricInputWidgetData(
      widgetType,
      title: json['title'],
      subtitle: json['subtitle'],
      value: json['value'],
      unit: json['unit'],
      metricId: json['metricId'],
      precision: json['precision'],
      initialState: json['initialState'],
      usePadding: json['usePadding'] ?? true,
      subCategoryCode: json['subCategoryCode'],
      editAction: json['editAction'] != null
          ? Action.fromJson(json['editAction'])
          : null,
      updateAction: json['updateAction'] != null
          ? Action.fromJson(json['updateAction'])
          : null,
      widgetInfo: widgetInfo,
    );
  }
}

class FitnessPlanWidgetData implements IWidgetData {
  FitnessPlanWidgetData(
    this.widgetType, {
    this.currentIndex = 0,
    this.fitnessDetailEntryList = const [],
    this.header,
    this.widgetInfo,
  }) : super();

  final int currentIndex;
  final List<FitnessDetailEntry> fitnessDetailEntryList;
  final WidgetHeaderData? header;

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  factory FitnessPlanWidgetData.fromJson(
      WidgetTypes widgetType, dynamic json, WidgetInfo? widgetInfo) {
    return FitnessPlanWidgetData(
      widgetType,
      currentIndex: json['fitnessPlanWidgetView']['currentIndex'] ?? 0,
      fitnessDetailEntryList:
          json['fitnessPlanWidgetView']['fitnessDetailEntryList'] != null
              ? json['fitnessPlanWidgetView']['fitnessDetailEntryList']
                  .map<FitnessDetailEntry>((entry) {
                  return FitnessDetailEntry.fromJson(entry);
                }).toList()
              : [],
      header: json['header'] != null
          ? WidgetHeaderData.fromJson(json['header'])
          : null,
      widgetInfo: widgetInfo,
    );
  }
}

class FitnessDetailEntry {
  FitnessDetailEntry({
    this.widgets,
    this.date,
    this.id,
  });

  final String? date;
  final String? id;
  final List<dynamic>? widgets;

  static FitnessDetailEntry fromJson(json) {
    return FitnessDetailEntry(
      date: json["date"],
      widgets: json['widgets'],
      id: json['id'],
    );
  }
}

class WorkoutListWidgetData implements IWidgetData {
  WorkoutListWidgetData(
    this.widgetType, {
    this.title,
    this.workoutCards = const [],
    this.widgetInfo,
  }) : super();

  final String? title;
  final List<WorkoutCard> workoutCards;

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  factory WorkoutListWidgetData.fromJson(
      WidgetTypes widgetType, dynamic json, WidgetInfo? widgetInfo) {
    return WorkoutListWidgetData(
      widgetType,
      title: json['title'],
      workoutCards: json['workoutCards'] != null
          ? json['workoutCards'].map<WorkoutCard>((card) {
              return WorkoutCard.fromJson(card);
            }).toList()
          : [],
      widgetInfo: widgetInfo,
    );
  }
}

class WorkoutCard {
  WorkoutCard({
    this.title,
    this.subtitle,
    this.imageUrl,
    this.isCompleted = false,
    this.action,
  });

  final String? title;
  final String? subtitle;
  final String? imageUrl;
  final bool isCompleted;
  final Action? action;

  static WorkoutCard fromJson(json) {
    return WorkoutCard(
      title: json["title"],
      subtitle: json['subtitle'],
      imageUrl: json['imageUrl'],
      isCompleted: json['isCompleted'] ?? false,
      action: json['action'] != null ? Action.fromJson(json['action']) : null,
    );
  }
}

class WorkoutModalData {
  WorkoutModalData({
    this.id,
    this.exerciseId,
    this.modalTitle,
    this.title,
    this.subtitle,
    this.imageUrl,
    this.isCompleted = false,
    this.setDetails = const [],
    this.action,
    this.primaryAction,
    this.secondaryAction,
  });

  final String? id;
  final String? exerciseId;
  final String? modalTitle;
  final String? title;
  final String? subtitle;
  final String? imageUrl;
  final bool isCompleted;
  final List<SetDetails> setDetails;
  final Action? action;
  final Action? primaryAction;
  final Action? secondaryAction;

  static WorkoutModalData fromJson(json) {
    return WorkoutModalData(
      id: json['id'],
      exerciseId: json['exerciseId'],
      modalTitle: json["modalTitle"],
      title: json["title"],
      subtitle: json['subtitle'],
      imageUrl: json['imageUrl'],
      isCompleted: json['isCompleted'] ?? false,
      setDetails: json['setDetail'] != null
          ? json['setDetail'].map<SetDetails>((set) {
              return SetDetails.fromJson(set);
            }).toList()
          : [],
      action: json['action'] != null ? Action.fromJson(json['action']) : null,
      primaryAction: json['primaryAction'] != null
          ? Action.fromJson(json['primaryAction'])
          : null,
      secondaryAction: json['secondaryAction'] != null
          ? Action.fromJson(json['secondaryAction'])
          : null,
    );
  }
}

class SetDetails {
  SetDetails({
    this.id,
    this.title,
    this.reps,
    this.weight,
    this.repUnit,
    this.weightUnit,
    this.suggestedReps,
    this.suggestedWeight,
    this.repEditable = false,
    this.weightEditable = false,
  });

  final String? id;
  final String? title;
  late String? reps;
  late String? weight;
  final String? repUnit;
  final String? weightUnit;
  final String? suggestedReps;
  final String? suggestedWeight;
  final bool repEditable;
  final bool weightEditable;

  static SetDetails fromJson(json) {
    return SetDetails(
      id: json["id"],
      title: json["title"],
      reps: json['reps'],
      weight: json['weight'],
      repUnit: json['repUnit'],
      weightUnit: json['weightUnit'],
      suggestedReps: json['suggestedRep'],
      suggestedWeight: json['suggestedWeight'],
      repEditable: json['repEditable'] ?? false,
      weightEditable: json['weightEditable'] ?? false,
    );
  }
}

class ExerciseMetric {
  ExerciseMetric({
    this.weight,
    this.reps,
    this.exerciseId,
    this.id,
  });

  String? weight;
  String? reps;
  String? exerciseId;
  String? id;

  static ExerciseMetric fromJson(json) {
    return ExerciseMetric(
      weight: json["weight"],
      reps: json["reps"],
      exerciseId: json['exerciseId'],
      id: json['id'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "weight": this.weight,
      "reps": this.reps,
      "exerciseId": this.exerciseId,
      "id": this.id,
    };
  }
}

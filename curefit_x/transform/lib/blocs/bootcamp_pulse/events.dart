import 'package:transform/UI/clp/screens/bootcamp_pulse_clp.dart';

abstract class BootcampPulseClpEvent {
  BootcampPulseClpEvent() : super();
}

class LoadBootcampPulseClpEvent extends BootcampPulseClpEvent {
  final bool showLoader;
  final BootcampPulseClpArguments? arguments;

  LoadBootcampPulseClpEvent({this.showLoader = false, this.arguments}) : super();

  @override
  String toString() {
    return "LoadBootcampPulseClpEvent";
  }
}

class ResetBootcampPulseClpEvent extends BootcampPulseClpEvent {
  @override
  String toString() => 'ResetBootcampPulseClpEvent';
}

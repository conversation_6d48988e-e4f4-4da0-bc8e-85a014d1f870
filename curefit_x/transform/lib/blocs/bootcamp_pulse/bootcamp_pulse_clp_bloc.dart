import 'dart:convert';
import 'dart:io';

import 'package:bloc/bloc.dart';
import 'package:common/model/error_prop_model.dart';
import 'package:common/network/client.dart';
import 'package:common/network/client_repository.dart';
import 'package:transform/blocs/bootcamp_pulse/events.dart';
import 'package:transform/blocs/bootcamp_pulse/models.dart';
import 'package:transform/blocs/bootcamp_pulse/state.dart';

class BootcampPulseClpBloc
    extends Bloc<BootcampPulseClpEvent, BootcampPulseClpState> {
  final CoachClientRepository repository;
  BootcampPulseClpData screenData = BootcampPulseClpData(widgets: []);

  BootcampPulseClpBloc(this.repository) : super(BootcampPulseClpIdleState()) {
    on<LoadBootcampPulseClpEvent>((event, emit) async {
      await _mapLoadBootcampPulseClp(event, emit);
    });
    on<ResetBootcampPulseClpEvent>((event, emit) async {
      screenData = BootcampPulseClpData(widgets: []);
      emit(BootcampPulseClpLoadedState(screenData: screenData));
    });
  }

  void refresh() {
    this.add(LoadBootcampPulseClpEvent());
  }

  Future<void> _mapLoadBootcampPulseClp(LoadBootcampPulseClpEvent event,
      Emitter<BootcampPulseClpState> emit) async {
    emit(BootcampPulseClpLoadingState(
        screenData: screenData, showLoader: event.showLoader));
    try {
      final String pageId = event.arguments?.pageId ?? "bootcamp_pulse_pre";
      final response =
          await this.repository.getPageData('v2/transform/$pageId', {
        "subCategoryCode": event.arguments?.subCategoryCode ?? "",
        "slotId": event.arguments?.slotId ?? "",
        "batchId": event.arguments?.batchId ?? "",
        "centerId": event.arguments?.centerId ?? "",
        "pageId": pageId,
      });
      if (response != null && response['widgets'] != null) {
        screenData = BootcampPulseClpData.fromJson(response);
        emit(BootcampPulseClpLoadedState(screenData: screenData));
      } else {
        emit(BootcampPulseClpErrorState(UnknownError(
            failed: true, responseCode: "404", callbackFunction: refresh)));
      }
    } on NetworkException catch (exception) {
      emit(BootcampPulseClpErrorState(NetworkError(
          failed: true,
          responseCode: '${(exception).statusCode}',
          callbackFunction: refresh)));
    } on SocketException {
      emit(BootcampPulseClpErrorState(
          NoInternetError(failed: true, callbackFunction: refresh)));
    } catch (e) {
      emit(BootcampPulseClpErrorState(
          UnknownError(failed: true, callbackFunction: refresh)));
    }
  }
}

import 'package:common/model/error_prop_model.dart';
import 'package:transform/blocs/bootcamp_pulse/models.dart';

abstract class BootcampPulseClpState {
  BootcampPulseClpState() : super();
}

class BootcampPulseClpIdleState extends BootcampPulseClpState {
  @override
  String toString() => 'BootcampPulseClpIdleState';
}

class BootcampPulseClpLoadingState extends BootcampPulseClpState {
  final BootcampPulseClpData? screenData;
  final bool showLoader;

  BootcampPulseClpLoadingState({this.screenData, this.showLoader = true});

  @override
  String toString() => 'BootcampPulseClpLoadingState';
}

class BootcampPulseClpLoadedState extends BootcampPulseClpState {
  final BootcampPulseClpData screenData;

  BootcampPulseClpLoadedState(
      {required this.screenData})
      : super();

  @override
  String toString() => 'BootcampPulseClpLoadedState';
}

class BootcampPulseClpErrorState extends BootcampPulseClpState {
  final ErrorInfo? errorInfo;

  BootcampPulseClpErrorState([this.errorInfo]) : super();

  @override
  String toString() => 'BootcampPulseClpErrorState';
}

import 'package:common/action/action_handler.dart';

class BootcampPulseClpData {
  BootcampPulseClpData({
    required this.widgets,
    this.footerAction,
    this.rightAction,
    this.floatingActions,
    this.title,
  });

  final String? title;
  final List<dynamic>? widgets;
  final Action? footerAction;
  final Action? rightAction;
  final List<Action>? floatingActions;

  static BootcampPulseClpData fromJson(json) {
    return BootcampPulseClpData(
      title: json["title"],
      widgets: json['widgets'],
      footerAction: json['footerAction'] != null
          ? Action.fromJson(json['footerAction'])
          : null,
      rightAction: json['rightAction'] != null
          ? Action.fromJson(json['rightAction'])
          : null,
      floatingActions: json["floatingActions"] != null
          ? json["floatingActions"]
              .map<Action>((action) => Action.fromJson(action))
              .toList()
          : null,
    );
  }
}

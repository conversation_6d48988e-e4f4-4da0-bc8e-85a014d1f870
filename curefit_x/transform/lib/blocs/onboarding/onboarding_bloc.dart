import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/onboarding/events.dart';
import 'package:transform/blocs/onboarding/models.dart';
import 'package:transform/blocs/onboarding/state.dart';
import 'package:transform/network/onboarding_repository.dart';
import 'package:common/action/action_handler.dart';

class OnboardingBloc extends Bloc<OnboardingEvent, OnboardingState> {
  final OnboardingRepository repository;

  OnboardingBloc(OnboardingState initialState, this.repository)
      : super(initialState) {
    on<LoadOnboardingEvent>((event, emit) async {
      await _mapLoadOnboarding(event, emit);
    });
  }

  Stream<OnboardingState> _mapLoadOnboarding(
      LoadOnboardingEvent event, Emitter<OnboardingState> emit) async* {
    emit(OnboardingLoading());
    try {
      final response =
          await this.repository.getOnboardingDetails(event.showTrial);

      if (response == null) {
        emit(OnboardingNotLoaded(throw new Error()));
      }

      final page = OnboardingPage(
          actions: response["actions"]
              .map<Action>((e) => Action.fromJson(e))
              .toList(),
          cards: response["cards"]
              .map<OnboardingCard>((e) => OnboardingCard(
                  title: e["title"],
                  description: e["description"],
                  imageUrl: e["imageUrl"]))
              .toList());
      emit(OnboardingLoaded(page));
    } catch (error) {
      emit(OnboardingNotLoaded(error));
    }
  }
}

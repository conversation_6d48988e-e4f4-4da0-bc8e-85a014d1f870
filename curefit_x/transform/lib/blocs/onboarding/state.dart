import 'package:meta/meta.dart';
import 'package:transform/blocs/onboarding/models.dart';

@immutable
abstract class OnboardingState {
  OnboardingState() : super();
}

class OnboardingIdleState extends OnboardingState {
  @override
  String toString() => 'OnboardingIdleState';
}

class OnboardingLoading extends OnboardingState {
  @override
  String toString() => 'OnboardingLoading';
}

class OnboardingLoaded extends OnboardingState {
  final OnboardingPage page;
  OnboardingLoaded(this.page) : super();

  @override
  String toString() => 'OnboardingLoaded';
}

class OnboardingNotLoaded extends OnboardingState {
  final Object? error;

  OnboardingNotLoaded([this.error]) : super();

  @override
  String toString() => 'OnboardingNotLoaded';
}

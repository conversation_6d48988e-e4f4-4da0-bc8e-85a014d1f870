import 'package:common/action/action_handler.dart';
import 'package:common/ui/header/card_header.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:transform/blocs/user_progress/models.dart';

class LifestyleAssessmentScreenData {
  LifestyleAssessmentScreenData({
    this.title,
    this.widgets = const [],
    this.action,
  });

  final List<dynamic> widgets;
  final String? title;
  final Action? action;

  static LifestyleAssessmentScreenData fromJson(json) {
    return LifestyleAssessmentScreenData(
      title: json['title'],
      widgets: json['widgets'] ?? [],
      action: json['action'] != null ? Action.fromJson(json['action']) : null,
    );
  }
}

class LifestyleAssessmentWidgetData implements IWidgetData {
  LifestyleAssessmentWidgetData(this.widgetType, {
    this.image,
    this.headerImage,
    this.headerData,
    this.widgetInfo,
    this.action,
    this.showWaterView = false,
    this.waterLevelAnimationView,
    this.subCategoryCode,
    this.imageHeight,
    this.imageWidth,
  }) : super();

  final String? image;
  final String? headerImage;
  final String? subCategoryCode;
  final Action? action;
  final bool showWaterView;
  final WidgetHeaderData? headerData;
  final double? imageHeight;
  final double? imageWidth;
  final WaterLevelAnimationView? waterLevelAnimationView;

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  factory LifestyleAssessmentWidgetData.fromJson(WidgetTypes widgetType,
      dynamic json, WidgetInfo? widgetInfo) {
    return LifestyleAssessmentWidgetData(
      widgetType,
      image: json['image'] ?? "",
      headerImage: json['headerImage'],
      action: json['action'] != null ? Action.fromJson(json['action']) : null,
      showWaterView: json['showWaterView'] ?? false,
      subCategoryCode: json['subCategoryCode'],
      imageHeight: json['imageHeight'],
      imageWidth: json['imageWidth'],
      waterLevelAnimationView: json["waterLevelAnimationView"] != null
          ? WaterLevelAnimationView.fromJson(json["waterLevelAnimationView"])
          : null,
      widgetInfo: widgetInfo,
    );
  }
}

import 'dart:io';

import 'package:bloc/bloc.dart';
import 'package:common/model/error_prop_model.dart';
import 'package:common/network/client.dart';
import 'package:common/network/client_repository.dart';
import 'package:transform/blocs/lifestyle_assessment/events.dart';
import 'package:transform/blocs/lifestyle_assessment/models.dart';
import 'package:transform/blocs/lifestyle_assessment/state.dart';

class LifestyleAssessmentScreenBloc extends Bloc<LifestyleAssessmentScreenEvent,
    LifestyleAssessmentScreenState> {
  final CoachClientRepository repository;
  String subCategoryCode = "";
  LifestyleAssessmentScreenData screenData = LifestyleAssessmentScreenData();

  LifestyleAssessmentScreenBloc(this.repository)
      : super(LifestyleAssessmentScreenIdleState()) {
    on<LoadLifestyleAssessmentEvent>((event, emit) async {
      await _mapLoadLifestyleAssessmentScreen(event, emit);
    });
    on<ResetLifestyleAssessmentEvent>((event, emit) async {
      screenData = LifestyleAssessmentScreenData();
      emit(LifestyleAssessmentScreenLoadedState(screenData: screenData));
    });
  }

  void refresh() {
    this.add(
        LoadLifestyleAssessmentEvent(subCategoryCode: this.subCategoryCode));
  }

  Future<void> _mapLoadLifestyleAssessmentScreen(
      LoadLifestyleAssessmentEvent event,
      Emitter<LifestyleAssessmentScreenState> emit) async {
    emit(LifestyleAssessmentScreenLoadingState(
        screenData: screenData, showLoader: event.showLoader));
    try {
      this.subCategoryCode = event.subCategoryCode ?? "";
      final response = await this.repository.getPageData(
          'v2/transform/lifestyleAssessment',
          {"subCategoryCode": this.subCategoryCode});
      if (response != null && response['widgets'] != null) {
        screenData = LifestyleAssessmentScreenData.fromJson(response);
        emit(LifestyleAssessmentScreenLoadedState(screenData: screenData));
      } else {
        emit(LifestyleAssessmentScreenErrorState(UnknownError(
            failed: true, responseCode: "404", callbackFunction: refresh)));
      }
    } on NetworkException catch (exception) {
      emit(LifestyleAssessmentScreenErrorState(NetworkError(
          failed: true,
          responseCode: '${(exception).statusCode}',
          callbackFunction: refresh)));
    } on SocketException {
      emit(LifestyleAssessmentScreenErrorState(
          NoInternetError(failed: true, callbackFunction: refresh)));
    } catch (e) {
      emit(LifestyleAssessmentScreenErrorState(
          UnknownError(failed: true, callbackFunction: refresh)));
    }
  }
}

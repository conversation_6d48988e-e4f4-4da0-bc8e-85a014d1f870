import 'package:common/model/error_prop_model.dart';
import 'package:transform/blocs/lifestyle_assessment/models.dart';


abstract class LifestyleAssessmentScreenState {
  LifestyleAssessmentScreenState() : super();
}

class LifestyleAssessmentScreenIdleState extends LifestyleAssessmentScreenState {
  @override
  String toString() => 'ReferralScreenIdleState';
}

class LifestyleAssessmentScreenLoadingState extends LifestyleAssessmentScreenState {
  final LifestyleAssessmentScreenData? screenData;
  final bool showLoader;

  LifestyleAssessmentScreenLoadingState({this.screenData, this.showLoader = true});

  @override
  String toString() => 'LifestyleAssessmentScreenLoadingState';
}

class LifestyleAssessmentScreenLoadedState extends LifestyleAssessmentScreenState {
  final LifestyleAssessmentScreenData screenData;

  LifestyleAssessmentScreenLoadedState(
      {required this.screenData})
      : super();

  @override
  String toString() => 'LifestyleAssessmentScreenLoadedState';
}

class LifestyleAssessmentScreenErrorState extends LifestyleAssessmentScreenState {
  final ErrorInfo? errorInfo;

  LifestyleAssessmentScreenErrorState([this.errorInfo]) : super();

  @override
  String toString() => 'LifestyleAssessmentScreenErrorState';
}

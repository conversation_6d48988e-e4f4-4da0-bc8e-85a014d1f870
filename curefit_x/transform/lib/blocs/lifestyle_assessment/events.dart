abstract class LifestyleAssessmentScreenEvent {
  LifestyleAssessmentScreenEvent() : super();
}

class LoadLifestyleAssessmentEvent extends LifestyleAssessmentScreenEvent {
  final bool showLoader;
  final String? subCategoryCode;

  LoadLifestyleAssessmentEvent({this.showLoader = false, this.subCategoryCode}) : super();

  @override
  String toString() {
    return "LoadLifestyleAssessmentEvent";
  }
}

class ResetLifestyleAssessmentEvent extends LifestyleAssessmentScreenEvent {
  @override
  String toString() => 'ResetLifestyleAssessmentEvent';
}

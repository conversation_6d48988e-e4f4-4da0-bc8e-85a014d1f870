import 'package:transform/blocs/content/models.dart';

abstract class ContentEvent {
  ContentEvent() : super();
}

class LoadChapterEvent extends ContentEvent {
  final String chapterId;
  final String? courseId;
  final bool? isReviewer;

  LoadChapterEvent({this.courseId, required this.chapterId, this.isReviewer}) : super();

  @override
  String toString() => 'LoadChapterEvent';
}

class UpdateQuestionAnswer extends ContentEvent {
  final String pageId;
  final String? answer;
  final Set<String>? selectedOptionIds;

  UpdateQuestionAnswer(
      {required this.pageId, this.answer, this.selectedOptionIds});
}

class UpdateChapterEvent extends ContentEvent {
  final String chapterId;
  final String courseId;
  UpdateChapterEvent({required this.chapterId, required this.courseId});
}

class LoadCourseEvent extends ContentEvent {
  final String courseId;
  final String? subCategoryCode;

  LoadCourseEvent({required this.courseId, this.subCategoryCode});

  @override
  String toString() => 'LoadCourseEvent';
}

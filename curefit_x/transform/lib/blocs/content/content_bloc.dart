import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/content/models.dart';
import 'package:transform/blocs/content/state.dart';
import 'package:transform/network/content_repository.dart';
import 'events.dart';

class ContentBloc extends Bloc<ContentEvent, ContentState> {
  final ContentRepository repository;

  ContentBloc(ContentState initialState, this.repository)
      : super(initialState) {
    on<LoadCourseEvent>((event, emit) async {
      emit(CourseLoading());
      final response = await this.repository.getCourse(event.courseId, event.subCategoryCode ?? "");
      if (response != null) {
        emit(CourseLoaded(CourseContent(
            widgets: response['widgets'], title: response['title'])));
      } else {
        emit(CourseNotLoaded());
      }
    });
  }
}

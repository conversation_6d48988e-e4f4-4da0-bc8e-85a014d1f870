import 'package:common/action/action_handler.dart';



class ContentProgress {
  final int completedChaptersCount;
  final String title;
  final String subTitle;
  final bool isUnreadChapterPresent;
  final String progressAnimation;
  final String? chaptersFinishedMessage;
  final String? nextHeading;
  final String? nextChapterTitle;
  final String? duration;
  final Action? nextAction;
  final Action closeAction;


  ContentProgress({required this.completedChaptersCount, required this.title, required this.subTitle, required this.isUnreadChapterPresent,
    required this.progressAnimation, this.chaptersFinishedMessage, this.nextHeading, this.nextChapterTitle, this.duration, this.nextAction, required this.closeAction});
  
  factory ContentProgress.fromJSON(dynamic payload) {
    return ContentProgress(
        completedChaptersCount: payload['completedChaptersCount'],
        title: payload['title'],
        subTitle: payload['subTitle'],
        isUnreadChapterPresent: payload['isUnreadChapterPresent'],
        chaptersFinishedMessage: payload['chaptersFinishedMessage'],
        nextHeading: payload['nextHeading'],
        nextChapterTitle: payload['nextChapterTitle'],
        duration: payload['duration'],
        nextAction:  Action.fromJson(payload['nextAction']),
        closeAction: Action.fromJson(payload['closeAction']),
        progressAnimation: payload['progressAnimation']
    );
  }
}

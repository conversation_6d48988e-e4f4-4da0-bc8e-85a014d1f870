import 'package:transform/blocs/content/progress/models.dart';

abstract class ContentProgressState {
  ContentProgressState() : super();
}


class ContentProgressStateIdle extends ContentProgressState {
  @override
  String toString() => 'IdleState';
}

class ContentProgressStateLoading extends ContentProgressState {
  @override
  String toString() => 'ContentProgressStateLoading';
}

class ContentProgressStateLoaded extends ContentProgressState {
  final ContentProgress contentProgressData;

  ContentProgressStateLoaded(this.contentProgressData) : super();
  @override
  String toString() => 'ContentProgressStateLoaded';
}

class ContentProgressStateFailed extends ContentProgressState {
  final String failedMessage;

  ContentProgressStateFailed(this.failedMessage) : super();
  @override
  String toString() => 'ContentProgressStateFailed';
}

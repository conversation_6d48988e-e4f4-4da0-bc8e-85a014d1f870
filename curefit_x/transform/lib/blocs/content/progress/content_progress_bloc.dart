import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/content/progress/events.dart';
import 'package:transform/blocs/content/progress/models.dart';
import 'package:transform/blocs/content/progress/state.dart';
import 'package:transform/network/content_repository.dart';

class ContentProgressBloc
    extends Bloc<ContentProgressEvent, ContentProgressState> {
  final ContentRepository repository;

  ContentProgressBloc(ContentProgressState initialState, this.repository)
      : super(initialState) {
    on<LoadUserContentProgress>((event, emit) async {
      emit(ContentProgressStateLoading());
      try {
        final response = await this.repository.getUserCourseProgress("HABIT101",
            subCategoryCode: event.subCategoryCode);
        if (response != null) {
          emit(ContentProgressStateLoaded(ContentProgress.fromJSON(response)));
        }
      } catch (e) {
        ContentProgressStateFailed("Something went wrong");
      }
    });
  }
}

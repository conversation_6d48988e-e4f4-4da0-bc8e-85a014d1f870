import 'package:common/action/action_handler.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:enum_to_string/enum_to_string.dart';
import '../../factory/content_widget_builder.dart' as ContentWidgetBuilder;

enum CTATheme { LIGHT, DARK }

class ChapterTheme {
  final String? backgroundImageUrl;
  final CTATheme? ctaTheme;

  ChapterTheme({this.backgroundImageUrl, this.ctaTheme});

  factory ChapterTheme.fromJSON(dynamic payload) {
    return ChapterTheme(
        backgroundImageUrl: payload['backgroundImageUrl'],
        ctaTheme: CTATheme.LIGHT);

    // EnumToString.fromString(CTATheme.values, payload['ctaTheme']));
  }
}

class CourseChapter {
  final int? durationInMinutes;
  final String code;
  final String title;
  final ChapterTheme? theme;

  final List<ChapterPage> pages;

  CourseChapter(
      {required this.title,
      required this.pages,
      required this.code,
      this.theme,
      this.durationInMinutes});
}

class ChapterQuestion {
  final String type;
  final String? text;
  final dynamic? options;

  ChapterQuestion({required this.type, this.text, this.options});

  Map<String, dynamic> toMap() {
    return {"type": this.type, "text": this.text, "options": options};
  }
}

class QuestionResponse {
  final String? answer;
  final Set<String>? selectedOptionIds;

  QuestionResponse({this.answer, this.selectedOptionIds});

  QuestionResponse.fromJson(Map<String, dynamic> json)
      : answer = json['answer'],
        selectedOptionIds = json['selectedOptionIds'];

  Map<String, dynamic> toJson() => {
        'answer': answer,
        'selectedOptionIds': selectedOptionIds?.toList(),
      };
}

class ChapterPage {
  final String id;
  final String? nextPageId;
  final String? ctaText;
  final String? backgroundColor;
  final String? backgroundImageURL;
  final ChapterQuestion? question;
  List<dynamic>? _widgets;

  List<dynamic>? get widgets {
    return _widgets;
  }

  set widgets(List<dynamic>? widgetData) {
    if (this.question != null) {
      _widgets = widgetData?.map((e) {
        if (e['type'] != null) {
          ContentWidgetBuilder.WidgetTypes? widgetType =
              EnumToString.fromString(
                  ContentWidgetBuilder.WidgetTypes.values, e['type']);
          if (widgetType == ContentWidgetBuilder.WidgetTypes.QUESTION) {
            dynamic questionWidget = Map.from(e);
            if (questionWidget['data'] == null) questionWidget['data'] = {};
            questionWidget['data'] = {
              ...questionWidget['data'],
              "question": this.question?.toMap(),
              "pageId": this.id
            };
            return questionWidget;
          }
        }
        return e;
      }).toList();
    } else {
      _widgets = widgetData;
    }
  }

  ChapterPage(
      {required this.id,
      this.nextPageId,
      this.ctaText,
      this.question,
      List<dynamic>? widgets,
      this.backgroundColor,
      this.backgroundImageURL}) {
    this.widgets = widgets;
  }
}

enum UserCourseStatus { IN_PROGRESS, COMPLETED }

class Course {
  UserCourseStatus? courseStatus;
  final String courseCode;
  final List<CourseChapter> chapters;

  Course({required this.courseCode, required this.chapters, this.courseStatus});
}

class CourseContent {
  dynamic widgets;
  final String? title;

  int _statusValue(dynamic chapter) {
    if (chapter['status'] == 'ASSIGNED') {
      return 0;
    } else if (chapter['status'] == 'IN_PROGRESS') {
      return 1;
    } else if (chapter['status'] == 'COMPLETED') {
      return chapter['completedEpoch'];
    }

    return -1;
  }

  CourseContent({required this.widgets, this.title}) {
    if (widgets != null && widgets.length > 0) {
      for (var widget in widgets) {
        if (widget['chapters'].length > 1) {
          widget['chapters']
              .sort((a, b) => _statusValue(b).compareTo(_statusValue(a)));
        }
      }
    }
  }
}

enum UserChapterStatus { ASSIGNED, COMPLETED, IN_PROGRESS }

class ChapterTileData {
  final String? title;
  final String? subTitle;
  final String? imageUrl;
  final String? completedDate;
  final bool? isLocked;
  final UserChapterStatus? status;
  final Action? action;

  ChapterTileData(
      {this.title,
      this.subTitle,
      this.imageUrl,
      this.completedDate,
      this.isLocked,
      this.status,
      this.action});
}

class ChapterSectionWidgetData implements IWidgetData {
  String? title;
  bool? current;
  List<ChapterTileData>? chapters;

  ChapterSectionWidgetData({required this.widgetType, this.widgetInfo});

  factory ChapterSectionWidgetData.fromJson(
      dynamic payload, WidgetTypes widgetType, WidgetInfo widgetInfo) {
    ChapterSectionWidgetData chapterSectionWidgetData =
      ChapterSectionWidgetData(widgetType: widgetType, widgetInfo: widgetInfo);
    chapterSectionWidgetData.title = payload["title"];
    chapterSectionWidgetData.current = payload["current"];
    if (payload["chapters"] != null) {
      chapterSectionWidgetData.chapters =
          payload["chapters"].map<ChapterTileData>((chapter) {
        return ChapterTileData(
            title: chapter["title"],
            subTitle: chapter["subTitle"],
            imageUrl: chapter["imageUrl"],
            completedDate: chapter["completedDate"],
            isLocked: chapter["isLocked"],
            status: EnumToString.fromString(
                UserChapterStatus.values, chapter["status"]),
            action: Action.fromJson(chapter["action"]));
      }).toList();
    }
    return chapterSectionWidgetData;
  }

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;
}

import 'package:common/action/action_handler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/content/models.dart';
import 'package:transform/blocs/content/state.dart';
import 'package:transform/network/content_repository.dart';
import 'events.dart';
import 'package:enum_to_string/enum_to_string.dart';

class ChapterBloc extends Bloc<ContentEvent, ContentState> {
  final ContentRepository repository;
  Map<String, QuestionResponse> currentChapterResponse = {};

  ChapterBloc(ContentState initialState, this.repository)
      : super(initialState) {
    on<LoadChapterEvent>((event, emit) async {
      await _mapLoadChapterToState(event, emit);
    });

    on<UpdateQuestionAnswer>((event, emit) async {
      QuestionResponse questionResponse = QuestionResponse(
          answer: event.answer, selectedOptionIds: event.selectedOptionIds);
      currentChapterResponse[event.pageId] = questionResponse;
    });

    on<UpdateChapterEvent>((event, emit) async {
      emit(ChapterCompleteLoading());
      final response = await this.repository.updateChapter(
          event.chapterId, event.courseId, currentChapterResponse);
      if (response != null) {
        emit(ChapterCompleted(
            chapterId: response['chapterCode'],
            status: EnumToString.fromString(
                UserChapterStatus.values, response['status'])));
      }
    });
  }

  Future<void> _mapLoadChapterToState(
      LoadChapterEvent event, Emitter<ContentState> emit) async {
    emit(ChapterLoading());
    var response = null;
    try {
      if (event.isReviewer == true) {
        response = await this.repository.getChapterForReview(event.chapterId);
      } else {
        response =
            await this.repository.getChapter(event.chapterId, event.courseId!);
      }

      if (response != null && response['chapter'] != null) {
        currentChapterResponse = {};
        final chapterData = response['chapter'];
        CourseChapter courseChapter = CourseChapter(
            title: chapterData['title'],
            theme: chapterData['theme'] != null
                ? ChapterTheme.fromJSON(chapterData['theme'])
                : null,
            pages: chapterData['pages'] != null
                ? chapterData['pages']
                    .map<ChapterPage>((e) => ChapterPage(
                        ctaText: e['ctaText'],
                        nextPageId: e['nextPageId'].toString(),
                        question: e['question'] != null
                            ? ChapterQuestion(
                                type: e['question']['type'],
                                text: e['question']['text'],
                                options: e['question']['options'])
                            : null,
                        id: e['id'].toString(),
                        widgets: e['widgets'],
                        backgroundColor: e['backgroundColor'],
                        backgroundImageURL: e['backgroundImageURL']))
                    .toList()
                : [],
            code: chapterData['code']);
        emit(ChapterLoaded(
            chapter: courseChapter,
            resetAction: response['resetAction'] != null
                ? Action.fromAction(
                    Action.fromJson(response['resetAction']), {}, canPop: true)
                : null,
            completionAction: response['completionAction'] != null
                ? Action.fromJson(response['completionAction'])
                : null));
      } else {
        emit(ChapterNotLoaded());
      }
    } catch (e) {
      emit(ChapterNotLoaded());
    }
  }
}

import 'package:flutter/material.dart';
import 'package:transform/blocs/content/models.dart';
import 'package:common/action/action_handler.dart' as ActionHandler;

@immutable
abstract class ContentState {
  ContentState() : super();
}

class ChapterStateIdle extends ContentState {
  @override
  String toString() => 'IdleState';
}

class ChapterLoading extends ContentState {
  @override
  String toString() => 'ChapterLoading';
}

class ChapterLoaded extends ContentState {
  final CourseChapter chapter;
  final ActionHandler.Action? completionAction;
  final ActionHandler.Action? resetAction;

  ChapterLoaded({required this.chapter, this.completionAction, this.resetAction}) : super();
  @override
  String toString() => 'ChapterLoaded';
}

class ChapterCompleteLoading extends ContentState {
  ChapterCompleteLoading() : super();
  @override
  String toString() => 'ChapterCompleteLoading';
}

class ChapterCompleted extends ContentState {
  final String chapterId;
  final UserChapterStatus? status;

  ChapterCompleted({required this.chapterId, this.status}) : super();
  @override
  String toString() => 'ChapterCompleted';
}

class ChapterNotLoaded extends ContentState {
  final Object? error;

  ChapterNotLoaded([this.error]) : super();

  @override
  String toString() => 'ChapterNotLoaded';
}

class CourseStateIdle extends ContentState {
  @override
  String toString() => 'CourseStateIdle';
}

class CourseLoading extends ContentState {
  @override
  String toString() => 'CourseLoading';
}

class CourseLoaded extends ContentState {
  final CourseContent courseContent;

  CourseLoaded(this.courseContent);

  @override
  String toString() => 'CourseLoaded';
}

class CourseNotLoaded extends ContentState {
  final Object? error;

  CourseNotLoaded([this.error]) : super();

  @override
  String toString() => 'CourseNotLoaded';
}

import 'package:flutter/material.dart';

@immutable
abstract class WaitlistState {
  WaitlistState() : super();
}

class WaitlistIdleState extends WaitlistState {
  @override
  String toString() => 'WaitlistIdleState';
}

class WaitlistLoading extends WaitlistState {
  @override
  String toString() => 'WaitlistLoading';
}

class WaitlistSuccess extends WaitlistState {
  @override
  String toString() => 'WaitlistSuccess';
}


class WaitlistFailed extends WaitlistState {
  @override
  String toString() => 'WaitlistFailed';
}

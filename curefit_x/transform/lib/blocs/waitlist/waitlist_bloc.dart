import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/waitlist/events.dart';
import 'package:transform/blocs/waitlist/state.dart';
import 'package:transform/network/waitlist_repository.dart';

class WaitlistBloc extends Bloc<WaitlistEvent, WaitlistState> {
  final WaitlistRepository repository;

  WaitlistBloc({required this.repository}) : super(WaitlistIdleState()) {
    on<WaitlistRegisterEvent>((event, emit) async {
      await _mapWaitlistToState(event, emit);
    });

    on<PreRegisterEvent>((event, emit) async {
      emit(WaitlistIdleState());
    });
  }

  Future<void> _mapWaitlistToState(
      WaitlistEvent event, Emitter<WaitlistState> emit) async {
    try {
      emit(WaitlistLoading());
      final response = await this.repository.updateWaitlist();
      if (response != null && response['success'] != null) {
        emit(WaitlistSuccess());
      } else {
        emit(WaitlistFailed());
      }
    } catch (e) {
      emit(WaitlistFailed());
    }
  }
}

abstract class AddressFormScreenEvent {
  AddressFormScreenEvent() : super();
}

class LoadAddressFormScreenEvent extends AddressFormScreenEvent {
  final bool showLoader;
  final String? subCategoryCode;

  LoadAddressFormScreenEvent({this.showLoader = false, this.subCategoryCode}) : super();

  @override
  String toString() {
    return "LoadAddressFormScreenEvent";
  }
}

class SaveAddressScreenEvent extends AddressFormScreenEvent {
  final Map<String,dynamic> addressFields;

  SaveAddressScreenEvent({required this.addressFields}) : super();

  @override
  String toString() {
    return "SaveAddressScreenEvent";
  }
}

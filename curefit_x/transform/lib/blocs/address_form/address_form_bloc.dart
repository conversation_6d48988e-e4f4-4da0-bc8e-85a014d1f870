import 'dart:io';

import 'package:bloc/bloc.dart';
import 'package:common/model/error_prop_model.dart';
import 'package:common/network/client.dart';
import 'package:transform/blocs/address_form/events.dart';
import 'package:transform/blocs/address_form/models.dart';
import 'package:transform/blocs/address_form/state.dart';
import 'package:transform/network/address_repository.dart';

class AddressFormScreenBloc
    extends Bloc<AddressFormScreenEvent, AddressFormScreenState> {
  final AddressRepository repository;
  AddressFormScreenData screenData = AddressFormScreenData();

  AddressFormScreenBloc(this.repository) : super(AddressFormScreenIdleState()) {
    on<LoadAddressFormScreenEvent>((event, emit) async {
      await _mapLoadAddressFormScreen(event, emit);
    });
    on<SaveAddressScreenEvent>((event, emit) async {
      await _mapSaveAddressFields(event, emit);
    });
  }

  void refresh() {
    this.add(LoadAddressFormScreenEvent());
  }

  Future<void> _mapLoadAddressFormScreen(LoadAddressFormScreenEvent event,
      Emitter<AddressFormScreenState> emit) async {
    emit(AddressFormScreenLoadingState(
        screenData: screenData, showLoader: event.showLoader));
    try {
      final response = await this
          .repository
          .getAddressPageData({"subCategoryCode": event.subCategoryCode ?? ""});
      if (response != null) {
        screenData = AddressFormScreenData.fromJson(response);
        emit(AddressFormScreenLoadedState(screenData: screenData));
      } else {
        emit(AddressFormScreenErrorState(UnknownError(
            failed: true, responseCode: "404", callbackFunction: refresh)));
      }
    } on NetworkException catch (exception) {
      emit(AddressFormScreenErrorState(NetworkError(
          failed: true,
          responseCode: '${(exception).statusCode}',
          callbackFunction: refresh)));
    } on SocketException {
      emit(AddressFormScreenErrorState(
          NoInternetError(failed: true, callbackFunction: refresh)));
    } catch (e) {
      emit(AddressFormScreenErrorState(
          UnknownError(failed: true, callbackFunction: refresh)));
    }
  }

  Future<void> _mapSaveAddressFields(SaveAddressScreenEvent event,
      Emitter<AddressFormScreenState> emit) async {
    try {
      await this.repository.addAddress(event.addressFields);
      emit(AddressAddedSuccessfullyState());
    } on NetworkException catch (exception) {
      emit(AddressFormScreenErrorState(NetworkError(
          failed: true,
          responseCode: '${(exception).statusCode}',
          callbackFunction: refresh)));
    } on SocketException {
      emit(AddressFormScreenErrorState(
          NoInternetError(failed: true, callbackFunction: refresh)));
    } catch (e) {
      emit(AddressFormScreenErrorState(
          UnknownError(failed: true, responseCode: "500", callbackFunction: refresh)));
    }
  }
}

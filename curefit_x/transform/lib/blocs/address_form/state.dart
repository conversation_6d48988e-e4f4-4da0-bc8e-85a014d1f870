import 'package:common/model/error_prop_model.dart';
import 'models.dart';

abstract class AddressFormScreenState {
  AddressFormScreenState() : super();
}

class AddressFormScreenIdleState extends AddressFormScreenState {
  @override
  String toString() => 'AddressFormScreenIdleState';
}

class AddressFormScreenLoadingState extends AddressFormScreenState {
  final AddressFormScreenData? screenData;
  final bool showLoader;

  AddressFormScreenLoadingState({this.screenData, this.showLoader = true});

  @override
  String toString() => 'AddressFormScreenLoadingState';
}

class AddressFormScreenLoadedState extends AddressFormScreenState {
  final AddressFormScreenData screenData;

  AddressFormScreenLoadedState(
      {required this.screenData})
      : super();

  @override
  String toString() => 'AddressFormScreenLoadedState';
}

class AddressFormScreenErrorState extends AddressFormScreenState {
  final ErrorInfo? errorInfo;

  AddressFormScreenErrorState([this.errorInfo]) : super();

  @override
  String toString() => 'AddressFormScreenErrorState';
}

class AddressAddedSuccessfullyState extends AddressFormScreenState {
  @override
  String toString() => 'AddressAddedSuccessfullyState';
}

import 'package:common/action/action_handler.dart';
import 'package:common/ui/atoms/input_fields/input_field.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:common/ui/aurora.dart';

class AddressFormScreenData {
  AddressFormScreenData({
    this.formFields,
    this.pageTitle,
    this.backAction,
    this.action,
    this.themeType,
  });

  final String? pageTitle;
  final List<FormFields>? formFields;
  final Action? backAction;
  final Action? action;
  final CanvasTheme? themeType;

  static AddressFormScreenData fromJson(json) {
    return AddressFormScreenData(
      pageTitle: json['pageTitle'],
      formFields: json['formFields'] != null
          ? json['formFields'].map<FormFields>((field) {
              return FormFields.fromJson(field);
            }).toList()
          : null,
      action: json['action'] != null ? Action.fromJson(json['action']) : null,
      backAction: json['backAction'] != null
          ? Action.fromJson(json['backAction'])
          : null,
      themeType: json['themeType'] != null
          ? EnumToString.fromString(CanvasTheme.values, json['themeType'])
          : null,
    );
  }
}

class FormFields {
  FormFields({
    this.id,
    this.otherId,
    this.enabled = true,
    this.expanded = true,
    this.placeholder,
    this.errorMessage,
    this.nested,
    this.mandatory = true,
    this.values,
    this.type = InputType.TEXT,
  });

  final String? id;
  final String? otherId;
  final bool enabled;
  final bool expanded;
  final String? placeholder;
  final String? errorMessage;
  final String? nested;
  final List<String>? values;
  final bool mandatory;
  final InputType type;

  static FormFields fromJson(json) {
    return FormFields(
      id: json['id'],
      otherId: json['otherId'],
      enabled: json['enabled'] ?? true,
      expanded: json['expanded'] ?? true,
      placeholder: json['placeholder'],
      errorMessage: json['errorMessage'],
      nested: json['nested'],
      values: json['values'] != null
          ? json['values'].map<String>((value) {
              return value.toString();
            }).toList()
          : null,
      mandatory: json['mandatory'] ?? true,
      type: json['type'] != null
          ? EnumToString.fromString(InputType.values, json['type']) ??
              InputType.TEXT
          : InputType.TEXT,
    );
  }
}

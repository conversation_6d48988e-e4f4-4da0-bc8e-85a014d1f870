import 'models.dart';

abstract class PackDetailsState {
  PackDetailsState() : super();
}

class PackDetailsIdleState extends PackDetailsState {
  @override
  String toString() => 'PackDetailsIdleState';
}

class PackDetailsLoadingState extends PackDetailsState {
  @override
  String toString() => 'PackDetailsLoadingState';
}

class PackDetailsLoadedState extends PackDetailsState {
  final PackDetailsScreenData? packDetailsScreenData;

  PackDetailsLoadedState({this.packDetailsScreenData})
      : super();

  @override
  String toString() => 'PackDetailsLoadedState';
}

class PackDetailsFailedState extends PackDetailsState {
  @override
  String toString() => 'PackDetailsFailedState';
}

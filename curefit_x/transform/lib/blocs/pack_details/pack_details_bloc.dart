import 'package:common/network/client.dart';
import 'package:common/network/client_repository.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/pack_details/events.dart';
import 'package:transform/blocs/pack_details/models.dart';
import 'package:transform/blocs/pack_details/state.dart';

class PackDetailsBloc extends Bloc<PackDetailsEvent, PackDetailsState> {
  final CoachClientRepository repository;

  PackDetailsBloc(this.repository) : super(PackDetailsIdleState()) {
    on<PackDetailsLoadEvent>((event, emit) async {
      await _mapPackDetailsEventToState(event, emit);
    });
  }

  Future<void> _mapPackDetailsEventToState(
      PackDetailsLoadEvent event, Emitter<PackDetailsState> emit) async {
    try {
      emit(PackDetailsLoadingState());
      final response = await this.repository.getPageData(
          'v2/transform/packDetails',
          {"subCategoryCode": event.subCategoryCode ?? ""});
      if (response != null) {
        emit(
          PackDetailsLoadedState(
              packDetailsScreenData: PackDetailsScreenData.fromJson(response)),
        );
      } else {
        emit(PackDetailsFailedState());
      }
    } on NetworkException catch (exception) {
      emit(PackDetailsFailedState());
    } catch (e) {
      emit(PackDetailsFailedState());
    }
  }
}

import 'package:common/action/action_handler.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/header/card_header.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:enum_to_string/enum_to_string.dart';

class PackDetailsScreenData {
  PackDetailsScreenData(
      {this.title, this.themeType, this.widgets, this.action});

  final String? title;
  final List<dynamic>? widgets;
  final CanvasTheme? themeType;
  final Action? action;

  static PackDetailsScreenData fromJson(widgetData) {
    return PackDetailsScreenData(
      title: widgetData["title"],
      widgets: widgetData['widgets'],
      themeType: widgetData['themeType'] != null
          ? EnumToString.fromString(CanvasTheme.values, widgetData['themeType'])
          : null,
      action: widgetData['action'] != null
          ? Action.fromJson(widgetData['action'])
          : null,
    );
  }
}

class PackDetailsWidgetData implements IWidgetData {
  PackDetailsWidgetData(
    this.widgetType, {
    this.packDetailsCards,
    this.widgetHeaderData,
    this.widgetInfo,
  }) : super();

  final List<PackDetailItem>? packDetailsCards;
  final WidgetHeaderData? widgetHeaderData;

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  factory PackDetailsWidgetData.fromJson(
      WidgetTypes widgetType, dynamic widgetData, WidgetInfo? widgetInfo) {
    return PackDetailsWidgetData(
      widgetType,
      packDetailsCards: widgetData['packDetailsItems'] != null
          ? widgetData['packDetailsItems']
              .map<PackDetailItem>(
                  (detailCard) => PackDetailItem.fromJson(detailCard))
              .toList()
          : null,
      widgetHeaderData: widgetData['header'] != null
          ? WidgetHeaderData.fromJson(widgetData['header'])
          : null,
      widgetInfo: widgetInfo,
    );
  }
}

class PackDetailItem {
  String? title;
  List<String>? points;

  PackDetailItem({
    this.title,
    this.points,
  });

  static PackDetailItem fromJson(dynamic json) {
    return PackDetailItem(
      title: json['title'],
      points: json['points'] != null
          ? json['points'].map<String>((point) => point.toString()).toList()
          : null,
    );
  }
}

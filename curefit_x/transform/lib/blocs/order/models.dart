import 'package:common/action/action_handler.dart' as ActionHandler;
import 'package:common/action/action_handler.dart';
import 'package:common/blocs/reminder_slots/reminder_slots_models.dart';
import 'package:common/ui/atoms/action_card.dart';
import 'package:common/ui/organisms/address_accordion.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:enum_to_string/enum_to_string.dart';

class TransformCoachInfo {
  String? title;
  String? name;
  String? experience;
  String? imageUrl;
  Action? action;
  TransformCoachInfo(
      {this.title, this.name, this.experience, this.action, this.imageUrl});
}

class TransformConfirmationWidgetData implements IWidgetData {
  final String? title;
  final String? subTitle;
  String? companyLogo;
  String? companyName;
  final TransformCoachInfo? coachInfo;

  @override
  WidgetTypes widgetType;

  @override
  WidgetInfo? widgetInfo;

  TransformConfirmationWidgetData(this.widgetType,
      {this.title,
      this.subTitle,
      this.coachInfo,
      this.companyLogo,
      this.companyName});

  static TransformConfirmationWidgetData fromJson(
    widget,
    WidgetTypes widgetType,
  ) {
    return TransformConfirmationWidgetData(widgetType,
        title: widget['title'],
        subTitle: widget['subTitle'],
        companyLogo: widget['companyLogo'],
        companyName: widget['companyName'],
        coachInfo: widget['coachInfo'] != null
            ? TransformCoachInfo(
                title: widget['coachInfo']["title"],
                imageUrl: widget['coachInfo']["imageUrl"],
                name: widget['coachInfo']["name"],
                experience: widget['coachInfo']["experience"],
                action: Action.fromJson(widget['coachInfo']["action"]))
            : null);
  }
}

class OrderConfirmationInfoData implements IWidgetData {
  OrderConfirmationInfoData(
      this.widgetType, this.title, this.subtitle, this.icon,
      {this.textInfoItems,
      this.footer,
      this.addressAccordionItemList,
      this.address,
      this.isWaitlisted});

  @override
  WidgetTypes widgetType;

  @override
  WidgetInfo? widgetInfo;

  final String? title;
  final String? subtitle;
  final String? icon;
  final List<TextInfoItem>? textInfoItems;
  final Footer? footer;
  final String? address;
  final List<AddressAccordionItem>? addressAccordionItemList;
  final bool? isWaitlisted;

  static OrderConfirmationInfoData fromJson(widget, WidgetTypes widgetType) {
    final dropDownInfo = widget['orderMeta']?['dropDownInfo'];
    final List<AddressAccordionItem> addressAccordionList = [];
    if (dropDownInfo != null) {
      for (int i = 0; i < dropDownInfo.length; i++) {
        var data = dropDownInfo[i];
        addressAccordionList.add(AddressAccordionItem(
            icon: data['icon'],
            title: data['title'],
            subtitle: data['subTitle'],
            action: data['cardAction'] != null
                ? ActionHandler.Action.fromJson(data['cardAction'])
                : null));
      }
    }
    var isWaitlisted = false;
    return OrderConfirmationInfoData(
        widgetType, widget['title'], widget['subtitle'], widget['icon'],
        textInfoItems: widget['textInfoItems']?.map<TextInfoItem>((e) {
          if (e['tag']?['value'] != null) isWaitlisted = true;
          return TextInfoItem(
              title: e['title'],
              tag: e['tag']?['value'].toString(),
              color: e['styleProps']?['color'],
              textStyleType: EnumToString.fromString(
                  TextInfoItemStyleType.values, e['styleProps']['variant']));
        }).toList(),
        footer: widget['footer'] != null
            ? Footer(
                icon: widget['footer']['icon'],
                title: widget['footer']['title'])
            : null,
        address: widget['orderMeta']?['title'],
        isWaitlisted: isWaitlisted,
        addressAccordionItemList: addressAccordionList);
  }
}

class NoteListProps {
  NoteListProps(this.title, this.color);

  final String title;
  final String color;
}

class NoteListItem {
  NoteListItem(this.icon, this.info, {this.title, this.description});

  String icon;
  String info;
  String? title;
  String? description;
}

class NoteListWidgetData implements IWidgetData {
  NoteListWidgetData(this.widgetType, this.note, this.data);

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  final NoteListProps note;
  final List<NoteListItem> data;

  static NoteListWidgetData fromJson(widget, WidgetTypes widgetType) {
    return NoteListWidgetData(
        widgetType,
        NoteListProps(widget['title'], widget['color']),
        widget['data']
            .map<NoteListItem>((e) => NoteListItem(e['icon'], e['info'],
                title: e['title'], description: e['description']))
            .toList());
  }
}

class Option {
  final String title;
  final String? subtitle;
  final String id;

  Option(this.title, this.id, {this.subtitle});
}

enum CoachQuestionId { GENDER, GOAL }

class CoachQuestion {
  final String? id;
  final String? title;
  final String? subtitle;
  final String? type;
  final List<Option> options;
  CoachQuestion(
      {required this.options, this.title, this.subtitle, this.id, this.type});
}

class CoachPreferenceWidgetData implements IWidgetData {
  CoachPreferenceWidgetData(this.widgetType,
      {required this.questions,
      required this.bookingId,
      this.defaultOptionId,
      this.subCategoryCode});

  @override
  WidgetTypes widgetType;

  @override
  WidgetInfo? widgetInfo;

  final String bookingId;
  final List<CoachQuestion> questions;
  final String? defaultOptionId;
  final String? subCategoryCode;

  factory CoachPreferenceWidgetData.fromJson(json, WidgetTypes widgetType) {
    return CoachPreferenceWidgetData(widgetType,
        bookingId: json['bookingId'],
        defaultOptionId: json['defaultOptionId'],
        subCategoryCode: json['subCategoryCode'],
        questions: json['questions']
            .map<CoachQuestion>((e) => CoachQuestion(
                title: e["title"],
                subtitle: e["subtitle"],
                id: e['id'],
                type: e['type'],
                options: e['options']
                    .map<Option>((option) => Option(
                        option['title'], option['id'],
                        subtitle: option['subtitle']))
                    .toList()))
            .toList());
  }
}

class CongoWidgetData implements IWidgetData {
  CongoWidgetData(this.widgetType,
      {required this.title, required this.subTitle, required this.action});

  @override
  WidgetTypes widgetType;

  @override
  WidgetInfo? widgetInfo;

  final String title;
  final String subTitle;
  final Action? action;

  factory CongoWidgetData.fromJson(json, WidgetTypes widgetType) {
    return CongoWidgetData(
      widgetType,
      title: json['title'],
      subTitle: json['subTitle'],
      action: json['action'] != null ? Action.fromJson(json['action']) : null,
    );
  }
}

class CareTCConfirmInfoWidgetData implements IWidgetData {
  @override
  WidgetTypes widgetType;

  @override
  WidgetInfo? widgetInfo;

  final String? imageUrl;

  final List<TextInfoItem>? textInfoItems;

  final String? coachImageUrl;

  final String? coachName;

  final String? coachDescription;

  CareTCConfirmInfoWidgetData(this.widgetType,
      {this.imageUrl,
      this.textInfoItems,
      this.coachImageUrl,
      this.coachName,
      this.coachDescription});

  factory CareTCConfirmInfoWidgetData.fromJson(json, WidgetTypes widgetType) {
    return CareTCConfirmInfoWidgetData(widgetType,
        imageUrl: json['imageUrl'],
        coachImageUrl: json['coachImageUrl'],
        coachName: json['coachName'],
        coachDescription: json['coachDescription'],
        textInfoItems: json['textInfoItems']
            .map<TextInfoItem>((e) => TextInfoItem(
                title: e['title'],
                textStyleType: EnumToString.fromString(
                    TextInfoItemStyleType.values, e['textStyleType'])))
            .toList());
  }
}

enum TextInfoItemStyleType {
  BOLD,
  BOLD_GRAY,
  REGULAR,
  REGULAR_GRAY,
  MEDIUM,
  MEDIUM_GRAY,
  paragraph4Text,
  header1Text,
  paragraph2Text
}

class TextInfoItem {
  final String? title;
  final TextInfoItemStyleType? textStyleType;
  final String? tag;
  final String? color;

  TextInfoItem({this.title, this.textStyleType, this.tag, this.color});
}

class Footer {
  final String? title;
  final String? icon;

  Footer({this.title, this.icon});
}

class OrderConfirmationAddOnsData implements IWidgetData {
  OrderConfirmationAddOnsData(this.widgetType,
      {required this.actionCardList, this.reminderSlotsScreenArguments});

  @override
  WidgetTypes widgetType;

  @override
  WidgetInfo? widgetInfo;

  final List<ActionCardData> actionCardList;
  final ReminderSlotsScreenArguments? reminderSlotsScreenArguments;

  static OrderConfirmationAddOnsData fromJson(widget, WidgetTypes widgetType) {
    final List<ActionCardData> actionCardList = [];
    if (widget['cards'] != null) {
      for (var card in widget['cards']) {
        actionCardList.add(ActionCardData(
            title: card['title'],
            leftIcon: card['leftIconName'],
            rightIcon: card['rightIconName']));
      }
    }

    ReminderSlotsScreenArguments? reminderSlotsScreenArguments;
    if (widget['slots'] != null) {
      String title = widget['slots']['title'];
      String checkBoxTitle = widget['slots']['checkBoxTitle'];
      String selectedCallTime = widget['selectedCallTime'];
      String bookingNumber = widget['bookingNumber'];
      List<String> callTimeBeforeList = [];
      List<String> callTimeKeys = [];
      List<String> callTimeList = [];
      List<int> minsBeforeList = [];
      List<bool> selectedList = [];
      for (var slotTimes in widget['slots']['slotTimes']) {
        callTimeBeforeList.add(slotTimes['callTimeBefore'] ?? '');
        callTimeKeys.add(slotTimes['key'] ?? '');
        callTimeList.add(slotTimes['callTime'] ?? '');
        minsBeforeList.add(slotTimes['minutesBefore'] ?? -1);
        selectedList.add(slotTimes['selected'] ?? false);
      }
      reminderSlotsScreenArguments = ReminderSlotsScreenArguments(
        title: title,
        checkBoxTitle: checkBoxTitle,
        callTimeList: callTimeList,
        callTimeBeforeList: callTimeBeforeList,
        callTimeKeys: callTimeKeys,
        selectedCallTime: selectedCallTime,
        bookingNumber: bookingNumber,
        minsBeforeList: minsBeforeList,
        selectedList: selectedList,
      );
    }

    return OrderConfirmationAddOnsData(widgetType,
        actionCardList: actionCardList,
        reminderSlotsScreenArguments: reminderSlotsScreenArguments);
  }
}

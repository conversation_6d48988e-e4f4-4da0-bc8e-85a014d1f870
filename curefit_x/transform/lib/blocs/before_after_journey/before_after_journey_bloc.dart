import 'dart:io';

import 'package:bloc/bloc.dart';
import 'package:common/model/error_prop_model.dart';
import 'package:common/network/client.dart';
import 'package:common/network/client_repository.dart';
import 'package:transform/blocs/before_after_journey/events.dart';
import 'package:transform/blocs/before_after_journey/models.dart';
import 'package:transform/blocs/before_after_journey/state.dart';

class BeforeAfterJourneyClpBloc
    extends Bloc<BeforeAfterJourneyClpEvent, BeforeAfterJourneyClpState> {
  final CoachClientRepository repository;
  BeforeAfterJourneyClpData screenData = BeforeAfterJourneyClpData(widgets: []);

  BeforeAfterJourneyClpBloc(this.repository)
      : super(BeforeAfterJourneyClpIdleState()) {
    on<LoadBeforeAfterJourneyClpEvent>((event, emit) async {
      await _mapLoadBeforeAfterJourneyClp(event, emit);
    });
  }

  void refresh() {
    this.add(LoadBeforeAfterJourneyClpEvent());
  }

  Future<void> _mapLoadBeforeAfterJourneyClp(
      LoadBeforeAfterJourneyClpEvent event,
      Emitter<BeforeAfterJourneyClpState> emit) async {
    emit(BeforeAfterJourneyClpLoadingState(
        screenData: screenData, showLoader: event.showLoader));
    try {
      final response = await this.repository.getPageData(
          'v2/transform/beforeAfterJourney',
          {"subCategoryCode": event.subCategoryCode ?? ""});
      if (response != null) {
        screenData = BeforeAfterJourneyClpData.fromJson(response);
        emit(BeforeAfterJourneyClpLoadedState(screenData: screenData));
      } else {
        emit(BeforeAfterJourneyClpErrorState(UnknownError(
            failed: true, responseCode: "404", callbackFunction: refresh)));
      }
    } on NetworkException catch (exception) {
      emit(BeforeAfterJourneyClpErrorState(NetworkError(
          failed: true,
          responseCode: '${(exception).statusCode}',
          callbackFunction: refresh)));
    } on SocketException {
      emit(BeforeAfterJourneyClpErrorState(
          NoInternetError(failed: true, callbackFunction: refresh)));
    } catch (e) {
      emit(BeforeAfterJourneyClpErrorState(
          UnknownError(failed: true, callbackFunction: refresh)));
    }
  }
}

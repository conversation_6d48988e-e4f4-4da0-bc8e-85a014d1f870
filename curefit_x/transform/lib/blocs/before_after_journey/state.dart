import 'package:common/model/error_prop_model.dart';
import 'models.dart';

abstract class BeforeAfterJourneyClpState {
  BeforeAfterJourneyClpState() : super();
}

class BeforeAfterJourneyClpIdleState extends BeforeAfterJourneyClpState {
  @override
  String toString() => 'BeforeAfterJourneyClpIdleState';
}

class BeforeAfterJourneyClpLoadingState extends BeforeAfterJourneyClpState {
  final BeforeAfterJourneyClpData? screenData;
  final bool showLoader;

  BeforeAfterJourneyClpLoadingState({this.screenData, this.showLoader = true});

  @override
  String toString() => 'BeforeAfterJourneyClpLoadingState';
}

class BeforeAfterJourneyClpLoadedState extends BeforeAfterJourneyClpState {
  final BeforeAfterJourneyClpData screenData;

  BeforeAfterJourneyClpLoadedState(
      {required this.screenData})
      : super();

  @override
  String toString() => 'BeforeAfterJourneyClpLoadedState';
}

class BeforeAfterJourneyClpErrorState extends BeforeAfterJourneyClpState {
  final ErrorInfo? errorInfo;

  BeforeAfterJourneyClpErrorState([this.errorInfo]) : super();

  @override
  String toString() => 'BeforeAfterJourneyClpErrorState';
}

import 'package:common/action/action_handler.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:common/ui/aurora.dart';

class BeforeAfterJourneyClpData {
  BeforeAfterJourneyClpData({
    required this.widgets,
    this.title,
    this.rightActionButton,
    this.themeType,
  });

  final String? title;
  final List<dynamic>? widgets;
  final Action? rightActionButton;
  final CanvasTheme? themeType;

  static BeforeAfterJourneyClpData fromJson(json) {
    return BeforeAfterJourneyClpData(
      title: json['title'],
      widgets: json['widgets'],
      rightActionButton: json['rightBarButton'] != null
          ? Action.fromJson(json['rightBarButton'])
          : null,
      themeType: json['themeType'] != null
          ? EnumToString.fromString(CanvasTheme.values, json['themeType'])
          : null,
    );
  }
}

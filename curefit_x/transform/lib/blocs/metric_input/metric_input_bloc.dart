import 'package:common/network/client.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/metric_input/events.dart';
import 'package:transform/blocs/metric_input/state.dart';
import 'package:transform/blocs/metric_input/models.dart';
import 'package:common/network/client_repository.dart';

class MetricInputBloc extends Bloc<MetricInputEvent, MetricInputState> {
  final CoachClientRepository repository;

  MetricInputBloc(MetricInputState initialState, this.repository)
      : super(initialState) {
    on<LoadMetricInputEvent>((event, emit) async {
      await _mapMetricInputToState(event, emit);
    });
  }

  Future<void> _mapMetricInputToState(
      LoadMetricInputEvent event, Emitter<MetricInputState> emit) async {
    try {
      emit(MetricInputLoading());
      final response =
          await this.repository.getPageData('v2/transform/userMetricInput', {"subCategoryCode": event.subCategoryCode ?? ""});
      if (response == null) {
        emit(MetricInputNotLoaded(throw new Error()));
      }
      final detail = response;
      emit(MetricInputLoaded(
          userMetricScreen:
              UserMetricScreen(detail['pageTitle'], detail['widgets'])));
    } on NetworkException catch (exception) {
      emit(MetricInputNotLoaded(exception.subTitle));
    } catch (e) {
      emit(MetricInputNotLoaded("Something went wrong"));
    }
  }
}

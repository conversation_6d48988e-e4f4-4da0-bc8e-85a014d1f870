import 'package:meta/meta.dart';
import 'package:transform/blocs/metric_input/models.dart';

@immutable
abstract class MetricInputState {
  MetricInputState() : super();
}

class MetricInputIdle extends MetricInputState {
  @override
  String toString() => 'IdleState';
}

class MetricInputLoading extends MetricInputState {
  @override
  String toString() => 'MetricInputLoading';
}

class MetricInputLoaded extends MetricInputState {
  final UserMetricScreen userMetricScreen;

  MetricInputLoaded({required this.userMetricScreen})
      : super();

  @override
  String toString() => 'MetricInputLoaded';
}

class MetricInputNotLoaded extends MetricInputState {
  final String? error;

  MetricInputNotLoaded([this.error]) : super();

  @override
  String toString() => 'MetricInputNotLoaded';
}

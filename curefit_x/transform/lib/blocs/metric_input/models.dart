import 'package:common/action/action_handler.dart';
import 'package:common/ui/widget_builder.dart';

class UserMetricScreen {
  UserMetricScreen(this.title, this.widgets);

  List widgets;
  String? title;
}

class IWidget {
  IWidget(this.widgetType);

  String widgetType;
}

class MeasurementInfo {
  MeasurementInfo(this.title, this.image, this.text);

  @override
  WidgetInfo? widgetInfo;

  static MeasurementInfo? fromJson(data) {
    if (data == null) return null;
    return MeasurementInfo(data['title'], data['image'], data['text']);
  }

  final String title;
  final String text;
  final String image;
}

class MeasurementInputCard {
  MeasurementInputCard(this.title, this.value, this.precision, this.unit,
      this.metricId, this.info);

  final String title;
  final double value;
  final int precision;
  final String unit;
  final int metricId;
  final MeasurementInfo? info;
}

class UserMetricInputWidgetData implements IWidgetData {
  UserMetricInputWidgetData(this.widgetType, this.metricCards,
      {this.widgetInfo,
      this.metricIdToCompare,
      this.currentMetricIdValue,
      this.action,
      this.confirmationAction,
      this.subCategoryCode,
      this.referralModalEnabled = false});

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  final List<MeasurementInputCard> metricCards;
  final bool referralModalEnabled;
  final num? metricIdToCompare;
  final num? currentMetricIdValue;
  final String? subCategoryCode;
  final Action? action;
  final Action? confirmationAction;

  static UserMetricInputWidgetData fromJson(
      widget, WidgetTypes widgetType, WidgetInfo widgetInfo) {
    return UserMetricInputWidgetData(
        widgetType,
        widget['cards']
            .map<MeasurementInputCard>((e) => MeasurementInputCard(
                e['title'],
                e['value'],
                e['precision'],
                e['unit'],
                e['metricId'],
                MeasurementInfo.fromJson(e['info'])))
            .toList(),
        referralModalEnabled: widget['referralModalEnabled'] ?? false,
        metricIdToCompare: widget['metricIdToCompare'],
        currentMetricIdValue: widget['currentMetricIdValue'],
        subCategoryCode: widget['subCategoryCode'],
        action:
            widget['action'] != null ? Action.fromJson(widget['action']) : null,
        confirmationAction: widget['confirmationAction'] != null ? Action.fromJson(widget['confirmationAction']) : null,
        widgetInfo: widgetInfo);
  }
}

class ConfirmationStatusWidgetData implements IWidgetData {
  ConfirmationStatusWidgetData(this.widgetType,
      {this.widgetInfo,
        this.title,
        this.subtitle,
        this.isConfirmed = true,
        this.imageUrl,
        this.action,
        this.description});

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  final String? title;
  final String? subtitle;
  final bool isConfirmed;
  final String? imageUrl;
  final String? description;
  final Action? action;

  static ConfirmationStatusWidgetData fromJson(
      widget, WidgetTypes widgetType, WidgetInfo widgetInfo) {
    return ConfirmationStatusWidgetData(
        widgetType,
        title: widget['title'],
        subtitle: widget['subtitle'],
        imageUrl: widget['imageUrl'],
        description: widget['description'],
        isConfirmed: widget['isConfirmed'] ?? true,
        action:
        widget['action'] != null ? Action.fromJson(widget['action']) : null,
        widgetInfo: widgetInfo);
  }
}

abstract class UserExperienceFormEvent {
  UserExperienceFormEvent() : super();
}

class UserExperienceFormLoadEvent extends UserExperienceFormEvent {
  final String? subCategoryCode;
  UserExperienceFormLoadEvent({this.subCategoryCode}) : super();

  @override
  String toString() => 'UserExperienceFormLoadEvent';
}

class UserExperienceFormSubmitEvent extends UserExperienceFormEvent {
  UserExperienceFormSubmitEvent() : super();

  @override
  String toString() => 'UserExperienceFormSubmitEvent';
}

abstract class ButtonEnableEvent {
  ButtonEnableEvent() : super();
}

class ButtonEnableTriggerEvent extends ButtonEnableEvent {
  ButtonEnableTriggerEvent() : super();

  @override
  String toString() => 'ButtonEnableTriggerEvent';
}

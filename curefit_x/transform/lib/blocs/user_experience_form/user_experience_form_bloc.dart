import 'package:common/network/client.dart';
import 'package:common/network/client_repository.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/user_experience_form/events.dart';
import 'package:transform/blocs/user_experience_form/models.dart';
import 'package:transform/blocs/user_experience_form/states.dart';

class UserExperienceFormBloc
    extends Bloc<UserExperienceFormEvent, UserExperienceFormState> {
  final CoachClientRepository repository;
  Map<String, String> responseMap = {};

  UserExperienceFormBloc(
      UserExperienceFormIdleState userExperienceFormIdleState, this.repository)
      : super(userExperienceFormIdleState) {
    on<UserExperienceFormLoadEvent>((event, emit) async {
      await _mapUserPreferenceFormDataToState(event, emit);
    });
    on<UserExperienceFormSubmitEvent>((event, emit) async {
      final response = await this.repository.postPageData(
          'v2/transform/submitUserExperienceForm',
          {"responseData": responseMap});
    });
  }

  Future<void> _mapUserPreferenceFormDataToState(
      UserExperienceFormLoadEvent event,
      Emitter<UserExperienceFormState> emit) async {
    try {
      emit(UserExperienceFormLoadingState());
      final response = await this.repository.getPageData(
          'v2/transform/userExperienceForm',
          {"subCategoryCode": event.subCategoryCode ?? ""});
      if (response != null) {
        emit(
          UserExperienceFormLoadedState(
              userExperienceFormData:
                  UserExperienceFormData.fromJson(response)),
        );
      } else {
        emit(UserExperienceFormFailedState());
      }
    } on NetworkException {
      emit(UserExperienceFormFailedState());
    } catch (e) {
      emit(UserExperienceFormFailedState());
    }
  }
}

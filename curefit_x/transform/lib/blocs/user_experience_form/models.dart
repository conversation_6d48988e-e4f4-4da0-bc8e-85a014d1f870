import 'package:common/action/action_handler.dart';
import 'package:enum_to_string/enum_to_string.dart';

class UserExperienceFormData {
  UserExperienceFormData({
    this.title,
    required this.pages,
    this.subtitle,
    this.icon,
    this.action,
  });

  final String? title;
  final List<Page>? pages;
  final String? subtitle;
  final String? icon;
  final Action? action;

  static UserExperienceFormData fromJson(json) {
    return UserExperienceFormData(
      title: json["title"],
      pages: json['pages'].map<Page>((e) => Page.fromJson(e)).toList(),
      subtitle: json['subtitle'],
      icon: json['icon'],
      action: json['action'] != null ? Action.fromJson(json['action']) : null,
    );
  }
}

class Page {
  Page({
    this.title,
    this.subtitle,
    this.description,
    this.pageType,
    this.pageId,
    this.items,
    this.lottieUrl,
    this.imageUrl,
    this.coachTitle,
    this.coachSubtitle,
    this.coachProfileImage,
    this.isPrimaryButton = true,
    this.action,
  });

  final String? title;
  final String? subtitle;
  final String? description;
  final PageType? pageType;
  final String? pageId;
  final List<Item>? items;
  final String? lottieUrl;
  final String? imageUrl;
  final String? coachTitle;
  final String? coachSubtitle;
  final String? coachProfileImage;
  final bool isPrimaryButton;
  final Action? action;

  static Page fromJson(data) {
    return Page(
      pageType: data['pageType'] != null
          ? EnumToString.fromString(PageType.values, data['pageType'])
          : null,
      pageId: data['pageId'],
      title: data["title"],
      subtitle: data['subtitle'],
      description: data["description"],
      imageUrl: data['imageUrl'],
      items: data['items'] != null
          ? data['items'].map<Item>((e) => Item.fromJson(e)).toList()
          : null,
      lottieUrl: data['lottieUrl'],
      coachTitle: data['coachTitle'],
      coachSubtitle: data['coachSubtitle'],
      isPrimaryButton: data['isPrimaryButton'] ?? true,
      coachProfileImage: data['coachProfileImage'],
      action: data['action'] != null ? Action.fromJson(data['action']) : null,
    );
  }
}

class Item {
  Item({this.title, this.message, this.id});

  final String? title;
  final String? message;
  final String? id;

  static Item fromJson(json) {
    return Item(
      title: json["title"],
      message: json["message"],
      id: json['id'],
    );
  }
}

enum PageType {
  INTRO_SCREEN,
  SINGLE_SELECT_SCREEN,
  PRESENTATION_SCREEN,
  RESPONSE_SCREEN
}

class UserExperienceFormResponseBody {
  UserExperienceFormResponseBody({
    this.loseWeightGoal,
    this.loseWightMethod,
    this.workoutFrequency,
  });

  late String? loseWeightGoal;
  late String? loseWightMethod;
  late String? workoutFrequency;

  Map<String, dynamic> toJson() => {
        'centerIds': loseWeightGoal,
        'formatIds': loseWightMethod,
        'timeSlots': workoutFrequency,
      };
}

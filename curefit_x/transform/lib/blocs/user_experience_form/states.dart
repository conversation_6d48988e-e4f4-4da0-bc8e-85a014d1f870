import 'package:transform/blocs/user_experience_form/models.dart';

abstract class UserExperienceFormState {
  UserExperienceFormState() : super();
}

class UserExperienceFormIdleState extends UserExperienceFormState {
  @override
  String toString() => 'UserExperienceFormIdleState';
}

class UserExperienceFormLoadingState extends UserExperienceFormState {
  @override
  String toString() => 'UserExperienceFormLoadingState';
}

class UserExperienceFormLoadedState extends UserExperienceFormState {
  final UserExperienceFormData userExperienceFormData;

  UserExperienceFormLoadedState({required this.userExperienceFormData})
      : super();

  @override
  String toString() => 'UserExperienceFormLoadedState';
}

class UserExperienceFormFailedState extends UserExperienceFormState {
  @override
  String toString() => 'UserExperienceFormFailedState';
}

abstract class ButtonEnableState {
  ButtonEnableState() : super();
}

class ButtonEnableIdleState extends ButtonEnableState {
  @override
  String toString() => 'ButtonEnableIdleState';
}

class ButtonEnableLoadedState extends ButtonEnableState {
  @override
  String toString() => 'ButtonEnableLoadedState';
}

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/user_experience_form/events.dart';
import 'package:transform/blocs/user_experience_form/states.dart';


class ButtonEnableBloc extends Bloc<ButtonEnableEvent, ButtonEnableState> {
  int selectedPage = 0;
  ButtonEnableBloc() : super(ButtonEnableIdleState()) {
    on<ButtonEnableTriggerEvent>((event, emit) async {
      emit(ButtonEnableIdleState());
      emit(ButtonEnableLoadedState());
    });
  }
}

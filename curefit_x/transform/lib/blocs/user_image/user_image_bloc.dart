import 'dart:io';

import 'package:common/action/action_handler.dart';
import 'package:common/network/client.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/user_image/events.dart';
import 'package:transform/blocs/user_image/models.dart';
import 'package:transform/blocs/user_image/state.dart';
import 'package:transform/network/user_image_repository.dart';

class UserImageBloc extends Bloc<UserImageEvent, UserImageState> {
  UserImageRepository repository;
  bool isBeginningImageAdded = false;

  UserImageBloc(this.repository) : super(UserImageIdleState()) {
    on<UploadUserImageEvent>((event, emit) async {
      try {
        String fileName = event.filePath.split("/").last;
        dynamic s3Info = await repository.getSignedUrl(fileName);
        await repository.uploadUserPicToS3(s3Info, File(event.filePath));
        final destinationUrlResponse =
            await repository.getDestinationImageUrl(s3Info['fileName']);
        if (destinationUrlResponse != null &&
            destinationUrlResponse['url'] != null) {
          final response = await repository.uploadUserImageUrl(
              destinationUrlResponse['url'],
              event.replacePic ?? false,
              event.pictureTag ?? "");
          if (response != null) {
            Action action = Action.fromJson(response['action']);
            emit(UserImageUploadedState(
                fileName: s3Info['fileName'], action: action));
          } else {
            emit(UserImageUploadingFail(
                subTitle: "Something went wrong. Please try again later."));
          }
        } else {
          emit(UserImageUploadingFail(
              subTitle: "Something went wrong. Please try again later."));
        }
      } on NetworkException catch (exception) {
        emit(UserImageUploadingFail(
          title: exception.title,
          subTitle: exception.subTitle ??
              "Something went wrong. Please try again later.",
        ));
      } catch (e) {
        emit(UserImageUploadingFail(
            subTitle: "Something went wrong. Please try again later."));
        rethrow;
      }
    });

    on<LoadUserImageConfirmationEvent>((event, emit) async {
      try {
        emit(UserImageConfirmationLoadingState());
        final response = await repository.getUserImageConfirmationData(
            subCategoryCode: event.subCategoryCode,
            pictureTag: event.pictureTag);
        emit(UserImageConfirmationLoadedState(
            userImageConfirmationViewData:
                UserImageConfirmationViewData.fromJson(response)));
      } on NetworkException catch (exception) {
        emit(UserImageConfirmationFailedState(
          title: exception.title,
          subTitle: exception.subTitle ??
              "Something went wrong. Please try again later.",
        ));
      } catch (e) {
        emit(UserImageConfirmationFailedState(
            subTitle: "Something went wrong. Please try again later."));
        rethrow;
      }
    });
  }
}

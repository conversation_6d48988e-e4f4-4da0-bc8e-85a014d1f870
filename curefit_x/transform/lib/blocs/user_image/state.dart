import 'package:common/action/action_handler.dart';
import 'package:meta/meta.dart';
import 'package:transform/blocs/user_image/models.dart';

@immutable
abstract class UserImageState {
  UserImageState() : super();
}

class UserImageIdleState extends UserImageState {
  @override
  String toString() => 'UserImageIdleState';
}

class UserImageUploadingState extends UserImageState {
  @override
  String toString() => 'UserImageUploadingState';
}

class UserImageUploadedState extends UserImageState {
  final String? fileName;
  final Action? action;

  UserImageUploadedState({required this.fileName, required this.action}) : super();

  @override
  String toString() => 'UserImageUploadedState';
}

class UserImageUploadingFail extends UserImageState {
  final String? title;
  final String? subTitle;

  UserImageUploadingFail({this.title, this.subTitle}) : super();

  @override
  String toString() => 'UserImageUploadingFail';
}

class UserImageConfirmationIdleState extends UserImageState {
  UserImageConfirmationIdleState() : super();

  @override
  String toString() => 'UserImageConfirmationIdleState';
}

class UserImageConfirmationLoadingState extends UserImageState {
  UserImageConfirmationLoadingState() : super();

  @override
  String toString() => 'UserImageConfirmationLoadingState';
}

class UserImageConfirmationLoadedState extends UserImageState {
  final UserImageConfirmationViewData userImageConfirmationViewData;
  UserImageConfirmationLoadedState({required this.userImageConfirmationViewData});

  @override
  String toString() => 'UserImageConfirmationLoadedState';
}

class UserImageConfirmationFailedState extends UserImageState {
  final String? title;
  final String? subTitle;

  UserImageConfirmationFailedState({this.title, this.subTitle}) : super();

  @override
  String toString() => 'UserImageConfirmationFailedState';
}

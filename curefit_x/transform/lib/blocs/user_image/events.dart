abstract class UserImageEvent {
  UserImageEvent() : super();
}

class UploadUserImageEvent extends UserImageEvent {
  final String filePath;
  final String? pictureTag;
  final bool? replacePic;

  UploadUserImageEvent({required this.filePath, this.pictureTag, this.replacePic}) : super();

  @override
  String toString() => 'UploadUserImageEvent';
}

class LoadUserImageConfirmationEvent extends UserImageEvent {
  final String? subCategoryCode;
  final String? pictureTag;
  LoadUserImageConfirmationEvent({this.subCategoryCode, this.pictureTag}) : super();

  @override
  String toString() => 'LoadUserImageConfirmationEvent';
}

import 'package:common/action/action_handler.dart';
import 'package:transform/blocs/clp/models.dart';

class UserImageConfirmationViewData {
  UserImageConfirmationViewData(
      {this.title,
      this.titleBarText,
      this.subtitle,
      this.showIcon,
      this.stepCard,
      this.doneAction,
      this.reUploadAction});

  final String? title;
  final String? titleBarText;
  final String? subtitle;
  final bool? showIcon;
  final StepCard? stepCard;
  final Action? doneAction;
  final Action? reUploadAction;

  static UserImageConfirmationViewData fromJson(json) {
    return UserImageConfirmationViewData(
      title: json["title"],
      titleBarText: json['titleBarText'],
      subtitle: json['subtitle'],
      showIcon: json['showIcon'],
      stepCard: json['stepCard'] != null
          ? StepCard.fromJSON(json['stepCard'], null)
          : null,
      doneAction: json['doneAction'] != null
          ? Action.fromJson(json['doneAction'])
          : null,
      reUploadAction: json['reUploadAction'] != null
          ? Action.fromJson(json['reUploadAction'])
          : null,
    );
  }
}

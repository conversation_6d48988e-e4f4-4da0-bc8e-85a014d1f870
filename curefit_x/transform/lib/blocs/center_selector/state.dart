import 'package:common/model/error_prop_model.dart';
import 'package:common/network/client.dart';

import 'models.dart';

abstract class CenterSelectorState {
  CenterSelectorState() : super();
}

class CenterSelectorIdleState extends CenterSelectorState {
  @override
  String toString() {
    return "CenterSelectorIdleState";
  }
}

class CenterSelectorLoadingState extends CenterSelectorState {
  @override
  String toString() {
    return "CenterSelectorLoadingState";
  }
}

class CenterSelectorLoadedState extends CenterSelectorState {
  final CenterSelectorScreenData screenData;

  CenterSelectorLoadedState({required this.screenData}) : super();

  @override
  String toString() {
    return "CenterSelectorLoadedState";
  }
}

class BatchSelectorLoadedState extends CenterSelectorState {
  final BatchSelectorScreenData screenData;

  BatchSelectorLoadedState({required this.screenData}) : super();

  @override
  String toString() {
    return "BatchSelectorLoadedState";
  }
}

class AddressSelectorLoadedState extends CenterSelectorState {
  final AddressSelectorScreenData screenData;

  AddressSelectorLoadedState({required this.screenData}) : super();

  @override
  String toString() {
    return "AddressSelectorLoadedState";
  }
}

class CenterSelectorErrorState extends CenterSelectorState {
  final ErrorInfo? errorInfo;

  CenterSelectorErrorState({this.errorInfo}) : super();

  @override
  String toString() {
    return "CenterSelectorErrorState";
  }
}

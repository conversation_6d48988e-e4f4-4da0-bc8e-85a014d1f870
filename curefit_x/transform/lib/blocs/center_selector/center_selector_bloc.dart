import 'package:common/model/error_prop_model.dart';
import 'package:common/network/client.dart';
import 'package:common/network/client_repository.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/center_selector/events.dart';
import 'package:transform/blocs/center_selector/models.dart';
import 'package:transform/blocs/center_selector/state.dart';

class CenterSelectorBloc
    extends Bloc<CenterSelectorEvent, CenterSelectorState> {
  final CoachClientRepository repository;
  late CenterSelectorScreenData? screenData = null;
  late BatchSelectorScreenData? batchSelectorScreenData = null;
  late AddressSelectorScreenData? addressSelectorScreenData = null;

  CenterSelectorBloc(
      CenterSelectorIdleState centerSelectorIdleState, this.repository)
      : super(centerSelectorIdleState) {
    on<LoadCenterSelectorContentEvent>((event, emit) async {
      await _mapCenterSelectorDataToState(event, emit);
    });
    on<LoadBatchSelectorContentEvent>((event, emit) async {
      await _mapBatchSelectorDataToState(event, emit);
    });
    on<LoadAddressSelectorContentEvent>((event, emit) async {
      await _mapAddressSelectorDataToState(event, emit);
    });
  }

  Future<void> _mapCenterSelectorDataToState(
      LoadCenterSelectorContentEvent event,
      Emitter<CenterSelectorState> emit) async {
    try {
      emit(CenterSelectorLoadingState());
      final response = await this.repository.getPageData(
          'v2/transform/centersOption',
          {"subCategoryCode": event.subCategoryCode ?? "",});
      if (response != null) {
        screenData = CenterSelectorScreenData.fromJson(response);
        emit(CenterSelectorLoadedState(screenData: screenData!));
      } else {
        emit(CenterSelectorErrorState(
            errorInfo: UnknownError(
                failed: true,
                responseCode: "404",
                callbackFunction: () {
                  refreshCenter(subCategoryCode: event.subCategoryCode);
                })));
      }
    } on NetworkException catch (exception) {
      emit(CenterSelectorErrorState(
          errorInfo: NoInternetError(
              failed: true,
              callbackFunction: () {
                refreshCenter(subCategoryCode: event.subCategoryCode);
              })));
    } catch (e) {
      emit(CenterSelectorErrorState(
          errorInfo: UnknownError(
              failed: true,
              callbackFunction: () {
                refreshCenter(subCategoryCode: event.subCategoryCode);
              })));
    }
  }

  void refreshCenter({String? subCategoryCode}) {
    this.add(LoadCenterSelectorContentEvent(subCategoryCode: subCategoryCode));
  }

  void refresh({String? subCategoryCode}) {
    this.add(LoadBatchSelectorContentEvent(subCategoryCode: subCategoryCode));
  }

  Future<void> _mapBatchSelectorDataToState(LoadBatchSelectorContentEvent event,
      Emitter<CenterSelectorState> emit) async {
    try {
      emit(CenterSelectorLoadingState());
      final response = await this.repository.getPageData(
          'v2/transform/batchesOption',
          {"subCategoryCode": event.subCategoryCode ?? "",
          "filter": event.filter ?? ""});
      if (response != null) {
        batchSelectorScreenData = BatchSelectorScreenData.fromJson(response);
        emit(BatchSelectorLoadedState(screenData: batchSelectorScreenData!));
      } else {
        emit(CenterSelectorErrorState(
            errorInfo: UnknownError(
                failed: true,
                responseCode: "404",
                callbackFunction: () {
                  refresh(subCategoryCode: event.subCategoryCode);
                })));
      }
    } on NetworkException catch (exception) {
      emit(CenterSelectorErrorState(
          errorInfo: NoInternetError(
              failed: true,
              callbackFunction: () {
                refresh(subCategoryCode: event.subCategoryCode);
              })));
    } catch (e) {
      emit(CenterSelectorErrorState(
          errorInfo: UnknownError(
              failed: true,
              callbackFunction: () {
                refresh(subCategoryCode: event.subCategoryCode);
              })));
    }
  }

  Future<void> _mapAddressSelectorDataToState(LoadAddressSelectorContentEvent event,
      Emitter<CenterSelectorState> emit) async {
    try {
      emit(CenterSelectorLoadingState());
      final response = await this.repository.getPageData(
          'transform/addressOption',
          {"subCategoryCode": event.subCategoryCode ?? ""});
      if (response != null) {
        addressSelectorScreenData = AddressSelectorScreenData.fromJson(response);
        emit(AddressSelectorLoadedState(screenData: addressSelectorScreenData!));
      } else {
        emit(CenterSelectorErrorState(
            errorInfo: UnknownError(
                failed: true,
                responseCode: "404",
                callbackFunction: () {
                  refresh(subCategoryCode: event.subCategoryCode);
                })));
      }
    } on NetworkException catch (exception) {
      emit(CenterSelectorErrorState(
          errorInfo: NoInternetError(
              failed: true,
              callbackFunction: () {
                refresh(subCategoryCode: event.subCategoryCode);
              })));
    } catch (e) {
      emit(CenterSelectorErrorState(
          errorInfo: UnknownError(
              failed: true,
              callbackFunction: () {
                refresh(subCategoryCode: event.subCategoryCode);
              })));
    }
  }
}

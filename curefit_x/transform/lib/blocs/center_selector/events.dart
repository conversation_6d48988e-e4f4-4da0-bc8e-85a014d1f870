abstract class CenterSelectorEvent {}

class LoadCenterSelector<PERSON>ontentEvent extends CenterSelector<PERSON>vent {
  final String? subCategoryCode;
  LoadCenterSelectorContentEvent({this.subCategoryCode}) : super();

  @override
  String toString() {
    return "LoadCenterSelectorContentEvent";
  }
}

class LoadBatchSelectorContentEvent extends CenterSelectorEvent {
  final String? subCategoryCode;
  final String? filter;
  LoadBatchSelectorContentEvent({this.subCategoryCode, this.filter}) : super();

  @override
  String toString() {
    return "LoadBatchSelectorContentEvent";
  }
}

class LoadAddressSelectorContentEvent extends CenterSelectorEvent {
  final String? subCategoryCode;
  LoadAddressSelectorContentEvent({this.subCategoryCode}) : super();

  @override
  String toString() {
    return "LoadAddressSelectorContentEvent";
  }
}

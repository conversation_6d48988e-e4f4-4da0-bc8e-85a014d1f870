import 'package:common/action/action_handler.dart';

class CenterSelectorScreenData {
  CenterSelectorScreenData({
    this.centerPageTitle,
    this.centersData,
    this.slotModalTitle,
    this.slotModalSubtitle,
    this.modalTextHexColor,
    this.centerSlotsMap,
    this.buttonTitle,
  });

  final String? centerPageTitle;
  final List<CenterData>? centersData;
  final String? slotModalTitle;
  final String? slotModalSubtitle;
  final String? modalTextHexColor;
  final Map<String, List<Slots>>? centerSlotsMap;
  final String? buttonTitle;

  static CenterSelectorScreenData fromJson(json) {
    Map<String, List<Slots>> centerSlotsMap = {};
    if (json["centerSlotsMap"] != null) {
      json["centerSlotsMap"].forEach((id, slot) {
        List<Slots> slots = slot.map<Slots>((data) {
          return Slots.fromJson(data);
        }).toList();
        centerSlotsMap[id] = slots;
      });
    }
    return CenterSelectorScreenData(
      centerPageTitle: json["centerPageTitle"],
      centersData: json["centersData"] != null
          ? json["centersData"].map<CenterData>((centerData) {
              return CenterData.fromJson(centerData);
            }).toList()
          : null,
      slotModalTitle: json["slotModalTitle"],
      slotModalSubtitle: json["slotModalSubtitle"],
      modalTextHexColor: json["modalTextHexColor"],
      centerSlotsMap: centerSlotsMap,
      buttonTitle: json["buttonTitle"],
    );
  }
}

class BatchSelectorScreenData {
  BatchSelectorScreenData({
    this.pageTitle,
    this.batchesData,
    this.selectedIndex,
    this.errorMessage,
    this.action,
    this.localitySelectorAction,
    this.bannerFormData,
  });

  final String? pageTitle;
  final List<BatchData>? batchesData;
  final int? selectedIndex;
  final String? errorMessage;
  final Action? action;
  final Action? localitySelectorAction;
  final BannerFormData? bannerFormData;

  static BatchSelectorScreenData fromJson(json) {
    return BatchSelectorScreenData(
      pageTitle: json["pageTitle"],
      batchesData: json["batchesData"] != null
          ? json["batchesData"].map<BatchData>((batchData) {
              return BatchData.fromJson(batchData);
            }).toList()
          : null,
      selectedIndex: json["selectedIndex"] ?? 0,
      errorMessage: json["errorMessage"],
      action: json["action"] != null ? Action.fromJson(json["action"]) : null,
      localitySelectorAction: json["localitySelectorAction"] != null
          ? Action.fromJson(json["localitySelectorAction"])
          : null,
      bannerFormData: json["bannerFormData"] != null
          ? BannerFormData.fromJson(json["bannerFormData"])
          : null,
    );
  }
}

class BatchData {
  final String? id;
  final String? title;
  final String? subtitle;
  final String? displayText;
  final List<CenterData>? centersData;
  final String? revisedTimingText;

  BatchData({
    this.id,
    this.title,
    this.subtitle,
    this.displayText,
    this.centersData,
    this.revisedTimingText,
  });

  static BatchData fromJson(dynamic json) {
    return BatchData(
      id: json['id'],
      title: json['title'],
      subtitle: json['subtitle'],
      displayText: json['displayText'],
      centersData: json['centersData'] != null
          ? json["centersData"].map<CenterData>((centerData) {
              return CenterData.fromJson(centerData);
            }).toList()
          : null,
      revisedTimingText: json["revisedTimingText"] ?? null,
    );
  }
}

class CenterData {
  final String? id;
  final String? title;
  final String? subtitle;
  final String? slotsText;
  final String? imageUrl;
  final String? distanceText;
  final String? hexColor;
  final bool? enabled;
  final bool isSoldOut;
  final Action? action;
  final List<Slots>? slots;
  final List<String>? tagsImage;
  final String? revisedTimingText;

  CenterData(
      {this.id,
      this.title,
      this.subtitle,
      this.slotsText,
      this.distanceText,
      this.imageUrl,
      this.hexColor,
      this.enabled,
      this.isSoldOut = false,
      this.action,
      this.tagsImage,
      this.slots,
      this.revisedTimingText});

  static CenterData fromJson(dynamic json) {
    return CenterData(
      id: json['id'],
      title: json['title'],
      subtitle: json['subtitle'],
      slotsText: json['slotsText'],
      distanceText: json['distanceText'],
      imageUrl: json['imageUrl'],
      hexColor: json['hexColor'],
      enabled: json['enabled'],
      isSoldOut: json['isSoldOut'] ?? false,
      slots: json['slots'] != null
          ? json["slots"].map<Slots>((slot) {
              return Slots.fromJson(slot);
            }).toList()
          : null,
      tagsImage: json['tagsImage'] != null
          ? json["tagsImage"].map<String>((tag) {
              return tag.toString();
            }).toList()
          : null,
      action: json['action'] != null ? Action.fromJson(json['action']) : null,
      revisedTimingText: json['revisedTimingText'] ?? null,
    );
  }
}

class Slots {
  final String? id;
  final String? title;
  final bool? enabled;
  final String? slotText;
  final String? hexColor;
  final bool isActionSlot;
  final Action? action;

  Slots(
      {this.id,
      this.title,
      this.enabled,
      this.slotText,
      this.hexColor,
      this.isActionSlot = false,
      this.action});

  static Slots fromJson(dynamic json) {
    return Slots(
      id: json['id'],
      title: json['title'],
      enabled: json['enabled'],
      slotText: json['slotText'],
      hexColor: json['hexColor'],
      isActionSlot: json['isActionSlot'] ?? false,
      action: json['action'] != null ? Action.fromJson(json['action']) : null,
    );
  }
}

class BannerFormData {
  final String? title;
  final Action? action;

  BannerFormData({
    this.title,
    this.action,
  });

  static BannerFormData fromJson(dynamic json) {
    return BannerFormData(
      title: json['title'],
      action: json['action'] != null ? Action.fromJson(json['action']) : null,
    );
  }
}

class AddressData {
  final String? id;
  final String? title;
  final String? subtitle;

  AddressData({
    this.id,
    this.title,
    this.subtitle,
  });

  static AddressData fromJson(dynamic json) {
    return AddressData(
      id: json['id'],
      title: json['title'],
      subtitle: json['subtitle'],
    );
  }
}

class AddressSelectorScreenData {
  AddressSelectorScreenData({
    this.pageTitle,
    this.addressesData,
    this.selectedAddressId,
    this.errorMessage,
    this.addAction,
    this.action,
  });

  final String? pageTitle;
  final List<AddressData>? addressesData;
  final String? selectedAddressId;
  final String? errorMessage;
  final Action? addAction;
  final Action? action;

  static AddressSelectorScreenData fromJson(json) {
    return AddressSelectorScreenData(
      pageTitle: json["pageTitle"],
      addressesData: json["addressesData"] != null
          ? json["addressesData"].map<AddressData>((addressData) {
              return AddressData.fromJson(addressData);
            }).toList()
          : null,
      selectedAddressId: json["selectedAddressId"] ?? null,
      errorMessage: json["errorMessage"],
      addAction:
          json["addAction"] != null ? Action.fromJson(json["addAction"]) : null,
      action: json["action"] != null ? Action.fromJson(json["action"]) : null,
    );
  }
}

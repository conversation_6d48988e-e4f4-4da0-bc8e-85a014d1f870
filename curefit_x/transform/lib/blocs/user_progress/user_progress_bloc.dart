import 'package:common/network/client.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/user_progress/events.dart';
import 'package:transform/blocs/user_progress/state.dart';
import 'package:transform/blocs/user_progress/models.dart';
import 'package:common/network/client_repository.dart';

class UserProgressBloc extends Bloc<UserProgressEvent, UserProgressState> {
  final CoachClientRepository repository;

  UserProgressBloc(UserProgressState initialState, this.repository)
      : super(initialState) {
    on<LoadUserProgressEvent>((event, emit) async {
      await _mapToProgressState(event, emit);
    });
  }

  Future<void> _mapToProgressState(
      LoadUserProgressEvent event, Emitter<UserProgressState> emit) async {
    try {
      emit(UserProgressLoading());
      final response = await this.repository.getPageData(
          'v2/transform/userProgress',
          {"subCategoryCode": event.subCategoryCode ?? ""});

      if (response == null) {
        emit(UserProgressNotLoaded(throw new Error()));
      }
      final detail = response;
      emit(UserProgressLoaded(
          userProgressScreen: UserProgressScreen(
              detail['title'], detail['subtitle'], detail['widgets'])));
    } on NetworkException catch (exception) {
      emit(UserProgressNotLoaded(exception.subTitle));
    } catch (e) {
      emit(UserProgressNotLoaded("Something went wrong"));
    }
  }
}

import 'package:meta/meta.dart';
import 'package:transform/blocs/user_progress/models.dart';

@immutable
abstract class UserProgressState {
  UserProgressState() : super();
}

class UserProgressIdle extends UserProgressState {
  @override
  String toString() => 'IdleState';
}

class UserProgressLoading extends UserProgressState {
  @override
  String toString() => 'UserProgressLoading';
}

class UserProgressLoaded extends UserProgressState {
  final UserProgressScreen userProgressScreen;

  UserProgressLoaded({required this.userProgressScreen})
      : super();

  @override
  String toString() => 'UserProgressLoaded';
}

class UserProgressNotLoaded extends UserProgressState {
  final String? error;

  UserProgressNotLoaded([this.error]) : super();

  @override
  String toString() => 'UserProgressNotLoaded';
}

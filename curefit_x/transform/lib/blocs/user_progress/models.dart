import 'package:common/action/action_handler.dart';
import 'package:common/constants/action_constants.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:transform/UI/health_score/widgets/lifestyle_indicator_legend.dart';

class UserProgressScreen {
  UserProgressScreen(this.title, this.subtitle, this.widgets);

  List widgets;
  String title;
  String subtitle;
}

class IWidget {
  IWidget(this.widgetType);

  String widgetType;
}

class TabbedContainerWidgetData implements IWidgetData {
  TabbedContainerWidgetData(this.widgetType, this.tabs, this.widgets,
      {this.action,
      this.widgetInfo,
      this.showAlertBox,
      this.measurementUpdateTitle,
      this.updateButtonTitle});

  @override
  WidgetInfo? widgetInfo;

  final List<String> tabs;
  final List widgets;
  final String? measurementUpdateTitle;
  final String? updateButtonTitle;
  final bool? showAlertBox;
  final Action? action;

  @override
  WidgetTypes widgetType;

  static TabbedContainerWidgetData fromJson(
      widget, WidgetTypes widgetType, WidgetInfo widgetInfo) {
    return TabbedContainerWidgetData(
      widgetType,
      widget['tabs'].map<String>((e) => e.toString()).toList(),
      widget['widgets'],
      action:
          widget['action'] != null ? Action.fromJson(widget['action']) : null,
      widgetInfo: widgetInfo,
      showAlertBox: widget['showAlertBox'],
      measurementUpdateTitle: widget['measurementUpdateTitle'],
      updateButtonTitle: widget['updateButtonTitle'],
    );
  }
}

class MeasurementMissingWidgetData implements IWidgetData {
  MeasurementMissingWidgetData(
      this.widgetType, this.title, this.description, this.action);

  @override
  WidgetInfo? widgetInfo;

  final String title;
  final String description;
  final Action action;

  @override
  WidgetTypes widgetType;

  static MeasurementMissingWidgetData fromJson(widget, WidgetTypes widgetType) {
    return MeasurementMissingWidgetData(widgetType, widget['title'],
        widget['description'], Action.fromJson(widget['action']));
  }
}

class LifestyleScoreSummaryWidgetData implements IWidgetData {
  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  final String title;
  final Map<String, dynamic> titleStyle;
  final String subTitle;
  final String motivationText;
  final int overallScore;
  final LifestyleScoreDescriptionView scoreDescriptionView;
  final WaterLevelAnimationView? waterLevelAnimationView;

  LifestyleScoreSummaryWidgetData(
      this.widgetType,
      this.title,
      this.titleStyle,
      this.subTitle,
      this.motivationText,
      this.overallScore,
      this.scoreDescriptionView,
      {this.waterLevelAnimationView});

  static LifestyleScoreSummaryWidgetData fromJson(
      widget, WidgetTypes widgetType) {
    return LifestyleScoreSummaryWidgetData(
      widgetType,
      widget["title"],
      widget["titleStyle"],
      "",
      widget["motivationText"],
      widget["overallScore"],
      LifestyleScoreDescriptionView.fromJson(widget["legendsView"]),
      waterLevelAnimationView: widget["waterLevelAnimationView"] != null
          ? WaterLevelAnimationView.fromJson(widget["waterLevelAnimationView"])
          : null,
    );
  }
}

class LifestyleScoreDescriptionView {
  String title;
  List<LifestyleScoreDescription> scoreDescriptions;

  LifestyleScoreDescriptionView(this.title, this.scoreDescriptions);

  static LifestyleScoreDescriptionView fromJson(scoreDescriptions) {
    return LifestyleScoreDescriptionView(
        scoreDescriptions["title"],
        scoreDescriptions["legends"]
            .map<LifestyleScoreDescription>(
                (scoreDesc) => LifestyleScoreDescription.fromJson(scoreDesc))
            .toList());
  }
}

class WaterLevelAnimationView {
  String? title;
  String? idealScoreTitle;
  String? headerTitle;
  int? score;
  int? idealScore;
  int? deltaScore;
  bool? isPositive;
  List<dynamic>? displayTexts;
  List<dynamic>? displayScores;

  WaterLevelAnimationView({
    this.title,
    this.idealScoreTitle,
    this.headerTitle,
    this.score,
    this.idealScore,
    this.deltaScore,
    this.displayTexts,
    this.displayScores,
    this.isPositive,
  });

  static WaterLevelAnimationView fromJson(data) {
    return WaterLevelAnimationView(
      title: data["title"],
      idealScoreTitle: data["idealScoreTitle"],
      headerTitle: data["headerTitle"],
      score: data["score"],
      idealScore: data["idealScore"],
      deltaScore: data["deltaScore"],
      displayTexts: data["displayTexts"],
      displayScores: data["displayScores"],
      isPositive: data["positive"],
    );
  }
}

class LifestyleScoreDescription {
  final String title;
  final String subTitle;
  final int minScore;

  LifestyleScoreDescription(this.title, this.subTitle, this.minScore);

  static LifestyleScoreDescription fromJson(scoreDescription) {
    return LifestyleScoreDescription(scoreDescription["title"],
        scoreDescription["subTitle"], scoreDescription["minScore"]);
  }
}

class LifestyleAreaHabitWidgetData implements IWidgetData {
  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  List<AreaHabit> areaHabits;

  int initialSelectedAreaIndex;

  LifestyleAreaHabitWidgetData(
      this.widgetType, this.initialSelectedAreaIndex, this.areaHabits,
      {this.widgetInfo});

  static LifestyleAreaHabitWidgetData fromJson(
      widgetData, WidgetTypes widgetType, WidgetInfo widgetInfo) {
    return LifestyleAreaHabitWidgetData(
      widgetType,
      widgetData["initialSelectedAreaIndex"],
      widgetData["areaHabits"]
          .map<AreaHabit>((habit) => AreaHabit.fromJson(habit))
          .toList(),
      widgetInfo: widgetInfo,
    );
  }
}

class AreaHabit {
  String areaName;
  int score;
  String areaMessage;
  List<HabitScore> habitScores;
  Action action;

  AreaHabit(this.areaName, this.score, this.areaMessage, this.habitScores,
      this.action);

  static AreaHabit fromJson(areaHabit) {
    return AreaHabit(
        areaHabit["areaName"],
        areaHabit["areaScore"],
        areaHabit["areaMessage"],
        areaHabit["habitScores"]
            .map<HabitScore>((score) => HabitScore.fromJson(score))
            .toList(),
        Action.fromJson(areaHabit["action"]));
  }
}

class HabitScore {
  String habitName;
  int score;

  HabitScore(this.habitName, this.score);

  static HabitScore fromJson(habitScore) {
    return HabitScore(habitScore["habitName"], habitScore["score"]);
  }
}

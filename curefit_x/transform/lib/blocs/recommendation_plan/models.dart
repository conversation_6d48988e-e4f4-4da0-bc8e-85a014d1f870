import 'package:common/action/action_handler.dart';

class RecommendationPlanData {
  RecommendationPlanData({
    this.title,
    this.pageTitle,
    this.titleImageUrl,
    this.subtitle,
    this.description,
    this.imageUrl,
    this.primaryAction,
    this.secondaryAction,
  }) : super();

  final String? title;
  final String? pageTitle;
  final String? titleImageUrl;
  final String? subtitle;
  final String? description;
  final String? imageUrl;
  final Action? primaryAction;
  final Action? secondaryAction;

  factory RecommendationPlanData.fromJson(dynamic json) {
    return RecommendationPlanData(
      title: json['title'],
      pageTitle: json['pageTitle'],
      titleImageUrl: json['titleImageUrl'],
      subtitle: json['subtitle'],
      description: json['description'],
      imageUrl: json['imageUrl'],
      primaryAction: json['primaryAction'] != null
          ? Action.fromJson(json['primaryAction'])
          : null,
      secondaryAction: json['secondaryAction'] != null
          ? Action.fromJson(json['secondaryAction'])
          : null,
    );
  }
}

abstract class RecommendationPlanEvent {
  RecommendationPlanEvent() : super();
}

class LoadRecommendationPlanEvent extends RecommendationPlanEvent {
  final bool showLoader;

  LoadRecommendationPlanEvent({this.showLoader = false}) : super();

  @override
  String toString() {
    return "LoadRecommendationPlanEvent";
  }
}

class ResetRecommendationPlanEvent extends RecommendationPlanEvent {
  @override
  String toString() => 'ResetRecommendationPlanEvent';
}

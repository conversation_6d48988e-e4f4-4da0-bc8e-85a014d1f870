import 'package:common/model/error_prop_model.dart';
import 'models.dart';

abstract class RecommendationPlanState {
  RecommendationPlanState() : super();
}

class RecommendationPlanIdleState extends RecommendationPlanState {
  @override
  String toString() => 'RecommendationPlanIdleState';
}

class RecommendationPlanLoadingState extends RecommendationPlanState {
  final RecommendationPlanData? screenData;
  final bool showLoader;

  RecommendationPlanLoadingState({this.screenData, this.showLoader = true});

  @override
  String toString() => 'RecommendationPlanLoadingState';
}

class RecommendationPlanLoadedState extends RecommendationPlanState {
  final RecommendationPlanData screenData;

  RecommendationPlanLoadedState(
      {required this.screenData})
      : super();

  @override
  String toString() => 'RecommendationPlanLoadedState';
}

class RecommendationPlanErrorState extends RecommendationPlanState {
  final ErrorInfo? errorInfo;

  RecommendationPlanErrorState([this.errorInfo]) : super();

  @override
  String toString() => 'RecommendationPlanErrorState';
}

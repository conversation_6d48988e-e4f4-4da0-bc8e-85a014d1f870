import 'dart:io';

import 'package:bloc/bloc.dart';
import 'package:common/model/error_prop_model.dart';
import 'package:common/network/client.dart';
import 'package:common/network/client_repository.dart';
import 'package:transform/blocs/recommendation_plan/events.dart';
import 'package:transform/blocs/recommendation_plan/models.dart';
import 'package:transform/blocs/recommendation_plan/state.dart';

class RecommendationPlanBloc
    extends Bloc<RecommendationPlanEvent, RecommendationPlanState> {
  final CoachClientRepository repository;
  RecommendationPlanData screenData = RecommendationPlanData();

  RecommendationPlanBloc(this.repository)
      : super(RecommendationPlanIdleState()) {
    on<LoadRecommendationPlanEvent>((event, emit) async {
      await _mapLoadWeightLossTab(event, emit);
    });
    on<ResetRecommendationPlanEvent>((event, emit) async {
      screenData = RecommendationPlanData();
      emit(RecommendationPlanLoadedState(screenData: screenData));
    });
  }

  void refresh() {
    this.add(LoadRecommendationPlanEvent());
  }

  Future<void> _mapLoadWeightLossTab(LoadRecommendationPlanEvent event,
      Emitter<RecommendationPlanState> emit) async {
    emit(RecommendationPlanLoadingState(
        screenData: screenData, showLoader: event.showLoader));
    await Future.delayed(Duration(seconds: 3));
    try {
      final response = await this
          .repository
          .getPageData('v2/transform/recommended_plan', {});
      if (response != null) {
        screenData = RecommendationPlanData.fromJson(response);
        emit(RecommendationPlanLoadedState(screenData: screenData));
      } else {
        emit(RecommendationPlanErrorState(UnknownError(
            failed: true, responseCode: "404", callbackFunction: refresh)));
      }
    } on NetworkException catch (exception) {
      emit(RecommendationPlanErrorState(NetworkError(
          failed: true,
          responseCode: '${(exception).statusCode}',
          callbackFunction: refresh)));
    } on SocketException {
      emit(RecommendationPlanErrorState(
          NoInternetError(failed: true, callbackFunction: refresh)));
    } catch (e) {
      emit(RecommendationPlanErrorState(
          UnknownError(failed: true, callbackFunction: refresh)));
    }
  }
}

import 'package:transform/blocs/guides/models.dart';

abstract class GuidesDataState {
  GuidesDataState() : super();
}

class GuidesDataIdleState extends GuidesDataState {
  @override
  String toString() => 'GuidesDataIdleState';
}

class GuidesDataLoadingState extends GuidesDataState {
  @override
  String toString() => 'GuidesDataLoadingState';
}

class GuidesDataLoadedState extends GuidesDataState {
  final GuidesScreenData? guidesScreenData;

  GuidesDataLoadedState({this.guidesScreenData})
      : super();

  @override
  String toString() => 'GuidesDataLoadedState';
}

class GuidesDataFailedState extends GuidesDataState {
  @override
  String toString() => 'GuidesDataFailedState';
}

import 'package:common/network/client.dart';
import 'package:common/network/client_repository.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/guides/events.dart';
import 'package:transform/blocs/guides/models.dart';
import 'package:transform/blocs/guides/state.dart';

class GuidesBloc extends Bloc<GuidesDataEvent, GuidesDataState> {
  final CoachClientRepository repository;

  GuidesBloc(GuidesDataIdleState guidesDataIdleState, this.repository)
      : super(guidesDataIdleState) {
    on<GuidesDataLoadEvent>((event, emit) async {
      await _mapGuidesDataToState(event, emit);
    });
  }

  Future<void> _mapGuidesDataToState(
      GuidesDataLoadEvent event, Emitter<GuidesDataState> emit) async {
    try {
      emit(GuidesDataLoadingState());
      final response = await this.repository.getPageData('v2/transform/guides',
          {"subCategoryCode": event.subCategoryCode ?? ""});
      if (response != null) {
        emit(
          GuidesDataLoadedState(
              guidesScreenData: GuidesScreenData.fromJson(response)),
        );
      } else {
        emit(GuidesDataFailedState());
      }
    } on NetworkException catch (exception) {
      emit(GuidesDataFailedState());
    } catch (e) {
      emit(GuidesDataFailedState());
    }
  }
}

import 'package:common/ui/aurora.dart';
import 'package:enum_to_string/enum_to_string.dart';

class GuidesScreenData {
  GuidesScreenData({
    this.title,
    this.themeType,
    this.widgets,
  });

  final String? title;
  final List<dynamic>? widgets;
  final CanvasTheme? themeType;

  static GuidesScreenData fromJson(widgetData) {
    return GuidesScreenData(
      title: widgetData["title"],
      widgets: widgetData['widgets'],
      themeType: widgetData['themeType'] != null
          ? EnumToString.fromString(CanvasTheme.values, widgetData['themeType'])
          : null,
    );
  }
}

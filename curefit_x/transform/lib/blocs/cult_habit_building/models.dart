import 'package:common/action/action_handler.dart' as action_handler;


class HabitBuildingFirstPage {
  String header;
  String habitsHeader;
  String editHeader;
  List<HabitCard> habitCardsSelected;
  List<HabitCard> habitCardsNotSelected;

  HabitBuildingFirstPage({
    required this.header,
    required this.habitsHeader,
    required this.editHeader,
    required this.habitCardsSelected,
    required this.habitCardsNotSelected
  });

  factory HabitBuildingFirstPage.fromJson(dynamic page) {
    print("reached from json");
    return HabitBuildingFirstPage(
        header: page["header"],
        habitsHeader: page["habitsHeader"],
        editHeader: page["editHeader"],
        habitCardsSelected: page["habitCardsSelected"].map<HabitCard>((card) => HabitCard.fromJson(card)).toList(),
        habitCardsNotSelected: page["habitCardsNotSelected"].map<HabitCard>((card) => HabitCard.fromJson(card)).toList()
    );
  }
}

class HabitCard {
  int id;
  String header;
  String subHeader;
  String secondaryText;
  List<String> backgroundColors;
  String? imageUrl;
  bool selected;
  action_handler.Action? action;
  Pages page;
  String? url;

  HabitCard({
    required this.id,
    required this.header,
    required this.subHeader,
    required this.secondaryText,
    required this.backgroundColors,
    this.action,
    this.imageUrl,
    required this.selected,
    required this.page,
    this.url
  });

  factory HabitCard.fromJson(dynamic habit) {
    print("habit");
    // List<String> colors = habit["backgroundColors"];
    // print(colors);
    return HabitCard(
        id: habit["id"],
        header: habit["header"],
        subHeader: habit["subHeader"],
        secondaryText: habit["secondaryHeader"],
        backgroundColors: habit["backgroundColors"].map<String>((dynamic obj) =>  obj.toString()).toList(),
        imageUrl: habit["imageUrl"],
        action: action_handler.Action.fromJson(habit['action']),
        selected: habit["isSelected"],
        page: Pages.fromJson(habit["page"]),
        url: habit["url"]
    );
  }
}


class Pages {
  String header;
  String habitID;
  String subHeader;
  Map<String,dynamic>? prevButton;
  Map<String,dynamic>? nextButton;
  bool selected;
  List<String> backgroundColors;
  String? type;
  List<int>? initial;
  List<int>? current;
  int? actionID;

  Pages({
    required this.header,
    required this.habitID,
    required this.subHeader,
    this.prevButton,
    this.nextButton,
    required this.selected,
    this.type,
    required this.backgroundColors,
    this.initial,
    this.current,
    this.actionID
  });

  factory Pages.fromJson(dynamic page) {
    print("reached page");
    print(page);
    return Pages(
        header: page["header"],
        subHeader: page["subHeader"],
        habitID: page['habitID'].toString(),
        prevButton: Map.castFrom(page["prevButton"]),
        nextButton: Map.castFrom(page["nextButton"]),
        selected: page["selected"],
        backgroundColors: page["backgroundColors"].map<String>((dynamic obj) =>  obj.toString()).toList(),
        type: page["type"],
        initial: page["initial"].map<int>((dynamic obj) => int.parse(obj.toString())).toList(),
        current: page["current"] != null ? page["current"].map<int>((dynamic obj) => int.parse(obj.toString())).toList() : [],
        actionID: page["actionId"]

    );
  }
}

class SaveDataPage {
  String header;
  String subHeader;
  String? url;
  action_handler.Action action;
  String? type;
  bool? showNextHabit;

  SaveDataPage({required this.header,required this.subHeader,this.url,required this.action,this.type,this.showNextHabit}) : super();

  factory SaveDataPage.fromJson(dynamic page) {
    return SaveDataPage(
        header: page["header"],
        subHeader: page["subHeader"],
        url: page["url"],
        action: action_handler.Action.fromJson(page['action']),
        type: page["type"],
        showNextHabit: page["showNextHabit"] ?? true
    );
  }
}

class TodayHabitPageButton {
  String title;
  action_handler.Action action;

  TodayHabitPageButton({required this.action,required this.title});

  factory TodayHabitPageButton.fromJson(dynamic button) {
    return TodayHabitPageButton(action: action_handler.Action.fromJson(button['action']), title: button['title']);
  }
}

class HabitBuildingData {
  final HabitBuildingFirstPage habitBuildingFirstPage;
  final SaveDataPage saveDataPage;
  final TodayHabitPageButton? todayHabitPageButton;
  HabitBuildingData({
    required this.habitBuildingFirstPage,
    required this.saveDataPage,
    this.todayHabitPageButton
  });

  factory HabitBuildingData.fromJson(dynamic payload) {
    return HabitBuildingData(
        habitBuildingFirstPage: HabitBuildingFirstPage.fromJson(payload["firstPage"]),
        saveDataPage: SaveDataPage.fromJson(payload["saveDataPage"]),
        todayHabitPageButton: payload["pageAction"] != null ?
                      TodayHabitPageButton.fromJson(payload["pageAction"]) : null
    );
  }
}

class HabitResponse {

  bool isSelected;
  String habitId;
  List<int> response;
  int? actionID;

  HabitResponse({
    required this.isSelected,
    required this.habitId,
    required this.response,
    this.actionID
  });
}

class EditHabitResponse {
  List<int> response;
  int id;

  EditHabitResponse({required this.response,required this.id});
}

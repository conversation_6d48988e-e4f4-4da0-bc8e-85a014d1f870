import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/cult_habit_building/cult_habit_building_event.dart';
import 'package:transform/blocs/cult_habit_building/cult_habit_building_state.dart';
import 'package:transform/blocs/cult_habit_building/models.dart';
import 'package:transform/network/cult_habit_building_repository.dart';


class HabitBuildingBloc extends Bloc <HabitBuildingEvent,HabitBuildingState> {
  HabitBuildingRepository repository;
  HabitBuildingData? data;
  List<HabitResponse>? habitResponse;

  HabitBuildingBloc({required this.repository}) : super(HabitBuildingIdleState()) {
    on<LoadHabitBuildingEvent>((event, emit) async {
      if (event.showLoader) {
        emit(HabitBuildingLoadingState());
      }
      try {
        final response = await repository.getHabitBuildingData({});
        print("response");
        print(response);
        if (response != null) {
          data = HabitBuildingData.fromJson(response);
          emit(HabitBuildingLoadedState(data:data!));
        } else {
          emit(const HabitBuildingFailedState("Report load failed"));
        }
      } catch (error) {
        if (kDebugMode) {
          print(error.toString());
        }
        emit(HabitBuildingFailedState(error.toString()));
      }
    });

    on<LoadHabitEditPagesEvent> ((event,emit) async {
      habitResponse = [];
      emit(HabitBuildingShowEditState(editPages: event.pages));
    });

    on<HabitBuildingShowMainPageEvent> ((event,emit) async {
      emit(HabitBuildingLoadedState(data:data!,shouldUpdateState: false));
    });
    on<ChangeHabitResponseDataEvent> ((event,emit) async {

    });

    on<SaveHabitResponseEvent> ((event,emit) async {
      emit(HabitBuildingLoadingState());
      print(event.responses.response);
      final response = await repository.setHabit(event.responses);
      if(response != null) {
        List<HabitCard> notSelectedHabitCards = data!.habitBuildingFirstPage!.habitCardsNotSelected;
        int index= -1;
        for(int i=0 ; i<notSelectedHabitCards.length ; i++) {
          HabitCard card = notSelectedHabitCards[i];
          if(card.page.habitID == event.responses.habitId) {
            data!.habitBuildingFirstPage.habitCardsNotSelected[i].selected = true;
            index = (i + 1) % notSelectedHabitCards.length;
            break;
          }
        }
        if(index>-1 && !notSelectedHabitCards[index].selected) {
          emit(HabitBuildingSaveHabitLoadedState(page: SaveDataPage.fromJson(response),card: notSelectedHabitCards[index]));
        } else {
          emit(HabitBuildingSaveHabitLoadedState(page: SaveDataPage.fromJson(response)));
        }
      }
    });
  }
}


import 'package:flutter/foundation.dart';
import 'package:transform/blocs/cult_habit_building/models.dart';


@immutable
abstract class HabitBuildingState {
  const HabitBuildingState(): super();
}

class HabitBuildingIdleState extends HabitBuildingState {
  @override
  String toString() => 'HabitBuildingIdleState';
}

class HabitBuildingShowEditState extends HabitBuildingState {

  final Pages editPages;

  @override
  String toString() => 'HabitBuildingShowEditState';
  const HabitBuildingShowEditState({required this.editPages}): super();
}

class HabitBuildingLoadingState extends HabitBuildingState {
  @override
  String toString() => "HabitBuildingLoadingState";
}

class HabitBuildingLoadedState extends HabitBuildingState {

  final HabitBuildingData data;
  final bool? shouldUpdateState;

  @override
  String toString() => "HabitBuildingLoadedState";
  const HabitBuildingLoadedState({required this.data,this.shouldUpdateState = false}): super();
}

class HabitBuildingFailedState extends HabitBuildingState {
  final String? error;
  @override
  String toString() => "HabitBuildingFailedState";
  const HabitBuildingFailedState(this.error): super();
}


class HabitBuildingShowMainPageState extends HabitBuildingState {
  @override
  String toString() => "HabitBuildingShowMainPageState";
  const HabitBuildingShowMainPageState(): super();
}


class HabitBuildingSaveHabitLoadingState extends HabitBuildingState {
  @override
  String toString() => "HabitBuildingSaveHabitLoadingState";
  const HabitBuildingSaveHabitLoadingState(): super();
}

class HabitBuildingSaveHabitLoadedState extends HabitBuildingState {
  final SaveDataPage page;
  HabitCard? card;
  @override
  String toString() => "HabitBuildingSaveHabitLoadedState";
  HabitBuildingSaveHabitLoadedState({required this.page,this.card}): super();
}



import 'package:transform/blocs/cult_habit_building/models.dart';

abstract class HabitBuildingEvent {}

class LoadHabitBuildingEvent extends HabitBuildingEvent {
  bool showLoader;
  LoadHabitBuildingEvent({this.showLoader = true}) : super();

  @override
  String toString() => "LoadHabitBuildingEvent";
}

class LoadHabitEditPagesEvent extends HabitBuildingEvent {
  Pages pages;
  LoadHabitEditPagesEvent({required this.pages}): super();

  @override
  String toString() => "LoadHabitEditPagesEvent";

}

class HabitBuildingShowMainPageEvent extends HabitBuildingEvent {
  bool shouldUpdateState;
  HabitBuildingShowMainPageEvent({required this.shouldUpdateState}): super();

  @override
  String toString() => "HabitBuildingShowMainPageEvent";

}

class ChangeHabitResponseDataEvent extends HabitBuildingEvent {

  int index;
  String habitID;
  String response;

  ChangeHabitResponseDataEvent({
    required this.index,
    required this.habitID,
    required this.response
  }): super();

  @override
  String toString() => "ChangeHabitResponseDataEvent";

}


class SaveHabitResponseEvent extends HabitBuildingEvent {

  HabitResponse responses;
  SaveHabitResponseEvent({
    required this.responses
  }): super();

  @override
  String toString() => "SaveHabitResponseEvent";

}

import 'package:flutter/cupertino.dart';

abstract class VideoGuideEvent {
  VideoGuideEvent() : super();
}

class LoadVideoGuideEvent extends VideoGuideEvent {
  LoadVideoGuideEvent() : super();

  @override
  String toString() => "LoadVideoGuideEvent";
}

class LoadVideoGuideExitEvent extends VideoGuideEvent {
  final String? contentId;
  final int? duration;

  LoadVideoGuideExitEvent({this.contentId, this.duration}) : super();

  @override
  String toString() => "LoadVideoGuideExitEvent";
}

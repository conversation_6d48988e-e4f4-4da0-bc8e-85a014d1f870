import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/video_guide/event.dart';
import 'package:transform/blocs/video_guide/state.dart';
import 'package:transform/network/video_guide_repository.dart';

class VideoGuideBloc extends Bloc<VideoGuideEvent, VideoGuideState> {
  final VideoGuideRepository repository;

  VideoGuideBloc(VideoGuideIdle videoGuideIdle, this.repository)
      : super(videoGuideIdle) {
    on<LoadVideoGuideExitEvent>((event, emit) async {
      await _makeApiCall(event, emit);
    });
  }

  Future<void> _makeApiCall(LoadVideoGuideExitEvent event, Emitter<VideoGuideState> emit) async {
    try {
      await repository.sendUserFulfillmentDetails(event.contentId, event.duration);
      emit(VideoGuideIdle());
    } catch (e) {
    }
  }
}

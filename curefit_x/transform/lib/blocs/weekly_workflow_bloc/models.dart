import 'package:common/ui/widget_builder.dart';

class WeeklyWorkflowWidgetData implements IWidgetData {
  WeeklyWorkflowWidgetData(
      this.widgetType, {
        this.selectedDate ="",
        this.workflowEntries = const [],
        this.widgetInfo,
        this.subCategoryCode,
        this.widgets,
        this.isCallScheduled = false,
      }) : super();

  final String selectedDate;
  final String? subCategoryCode;
  final List<WorkflowEntry> workflowEntries;
  final List<dynamic>? widgets;
  final bool isCallScheduled;

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  factory WeeklyWorkflowWidgetData.fromJson(
      WidgetTypes widgetType, dynamic json, WidgetInfo? widgetInfo) {
    return WeeklyWorkflowWidgetData(
      widgetType,
      selectedDate: json['selectedDate'] ?? "",
      workflowEntries: json['workflowEntries'] != null
          ? json['workflowEntries'].map<WorkflowEntry>((entry) {
        return WorkflowEntry.fromJson(entry);
      }).toList()
          : [],
      widgets: json['widgets'] ?? [],
      subCategoryCode: json['subCategoryCode'],
      isCallScheduled: json['callScheduled'] ?? false,
      widgetInfo: widgetInfo,
    );
  }
}

class WorkflowEntry {
  WorkflowEntry({
    this.date,
    this.epoch,
    this.id,
  });

  final String? date;
  final String? epoch;
  final String? id;

  static WorkflowEntry fromJson(json) {
    return WorkflowEntry(
      date: json["date"],
      epoch: json["epoch"],
      id: json['id'],
    );
  }
}

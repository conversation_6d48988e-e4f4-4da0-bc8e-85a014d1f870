import 'package:flutter/cupertino.dart';
import 'package:transform/blocs/weekly_workflow_bloc/models.dart';

@immutable
abstract class WeeklyWorkflowState {
  WeeklyWorkflowState() : super();
}

class WeeklyWorkflowIdleState extends WeeklyWorkflowState {
  @override
  String toString() => 'WeeklyWorkflowIdleState';
}

class WeeklyWorkflowLoading extends WeeklyWorkflowState {
  @override
  String toString() => 'WeeklyWorkflowLoading';
}

class WeeklyWorkflowLoaded extends WeeklyWorkflowState {
  final List<WorkflowEntry>? workflowEntries;
  final List<dynamic>? widgets;

  WeeklyWorkflowLoaded(this.workflowEntries, this.widgets);

  @override
  String toString() => 'WeeklyWorkflowLoaded';
}

class WeeklyWorkflowNotLoaded extends WeeklyWorkflowState {
  final Error? error;

  WeeklyWorkflowNotLoaded([this.error]) : super();

  @override
  String toString() => 'WeeklyWorkflowNotLoaded';
}

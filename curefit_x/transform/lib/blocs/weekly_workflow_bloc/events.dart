import 'package:transform/blocs/weekly_workflow_bloc/models.dart';

abstract class WeeklyWorkflowEvent {
  WeeklyWorkflowEvent() : super();
}

class WorkflowLoadEvent extends WeeklyWorkflowEvent {
  final String? epoch;
  final String? subCategoryCode;
  WorkflowLoadEvent({this.epoch,this.subCategoryCode});
}

class WorkflowPreLoadEvent extends WeeklyWorkflowEvent {
  final List<WorkflowEntry>? workflowEntries;
  final List<dynamic>? widgets;

  WorkflowPreLoadEvent({required this.workflowEntries, required this.widgets});
}

class WorkflowDateEvent extends WeeklyWorkflowEvent {
  final String selectedEpoch;

  WorkflowDateEvent({required this.selectedEpoch});
}

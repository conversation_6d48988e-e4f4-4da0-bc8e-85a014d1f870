import 'package:common/ui/widget_builder.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/weekly_workflow_bloc/events.dart';
import 'package:transform/blocs/weekly_workflow_bloc/models.dart';
import 'package:transform/blocs/weekly_workflow_bloc/state.dart';
import 'package:transform/network/weekly_workflow_repository.dart';
import 'package:enum_to_string/enum_to_string.dart';

class WeeklyWorkflowBloc
    extends Bloc<WeeklyWorkflowEvent, WeeklyWorkflowState> {
  WeeklyWorkflowRepository repository;
  List<WorkflowEntry>? workflowEntries;
  List<dynamic>? widgets;
  String? selectedEpoch;
  bool? callScheduled;

  WeeklyWorkflowBloc(
    WeeklyWorkflowState initialState,
    this.repository,
  ) : super(initialState) {
    on<WorkflowLoadEvent>((event, emit) async {
      await _mapLoadWorkflowState(event, emit);
    });
    on<WorkflowPreLoadEvent>((event, emit) async {
      this.workflowEntries = event.workflowEntries;
      this.widgets = event.widgets;
      emit(WeeklyWorkflowLoaded(this.workflowEntries!, this.widgets!));
    });

    on<WorkflowDateEvent>((event, emit) async {
      selectedEpoch = event.selectedEpoch;
    });
  }

  Future<void> _mapLoadWorkflowState(
      WorkflowLoadEvent event, Emitter<WeeklyWorkflowState> emit) async {
    emit(WeeklyWorkflowLoading());
    try {
      final response = await this
          .repository
          .getWorkflowList(epoch: event.epoch ?? "", subCategoryCode: event.subCategoryCode);
      if (response != null && response.length > 0) {
        final widgetData = response.first;
        WidgetTypes? widgetType = EnumToString.fromString(
            WidgetTypes.values, widgetData['widgetType']);
        WeeklyWorkflowWidgetData workflowWidgetData =
            WeeklyWorkflowWidgetData.fromJson(
                widgetType ?? WidgetTypes.WEEKLY_WORKFLOW_WIDGET,
                widgetData,
                null);
        this.workflowEntries = workflowWidgetData.workflowEntries;
        this.widgets = workflowWidgetData.widgets;
        emit(WeeklyWorkflowLoaded(this.workflowEntries!, this.widgets!));
      } else {
        emit(WeeklyWorkflowNotLoaded());
      }
    } catch (e) {
      emit(WeeklyWorkflowNotLoaded());
    }
  }
}

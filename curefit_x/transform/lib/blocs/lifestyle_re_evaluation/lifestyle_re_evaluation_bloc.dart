import 'package:common/network/lifestyle_re_evaluation_form_repository.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/lifestyle_re_evaluation/events.dart';
import 'package:transform/blocs/lifestyle_re_evaluation/models.dart';
import 'package:transform/blocs/lifestyle_re_evaluation/state.dart';

class LifestyleReEvaluationBloc
    extends Bloc<LifestyleReEvaluationEvent, LifestyleReEvaluationState> {
  final LifestyleReEvaluationFormRepository repository;

  LifestyleReEvaluationBloc(
      LifestyleReEvaluationIdleState idleState, this.repository)
      : super(idleState) {
    on<LifestyleReEvaluationSuccessLoadEvent>((event, emit) async {
      await _mapLoadLifestyleReEvaluationSuccessToState(event, emit);
    });
  }

  Future<void> _mapLoadLifestyleReEvaluationSuccessToState(
      LifestyleReEvaluationSuccessLoadEvent event,
      Emitter<LifestyleReEvaluationState> emit) async {
    emit(LifestyleReEvaluationSuccessLoadingState());
    try {
      final response = await this.repository.getReEvaluationResponse();
      emit(LifestyleReEvaluationSuccessState(
          LifestyleReEvaluationSuccessData.fromJSON(response)));
    } catch (e) {
      emit(LifestyleReEvaluationFailedState());
    }
  }
}

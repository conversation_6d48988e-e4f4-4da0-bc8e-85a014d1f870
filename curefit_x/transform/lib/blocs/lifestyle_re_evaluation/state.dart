import 'package:transform/blocs/lifestyle_re_evaluation/models.dart';

abstract class LifestyleReEvaluationState {
  LifestyleReEvaluationState() : super();
}


class LifestyleReEvaluationIdleState extends LifestyleReEvaluationState {
  @override
  String toString() => 'LifestyleReEvaluationStateIdleState';
}

class LifestyleReEvaluationSuccessLoadingState extends LifestyleReEvaluationState {
  @override
  String toString() => 'LifestyleReEvaluationSuccessLoadingState';
}

class LifestyleReEvaluationSuccessState extends LifestyleReEvaluationState {

  LifestyleReEvaluationSuccessData data;

  LifestyleReEvaluationSuccessState(this.data);

  @override
  String toString() => 'LifestyleReEvaluationSuccessState';
}


class LifestyleReEvaluationFailedState extends LifestyleReEvaluationState {

  LifestyleReEvaluationFailedState();

  @override
  String toString() => 'LifestyleReEvaluationFailedState';
}

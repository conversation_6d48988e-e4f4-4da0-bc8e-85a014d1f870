import 'package:common/ui/widget_builder.dart';
import 'package:common/action/action_handler.dart' as ActionHandler;

class LifestyleReEvaluationSuccessData {
  String heroImage;
  String title;
  String subTitle;
  ActionHandler.Action action;

  LifestyleReEvaluationSuccessData({ required this.heroImage, required this.title, required this.subTitle, required this.action });

  static LifestyleReEvaluationSuccessData fromJSON(widgetData) {
    return LifestyleReEvaluationSuccessData(
        heroImage: widgetData['heroImage'],
        title: widgetData['title'],
        subTitle: widgetData['subTitle'],
        action: ActionHandler.Action.fromJson(widgetData['action'])
    );
  }

}

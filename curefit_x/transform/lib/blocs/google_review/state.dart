import 'package:common/model/error_prop_model.dart';
import 'package:flutter/material.dart';
import 'package:transform/blocs/google_review/models.dart';

@immutable
abstract class GoogleReviewState {
  GoogleReviewState() : super();
}

class GoogleReviewIdle extends GoogleReviewState {
  @override
  String toString() => 'GoogleReviewIdle';
}

class GoogleReviewScreenLoaded extends GoogleReviewState {
  final GoogleReviewScreenData screenData;

  GoogleReviewScreenLoaded({required this.screenData}) : super();

  @override
  String toString() => 'GoogleReviewScreenLoaded';
}

class GoogleReviewScreenLoading extends GoogleReviewState {
  final GoogleReviewScreenData? screenData;

  GoogleReviewScreenLoading({this.screenData}) : super();
  @override
  String toString() => 'GoogleReviewScreenLoading';
}

class GoogleReviewScreenFailed extends GoogleReviewState {
  final ErrorInfo? errorInfo;

  GoogleReviewScreenFailed([this.errorInfo]) : super();
  @override
  String toString() => 'GoogleReviewScreenFailed';
}

import 'dart:io';

import 'package:common/model/error_prop_model.dart';
import 'package:common/network/client.dart';
import 'package:common/network/client_repository.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/google_review/events.dart';
import 'package:transform/blocs/google_review/models.dart';
import 'package:transform/blocs/google_review/state.dart';

class GoogleReviewBloc extends Bloc<GoogleReviewEvent, GoogleReviewState> {
  final CoachClientRepository repository;
  GoogleReviewScreenData screenData = GoogleReviewScreenData();
  String centerId = "";
  String offset = "";
  String limit = "10";
  String subCategoryCode = "";

  GoogleReviewBloc(this.repository) : super(GoogleReviewIdle()) {
    on<GoogleReviewScreenLoadEvent>((event, emit) async {
      await _mapReviewLoadToState(event, emit);
    });
    on<FetchGoogleReviewEvent>((event, emit) async {
      await _mapGoogleReviewLoadToState(event, emit);
    });
  }

  void refresh(String centerId) {
    this.add(GoogleReviewScreenLoadEvent(
        subCategoryCode: subCategoryCode,
        centerId: centerId,
        limit: limit,
        offset: offset));
  }

  Future<void> _mapReviewLoadToState(GoogleReviewScreenLoadEvent event,
      Emitter<GoogleReviewState> emit) async {
    emit(GoogleReviewScreenLoading());
    try {
      centerId = event.centerId ?? "";
      offset = event.offset ?? "";
      limit = event.limit ?? "";
      subCategoryCode = event.subCategoryCode ?? "";
      final response =
          await this.repository.getPageData('v2/transform/review/pagination', {
        "subCategoryCode": event.subCategoryCode ?? "",
        "centerId": event.centerId ?? "",
        "offset": event.offset ?? "",
        "limit": event.limit ?? "",
      });
      if (response != null) {
        screenData = GoogleReviewScreenData.fromJson(response);
        emit(GoogleReviewScreenLoaded(screenData: screenData));
      } else {
        emit(GoogleReviewScreenFailed(UnknownError(
            failed: true, responseCode: "404", callbackFunction: refresh)));
      }
    } on NetworkException catch (exception) {
      emit(GoogleReviewScreenFailed(NetworkError(
          failed: true,
          responseCode: '${(exception).statusCode}',
          callbackFunction: refresh)));
    } on SocketException {
      emit(GoogleReviewScreenFailed(
          NoInternetError(failed: true, callbackFunction: refresh)));
    } catch (e) {
      emit(GoogleReviewScreenFailed(
          UnknownError(failed: true, callbackFunction: refresh)));
    }
  }

  Future<void> _mapGoogleReviewLoadToState(
      FetchGoogleReviewEvent event, Emitter<GoogleReviewState> emit) async {
    try {
      int offsetVal = 0;
      if (offset.isNotEmpty && limit.isNotEmpty) {
        offsetVal = int.parse(offset) + int.parse(limit) + 1;
        offset = offsetVal.toString();
      }
      final response =
          await this.repository.getPageData('v2/transform/review/pagination', {
        "subCategoryCode": subCategoryCode ?? "",
        "centerId": centerId ?? "",
        "offset": offsetVal.toString(),
        "limit": limit ?? "",
      });
      if (response != null) {
        GoogleReviewScreenData data = GoogleReviewScreenData.fromJson(response);
        screenData.reviewCards?.addAll(data.reviewCards ?? []);
        screenData.isLimit = data.isLimit;
        emit(GoogleReviewScreenLoaded(screenData: screenData));
      } else {
        emit(GoogleReviewScreenFailed(UnknownError(
            failed: true, responseCode: "404", callbackFunction: refresh)));
      }
    } on NetworkException catch (exception) {
      emit(GoogleReviewScreenFailed(NetworkError(
          failed: true,
          responseCode: '${(exception).statusCode}',
          callbackFunction: refresh)));
    } on SocketException {
      emit(GoogleReviewScreenFailed(
          NoInternetError(failed: true, callbackFunction: refresh)));
    } catch (e) {
      emit(GoogleReviewScreenFailed(
          UnknownError(failed: true, callbackFunction: refresh)));
    }
  }
}

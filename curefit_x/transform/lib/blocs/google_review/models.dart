import 'package:common/action/action_handler.dart';
import 'package:transform/blocs/clp/models.dart';

class GoogleReviewScreenData {
  GoogleReviewScreenData({
    this.title,
    this.reviewCards,
    this.widgets,
    this.action,
    this.isLimit = false,
  });

  final String? title;
  final List<ReviewCardData>? reviewCards;
  final List? widgets;
  final Action? action;
  late bool isLimit;

  static GoogleReviewScreenData fromJson(widget) {
    return GoogleReviewScreenData(
        title: widget['title'] != null ? widget['title'] : null,
        action:
            widget['action'] != null ? Action.fromJson(widget['action']) : null,
        reviewCards: widget['reviewCards'] != null
            ? widget['reviewCards']
                .map<ReviewCardData>((card) => ReviewCardData.fromJson(card))
                .toList()
            : null,
        widgets: widget['widgets'],
        isLimit: widget['isLimit'] ?? false);
  }
}

abstract class GoogleReviewEvent {
  GoogleReviewEvent() : super();
}

class GoogleReviewScreenLoadEvent extends GoogleReviewEvent {
  final String? subCategoryCode;
  final String? centerId;
  final String? offset;
  final String? limit;

  GoogleReviewScreenLoadEvent({this.subCategoryCode, this.centerId, this.limit, this.offset}) : super();

  @override
  String toString() => 'GoogleReviewScreenLoadEvent';
}

class FetchGoogleReviewEvent extends GoogleReviewEvent {
  final String? subCategoryCode;
  final String? centerId;
  final String? offset;
  final String? limit;

  FetchGoogleReviewEvent({this.subCategoryCode, this.centerId, this.limit, this.offset}) : super();

  @override
  String toString() => 'FetchGoogleReviewEvent';
}

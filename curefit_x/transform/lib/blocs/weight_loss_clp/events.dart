abstract class WeightLossClpEvent {
  WeightLossClpEvent() : super();
}

class LoadWeightLossClpEvent extends WeightLossClpEvent {
  final bool showLoader;

  LoadWeightLossClpEvent({this.showLoader = false}) : super();

  @override
  String toString() {
    return "LoadWeightLossClpEvent";
  }
}

class ResetWeightLossClpEvent extends WeightLossClpEvent {
  @override
  String toString() => 'ResetWeightLossClpEvent';
}

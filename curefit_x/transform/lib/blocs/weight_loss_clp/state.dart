import 'package:common/model/error_prop_model.dart';
import 'models.dart';

abstract class WeightLossClpState {
  WeightLossClpState() : super();
}

class WeightLossClpIdleState extends WeightLossClpState {
  @override
  String toString() => 'WeightLossClpIdleState';
}

class WeightLossClpLoadingState extends WeightLossClpState {
  final WeightLossClpData? screenData;
  final bool showLoader;

  WeightLossClpLoadingState({this.screenData, this.showLoader = true});

  @override
  String toString() => 'WeightLossClpLoadingState';
}

class WeightLossClpLoadedState extends WeightLossClpState {
  final WeightLossClpData screenData;

  WeightLossClpLoadedState(
      {required this.screenData})
      : super();

  @override
  String toString() => 'WeightLossClpLoadedState';
}

class WeightLossClpErrorState extends WeightLossClpState {
  final ErrorInfo? errorInfo;

  WeightLossClpErrorState([this.errorInfo]) : super();

  @override
  String toString() => 'WeightLossClpErrorState';
}

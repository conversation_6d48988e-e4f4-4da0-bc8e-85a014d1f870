import 'package:common/action/action_handler.dart';
import 'package:common/ui/widget_builder.dart';

class WeightLossClpData {
  WeightLossClpData({
    required this.widgets,
    this.title,
    this.rightActionButton,
  });

  final String? title;
  final List<dynamic>? widgets;
  final Action? rightActionButton;

  static WeightLossClpData fromJson(json) {
    return WeightLossClpData(
      title: json['name'],
      widgets: json['body'],
      rightActionButton: json['rightBarButton'] != null
          ? Action.fromJson(json['rightBarButton'])
          : null,
    );
  }
}

class SKUCardWidgetV3Data implements IWidgetData {
  SKUCardWidgetV3Data(
    this.widgetType, {
    this.title,
    this.subtitle,
    this.titleImageUrl,
    this.imageUrl,
    this.offerings,
    this.footerTitle,
    this.footerPrefix,
    this.footerSuffix,
    this.action,
    this.isActivePack = false,
    this.activePackText,
    this.widgetInfo,
  }) : super();

  final String? title;
  final String? subtitle;
  final String? titleImageUrl;
  final String? imageUrl;
  final List<String>? offerings;
  final String? footerTitle;
  final String? footerPrefix;
  final String? footerSuffix;
  final bool isActivePack;
  final String? activePackText;
  final Action? action;

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  factory SKUCardWidgetV3Data.fromJson(
      WidgetTypes widgetType, dynamic json, WidgetInfo? widgetInfo) {
    return SKUCardWidgetV3Data(
      widgetType,
      title: json['title'],
      subtitle: json['subtitle'],
      titleImageUrl: json['titleImageUrl'],
      imageUrl: json['imageUrl'],
      offerings: json['offerings'] != null
          ? json['offerings'].map<String>((offering) {
              return offering.toString();
            }).toList()
          : null,
      footerTitle: json['footerTitle'],
      footerPrefix: json['footerPrefix'],
      footerSuffix: json['footerSuffix'],
      isActivePack: json['isActivePack'] ?? false,
      activePackText: json['activePackText'],
      action: json['action'] != null ? Action.fromJson(json['action']) : null,
      widgetInfo: widgetInfo,
    );
  }
}

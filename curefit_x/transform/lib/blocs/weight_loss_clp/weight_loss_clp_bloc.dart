import 'dart:io';

import 'package:bloc/bloc.dart';
import 'package:common/model/error_prop_model.dart';
import 'package:common/network/client.dart';
import 'package:common/network/client_repository.dart';
import 'package:transform/blocs/weight_loss_clp/events.dart';
import 'package:transform/blocs/weight_loss_clp/models.dart';
import 'package:transform/blocs/weight_loss_clp/state.dart';

class WeightLossClpBloc extends Bloc<WeightLossClpEvent, WeightLossClpState> {
  final String pageId = "weight_loss";
  final CoachClientRepository repository;
  WeightLossClpData screenData = WeightLossClpData(widgets: []);

  WeightLossClpBloc(this.repository) : super(WeightLossClpIdleState()) {
    on<LoadWeightLossClpEvent>((event, emit) async {
      await _mapLoadWeightLossClp(event, emit);
    });
    on<ResetWeightLossClpEvent>((event, emit) async {
      screenData = WeightLossClpData(widgets: []);
      emit(WeightLossClpLoadedState(screenData: screenData));
    });
  }

  void refresh() {
    this.add(LoadWeightLossClpEvent());
  }

  Future<void> _mapLoadWeightLossClp(
      LoadWeightLossClpEvent event, Emitter<WeightLossClpState> emit) async {
    emit(WeightLossClpLoadingState(
        screenData: screenData, showLoader: event.showLoader));
    try {
      final response = await this.repository.getPageData('v2/page/$pageId', {});
      if (response != null && response['body'] != null) {
        screenData = WeightLossClpData.fromJson(response);
        emit(WeightLossClpLoadedState(screenData: screenData));
      } else {
        emit(WeightLossClpErrorState(UnknownError(
            failed: true, responseCode: "404", callbackFunction: refresh)));
      }
    } on NetworkException catch (exception) {
      emit(WeightLossClpErrorState(NetworkError(
          failed: true,
          responseCode: '${(exception).statusCode}',
          callbackFunction: refresh)));
    } on SocketException {
      emit(WeightLossClpErrorState(
          NoInternetError(failed: true, callbackFunction: refresh)));
    } catch (e) {
      emit(WeightLossClpErrorState(
          UnknownError(failed: true, callbackFunction: refresh)));
    }
  }
}

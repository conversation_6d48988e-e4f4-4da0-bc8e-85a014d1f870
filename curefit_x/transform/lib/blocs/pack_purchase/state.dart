import 'package:meta/meta.dart';
import 'package:transform/blocs/pack_purchase/models.dart';

@immutable
abstract class PackPurchaseState {
  PackPurchaseState() : super();
}

class PackPurchaseIdleState extends PackPurchaseState {
  @override
  String toString() => 'IdleState';
}

class PackPurchaseLoading extends PackPurchaseState {
  @override
  String toString() => 'PackPurchaseLoading';
}

class PackPurchaseLoaded extends PackPurchaseState {
  final PackPurchasePage packPurchasePage;

  PackPurchaseLoaded({required this.packPurchasePage})
      : assert(packPurchasePage != null),
        super();

  @override
  String toString() => 'PackPurchaseLoaded';
}

class PackPurchaseNotLoaded extends PackPurchaseState {
  final String? error;

  PackPurchaseNotLoaded([this.error]) : super();

  @override
  String toString() => 'PackPurchaseNotLoaded';
}

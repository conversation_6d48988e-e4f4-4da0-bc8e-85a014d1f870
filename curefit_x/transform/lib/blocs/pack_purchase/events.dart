import 'package:meta/meta.dart';
import 'package:transform/factory/widget_data.dart';

abstract class PackPurchaseEvent {
  PackPurchaseEvent() : super();
}

class LoadPackPurchaseEvent extends PackPurchaseEvent {
  LoadPackPurchaseEvent() : super();

  @override
  String toString() => 'LoadPackPurchaseEvent';
}

class SelectPackEvent extends PackPurchaseEvent {
  final Pack pack;

  SelectPackEvent(this.pack) : super();

  @override
  String toString() => 'SelectPackEvent';
}

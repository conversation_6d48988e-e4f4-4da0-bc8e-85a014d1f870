import 'package:common/action/action_handler.dart' as ActionHandler;
import 'package:common/ui/widget_builder.dart';
import 'package:common/data/cf_text_data.dart';
import 'package:intl/intl.dart';

class PackPurchasePage {
  PackPurchasePage(this.action, this.widgets);

  ActionHandler.Action? action;
  List<dynamic>? widgets;
}

class WidgetHeader {
  WidgetHeader({this.subtitle, this.title, this.icon, this.background});

  String? title;
  String? subtitle;
  String? icon;
  String? background;
}

class PackPrice {
  PackPrice(this.mrp, this.listingPrice, this.currency, this.showPriceCut);

  String mrp;
  bool showPriceCut;
  String listingPrice;
  String currency;
}

class ProductOffer {
  ProductOffer(this.title, this.iconType);

  String? title;
  String? iconType;
}

class ProductActionOffers {
  ActionHandler.Action? action;
  String? prefix;
  String? postfix;
  String? iconType;

  ProductActionOffers(this.action, this.prefix, this.postfix, this.iconType);
}

class CultPackDetailWidgetBodyData {
  CultPackDetailWidgetBodyData({
    required this.title,
    required this.subtitle,
    required this.textColorVariant,
    required this.price,
    required this.priceMeta,
    required this.action,
    this.offers,
    this.actionOffers,
    this.productId,
    this.priceBreakup,
    this.additionalPrice,
  });

  String title;
  String subtitle;
  String textColorVariant;
  PackPrice price;
  String priceMeta;
  String? productId;
  ActionHandler.Action action;
  List<ProductOffer>? offers;
  List<ProductActionOffers>? actionOffers;
  CFTextData? additionalPrice;
  dynamic priceBreakup;

  static CultPackDetailWidgetBodyData fromJson(dynamic json) {
    var colorMap = {
      'blue': '#7BF7FF',
      'green': '#9EFDDB',
      'lightBlue': '#51B9CF',
      'pink': "#fc55da",
      "orange": "#ff6522"
    };
    var numFormat = new NumberFormat();
    return CultPackDetailWidgetBodyData(
      title: json['duration'],
      subtitle: json['durationUnit'],
      textColorVariant: (colorMap[json['textGradientVariant']] ?? '#ffc0cbff'),
      price: PackPrice(
          json['costPrice'] ?? "",
          json['offerPrice'],
          numFormat.simpleCurrencySymbol(json['currencyUnit']),
          json['costPrice'] != json['offerPrice']),
      priceMeta: json['perMonthPrice'],
      action: ActionHandler.Action.fromJson(json['action']),
      offers: json['offers'] != null
          ? json['offers']
              .map<ProductOffer>(
                  (offer) => ProductOffer(offer['title'], offer['icon']))
              .toList()
          : [],
      actionOffers: json['actionOffers'] != null
          ? json['actionOffers']
              .map<ProductActionOffers>((actionOffer) => ProductActionOffers(
                    ActionHandler.Action.fromJson(actionOffer['action']),
                    actionOffer['prefix'],
                    actionOffer['postfix'],
                    actionOffer['icon'],
                  ))
              .toList()
          : [],
      priceBreakup: json['priceBreakup'],
      productId: json['productId'],
      additionalPrice: json['additionalPrice'] != null
          ? CFTextData.fromJson(json['additionalPrice'])
          : null,
    );
  }
}

class CultPackBrowseWidgetItemData {
  CultPackBrowseWidgetItemData(
      this.packs, this.header, this.simplifyHeader, this.topSpacing);

  WidgetHeader header;
  bool? simplifyHeader;
  String? topSpacing;
  List<CultPackDetailWidgetBodyData> packs;

  factory CultPackBrowseWidgetItemData.fromJson(widget) {
    var colorMap = {
      'blue': '#7BF7FF',
      'green': '#9EFDDB',
      'lightBlue': '#51B9CF',
      'pink': "#fc55da",
      "orange": "#ff6522"
    };
    var numFormat = new NumberFormat();

    return CultPackBrowseWidgetItemData(
        widget['packs'] != null
            ? widget['packs']
                .map<CultPackDetailWidgetBodyData>((e) =>
                    CultPackDetailWidgetBodyData(
                      title: e['duration'],
                      subtitle: e['durationUnit'],
                      textColorVariant:
                          (colorMap[e['textGradientVariant']] ?? '#ffc0cbff'),
                      price: PackPrice(
                          e['costPrice'] ?? "",
                          e['offerPrice'],
                          numFormat.simpleCurrencySymbol(e['currencyUnit']),
                          e['costPrice'] != e['offerPrice']),
                      priceMeta: e['perMonthPrice'],
                      action: ActionHandler.Action.fromJson(e['action']),
                      offers: e['offers'] != null
                          ? e['offers']
                              .map<ProductOffer>((offer) =>
                                  ProductOffer(offer['title'], offer['icon']))
                              .toList()
                          : [],
                      actionOffers: e['actionOffers'] != null
                          ? e['actionOffers']
                              .map<ProductActionOffers>(
                                  (actionOffer) => ProductActionOffers(
                                        ActionHandler.Action.fromJson(
                                            actionOffer['action']),
                                        actionOffer['prefix'],
                                        actionOffer['postfix'],
                                        actionOffer['icon'],
                                      ))
                              .toList()
                          : [],
                      priceBreakup: e['priceBreakup'],
                      additionalPrice: e['additionalPrice'] != null
                          ? CFTextData.fromJson(e['additionalPrice'])
                          : null,
                    ))
                .toList()
            : [],
        WidgetHeader(
            background: widget['header']['headerImage'],
            title: widget['header']['headerTitle'],
            subtitle: widget['header']['headerSubTitle']),
        widget["simplifyHeader"],
        widget["topSpacing"]);
  }
}

class ExpandedMembershipPackInfo {
  final String? headerTitle;
  final String? headerSubTitle;
  final String? headerImage;
  final double? headerImageAspectRatio;

  ExpandedMembershipPackInfo(
      {this.headerTitle,
      this.headerSubTitle,
      this.headerImage,
      this.headerImageAspectRatio});

  factory ExpandedMembershipPackInfo.fromJson(payload) {
    return ExpandedMembershipPackInfo(
        headerTitle: payload['headerTitle'],
        headerImage: payload['headerImage'],
        headerSubTitle: payload['headerSubTitle'],
        headerImageAspectRatio: payload['headerImageAspectRatio']);
  }
}

class CultPackBrowseWidgetData implements IWidgetData {
  CultPackBrowseWidgetData({
    required this.widgetType,
    required this.items,
    this.widgetInfo,
    this.expandedMembershipPackInfo,
  });

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  List<CultPackBrowseWidgetItemData> items;

  final ExpandedMembershipPackInfo? expandedMembershipPackInfo;

  factory CultPackBrowseWidgetData.fromJson(widget,
      {required WidgetTypes widgetType, required WidgetInfo widgetInfo}) {
    return CultPackBrowseWidgetData(
        expandedMembershipPackInfo: widget['expandedMembershipPack'] != null
            ? ExpandedMembershipPackInfo.fromJson(
                widget['expandedMembershipPack'])
            : null,
        widgetType: widgetType,
        widgetInfo: widgetInfo,
        items: widget['items']
            .map<CultPackBrowseWidgetItemData>(
                (e) => CultPackBrowseWidgetItemData.fromJson(e))
            .toList());
  }
}

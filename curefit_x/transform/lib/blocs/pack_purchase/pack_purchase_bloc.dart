import 'dart:developer';

import 'package:common/network/client.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/pack_purchase/events.dart';
import 'package:transform/blocs/pack_purchase/state.dart';
import 'package:transform/blocs/pack_purchase/models.dart';
import 'package:common/network/pack_purchase_repository.dart';
import 'package:common/action/action_handler.dart' as ActionHandler;
import 'package:transform/factory/widget_data.dart';

class PackPurchaseBloc extends Bloc<PackPurchaseEvent, PackPurchaseState> {
  final PackPurchaseRepository repository;

  Pack? selectedPack;

  PackPurchaseBloc(PackPurchaseState initialState, this.repository)
      : super(initialState) {
    on<LoadPackPurchaseEvent>((event, emit) async {
      await _mapPackPurchaseDetails(event, emit);
    });

    on<SelectPackEvent>((event, emit) async {
      this.selectedPack = event.pack;
    });
  }

  Future<void> _mapPackPurchaseDetails(
      PackPurchaseEvent event, Emitter<PackPurchaseState> emit) async {
    try {
      selectedPack = null;
      final response = await this
          .repository
          .getPacks("TRANSFORM_WL_3_MONTH", "TRANSFORM_WL");
      if (response == null) {
        emit(PackPurchaseNotLoaded(throw new Error()));
      }
      final detail = response;
      log(detail['widgets'].toString());
      final PackPurchasePage packPurchasePage = PackPurchasePage(
          detail['actions'].length > 0
              ? ActionHandler.Action.fromJson(detail['actions'][0])
              : null,
          detail['widgets']);
      emit(PackPurchaseLoaded(packPurchasePage: packPurchasePage));
    } on NetworkException catch (exception) {
      emit(PackPurchaseNotLoaded(exception.subTitle));
    } catch (e) {
      emit(PackPurchaseNotLoaded("Something went wrong"));
    }
  }
}

import 'package:common/action/action_handler.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:enum_to_string/enum_to_string.dart';

class ChallengesScreenData {
  ChallengesScreenData({
    this.title,
    this.themeType,
    this.widgets,
  });

  final String? title;
  final List<dynamic>? widgets;
  final CanvasTheme? themeType;

  static ChallengesScreenData fromJson(json) {
    return ChallengesScreenData(
      title: json["title"],
      widgets: json['widgets'],
      themeType: json['themeType'] != null
          ? EnumToString.fromString(CanvasTheme.values, json['themeType'])
          : null,
    );
  }
}

class PodiumWidgetData implements IWidgetData {
  final List<PodiumItem>? podiumItems;

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  PodiumWidgetData(
    this.widgetType, {
    this.podiumItems,
    this.widgetInfo,
  }) : super();

  static PodiumWidgetData fromJson(WidgetTypes widgetType, dynamic json,
      {WidgetInfo? widgetInfo}) {
    return PodiumWidgetData(
      widgetType,
      podiumItems: json['podiumItems'] != null
          ? json['podiumItems']
              .map<PodiumItem>((item) => PodiumItem.fromJson(item))
              .toList()
          : null,
      widgetInfo: json['widgetInfo'],
    );
  }
}

class PodiumItem {
  final double leftCornerRadius;
  final double rightCornerRadius;
  final double? height;
  final double? width;
  final double? standHeight;
  final double? rayHeight;
  final String standColor;
  final List<String> gradientColors;
  final String? userName;
  final String? userProfilePicUrl;
  final String? rankTag;
  final String? crownTag;

  PodiumItem({
    this.rightCornerRadius =0.0,
    this.leftCornerRadius =0.0,
    this.height,
    this.width,
    this.standHeight,
    this.rayHeight,
    this.standColor = "#FFFFFF",
    this.gradientColors = const ["#FFFFFF", "#FFFFFF"],
    this.userName,
    this.crownTag,
    this.rankTag,
    this.userProfilePicUrl,
  });

  static PodiumItem fromJson(dynamic json) {
    return PodiumItem(
      leftCornerRadius: json['leftCornerRadius'] ?? 0.0,
      rightCornerRadius: json['rightCornerRadius'] ?? 0.0,
      height: json['height'],
      width: json['width'],
      standHeight: json['standHeight'],
      rayHeight: json['rayHeight'],
      standColor: json['standColor'] ?? "#FFFFFF",
      gradientColors: json['gradientColors'] != null
          ? json['gradientColors']
              .map<String>((item) => item.toString())
              .toList()
          : ["#FFFFFF", "#FFFFFF"],
      userName: json['userName'],
      crownTag: json['crownTag'],
      rankTag: json['rankTag'],
      userProfilePicUrl: json['userProfilePicUrl'],
    );
  }
}

class ChallengersListWidgetData implements IWidgetData {
  final String? title;
  final String? subtitle;
  final ChallengerCardData? userCard;
  final List<ChallengerCardData>? challengersList;
  final Action? action;

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  ChallengersListWidgetData(
    this.widgetType, {
    this.title,
    this.subtitle,
    this.userCard,
    this.challengersList,
    this.action,
    this.widgetInfo,
  }) : super();

  static ChallengersListWidgetData fromJson(
      WidgetTypes widgetType, dynamic json,
      {WidgetInfo? widgetInfo}) {
    return ChallengersListWidgetData(
      widgetType,
      title: json['title'],
      subtitle: json['subtitle'],
      userCard: json['userCard'] != null
          ? ChallengerCardData.fromJson(json['userCard'])
          : null,
      challengersList: json['challengersList'] != null
          ? json['challengersList']
              .map<ChallengerCardData>(
                  (item) => ChallengerCardData.fromJson(item))
              .toList()
          : null,
      action: json['action'] != null ? Action.fromJson(json['action']) : null,
      widgetInfo: json['widgetInfo'],
    );
  }
}

class ChallengerCardData {
  final String? rank;
  final String? profileImageUrl;
  final String? title;
  final String? subtitle;
  final String? tagUrl;
  final String? emoji;
  final bool increaseInRank;
  final bool decreaseInRank;

  ChallengerCardData({
    this.rank,
    this.profileImageUrl,
    this.title,
    this.subtitle,
    this.tagUrl,
    this.emoji,
    this.increaseInRank = false,
    this.decreaseInRank = false,
  });

  static ChallengerCardData fromJson(dynamic json) {
    return ChallengerCardData(
      rank: json['rank'],
      profileImageUrl: json['profileImageUrl'],
      title: json['title'],
      subtitle: json['subtitle'],
      tagUrl: json['tagUrl'],
      emoji: json['emoji'],
      increaseInRank: json['increaseInRank'] ?? false,
      decreaseInRank: json['decreaseInRank'] ?? false,
    );
  }
}

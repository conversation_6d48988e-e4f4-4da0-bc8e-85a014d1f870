import 'package:common/network/client.dart';

import 'models.dart';

abstract class ChallengesBlocState {
  ChallengesBlocState() : super();
}

class ChallengesBlocIdleState extends ChallengesBlocState {
  @override
  String toString() {
    return "ChallengesBlocIdleState";
  }
}

class ChallengesBlocLoadingState extends ChallengesBlocState {
  @override
  String toString() {
    return "ChallengesBlocLoadingState";
  }
}

class ChallengesBlocLoadedState extends ChallengesBlocState {
  final ChallengesScreenData challengesScreenData;

  ChallengesBlocLoadedState({required this.challengesScreenData}) : super();

  @override
  String toString() {
    return "ChallengesBlocLoadedState";
  }
}

class ChallengesBlocErrorState extends ChallengesBlocState {
  final NetworkException? exception;

  ChallengesBlocErrorState(this.exception) : super();

  @override
  String toString() {
    return "ChallengesBlocErrorState";
  }
}

import 'package:common/network/client.dart';
import 'package:common/network/client_repository.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/challenges/events.dart';
import 'package:transform/blocs/challenges/models.dart';
import 'package:transform/blocs/challenges/state.dart';

class ChallengesBloc extends Bloc<ChallengesBlocEvent, ChallengesBlocState> {
  final CoachClientRepository repository;

  ChallengesBloc(
      ChallengesBlocIdleState challengesBlocIdleState, this.repository)
      : super(challengesBlocIdleState) {
    on<LoadChallengesContentEvent>((event, emit) async {
      await _mapChallengesDataToState(event, emit);
    });
  }

  Future<void> _mapChallengesDataToState(LoadChallengesContentEvent event,
      Emitter<ChallengesBlocState> emit) async {
    try {
      emit(ChallengesBlocLoadingState());
      final response = await this.repository.getPageData(
          'v2/transform/challenges', {
        "challengeId": event.challengeId,
        "subCategoryCode": event.subCategoryCode ?? ""
      });
      if (response != null) {
        emit(ChallengesBlocLoadedState(
            challengesScreenData: ChallengesScreenData.fromJson(response)));
      } else {
        emit(ChallengesBlocErrorState(
            throw new Exception("Something went wrong")));
      }
    } on NetworkException catch (exception) {
      emit(ChallengesBlocErrorState(exception));
    } catch (e) {
      emit(ChallengesBlocErrorState(
          throw new Exception("Something went wrong")));
    }
  }
}

import 'package:flutter/material.dart';
import 'package:transform/blocs/clp/models.dart';

abstract class HabitEvent {
  HabitEvent() : super();
}

class HabitsLoadEvent extends HabitEvent {
  final String? epoch;
  final String? tenant;
  HabitsLoadEvent({this.epoch,this.tenant});
}

class HabitsPreLoadEvent extends HabitEvent {
  final List<HabitCard>? habitCards;

  HabitsPreLoadEvent({required this.habitCards});
}

class UpdateHabitEvent extends HabitEvent {
  final String habitId;
  final String status;

  UpdateHabitEvent({required this.habitId, required this.status});
}

class HabitsDateEvent extends HabitEvent {
  final String selectedDate;

  HabitsDateEvent({required this.selectedDate});
}

class ResetHabitCardsEvent extends HabitEvent {
   ResetHabitCardsEvent();
}

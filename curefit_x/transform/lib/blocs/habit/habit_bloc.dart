import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/clp/models.dart';
import 'package:transform/blocs/habit/events.dart';
import 'package:transform/blocs/habit/state.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:transform/network/habit_repository.dart';
import 'package:enum_to_string/enum_to_string.dart';

class HabitBloc extends Bloc<HabitEvent, HabitState> {
  HabitRepository repository;
  List<HabitCard>? habitCards;
  String? selectedDate;

  HabitBloc(
    HabitState initialState,
    this.repository,
  ) : super(initialState) {
    on<HabitsLoadEvent>((event, emit) async {
      await _mapLoadHabitState(event, emit);
    });
    on<HabitsPreLoadEvent>((event, emit) async {
      this.habitCards = event.habitCards;
      emit(HabitsLoaded(this.habitCards!));
    });

    on<UpdateHabitEvent>((event, emit) async {
      if (this.habitCards != null && this.habitCards!.isNotEmpty) {
        List<HabitCard> cards = [];
        habitCards!.forEach((element) {
          if (element.id == event.habitId) {
            element.response =
                EnumToString.fromString(UserResponse.values, event.status)!;
          }
          cards.add(element);
        });
        this.habitCards = cards;
        emit(HabitsLoaded(this.habitCards!));
      }
    });

    on<HabitsDateEvent>((event,emit) async {
      selectedDate = event.selectedDate;
    });

    on<ResetHabitCardsEvent> ((event,emit) async {
      habitCards = null;
      selectedDate = null;
    });
  }

  Future<void> _mapLoadHabitState(
      HabitsLoadEvent event, Emitter<HabitState> emit) async {
    emit(HabitsLoading());
    try {
      final response =
          await this.repository.getHabitsList(epoch: event.epoch ?? "",tenant: event.tenant);
      if (response != null && response.length > 0) {
        final widgetData = response.first;
        WidgetTypes? widgetType = EnumToString.fromString(
            WidgetTypes.values, widgetData['widgetType']);
        HabitCardWidgetData habitCardWidgetData = HabitCardWidgetData.fromJson(
            widgetData,
            widgetType: widgetType ?? WidgetTypes.HABIT_CARD_WIDGET);
        this.habitCards = habitCardWidgetData.habitCards;
        emit(HabitsLoaded(this.habitCards!));
      } else {
        emit(HabitsNotLoaded());
      }
    } catch (e) {
      emit(HabitsNotLoaded());
    }
  }
}

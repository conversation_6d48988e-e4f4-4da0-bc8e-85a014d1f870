import 'package:common/network/client.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/clp/models.dart';
import 'package:transform/blocs/habit/events.dart';
import 'package:transform/blocs/habit/habit_bloc.dart';
import 'package:transform/blocs/habit/state.dart';
import 'package:transform/network/habit_repository.dart';
import 'package:enum_to_string/enum_to_string.dart';

class UpdateHabitBloc extends Bloc<HabitEvent, UpdateHabitState> {
  HabitRepository repository;
  HabitBloc habitBloc;

  UpdateHabitBloc({required this.repository, required this.habitBloc})
      : super(UpdateHabitIdleState()) {
    on<UpdateHabitEvent>((event, emit) async {
      try {
        final response = await this
            .repository
            .updateHabit(event.habitId, event.status, channel: "APP");
        if (response != null) {
          emit(
            UpdateHabitsLoaded(
              HabitCard(
                  id: response['id'].toString(),
                  habitActions: [],
                  undoAction: new HabitActionButton("text", null),
                  response: EnumToString.fromString(
                          UserResponse.values, response['response']) ??
                      UserResponse.NO_RESPONSE,
                  status: EnumToString.fromString(
                          HabitStatus.values, response['status']) ??
                      HabitStatus.CREATED),
            ),
          );
          this.habitBloc.add(
              UpdateHabitEvent(habitId: event.habitId, status: event.status));
        } else {
          throw NetworkException();
        }
      } on NetworkException catch (exception) {
        emit(UpdateHabitsNotLoaded());
      } catch (e) {
        emit(UpdateHabitsNotLoaded());
      }
    });
  }
}

import 'package:flutter/material.dart';
import 'package:transform/blocs/clp/models.dart';

@immutable
abstract class HabitState {
  HabitState() : super();
}

class HabitIdleState extends HabitState {
  @override
  String toString() => 'HabitIdleState';
}

class HabitsLoading extends HabitState {
  @override
  String toString() => 'HabitsLoading';
}

class HabitsLoaded extends HabitState {
  final List<HabitCard> cards;

  HabitsLoaded(this.cards);

  @override
  String toString() => 'HabitsLoaded';
}

class HabitsNotLoaded extends HabitState {
  final Error? error;

  HabitsNotLoaded([this.error]) : super();

  @override
  String toString() => 'HabitsNotLoaded';
}

@immutable
abstract class UpdateHabitState {
  UpdateHabitState() : super();
}

class UpdateHabitIdleState extends UpdateHabitState {
  @override
  String toString() => 'UpdateHabitIdleState';
}

class UpdateHabitsLoading extends UpdateHabitState {
  @override
  String toString() => 'HabitsLoading';
}

class UpdateHabitsLoaded extends UpdateHabitState {
  final HabitCard card;

  UpdateHabitsLoaded(this.card);

  @override
  String toString() => 'HabitsLoaded';
}

class UpdateHabitsNotLoaded extends UpdateHabitState {
  final Error? error;

  UpdateHabitsNotLoaded([this.error]) : super();

  @override
  String toString() => 'HabitsNotLoaded';
}

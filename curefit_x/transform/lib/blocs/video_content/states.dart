import 'package:transform/blocs/video_content/models.dart';

abstract class VideoContentViewState {
  VideoContentViewState() : super();
}

class VideoContentViewIdleState extends VideoContentViewState {
  @override
  String toString() => 'VideoContentViewIdleState';
}

class VideoContentViewLoadingState extends VideoContentViewState {
  @override
  String toString() => 'VideoContentViewLoadingState';
}

class VideoContentViewLoadedState extends VideoContentViewState {
  final VideoContentScreenData? videoContentScreenData;

  VideoContentViewLoadedState({this.videoContentScreenData}) : super();

  @override
  String toString() => 'VideoContentViewLoadedState';
}

class VideoContentViewFailedState extends VideoContentViewState {
  @override
  String toString() => 'VideoContentViewFailedState';
}

import 'package:common/action/action_handler.dart';
import 'package:common/ui/aurora.dart';
import 'package:enum_to_string/enum_to_string.dart';

class VideoContentScreenData {
  VideoContentScreenData({
    this.title,
    this.themeType,
    this.widgets,
    this.action,
  });

  final String? title;
  final List<dynamic>? widgets;
  final CanvasTheme? themeType;
  final Action? action;

  static VideoContentScreenData fromJson(widgetData) {
    return VideoContentScreenData(
      title: widgetData["title"],
      widgets: widgetData['widgets'],
      themeType: widgetData['themeType'] != null
          ? EnumToString.fromString(CanvasTheme.values, widgetData['themeType'])
          : null,
      action: widgetData['action'] != null
          ? Action.fromJson(widgetData['action'])
          : null,
    );
  }
}

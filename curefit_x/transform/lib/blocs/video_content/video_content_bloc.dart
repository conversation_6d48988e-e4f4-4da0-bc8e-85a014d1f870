import 'package:common/network/client.dart';
import 'package:common/network/client_repository.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/video_content/events.dart';
import 'package:transform/blocs/video_content/models.dart';
import 'package:transform/blocs/video_content/states.dart';

class VideoContentViewBloc
    extends Bloc<VideoContentViewEvent, VideoContentViewState> {
  final CoachClientRepository repository;

  VideoContentViewBloc(
      VideoContentViewIdleState videoContentViewIdleState, this.repository)
      : super(videoContentViewIdleState) {
    on<LoadVideoContentViewEvent>((event, emit) async {
      await _mapVideoContentDataToState(event, emit);
    });
  }

  Future<void> _mapVideoContentDataToState(LoadVideoContentViewEvent event,
      Emitter<VideoContentViewState> emit) async {
    try {
      emit(VideoContentViewLoadingState());
      final response = await this.repository.getPageData(
          'v2/transform/videoContent', {
        "pillarId": event.pillarId,
        "subCategoryCode": event.subCategoryCode ?? ""
      });
      if (response != null) {
        emit(
          VideoContentViewLoadedState(
              videoContentScreenData:
                  VideoContentScreenData.fromJson(response)),
        );
      } else {
        emit(VideoContentViewFailedState());
      }
    } on NetworkException catch (exception) {
      emit(VideoContentViewFailedState());
    } catch (e) {
      emit(VideoContentViewFailedState());
    }
  }
}

import 'package:transform/blocs/testimonials/models.dart';


abstract class TestimonialsState {
  TestimonialsState() : super();
}

class TestimonialsIdleState extends TestimonialsState {
  @override
  String toString() => 'TestimonialsIdleState';
}

class TestimonialsLoadingState extends TestimonialsState {
  @override
  String toString() => 'TestimonialsLoadingState';
}

class TestimonialsLoadedState extends TestimonialsState {
  final TestimonialsScreenData testimonialsScreenData;

  TestimonialsLoadedState(this.testimonialsScreenData)
      : super();

  @override
  String toString() => 'TestimonialsLoadedState';
}

class TestimonialsFailedState extends TestimonialsState {
  @override
  String toString() => 'TestimonialsFailedState';
}

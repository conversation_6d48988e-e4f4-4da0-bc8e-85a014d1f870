import 'package:common/network/client.dart';
import 'package:common/network/client_repository.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/testimonials/events.dart';
import 'package:transform/blocs/testimonials/state.dart';
import 'package:transform/blocs/testimonials/models.dart';

class TestimonialsBloc extends Bloc<TestimonialsEvent, TestimonialsState> {
  final CoachClientRepository repository;

  TestimonialsBloc(TestimonialsIdleState idleState, this.repository)
      : super(idleState) {
    on<TestimonialsLoadEvent>((event, emit) async {
      await _mapLoadTestimonialsToState(event, emit);
    });
  }

  Future<void> _mapLoadTestimonialsToState(
      TestimonialsLoadEvent event, Emitter<TestimonialsState> emit) async {
    try {
      emit(TestimonialsLoadingState());
      final response = await this.repository.getPageData(
          'v2/transform/testimonials',
          {"subCategoryCode": event.subCategoryCode ?? ""});
      if (response != null) {
        emit(
            TestimonialsLoadedState(TestimonialsScreenData.fromJson(response)));
      } else {
        emit(TestimonialsFailedState());
      }
    } on NetworkException catch (exception) {
      emit(TestimonialsFailedState());
    } catch (e) {
      emit(TestimonialsFailedState());
    }
  }
}

import 'package:common/ui/components/testimonials_widget.dart';

class TestimonialsScreenData {
  String title;
  List<Testimonial> testimonials;

  TestimonialsScreenData(this.title, this.testimonials);
  static TestimonialsScreenData from<PERSON>son(widgetData) {
    // map Testimonials
    return TestimonialsScreenData(
        widgetData["title"],
        widgetData['testimonials'].map<Testimonial>((e) => Testimonial.fromJson(e)).toList()
    );
  }
}

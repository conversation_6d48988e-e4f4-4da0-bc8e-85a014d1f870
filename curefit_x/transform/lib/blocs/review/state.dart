import 'package:flutter/material.dart';
import 'package:transform/blocs/review/models.dart';

@immutable
abstract class ReviewState {
  ReviewState() : super();
}

class ReviewIdle extends ReviewState {
  @override
  String toString() => 'ReviewIdle';
}

class ReviewLoaded extends ReviewState {
  final ReviewScreenData reviewScreenData;

  ReviewLoaded({required this.reviewScreenData})
      : super();

  @override
  String toString() => 'ReviewLoaded';
}

class ReviewLoading extends ReviewState {
  @override
  String toString() => 'ReviewLoading';
}

class ReviewSuccess extends ReviewState {
  @override
  String toString() => 'ReviewSuccess';
}


class ReviewFailed extends ReviewState {
  @override
  String toString() => 'ReviewFailed';
}

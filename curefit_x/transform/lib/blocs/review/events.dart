import 'package:flutter/cupertino.dart';

abstract class ReviewEvent {
  ReviewEvent() : super();
}

class ReviewRegisterEvent extends ReviewEvent {
  final String? feedbackId;
  final String? rating;
  final String? review;
  final List<dynamic>? selectedTags;

  ReviewRegisterEvent(
      {@required this.feedbackId,
      @required this.rating,
      @required this.review,
      @required this.selectedTags})
      : super();

  @override
  String toString() => 'ReviewRegisterEvent';
}

class ReviewLoadEvent extends ReviewEvent {
  final String? feedbackId;
  final String? subCategoryCode;

  ReviewLoadEvent({@required this.feedbackId, this.subCategoryCode}) : super();

  @override
  String toString() => 'ReviewLoadEvent';
}

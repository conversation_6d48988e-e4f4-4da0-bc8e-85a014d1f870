import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/review/events.dart';
import 'package:transform/blocs/review/models.dart';
import 'package:transform/blocs/review/state.dart';
import 'package:transform/network/feedback_repository.dart';

class ReviewBloc extends Bloc<ReviewEvent, ReviewState> {
  final FeedbackRepository repository;

  ReviewBloc(ReviewIdle initialState, this.repository) : super(initialState) {
    on<ReviewLoadEvent>((event, emit) async {
      await _mapReviewLoadToState(event, emit);
    });

    on<ReviewRegisterEvent>((event, emit) async {
      await _mapReviewRegisterToState(event, emit);
    });
  }

  Future<void> _mapReviewLoadToState(
      ReviewLoadEvent event, Emitter<ReviewState> emit) async {
    try {
      emit(ReviewLoading());
      final response = await this.repository.getPageData(
          feedbackId: event.feedbackId ?? "",
          subCategoryCode: event.subCategoryCode);
      if (response != null) {
        final detail = response;
        emit(ReviewLoaded(reviewScreenData: ReviewScreenData.fromJson(detail)));
      } else {
        emit(ReviewFailed());
      }
    } catch (e) {
      emit(ReviewFailed());
    }
  }

  Future<void> _mapReviewRegisterToState(
      ReviewRegisterEvent event, Emitter<ReviewState> emit) async {
    try {
      emit(ReviewLoading());
      final response = await this.repository.submitFeedback(
          feedbackId: event.feedbackId,
          rating: event.rating,
          review: event.review,
          selectedTags: event.selectedTags);
      if (response != null) {
        emit(ReviewSuccess());
      } else {
        emit(ReviewFailed());
      }
    } catch (e) {
      emit(ReviewFailed());
    }
  }
}

import 'package:common/action/action_handler.dart';

class EmojiInfo {
  EmojiInfo(
      {this.emotion,
      this.title,
      this.subtitle,
      this.tags,
      this.reviewQuestion,
      this.rating,
      this.icon});

  final String? emotion;
  final String? title;
  final String? subtitle;
  final String? reviewQuestion;
  final String? icon;
  final String? rating;
  final List<String>? tags;
}

class ReviewScreenData {
  ReviewScreenData(
      {this.title,
      this.subtitle,
      this.imageUrl,
      this.question,
      this.emojiList,
      this.buttonTitle,
      this.endRouteAction
      });

  final String? title;
  final String? subtitle;
  final String? imageUrl;
  final String? question;
  final List<EmojiInfo>? emojiList;
  final Action? endRouteAction;

  final String? buttonTitle;

  static ReviewScreenData fromJson(widget) {
    return ReviewScreenData(
      title: widget['widgets'] != null
          ? widget['widgets'][0]['activityName']
          : null,
      subtitle:
          widget['widgets'] != null ? widget['widgets'][0]['message'] : null,
      imageUrl: widget['widgets'] != null
          ? widget['widgets'][0]['activityName']
          : null,
      question:
          widget['widgets'] != null ? widget['widgets'][1]['question'] : null,
      endRouteAction: widget['endRouteAction'] != null ? Action.fromJson(widget['endRouteAction']) : null,
      emojiList: widget['widgets'][1]['ratings'] != null
          ? widget['widgets'][1]['ratings']
              .map<EmojiInfo>(
                (data) => EmojiInfo(
                  emotion: data['rating'],
                  title: data['emotion'],
                  subtitle: data['emotionDetail'],
                  icon: data['icon'],
                  rating: data['rating'],
                  reviewQuestion: data['reviewQuestion'],
                  tags: data['tags'] != null
                      ? data['tags']
                          .map<String>((tagData) => tagData['text'].toString())
                          .toList()
                      : null,
                ),
              )
              .toList()
          : null,
      buttonTitle: widget['pageAction']['title'],
    );
  }
}

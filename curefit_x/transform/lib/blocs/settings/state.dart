import 'package:flutter/material.dart';
import 'package:transform/blocs/settings/models.dart';

@immutable
abstract class SettingsState {
  SettingsState() : super();
}

class SettingsIdle extends SettingsState {
  @override
  String toString() => 'SettingsIdle';
}

class SettingsLoaded extends SettingsState {
  final SettingsModalScreenData settingsModalScreenData;

  SettingsLoaded({required this.settingsModalScreenData})
      : super();

  @override
  String toString() => 'SettingsLoaded';
}

class SettingsLoading extends SettingsState {
  @override
  String toString() => 'SettingsLoading';
}

class SettingsSuccess extends SettingsState {
  @override
  String toString() => 'SettingsSuccess';
}

class SettingsFailed extends SettingsState {
  @override
  String toString() => 'SettingsFailed';
}

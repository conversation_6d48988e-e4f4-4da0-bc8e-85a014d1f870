class SettingsModalScreenData {
  SettingsModalScreenData(
      {this.title, this.subtitle, this.description, this.buttonTitles});

  final String? title;
  final String? subtitle;
  final String? description;
  final List<dynamic>? buttonTitles;

  static SettingsModalScreenData fromJson(data) {
    print(data);
    return SettingsModalScreenData(
      title: data['title'],
      subtitle: data['subtitle'],
      description: data['description'],
      buttonTitles:
          data['buttonTitles'].map((title) => title.toString()).toList(),
    );
  }
}

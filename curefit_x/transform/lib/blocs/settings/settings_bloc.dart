import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/settings/events.dart';
import 'package:transform/blocs/settings/models.dart';
import 'package:transform/blocs/settings/state.dart';
import 'package:transform/network/settings_repository.dart';

class SettingsBloc extends Bloc<SettingsEvent, SettingsState> {
  final SettingsRepository repository;

  SettingsBloc(SettingsState initialState, this.repository)
      : super(initialState) {
    on<SettingsLoadEvent>((event, emit) async {
      await _mapSettingsLoadToState(event, emit);
    });

    on<SettingsSubmitEvent>((event, emit) async {
      await _mapSettingsSubmitToState(event, emit);
    });
  }

  Future<void> _mapSettingsLoadToState(
      SettingsLoadEvent event, Emitter<SettingsState> emit) async {
    try {
      emit(SettingsLoading());
      final response = await this.repository.getPageData();
      if (response != null) {
        emit(SettingsLoaded(
            settingsModalScreenData:
                SettingsModalScreenData.fromJson(response)));
      } else {
        emit(SettingsFailed());
      }
    } catch (e) {
      emit(SettingsFailed());
    }
  }

  Future<void> _mapSettingsSubmitToState(
      SettingsSubmitEvent event, Emitter<SettingsState> emit) async {
    try {
      emit(SettingsLoading());
      final response = await this
          .repository
          .submitSettings(preference: event.preference ?? false);
      if (response != null) {
        emit(SettingsSuccess());
      } else {
        emit(SettingsFailed());
      }
    } catch (e) {
      emit(SettingsFailed());
    }
  }
}

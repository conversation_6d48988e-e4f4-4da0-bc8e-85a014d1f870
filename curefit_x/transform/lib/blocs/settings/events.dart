import 'package:flutter/material.dart';

abstract class SettingsEvent {
  SettingsEvent() : super();
}

class SettingsSubmitEvent extends SettingsEvent {
  final bool? preference;
  SettingsSubmitEvent({@required this.preference}) : super();

  @override
  String toString() => 'SettingsSubmitEvent';
}

class SettingsLoadEvent extends SettingsEvent {
  SettingsLoadEvent() : super();

  @override
  String toString() => 'SettingsLoadEvent';
}

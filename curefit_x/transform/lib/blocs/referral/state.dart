import 'package:common/model/error_prop_model.dart';
import 'models.dart';

abstract class ReferralScreenState {
  ReferralScreenState() : super();
}

class ReferralScreenIdleState extends ReferralScreenState {
  @override
  String toString() => 'ReferralScreenIdleState';
}

class ReferralScreenLoadingState extends ReferralScreenState {
  final ReferralScreenData? screenData;
  final bool showLoader;

  ReferralScreenLoadingState({this.screenData, this.showLoader = true});

  @override
  String toString() => 'ReferralScreenLoadingState';
}

class ReferralScreenLoadedState extends ReferralScreenState {
  final ReferralScreenData screenData;

  ReferralScreenLoadedState(
      {required this.screenData})
      : super();

  @override
  String toString() => 'ReferralScreenLoadedState';
}

class ReferralScreenErrorState extends ReferralScreenState {
  final ErrorInfo? errorInfo;

  ReferralScreenErrorState([this.errorInfo]) : super();

  @override
  String toString() => 'ReferralScreenErrorState';
}

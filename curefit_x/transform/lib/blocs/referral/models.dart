import 'package:common/action/action_handler.dart';
import 'package:common/ui/header/card_header.dart';
import 'package:common/ui/widget_builder.dart';

class ReferralScreenData {
  ReferralScreenData({
    this.pageDataList,
  });

  final List<PageData>? pageDataList;

  static ReferralScreenData fromJson(json) {
    return ReferralScreenData(
      pageDataList: json['pageDataList'] != null
          ? json['pageDataList'].map<PageData>((data) {
              return PageData.fromJson(data);
            }).toList()
          : null,
    );
  }
}

class PageData {
  PageData(
      {this.title,
      this.pageId,
      this.pageName,
      this.routeName,
      this.hexColor = "#FFFFFF",
      this.action})
      : super();

  final String? title;
  final String? pageId;
  final String? pageName;
  final String? routeName;
  final String hexColor;
  final Action? action;

  factory PageData.fromJson(dynamic json) {
    return PageData(
      title: json['title'],
      pageId: json['pageId'],
      pageName: json['pageName'],
      routeName: json['routeName'],
      hexColor: json['hexColor'] ?? "#FFFFFF",
      action: json['action'] != null ? Action.fromJson(json['action']) : null,
    );
  }
}

class LevelPointsWidgetData implements IWidgetData {
  LevelPointsWidgetData(
    this.widgetType, {
    this.header,
    this.items,
    this.subCategoryCode,
    this.widgetInfo,
  });

  final WidgetHeaderData? header;
  final List<LevelItem>? items;
  final String? subCategoryCode;

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  factory LevelPointsWidgetData.fromJson(
      WidgetTypes widgetType, dynamic widget, WidgetInfo? widgetInfo) {
    return LevelPointsWidgetData(
      widgetType,
      header: widget['header'] != null
          ? WidgetHeaderData.fromJson(widget['header'])
          : null,
      items: widget['items'] != null
          ? widget['items']
              .map<LevelItem>((data) => LevelItem.fromJson(data))
              .toList()
          : null,
      subCategoryCode: widget['subCategoryCode'],
      widgetInfo: widgetInfo,
    );
  }
}

class LevelItem {
  final String? title;
  final String? tagText;
  final String? imageUrl;

  LevelItem({
    this.title,
    this.tagText,
    this.imageUrl,
  });

  static LevelItem fromJson(dynamic json) {
    return LevelItem(
      title: json['title'],
      tagText: json['tagText'],
      imageUrl: json['imageUrl'],
    );
  }
}

class ShareActionWidgetData implements IWidgetData {
  ShareActionWidgetData(
    this.widgetType, {
    this.header,
    this.title,
    this.actionList,
    this.copyText,
    this.showCopyButton = false,
    this.subCategoryCode,
    this.widgetInfo,
  });

  final WidgetHeaderData? header;
  final String? title;
  final List<ShareAction>? actionList;
  final bool showCopyButton;
  final String? copyText;
  final String? subCategoryCode;

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  factory ShareActionWidgetData.fromJson(
      WidgetTypes widgetType, dynamic widget, WidgetInfo? widgetInfo) {
    return ShareActionWidgetData(
      widgetType,
      header: widget['header'] != null
          ? WidgetHeaderData.fromJson(widget['header'])
          : null,
      title: widget['title'],
      showCopyButton: widget['showCopyButton'],
      copyText: widget['copyText'],
      actionList: widget['actionList'] != null
          ? widget['actionList']
              .map<ShareAction>((action) => ShareAction.fromJson(action))
              .toList()
          : null,
      subCategoryCode: widget['subCategoryCode'],
      widgetInfo: widgetInfo,
    );
  }
}

class ShareAction {
  final String? title;
  final String? imageUrl;
  final Action? action;

  ShareAction({
    this.title,
    this.imageUrl,
    this.action,
  });

  static ShareAction fromJson(dynamic json) {
    return ShareAction(
      title: json['title'],
      imageUrl: json['imageUrl'],
      action: json['action'] != null ? Action.fromJson(json['action']) : null,
    );
  }
}

class ReferralCardsWidgetData implements IWidgetData {
  ReferralCardsWidgetData(
    this.widgetType, {
    this.header,
    this.title,
    this.action,
    this.errorMessage,
    this.summaryCard,
    this.items,
    this.subCategoryCode,
    this.widgetInfo,
  });

  final WidgetHeaderData? header;
  final String? title;
  final String? errorMessage;
  final SummaryCard? summaryCard;
  final List<ReferralCard>? items;
  final Action? action;
  final String? subCategoryCode;

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  factory ReferralCardsWidgetData.fromJson(
      WidgetTypes widgetType, dynamic widget, WidgetInfo? widgetInfo) {
    return ReferralCardsWidgetData(
      widgetType,
      header: widget['header'] != null
          ? WidgetHeaderData.fromJson(widget['header'])
          : null,
      title: widget['title'],
      errorMessage: widget['errorMessage'],
      action:
          widget['action'] != null ? Action.fromJson(widget['action']) : null,
      summaryCard: widget['summaryCard'] != null
          ? SummaryCard.fromJson(widget['summaryCard'])
          : null,
      items: widget['items'] != null
          ? widget['items']
              .map<ReferralCard>((data) => ReferralCard.fromJson(data))
              .toList()
          : null,
      subCategoryCode: widget['subCategoryCode'],
      widgetInfo: widgetInfo,
    );
  }
}

class ReferralCard {
  final String? imageUrl;
  final String? title;
  final String? subTitle;
  final String? description;
  final String? footerTitle;
  final String? footerSubTitle;
  final Action? action;

  ReferralCard({
    this.imageUrl,
    this.title,
    this.subTitle,
    this.description,
    this.footerTitle,
    this.footerSubTitle,
    this.action,
  });

  static ReferralCard fromJson(dynamic json) {
    return ReferralCard(
      imageUrl: json['imageUrl'],
      title: json['title'],
      subTitle: json['subTitle'],
      description: json['description'],
      footerTitle: json['footerTitle'],
      footerSubTitle: json['footerSubTitle'],
      action: json['action'] != null ? Action.fromJson(json['action']) : null,
    );
  }
}

class SummaryCard {
  final String? imageUrl;
  final List<SummaryItem>? items;

  SummaryCard({
    this.imageUrl,
    this.items,
  });

  static SummaryCard fromJson(dynamic json) {
    return SummaryCard(
      imageUrl: json['imageUrl'],
      items: json['items'] != null
          ? json['items']
              .map<SummaryItem>((data) => SummaryItem.fromJson(data))
              .toList()
          : null,
    );
  }
}

class SummaryItem {
  final String? title;
  final String? subtitle;

  SummaryItem({
    this.title,
    this.subtitle,
  });

  static SummaryItem fromJson(dynamic json) {
    return SummaryItem(
      title: json['title'],
      subtitle: json['subtitle'],
    );
  }
}

class BannerCardWidgetData implements IWidgetData {
  BannerCardWidgetData(
    this.widgetType, {
    this.title,
    this.action,
    this.subTitle,
    this.description,
    this.imageUrl,
    this.bgImageUrl,
    this.lottieUrl,
    this.widgetInfo,
  });

  final String? title;
  final String? subTitle;
  final String? description;
  final String? imageUrl;
  final String? lottieUrl;
  final String? bgImageUrl;
  final Action? action;

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  factory BannerCardWidgetData.fromJson(
      WidgetTypes widgetType, dynamic widget, WidgetInfo? widgetInfo) {
    return BannerCardWidgetData(
      widgetType,
      title: widget['title'],
      subTitle: widget['subTitle'],
      description: widget['description'],
      imageUrl: widget['imageUrl'],
      bgImageUrl: widget['bgImageUrl'],
      lottieUrl: widget['lottieUrl'],
      action:
          widget['action'] != null ? Action.fromJson(widget['action']) : null,
      widgetInfo: widgetInfo,
    );
  }
}

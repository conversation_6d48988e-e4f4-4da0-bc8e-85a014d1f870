abstract class ReferralScreenEvent {
  ReferralScreenEvent() : super();
}

class LoadReferralScreenEvent extends ReferralScreenEvent {
  final bool showLoader;
  final String? subCategoryCode;

  LoadReferralScreenEvent({this.showLoader = false, this.subCategoryCode}) : super();

  @override
  String toString() {
    return "LoadReferralScreenEvent";
  }
}

class ResetReferralScreenEvent extends ReferralScreenEvent {
  @override
  String toString() => 'ResetReferralScreenEvent';
}

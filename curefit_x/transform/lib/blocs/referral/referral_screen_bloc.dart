import 'dart:io';

import 'package:bloc/bloc.dart';
import 'package:common/model/error_prop_model.dart';
import 'package:common/network/client.dart';
import 'package:common/network/client_repository.dart';
import 'package:transform/blocs/referral/events.dart';
import 'package:transform/blocs/referral/models.dart';
import 'package:transform/blocs/referral/state.dart';

class ReferralScreenBloc
    extends Bloc<ReferralScreenEvent, ReferralScreenState> {
  final CoachClientRepository repository;
  String subCategoryCode = "";
  ReferralScreenData screenData = ReferralScreenData();

  ReferralScreenBloc(this.repository) : super(ReferralScreenIdleState()) {
    on<LoadReferralScreenEvent>((event, emit) async {
      await _mapLoadReferralScreen(event, emit);
    });
    on<ResetReferralScreenEvent>((event, emit) async {
      screenData = ReferralScreenData();
      emit(ReferralScreenLoadedState(screenData: screenData));
    });
  }

  void refresh() {
    this.add(LoadReferralScreenEvent(subCategoryCode: this.subCategoryCode));
  }

  Future<void> _mapLoadReferralScreen(
      LoadReferralScreenEvent event, Emitter<ReferralScreenState> emit) async {
    emit(ReferralScreenLoadingState(
        screenData: screenData, showLoader: event.showLoader));
    try {
      this.subCategoryCode = event.subCategoryCode ?? "";
      final response = await this.repository.getPageData(
          'transform/referral', {"subCategoryCode": this.subCategoryCode});
      if (response != null && response['pageDataList'] != null) {
        screenData = ReferralScreenData.fromJson(response);
        emit(ReferralScreenLoadedState(screenData: screenData));
      } else {
        emit(ReferralScreenErrorState(UnknownError(
            failed: true, responseCode: "404", callbackFunction: refresh)));
      }
    } on NetworkException catch (exception) {
      emit(ReferralScreenErrorState(NetworkError(
          failed: true,
          responseCode: '${(exception).statusCode}',
          callbackFunction: refresh)));
    } on SocketException {
      emit(ReferralScreenErrorState(
          NoInternetError(failed: true, callbackFunction: refresh)));
    } catch (e) {
      emit(ReferralScreenErrorState(
          UnknownError(failed: true, callbackFunction: refresh)));
    }
  }
}

abstract class WeightLossTabEvent {
  WeightLossTabEvent() : super();
}

class LoadWeightLossTabEvent extends WeightLossTabEvent {
  final bool showLoader;

  LoadWeightLossTabEvent({this.showLoader = false}) : super();

  @override
  String toString() {
    return "LoadWeightLossClpEvent";
  }
}

class ResetWeightLossTabEvent extends WeightLossTabEvent {
  @override
  String toString() => 'ResetWeightLossTabEvent';
}

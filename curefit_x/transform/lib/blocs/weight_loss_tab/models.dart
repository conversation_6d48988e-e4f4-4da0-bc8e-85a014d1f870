import 'package:common/action/action_handler.dart';
import 'package:common/blocs/page/models.dart';
import 'package:common/constants/action_constants.dart';

class WeightLossTabData {
  WeightLossTabData({
    this.pageDatalist,
    this.title
  });

  final List<PageData>? pageDatalist;
  final String? title;
  static WeightLossTabData fromJson(json) {
    return WeightLossTabData(
      pageDatalist: json['pageDataList'] != null
          ? json['pageDataList'].map<PageData>((data) {
              return PageData.fromJson(data);
            }).toList()
          : null,
      title: json['title'],
    );
  }
}

class PageData {
  PageData({
    this.title,
    this.pageId,
    this.pageName,
    this.routeName,
    this.isListPage = true,
    this.coachContactView,
    this.hexColor ="#FFFFFF",
    this.rightBarAction,
  }) : super();

  final String? title;
  final String? pageId;
  final String? pageName;
  final String? routeName;
  final bool isListPage;
  final CoachContactView? coachContactView;
  final String hexColor;
  final Action? rightBarAction;


  factory PageData.fromJson(dynamic json) {
    return PageData(
      title: json['title'],
      pageId: json['pageId'],
      pageName: json['pageName'],
      routeName: json['routeName'],
      isListPage: json['isListPage'] ?? false,
      coachContactView: json['coachContactView'] != null
          ? CoachContactView.fromJson(json['coachContactView'])
          : null,
        hexColor: json['hexColor'] ?? "#FFFFFF",
      rightBarAction: json['rightBarAction'] != null
          ? Action.fromJson(json['rightBarAction'])
          : null,
    );
  }
}

class CoachContactView {
  CoachContactView(
      {this.title,
      this.subtitle,
      this.imagesList,
      this.progressAction,
      this.chatAction,
      this.action,
      this.callAction});

  final String? title;
  final String? subtitle;
  final List<String>? imagesList;
  final Action? progressAction;
  final Action? chatAction;
  final Action? callAction;
  final Action? action;

  static CoachContactView fromJson(widget) {
    return CoachContactView(
      title: widget['title'],
      subtitle: widget['subtitle'],
      imagesList: widget['imagesList'] != null
          ? widget['imagesList']
              .map<String>((image) => image.toString())
              .toList()
          : null,
      progressAction: widget['progressAction'] != null
          ? Action.fromJson(widget['progressAction'])
          : null,
      action:
          widget['action'] != null ? Action.fromJson(widget['action']) : null,
      chatAction: widget['chatAction'] != null
          ? Action.fromJson(widget['chatAction'])
          : null,
      callAction: widget['callAction'] != null
          ? Action.fromJson(widget['callAction'])
          : null,
    );
  }
}

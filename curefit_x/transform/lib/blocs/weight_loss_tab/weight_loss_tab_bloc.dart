import 'dart:io';

import 'package:bloc/bloc.dart';
import 'package:common/model/error_prop_model.dart';
import 'package:common/network/client.dart';
import 'package:common/network/client_repository.dart';
import 'package:transform/blocs/weight_loss_tab/events.dart';
import 'package:transform/blocs/weight_loss_tab/models.dart';
import 'package:transform/blocs/weight_loss_tab/state.dart';

class WeightLossTabBloc extends Bloc<WeightLossTabEvent, WeightLossTabState> {
  final CoachClientRepository repository;
  WeightLossTabData screenData = WeightLossTabData();

  WeightLossTabBloc(this.repository) : super(WeightLossTabIdleState()) {
    on<LoadWeightLossTabEvent>((event, emit) async {
      await _mapLoadWeightLossTab(event, emit);
    });
    on<ResetWeightLossTabEvent>((event, emit) async {
      screenData = WeightLossTabData();
      emit(WeightLossTabLoadedState(screenData: screenData));
    });
  }

  void refresh() {
    this.add(LoadWeightLossTabEvent());
  }

  Future<void> _mapLoadWeightLossTab(
      LoadWeightLossTabEvent event, Emitter<WeightLossTabState> emit) async {
    emit(WeightLossTabLoadingState(
        screenData: screenData, showLoader: event.showLoader));
    try {
      final response =
          await this.repository.getPageData('v2/transform/weight_loss_tab', {});
      if (response != null && response['pageDataList'] != null) {
        screenData = WeightLossTabData.fromJson(response);
        emit(WeightLossTabLoadedState(screenData: screenData));
      } else {
        emit(WeightLossTabErrorState(UnknownError(
            failed: true, responseCode: "404", callbackFunction: refresh)));
      }
    } on NetworkException catch (exception) {
      emit(WeightLossTabErrorState(NetworkError(
          failed: true,
          responseCode: '${(exception).statusCode}',
          callbackFunction: refresh)));
    } on SocketException {
      emit(WeightLossTabErrorState(
          NoInternetError(failed: true, callbackFunction: refresh)));
    } catch (e) {
      emit(WeightLossTabErrorState(
          UnknownError(failed: true, callbackFunction: refresh)));
    }
  }
}

import 'package:common/model/error_prop_model.dart';
import 'models.dart';

abstract class WeightLossTabState {
  WeightLossTabState() : super();
}

class WeightLossTabIdleState extends WeightLossTabState {
  @override
  String toString() => 'WeightLossTabIdleState';
}

class WeightLossTabLoadingState extends WeightLossTabState {
  final WeightLossTabData? screenData;
  final bool showLoader;

  WeightLossTabLoadingState({this.screenData, this.showLoader = true});

  @override
  String toString() => 'WeightLossTabLoadingState';
}

class WeightLossTabLoadedState extends WeightLossTabState {
  final WeightLossTabData screenData;

  WeightLossTabLoadedState(
      {required this.screenData})
      : super();

  @override
  String toString() => 'WeightLossTabLoadedState';
}

class WeightLossTabErrorState extends WeightLossTabState {
  final ErrorInfo? errorInfo;

  WeightLossTabErrorState([this.errorInfo]) : super();

  @override
  String toString() => 'WeightLossTabErrorState';
}

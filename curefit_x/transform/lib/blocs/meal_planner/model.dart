import 'package:common/action/action_handler.dart';
import 'package:common/ui/widget_builder.dart';

class IWidget {
  IWidget(this.widgetType);

  String widgetType;
}

class MealContentScreen {
  MealContentScreen(
      {required this.widgets,
      required this.mealType,
      required this.dates,
      required this.action,
      required this.pageTitle,
      this.pageSubTitle});

  final List<dynamic> widgets;
  final List<dynamic> mealType;
  final List<dynamic> dates;
  final Action action;
  final String pageTitle;
  final String? pageSubTitle;
}

class MealPlanCardWidgetInfo implements IWidgetData {
  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  Map<String, dynamic>? data;

  MealPlanCardWidgetInfo(this.widgetType, {this.widgetInfo, this.data});

  static MealPlanCardWidgetInfo fromJson(
      widget, WidgetTypes widgetType, WidgetInfo widgetInfo) {
    return MealPlanCardWidgetInfo(
      widgetType,
      widgetInfo: widgetInfo,
      data: widget['data'],
    );
  }
}

class CalorieBreakdownWidgetInfo implements IWidgetData {
  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  Map<String, dynamic>? data;
  final String? icon;
  final String? titleHeader;

  CalorieBreakdownWidgetInfo(this.widgetType,
      {this.data, this.widgetInfo, this.icon, this.titleHeader});

  static CalorieBreakdownWidgetInfo fromJson(
      widget, WidgetTypes widgetType, WidgetInfo widgetInfo) {
    return CalorieBreakdownWidgetInfo(
      widgetType,
      widgetInfo: widgetInfo,
      data: widget['data'],
      icon: widget['icon'],
      titleHeader: widget['titleHeader'],
    );
  }
}

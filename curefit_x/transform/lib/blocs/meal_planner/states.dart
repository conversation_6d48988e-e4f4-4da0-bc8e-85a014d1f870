import 'package:common/model/error_prop_model.dart';

import 'model.dart';

abstract class MealPlannerState {
  MealPlannerState(): super();
}

class MealPlannerInitialState extends MealPlannerState {
  @override
  String toString() => 'MealPlannerInitialState';
}

class MealPlannerLoadingState extends MealPlannerState {
  @override
  String toString() => 'MealPlannerLoadingState';
}

class MealPlannerLoadedState extends MealPlannerState {
  final MealContentScreen screenData;
  MealPlannerLoadedState({required this.screenData});
  @override
  String toString() => 'MealPlannerLoadedState';
}

class MealPlannerErrorState extends MealPlannerState {
  final ErrorInfo? errorInfo;
  MealPlannerErrorState(this.errorInfo);
  @override
  String toString() => 'MealPlannerErrorState';
}

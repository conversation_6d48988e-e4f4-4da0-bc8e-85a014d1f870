import 'package:bloc/bloc.dart';
import 'package:common/action/action_handler.dart';
import 'package:common/model/error_prop_model.dart';
import '../../network/meal_repository.dart';
import 'model.dart';
import 'events.dart';
import 'states.dart';

class MealPlannerBloc extends Bloc<MealPlannerEvent, MealPlannerState> {
  MealPlannerRepository repository;

  MealPlannerBloc({required this.repository})
      : super(MealPlannerInitialState()) {
    on<LoadMealPlannerContentEvent>((event, emit) async {
      await _mapLoadOnboarding(event, emit);
    });
  }

  void refresh({String? subCategoryCode}) {
    this.add(LoadMealPlannerContentEvent(subCategoryCode: subCategoryCode));
  }

  Future<void> _mapLoadOnboarding(
      LoadMealPlannerContentEvent event, Emitter<MealPlannerState> emit) async {
    emit(MealPlannerLoadingState());
    try {
      final response = await repository.getMealPlannerContent();
      if (response == null) {
        emit(MealPlannerErrorState(UnknownError(
            failed: true, responseCode: "404", callbackFunction: (){
              refresh(subCategoryCode: event.subCategoryCode ?? "");
        })));
      }
      MealContentScreen mealContentScreen = new MealContentScreen(
          widgets: response['widgets'],
          mealType: response['mealTypes'],
          dates: response['dates'],
          pageSubTitle: response['pageSubTitle'],
          action: Action.fromJson(response['action']),
          pageTitle: response['pageTitle']);
      emit(MealPlannerLoadedState(screenData: mealContentScreen));
    } catch (e) {
      emit(MealPlannerErrorState(UnknownError(
          failed: true, responseCode: "404", callbackFunction: (){
        refresh(subCategoryCode: event.subCategoryCode ?? "");
      })));
    }
  }
}

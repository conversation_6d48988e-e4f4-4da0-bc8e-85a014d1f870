import 'package:common/action/action_handler.dart';
import 'package:transform/blocs/fitness_plan/fitness_plan_models/sgt_detail.dart';
import 'package:transform/blocs/fitness_plan/fitness_plan_models/workout_detail.dart';

import 'empty_message.dart';

class PlanList {
  String? type;
  String? date;
  bool? isRedoAllowed;
  List<dynamic>? widgets;
  List<WorkoutDetail>? baseWidgets;
  List<SgtDetail>? sgtClasses;
  List<Action>? pageActions;
  EmptyMessage? emptyMessage;

  PlanList({this.type, this.date, this.widgets, this.baseWidgets, this.sgtClasses,this.pageActions, this.emptyMessage, this.isRedoAllowed});

  factory PlanList.fromJson(Map<String, dynamic> json) => PlanList(
        type: json['type'],
        date: json['date'] as String?,
        isRedoAllowed: json['isRedoAllowed'] ?? false,
        widgets: json['widgets'],
        baseWidgets: (json['baseWidgets'] as List<dynamic>?)
            ?.map((e) => WorkoutDetail.fromJson(e as Map<String, dynamic>))
            .toList(),
        sgtClasses: (json['sgtClasses'] as List<dynamic>?)
            ?.map((e) => SgtDetail.fromJson(e as Map<String, dynamic>))
            .toList(),
        pageActions: (json['pageActions'] as List<dynamic>?)
            ?.map((e) => Action.fromJson(e as Map<String, dynamic>))
            .toList(),
        emptyMessage: json['emptyMessage'] == null
            ? null
            : EmptyMessage.fromJson(
                json['emptyMessage'] as Map<String, dynamic>),
      );
}

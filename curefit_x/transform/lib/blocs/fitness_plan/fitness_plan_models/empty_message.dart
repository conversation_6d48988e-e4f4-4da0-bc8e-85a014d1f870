class EmptyMessage {
  String? header;
  String? imageUrl;
  String? description;

  EmptyMessage({this.header, this.imageUrl, this.description});

  factory EmptyMessage.fromJson(Map<String, dynamic> json) => EmptyMessage(
        header: json['header'] as String?,
        imageUrl: json['imageUrl'] as String?,
        description: json['description'] as String?,
      );

  Map<String, dynamic> toJson() => {
        'header': header,
        'imageUrl': imageUrl,
        'description': description,
      };
}

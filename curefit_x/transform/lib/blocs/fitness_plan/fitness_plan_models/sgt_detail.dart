class SgtDetail {

  SgtDetail({this.workoutName, this.coachName, this.workoutImageUrl, this.slotTiming,  this.slotStatus, this.zoomLink, this.slotId, this.coachId});

  String? workoutName;
  String? coachName;
  String? workoutImageUrl;
  String? slotTiming;
  String? slotStatus;
  String? zoomLink;
  String? slotId;
  String? coachId;

  factory SgtDetail.fromJson(json) {
    return SgtDetail(
        workoutName: json['workoutName'],
        coachName: json['coachName'],
        workoutImageUrl: json['workoutImageUrl'],
        slotTiming: json['slotTiming'],
        slotStatus: json['slotStatus'],
        zoomLink: json['zoomLink'],
        slotId: json['slotId'],
        coachId: json['coachId']
    );
  }
}

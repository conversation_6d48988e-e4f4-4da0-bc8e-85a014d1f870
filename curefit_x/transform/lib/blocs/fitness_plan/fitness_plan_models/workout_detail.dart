import 'package:common/action/action_handler.dart';

class WorkoutDetail {

  WorkoutDetail({this.fitnessPlanWorkoutStatus, this.workoutName, this.workoutTitle, this.workoutSubTitle, this.workoutDuration,  this.workoutImageUrl, this.action, this.statusImage,  this.workoutType, this.dayNumber});

  String? fitnessPlanWorkoutStatus;
  String? workoutName;
  String? workoutTitle;
  String? workoutSubTitle;
  String? workoutDuration;
  String? workoutImageUrl;
  String? statusImage;
  Action? action;
  String? workoutType;
  String? dayNumber;

  factory WorkoutDetail.fromJson(json) {
    return WorkoutDetail(
        fitnessPlanWorkoutStatus: json['fitnessPlanWorkoutStatus'],
        workoutName: json['workoutName'],
        workoutTitle: json['workoutTitle'],
        workoutDuration: json['workoutDuration'],
        workoutSubTitle: json['workoutSubTitle'],
        workoutImageUrl: json['workoutImageUrl'],
        statusImage: json['statusImage'],
        action: Action.fromJson(json['action'] as Map<String, dynamic>),
        workoutType: json['workoutType'],
        dayNumber: json['dayNumber'],
    );
  }
}

import 'package:intl/intl.dart';

import 'plan_list.dart';

class FitnessPlan {
  String? subCategoryCode;
  String? currentDate;
  String? currentDayNumber;
  String? separatorText;
  List<PlanList>? planList;
  int selectedTabIndex;
  bool? isSwapWithTodayActive;
  FitnessPlan({this.subCategoryCode, this.currentDate, this.currentDayNumber, this.separatorText,this.planList, this.selectedTabIndex = 0, this.isSwapWithTodayActive});

  factory FitnessPlan.fromJson(Map<String, dynamic> json) {
    String subCategoryCode = json['subCategoryCode'] ?? "";
    String currentDate =
        json['currentDate'] ?? DateFormat("yyyy-MM-dd").format(DateTime.now());
    String currentDayNumber = json['currentDayNumber'];
    String separatorText = json['separatorText'] ?? " OR ";
    List<PlanList>? planList = (json['planList'] as List<dynamic>?)
        ?.map((e) => PlanList.fromJson(e as Map<String, dynamic>))
        .toList();
    int selectedTabIndex = planList != null
        ? planList.indexWhere(
            (element) => element.date! == currentDate,
          )
        : 0;
    bool isSwapWithTodayActive = json['isSwapWithTodayActive'] ?? true;
    return FitnessPlan(
      subCategoryCode: subCategoryCode,
        currentDate: currentDate,
        planList: planList,
        separatorText: separatorText,
        isSwapWithTodayActive: isSwapWithTodayActive,
        currentDayNumber: currentDayNumber,
        selectedTabIndex: (selectedTabIndex < 0) ? 0 : selectedTabIndex)
    ;
  }
}

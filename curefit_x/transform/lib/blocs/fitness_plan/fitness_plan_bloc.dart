import 'dart:io';

import 'package:bloc/bloc.dart';
import 'package:common/model/error_prop_model.dart';
import 'package:common/network/client.dart';
import 'package:meta/meta.dart';
import 'package:transform/network/content_repository.dart';

import 'fitness_plan_models/fitness_plan.dart';

part 'fitness_plan_event.dart';

part 'fitness_plan_state.dart';

class FitnessPlanBloc extends Bloc<FitnessPlanEvent, FitnessPlanState> {
  final ContentRepository repository;

  FitnessPlanBloc(this.repository) : super(FitnessPlanInitial()) {
    on<LoadFitnessPlanEvent>((event, emit) async {
      await _mapLoadFitnessPlan(event, emit);
    });
    on<MockFitnessPlanEvent>((event, emit) async {
      try {
        emit(FitnessPlanLoading());
        final response =
        await this.repository.loadMockJson("fitness_plan.json");
        if (response == null) {
          emit(FitnessPlanFailed(UnknownError(
              failed: true, responseCode: "404", callbackFunction: refresh)));
        } else {
          emit(FitnessPlanLoaded(plan: FitnessPlan.fromJson(response)));
        }
      } catch (e) {
        print(e);
        emit(FitnessPlanFailed(
            UnknownError(failed: true, callbackFunction: refresh)));
      }
    });
  }

  void refresh({String? subCategoryCode}) {
    this.add(LoadFitnessPlanEvent(subCategoryCode: subCategoryCode));
  }

  Future<void> _mapLoadFitnessPlan(LoadFitnessPlanEvent event,
      Emitter<FitnessPlanState> emit) async {
    emit(FitnessPlanLoading());
    try {
      final response = await this
          .repository
          .getFitnessPlan(subCategoryCode: event.subCategoryCode);

      if (response == null) {
        emit(FitnessPlanFailed(UnknownError(
            failed: true,
            responseCode: "404",
            callbackFunction: () {
              refresh(subCategoryCode: event.subCategoryCode);
            })));
      } else {
        emit(FitnessPlanLoaded(plan: FitnessPlan.fromJson(response)));
      }
    } on NetworkException catch (exception) {
      emit(FitnessPlanFailed(NetworkError(
          failed: true,
          responseCode: '${(exception).statusCode}',
          callbackFunction: refresh)));
    } on SocketException {
      emit(FitnessPlanFailed(NoInternetError(
          failed: true,
          callbackFunction: () {
            refresh(subCategoryCode: event.subCategoryCode);
          })));
    } catch (e) {
      emit(FitnessPlanFailed(UnknownError(
          failed: true,
          callbackFunction: () {
            refresh(subCategoryCode: event.subCategoryCode);
          })));
    }
  }
}

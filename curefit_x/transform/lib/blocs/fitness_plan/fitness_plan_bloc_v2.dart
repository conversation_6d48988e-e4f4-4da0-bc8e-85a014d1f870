import 'dart:io';

import 'package:common/model/error_prop_model.dart';
import 'package:common/network/client.dart';
import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';
import '../../network/content_repository.dart';
import 'fitness_plan_models/fitness_plan.dart';

part 'fitness_plan_state_v2.dart';
part 'fitness_plan_event_v2.dart';




class FitnessPlanBlocV2 extends Bloc<FitnessPlanEventV2, FitnessPlanStateV2> {
  final ContentRepository repository;

  FitnessPlanBlocV2(this.repository) : super(FitnessPlanInitialV2()) {
    on<LoadFitnessPlanEventV2>((event, emit) async {
      await _mapLoadFitnessPlanV2(event, emit);
    });
    on<SwapWorkoutFitnessPlanEventV2>((event, emit) async {
      await _mapSwapWorkoutFitnessPlanEventV2(event, emit);
    });
    // on<SubmitFitnessPlanEventV2>((event, emit) async {
    //   await _mapSubmitFitnessPlanV2(event, emit);
    // });
    on<MockFitnessPlanEventV2>((event, emit) async {
      try {
        emit(FitnessPlanLoadingV2());
        final response =
        await this.repository.loadMockJson("fitness_plan.json");
        if (response == null) {
          emit(FitnessPlanFailedV2(UnknownError(
              failed: true, responseCode: "404", callbackFunction: refresh)));
        } else {
          emit(FitnessPlanLoadedV2(plan: FitnessPlan.fromJson(response)));
        }
      } catch (e) {
        print(e);
        emit(FitnessPlanFailedV2(
            UnknownError(failed: true, callbackFunction: refresh)));
      }
    });
  }

  void refresh({String? subCategoryCode}) {
    this.add(LoadFitnessPlanEventV2(subCategoryCode: subCategoryCode));
  }


  Future<void> _mapLoadFitnessPlanV2(
      LoadFitnessPlanEventV2 event, Emitter<FitnessPlanStateV2> emit) async {
    emit(FitnessPlanLoadingV2());
    try {
      final response = await this
          .repository
          .getFitnessPlanV2(subCategoryCode: event.subCategoryCode ?? "TRANSFORM_PLUS");

      if (response == null) {
        emit(FitnessPlanFailedV2(UnknownError(
            failed: true,
            responseCode: "404",
            callbackFunction: () {
              refresh(subCategoryCode: event.subCategoryCode);
            })));
      } else {
        emit(FitnessPlanLoadedV2(plan: FitnessPlan.fromJson(response)));
      }
    } on NetworkException catch (exception) {
      emit(FitnessPlanFailedV2(NetworkError(
          failed: true,
          responseCode: '${(exception).statusCode}',
          callbackFunction: refresh)));
    } on SocketException {
      emit(FitnessPlanFailedV2(NoInternetError(
          failed: true,
          callbackFunction: () {
            refresh(subCategoryCode: event.subCategoryCode);
          })));
    } catch (e) {
      emit(FitnessPlanFailedV2(UnknownError(
          failed: true,
          callbackFunction: () {
            refresh(subCategoryCode: event.subCategoryCode);
          })));
    }
  }

  Future<void> _mapSwapWorkoutFitnessPlanEventV2(
      SwapWorkoutFitnessPlanEventV2 event, Emitter<FitnessPlanStateV2> emit) async {
    emit(FitnessPlanLoadingV2());
    try {
      final response = await this
          .repository
          .swapWorkoutFitnessPlan(event.payload);

      if (response == null) {
        emit(FitnessPlanFailedV2(UnknownError(
            failed: true,
            responseCode: "404",
            callbackFunction: () {
              refresh(subCategoryCode: event.subCategoryCode);
            })));
      } else {
        refresh(subCategoryCode: event.subCategoryCode);
      }
    } on NetworkException catch (exception) {
      emit(FitnessPlanFailedV2(NetworkError(
          failed: true,
          responseCode: '${(exception).statusCode}',
          callbackFunction: refresh)));
    } on SocketException {
      emit(FitnessPlanFailedV2(NoInternetError(
          failed: true,
          callbackFunction: () {
            refresh(subCategoryCode: event.subCategoryCode);
          })));
    } catch (e) {
      emit(FitnessPlanFailedV2(UnknownError(
          failed: true,
          callbackFunction: () {
            refresh(subCategoryCode: event.subCategoryCode);
          })));
    }
  }


  // Future<void> _mapSubmitFitnessPlanV2(
  //     SubmitFitnessPlanEventV2 event, Emitter<FitnessPlanStateV2> emit) async {
  //   emit(FitnessPlanLoadingV2());
  //   // try {
  //     final response = await this
  //         .repository
  //         .updateFitnessPlanSportWorkout(event.fitnessPlanId, event.payload);

      // if (response == null) {
      //   emit(FitnessPlanFailedV2(UnknownError(
      //       failed: true,
      //       responseCode: "404",
      //       callbackFunction: () {
      //         refresh(subCategoryCode: event.subCategoryCode);
      //       })));
      // } else {
      //   emit(FitnessPlanLoadedV2(plan: FitnessPlan.fromJson(response)));
      // }

    // } on NetworkException catch (exception) {
    //   emit(FitnessPlanFailedV2(NetworkError(
    //       failed: true,
    //       responseCode: '${(exception).statusCode}',
    //       callbackFunction: refresh)));
    // } on SocketException {
    //   emit(FitnessPlanFailedV2(NoInternetError(
    //       failed: true,
    //       callbackFunction: () {
    //         refresh(subCategoryCode: event.subCategoryCode);
    //       })));
    // } catch (e) {
    //   emit(FitnessPlanFailedV2(UnknownError(
    //       failed: true,
    //       callbackFunction: () {
    //         refresh(subCategoryCode: event.subCategoryCode);
    //       })));
    // }
  // }

}

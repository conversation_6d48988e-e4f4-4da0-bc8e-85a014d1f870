part of 'fitness_plan_bloc_v2.dart';

@immutable
abstract class FitnessPlanStateV2 {}

class FitnessPlanInitialV2 extends FitnessPlanStateV2 {
  @override
  String toString() {
    return "FitnessPlanInitialStateV2";
  }
}

class FitnessPlanLoadingV2 extends FitnessPlanStateV2 {
  @override
  String toString() {
    return "FitnessPlanLoadingStateV2";
  }
}

class FitnessPlanLoadedV2 extends FitnessPlanStateV2 {
  final FitnessPlan plan;
  FitnessPlanLoadedV2({required this.plan});
  @override
  String toString() {
    return "FitnessPlanLoadedStateV2";
  }
}

class FitnessPlanFailedV2 extends FitnessPlanStateV2 {
  final ErrorInfo? errorInfo;

  FitnessPlanFailedV2(this.errorInfo) : super();
  @override
  String toString() {
    return "FitnessPlanFailedStateV2";
  }
}

part of 'fitness_plan_bloc.dart';

@immutable
abstract class FitnessPlanState {}

class FitnessPlanInitial extends FitnessPlanState {
  @override
  String toString() {
    return "FitnessPlanInitialState";
  }
}

class FitnessPlanLoading extends FitnessPlanState {
  @override
  String toString() {
    return "FitnessPlanLoadingState";
  }
}

class FitnessPlanLoaded extends FitnessPlanState {
  final FitnessPlan plan;
  FitnessPlanLoaded({required this.plan});
  @override
  String toString() {
    return "FitnessPlanLoadedState";
  }
}

class FitnessPlanFailed extends FitnessPlanState {
  final ErrorInfo? errorInfo;

  FitnessPlanFailed(this.errorInfo) : super();
  @override
  String toString() {
    return "FitnessPlanFailedState";
  }
}

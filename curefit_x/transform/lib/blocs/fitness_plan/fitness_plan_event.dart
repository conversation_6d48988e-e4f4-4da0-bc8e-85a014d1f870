part of 'fitness_plan_bloc.dart';

@immutable
abstract class FitnessPlanEvent {}

class LoadFitnessPlanEvent extends FitnessPlanEvent {
  final String? subCategoryCode;

  LoadFitnessPlanEvent({this.subCategoryCode}) : super();

  @override
  String toString() {
    return "LoadFitnessPlanEvent";
  }
}


class MockFitnessPlanEvent extends FitnessPlanEvent {
  @override
  String toString() {
    return "MockFitnessPlanEvent";
  }
}


part of 'fitness_plan_bloc_v2.dart';

@immutable
abstract class FitnessPlanEventV2 {}

class LoadFitnessPlanEventV2 extends FitnessPlanEventV2 {
  final String? subCategoryCode;

  LoadFitnessPlanEventV2({this.subCategoryCode}) : super();

  @override
  String toString() {
    return "LoadFitnessPlanEventV2";
  }
}

// class SubmitFitnessPlanEventV2 extends FitnessPlanEventV2 {
//   final String fitnessPlanId;
//   final Map<String, String> payload;
//
//   SubmitFitnessPlanEventV2({required this.fitnessPlanId, required this.payload}) : super();
//
//   @override
//   String toString() {
//     return "SubmitFitnessPlanEventV2";
//   }
// }

// SwapWorkoutFitnessPlanEventV2
class SwapWorkoutFitnessPlanEventV2 extends FitnessPlanEventV2 {
  final String? subCategoryCode;
  final Map<String, String> payload;

  SwapWorkoutFitnessPlanEventV2({this.subCategoryCode, required this.payload}) : super();

  @override
  String toString() {
    return "SwapWorkoutFitnessPlanEventV2";
  }
}


class MockFitnessPlanEventV2 extends FitnessPlanEventV2 {
  @override
  String toString() {
    return "MockFitnessPlanEventV2";
  }
}

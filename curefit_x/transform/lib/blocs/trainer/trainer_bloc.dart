import 'package:common/constants/common_constants.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/trainer/events.dart';
import 'package:transform/blocs/trainer/state.dart';
import 'package:transform/blocs/trainer/models.dart';
import 'package:common/network/trainer_repository.dart';

class TrainerBloc extends Bloc<TrainerEvent, TrainerState> {
  final TrainerRepository repository;

  TrainerBloc(TrainerState initialState, this.repository)
      : super(initialState) {
    on<LoadTrainerEvent>((event, emit) async {
      await _mapMealDetailsToState(event, emit);
    });
  }

  Future<void> _mapMealDetailsToState(
      LoadTrainerEvent event, Emitter<TrainerState> emit) async {
    try {
      final response = await this.repository.getTrainerPage(event.productId!);
      if (response == null) {
        emit(TrainerNotLoaded(message: ERROR_MSG));
      }
      final TrainerPage trainerPage = TrainerPage(response['widgets']);
      emit(TrainerLoaded(trainerPage: trainerPage));
    } catch (error) {
      print(error);
      emit(TrainerNotLoaded(message: ERROR_MSG));
    }
  }
}

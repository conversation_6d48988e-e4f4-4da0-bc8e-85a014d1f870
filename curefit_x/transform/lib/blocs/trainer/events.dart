abstract class TrainerEvent {
  TrainerEvent() : super();
}

class LoadTrainerEvent extends TrainerEvent {
  final String? productId;

  LoadTrainerEvent(this.productId) : super();

  @override
  String toString() => 'LoadTrainerEvent';
}

class LoadCoachDetailEvent extends TrainerEvent {
  final String? coachId;
  final String? heroId;
  final String? productId;

  LoadCoachDetailEvent(this.coachId, {this.heroId, this.productId}) : super();

  @override
  String toString() => 'LoadCoachDetailEvent';
}

class SubmitCoachPreference extends TrainerEvent {
  final String bookingId;
  final Map? selectedOptionMap;
  final String? subCategoryCode;

  SubmitCoachPreference({required this.bookingId, this.selectedOptionMap, this.subCategoryCode});

  @override
  String toString() => 'SubmitCoachPreference';
}

class LoadTransformCoachesDetailEvent extends TrainerEvent {
  final String? subCategoryCode;
  LoadTransformCoachesDetailEvent({this.subCategoryCode}) : super();

  @override
  String toString() => 'LoadTransformCoachesDetailEvent';
}

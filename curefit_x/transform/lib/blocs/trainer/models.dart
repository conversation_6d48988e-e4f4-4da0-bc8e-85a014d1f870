import 'package:common/action/action_handler.dart';
import 'package:common/constants/action_constants.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:common/util/util.dart';
import 'package:enum_to_string/enum_to_string.dart';

class TrainerPage {
  TrainerPage(this.widgets);

  List? widgets;
}

class CoachDetailPage {
  CoachDetailPage(this.widgets, {this.actions});

  List? widgets;
  List<Action>? actions;
}

class IWidget {
  IWidget(this.widgetType);

  String widgetType;
}

class CoachRecommendationWidgetData implements IWidgetData {
  CoachRecommendationWidgetData(this.widgetType, this.title, this.coaches);

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  final String? title;
  final List<Coach>? coaches;

  static CoachRecommendationWidgetData fromJson(
      widget, WidgetTypes widgetType) {
    return CoachRecommendationWidgetData(
        widgetType,
        widget['title'],
        widget['data'].map<Coach>((dynamic coach) {
          String heroId = getRandString(6);
          return Coach(
              coach['title'],
              coach['subtitle'],
              coach['imageUrl'],
              coach['description'],
              Action(
                  type: EnumToString.fromString(
                      ActionTypes.values, coach['action']['actionType']),
                  url: coach['action']['url'],
                  meta: {"imageUrl": coach['imageUrl'], "heroId": heroId}),
              heroId: heroId);
        }).toList());
  }
}

class CoachListingWidgetData implements IWidgetData {
  final String? title;
  final String? subtitle;
  final String? imageUrl;
  final String? description;
  final Action action;
  final String? heroId;

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  CoachListingWidgetData(this.widgetType, this.title, this.subtitle,
      this.imageUrl, this.description, this.action,
      {this.heroId});

  static CoachListingWidgetData fromJson(widget, WidgetTypes widgetType) {
    String heroId = getRandString(6);
    return CoachListingWidgetData(
        widgetType,
        widget['title'],
        widget['subtitle'],
        widget['imageUrl'],
        widget['description'],
        Action(
            type: EnumToString.fromString(
                ActionTypes.values, widget['action']['actionType']),
            url: widget['action']['url'],
            meta: {"imageUrl": widget['imageUrl'], "heroId": heroId}),
        heroId: heroId);
  }
}

class Coach {
  final String? title;
  final String? subtitle;
  final String? imageUrl;
  final String? description;
  final Action action;
  final String? heroId;

  Coach(this.title, this.subtitle, this.imageUrl, this.description, this.action,
      {this.heroId});
}

class CoachSectionItem {
  String? iconUrl;
  String? text;
}

class CoachSection {
  String? title;
  String? subtitle;
  String? imageUrl;
  String? about;
  String? tagTitle;
  String? funFactText;
  List<CoachSectionItem>? sectionItems;

  CoachSection(
      {this.title,
      this.about,
      this.subtitle,
      this.imageUrl,
      this.sectionItems,
      this.tagTitle,
      this.funFactText});

  factory CoachSection.fromJson(json) {
    return CoachSection(
      title: json['title'],
      subtitle: json['subtitle'],
      imageUrl: json['imageUrl'],
      about: json['about'],
      sectionItems: json['sectionItems'],
      tagTitle: json['tagTitle'],
        funFactText: json['funFactText']
    );
  }
}

class CoachDetailWidgetData implements IWidgetData {
  final String? title;
  final String? subtitle;
  final List<String>? images;
  final String? description;
  final CoachSection? coachSection;
  final String? heroId;

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  CoachDetailWidgetData(
      this.widgetType, this.title, this.subtitle, this.description,
      {this.images, this.coachSection, this.heroId});

  factory CoachDetailWidgetData.fromJson(
    widget,
    WidgetTypes widgetType,
  ) {
    return CoachDetailWidgetData(
        widgetType, widget['title'], widget['subtitle'], widget['description'],
        images: widget['images'].map<String>((e) => e.toString()).toList(),
        coachSection: widget['coachSection'] != null
            ? CoachSection(
                title: widget['coachSection']['title'],
                about: widget['coachSection']['about'])
            : null,
        heroId: widget["heroId"]);
  }
}

class CoachDetailCardsWidgetData implements IWidgetData {
  final String? title;
  final String? subtitle;
  final List<CoachSection>? coachSections;

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  CoachDetailCardsWidgetData(this.widgetType,
      {this.title, this.subtitle, this.coachSections, this.widgetInfo});

  factory CoachDetailCardsWidgetData.fromJson(widget, WidgetTypes widgetType,
      {WidgetInfo? widgetInfo}) {
    return CoachDetailCardsWidgetData(
      widgetType,
      title: widget['title'],
      subtitle: widget['subtitle'] ?? "",
      coachSections: widget['coachSections'] != null
          ? widget['coachSections'].map<CoachSection>((detail) {
              return CoachSection.fromJson(detail);
            }).toList()
          : null,
      widgetInfo: widgetInfo,
    );
  }
}

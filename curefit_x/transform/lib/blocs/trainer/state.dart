import 'package:meta/meta.dart';
import 'package:transform/blocs/trainer/models.dart';

@immutable
abstract class TrainerState {
  TrainerState() : super();
}

class TrainerIdle extends TrainerState {
  @override
  String toString() => 'IdleState';
}

class TrainerLoading extends TrainerState {
  @override
  String toString() => 'TrainerLoading';
}

class TrainerLoaded extends TrainerState {
  final TrainerPage trainerPage;

  TrainerLoaded({required this.trainerPage})
      : assert(trainerPage != null),
        super();

  @override
  String toString() => 'TrainerLoaded';
}

class TrainerNotLoaded extends TrainerState {
  final Object? error;
  final String? message;
  TrainerNotLoaded({this.error, this.message}) : super();

  @override
  String toString() => 'TrainerNotLoaded';
}

class CoachDetailLoading extends TrainerState {
  @override
  String toString() => 'CoachDetailLoading';
}

class CoachDetailLoaded extends TrainerState {
  final CoachDetailPage coachDetailPage;

  CoachDetailLoaded({required this.coachDetailPage})
      : assert(coachDetailPage != null),
        super();

  @override
  String toString() => 'CoachDetailLoaded';
}

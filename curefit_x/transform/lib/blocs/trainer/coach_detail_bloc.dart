import 'package:common/action/action_handler.dart';
import 'package:common/constants/common_constants.dart';
import 'package:common/network/client.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/trainer/events.dart';
import 'package:transform/blocs/trainer/state.dart';
import 'package:transform/blocs/trainer/models.dart';
import 'package:common/network/trainer_repository.dart';

class CoachDetailBloc extends Bloc<TrainerEvent, TrainerState> {
  final TrainerRepository repository;

  CoachDetailBloc(TrainerState initialState, this.repository)
      : super(initialState) {
    on<LoadCoachDetailEvent>((event, emit) async {
      await _mapCoachDetailsToState(event, emit);
    });

    on<SubmitCoachPreference>((event, emit) async {
      await _mapCoachPreferenceToState(event, emit);
    });

    on<LoadTransformCoachesDetailEvent>((event, emit) async {
      await _mapTransformCoachesDetailsToState(event, emit);
    });
  }

  Future<void> _mapCoachPreferenceToState(
      SubmitCoachPreference event, Emitter<TrainerState> emit) async {
    try {
      emit(CoachDetailLoading());
      final response = await this.repository.submitCoachPreference(
          bookingId: event.bookingId,
          selectedOptionMap: event.selectedOptionMap,
        subCategoryCode: event.subCategoryCode,
      );
      if (response == null) {
        emit(TrainerNotLoaded(message: ERROR_MSG));
      }
      final CoachDetailPage coachDetailPage = CoachDetailPage(
          response['widgets'],
          actions: response['actions'] != null
              ? response['actions']
                  .map<Action>((e) => Action.fromJson(e))
                  .toList()
              : []);
      emit(CoachDetailLoaded(coachDetailPage: coachDetailPage));
    } on NetworkException catch (exception) {
      emit(TrainerNotLoaded(
          message: exception.title ?? "Something went wrong."));
    } catch (e) {
      emit(TrainerNotLoaded(message: "Something went wrong."));
    }
  }

  Future<void> _mapCoachDetailsToState(
      LoadCoachDetailEvent event, Emitter<TrainerState> emit) async {
    try {
      final response = await this
          .repository
          .getCoachDetails(event.coachId!, event.productId!);
      if (response == null) {
        emit(TrainerNotLoaded(message: ERROR_MSG));
      }
      final detail = response;
      final CoachDetailPage coachDetailPage = CoachDetailPage(
          detail['widgets'].map((e) {
            e['heroId'] = event.heroId;
            return e;
          }).toList(),
          actions: detail['actions'] != null
              ? detail['actions']
                  .map<Action>((e) => Action.fromJson(e))
                  .toList()
              : []);
      emit(CoachDetailLoaded(coachDetailPage: coachDetailPage));
    } on NetworkException catch (error) {
      emit(TrainerNotLoaded(message: ERROR_MSG));
    } catch (e) {
      emit(TrainerNotLoaded(message: ERROR_MSG));
    }
  }

  Future<void> _mapTransformCoachesDetailsToState(
      LoadTransformCoachesDetailEvent event, Emitter<TrainerState> emit) async {
    try {
      final response = await this
          .repository.getTransformCoachesDetails(subCategoryCode: event.subCategoryCode);
      if (response == null) {
        emit(TrainerNotLoaded(message: ERROR_MSG));
      }
      final detail = response;
      final CoachDetailPage coachDetailPage = CoachDetailPage(
          detail['widgets'],
          actions: detail['actions'] != null
              ? detail['actions']
              .map<Action>((e) => Action.fromJson(e))
              .toList()
              : []);
      emit(CoachDetailLoaded(coachDetailPage: coachDetailPage));
    } on NetworkException catch (error) {
      emit(TrainerNotLoaded(message: ERROR_MSG));
    } catch (e) {
      emit(TrainerNotLoaded(message: ERROR_MSG));
    }
  }
}

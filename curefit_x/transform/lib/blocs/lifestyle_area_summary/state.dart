import 'package:transform/blocs/lifestyle_area_summary/models.dart';

abstract class LifestyleAreaSummaryState {
  LifestyleAreaSummaryState() : super();
}

class LifestyleAreaSummaryIdleState extends LifestyleAreaSummaryState {
  @override
  String toString() => 'LifestyleAreaSummaryIdleState';
}

class LifestyleAreaSummaryLoadingState extends LifestyleAreaSummaryState {
  @override
  String toString() => 'LifestyleAreaSummaryLoadingState';
}

class LifestyleAreaSummaryLoadedState extends LifestyleAreaSummaryState {
  final LifestyleFormInfoScreenData lifestyleFormInfoScreenData;

  LifestyleAreaSummaryLoadedState(this.lifestyleFormInfoScreenData)
      : super();

  @override
  String toString() => 'LifestyleAreaSummaryLoadedState';
}

class LifestyleAreaSummaryFailedState extends LifestyleAreaSummaryState {
  @override
  String toString() => 'LifestyleAreaSummaryFailedState';
}

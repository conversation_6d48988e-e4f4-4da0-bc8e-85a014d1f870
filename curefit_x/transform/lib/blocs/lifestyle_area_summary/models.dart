import 'package:common/ui/aurora.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:common/action/action_handler.dart';
import 'package:enum_to_string/enum_to_string.dart';

class LifestyleFormInfoScreenData {
  String backgroundImageUrl;
  dynamic widget;
  Action onCloseAction;
  final CanvasTheme? themeType;

  LifestyleFormInfoScreenData(this.backgroundImageUrl, this.widget, this.onCloseAction,{this.themeType});

  static LifestyleFormInfoScreenData fromJson(widgetData) {
    return LifestyleFormInfoScreenData(
      widgetData["backgroundImageUrl"],
      widgetData["widget"][0],
      Action.fromJson(widgetData["onCloseAction"],),
      themeType: widgetData['themeType'] != null ? EnumToString.fromString(CanvasTheme.values, widgetData['themeType']) : null,
    );
  }
}

class LifestyleFormIntroWidgetData implements IWidgetData {
  LifestyleFormIntroWidgetData(this.widgetType,
      {this.title,
      this.lottieUrl,
      this.description,
      this.lifestyleImageDataList,
      this.action});

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  final String? title;
  final String? lottieUrl;
  final String? description;
  final List<LifestyleImageData>? lifestyleImageDataList;
  final Action? action;

  static LifestyleFormIntroWidgetData fromJson(widget, WidgetTypes widgetType) {
    return LifestyleFormIntroWidgetData(
      widgetType,
      title: widget["formIntroWidget"]["title"],
      lottieUrl: widget["formIntroWidget"]["lottieUrl"],
      description: widget["formIntroWidget"]["description"],
      lifestyleImageDataList: widget["formIntroWidget"]["imageDataList"] != null
          ? widget["formIntroWidget"]["imageDataList"]
              .map<LifestyleImageData>(
                  (data) => LifestyleImageData.fromJson(data))
              .toList()
          : null,
      action: Action.fromJson(widget["formIntroWidget"]["action"]),
    );
  }
}

class LifestyleAreaSummaryWidgetData implements IWidgetData {
  LifestyleAreaSummaryWidgetData(this.widgetType,
      {this.title,
      this.imageUrl,
      this.score,
      this.lottieUrl,
      this.description,
      this.subtitle,
      this.lifestyleImageDataList,
      this.action,
      });

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  final String? title;
  final String? imageUrl;
  final int? score;
  final String? lottieUrl;
  final String? description;
  final String? subtitle;
  final List<LifestyleImageData>? lifestyleImageDataList;
  final Action? action;

  static LifestyleAreaSummaryWidgetData fromJson(
      widget, WidgetTypes widgetType) {
    return LifestyleAreaSummaryWidgetData(
      widgetType,
      title: widget["areaSummaryWidget"]["title"],
      imageUrl: widget["areaSummaryWidget"]["imageUrl"],
      score: widget["areaSummaryWidget"]["score"],
      lottieUrl: widget["areaSummaryWidget"]["lottieUrl"],
      description: widget["areaSummaryWidget"]["description"],
      subtitle: widget["areaSummaryWidget"]["subtitle"],
      lifestyleImageDataList:
          widget["areaSummaryWidget"]["imageDataList"] != null
              ? widget["areaSummaryWidget"]["imageDataList"]
                  .map<LifestyleImageData>(
                      (data) => LifestyleImageData.fromJson(data))
                  .toList()
              : null,
      action: Action.fromJson(widget["areaSummaryWidget"]["action"]),
    );
  }
}

class LifestyleImageData {
  LifestyleImageData({
    this.title,
    this.imageUrl,
    this.lottieUrl,
    this.opacity,
  });

  final String? title;
  final String? imageUrl;
  final String? lottieUrl;
  final double? opacity;

  static LifestyleImageData fromJson(data) {
    return LifestyleImageData(
      title: data["title"],
      imageUrl: data["imageUrl"],
      lottieUrl: data["lottieUrl"],
      opacity: data["opacity"],
    );
  }
}

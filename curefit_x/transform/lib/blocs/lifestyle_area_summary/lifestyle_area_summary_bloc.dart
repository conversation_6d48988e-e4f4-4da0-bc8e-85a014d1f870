import 'package:common/network/client.dart';
import 'package:common/network/client_repository.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/lifestyle_area_summary/events.dart';
import 'package:transform/blocs/lifestyle_area_summary/models.dart';
import 'package:transform/blocs/lifestyle_area_summary/state.dart';

class LifestyleAreaSummaryBloc
    extends Bloc<LifestyleAreaSummaryEvent, LifestyleAreaSummaryState> {
  final CoachClientRepository repository;

  LifestyleAreaSummaryBloc(
      LifestyleAreaSummaryIdleState lifestyleAreaSummaryIdleState,
      this.repository)
      : super(lifestyleAreaSummaryIdleState) {
    on<LifestyleAreaSummaryLoadEvent>((event, emit) async {
      await _mapLoadLifestyleAreaSummaryToState(event, emit);
    });
  }

  Future<void> _mapLoadLifestyleAreaSummaryToState(
      LifestyleAreaSummaryLoadEvent event,
      Emitter<LifestyleAreaSummaryState> emit) async {
    try {
      emit(LifestyleAreaSummaryLoadingState());
      String? areaId = event.areaId;
      final response = await this.repository.getPageData(
        'v2/transform/lifestyleAreaSummary',
        {
          "areaId": areaId ?? "",
          "subCategoryCode": event.subCategoryCode ?? "",
          "flowType": event.flowType ?? "",
        },
      );
      if (response != null) {
        emit(LifestyleAreaSummaryLoadedState(
          LifestyleFormInfoScreenData.fromJson(response),
        ));
      } else {
        emit(LifestyleAreaSummaryFailedState());
      }
    } on NetworkException catch (exception) {
      emit(LifestyleAreaSummaryFailedState());
    } catch (e) {
      emit(LifestyleAreaSummaryFailedState());
    }
  }
}

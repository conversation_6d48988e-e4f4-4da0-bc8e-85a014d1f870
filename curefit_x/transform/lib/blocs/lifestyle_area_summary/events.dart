abstract class LifestyleAreaSummaryEvent {
  LifestyleAreaSummaryEvent() : super();
}

class LifestyleAreaSummaryLoadEvent extends LifestyleAreaSummaryEvent {
  final String? areaId;
  final String? subCategoryCode;
  final String? flowType;
  LifestyleAreaSummaryLoadEvent({this.areaId, this.subCategoryCode, this.flowType}) : super();

  @override
  String toString() => 'LifestyleAreaSummaryLoadEvent';
}

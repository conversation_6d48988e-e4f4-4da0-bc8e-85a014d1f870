import 'dart:core';

import 'package:common/action/action_handler.dart' as ActionHandler;
import 'package:common/action/action_handler.dart';
import 'package:common/blocs/form/models.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/components/testimonials_widget.dart';
import 'package:common/ui/header/card_header.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:common/ui/widgets/banner_carousel_widget.dart';
import 'package:common/util/color.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:flutter/material.dart' hide Action;
import 'package:transform/UI/clp/widgets/habit_card_widget.dart';
import 'package:transform/UI/waitlist/models/waitlist_data.dart';
import 'package:transform/blocs/lifestyle_area_summary/models.dart';
import 'package:transform/blocs/pack_purchase/models.dart';

class BackgroundTheme {
  final CanvasTheme theme;
  final String? lottieUrl;
  final int height;
  final int width;

  BackgroundTheme(
      {required this.theme,
      required this.lottieUrl,
      required this.height,
      required this.width});
}

class CoachCLPData {
  final ActionHandler.Action? storyAction;
  final String? backgroundImageUrl;
  final Map<String, dynamic>? backgroundImageAttributes;
  final String? lottieUrl;
  final CoachContactWidgetData? coachContactWidgetData;
  final AppbarActionWidgetData? appbarActionWidgetData;
  final BackgroundTheme? backgroundTheme;
  final Action? feedbackAction;
  final String? appBarTitle;
  final String? appBarTitleUrl;
  final Action? weightLoggingAction;
  final Action? fitnessDeviceSyncAction;
  final Action? footerAction;
  final int? weightModalVisibility;
  final int? clearGrantedPermissionEpoch;
  final String? coachMarkImageUrl;

  CoachCLPData(
      {required this.widgets,
      this.backgroundImageUrl,
      this.backgroundImageAttributes,
      this.lottieUrl,
      this.coachContactWidgetData,
      this.appbarActionWidgetData,
      this.backgroundTheme,
      this.storyAction,
      this.feedbackAction,
      this.appBarTitle,
      this.appBarTitleUrl,
      this.weightLoggingAction,
      this.fitnessDeviceSyncAction,
      this.footerAction,
      this.weightModalVisibility,
      this.coachMarkImageUrl,
      this.clearGrantedPermissionEpoch});

  final List widgets;
}

class IWidget {
  IWidget(this.widgetType);

  String widgetType;
}

class CoachmarkTextInfo {
  final String title;
  final String hexColor;
  final TypescaleValues? typescale;

  const CoachmarkTextInfo(
      {required this.title, required this.hexColor, required this.typescale});
}

class CoachmarkInfo {
  final List<CoachmarkTextInfo> textSpans;

  CoachmarkInfo({required this.textSpans});

  factory CoachmarkInfo.fromJson(dynamic payload) {
    return CoachmarkInfo(
        textSpans: payload['textSpans'] != null
            ? payload['textSpans']
                .map<CoachmarkTextInfo>((e) => CoachmarkTextInfo(
                    title: e['title'],
                    hexColor: e['hexColor'],
                    typescale: EnumToString.fromString(
                        TypescaleValues.values, e['typescale'])))
                .toList()
            : []);
  }
}

class CoachContactWidgetData implements IWidgetData {
  CoachContactWidgetData(this.widgetType,
      {this.title,
      this.subtitle,
      this.imagesList,
      this.progressAction,
      this.chatAction,
      this.widgetInfo,
      this.action,
      this.coachmarkInfo,
      this.callAction});

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  final String? title;
  final String? subtitle;
  final List<String>? imagesList;
  final Action? progressAction;
  final Action? chatAction;
  final Action? callAction;
  final Action? action;
  CoachmarkInfo? coachmarkInfo;

  static CoachContactWidgetData fromJson(
      widget, WidgetTypes widgetType, WidgetInfo widgetInfo) {
    return CoachContactWidgetData(
      widgetType,
      widgetInfo: widgetInfo,
      coachmarkInfo: widget['coachmarkInfo'] != null
          ? CoachmarkInfo.fromJson(widget['coachmarkInfo'])
          : null,
      title: widget['contactView']['title'],
      subtitle: widget['contactView']['subtitle'],
      imagesList: widget['contactView']['imagesList'] != null
          ? widget['contactView']['imagesList']
              .map<String>((image) => image.toString())
              .toList()
          : null,
      progressAction: widget['contactView']['progressAction'] != null
          ? Action.fromJson(widget['contactView']['progressAction'])
          : null,
      action: widget['contactView']['action'] != null
          ? Action.fromJson(widget['contactView']['action'])
          : null,
      chatAction: widget['contactView']['chatAction'] != null
          ? Action.fromJson(widget['contactView']['chatAction'])
          : null,
      callAction: widget['contactView']['callAction'] != null
          ? Action.fromJson(widget['contactView']['callAction'])
          : null,
    );
  }
}

class AppbarActionWidgetData implements IWidgetData {
  AppbarActionWidgetData(
    this.widgetType, {
    this.widgetInfo,
    this.textAction,
  });

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;
  final Action? textAction;

  static AppbarActionWidgetData fromJson(
      widget, WidgetTypes widgetType, WidgetInfo widgetInfo) {
    return AppbarActionWidgetData(widgetType,
        widgetInfo: widgetInfo,
        textAction: widget['action'] != null
            ? Action.fromJson(widget['action'])
            : null);
  }
}

class ActionCardWidgetData implements IWidgetData {
  ActionCardWidgetData(this.widgetType, this.widgetInfo, {required this.cards});

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  final List<StepCard> cards;

  static ActionCardWidgetData fromJson(
      widgetData, WidgetTypes widgetType, WidgetInfo widgetInfo) {
    return new ActionCardWidgetData(widgetType, widgetInfo,
        cards: widgetData['cards']
            .map<StepCard>((card) => StepCard.fromJSON(card, widgetInfo))
            .toList());
  }
}

class LockDialogData {
  final String? title;
  final String? description;
  final String? buttonText;

  LockDialogData({this.title, this.description, this.buttonText});

  factory LockDialogData.fromJson(widget) {
    return LockDialogData(
      title: widget['title'],
      description: widget['description'],
      buttonText: widget['buttonTitle'],
    );
  }
}

class LevelCardChapter implements IWidgetData {
  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  LevelCardChapter(this.widgetType,
      {this.widgetInfo,
      this.title,
      this.subtitle,
      this.tagName,
      this.complete = false,
      this.action,
      this.imageBackground,
      this.isLocked = true});

  final String? title;
  final String? subtitle;
  final bool complete;
  final bool isLocked;
  final Action? action;
  final String? tagName;
  final String? imageBackground;
}

class LevelCardWidgetData implements IWidgetData {
  LevelCardWidgetData(this.widgetType, this.title, this.cards,
      {this.widgetInfo,
      this.subtitle,
      this.viewAllAction,
      this.lockDialogData});

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  final String title;
  final Action? viewAllAction;
  final String? subtitle;
  final List<LevelCardChapter> cards;
  final LockDialogData? lockDialogData;

  factory LevelCardWidgetData.fromJson(
      widget, WidgetTypes widgetType, WidgetInfo widgetInfo) {
    return LevelCardWidgetData(
        widgetType,
        widget['title'],
        widget['cards']
            .map<LevelCardChapter>((e) => LevelCardChapter(widgetType,
                widgetInfo: widgetInfo,
                title: e['title'],
                tagName: e['tagName'],
                isLocked: e['isLocked'],
                subtitle: e['subtitle'],
                complete: e['complete'],
                imageBackground: e['imageBackground'],
                action: Action.fromJson(e['action'])))
            .toList(),
        widgetInfo: widgetInfo,
        subtitle: widget['subtitle'],
        lockDialogData: LockDialogData.fromJson(widget['lockInfo']),
        viewAllAction: Action.fromJson(widget['viewAllAction']));
  }
}

class Info {
  Info(this.title, this.value, this.trend, {this.action, this.unit});

  static Info? fromJson(data) {
    if (data == null) return null;
    return Info(data['title'], data['value'], data['trend'],
        action: Action.fromJson(data['action']), unit: data['unit']);
  }

  final String title;
  final double value;
  final Action? action;
  final String? unit;
  final int trend;
}

class ChartItem {
  ChartItem(this.date, this.value);

  final String date;
  final double value;
}

class UserMetricChartWidgetData implements IWidgetData {
  UserMetricChartWidgetData(this.widgetType,
      {this.widgetInfo,
      this.subtitle,
      this.progressText,
      this.progressImageUrl,
      this.chartItems,
      this.chartColor,
      this.title,
      this.action,
      this.icon,
      this.rightInfo,
      this.leftInfo,
      this.bottomInfo,
      this.card});

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  final String? title;
  final String? subtitle;
  final String? progressText;
  final String? progressImageUrl;
  final List<ChartItem>? chartItems;
  final int? chartColor;
  final Action? action;
  final String? icon;
  final Info? leftInfo;
  final Info? rightInfo;
  final Info? bottomInfo;
  final bool? card;

  factory UserMetricChartWidgetData.fromJson(
      widget, WidgetTypes widgetType, WidgetInfo widgetInfo) {
    return UserMetricChartWidgetData(widgetType,
        widgetInfo: widgetInfo,
        subtitle: widget['userMetricChart']['subtitle'],
        progressText: widget['userMetricChart']['progressText'],
        progressImageUrl: widget['userMetricChart']['progressImageUrl'],
        card: widget['userMetricChart']['card'],
        chartItems: widget['userMetricChart']['chartItems'] != null
            ? widget['userMetricChart']['chartItems']
                .map<ChartItem>((e) => ChartItem(e['date'], e['value']))
                .toList()
            : null,
        chartColor: widget['userMetricChart']['chartColor'],
        title: widget['userMetricChart']['title'],
        action: Action.fromJson(widget['userMetricChart']['action']),
        icon: widget['userMetricChart']['icon'],
        leftInfo: Info.fromJson(widget['userMetricChart']['leftInfo']),
        rightInfo: Info.fromJson(widget['userMetricChart']['rightInfo']),
        bottomInfo: Info.fromJson(widget['userMetricChart']['bottomInfo']));
  }
}

class HabitActionButton {
  HabitActionButton(this.text, this.action);

  final String text;
  final Action? action;
}

enum UserResponse { DONE, ALMOST_DONE, NOT_DONE, NO_RESPONSE }

enum HabitStatus { CREATED, ACTION_RECEIVED }

class WorkoutInfo {
  WorkoutInfo(
      {this.name,
      this.duration,
      this.image,
      this.action,
      this.completed = false});

  static WorkoutInfo? fromJson(data) {
    if (data == null) return null;

    return WorkoutInfo(
        name: data['name'],
        duration: data['duration'],
        image: data['image'],
        action: Action.fromJson(data['action']),
        completed: data['completed']);
  }

  final String? name;
  final String? duration;
  final String? image;
  final Action? action;
  final bool completed;
}

class HabitCardAnimation {
  final String lottieUrl;
  final String title;

  HabitCardAnimation({required this.lottieUrl, required this.title});

  factory HabitCardAnimation.fromJson(dynamic payload) {
    return HabitCardAnimation(
        lottieUrl: payload['lottieUrl'], title: payload['title']);
  }
}

class HabitCard {
  HabitCard(
      {this.id,
      this.title,
      this.doneAnimation,
      this.notDoneAnimation,
      this.subtitle,
      this.description,
      required this.habitActions,
      required this.undoAction,
      this.name,
      this.header,
      this.type,
      this.workoutInfo,
      this.status = HabitStatus.CREATED,
      this.response = UserResponse.NO_RESPONSE,
      this.tapAction,
      this.streakText,
      this.imageUrl,
      this.habitStatusText,
      this.metricsText,
      this.coachmarkInfo});

  final CoachmarkInfo? coachmarkInfo;
  final String? id;
  final String? title;
  final String? subtitle;
  final String? description;
  final String? name;
  final List<HabitActionButton> habitActions;
  final HabitActionButton undoAction;
  final HabitCardType? type;
  final String? header;
  UserResponse response;
  final HabitStatus status;
  final WorkoutInfo? workoutInfo;
  final HabitCardAnimation? doneAnimation;
  final HabitCardAnimation? notDoneAnimation;
  final Action? tapAction;
  final String? streakText;
  final String? metricsText;
  final String? imageUrl;
  final String? habitStatusText;
}

class MealDetailsWidgetData implements IWidgetData {
  MealDetailsWidgetData(this.widgetType,
      {this.widgetInfo, this.data, this.mealTypesWithMealNumberMap});

  @override
  WidgetTypes widgetType;

  @override
  WidgetInfo? widgetInfo;

  Map<String, dynamic>? data;
  Map<String, String>? mealTypesWithMealNumberMap;

  factory MealDetailsWidgetData.fromJson(widget,
      {required WidgetTypes widgetType, required WidgetInfo widgetInfo}) {
    return MealDetailsWidgetData(
      widgetType,
      widgetInfo: widgetInfo,
      data: widget['data'],
      mealTypesWithMealNumberMap: widget['mealTypesWithMealNumberMap'] != null
          ? Map.castFrom(widget['mealTypesWithMealNumberMap'])
          : null,
    );
  }
}

class MealDetailsWidgetDataV2 implements IWidgetData {
  MealDetailsWidgetDataV2(this.widgetType,
      {this.widgetInfo, this.mealPlanDataV2});

  @override
  WidgetTypes widgetType;

  @override
  WidgetInfo? widgetInfo;

  List<MealPlanDataV2>? mealPlanDataV2;

  factory MealDetailsWidgetDataV2.fromJson(widget,
      {required WidgetTypes widgetType, required WidgetInfo widgetInfo}) {
    return MealDetailsWidgetDataV2(
      widgetType,
      widgetInfo: widgetInfo,
      mealPlanDataV2: widget['mealPlanDataV2List'] != null
          ? widget['mealPlanDataV2List'].map<MealPlanDataV2>((data) {
              return MealPlanDataV2.fromJson(data);
            }).toList()
          : null,
    );
  }
}

class MealPlanDataV2 {
  MealPlanDataV2({required this.mealDetails, this.id = ""});

  List<MealDetails> mealDetails;
  String id;

  factory MealPlanDataV2.fromJson(json) {
    return MealPlanDataV2(
      id: json['id'] ?? "",
      mealDetails: json['mealDetails'] != null
          ? json['mealDetails'].map<MealDetails>((data) {
              return MealDetails.fromJson(data);
            }).toList()
          : null,
    );
  }
}

class MealDetails {
  MealDetails(
      {this.title,
      this.description,
      this.id,
      this.foodGroup,
      this.foodGroupColor,
      this.unitImage});

  String? title;
  String? description;
  String? id;
  String? foodGroup;
  String? foodGroupColor;
  String? unitImage;

  factory MealDetails.fromJson(json) {
    return MealDetails(
        title: json['title'],
        id: json['id'],
        foodGroup: json['foodGroup'],
        description: json['description'],
        foodGroupColor: json['foodGroupColor'],
        unitImage: json['unitImage']);
  }
}

class MealData {
  Map<String, dynamic>? data;

  MealData(this.data);
}

class MealPlanEmptyStateWidgetData implements IWidgetData {
  MealPlanEmptyStateWidgetData(this.widgetType,
      {this.imageUrl,
      this.title,
      this.subtitle,
      this.primaryButtonAction,
      this.secondaryButtonAction});

  @override
  WidgetTypes widgetType;

  @override
  WidgetInfo? widgetInfo;

  final String? imageUrl;
  final String? title;
  final String? subtitle;
  final Action? primaryButtonAction;
  final Action? secondaryButtonAction;

  factory MealPlanEmptyStateWidgetData.fromJson(widget,
      {required WidgetTypes widgetType, required WidgetInfo widgetInfo}) {
    return MealPlanEmptyStateWidgetData(widgetType,
        imageUrl: widget['imageUrl'],
        title: widget['title'],
        subtitle: widget['subtitle'],
        primaryButtonAction: widget['primaryButtonAction'] != null
            ? Action.fromJson(widget['primaryButtonAction'])
            : null,
        secondaryButtonAction: widget['secondaryButtonAction'] != null
            ? Action.fromJson(widget['secondaryButtonAction'])
            : null);
  }
}

class HabitDateWidgetData implements IWidgetData {
  HabitDateWidgetData(this.widgetType, {this.habitDates, this.widgetInfo});

  @override
  WidgetTypes widgetType;

  @override
  WidgetInfo? widgetInfo;

  final List<String>? habitDates;

  factory HabitDateWidgetData.fromJson(widget,
      {required WidgetTypes widgetType, required WidgetInfo widgetInfo}) {
    return HabitDateWidgetData(
      widgetType,
      habitDates: widget["habitDates"] != null
          ? widget["habitDates"].map<String>((date) => date.toString()).toList()
          : [],
      widgetInfo: widgetInfo,
    );
  }
}

class HabitCardWidgetData implements IWidgetData {
  HabitCardWidgetData(this.widgetType, this.title,
      {this.bottomPadding,
      this.habitCards,
      this.subtitle,
      this.timestamp,
      this.widgetInfo});

  @override
  WidgetTypes widgetType;

  final String? title;
  final String? timestamp;
  final String? subtitle;
  final List<HabitCard>? habitCards;
  final double? bottomPadding;

  @override
  WidgetInfo? widgetInfo;

  factory HabitCardWidgetData.fromJson(widget,
      {required WidgetTypes widgetType, WidgetInfo? widgetInfo}) {
    return HabitCardWidgetData(widgetType, widget['title'],
        bottomPadding: 10.0,
        habitCards: widget['habitCards'] != null
            ? widget['habitCards']
                .map<HabitCard>((e) => HabitCard(
                      coachmarkInfo: e['coachmarkInfo'] != null
                          ? CoachmarkInfo.fromJson(e['coachmarkInfo'])
                          : null,
                      status: EnumToString.fromString(
                              HabitStatus.values, e['status']) ??
                          HabitStatus.CREATED,
                      response: EnumToString.fromString(
                              UserResponse.values, e['response']) ??
                          UserResponse.NO_RESPONSE,
                      id: e['id'],
                      title: e['title'],
                      doneAnimation: e['doneAnimation'] != null
                          ? HabitCardAnimation.fromJson(e['doneAnimation'])
                          : null,
                      notDoneAnimation: e['notDoneAnimation'] != null
                          ? HabitCardAnimation.fromJson(e['notDoneAnimation'])
                          : null,
                      name: e['type'],
                      subtitle: e['subtitle'],
                      description: e['description'],
                      habitActions: e['actions']
                          .map<HabitActionButton>((action) =>
                              new HabitActionButton(action["title"],
                                  Action.fromJson(action['action'])))
                          .toList(),
                      undoAction: new HabitActionButton(
                          e['undoAction']["title"],
                          Action.fromJson(e['undoAction']['action'])),
                      workoutInfo: WorkoutInfo.fromJson(e['workoutInfo']),
                      type: EnumToString.fromString(
                              HabitCardType.values, e['type']) ??
                          HabitCardType.HABIT,
                      header: e['header'],
                      tapAction: Action.fromJson(e['tapAction']),
                      streakText: e['streakText'],
                      metricsText: e['metricsText'],
                      imageUrl: e['imageUrl'],
                      habitStatusText: e['habitStatusText'],
                    ))
                .toList()
            : null,
        subtitle: widget['subtitle'],
        widgetInfo: widgetInfo,
        timestamp: widget["timestamp"]);
  }
}

class ProgressUpdateWidgetData implements IWidgetData {
  ProgressUpdateWidgetData(this.widgetType, this.title, this.action,
      {this.widgetInfo,
      this.metric,
      this.value,
      this.unit,
      this.description,
      this.imageUrl,
      this.habitClpWidget,
      this.lifestyleClpWidget,
      this.weightGraphWidget,
      this.noMeasurementWidget});

  @override
  WidgetInfo? widgetInfo;

  factory ProgressUpdateWidgetData.fromJson(
      data, WidgetTypes widgetType, WidgetInfo widgetInfo) {
    return ProgressUpdateWidgetData(
      widgetType,
      data['title'],
      Action.fromJson(data['action']),
      widgetInfo: widgetInfo,
      metric: data['metric'],
      value: data['value'],
      unit: data['unit'],
      description: data['description'],
      imageUrl: data['imageUrl'],
      habitClpWidget: data['habitClpWidget'] != null
          ? HabitProgressClpWidgetData.fromJson(data['habitClpWidget'])
          : null,
      lifestyleClpWidget: data['lifestyleClpWidget'] != null
          ? LifestyleProgressClpWidgetData.fromJson(data['lifestyleClpWidget'])
          : null,
      weightGraphWidget: data['weightGraphWidget'] != null
          ? ProgressChartClpData.fromJson(data['weightGraphWidget'])
          : null,
      noMeasurementWidget: data['noMeasurementProgressWidget'] != null
          ? NoMeasurementProgressWidgetData.fromJson(
              data['noMeasurementProgressWidget'])
          : null,
    );
  }

  @override
  WidgetTypes widgetType;

  final String title;
  final Action action;
  final String? description;
  final String? metric;
  final String? unit;
  final String? value;
  final String? imageUrl;
  final HabitProgressClpWidgetData? habitClpWidget;
  final LifestyleProgressClpWidgetData? lifestyleClpWidget;
  final ProgressChartClpData? weightGraphWidget;
  final NoMeasurementProgressWidgetData? noMeasurementWidget;
}

class HabitProgressClpWidgetData {
  HabitProgressClpWidgetData(
      {this.habitCount,
      this.action,
      this.description,
      this.isCard,
      this.imageUrl});

  factory HabitProgressClpWidgetData.fromJson(data) {
    return HabitProgressClpWidgetData(
      habitCount: data['totalCount'],
      action: Action.fromJson(data['action']),
      description: data['description'],
      isCard: data['card'],
      imageUrl: data['imageUrl'],
    );
  }

  final String? habitCount;
  final Action? action;
  final String? description;
  final bool? isCard;
  final String? imageUrl;
}

class LifestyleProgressClpWidgetData {
  LifestyleProgressClpWidgetData(
      {this.lifestyleScore,
      this.action,
      this.description,
      this.isCard,
      this.imageUrl});

  factory LifestyleProgressClpWidgetData.fromJson(data) {
    return LifestyleProgressClpWidgetData(
      lifestyleScore: data['lifestyleScore'],
      action: Action.fromJson(data['action']),
      description: data['description'],
      isCard: data['card'],
      imageUrl: data['imageUrl'],
    );
  }

  final String? lifestyleScore;
  final Action? action;
  final String? description;
  final bool? isCard;
  final String? imageUrl;
}

class ProgressChartClpData {
  ProgressChartClpData({
    this.progressTextValue,
    this.subtitle,
    this.chartItems,
    this.action,
  });

  final String? subtitle;
  final String? progressTextValue;
  final List<ChartItem>? chartItems;
  final Action? action;

  factory ProgressChartClpData.fromJson(data) {
    return ProgressChartClpData(
      subtitle: data['subtitle'],
      progressTextValue: data['progressTextValue'],
      chartItems: data['chartItems'] != null
          ? data['chartItems']
              .map<ChartItem>((e) => ChartItem(e['date'], e['value']))
              .toList()
          : null,
      action: Action.fromJson(data['action']),
    );
  }
}

class NoMeasurementProgressWidgetData {
  NoMeasurementProgressWidgetData({this.action, this.description});

  factory NoMeasurementProgressWidgetData.fromJson(data) {
    return NoMeasurementProgressWidgetData(
      action: Action.fromJson(data['action']),
      description: data['description'],
    );
  }

  final Action? action;
  final String? description;
}

enum StepCardState { ACTIVE, COMPLETED, LOCKED, COMPLETED_CLICKABLE }

class StepCard {
  StepCard(
      {required this.title,
      this.action,
      this.image,
      this.lottieUrl,
      this.description,
      this.subtite,
      this.state = StepCardState.LOCKED,
      this.lifestyleImageDataList,
      this.widgetInfo,
      this.lockText,
      this.imageCards,
      this.showUpcomingCard = false});

  WidgetInfo? widgetInfo;
  final String title;
  final String? description;
  final String? subtite;
  final StepCardState state;
  final Action? action;
  final String? lottieUrl;
  final String? image;
  final List<LifestyleImageData>? lifestyleImageDataList;
  final bool showUpcomingCard;
  final String? lockText;
  final List<ImageCard>? imageCards;

  static StepCard fromJSON(e, WidgetInfo? widgetInfo) {
    return StepCard(
      title: e['title'],
      subtite: e['subtitle'],
      state: e['state'] != null
          ? EnumToString.fromString(StepCardState.values, e['state'])!
          : StepCardState.LOCKED,
      action: e['action'] != null ? Action.fromJson(e['action']) : null,
      image: e['image'],
      lottieUrl: e['lottieUrl'],
      description: e['description'],
      lifestyleImageDataList: e['lifestyleImageDataCards'] != null
          ? e["lifestyleImageDataCards"]
              .map<LifestyleImageData>(
                  (data) => LifestyleImageData.fromJson(data))
              .toList()
          : null,
      imageCards: e['imageCards'] != null
          ? e['imageCards']
              .map<ImageCard>((card) => ImageCard.fromJson(card))
              .toList()
          : null,
      showUpcomingCard: e['showUpcomingCard'] ?? false,
      lockText: e["lockText"],
      widgetInfo: widgetInfo,
    );
  }
}

enum WorkflowCardState {
  ACTIVE,
  COMPLETED,
  LOCKED,
  COMPLETED_CLICKABLE,
  NOT_COMPLETED,
  IN_FOCUS
}

class WorkflowCard {
  WorkflowCard({
    this.title,
    this.action,
    this.secondaryAction,
    this.image,
    this.description,
    this.subtitle,
    this.iconUrl,
    this.workoutCardList,
    this.captureScreenShot = false,
    this.state = WorkflowCardState.ACTIVE,
  });

  final String? title;
  final String? description;
  final String? subtitle;
  final String? iconUrl;
  final WorkflowCardState state;
  final List<WorkoutCard>? workoutCardList;
  final Action? action;
  final Action? secondaryAction;
  final String? image;
  final bool captureScreenShot;

  static WorkflowCard fromJSON(json) {
    return WorkflowCard(
      title: json['title'],
      subtitle: json['subtitle'],
      iconUrl: json['iconUrl'],
      state: json['state'] != null
          ? EnumToString.fromString(WorkflowCardState.values, json['state'])!
          : WorkflowCardState.LOCKED,
      action: json['action'] != null ? Action.fromJson(json['action']) : null,
      secondaryAction: json['secondaryAction'] != null
          ? Action.fromJson(json['secondaryAction'])
          : null,
      image: json['image'],
      description: json['description'],
      captureScreenShot: json['captureScreenShot'] ?? false,
      workoutCardList: json['workoutCardList'] != null
          ? json["workoutCardList"]
              .map<WorkoutCard>((data) => WorkoutCard.fromJSON(data))
              .toList()
          : null,
    );
  }
}

enum StepCardV2State {
  COMPLETED,
  NOT_COMPLETED,
}

class StepCardV2 {
  StepCardV2({this.title, this.cards, this.stepCardV2State});

  final String? title;
  final List<WorkflowCard>? cards;
  final StepCardV2State? stepCardV2State;

  static StepCardV2 fromJSON(json) {
    return StepCardV2(
      title: json['title'],
      cards: json['cards'] != null
          ? json["cards"]
              .map<WorkflowCard>((data) => WorkflowCard.fromJSON(data))
              .toList()
          : null,
      stepCardV2State: json['stepCardV2State'] != null
          ? EnumToString.fromString(
              StepCardV2State.values, json['stepCardV2State'])
          : StepCardV2State.NOT_COMPLETED,
    );
  }
}

class WorkoutCard {
  WorkoutCard({
    this.title,
    this.action,
    this.imageUrl,
    this.description,
    this.subtitle,
    this.showOnlyAction = false,
  });

  final String? title;
  final String? description;
  final String? subtitle;
  final Action? action;
  final String? imageUrl;
  final bool showOnlyAction;

  static WorkoutCard fromJSON(json) {
    return WorkoutCard(
      title: json['title'],
      subtitle: json['subtitle'],
      action: json['action'] != null ? Action.fromJson(json['action']) : null,
      imageUrl: json['imageUrl'],
      description: json['description'],
      showOnlyAction: json['showOnlyAction'] ?? false,
    );
  }
}

class ImageCard {
  final String? title;
  final String? subtitle;
  final String? tagText;
  final bool showIcon;
  final String? imageUrl;

  ImageCard(
      {this.title,
      this.subtitle,
      this.tagText,
      this.imageUrl,
      this.showIcon = true});

  factory ImageCard.fromJson(data) {
    return ImageCard(
      title: data['title'],
      subtitle: data['subtitle'],
      tagText: data['tagText'],
      imageUrl: data['imageUrl'],
      showIcon: data['showIcon'] ?? true,
    );
  }
}

class StepsWidgetData implements IWidgetData {
  StepsWidgetData(this.widgetType, this.title, this.description, this.cards,
      this.cardLists, this.tagText,
      {this.autoAssigned = false, this.isVmWidget = false});

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  final String title;
  final String description;
  final bool autoAssigned;
  final List<StepCard>? cards;
  final List<List<StepCard>>? cardLists;
  final bool? isVmWidget;
  final String tagText;

  static StepsWidgetData fromJson(
      widget, WidgetTypes widgetType, WidgetInfo widgetInfo) {
    List<List<StepCard>> cardLists = [];
    if (widget['cardLists'] != null)
      widget['cardLists'].forEach((eList) {
        List<StepCard> cards = [];
        eList.forEach((e) {
          cards.add(StepCard.fromJSON(e, widgetInfo));
        });
        cardLists.add(cards);
      });

    return StepsWidgetData(
        widgetType,
        widget['title'],
        widget['description'] != null ? widget['description'] : "",
        widget['cards'] != null
            ? widget['cards']
                .map<StepCard>((e) => StepCard.fromJSON(e, widgetInfo))
                .toList()
            : null,
        cardLists,
        widget['tagText'] != null ? widget['tagText'] : "",
        autoAssigned: widget['autoAssigned'],
        isVmWidget: widget['isVmWidget']);
  }
}

class WorkflowWidgetData implements IWidgetData {
  WorkflowWidgetData(
    this.widgetType,
    this.cards,
  );

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  final List<WorkflowCard>? cards;

  static WorkflowWidgetData fromJson(
      widget, WidgetTypes widgetType, WidgetInfo widgetInfo) {
    return WorkflowWidgetData(
      widgetType,
      widget['cards'] != null
          ? widget['cards']
              .map<WorkflowCard>((card) => WorkflowCard.fromJSON(card))
              .toList()
          : null,
    );
  }
}

class OnboardingStepWidgetData implements IWidgetData {
  OnboardingStepWidgetData(
    this.widgetType,
    this.cardsV2,
  );

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  final List<StepCardV2>? cardsV2;

  static OnboardingStepWidgetData fromJson(
      widget, WidgetTypes widgetType, WidgetInfo widgetInfo) {
    return OnboardingStepWidgetData(
      widgetType,
      widget['cardsV2'] != null
          ? widget['cardsV2']
              .map<StepCardV2>((card) => StepCardV2.fromJSON(card))
              .toList()
          : null,
    );
  }
}

class Offering {
  Offering({this.image, this.title, this.text});

  String? image;
  String? title;
  String? text;
}

class PackOfferingsWidgetData implements IWidgetData {
  PackOfferingsWidgetData(this.widgetType, this.title, this.items, this.widget);

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  final String title;
  final List<Offering> items;
  final dynamic widget;

  factory PackOfferingsWidgetData.fromJson(widget, WidgetTypes widgetType) {
    return PackOfferingsWidgetData(
        widgetType,
        widget['title'],
        widget['items']
            .map<Offering>((e) =>
                Offering(image: e['image'], title: e['title'], text: e['text']))
            .toList(),
        widget['widget']);
  }
}

enum CTAButtonTheme { GRADIENT, BLUR }

class QuoteWidgetData implements IWidgetData {
  QuoteWidgetData(
      {required this.widgetType,
      this.title,
      this.action,
      this.subtitle,
      this.waitlistInfo,
      this.buttonTheme = CTAButtonTheme.BLUR,
      this.widgetInfo});

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  final CTAButtonTheme buttonTheme;
  final String? title;
  final String? subtitle;
  final Action? action;
  final WaitlistInfo? waitlistInfo;

  factory QuoteWidgetData.fromJson(widget,
      {required WidgetTypes widgetType, required WidgetInfo widgetInfo}) {
    return QuoteWidgetData(
        widgetInfo: widgetInfo,
        waitlistInfo: widget['waitlistInfo'] != null
            ? WaitlistInfo.fromJson(widget['waitlistInfo'])
            : null,
        widgetType: widgetType,
        title: widget['title'],
        buttonTheme: widget['buttonTheme'] != null
            ? EnumToString.fromString(
                CTAButtonTheme.values, widget['buttonTheme'])!
            : CTAButtonTheme.BLUR,
        subtitle: widget['subtitle'],
        action: Action.fromJson(widget['action']));
  }
}

class FAQWidgetData implements IWidgetData {
  FAQWidgetData(this.widgetType, this.title, this.action);

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  final String title;
  final Action action;

  factory FAQWidgetData.fromJson(widget, WidgetTypes widgetType) {
    return FAQWidgetData(
        widgetType, widget['title'], Action.fromJson(widget['action']));
  }
}

class CLPHeaderWidgetData implements IWidgetData {
  CLPHeaderWidgetData(this.widgetType, this.title);

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  final String title;

  factory CLPHeaderWidgetData.fromJson(widget, WidgetTypes widgetType) {
    return CLPHeaderWidgetData(widgetType, widget['title']);
  }
}

class PackRenewalWidgetData implements IWidgetData {
  PackRenewalWidgetData(
      {required this.widgetType,
      required this.title,
      this.subtitle,
      this.action});

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  final String? title;
  final String? subtitle;
  final Action? action;

  factory PackRenewalWidgetData.fromJson(widget, WidgetTypes widgetType) {
    return PackRenewalWidgetData(
        widgetType: widgetType,
        title: widget['title'],
        subtitle: widget['subtitle'],
        action: Action.fromJson(widget['action']));
  }
}

class HabitActionCard {
  HabitActionCard(this.id, this.title, this.subtitle, {this.action});

  final String id;
  final String title;
  final String subtitle;
  final Action? action;

  factory HabitActionCard.fromJson(widget) {
    return HabitActionCard(widget['id'], widget['title'], widget['subtitle'],
        action: Action.fromJson(widget['action']));
  }
}

class ProgressHabitsCard {
  ProgressHabitsCard(this.title, this.subtitle, this.imageUrl,
      {this.pastActionText,
      this.showDivider,
      this.activeActionCards,
      this.inActiveActionCards});

  final String title;
  final String subtitle;
  final String imageUrl;
  final String? pastActionText;
  final bool? showDivider;
  final List<HabitActionCard>? activeActionCards;
  final List<HabitActionCard>? inActiveActionCards;

  factory ProgressHabitsCard.fromJson(widget) {
    return ProgressHabitsCard(
      widget['title'],
      widget['subtitle'],
      widget['imageUrl'],
      pastActionText: widget['pastActionText'],
      showDivider: widget['showDivider'],
      activeActionCards: widget['activeActions']
          .map<HabitActionCard>((data) => (HabitActionCard.fromJson(data)))
          .toList(),
      inActiveActionCards: widget['inActiveActions']
          .map<HabitActionCard>((data) => (HabitActionCard.fromJson(data)))
          .toList(),
    );
  }
}

class ProgressCard {
  ProgressCard(
      this.title, this.consistencyScoreText, this.key, this.progresHabitCards);

  final String title;
  final String consistencyScoreText;
  final String key;
  final List<ProgressHabitsCard> progresHabitCards;

  factory ProgressCard.fromJson(widget) {
    return ProgressCard(
      widget['title'],
      widget['consistencyScoreText'],
      widget['key'],
      widget['habitCards'].map<ProgressHabitsCard>((data) {
        return ProgressHabitsCard.fromJson(data);
      }).toList(),
    );
  }
}

class HabitsProgressWidgetData implements IWidgetData {
  HabitsProgressWidgetData(this.widgetType,
      {this.title,
      this.subtitle,
      this.lottieUrl,
      this.key,
      this.progressCards,
      this.showCoachMessage,
      this.coachMessage});

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  final String? title;
  final String? subtitle;
  final String? lottieUrl;
  final String? key;
  final String? coachMessage;
  final bool? showCoachMessage;
  final List<ProgressCard>? progressCards;

  factory HabitsProgressWidgetData.fromJson(widget, WidgetTypes widgetType) {
    return HabitsProgressWidgetData(
      widgetType,
      title: widget['habitProgressView']['title'],
      subtitle: widget['habitProgressView']['subtitle'],
      lottieUrl: widget['habitProgressView']['lottieUrl'],
      key: widget['habitProgressView']['key'],
      progressCards:
          widget['habitProgressView']['cardItems'].map<ProgressCard>((data) {
        return ProgressCard.fromJson(data);
      }).toList(),
      showCoachMessage: widget['habitProgressView']['showCoachMessage'],
      coachMessage: widget['habitProgressView']['coachMessage'],
    );
  }
}

class SettingsWidgetData implements IWidgetData {
  SettingsWidgetData(
    this.widgetType, {
    this.title,
    this.imageUrl,
    this.showModal,
    this.widgetInfo,
  });

  final String? title;
  final String? imageUrl;
  final bool? showModal;

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  factory SettingsWidgetData.fromJson(
      widget, WidgetTypes widgetType, WidgetInfo widgetInfo) {
    return SettingsWidgetData(
      widgetType,
      title: widget['title'],
      imageUrl: widget['imageUrl'],
      showModal: widget['showModal'],
      widgetInfo: widgetInfo,
    );
  }
}

class LifestyleReEvaluationFormWidgetData implements IWidgetData {
  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  FormResponse currentFormResponse;

  String title;

  LifestyleReEvaluationFormWidgetData(
      this.currentFormResponse, this.title, this.widgetType, this.widgetInfo);

  static LifestyleReEvaluationFormWidgetData fromJSON(
      response, WidgetTypes widgetType, WidgetInfo widgetInfo) {
    return LifestyleReEvaluationFormWidgetData(
        FormResponse.fromJSON(response['userForm']),
        response["title"],
        widgetType,
        widgetInfo);
  }
}

class ContentProgressModel implements IWidgetData {
  String? progressStatusText;
  int? totalChaptersCount;
  int? currentProgressCount;
  Action? viewAllAction;
  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  ContentProgressModel(
    this.widgetType, {
    this.progressStatusText,
    this.totalChaptersCount,
    this.currentProgressCount,
    this.viewAllAction,
    this.widgetInfo,
  }) : super();

  static ContentProgressModel fromJson(WidgetTypes widgetType,
      Map<String, dynamic> json, WidgetInfo? widgetInfo) {
    return ContentProgressModel(
      widgetType,
      progressStatusText: json['progressStatusText'],
      totalChaptersCount: json['totalChaptersCount'],
      currentProgressCount: json['currentProgressCount'],
      viewAllAction: json['viewAllAction'] != null
          ? Action.fromJson(json['viewAllAction'])
          : null,
      widgetInfo: widgetInfo,
    );
  }
}

class ContentTodayReadModel implements IWidgetData {
  final List<LevelCardChapter> cards;
  final bool? isSwippable;

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  ContentTodayReadModel(
    this.cards,
    this.widgetType, {
    this.isSwippable,
    this.widgetInfo,
  }) : super();

  static ContentTodayReadModel fromJson(WidgetTypes widgetType,
      Map<String, dynamic> json, WidgetInfo? widgetInfo) {
    return ContentTodayReadModel(
      json['cards']
          .map<LevelCardChapter>((e) => LevelCardChapter(widgetType,
              widgetInfo: widgetInfo,
              title: e['title'],
              tagName: e['tagName'],
              isLocked: e['isLocked'],
              subtitle: e['subtitle'],
              complete: e['complete'],
              imageBackground: e['imageBackground'],
              action: Action.fromJson(e['action'])))
          .toList(),
      widgetType,
      widgetInfo: widgetInfo,
      isSwippable: json['isSwippable'] ?? false,
    );
  }
}

class ProductInfoWidgetData implements IWidgetData {
  final String? title;
  final String? subtitle;
  final List<ProductInfoCard>? cards;
  final Action? action;
  final String? footerText;

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  ProductInfoWidgetData(
    this.widgetType, {
    this.title,
    this.subtitle,
    this.cards,
    this.footerText,
    this.action,
    this.widgetInfo,
  }) : super();

  static ProductInfoWidgetData fromJson(
      WidgetTypes widgetType, dynamic json, WidgetInfo? widgetInfo) {
    return ProductInfoWidgetData(
      widgetType,
      title: json['title'],
      subtitle: json['subtitle'],
      cards: json['cards'] != null
          ? json['cards']
              .map<ProductInfoCard>((card) => ProductInfoCard.fromJson(card))
              .toList()
          : null,
      action: json['action'] != null ? Action.fromJson(json['action']) : null,
      footerText: json['footerText'],
      widgetInfo: json['widgetInfo'],
    );
  }
}

class ProductInfoCard {
  String? title;
  String? subtitle;
  Action? action;
  String? backgroundImageUrl;
  bool isExpanded;

  ProductInfoCard({
    this.title,
    this.subtitle,
    this.backgroundImageUrl,
    this.action,
    this.isExpanded = false,
  });

  static ProductInfoCard fromJson(dynamic json) {
    return ProductInfoCard(
      title: json['title'],
      subtitle: json['subtitle'],
      action: json['action'] != null ? Action.fromJson(json['action']) : null,
      backgroundImageUrl: json['backgroundImageUrl'],
      isExpanded: json['isExpanded'] ?? false,
    );
  }
}

class ChallengesActionWidgetData implements IWidgetData {
  ChallengesActionWidgetData(
    this.widgetType, {
    this.title,
    this.subtitle,
    this.iconUrl,
    this.action,
    this.gradientColors = const ["#FFFFFF", "#FFFFFF"],
    this.widgetInfo,
  }) : super();

  final String? title;
  final String? subtitle;
  final String? iconUrl;
  final List<String> gradientColors;
  final Action? action;

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  factory ChallengesActionWidgetData.fromJson(
      WidgetTypes widgetType, dynamic widgetData, WidgetInfo? widgetInfo) {
    return ChallengesActionWidgetData(
      widgetType,
      title: widgetData['title'],
      subtitle: widgetData['subtitle'],
      iconUrl: widgetData['iconUrl'],
      action: widgetData['action'] != null
          ? Action.fromJson(widgetData['action'])
          : null,
      gradientColors: widgetData['gradientColors'] != null
          ? widgetData['gradientColors']
              .map<String>((hexColor) => hexColor.toString())
              .toList()
          : ["#FFFFFF", "#FFFFFF"],
      widgetInfo: widgetInfo,
    );
  }
}

class CoachInfoWidgetData implements IWidgetData {
  CoachInfoWidgetData(
    this.widgetType, {
    this.coachInfoItems,
    this.widgetInfo,
    this.horizontalPadding,
  }) : super();

  final List<CoachInfoCardData>? coachInfoItems;
  final num? horizontalPadding;

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  factory CoachInfoWidgetData.fromJson(
      WidgetTypes widgetType, dynamic widgetData, WidgetInfo? widgetInfo) {
    return CoachInfoWidgetData(
      widgetType,
      coachInfoItems: widgetData['coachInfoCards'] != null
          ? widgetData['coachInfoCards']
              .map<CoachInfoCardData>(
                  (infoCard) => CoachInfoCardData.fromJson(infoCard))
              .toList()
          : null,
      horizontalPadding: widgetData['horizontalPadding'],
      widgetInfo: widgetInfo,
    );
  }
}

class CoachInfoCardData {
  CoachInfoCardData({
    this.tagTitle,
    this.coachInfoItems,
  }) : super();

  final String? tagTitle;
  final List<CoachInfoItem>? coachInfoItems;

  factory CoachInfoCardData.fromJson(dynamic json) {
    return CoachInfoCardData(
      tagTitle: json['tagTitle'],
      coachInfoItems: json['coachInfoItems'] != null
          ? json['coachInfoItems']
              .map<CoachInfoItem>(
                  (infoItem) => CoachInfoItem.fromJson(infoItem))
              .toList()
          : null,
    );
  }
}

class CoachInfoItem {
  String? title;
  String? subtitle;
  String? imageUrl;
  List<ActionItems>? actionItems;

  CoachInfoItem({
    this.title,
    this.subtitle,
    this.imageUrl,
    this.actionItems,
  });

  static CoachInfoItem fromJson(dynamic json) {
    return CoachInfoItem(
      title: json['title'],
      subtitle: json['subtitle'],
      imageUrl: json['imageUrl'],
      actionItems: json['actionItems'] != null
          ? json['actionItems']
              .map<ActionItems>(
                  (actionItem) => ActionItems.fromJson(actionItem))
              .toList()
          : null,
    );
  }
}

class ActionItems {
  String? iconImage;
  Action? action;

  ActionItems({
    this.iconImage,
    this.action,
  });

  static ActionItems fromJson(dynamic json) {
    return ActionItems(
      iconImage: json['iconImage'],
      action: json['action'] != null ? Action.fromJson(json['action']) : null,
    );
  }
}

class ProductOfferingWidgetData implements IWidgetData {
  ProductOfferingWidgetData(
    this.widgetType, {
    this.productOfferingCards,
    this.widgetHeaderData,
    this.widgetInfo,
  }) : super();

  final List<ProductOfferingCard>? productOfferingCards;
  final WidgetHeaderData? widgetHeaderData;

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  factory ProductOfferingWidgetData.fromJson(
      WidgetTypes widgetType, dynamic widgetData, WidgetInfo? widgetInfo) {
    return ProductOfferingWidgetData(
      widgetType,
      productOfferingCards: widgetData['productOfferingCards'] != null
          ? widgetData['productOfferingCards']
              .map<ProductOfferingCard>(
                  (offeringCard) => ProductOfferingCard.fromJson(offeringCard))
              .toList()
          : null,
      widgetHeaderData: widgetData['header'] != null
          ? WidgetHeaderData.fromJson(widgetData['header'])
          : null,
      widgetInfo: widgetInfo,
    );
  }
}

class ProductOfferingCard {
  String? title;
  String? subtitle;
  String? imageUrl;
  bool showIcon;
  Action? action;

  ProductOfferingCard({
    this.title,
    this.subtitle,
    this.imageUrl,
    this.showIcon = true,
    this.action,
  });

  static ProductOfferingCard fromJson(dynamic json) {
    return ProductOfferingCard(
      title: json['title'],
      subtitle: json['description'],
      imageUrl: json['imageUrl'],
      showIcon: json['icon'] != null,
      action: json['action'] != null ? Action.fromJson(json['action']) : null,
    );
  }
}

class TransformHeaderWidgetData implements IWidgetData {
  TransformHeaderWidgetData(
    this.widgetType, {
    this.title,
    this.subtitle,
    this.description,
    this.gradientColors = const [Colors.white, Colors.white],
    this.titleAlignment = HeaderAlignmentType.LEFT,
    this.action,
    this.widgetInfo,
  }) : super();

  final String? title;
  final String? subtitle;
  final String? description;
  final List<Color> gradientColors;
  final HeaderAlignmentType titleAlignment;
  final Action? action;

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  factory TransformHeaderWidgetData.fromJson(
      WidgetTypes widgetType, dynamic json, WidgetInfo? widgetInfo) {
    return TransformHeaderWidgetData(
      widgetType,
      title: json['title'],
      subtitle: json['subTitle'],
      description: json['description'],
      action: json['action'] != null ? Action.fromJson(json['action']) : null,
      gradientColors: json['gradientColors'] != null
          ? json['gradientColors']
              .map<Color>((color) => HexColor.fromHex(color.toUpperCase()))
              .toList()
          : [Colors.white, Colors.white],
      titleAlignment:
          (json['titleAlignment'] != null && json['titleAlignment'] == 'CENTER')
              ? HeaderAlignmentType.CENTER
              : HeaderAlignmentType.LEFT,
      widgetInfo: widgetInfo,
    );
  }
}

class TransformMealPlanWidgetData implements IWidgetData {
  TransformMealPlanWidgetData(
    this.widgetType, {
    this.title,
    this.imageUrl,
    this.subtitle,
    this.action,
    this.widgetInfo,
  });

  final String? title;
  final String? imageUrl;
  final String? subtitle;
  final Action? action;

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  factory TransformMealPlanWidgetData.fromJson(
      widget, WidgetTypes widgetType, WidgetInfo widgetInfo) {
    return TransformMealPlanWidgetData(
      widgetType,
      title: widget['title'],
      imageUrl: widget['imageUrl'],
      subtitle: widget['subtitle'],
      action:
          widget['action'] != null ? Action.fromJson(widget['action']) : null,
      widgetInfo: widgetInfo,
    );
  }
}

class TransformImageUploadWidgetData implements IWidgetData {
  TransformImageUploadWidgetData(
    this.widgetType, {
    this.title,
    this.imageCards,
    this.journeyAction,
    this.action,
    this.showUploadWidget = false,
    this.widgetInfo,
  });

  final String? title;
  final List<ImageCard>? imageCards;
  final Action? journeyAction;
  final Action? action;
  final bool showUploadWidget;

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  factory TransformImageUploadWidgetData.fromJson(
      widget, WidgetTypes widgetType, WidgetInfo widgetInfo) {
    return TransformImageUploadWidgetData(
      widgetType,
      title: widget['title'],
      imageCards: widget['imageCards'] != null
          ? widget['imageCards']
              .map<ImageCard>((card) => ImageCard.fromJson(card))
              .toList()
          : null,
      showUploadWidget: widget['showUploadWidget'] ?? false,
      journeyAction: widget['journeyAction'] != null
          ? Action.fromJson(widget['journeyAction'])
          : null,
      action:
          widget['action'] != null ? Action.fromJson(widget['action']) : null,
      widgetInfo: widgetInfo,
    );
  }
}

class ProductComparisonWidgetData implements IWidgetData {
  final WidgetHeaderData? header;
  final int numberOfColumns;
  final List<ProductComparisonWidgetRowItem>? data;
  final List<ProductComparisonWidgetRowItem>? footerData;
  final String? subCategoryCode;

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  ProductComparisonWidgetData(
    this.widgetType, {
    this.header,
    this.numberOfColumns = 0,
    this.data,
    this.footerData,
    this.subCategoryCode,
    this.widgetInfo,
  }) : super();

  static ProductComparisonWidgetData fromJson(
      WidgetTypes widgetType, dynamic widget, WidgetInfo? widgetInfo) {
    return ProductComparisonWidgetData(
      widgetType,
      header: widget['header'] != null
          ? WidgetHeaderData.fromJson(widget['header'])
          : null,
      numberOfColumns: widget['numberOfColumns'] ?? 0,
      data: widget['data'] != null
          ? widget['data']
              .map<ProductComparisonWidgetRowItem>(
                  (data) => ProductComparisonWidgetRowItem.fromJson(data))
              .toList()
          : null,
      footerData: widget['footerData'] != null
          ? widget['footerData']
              .map<ProductComparisonWidgetRowItem>(
                  (data) => ProductComparisonWidgetRowItem.fromJson(data))
              .toList()
          : null,
      subCategoryCode: widget['subCategoryCode'],
      widgetInfo: widget['widgetInfo'],
    );
  }
}

class ProductComparisonWidgetRowItem {
  List<RowItems>? rowItems;
  bool isHeaderRow;

  ProductComparisonWidgetRowItem({
    this.rowItems,
    this.isHeaderRow = false,
  });

  static ProductComparisonWidgetRowItem fromJson(dynamic json) {
    return ProductComparisonWidgetRowItem(
      rowItems: json['rowItems'] != null
          ? json['rowItems']
              .map<RowItems>((rowItem) => RowItems.fromJson(rowItem))
              .toList()
          : null,
      isHeaderRow: json['isHeaderRow'] ?? false,
    );
  }
}

class RowItems {
  String? title;
  String? subtitle;
  String? imageUrl;
  num imageWidth;
  num imageHeight;
  String? productType;
  Action? action;

  RowItems({
    this.title,
    this.subtitle,
    this.imageUrl,
    this.imageWidth = 50.0,
    this.imageHeight = 45.0,
    this.productType,
    this.action,
  });

  static RowItems fromJson(dynamic json) {
    return RowItems(
      title: json['title'],
      subtitle: json['subtitle'],
      imageUrl: json['imageUrl'],
      productType: json['productType'],
      imageWidth: json['imageWidth'] != null ? json['imageWidth'] : 50.0,
      imageHeight: json['imageHeight'] != null ? json['imageHeight'] : 45.0,
      action: json['action'] != null ? Action.fromJson(json['action']) : null,
    );
  }
}

class TransformPackTabWidgetData implements IWidgetData {
  final int initialTab;
  final String initialProduct;
  final List<TransformPackTabData>? tabData;

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  TransformPackTabWidgetData(
    this.widgetType, {
    this.initialTab = 0,
    this.initialProduct = "WEIGHT_LOSS",
    this.tabData,
    this.widgetInfo,
  }) : super();

  static TransformPackTabWidgetData fromJson(
      WidgetTypes widgetType, dynamic widget, WidgetInfo? widgetInfo) {
    return TransformPackTabWidgetData(
      widgetType,
      initialTab: widget['initialTab'] ?? 0,
      initialProduct: widget['initialProduct'] ?? "WEIGHT_LOSS",
      tabData: widget['tabs'] != null
          ? widget['tabs']
              .map<TransformPackTabData>(
                  (data) => TransformPackTabData.fromJson(data))
              .toList()
          : null,
      widgetInfo: widget['widgetInfo'],
    );
  }
}

class TransformPackTabData {
  final String? id;
  final String? title;
  final String? titleImageUrl;
  final String? subtitle;
  final String? imageUrl;
  final String? initialProduct;
  final List<TabItem>? tabItems;
  final List<CultPackDetailWidgetBodyData>? packItems;
  final Map<String, List<String>>? packMappingData;
  final List<dynamic>? widgets;
  final String? refundImageUrl;
  final Action? refundImageAction;

  TransformPackTabData({
    this.id,
    this.title,
    this.subtitle,
    this.titleImageUrl,
    this.imageUrl,
    this.initialProduct,
    this.tabItems,
    this.packItems,
    this.packMappingData,
    this.widgets,
    this.refundImageUrl,
    this.refundImageAction,
  });

  static TransformPackTabData fromJson(dynamic json) {
    Map<String, List<String>> packMappingData = {};
    json['packMappingData'] != null
        ? json['packMappingData'].forEach((id, packIds) {
            packMappingData[id] = packIds != null
                ? packIds.map<String>((data) => data.toString()).toList()
                : [];
          })
        : null;
    return TransformPackTabData(
        id: json['id'],
        title: json['title'],
        subtitle: json['subtitle'],
        titleImageUrl: json['titleImageUrl'],
        imageUrl: json['imageUrl'],
        initialProduct: json['initialProduct'],
        packItems: json['packItems'] != null
            ? json['packItems']
                .map<CultPackDetailWidgetBodyData>(
                    (data) => CultPackDetailWidgetBodyData.fromJson(data))
                .toList()
            : null,
        tabItems: json['tabItems'] != null
            ? json['tabItems']
                .map<TabItem>((data) => TabItem.fromJson(data))
                .toList()
            : null,
        packMappingData: packMappingData,
        widgets: json['widgets'],
        refundImageUrl: json['refundImageUrl'],
        refundImageAction: json['refundImageAction'] != null
            ? Action.fromJson(json['refundImageAction'])
            : null);
  }
}

class TabItem {
  final String? id;
  final String? title;
  final bool showPackSelectionModal;
  final ModalData? modalData;

  TabItem({
    this.id,
    this.title,
    this.showPackSelectionModal = false,
    this.modalData,
  });

  static TabItem fromJson(dynamic json) {
    return TabItem(
      id: json['id'],
      title: json['title'],
      showPackSelectionModal: json['showPackSelectionModal'] ?? false,
      modalData: json['modalData'] != null
          ? ModalData.fromJson(json['modalData'])
          : null,
    );
  }
}

class ModalData {
  final String? title;
  final String? benefitsTitle;
  final List<String>? benefits;
  final List<ProductItem>? productItems;

  ModalData({
    this.title,
    this.benefitsTitle,
    this.benefits,
    this.productItems,
  });

  static ModalData fromJson(dynamic json) {
    return ModalData(
      title: json['title'],
      benefitsTitle: json['benefitsTitle'],
      benefits: json['benefits'] != null
          ? json['benefits'].map<String>((data) => data.toString()).toList()
          : null,
      productItems: json['productItems'] != null
          ? json['productItems']
              .map<ProductItem>((data) => ProductItem.fromJson(data))
              .toList()
          : null,
    );
  }
}

class ProductItem {
  final String? title;
  final String? subtitle;
  final String? id;

  ProductItem({
    this.title,
    this.subtitle,
    this.id,
  });

  static ProductItem fromJson(dynamic json) {
    return ProductItem(
      title: json['title'],
      subtitle: json['subtitle'],
      id: json['id'],
    );
  }
}

class TransformTestimonialsWidgetV3Data implements IWidgetData {
  TransformTestimonialsWidgetV3Data(
    this.widgetType, {
    this.header,
    this.data,
    this.subCategoryCode,
    this.widgetInfo,
  });

  final WidgetHeaderData? header;
  final List<Testimonial>? data;
  final String? subCategoryCode;

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  factory TransformTestimonialsWidgetV3Data.fromJson(
      WidgetTypes widgetType, dynamic widget, WidgetInfo? widgetInfo) {
    return TransformTestimonialsWidgetV3Data(
      widgetType,
      header: widget['header'] != null
          ? WidgetHeaderData.fromJson(widget['header'])
          : null,
      data: widget['data'] != null
          ? widget['data']
              .map<Testimonial>((data) => Testimonial.fromJson(data))
              .toList()
          : null,
      subCategoryCode: widget['subCategoryCode'],
      widgetInfo: widgetInfo,
    );
  }
}

class ProgressCardsWidgetData implements IWidgetData {
  final WidgetHeaderData? header;
  final List<ProgressV2Card>? progressCards;
  final String? subCategoryCode;

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  ProgressCardsWidgetData(
    this.widgetType, {
    this.header,
    this.progressCards,
    this.subCategoryCode,
    this.widgetInfo,
  }) : super();

  static ProgressCardsWidgetData fromJson(
      WidgetTypes widgetType, dynamic widget, WidgetInfo? widgetInfo) {
    return ProgressCardsWidgetData(
      widgetType,
      header: widget['header'] != null
          ? WidgetHeaderData.fromJson(widget['header'])
          : null,
      progressCards: widget['progressCards'] != null
          ? widget['progressCards']
              .map<ProgressV2Card>((data) => ProgressV2Card.fromJson(data))
              .toList()
          : null,
      subCategoryCode: widget['subCategoryCode'],
      widgetInfo: widget['widgetInfo'],
    );
  }
}

class ProgressV2Card {
  final String? id;
  final String? heading;
  final String? subtitle;
  final String? iconUrl;
  final String? imageUrl;
  final String? metricText;
  final String? differenceText;
  final String? color;
  final String? description;
  final bool isExpanded;
  final Action? action;

  ProgressV2Card({
    this.id,
    this.heading,
    this.subtitle,
    this.iconUrl,
    this.imageUrl,
    this.metricText,
    this.differenceText,
    this.color,
    this.description,
    this.isExpanded = false,
    this.action,
  });

  static ProgressV2Card fromJson(dynamic json) {
    return ProgressV2Card(
      id: json['id'],
      heading: json['heading'],
      iconUrl: json['iconUrl'],
      subtitle: json['subtitle'],
      imageUrl: json['imageUrl'],
      metricText: json['metricText'],
      differenceText: json['differenceText'],
      color: json['color'],
      description: json['description'],
      isExpanded: json['isExpanded'] ?? false,
      action: json['action'] != null ? Action.fromJson(json['action']) : null,
    );
  }
}

enum Graph { BAR_CHART, PIE_CHART, BASE_CHART, CALENDAR, NO_GRAPH }

class UserMetricChartWidgetV2Data implements IWidgetData {
  UserMetricChartWidgetV2Data(this.widgetType,
      {this.widgetInfo,
      this.subtitle,
      this.title,
      this.metricText,
      this.differenceText,
      this.color,
      this.description,
      this.expandable = false,
      this.isExpanded = false,
      this.graph,
      this.graphViewList,
      this.pieChartValue,
      this.initialGraph = 0,
      this.action,
      this.calendarChartData});

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  final String? title;
  final String? subtitle;
  final String? metricText;
  final String? differenceText;
  final String? color;
  final String? description;
  final bool expandable;
  final bool isExpanded;
  final Graph? graph;
  final List<GraphViewData>? graphViewList;
  final PieChartValue? pieChartValue;
  final CalendarChartData? calendarChartData;
  final int initialGraph;
  final ActionHandler.Action? action;

  factory UserMetricChartWidgetV2Data.fromJson(
      widget, WidgetTypes widgetType, WidgetInfo widgetInfo) {
    return UserMetricChartWidgetV2Data(
      widgetType,
      widgetInfo: widgetInfo,
      title: widget['userMetricChart']['title'],
      subtitle: widget['userMetricChart']['subtitle'],
      metricText: widget['userMetricChart']['metricText'],
      differenceText: widget['userMetricChart']['differenceText'],
      color: widget['userMetricChart']['color'],
      description: widget['userMetricChart']['description'],
      expandable: widget['userMetricChart']['expandable'] ?? false,
      isExpanded: widget['userMetricChart']['isExpanded'] ?? false,
      initialGraph: widget['userMetricChart']['initialGraph'] ?? 0,
      graph: EnumToString.fromString(
              Graph.values, widget['userMetricChart']['graph']) ??
          null,
      graphViewList: widget['userMetricChart']['graphViewList'] != null
          ? widget['userMetricChart']['graphViewList']
              .map<GraphViewData>((e) => GraphViewData.fromJson(e))
              .toList()
          : [],
      pieChartValue: widget['userMetricChart']['pieChartData'] != null
          ? PieChartValue.fromJson(widget['userMetricChart']['pieChartData'])
          : null,
      calendarChartData: widget['userMetricChart']['calendarChartData'] != null
          ? CalendarChartData.fromJson(
              widget['userMetricChart']['calendarChartData'])
          : null,
      action: widget['userMetricChart']['action'] != null
          ? ActionHandler.Action.fromJson(widget['userMetricChart']['action'])
          : null,
    );
  }
}

class GraphViewData {
  final String? id;
  final String? title;
  final String? subtitle;
  final String? unit;
  final GraphData? graphData;

  GraphViewData({
    this.id,
    this.title,
    this.subtitle,
    this.unit,
    this.graphData,
  });

  static GraphViewData fromJson(dynamic json) {
    return GraphViewData(
      id: json['id'],
      title: json['title'],
      subtitle: json['subtitle'],
      unit: json['unit'],
      graphData: json['graphData'] != null
          ? GraphData.fromJson(json['graphData'])
          : null,
    );
  }
}

class GraphData {
  final Map<double, String>? indexToDay;
  final List<GraphPoint> points;

  GraphData({
    this.indexToDay,
    this.points = const [],
  });

  static GraphData fromJson(dynamic json) {
    return GraphData(
      points: json['points'] != null
          ? json['points']
              .map<GraphPoint>((e) => GraphPoint.fromJson(e))
              .toList()
          : [],
      indexToDay:
          json['indexToDay'] != null ? Map.castFrom(json['indexToDay']) : null,
    );
  }
}

class GraphPoint {
  final double? x;
  final double? y;
  final String? day;
  final String? color;

  GraphPoint({
    this.x,
    this.y,
    this.color,
    this.day,
  });

  static GraphPoint fromJson(dynamic json) {
    return GraphPoint(
      x: json['x'],
      y: json['y'],
      color: json['color'],
      day: json['day'],
    );
  }
}

class PieChartValue {
  final List<PieChartPoints> points;

  PieChartValue({
    this.points = const [],
  });

  static PieChartValue fromJson(dynamic json) {
    return PieChartValue(
      points: json['points'] != null
          ? json['points']
              .map<PieChartPoints>((e) => PieChartPoints.fromJson(e))
              .toList()
          : [],
    );
  }
}

class PieChartPoints {
  final double? value;
  final double? percentage;
  final double? total;
  final String? title;
  final String? color;

  PieChartPoints(
      {this.value, this.percentage, this.total, this.title, this.color});

  static PieChartPoints fromJson(dynamic json) {
    return PieChartPoints(
      value: json['value'],
      percentage: json['percentage'],
      total: json['total'],
      title: json['title'],
      color: json['color'],
    );
  }
}

class CalendarChartData {
  final int numberOfRows;
  final int numberOfColumns;
  final List<List<CalendarChartPoints>> points;

  CalendarChartData({
    this.points = const [],
    this.numberOfColumns = 0,
    this.numberOfRows = 0,
  });

  static CalendarChartData fromJson(dynamic json) {
    List<List<CalendarChartPoints>> chartPointsList = [];
    if (json['points'] != null) {
      for (dynamic points in json['points']) {
        List<CalendarChartPoints> chartPoints =
            points.map<CalendarChartPoints>((point) {
                  return CalendarChartPoints.fromJson(point);
                }).toList() ??
                [];
        chartPointsList.add(chartPoints);
      }
    }
    return CalendarChartData(
      numberOfColumns: json['numberOfColumns'] ?? 0,
      numberOfRows: json['numberOfRows'] ?? 0,
      points: chartPointsList,
    );
  }
}

class CalendarChartPoints {
  final CalendarDayStatus? status;
  final String? color;
  final String? title;

  CalendarChartPoints({this.status, this.title, this.color});

  static CalendarChartPoints fromJson(dynamic json) {
    return CalendarChartPoints(
      title: json['title'],
      color: json['color'],
      status:
          EnumToString.fromString(CalendarDayStatus.values, json['status']) ??
              CalendarDayStatus.NOT_COMPLETED,
    );
  }
}

enum CalendarDayStatus { COMPLETED, NOT_COMPLETED, MISSED }

class TimerCardWidgetData implements IWidgetData {
  TimerCardWidgetData(
    this.widgetType, {
    this.title,
    this.action,
    this.subtitle,
    this.description,
    this.imageUrl,
    this.bgImageUrl,
    this.lottieUrl,
    this.widgetInfo,
    this.showGradient = true,
    this.cardData,
    this.timer,
  });

  final String? title;
  final String? subtitle;
  final String? description;
  final String? imageUrl;
  final String? lottieUrl;
  final String? bgImageUrl;
  final bool showGradient;
  final Action? action;
  final Timer? timer;
  final RenewalCardData? cardData;

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  factory TimerCardWidgetData.fromJson(
      WidgetTypes widgetType, dynamic widget, WidgetInfo? widgetInfo) {
    return TimerCardWidgetData(
      widgetType,
      title: widget['title'],
      subtitle: widget['subtitle'],
      description: widget['description'],
      imageUrl: widget['imageUrl'],
      bgImageUrl: widget['bgImageUrl'],
      lottieUrl: widget['lottieUrl'],
      showGradient: widget['showGradient'] ?? true,
      action:
          widget['action'] != null ? Action.fromJson(widget['action']) : null,
      timer: widget['timer'] != null ? Timer.fromJson(widget['timer']) : null,
      cardData: widget['cardData'] != null
          ? RenewalCardData.fromJson(widget['cardData'])
          : null,
      widgetInfo: widgetInfo,
    );
  }
}

class RenewalCardData {
  final String? title;
  final String? prefix;
  final String? suffix;
  final String? color;
  final String? description;
  final String? imageUrl;
  final Action? action;

  RenewalCardData({
    this.title,
    this.prefix,
    this.suffix,
    this.color,
    this.description,
    this.imageUrl,
    this.action,
  });

  static RenewalCardData fromJson(dynamic json) {
    return RenewalCardData(
      title: json['title'],
      prefix: json['prefix'],
      suffix: json['suffix'],
      color: json['color'],
      description: json['description'],
      imageUrl: json['imageUrl'],
      action: json['action'] != null ? Action.fromJson(json['action']) : null,
    );
  }
}

class ProductProgressTabWidgetData implements IWidgetData {
  final WidgetHeaderData? header;
  final List<ProgressTabProductItem>? tabItems;
  final String? subCategoryCode;
  final bool showTabProgress;
  final int duration;

  ProductProgressTabWidgetData(
    this.widgetType, {
    this.header,
    this.tabItems,
    this.subCategoryCode,
    this.showTabProgress = true,
    this.duration = 5,
    this.widgetInfo,
  }) : super();

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  static ProductProgressTabWidgetData fromJson(
      WidgetTypes widgetType, dynamic widget, WidgetInfo? widgetInfo) {
    return ProductProgressTabWidgetData(
      widgetType,
      header: widget['header'] != null
          ? WidgetHeaderData.fromJson(widget['header'])
          : null,
      tabItems: widget['tabItems'] != null
          ? widget['tabItems']
              .map<ProgressTabProductItem>(
                  (data) => ProgressTabProductItem.fromJson(data))
              .toList()
          : null,
      subCategoryCode: widget['subCategoryCode'],
      showTabProgress: widget['showTabProgress'] ?? true,
      duration: widget['duration'] ?? 5,
      widgetInfo: widget['widgetInfo'],
    );
  }
}

class ReviewCardsWidgetData implements IWidgetData {
  final WidgetHeaderData? header;
  final List<ReviewCardData>? reviewCards;
  final Action? action;

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  ReviewCardsWidgetData(
    this.widgetType, {
    this.header,
    this.reviewCards,
    this.action,
    this.widgetInfo,
  }) : super();

  static ReviewCardsWidgetData fromJson(
      WidgetTypes widgetType, dynamic widget, WidgetInfo? widgetInfo) {
    return ReviewCardsWidgetData(
      widgetType,
      header: widget['header'] != null
          ? WidgetHeaderData.fromJson(widget['header'])
          : null,
      reviewCards: widget['reviewCards'] != null
          ? widget['reviewCards']
              .map<ReviewCardData>((data) => ReviewCardData.fromJson(data))
              .toList()
          : null,
      action:
          widget['action'] != null ? Action.fromJson(widget['action']) : null,
      widgetInfo: widget['widgetInfo'],
    );
  }
}

class ProgressTabProductItem {
  final ProductItemData? productItem;
  final String? iconUrl;
  final String? title;

  ProgressTabProductItem({this.iconUrl, this.title, this.productItem});

  static ProgressTabProductItem fromJson(dynamic json) {
    return ProgressTabProductItem(
      title: json['title'],
      iconUrl: json['iconUrl'],
      productItem: json['productItem'] != null
          ? ProductItemData.fromJson(json['productItem'])
          : null,
    );
  }
}

class ProductItemData {
  final String? title;
  final String? tagText;
  final String? imageUrl;
  final String? lottieUrl;
  final List<String>? offerings;
  final Action? action;

  ProductItemData(
      {this.title,
      this.tagText,
      this.imageUrl,
      this.lottieUrl,
      this.offerings,
      this.action});

  static ProductItemData fromJson(dynamic json) {
    return ProductItemData(
      title: json['title'],
      tagText: json['tagText'],
      imageUrl: json['imageUrl'],
      lottieUrl: json['lottieUrl'],
      offerings: json['offerings'] != null
          ? json['offerings']
              .map<String>((offering) => offering.toString())
              .toList()
          : null,
      action: json['action'] != null ? Action.fromJson(json['action']) : null,
    );
  }
}

class ReviewCardData {
  final String? title;
  final String? subtitle;
  final String? description;
  final String? imageUrl;
  final String? prefix;
  final String? suffix;
  final double? cardWidth;

  ReviewCardData({
    this.title,
    this.subtitle,
    this.description,
    this.imageUrl,
    this.prefix,
    this.suffix,
    this.cardWidth,
  });

  static ReviewCardData fromJson(dynamic json) {
    return ReviewCardData(
      title: json['title'],
      subtitle: json['subtitle'],
      description: json['description'],
      imageUrl: json['imageUrl'],
      prefix: json['prefix'],
      suffix: json['suffix'],
      cardWidth: json['width'],
    );
  }
}

class GreetingsHeaderWidgetData implements IWidgetData {
  final String? title;
  final String? subtitle;
  final String? prefix;
  final String? suffix;
  final String? imageUrl;
  final String? lottieUrl;
  final Action? action;

  GreetingsHeaderWidgetData(
    this.widgetType, {
    this.title,
    this.subtitle,
    this.prefix,
    this.suffix,
    this.imageUrl,
    this.lottieUrl,
    this.action,
    this.widgetInfo,
  }) : super();

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  static GreetingsHeaderWidgetData fromJson(
      WidgetTypes widgetType, dynamic widget, WidgetInfo? widgetInfo) {
    return GreetingsHeaderWidgetData(
      widgetType,
      title: widget['title'],
      subtitle: widget['subtitle'],
      prefix: widget['prefix'],
      suffix: widget['suffix'],
      imageUrl: widget['imageUrl'],
      lottieUrl: widget['lottieUrl'],
      action:
          widget['action'] != null ? Action.fromJson(widget['action']) : null,
      widgetInfo: widget['widgetInfo'],
    );
  }
}

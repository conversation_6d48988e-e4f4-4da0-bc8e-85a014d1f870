abstract class CoachCLPEvent {
  CoachCLPEvent() : super();
}

class LoadCoachCLPEvent extends CoachCLPEvent {
  final bool showLoader;
  LoadCoachCLPEvent({this.showLoader = true}) : super();

  @override
  String toString() => 'LoadCoachCLPEvent';
}

class AddLoaderCoachCLPEvent extends CoachCLPEvent {
  AddLoaderCoachCLPEvent() : super();

  @override
  String toString() => 'AddLoaderCoachCLPEvent';
}

class ResetCoachCLPEvent extends CoachCLPEvent {
  @override
  String toString() => 'ResetCoachCLPEvent';
}

class ScrollCLPBottomEvent extends CoachCLPEvent {
  final double scrollFactor;
  ScrollCLPBottomEvent({this.scrollFactor = 2.5});
  @override
  String toString() => 'ScrollToBottomEvent';
}

class ProgressCoachmarkShownEvent extends CoachCLPEvent {
  @override
  String toString() => 'ProgressCoachmarkShownEvent';
}

class SubmitRecordConsentEvent extends CoachCLPEvent {
  final bool preference;
  final String patientId;
  final String appointmentId;
  SubmitRecordConsentEvent({this.preference = false, this.patientId = "", this.appointmentId= ""});

  @override
  String toString() => 'SubmitRecordConsentEvent';
}

class LoadBootcampCLPEvent extends CoachCLPEvent {
  final bool showLoader;
  LoadBootcampCLPEvent({this.showLoader = true}) : super();

  @override
  String toString() => 'LoadBootcampCLPEvent';
}

class ResetBootcampCLPEvent extends CoachCLPEvent {
  @override
  String toString() => 'ResetBootcampCLPEvent';
}

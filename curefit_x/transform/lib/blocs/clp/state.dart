import 'package:transform/blocs/clp/models.dart';

abstract class CoachCLPState {
  CoachCLPState() : super();
}

class CoachCLPIdle extends CoachCLPState {
  @override
  String toString() => 'IdleState';
}

class CoachCLPLoading extends CoachCLPState {
  final CoachCLPData? coachCLP;
  final bool showLoader;

  CoachCLPLoading({this.coachCLP, this.showLoader = true});

  @override
  String toString() => 'CoachCLPLoading';
}

class CoachCLPLoaded extends CoachCLPState {
  final bool scrollToEnd;
  final double scrollFactor;
  final CoachCLPData coachCLP;

  CoachCLPLoaded(
      {required this.coachCLP,
      this.scrollToEnd = false,
      this.scrollFactor = 2.5})
      : super();

  @override
  String toString() => 'CoachCLPLoaded';
}

class CoachCLPNotLoaded extends CoachCLPState {
  final String? error;

  CoachCLPNotLoaded([this.error]) : super();

  @override
  String toString() => 'CoachCLPNotLoaded';
}

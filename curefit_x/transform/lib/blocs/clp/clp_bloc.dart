import 'dart:developer';

import 'package:common/action/action_handler.dart' as ActionHandler;
import 'package:common/constants/common_constants.dart';
import 'package:common/network/client.dart';
import 'package:common/network/client_repository.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/clp/events.dart';
import 'package:transform/blocs/clp/models.dart';
import 'package:transform/blocs/clp/state.dart';
import 'package:transform/constants/constants.dart';

class CoachCLPBloc extends Bloc<CoachCLPEvent, CoachCLPState> {
  final String pageId = "transformcoach";
  final String bootcampPageId = "cultbootcamp";
  final CoachClientRepository repository;

  CoachCLPData coachCLP = CoachCLPData(widgets: []);

  CoachCLPBloc(CoachCLPState initialState, this.repository)
      : super(initialState) {
    on<ResetCoachCLPEvent>((event, emit) async {
      coachCLP = CoachCLPData(widgets: []);
      emit(CoachCLPLoaded(coachCLP: coachCLP));
    });

    on<ResetBootcampCLPEvent>((event, emit) async {
      coachCLP = CoachCLPData(widgets: []);
      emit(CoachCLPLoaded(coachCLP: coachCLP));
    });

    on<LoadCoachCLPEvent>((event, emit) async {
      await _mapCoachCLPToState(event, emit);
    });

    on<LoadBootcampCLPEvent>((event, emit) async {
      await _mapBootcampCLPToState(event, emit);
    });

    on<ScrollCLPBottomEvent>((event, emit) async {
      emit(CoachCLPLoaded(
          coachCLP: coachCLP,
          scrollToEnd: true,
          scrollFactor: event.scrollFactor));
    });

    on<ProgressCoachmarkShownEvent>((event, emit) async {
      coachCLP.coachContactWidgetData?.coachmarkInfo = null;
      emit(CoachCLPLoaded(coachCLP: coachCLP));
    });

    on<AddLoaderCoachCLPEvent>((event, emit) async {
      emit(CoachCLPLoading(coachCLP: null));
    });

    on<SubmitRecordConsentEvent>((event, emit) async {
      try {
        final response = await this.repository.submitRecordingPreference(
            event.preference, event.patientId, event.appointmentId);
      } catch (e) {
        log(e.toString());
      }
    });
  }

  Future<void> _mapCoachCLPToState(
      LoadCoachCLPEvent event, Emitter<CoachCLPState> emit) async {
    try {
      emit(CoachCLPLoading(coachCLP: coachCLP, showLoader: event.showLoader));
      final response = await this.repository.getPageData('v2/page/$pageId', {});
      if (response == null) {
        emit(CoachCLPNotLoaded(throw new Error()));
      }
      String? backgroundImageUrl;
      Map<String, dynamic>? backgroundImageAttributes;
      String? lottieUrl;
      ActionHandler.Action? storyAction;
      BackgroundTheme? backgroundTheme;
      ActionHandler.Action? feedbackAction;
      ActionHandler.Action? weightLoggingAction;
      ActionHandler.Action? fitnessDeviceSyncAction;
      ActionHandler.Action? footerAction;
      int? weightModalVisibility;
      String? appBarTitle;
      String? appBarTitleUrl;
      String? coachMarkImageUrl;
      int? clearGrantedPermissionEpoch;

      if (response["pageData"] != null) {
        backgroundImageUrl = response['pageData']['backgroundImageUrl'];
        backgroundImageAttributes =
            response['pageData']['backgroundImageAttributes'];
        lottieUrl = response['pageData']['lottieUrl'];
        if (response['pageData']['storyAction'] != null) {
          storyAction = ActionHandler.Action.fromJson(
              response['pageData']['storyAction']);
        }

        if (response['pageData']['theme'] != null) {
          final themeData = response['pageData']['theme'];
          CanvasTheme? canvasTheme =
              EnumToString.fromString(CanvasTheme.values, themeData['type']);
          if (canvasTheme != null) {
            backgroundTheme = BackgroundTheme(
                theme: canvasTheme,
                lottieUrl: themeData['lottieUrl'],
                width: themeData['width'],
                height: themeData['height']);
          }
        }
        if (response['pageData']['feedbackAction'] != null) {
          feedbackAction = ActionHandler.Action.fromJson(
              response['pageData']['feedbackAction']);
        }
        if (response['pageData']['weightLoggingAction'] != null) {
          weightModalVisibility = response['pageData']['weightModalVisibility'];
          weightLoggingAction = ActionHandler.Action.fromJson(
              response['pageData']['weightLoggingAction']);
        }
        if (response['pageData']['fitnessDeviceSyncAction'] != null) {
          fitnessDeviceSyncAction = ActionHandler.Action.fromJson(
              response['pageData']['fitnessDeviceSyncAction']);
        }
        if (response['pageData']['footerAction'] != null) {
          footerAction = ActionHandler.Action.fromJson(
              response['pageData']['footerAction']);
        }
        if (response['pageData']['appBarTitle'] != null) {
          appBarTitle = response['pageData']['appBarTitle'];
        }
        if (response['pageData']['appBarTitleUrl'] != null) {
          appBarTitleUrl = response['pageData']['appBarTitleUrl'];
        }
        if (response['pageData']['coachMarkImageUrl'] != null) {
          coachMarkImageUrl = response['pageData']['coachMarkImageUrl'];
        }
        clearGrantedPermissionEpoch = response['pageData']['clearGrantedPermissionEpoch'];
      }
      CoachContactWidgetData? coachContactWidgetData;
      AppbarActionWidgetData? appbarActionWidgetData;
      List<dynamic> filteredWidgets = [];
      if (response['body'] != null) {
        List<dynamic> widgets = response['body'];
        widgets.forEach((element) {
          WidgetTypes? widgetType = EnumToString.fromString(
              WidgetTypes.values, element['widgetType']);
          if (widgetType == WidgetTypes.COACH_CONTACT_WIDGET) {
            WidgetInfo widgetInfo = element['widgetMetric'] != null
                ? WidgetInfo.fromJson(element['widgetMetric'], widgetType!)
                : WidgetInfo(
                    widgetType: widgetType!,
                    widgetMetric: WidgetMetric(widgetName: "", widgetId: ""));
            coachContactWidgetData = CoachContactWidgetData.fromJson(
                element, WidgetTypes.COACH_CONTACT_WIDGET, widgetInfo);
          } else if (widgetType == WidgetTypes.APPBAR_ACTION_WIDGET) {
            WidgetInfo widgetInfo = element['widgetMetric'] != null
                ? WidgetInfo.fromJson(element['widgetMetric'], widgetType!)
                : WidgetInfo(
                widgetType: widgetType!,
                widgetMetric: WidgetMetric(widgetName: "", widgetId: ""));
            appbarActionWidgetData = AppbarActionWidgetData.fromJson(
                element, WidgetTypes.APPBAR_ACTION_WIDGET, widgetInfo);
          } else {
            filteredWidgets.add(element);
          }
        });
      }
      coachCLP = CoachCLPData(
          widgets: filteredWidgets,
          coachContactWidgetData: coachContactWidgetData,
          appbarActionWidgetData: appbarActionWidgetData,
          backgroundImageUrl: backgroundImageUrl,
          backgroundImageAttributes: backgroundImageAttributes,
          lottieUrl: lottieUrl,
          storyAction: storyAction,
          backgroundTheme: backgroundTheme,
          feedbackAction: feedbackAction,
          appBarTitle: appBarTitle,
          appBarTitleUrl: appBarTitleUrl,
          weightLoggingAction: weightLoggingAction,
          fitnessDeviceSyncAction: fitnessDeviceSyncAction,
          footerAction: footerAction,
          weightModalVisibility: weightModalVisibility,
          coachMarkImageUrl: coachMarkImageUrl,
          clearGrantedPermissionEpoch: clearGrantedPermissionEpoch);
      if (isFirstLaunch) {
        isFirstLaunch = false;
        await Future.delayed(
            Duration(milliseconds: iOSLaunchDelayLimitInMilliSeconds));
      }
      emit(CoachCLPLoaded(coachCLP: coachCLP));
    } on NetworkException catch (exception) {
      emit(CoachCLPNotLoaded(exception.subTitle));
    } catch (e) {
      emit(CoachCLPNotLoaded(ERROR_MSG));
    }
  }

  Future<void> _mapBootcampCLPToState(
      LoadBootcampCLPEvent event, Emitter<CoachCLPState> emit) async {
    try {
      emit(CoachCLPLoading(coachCLP: coachCLP, showLoader: event.showLoader));
      final response = await this.repository.getPageData('v2/page/$bootcampPageId', {});
      if (response == null) {
        emit(CoachCLPNotLoaded(throw new Error()));
      }
      String? backgroundImageUrl;
      Map<String, dynamic>? backgroundImageAttributes;
      String? lottieUrl;
      ActionHandler.Action? storyAction;
      BackgroundTheme? backgroundTheme;
      ActionHandler.Action? feedbackAction;
      ActionHandler.Action? weightLoggingAction;
      ActionHandler.Action? fitnessDeviceSyncAction;
      ActionHandler.Action? footerAction;
      int? weightModalVisibility;
      String? appBarTitle;
      String? appBarTitleUrl;
      String? coachMarkImageUrl;
      int? clearGrantedPermissionEpoch;

      if (response["pageData"] != null) {
        backgroundImageUrl = response['pageData']['backgroundImageUrl'];
        backgroundImageAttributes =
        response['pageData']['backgroundImageAttributes'];
        lottieUrl = response['pageData']['lottieUrl'];
        if (response['pageData']['storyAction'] != null) {
          storyAction = ActionHandler.Action.fromJson(
              response['pageData']['storyAction']);
        }

        if (response['pageData']['theme'] != null) {
          final themeData = response['pageData']['theme'];
          CanvasTheme? canvasTheme =
          EnumToString.fromString(CanvasTheme.values, themeData['type']);
          if (canvasTheme != null) {
            backgroundTheme = BackgroundTheme(
                theme: canvasTheme,
                lottieUrl: themeData['lottieUrl'],
                width: themeData['width'],
                height: themeData['height']);
          }
        }
        if (response['pageData']['feedbackAction'] != null) {
          feedbackAction = ActionHandler.Action.fromJson(
              response['pageData']['feedbackAction']);
        }
        if (response['pageData']['weightLoggingAction'] != null) {
          weightModalVisibility = response['pageData']['weightModalVisibility'];
          weightLoggingAction = ActionHandler.Action.fromJson(
              response['pageData']['weightLoggingAction']);
        }
        if (response['pageData']['fitnessDeviceSyncAction'] != null) {
          fitnessDeviceSyncAction = ActionHandler.Action.fromJson(
              response['pageData']['fitnessDeviceSyncAction']);
        }
        if (response['pageData']['footerAction'] != null) {
          footerAction = ActionHandler.Action.fromJson(
              response['pageData']['footerAction']);
        }
        if (response['pageData']['appBarTitle'] != null) {
          appBarTitle = response['pageData']['appBarTitle'];
        }
        if (response['pageData']['appBarTitleUrl'] != null) {
          appBarTitleUrl = response['pageData']['appBarTitleUrl'];
        }
        if (response['pageData']['coachMarkImageUrl'] != null) {
          coachMarkImageUrl = response['pageData']['coachMarkImageUrl'];
        }
        clearGrantedPermissionEpoch = response['pageData']['clearGrantedPermissionEpoch'];
      }
      CoachContactWidgetData? coachContactWidgetData;
      AppbarActionWidgetData? appbarActionWidgetData;
      List<dynamic> filteredWidgets = [];
      if (response['body'] != null) {
        List<dynamic> widgets = response['body'];
        widgets.forEach((element) {
          WidgetTypes? widgetType = EnumToString.fromString(
              WidgetTypes.values, element['widgetType']);
          if (widgetType == WidgetTypes.COACH_CONTACT_WIDGET) {
            WidgetInfo widgetInfo = element['widgetMetric'] != null
                ? WidgetInfo.fromJson(element['widgetMetric'], widgetType!)
                : WidgetInfo(
                widgetType: widgetType!,
                widgetMetric: WidgetMetric(widgetName: "", widgetId: ""));
            coachContactWidgetData = CoachContactWidgetData.fromJson(
                element, WidgetTypes.COACH_CONTACT_WIDGET, widgetInfo);
          } else if (widgetType == WidgetTypes.APPBAR_ACTION_WIDGET) {
            WidgetInfo widgetInfo = element['widgetMetric'] != null
                ? WidgetInfo.fromJson(element['widgetMetric'], widgetType!)
                : WidgetInfo(
                widgetType: widgetType!,
                widgetMetric: WidgetMetric(widgetName: "", widgetId: ""));
            appbarActionWidgetData = AppbarActionWidgetData.fromJson(
                element, WidgetTypes.APPBAR_ACTION_WIDGET, widgetInfo);
          } else {
            filteredWidgets.add(element);
          }
        });
      }
      coachCLP = CoachCLPData(
          widgets: filteredWidgets,
          coachContactWidgetData: coachContactWidgetData,
          appbarActionWidgetData: appbarActionWidgetData,
          backgroundImageUrl: backgroundImageUrl,
          backgroundImageAttributes: backgroundImageAttributes,
          lottieUrl: lottieUrl,
          storyAction: storyAction,
          backgroundTheme: backgroundTheme,
          feedbackAction: feedbackAction,
          appBarTitle: appBarTitle,
          appBarTitleUrl: appBarTitleUrl,
          weightLoggingAction: weightLoggingAction,
          fitnessDeviceSyncAction: fitnessDeviceSyncAction,
          footerAction: footerAction,
          weightModalVisibility: weightModalVisibility,
          coachMarkImageUrl: coachMarkImageUrl,
          clearGrantedPermissionEpoch: clearGrantedPermissionEpoch);
      emit(CoachCLPLoaded(coachCLP: coachCLP));
    } on NetworkException catch (exception) {
      emit(CoachCLPNotLoaded(exception.subTitle));
    } catch (e) {
      emit(CoachCLPNotLoaded(ERROR_MSG));
    }
  }
}

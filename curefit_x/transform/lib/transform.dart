library transform;

import 'dart:convert';

import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/booking/booking_bloc.dart';
import 'package:common/blocs/calendar/calendar_bloc.dart';
import 'package:common/blocs/checkout/checkout_state.dart'
    as CommonCheckoutState;
import 'package:common/blocs/checkout/trial_checkout_bloc.dart';
import 'package:common/blocs/checkout_v2/checkout_v2_bloc.dart';
import 'package:common/blocs/checkout_v2/checkout_v2_state.dart';
import 'package:common/blocs/fitness_device/fitness_device_bloc.dart';
import 'package:common/blocs/fitness_device/state.dart';
import 'package:common/blocs/form/form_action_bloc.dart';
import 'package:common/blocs/form/form_bloc.dart';
import 'package:common/blocs/form/state.dart';
import 'package:common/blocs/page/page_bloc.dart';
import 'package:common/blocs/payment/payment_bloc.dart';
import 'package:common/blocs/slot_selection/slot_selection_bloc.dart';
import 'package:common/blocs/slot_selection/state.dart';
import 'package:common/network/booking_repository.dart';
import 'package:common/network/checkout_repository.dart';
import 'package:common/network/client.dart';
import 'package:common/network/client_repository.dart';
import 'package:common/network/clp_repository.dart';
import 'package:common/network/fitness_device_repository.dart';
import 'package:common/network/form_repository.dart';
import 'package:common/network/lifestyle_form_repository.dart';
import 'package:common/network/lifestyle_re_evaluation_form_repository.dart';
import 'package:common/network/pack_purchase_repository.dart';
import 'package:common/network/payment_repository.dart';
import 'package:common/network/slot_selection_repository.dart';
import 'package:common/network/trainer_repository.dart';
import 'package:common/ui/checkout/trial_checkout_screen.dart';
import 'package:common/ui/checkout_v2/screens/checkout_v2_screen.dart';
import 'package:common/ui/form/form_screen.dart';
import 'package:common/ui/page/screen/fl_tab_page.dart';
import 'package:common/ui/screens/info_screen.dart';
import 'package:common/ui/slot_selection/screens/slot_selection_screen.dart';
import 'package:common/ui/widget_builder.dart' as CommonWidgetBuilder;
import 'package:common/user/user_repository.dart';
import 'package:enum_to_string/enum_to_string.dart';
import "package:flutter/material.dart";
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/UI/challenges/screen/challenges_screen.dart';
import 'package:transform/UI/checkout/screens/checkout_screen.dart';
import 'package:transform/UI/checkout_v2/screens/address_form_screen.dart';
import 'package:transform/UI/checkout_v2/screens/address_selection_screen.dart';
import 'package:transform/UI/checkout_v2/screens/batch_selection_screen.dart';
import 'package:transform/UI/checkout_v2/screens/center_selection_screen.dart';
import 'package:transform/UI/clp/screens/before_after_journey_screen.dart';
import 'package:transform/UI/clp/screens/bootcamp_clp.dart';
import 'package:transform/UI/clp/screens/bootcamp_pulse_clp.dart';
import 'package:transform/UI/clp/screens/clp.dart';
import 'package:transform/UI/clp/screens/image_upload_confirmation_screen.dart';
import 'package:transform/UI/clp/screens/lifestyle_assessment_screen.dart';
import 'package:transform/UI/clp/screens/lift_clp.dart';
import 'package:transform/UI/clp/screens/user_metric_screen.dart';
import 'package:transform/UI/clp/screens/user_progress_screen.dart';
import 'package:transform/UI/clp/screens/weight_loss_clp.dart';
import 'package:transform/UI/clp/screens/weight_loss_tab.dart';
import 'package:transform/UI/content/chapter_screen.dart';
import 'package:transform/UI/content/content_list_screen.dart';
import 'package:transform/UI/content/content_progress_screen.dart';
import 'package:transform/UI/content_history/screen/content_history_screen.dart';
import 'package:transform/UI/cult_habit_building/habit_building_screen.dart';
import 'package:transform/UI/fitness_plan/screens/fitness_plan_screen.dart';
import 'package:transform/UI/google_review/google_review_screen.dart';
import 'package:transform/UI/guides/screen/guides_screen.dart';
import 'package:transform/UI/health_score/screen/lifestyle_form_info_screen.dart';
import 'package:transform/UI/onboarding/screens/chart_screen.dart';
import 'package:transform/UI/onboarding/screens/intro_screen.dart';
import 'package:transform/UI/order/screens/order_confirmation_screen.dart';
import 'package:transform/UI/pack_details/screens/pack_details_screen.dart';
import 'package:transform/UI/pack_purchase/screens/pack_purchase_screen.dart';
import 'package:transform/UI/recommendation_plan/screen/recommendation_plan_screen.dart';
import 'package:transform/UI/referral/screen/referral_screen.dart';
import 'package:transform/UI/review/screen/review_screen.dart';
import 'package:transform/UI/testimonials/testimonials_screen.dart';
import 'package:transform/UI/trainer/screens/coach_detail_screen.dart';
import 'package:transform/UI/trainer/screens/coach_selection_screen.dart';
import 'package:transform/UI/user_experience_form/screens/user_experience_form_screen.dart';
import 'package:transform/UI/video_content/screens/video_content_screen.dart';
import 'package:transform/UI/video_guide/screens/video_guide_screen.dart';
import 'package:transform/blocs/before_after_journey/before_after_journey_bloc.dart';
import 'package:transform/blocs/bootcamp_pulse/bootcamp_pulse_clp_bloc.dart';
import 'package:transform/blocs/center_selector/center_selector_bloc.dart';
import 'package:transform/blocs/center_selector/state.dart';
import 'package:transform/blocs/challenges/challenges_bloc.dart';
import 'package:transform/blocs/challenges/state.dart';
import 'package:transform/blocs/clp/clp_bloc.dart';
import 'package:transform/blocs/clp/state.dart';
import 'package:transform/blocs/content/chapter_bloc.dart';
import 'package:transform/blocs/content/content_bloc.dart';
import 'package:transform/blocs/content/progress/content_progress_bloc.dart';
import 'package:transform/blocs/content/progress/state.dart';
import 'package:transform/blocs/content_history/content_history_bloc.dart';
import 'package:transform/blocs/cult_habit_building/cult_habit_building_bloc.dart';
import 'package:transform/blocs/fitness_plan/fitness_plan_bloc.dart';
import 'package:transform/blocs/guides/guides_bloc.dart';
import 'package:transform/blocs/guides/state.dart';
import 'package:transform/blocs/habit/habit_bloc.dart';
import 'package:transform/blocs/habit/state.dart';
import 'package:transform/blocs/habit/update_habit_bloc.dart';
import 'package:transform/blocs/lifestyle_area_summary/lifestyle_area_summary_bloc.dart';
import 'package:transform/blocs/lifestyle_area_summary/state.dart';
import 'package:transform/blocs/lifestyle_assessment/lifestyle_assessment_bloc.dart';
import 'package:transform/blocs/lifestyle_re_evaluation/lifestyle_re_evaluation_bloc.dart';
import 'package:transform/blocs/lifestyle_re_evaluation/state.dart';
import 'package:transform/blocs/lift_clp/lift_clp_bloc.dart';
import 'package:transform/blocs/metric_input/metric_input_bloc.dart';
import 'package:transform/blocs/onboarding/onboarding_bloc.dart';
import 'package:transform/blocs/onboarding/state.dart';
import 'package:transform/blocs/pack_details/pack_details_bloc.dart';
import 'package:transform/blocs/pack_purchase/pack_purchase_bloc.dart';
import 'package:transform/blocs/pack_purchase/state.dart';
import 'package:transform/blocs/recommendation_plan/recommendation_plan_bloc.dart';
import 'package:transform/blocs/review/review_bloc.dart';
import 'package:transform/blocs/review/state.dart';
import 'package:transform/blocs/settings/settings_bloc.dart';
import 'package:transform/blocs/settings/state.dart';
import 'package:transform/blocs/testimonials/state.dart';
import 'package:transform/blocs/testimonials/testimonials_bloc.dart';
import 'package:transform/blocs/trainer/coach_detail_bloc.dart';
import 'package:transform/blocs/trainer/state.dart';
import 'package:transform/blocs/trainer/trainer_bloc.dart';
import 'package:transform/blocs/user_experience_form/user_experience_form_bloc.dart';
import 'package:transform/blocs/user_image/user_image_bloc.dart';
import 'package:transform/blocs/user_progress/state.dart';
import 'package:transform/blocs/user_progress/user_progress_bloc.dart';
import 'package:transform/blocs/video_content/states.dart';
import 'package:transform/blocs/video_content/video_content_bloc.dart';
import 'package:transform/blocs/video_guide/video_guides_bloc.dart';
import 'package:transform/blocs/weekly_workflow_bloc/state.dart';
import 'package:transform/blocs/weekly_workflow_bloc/weekly_workflow_bloc.dart';
import 'package:transform/blocs/weight_loss_tab/weight_loss_tab_bloc.dart';
import 'package:transform/constants/constants.dart';
import 'package:transform/factory/content_widget_builder.dart';
import 'package:transform/network/address_repository.dart';
import 'package:transform/network/content_repository.dart';
import 'package:transform/network/cult_habit_building_repository.dart';
import 'package:transform/network/feedback_repository.dart';
import 'package:transform/network/habit_repository.dart';
import 'package:transform/network/onboarding_repository.dart';
import 'package:transform/network/settings_repository.dart';
import 'package:transform/network/user_image_repository.dart';
import 'package:transform/network/video_guide_repository.dart';

import 'UI/calendar/screen/calendar_screen.dart';
import 'UI/fitness_plan/screens/fitness_plan_screen_v2.dart';
import 'UI/mealPlanner/screens/meal_planner_screen.dart';
import 'blocs/address_form/address_form_bloc.dart';
import 'blocs/calendar/action_calendar_bloc.dart';
import 'blocs/calendar/state.dart';
import 'blocs/content/state.dart';
import 'blocs/fitness_plan/fitness_plan_bloc_v2.dart';
import 'blocs/google_review/google_review_bloc.dart';
import 'blocs/meal_planner/meal_planner_bloc.dart';
import 'blocs/metric_input/state.dart';
import 'blocs/referral/referral_screen_bloc.dart';
import 'blocs/user_experience_form/button_enable_bloc.dart';
import 'blocs/user_experience_form/states.dart';
import 'blocs/video_guide/state.dart';
import 'blocs/weight_loss_clp/weight_loss_clp_bloc.dart';
import 'factory/widget_builder.dart' as TransformWidgetBuilder;
import 'network/meal_repository.dart';
import 'network/weekly_workflow_repository.dart';

class Transform {
  CommonWidgetBuilder.IWidgetBuilder get widgetBuilder {
    return TransformWidgetBuilder.WidgetBuilder();
  }

  String _routeName(RouteNames routeName) {
    return '/${EnumToString.convertToString(routeName)}';
  }

  Map<String, WidgetBuilder> tabRoute(NetworkClient networkClient) {
    return {
      _routeName(RouteNames.weight_loss): (_) =>
          _weightLossRoute(TabMode.EMBEDDED),
      _routeName(RouteNames.weight_loss_tab): (_) =>
          _weightLossTabRoute(networkClient, TabMode.EMBEDDED),
      _routeName(RouteNames.tf_weight_loss_tab): (_) =>
          _weightLossTabV2Route(networkClient, TabMode.EMBEDDED),
    };
  }

  Widget _weightLossRoute(TabMode tabMode) {
    return WeightLossClp(tabMode: TabMode.EMBEDDED);
  }

  Widget _weightLossTabRoute(NetworkClient networkClient, TabMode tabMode) {
    return BlocProvider(
      create: (BuildContext context) => PageBloc(
          repository: CLPRepository(networkClient),
          userRepository: RepositoryProvider.of<UserRepository>(context)),
      child: FLTabPage(
        pageId: "weight_loss_tab",
        inTabMode: true,
        tab: AppTabs.TRANSFORM,
      ),
    );
  }

  Widget _weightLossTabV2Route(NetworkClient networkClient, TabMode tabMode) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
            create: (BuildContext context) =>
                WeightLossTabBloc(CoachClientRepository(networkClient))),
        BlocProvider(
            create: (BuildContext context) => PageBloc(
                repository: CLPRepository(networkClient),
                userRepository:
                    RepositoryProvider.of<UserRepository>(context))),
        BlocProvider<FormBloc>(
          create: (context) => FormBloc(
              FormIdleState(),
              LifestyleReEvaluationFormRepository(networkClient),
              BlocProvider.of<FormActionBloc>(context)),
        )
      ],
      child: WeightLossTab(
        inTabMode: true,
        tab: AppTabs.TRANSFORM,
      ),
    );
  }

  List<String> fadeTransitionRoutes() {
    return [
      EnumToString.convertToString(RouteNames.tf_slot_selection),
      EnumToString.convertToString(RouteNames.tf_trial_checkout),
    ];
  }

  List<String> contextualTransitionRoutes() {
    return [
      EnumToString.convertToString(RouteNames.tf_checkout),
    ];
  }

  List<BlocProvider> blocProviders(NetworkClient networkClient) {
    return [
      BlocProvider<FormActionBloc>(
          create: (context) => FormActionBloc(FormActionIdleState(),
              actionBloc: BlocProvider.of<ActionBloc>(context))),
      BlocProvider<BookingBloc>(
          create: (context) => BookingBloc(
              bookingRepository: BookingRepository(networkClient),
              context: context)),
      BlocProvider<CoachCLPBloc>(
          create: (context) => CoachCLPBloc(
              CoachCLPIdle(), CoachClientRepository(networkClient))),
      BlocProvider<HabitBloc>(
          lazy: false,
          create: (context) => HabitBloc(
                HabitIdleState(),
                HabitRepository(networkClient),
              )),
      BlocProvider<WeightLossTabBloc>(create: (context) => WeightLossTabBloc(
        CoachClientRepository(networkClient),
      )),
      BlocProvider<WeeklyWorkflowBloc>(
          lazy: false,
          create: (context) => WeeklyWorkflowBloc(
            WeeklyWorkflowIdleState(),
            WeeklyWorkflowRepository(networkClient),
          )),
      BlocProvider<UpdateHabitBloc>(
          create: (context) => UpdateHabitBloc(
              repository: HabitRepository(networkClient),
              habitBloc: BlocProvider.of<HabitBloc>(context))),
      BlocProvider<ChapterBloc>(
        create: (context) {
          ContentRepository contentRepository =
              ContentRepository(networkClient);
          return ChapterBloc(CourseStateIdle(), contentRepository);
        },
      ),
      BlocProvider<SettingsBloc>(
        create: (context) =>
            SettingsBloc(SettingsIdle(), SettingsRepository(networkClient)),
      ),
      BlocProvider<LifestyleReEvaluationBloc>(
        create: (context) {
          LifestyleReEvaluationFormRepository repository =
              LifestyleReEvaluationFormRepository(networkClient);
          return LifestyleReEvaluationBloc(
              LifestyleReEvaluationIdleState(), repository);
        },
      ),
      BlocProvider<CalendarBloc>(create: (context) => CalendarBloc()),
      BlocProvider<FitnessDeviceBloc>(
        create: (context) => FitnessDeviceBloc(
          FitnessDeviceIdleState(),
          FitnessDeviceRepository(networkClient),
        ),
      ),
      BlocProvider<UserImageBloc>(
        create: (context) => UserImageBloc(
          UserImageRepository(networkClient),
        ),
      ),
      BlocProvider<VideoGuideBloc>(
        create: (context) {
          VideoGuideRepository repository = VideoGuideRepository(networkClient);
          return VideoGuideBloc(VideoGuideIdle(), repository);
        },
      ),
      BlocProvider<CenterSelectorBloc>(create: (context) {
        return CenterSelectorBloc(
            CenterSelectorIdleState(), CoachClientRepository(networkClient));
      }),
      BlocProvider<LiftClpBloc>(
          create: (context) =>
              LiftClpBloc(CoachClientRepository(networkClient))),
      BlocProvider<WeightLossClpBloc>(
          create: (context) =>
              WeightLossClpBloc(CoachClientRepository(networkClient))),
    ];
  }

  List<String> resumeEventRoutes() {
    return [_routeName(RouteNames.tf_clp)];
  }

  Map<String, WidgetBuilder> getRoutes(
      NetworkClient networkClient, Function onClose) {
    PackPurchaseRepository packPurchaseRepository =
        PackPurchaseRepository(networkClient);
    CheckoutRepository checkoutRepository = CheckoutRepository(networkClient);
    TrainerRepository trainerRepository = TrainerRepository(networkClient);
    FeedbackRepository feedbackRepository = FeedbackRepository(networkClient);
    SlotSelectionRepository slotSelectionRepository =
        SlotSelectionRepository(networkClient);
    OnboardingRepository onboardingRepository =
        OnboardingRepository(networkClient);
    CoachClientRepository coachClientRepository =
        CoachClientRepository(networkClient);
    HabitBuildingRepository habitBuildingRepository =
        HabitBuildingRepository(networkClient);
    ContentRepository contentRepository = ContentRepository(networkClient);
    MealPlannerRepository mealPlannerRepository =
        MealPlannerRepository(networkClient);

    UserImageRepository userImageRepository =
        UserImageRepository(networkClient);
    VideoGuideRepository videoGuideRepository =
        VideoGuideRepository(networkClient);

    return {
      _routeName(RouteNames.tf_chapter): (_) =>
          ChapterScreen(widgetBuilder: ContentWidgetBuilder()),
      _routeName(RouteNames.tf_content_list): (_) => MultiBlocProvider(
            providers: [
              BlocProvider(create: (context) {
                ContentRepository contentRepository =
                    ContentRepository(networkClient);
                return ContentBloc(CourseStateIdle(), contentRepository);
              })
            ],
            child: ContentListScreen(),
          ),
      _routeName(RouteNames.tf_content_progress): (_) =>
          BlocProvider<ContentProgressBloc>(
            create: (context) => ContentProgressBloc(
                ContentProgressStateIdle(), ContentRepository(networkClient)),
            child: ContentProgressScreen(),
          ),
      _routeName(RouteNames.tf_onboarding): (_) => BlocProvider(
            create: (context) =>
                OnboardingBloc(OnboardingIdleState(), onboardingRepository),
            child: IntroScreen(onClose: onClose),
          ),
      _routeName(RouteNames.tf_question): (_) => BlocProvider<FormBloc>(
          create: (context) => FormBloc(
              FormIdleState(),
              FormRepository(networkClient),
              BlocProvider.of<FormActionBloc>(context)),
          child: FormScreen()),
      _routeName(RouteNames.tf_lifestyle_form): (_) => BlocProvider<FormBloc>(
          create: (context) => FormBloc(
              FormIdleState(),
              LifestyleFormRepository(networkClient),
              BlocProvider.of<FormActionBloc>(context)),
          child: FormScreen()),
      _routeName(RouteNames.tf_pack_purchase): (_) => BlocProvider(
            create: (context) => PackPurchaseBloc(
                PackPurchaseIdleState(), packPurchaseRepository),
            child: PackPurchaseScreen(),
          ),
      _routeName(RouteNames.tf_checkout): (_) => BlocProvider(
            create: (context) =>
                CheckoutV2Bloc(CheckoutIdle(), checkoutRepository),
            child: CheckoutScreen(),
          ),
      _routeName(RouteNames.tf_coach_selection): (_) => BlocProvider(
            create: (context) => TrainerBloc(TrainerIdle(), trainerRepository),
            child: CoachSelectionScreen(),
          ),
      _routeName(RouteNames.tf_chart): (_) => ChartScreen(),
      _routeName(RouteNames.tf_video_guide): (_) => BlocProvider(
            create: (context) =>
                VideoGuideBloc(VideoGuideIdle(), videoGuideRepository),
            child: VideoGuideScreen(),
          ),
      _routeName(RouteNames.tf_coachdetail): (_) => BlocProvider(
            create: (context) =>
                CoachDetailBloc(TrainerIdle(), trainerRepository),
            child: CoachDetailScreen(),
          ),
      _routeName(RouteNames.tf_slot_selection): (_) => BlocProvider(
            create: (context) =>
                SlotSelectionBloc(SlotSelectionIdle(), slotSelectionRepository),
            child: SlotSelectionScreen(),
          ),
      _routeName(RouteNames.tf_orderconfirmation): (_) =>
          MultiBlocProvider(providers: [
            BlocProvider(
                create: (context) =>
                    CoachDetailBloc(TrainerIdle(), trainerRepository)),
          ], child: OrderConfirmationScreen()),
      _routeName(RouteNames.tf_info): (_) => InfoScreen(),
      _routeName(RouteNames.tf_user_metric): (_) => BlocProvider(
            create: (context) =>
                MetricInputBloc(MetricInputIdle(), coachClientRepository),
            child: UserMetricScreen(),
          ),
      _routeName(RouteNames.tf_clp): (_) => BlocProvider<FormBloc>(
          create: (context) => FormBloc(
              FormIdleState(),
              LifestyleReEvaluationFormRepository(networkClient),
              BlocProvider.of<FormActionBloc>(context)),
          child: CoachCLP()),
      _routeName(RouteNames.tf_trial_checkout): (_) =>
          MultiBlocProvider(providers: [
            BlocProvider(create: (context) => CalendarBloc()),
            BlocProvider(
                create: (context) => PaymentBloc(
                    paymentRepository: PaymentRepository(networkClient))),
            BlocProvider(
              create: (context) => TrialCheckoutBloc(
                  CommonCheckoutState.CheckoutIdle(), checkoutRepository),
            )
          ], child: TrialCheckoutScreen()),
      _routeName(RouteNames.tf_user_progress): (_) => BlocProvider(
            create: (context) =>
                UserProgressBloc(UserProgressIdle(), coachClientRepository),
            child: UserProgressScreen(),
          ),
      _routeName(RouteNames.tf_calendar_action): (_) => BlocProvider(
            create: (context) => ActionCalendarBloc(ActionCalendarIdleState(),
                coachClientRepository, habitBuildingRepository),
            child: CalendarScreen(),
          ),
      _routeName(RouteNames.tf_testimonials): (_) => BlocProvider(
            create: (context) => TestimonialsBloc(
                TestimonialsIdleState(), coachClientRepository),
            child: TestimonialsScreen(),
          ),
      _routeName(RouteNames.tf_review): (_) => BlocProvider(
            create: (context) => ReviewBloc(ReviewIdle(), feedbackRepository),
            child: ReviewScreen(),
          ),
      _routeName(RouteNames.tf_lifestyle_form_info): (_) => BlocProvider(
            create: (context) => LifestyleAreaSummaryBloc(
                LifestyleAreaSummaryIdleState(), coachClientRepository),
            child: LifestyleFormInfoScreen(),
          ),
      _routeName(RouteNames.tf_guides): (_) => BlocProvider(
            create: (context) =>
                GuidesBloc(GuidesDataIdleState(), coachClientRepository),
            child: GuidesScreen(),
          ),
      _routeName(RouteNames.tf_mealplanner): (context) =>
          BlocProvider<MealPlannerBloc>(
              create: (context) =>
                  MealPlannerBloc(repository: mealPlannerRepository),
              child: MealPlannerScreen()),
      _routeName(RouteNames.tf_content_history): (_) => BlocProvider(
          create: (context) {
            return ContentHistoryBloc(contentRepository);
          },
          child: ContentHistoryPage()),
      _routeName(RouteNames.tf_challenges): (_) => BlocProvider(
          create: (context) {
            return ChallengesBloc(
                ChallengesBlocIdleState(), coachClientRepository);
          },
          child: ChallengesScreen()),
      _routeName(RouteNames.tf_fitnessplan): (_) => BlocProvider(
            create: (context) => FitnessPlanBloc(contentRepository),
            child: FitnessPlanScreen(),
          ),
      _routeName(RouteNames.tf_fitnessplanv2): (_) => BlocProvider(
        create: (context) => FitnessPlanBlocV2(contentRepository),
        child: FitnessPlanScreenV2(),
      ),
      _routeName(RouteNames.tf_pack_details): (_) => BlocProvider(
            create: (context) => PackDetailsBloc(coachClientRepository),
            child: PackDetailsScreen(),
          ),
      _routeName(RouteNames.tf_user_experience_form): (_) => MultiBlocProvider(
            providers: [
              BlocProvider(create: (context) {
                return UserExperienceFormBloc(
                    UserExperienceFormIdleState(), coachClientRepository);
              }),
              BlocProvider(create: (context) {
                return ButtonEnableBloc();
              }),
            ],
            child: UserExperienceFormScreen(),
          ),
      _routeName(RouteNames.tf_image_upload_confirmation): (_) => BlocProvider(
            create: (context) => UserImageBloc(userImageRepository),
            child: ImageUploadConfirmationScreen(),
          ),
      _routeName(RouteNames.tf_video_content): (_) => BlocProvider(
            create: (context) => VideoContentViewBloc(
                VideoContentViewIdleState(), coachClientRepository),
            child: VideoContentScreen(),
          ),
      _routeName(RouteNames.tf_new_checkout): (_) => CheckoutScreenV2(
            isTransformFlow: true,
          ),
      _routeName(RouteNames.tf_center_selector): (_) => CenterSelectionScreen(),
      _routeName(RouteNames.tf_batch_selector): (_) => BatchSelectionScreen(),
      _routeName(RouteNames.tf_bootcamp): (_) => BlocProvider<FormBloc>(
          create: (context) => FormBloc(
              FormIdleState(),
              LifestyleReEvaluationFormRepository(networkClient),
              BlocProvider.of<FormActionBloc>(context)),
          child: CultBootcampCLP()),
      _routeName(RouteNames.lift): (_) => LiftClp(),
      _routeName(RouteNames.weight_loss): (_) => WeightLossClp(),
      _routeName(RouteNames.weight_loss_tab): (_) => BlocProvider(
            create: (BuildContext context) => PageBloc(
                repository: CLPRepository(networkClient),
                userRepository: RepositoryProvider.of<UserRepository>(context)),
            child: FLTabPage(
              pageId: "weight_loss_tab",
              inTabMode: false,
            ),
          ),
      _routeName(RouteNames.tf_weight_loss_tab): (_) => MultiBlocProvider(
            providers: [
              BlocProvider(
                  create: (BuildContext context) =>
                      WeightLossTabBloc(CoachClientRepository(networkClient))),
              BlocProvider(
                  create: (BuildContext context) => PageBloc(
                      repository: CLPRepository(networkClient),
                      userRepository:
                          RepositoryProvider.of<UserRepository>(context))),
              BlocProvider<FormBloc>(
                create: (context) => FormBloc(
                    FormIdleState(),
                    LifestyleReEvaluationFormRepository(networkClient),
                    BlocProvider.of<FormActionBloc>(context)),
              )
            ],
            child: WeightLossTab(
              inTabMode: false,
            ),
          ),
      _routeName(RouteNames.tf_before_after): (_) => BlocProvider(
            create: (context) =>
                BeforeAfterJourneyClpBloc(coachClientRepository),
            child: BeforeAfterJourneyClp(),
          ),
      _routeName(RouteNames.tf_address_selector): (_) =>
          AddressSelectionScreen(),
      _routeName(RouteNames.tf_address_form): (_) => BlocProvider(
            create: (context) =>
                AddressFormScreenBloc(AddressRepository(networkClient)),
            child: AddressFormScreen(),
          ),
      _routeName(RouteNames.tf_recommendation_plan): (_) => BlocProvider(
            create: (context) =>
                RecommendationPlanBloc(CoachClientRepository(networkClient)),
            child: RecommendationPlanClp(),
          ),
      _routeName(RouteNames.habit_building): (_) => BlocProvider(
            create: (context) => HabitBuildingBloc(
                repository: HabitBuildingRepository(networkClient)),
            child: const HabitBuiling(),
          ),
      _routeName(RouteNames.tf_recommendation_plan): (_) => BlocProvider(
            create: (context) =>
                RecommendationPlanBloc(CoachClientRepository(networkClient)),
            child: RecommendationPlanClp(),
          ),
      _routeName(RouteNames.tf_referral): (_) => MultiBlocProvider(
            providers: [
              BlocProvider(
                  create: (BuildContext context) =>
                      ReferralScreenBloc(CoachClientRepository(networkClient))),
              BlocProvider(
                  create: (BuildContext context) => PageBloc(
                      repository: CLPRepository(networkClient),
                      userRepository:
                          RepositoryProvider.of<UserRepository>(context))),
            ],
            child: ReferralScreen(),
          ),
      _routeName(RouteNames.bootcamp_pulse_pre): (_) =>
          BlocProvider<BootcampPulseClpBloc>(
              create: (context) =>
                  BootcampPulseClpBloc(CoachClientRepository(networkClient)),
              child: BootcampPulseClp()),
      _routeName(RouteNames.tf_google_review): (_) =>
          BlocProvider<GoogleReviewBloc>(
              create: (context) =>
                  GoogleReviewBloc(CoachClientRepository(networkClient)),
              child: GoogleReviewClp()),
      _routeName(RouteNames.tf_lifestyle_assessment): (_) =>
          BlocProvider<LifestyleAssessmentScreenBloc>(
              create: (context) =>
                  LifestyleAssessmentScreenBloc(CoachClientRepository(networkClient)),
              child: LifestyleAssessmentScreen()),
    };
  }

  handleNotification(
      dynamic payload, NetworkClient client, BuildContext? context) async {
    if (payload['action'] != null) {
      HabitCardNotificationAction? action = EnumToString.fromString(
          HabitCardNotificationAction.values, payload['action']);
      if (action != null) {
        if (payload['notifee'] != null) {
          dynamic notifee = jsonDecode(payload['notifee']);
          if (notifee != null && notifee['data']['meta'] != null) {
            dynamic meta;
            if (notifee['data']['meta'] is String) {
              meta = jsonDecode(notifee['data']['meta']);
            } else {
              meta = notifee['data']['meta'];
            }
            String response = action == HabitCardNotificationAction.HABIT_DONE
                ? "DONE"
                : "NOT_DONE";
            String habitCardId = meta['habitCardId'].toString();
            HabitRepository habitRepository = HabitRepository(client);

            await habitRepository.updateHabit(habitCardId, response,
                channel: "PN");
            if (context != null) {
              RepositoryProvider.of<AnalyticsRepository>(context)
                  .logWidgetClick(
                      widgetInfo: CommonWidgetBuilder.WidgetInfo(
                          widgetMetric: CommonWidgetBuilder.WidgetMetric(
                              widgetId: "HABIT_INTERACTIVE_PN",
                              widgetName: "HABIT_INTERACTIVE_PN"),
                          widgetType: CommonWidgetBuilder
                              .WidgetTypes.HABIT_CARD_WIDGET),
                      extraInfo: {"response": response});
            }
          }
        }
      }
    }
  }
}

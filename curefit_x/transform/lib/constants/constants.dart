import 'package:flutter/material.dart';
import 'dart:io' show Platform;

const String CHART_SCREEN_TITLE =
    "We create long term results through habit and behaviour change among users, not restrictive dieting!";

enum RouteNames {
  tf_onboarding,
  tf_question,
  tf_pack_purchase,
  tf_checkout,
  tf_chart,
  tf_coach_selection,
  tf_coachdetail,
  tf_slot_selection,
  tf_orderconfirmation,
  tf_info,
  tf_trial_checkout,
  tf_clp,
  tf_content_progress,
  tf_user_metric,
  tf_user_progress,
  tf_content_list,
  tf_text_styles,
  tf_chapter,
  tf_web_view,
  tf_calendar_action,
  tf_testimonials,
  tf_review,
  tf_health_scores,
  tf_lifestyle_form,
  tf_lifestyle_form_info,
  tf_guides,
  tf_mealplanner,
  tf_content_history,
  tf_challenges,
  tf_fitnessplan,
  tf_fitnessplanv2,
  tf_pack_details,
  tf_user_experience_form,
  tf_image_upload_confirmation,
  tf_video_content,
  tf_video_guide,
  tf_new_checkout,
  tf_center_selector,
  tf_batch_selector,
  tf_bootcamp,
  lift,
  weight_loss,
  tf_before_after,
  tf_address_selector,
  tf_address_form,
  weight_loss_tab,
  tf_weight_loss_tab,
  tf_recommendation_plan,
  habit_building,
  tf_referral,
  bootcamp_pulse_pre,
  tf_google_review,
  tf_lifestyle_assessment,
}

const Color themeColor = Color.fromRGBO(31, 75, 87, 1.0);
const Color themeTextColor = Color.fromRGBO(37, 110, 117, 1);

bool isFirstLaunch = false;

int iOSLaunchDelayLimitInMilliSeconds = 1000;

enum HabitCardNotificationAction { HABIT_DONE, HABIT_NOT_DONE }

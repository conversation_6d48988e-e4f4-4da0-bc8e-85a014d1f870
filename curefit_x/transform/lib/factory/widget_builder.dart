import 'package:common/blocs/fitness_device/models.dart';
import 'package:common/ui/components/testimonials_widget.dart';
import 'package:common/ui/fitness_device/widgets/fd_collapsed_connection_widget.dart';
import 'package:common/ui/fitness_device/widgets/fd_connection_widget.dart';
import 'package:common/ui/fitness_device/widgets/fd_metrics_progress_widget.dart';
import 'package:common/ui/visibility_detector.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:common/ui/widgets/RTB_widget.dart';
import 'package:common/ui/widgets/RTB_widget_v3.dart';
import 'package:common/ui/widgets/checkout/care_checkout_info_item_widget.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:flutter/material.dart';
import 'package:transform/UI/calendar/widget/calendar_widget.dart';
import 'package:transform/UI/challenges/widgets/challengers_list_widget.dart';
import 'package:transform/UI/challenges/widgets/podium_widget.dart';
import 'package:transform/UI/checkout/widgets/offer_view_widget.dart';
import 'package:transform/UI/checkout/widgets/payment_details_widget.dart';
import 'package:transform/UI/checkout/widgets/product_list_widget.dart';
import 'package:transform/UI/checkout/widgets/share_action_widget_v2.dart';
import 'package:transform/UI/checkout_v2/widgets/card_clickable_widget.dart';
import 'package:transform/UI/checkout_v2/widgets/checkout_actions_widget.dart';
import 'package:transform/UI/checkout_v2/widgets/payment_details_widget_v2.dart';
import 'package:transform/UI/clp/widgets/RTB_widget_v2.dart';
import 'package:transform/UI/clp/widgets/TransformPackTabWidget.dart';
import 'package:transform/UI/clp/widgets/action_card_widget.dart';
import 'package:transform/UI/clp/widgets/challenges_action_widget.dart';
import 'package:transform/UI/clp/widgets/clp_header.dart';
import 'package:transform/UI/clp/widgets/coach_contact_widget.dart';
import 'package:transform/UI/clp/widgets/coach_info_widget.dart';
import 'package:transform/UI/clp/widgets/confirmation_status_widget.dart';
import 'package:transform/UI/clp/widgets/congo_widget.dart';
import 'package:transform/UI/clp/widgets/content_progress_widget.dart';
import 'package:transform/UI/clp/widgets/faq_widget.dart';
import 'package:transform/UI/clp/widgets/fitness_plan_widget.dart';
import 'package:transform/UI/clp/widgets/greetings_header_widget.dart';
import 'package:transform/UI/clp/widgets/habit_card_widget.dart';
import 'package:transform/UI/clp/widgets/habit_date_widget.dart';
import 'package:transform/UI/clp/widgets/instructions_widget.dart';
import 'package:transform/UI/clp/widgets/level_card_widget.dart';
import 'package:transform/UI/clp/widgets/lifestyle_assessment_widget.dart';
import 'package:transform/UI/clp/widgets/lifestyle_re_evaluation_form_widget.dart';
import 'package:transform/UI/clp/widgets/measurement_missing_widget.dart';
import 'package:transform/UI/clp/widgets/metric_input_widget.dart';
import 'package:transform/UI/clp/widgets/onboarding_step_widget.dart';
import 'package:transform/UI/clp/widgets/pack_offerings_widget.dart';
import 'package:transform/UI/clp/widgets/pack_renewal_widget.dart';
import 'package:transform/UI/clp/widgets/product_comparison_widget.dart';
import 'package:transform/UI/clp/widgets/product_info_widget.dart';
import 'package:transform/UI/clp/widgets/product_offering_widget.dart';
import 'package:transform/UI/clp/widgets/product_progress_tab_widget.dart';
import 'package:transform/UI/clp/widgets/progress_cards_widget.dart';
import 'package:transform/UI/clp/widgets/progress_update_widget.dart';
import 'package:transform/UI/clp/widgets/quote_widget.dart';
import 'package:transform/UI/clp/widgets/review_cards_widget.dart';
import 'package:transform/UI/clp/widgets/settings_widget.dart';
import 'package:transform/UI/clp/widgets/sku_card_widget_v3.dart';
import 'package:transform/UI/clp/widgets/steps_widget.dart';
import 'package:transform/UI/clp/widgets/tabbed_container_widget.dart';
import 'package:transform/UI/clp/widgets/timer_card_widget.dart';
import 'package:transform/UI/clp/widgets/todays_read_widget.dart';
import 'package:transform/UI/clp/widgets/transform_header_widget.dart';
import 'package:transform/UI/clp/widgets/transform_image_upload_widget.dart';
import 'package:transform/UI/clp/widgets/transform_meal_plan_widget.dart';
import 'package:transform/UI/clp/widgets/transform_testimonials_v2_widget.dart';
import 'package:transform/UI/clp/widgets/transform_testimonials_widget_v3.dart';
import 'package:transform/UI/clp/widgets/user_metric_chart_widget.dart';
import 'package:transform/UI/clp/widgets/user_metric_chart_widget_v2.dart';
import 'package:transform/UI/clp/widgets/user_metric_input_widget.dart';
import 'package:transform/UI/clp/widgets/weekly_workflow_widget.dart';
import 'package:transform/UI/clp/widgets/workout_list_widget.dart';
import 'package:transform/UI/content/widgets/chapter_section_widget.dart';
import 'package:transform/UI/content_history/widgets/coach_archive_module_widget.dart';
import 'package:transform/UI/health_score/widgets/lifestyle_area_habit_score.dart';
import 'package:transform/UI/health_score/widgets/lifestyle_area_summary_widget.dart';
import 'package:transform/UI/health_score/widgets/lifestyle_form_intro_widget.dart';
import 'package:transform/UI/health_score/widgets/lifestyle_summary.dart';
import 'package:transform/UI/order/widgets/care_tc_confirm_info_widget.dart';
import 'package:transform/UI/order/widgets/coach_preference_widget.dart';
import 'package:transform/UI/order/widgets/order_confirmation_add_ons_widget.dart';
import 'package:transform/UI/order/widgets/order_confirmation_info_widget.dart';
import 'package:transform/UI/order/widgets/order_confirmation_widget.dart';
import 'package:transform/UI/pack_details/widgets/pack_details_widget.dart';
import 'package:transform/UI/pack_purchase/widgets/fitness_pack_browse_widget.dart';
import 'package:transform/UI/pack_purchase/widgets/product_summary_widget.dart';
import 'package:transform/UI/progress/widgets/habits_progress_widget.dart';
import 'package:transform/UI/referral/widgets/banner_card_widget.dart';
import 'package:transform/UI/referral/widgets/level_points_widget.dart';
import 'package:transform/UI/referral/widgets/referral_cards_widget.dart';
import 'package:transform/UI/referral/widgets/share_action_widget.dart';
import 'package:transform/UI/trainer/widgets/coach_detail_cards_widget.dart';
import 'package:transform/UI/trainer/widgets/coach_detail_widget.dart';
import 'package:transform/UI/trainer/widgets/coach_listing_widget.dart';
import 'package:transform/UI/trainer/widgets/coach_recommendation_widget.dart';
import 'package:transform/UI/video_content/widgets/small_thumbnail_list_widget.dart';
import 'package:transform/blocs/calendar/models.dart';
import 'package:transform/blocs/challenges/models.dart';
import 'package:transform/blocs/clp/models.dart';
import 'package:transform/blocs/content/models.dart';
import 'package:transform/blocs/content_history/models.dart';
import 'package:transform/blocs/lifestyle_area_summary/models.dart';
import 'package:transform/blocs/lifestyle_assessment/models.dart';
import 'package:transform/blocs/lift_clp/models.dart';
import 'package:transform/blocs/metric_input/models.dart';
import 'package:transform/blocs/order/models.dart';
import 'package:transform/blocs/pack_details/models.dart';
import 'package:transform/blocs/pack_purchase/models.dart';
import 'package:transform/blocs/referral/models.dart';
import 'package:transform/blocs/trainer/models.dart';
import 'package:transform/blocs/user_progress/models.dart';
import 'package:transform/blocs/weekly_workflow_bloc/models.dart';
import 'package:transform/blocs/weight_loss_clp/models.dart';
import 'package:transform/factory/widget_data.dart';

import '../UI/checkout_v2/widgets/checkout_actions_widget_v2.dart';
import '../UI/clp/widgets/workflow_widget.dart';
import '../UI/mealPlanner/widgets/calorie_breakdown_widget.dart';
import '../UI/mealPlanner/widgets/empty_state_widget.dart';
import '../UI/mealPlanner/widgets/meal_details_widget.dart';
import '../UI/mealPlanner/widgets/meal_details_widget_v2.dart';
import '../blocs/meal_planner/model.dart';

class WidgetBuilder implements IWidgetBuilder {
  @override
  Widget? buildWidget(widgetData, {BuilderInfo? builderInfo}) {
    if (widgetData['widgetType'] == null) return null;
    WidgetTypes? widgetType =
        EnumToString.fromString(WidgetTypes.values, widgetData['widgetType']);
    if (widgetType != null) {
      WidgetInfo widgetInfo = WidgetInfo.fromJson(widgetData, widgetType);

      switch (widgetType) {
        case WidgetTypes.PRODUCT_SUMMARY_WIDGET:
          return ProductSummaryWidgetView(
              ProductSummaryWidgetData.fromJson(widgetData, widgetType));
        case WidgetTypes.FITNESS_PACK_BROWSE_WIDGET:
          return CultPackBrowseWidgetView(CultPackBrowseWidgetData.fromJson(
              widgetData,
              widgetType: widgetType,
              widgetInfo: widgetInfo));
        case WidgetTypes.COACH_LISTING_WIDGET:
          return CoachListWidgetView(
              CoachListingWidgetData.fromJson(widgetData, widgetType));
        case WidgetTypes.COACH_RECOMMENDATION_WIDGET:
          return CoachRecommendationWidgetView(
              CoachRecommendationWidgetData.fromJson(widgetData, widgetType));
        case WidgetTypes.COACH_DETAIL_WIDGET:
          return CoachDetailWidgetView(
              CoachDetailWidgetData.fromJson(widgetData, widgetType));
        case WidgetTypes.TRANSFORM_CONFIRMATION_WIDGET:
          return TransformConfirmationWidgetView(
              widgetData: TransformConfirmationWidgetData.fromJson(
                  widgetData, widgetType));
        case WidgetTypes.HABIT_CARD_WIDGET:
          return HabitCardWidgetView(HabitCardWidgetData.fromJson(widgetData,
              widgetType: widgetType, widgetInfo: widgetInfo));
        case WidgetTypes.CALORIE_BREAKDOWN_WIDGET:
          return CalorieBreakDownWidget(
            widgetData: CalorieBreakdownWidgetInfo.fromJson(
                widgetData, widgetType, widgetInfo),
          );
        case WidgetTypes.LIFESTYLE_RE_EVALUATION_FORM_WIDGET:
          return LifestyleReEvaluationFormWidget(
              LifestyleReEvaluationFormWidgetData.fromJSON(
                  widgetData, widgetType, widgetInfo));
        case WidgetTypes.ACTION_CARD_WIDGET:
          return ActionCardWidgetView(ActionCardWidgetData.fromJson(
              widgetData, widgetType, widgetInfo));
        case WidgetTypes.LEVEL_CARD_WIDGET:
          return LevelCardWidgetView(
              LevelCardWidgetData.fromJson(widgetData, widgetType, widgetInfo));
        case WidgetTypes.USER_METRIC_CHART_WIDGET:
          return UserMetricChartWidgetView(UserMetricChartWidgetData.fromJson(
              widgetData, widgetType, widgetInfo));
        case WidgetTypes.COACH_CONTACT_WIDGET:
          return CoachContactWidgetView(CoachContactWidgetData.fromJson(
              widgetData, widgetType, widgetInfo));
        case WidgetTypes.USER_METRIC_INPUT_WIDGET:
          return UserMetricInputView(UserMetricInputWidgetData.fromJson(
              widgetData, widgetType, widgetInfo));
        case WidgetTypes.TABBED_CONTAINER_WIDGET:
          return TabbedContainerWidget(TabbedContainerWidgetData.fromJson(
              widgetData, widgetType, widgetInfo));
        case WidgetTypes.MEASUREMENT_MISSING_WIDGET:
          return MeasurementMissingWidgetView(
              MeasurementMissingWidgetData.fromJson(widgetData, widgetType));
        case WidgetTypes.PROGRESS_UPDATE_WIDGET:
          return ProgressUpdateWidget(ProgressUpdateWidgetData.fromJson(
              widgetData, widgetType, widgetInfo));
        case WidgetTypes.HABIT_PROGRESS_WIDGET:
          return HabitsProgressWidget(
              HabitsProgressWidgetData.fromJson(widgetData, widgetType));
        case WidgetTypes.MEAL_DETAILS_WIDGET:
          return MealDetailsWidgetView(MealDetailsWidgetData.fromJson(
              widgetData,
              widgetType: widgetType,
              widgetInfo: widgetInfo));

        case WidgetTypes.MEAL_DETAILS_WIDGET_V2:
          return MealDetailsWidgetViewV2(MealDetailsWidgetDataV2.fromJson(
              widgetData,
              widgetType: widgetType,
              widgetInfo: widgetInfo));
        case WidgetTypes.MEAL_PLAN_EMPTY_STATE:
          return EmptyStateWidgetView(MealPlanEmptyStateWidgetData.fromJson(
              widgetData,
              widgetType: widgetType,
              widgetInfo: widgetInfo));
        case WidgetTypes.LIFESTYLE_SCORE_SUMMARY_WIDGET:
          return LifestyleScoreSummaryView(
              LifestyleScoreSummaryWidgetData.fromJson(widgetData, widgetType));
        case WidgetTypes.LIFESTYLE_AREA_HABIT_WIDGET:
          return LifestyleAreaHabitScoreView(
              LifestyleAreaHabitWidgetData.fromJson(
                  widgetData, widgetType, widgetInfo));
        case WidgetTypes.STEPS_WIDGET:
          return StepsWidgetView(
              StepsWidgetData.fromJson(widgetData, widgetType, widgetInfo));
        case WidgetTypes.CHAPTER_SECTION_WIDGET:
          return ChapterSectionWidgetView(ChapterSectionWidgetData.fromJson(
              widgetData, widgetType, widgetInfo));
        case WidgetTypes.ORDER_CONFIRMATION_INFO_WIDGET:
          return OrderConfirmationInfoWidgetView(
              OrderConfirmationInfoData.fromJson(widgetData, widgetType));
        case WidgetTypes.COACH_PREFERENCE_WIDGET:
          return CoachPreferenceWidgetView(
              CoachPreferenceWidgetData.fromJson(widgetData, widgetType));
        case WidgetTypes.CONGO_WIDGET:
          return CongoWidget(CongoWidgetData.fromJson(widgetData, widgetType));
        case WidgetTypes.OFFER_ADDONS_WIDGET:
          if (widgetData["widgetPosition"] != null) {
            return CustomVisibilityDetector(
                widgetCreator: ((isVisible) => OfferWidgetView(
                    OfferWidget.fromJson(widgetData, widgetType))),
                widgetData: widgetData,
                widgetInfo: WidgetInfo(
                    widgetType: widgetType,
                    widgetMetric: WidgetMetric(
                        isSampledWidgetImpressionRequired: false,
                        isWidgetImpressionRequired: true,
                        widgetName: EnumToString.convertToString(
                            WidgetTypes.OFFER_ADDONS_WIDGET),
                        widgetId: EnumToString.convertToString(
                            WidgetTypes.OFFER_ADDONS_WIDGET)),
                    widgetPosition: widgetData["widgetPosition"]),
                widgetType: widgetType);
          } else {
            return OfferWidgetView(
                OfferWidget.fromJson(widgetData, widgetType));
          }
        case WidgetTypes.PAYMENT_DETAILS_WIDGET:
          if (widgetData["widgetPosition"] != null) {
            return CustomVisibilityDetector(
                widgetCreator: ((isVisible) => PaymentDetailWidgetView(
                    PaymentDetailsWidgetData.fromJson(widgetData, widgetType))),
                widgetData: widgetData,
                widgetInfo: WidgetInfo(
                    widgetType: widgetType,
                    widgetMetric: WidgetMetric(
                        isSampledWidgetImpressionRequired: false,
                        isWidgetImpressionRequired: true,
                        widgetName: EnumToString.convertToString(
                            WidgetTypes.PAYMENT_DETAILS_WIDGET),
                        widgetId: EnumToString.convertToString(
                            WidgetTypes.PAYMENT_DETAILS_WIDGET)),
                    widgetPosition: widgetData["widgetPosition"]),
                widgetType: widgetType);
          } else {
            return PaymentDetailWidgetView(
                PaymentDetailsWidgetData.fromJson(widgetData, widgetType));
          }
        case WidgetTypes.PRODUCT_LIST_WIDGET:
          if (widgetData["widgetPosition"] != null) {
            return CustomVisibilityDetector(
                widgetCreator: ((isVisible) => ProductListWidgetView(
                    ProductListWidgetData.fromJson(widgetData, widgetType, widgetInfo))),
                widgetData: widgetData,
                widgetInfo: WidgetInfo(
                    widgetType: widgetType,
                    widgetMetric: WidgetMetric(
                        isSampledWidgetImpressionRequired: false,
                        isWidgetImpressionRequired: true,
                        widgetName: EnumToString.convertToString(
                            WidgetTypes.PRODUCT_LIST_WIDGET),
                        widgetId: EnumToString.convertToString(
                                WidgetTypes.PRODUCT_LIST_WIDGET) +
                            (widgetData["widgetPosition"] != null
                                ? widgetData["widgetPosition"]
                                : "")),
                    widgetPosition: widgetData["widgetPosition"]),
                widgetType: widgetType);
          } else {
            return ProductListWidgetView(
                ProductListWidgetData.fromJson(widgetData, widgetType, widgetInfo));
          }
        case WidgetTypes.INSTRUCTIONS_WIDGET:
          return InstructionsWidgetView(
              InstructionsWidgetData.fromJson(widgetData, widgetType));
        case WidgetTypes.PACK_OFFERINGS_WIDGET:
          return PackOfferingsWidgetView(
              PackOfferingsWidgetData.fromJson(widgetData, widgetType));
        case WidgetTypes.CARE_TC_CONFIRM_INFO_WIDGET:
          return CareTCConfirmInfoWidgetView(
              CareTCConfirmInfoWidgetData.fromJson(widgetData, widgetType));
        case WidgetTypes.FAQ_WIDGET_V3:
          return FAQWidgetView(FAQWidgetData.fromJson(widgetData, widgetType));
        case WidgetTypes.QUOTE_WIDGET:
          return QuoteWidgetView(QuoteWidgetData.fromJson(widgetData,
              widgetType: widgetType, widgetInfo: widgetInfo));
        case WidgetTypes.HABIT_DATE_WIDGET:
          return HabitDateWidgetView(HabitDateWidgetData.fromJson(widgetData,
              widgetType: widgetType, widgetInfo: widgetInfo));
        case WidgetTypes.CLP_HEADER_WIDGET:
          return CLPHeaderWidgetView(
              CLPHeaderWidgetData.fromJson(widgetData, widgetType));
        case WidgetTypes.PACK_RENEWAL_WIDGET:
          return PackRenewalWidgetView(
              packRenewalWidgetData:
                  PackRenewalWidgetData.fromJson(widgetData, widgetType));
        case WidgetTypes.CARE_CHECKOUT_INFO_ITEM_WIDGET:
          return CareCheckoutInfoItemWidgetView(
              widgetData: CareCheckoutInfoItemWidgetData.fromJson(
                  widgetData, widgetType));
        case WidgetTypes.ACTION_CALENDAR_WIDGET:
          return CalendarWidget(
              ActionCalendarData.fromJson(widgetData, widgetType));
        case WidgetTypes.TRANSFORM_TESTIMONIALS_WIDGET:
          return Testimonials(
              widgetData:
                  TestimonialsWidgetData.fromJson(widgetData, widgetInfo));
        case WidgetTypes.LIFESTYLE_FORM_INTRO_WIDGET:
          return LifestyleFormIntroWidget(
              LifestyleFormIntroWidgetData.fromJson(widgetData, widgetType));
        case WidgetTypes.LIFESTYLE_AREA_SUMMARY_WIDGET:
          return LifestyleAreaSummaryWidget(
              LifestyleAreaSummaryWidgetData.fromJson(widgetData, widgetType));
        case WidgetTypes.RTB_WIDGET:
          return ReasonsToBuy(
              ReasonsToBuyWidgetData.fromJson(widgetData, widgetType));
        case WidgetTypes.TRANSFORM_SETTINGS_WIDGET:
          return SettingsWidget(
              SettingsWidgetData.fromJson(widgetData, widgetType, widgetInfo));
        case WidgetTypes.CONTENT_TO_READ_WIDGET:
          return ContentForTodaysReadWidget(
              data: ContentTodayReadModel.fromJson(
                  widgetType, widgetData, widgetInfo));
        case WidgetTypes.CONTENT_PROGRESS_WIDGET:
          return ContentProgressWidget(
              data: ContentProgressModel.fromJson(
                  widgetType, widgetData, widgetInfo));
        case WidgetTypes.COACH_ARCHIVE_MODULE_WIDGET:
          return CoachArchiveModuleWidget(
              item: CoachArchiveModuleModel.fromJson(
                  widgetType, widgetData, widgetInfo));
        case WidgetTypes.PRODUCT_INFO_WIDGET:
          return ProductInfoWidget(
              widgetData: ProductInfoWidgetData.fromJson(
                  widgetType, widgetData, widgetInfo));
        case WidgetTypes.RTB_WIDGET_V2:
          return ReasonsToBuyWidgetV2(
            reasonsToBuyWidgetData: ReasonsToBuyWidgetData.fromJson(
                widgetData, widgetType,
                widgetInfo: widgetInfo),
          );
        case WidgetTypes.ORDER_CONFIRMATION_ADD_ONS_WIDGET:
          return OrderConfirmationAddOnsView(
              OrderConfirmationAddOnsData.fromJson(widgetData, widgetType));
        case WidgetTypes.FITNESS_DEVICE_CONNECTION_WIDGET:
          return FDConnectionWidget(
            widgetData: FitnessDeviceConnectionWidgetData.fromJson(
                widgetType, widgetData, widgetInfo),
          );
        case WidgetTypes.FITNESS_DEVICE_COLLAPSED_CONNECTION_WIDGET:
          return FDCollapsedConnectionWidget(
            widgetData: FitnessDeviceCollapsedConnectionWidgetData.fromJson(
                widgetType, widgetData, widgetInfo),
          );
        case WidgetTypes.FITNESS_DEVICE_METRICS_PROGRESS_WIDGET:
          return FDMetricsProgressWidget(
            widgetData: FitnessDeviceMetricsData.fromJson(
                widgetType, widgetData,
                widgetInfo: widgetInfo),
          );
        case WidgetTypes.PODIUM_WIDGET:
          return PodiumWidget(
            widgetData: PodiumWidgetData.fromJson(widgetType, widgetData,
                widgetInfo: widgetInfo),
          );
        case WidgetTypes.CHALLENGERS_LIST_WIDGET:
          return ChallengersListWidget(
              widgetData: ChallengersListWidgetData.fromJson(
                  widgetType, widgetData,
                  widgetInfo: widgetInfo));
        case WidgetTypes.CHALLENGES_ACTION_WIDGET:
          return ChallengesActionWidget(
            widgetData: ChallengesActionWidgetData.fromJson(
                widgetType, widgetData, widgetInfo),
          );
        case WidgetTypes.COACH_INFO_WIDGET:
          return CoachInfoWidget(
            widgetData: CoachInfoWidgetData.fromJson(
                widgetType, widgetData, widgetInfo),
          );
        case WidgetTypes.COACH_DETAIL_CARDS_WIDGET:
          return CoachDetailCardsWidget(
            widgetData: CoachDetailCardsWidgetData.fromJson(
                widgetData, widgetType,
                widgetInfo: widgetInfo),
          );
        case WidgetTypes.PRODUCT_OFFERING_WIDGET:
          return ProductOfferingWidget(
            widgetData: ProductOfferingWidgetData.fromJson(
                widgetType, widgetData, widgetInfo),
          );
        case WidgetTypes.RTB_WIDGET_V3:
          return ReasonsToBuyWidgetV3(
            reasonsToBuyWidgetData: ReasonsToBuyWidgetData.fromJson(
                widgetData, widgetType,
                widgetInfo: widgetInfo),
          );
        case WidgetTypes.PACK_DETAILS_WIDGET:
          return PackDetailsWidget(
            widgetData: PackDetailsWidgetData.fromJson(
                widgetType, widgetData, widgetInfo),
          );
        case WidgetTypes.TRANSFORM_TESTIMONIALS_WIDGET_V2:
          return TransformTestimonialsV2Widget(
            widgetData: TestimonialsWidgetData.fromJson(widgetData, widgetInfo),
          );
        case WidgetTypes.SMALL_THUMBNAIL_LIST_WIDGET:
          return SmallThumbnailListWidget(
            widgetData: SmallThumbnailListWidgetData.fromJson(
                widgetType, widgetData,
                widgetInfo: widgetInfo),
          );
        case WidgetTypes.TRANSFORM_PAYMENT_DETAILS_WIDGET:
          return PaymentDetailWidgetV2(
              PaymentDetailsWidgetData.fromJson(widgetData, widgetType));
        case WidgetTypes.CARD_CLICKABLE_WIDGET:
          return CardClickableWidget(
              widgetData:
                  CardClickableWidgetData.fromJson(widgetData, widgetType));
        case WidgetTypes.CHECKOUT_ACTIONS_WIDGET:
          return CheckoutActionsWidget(
              widgetData:
                  CheckoutActionsWidgetData.fromJson(widgetData, widgetType));
        case WidgetTypes.CHECKOUT_ACTIONS_WIDGET_V2:
          return CheckoutActionsWidgetV2(
              widgetData:
                  CheckoutActionsWidgetData.fromJson(widgetData, widgetType));
        case WidgetTypes.TRANSFORM_HEADER_WIDGET:
          return TransformHeaderWidget(
              widgetData: TransformHeaderWidgetData.fromJson(
                  widgetType, widgetData, widgetInfo));
        case WidgetTypes.METRIC_INPUT_WIDGET:
          return MetricInputWidget(
              widgetData: MetricInputWidgetData.fromJson(
                  widgetType, widgetData, widgetInfo));
        case WidgetTypes.FITNESS_PLAN_WIDGET:
          return FitnessPlanWidget(
              widgetData: FitnessPlanWidgetData.fromJson(
                  widgetType, widgetData, widgetInfo));
        case WidgetTypes.WORKOUT_LIST_WIDGET:
          return WorkoutListWidget(
              widgetData: WorkoutListWidgetData.fromJson(
                  widgetType, widgetData, widgetInfo));
        case WidgetTypes.TRANSFORM_MEAL_PLAN_WIDGET:
          return TransformMealPlanWidget(
              widgetData: TransformMealPlanWidgetData.fromJson(
                  widgetData, widgetType, widgetInfo));
        case WidgetTypes.SKU_CARD_WIDGET_V3:
          return SKUCardWidgetV3(
              widgetData: SKUCardWidgetV3Data.fromJson(
                  widgetType, widgetData, widgetInfo));
        case WidgetTypes.TRANSFORM_IMAGE_UPLOAD_WIDGET:
          return TransformImageUploadWidget(
              widgetData: TransformImageUploadWidgetData.fromJson(
                  widgetData, widgetType, widgetInfo));
        case WidgetTypes.PRODUCT_COMPARISON_WIDGET:
          return ProductComparisonWidget(
              widgetData: ProductComparisonWidgetData.fromJson(
                  widgetType, widgetData, widgetInfo));
        case WidgetTypes.TRANSFORM_PACK_TAB_WIDGET:
          return TransformPackTabWidget(
              widgetData: TransformPackTabWidgetData.fromJson(
                  widgetType, widgetData, widgetInfo));
        case WidgetTypes.TRANSFORM_TESTIMONIALS_WIDGET_V3:
          return TransformTestimonialsWidgetV3(
              widgetData: TransformTestimonialsWidgetV3Data.fromJson(
                  widgetType, widgetData, widgetInfo));
        case WidgetTypes.PROGRESS_CARDS_WIDGET:
          return ProgressCardsWidget(
            widgetData: ProgressCardsWidgetData.fromJson(
                widgetType, widgetData, widgetInfo),
          );
        case WidgetTypes.USER_METRIC_CHART_WIDGET_V2:
          return UserMetricChartWidgetV2(
            widgetData: UserMetricChartWidgetV2Data.fromJson(
                widgetData, widgetType, widgetInfo),
          );
        case WidgetTypes.LEVEL_POINTS_WIDGET:
          return LevelPointsWidget(
            widgetData: LevelPointsWidgetData.fromJson(
                widgetType, widgetData, widgetInfo),
          );
        case WidgetTypes.SHARE_ACTION_WIDGET:
          return ShareActionWidget(
            widgetData: ShareActionWidgetData.fromJson(
                widgetType, widgetData, widgetInfo),
          );
        case WidgetTypes.SHARE_ACTION_WIDGET_V2:
          return ShareActionWidgetV2(
            widgetData: ShareActionWidgetDataV2.fromJson(
                widgetType, widgetData, widgetInfo),
          );
        case WidgetTypes.REFERRAL_CARDS_WIDGET:
          return ReferralCardsWidget(
            widgetData: ReferralCardsWidgetData.fromJson(
                widgetType, widgetData, widgetInfo),
          );
        case WidgetTypes.BANNER_CARD_WIDGET:
          return BannerCardWidget(
            widgetData: BannerCardWidgetData.fromJson(
                widgetType, widgetData, widgetInfo),
          );
        case WidgetTypes.CONFIRMATION_STATUS_WIDGET:
          return ConfirmationStatusWidget(
            widgetData: ConfirmationStatusWidgetData.fromJson(
                widgetData, widgetType, widgetInfo),
          );
        case WidgetTypes.TIMER_CARD_WIDGET:
          return TimerCardWidget(
            widgetData: TimerCardWidgetData.fromJson(
                widgetType, widgetData, widgetInfo),
          );
        case WidgetTypes.PRODUCT_PROGRESS_TAB_WIDGET:
          return ProductProgressTabWidget(
            widgetData: ProductProgressTabWidgetData.fromJson(
                widgetType, widgetData, widgetInfo),
          );
        case WidgetTypes.REVIEW_CARDS_WIDGET:
          return ReviewCardsWidget(
            widgetData: ReviewCardsWidgetData.fromJson(
                widgetType, widgetData, widgetInfo),
          );
        case WidgetTypes.ONBOARDING_STEP_WIDGET:
          return OnboardingStepWidgetView(
              onboardingStepWidgetData: OnboardingStepWidgetData.fromJson(
                  widgetData, widgetType, widgetInfo));
        case WidgetTypes.ONBOARDING_STEP_V2_WIDGET:
          return OnboardingStepWidgetView(
              onboardingStepWidgetData: OnboardingStepWidgetData.fromJson(
                  widgetData, widgetType, widgetInfo));
        case WidgetTypes.WORKFLOW_WIDGET:
          return WorkflowWidgetView(
              workflowWidgetData: WorkflowWidgetData.fromJson(
                  widgetData, widgetType, widgetInfo));
        case WidgetTypes.WEEKLY_WORKFLOW_WIDGET:
          return WeeklyWorkflowWidget(
            widgetData: WeeklyWorkflowWidgetData.fromJson(
                widgetType, widgetData, widgetInfo),
          );
        case WidgetTypes.GREETINGS_HEADER_WIDGET:
          return GreetingsHeaderWidget(
            widgetData: GreetingsHeaderWidgetData.fromJson(
                widgetType, widgetData, widgetInfo),
          );
        case WidgetTypes.LIFESTYLE_ASSESSMENT_WIDGET:
          return LifestyleAssessmentWidget(
            widgetData: LifestyleAssessmentWidgetData.fromJson(
                widgetType, widgetData, widgetInfo),
          );
      }
    }
    return null;
  }
}

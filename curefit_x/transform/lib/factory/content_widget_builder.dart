import 'package:common/ui/widget_builder.dart';
import 'package:common/video/video_player_registry.dart';
import 'package:flutter/material.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:common/ui/storyview/widgets/html_widget.dart';
import 'package:common/ui/storyview/widgets/mark_down_widget.dart';
import 'package:common/ui/storyview/widgets/media_widget.dart';
import 'package:transform/UI/content/widgets/loader_widget.dart';
import 'package:transform/UI/content/widgets/question_widget.dart';
import 'package:common/ui/storyview/widgets/text_widget.dart';

enum WidgetTypes {
  TEXT,
  MEDIA,
  QUESTION,
  HTML,
  LOADER,
  TEMPLATE_HTML,
  MARKDOWN
}

class ContentWidgetBuilder implements IWidgetBuilder {
  @override
  Widget buildWidget(payload, {BuilderInfo? builderInfo}) {
    if (payload['type'] == null) return Container();
    WidgetTypes? widgetType =
        EnumToString.fromString(WidgetTypes.values, payload['type']);

    if (widgetType == null) return Container();

    dynamic data = payload['data'];
    if (data == null) return Container();

    switch (widgetType) {
      case WidgetTypes.TEXT:
        dynamic data = payload['data'];
        return TextWidget(
            textWidgetData: TextWidgetData(
          topPadding: data['topPadding'] != null
              ? double.tryParse(data['topPadding'].toString())
              : 0,
          bottomPadding: data['bottomPadding'] != null
              ? double.tryParse(data['bottomPadding'].toString())
              : 0,
          horizontalPadding: data['horizontalPadding'] != null
              ? double.tryParse(data['horizontalPadding'].toString())
              : null,
          value: data['value'],
          textStyle: data['textStyle'],
          color: data['color'],
          centerAlign: data['centerAlign'],
        ));
      case WidgetTypes.MEDIA:
        return MediaWidget(
            mediaWidgetData: MediaWidgetData(
                topPadding: data['topPadding'] != null
                    ? double.tryParse(data['topPadding'].toString())
                    : null,
                bottomPadding: data['bottomPadding'] != null
                    ? double.tryParse(data['bottomPadding'].toString())
                    : null,
                mediaUrl: data['url'],
                type: data['type'],
                height: data['height']?.toDouble(),
                isFullScreen: data['isFullScreen']));
      case WidgetTypes.QUESTION:
        dynamic question = data['question'];
        if (question == null) return Container();
        return QuestionWidget(
            questionWidgetData: QuestionWidgetData(
                questionTextColor: data['questionTextColor'],
                optionTextColor: data['optionTextColor'],
                questionTextSize: data['questionTextSize'] != null
                    ? double.parse(data['questionTextSize'])
                    : 18,
                optionTextSize: data['optionTextSize'] != null
                    ? double.parse(data['optionTextSize'])
                    : 14,
                pageId: data['pageId'],
                type: EnumToString.fromString(
                    QuestionType.values, question['type']),
                options: question['options'],
                text: question['text']));
      case WidgetTypes.TEMPLATE_HTML:
        return HtmlWidget(
            htmlWidgetData:
                HtmlWidgetData.fromTemplateReplacementJson(payload));
      case WidgetTypes.HTML:
        return HtmlWidget(
            htmlWidgetData: HtmlWidgetData(
          htmlText: data['value'],
          topPadding: data['topPadding'] != null
              ? double.tryParse(data['topPadding'].toString())
              : null,
          bottomPadding: data['bottomPadding'] != null
              ? double.tryParse(data['bottomPadding'].toString())
              : null,
          horizontalPadding: data['horizontalPadding'] != null
              ? double.tryParse(data['horizontalPadding'].toString())
              : null,
        ));
      case WidgetTypes.LOADER:
        return LoaderWidget(loaderWidgetData: LoaderWidgetData.fromJson(data));
      case WidgetTypes.MARKDOWN:
        return MarkdownWidget(
            HtmlWidgetData.fromTemplateReplacementJson(payload));
      default:
        return Container();
    }
  }
}

import 'package:common/ui/widget_builder.dart';
import 'package:flutter/material.dart';

class ProductSummaryWidgetData implements IWidgetData {
  ProductSummaryWidgetData(
      this.widgetType, this.title, this.subtitle, this.image)
      : super();

  String? title;
  String? subtitle;
  String? image;

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  static ProductSummaryWidgetData fromJson(payload, WidgetTypes widgetType) {
    return ProductSummaryWidgetData(
        widgetType, payload['title'], payload['subTitle'], payload['image']);
  }
}

class ProductPricingWidgetData implements IWidgetData {
  ProductPricingWidgetData(this.widgetType, this.header, this.sections)
      : super();

  Header header;
  List<Section>? sections;
  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  static ProductPricingWidgetData fromJson(payload, WidgetTypes widgetType) {
    return ProductPricingWidgetData(
      widgetType,
      Header(payload['header']['title'],
          subtitle: payload['header']['subtitle']),
      payload['sections']
          .map<Section>((dynamic section) => Section(
                section['type'],
                section['value']
                    .map<Pack>((dynamic pack) => Pack(
                        pack['title'],
                        pack['subTitle'],
                        pack['priceMeta'],
                        Price(
                            pack['price']['currency'],
                            pack['price']['listingPrice'],
                            pack['price']['mrp'],
                            pack['price']['showPriceCut']),
                        selected: pack['selected'],
                        productId: pack["productId"]))
                    .toList(),
              ))
          .toList(),
    );
  }
}

class Header {
  Header(this.title, {this.subtitle});

  String? title;
  String? subtitle;
}

class Section {
  Section(this.type, this.packs);

  String? type;
  List<Pack>? packs;
}

class Pack {
  Pack(this.title, this.subtitle, this.priceMeta, this.price,
      {this.selected, this.productId});
  bool? selected;

  String? title;
  String? subtitle;
  String? priceMeta;
  Price price;
  String? productId;
}

class Price {
  Price(this.currency, this.listingPrice, this.mrp, this.showPriceCut);

  static String displayPrice(String value, String? currency) {
    return '${currency == 'INR' ? '₹' : currency} ${value}';
  }

  String? currency;
  int? listingPrice;
  int? mrp;
  bool? showPriceCut;
}

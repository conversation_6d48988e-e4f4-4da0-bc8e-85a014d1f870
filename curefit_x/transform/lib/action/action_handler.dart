import 'package:common/action/action_handler.dart' as ActionHandler;
import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/blocs/checkout_v2/checkout_v2_bloc.dart';
import 'package:common/blocs/fitness_device/models.dart';
import 'package:common/constants/action_constants.dart';
import 'package:common/network/client.dart';
import 'package:common/network/client_repository.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/fitness_device/fitness_device_util.dart';
import 'package:common/ui/fitness_device/modal/fd_permission_modal.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:health/health.dart';
import 'package:transform/UI/checkout_v2/screens/center_details_modal.dart';
import 'package:transform/UI/checkout_v2/screens/start_date_selection_modal.dart';
import 'package:transform/UI/clp/screens/cancel_alert_modal.dart';
import 'package:transform/UI/checkout_v2/screens/slot_selection_modal.dart';
import 'package:transform/UI/clp/screens/coach_actions_modal.dart';
import 'package:transform/UI/clp/screens/exercise_updation_modal.dart';
import 'package:transform/UI/clp/screens/image_upload_instructions_modal.dart';
import 'package:transform/UI/clp/screens/info_action_modal.dart';
import 'package:transform/UI/clp/screens/product_info_modal.dart';
import 'package:transform/UI/clp/screens/recording_consent_modal.dart';
import 'package:transform/UI/clp/screens/stories_modal.dart';
import 'package:transform/UI/clp/screens/weight_logging_modal.dart';
import 'package:transform/UI/clp/widgets/coach_info_widget.dart';
import 'package:transform/UI/waitlist/models/waitlist_data.dart';
import 'package:transform/UI/waitlist/screens/waitlist_modal_screen.dart';
import 'package:common/blocs/booking/booking_bloc.dart';
import 'package:common/blocs/booking/events.dart';
import 'package:transform/blocs/center_selector/center_selector_bloc.dart';
import 'package:transform/blocs/center_selector/models.dart';
import 'package:transform/blocs/clp/clp_bloc.dart';
import 'package:transform/blocs/clp/events.dart';
import 'package:transform/blocs/clp/models.dart';
import 'package:transform/blocs/habit/events.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:transform/blocs/lift_clp/models.dart';

class TransformActionHandler extends ActionHandler.IActionHandler {
  late CoachClientRepository repository;
  final GlobalKey<NavigatorState> navigatorKey;

  TransformActionHandler(NetworkClient client, {required this.navigatorKey}) {
    this.repository = new CoachClientRepository(client);
  }

  @override
  bool handleAction(ActionHandler.Action action, ActionBloc actionBloc) {
    BuildContext? context = this.navigatorKey.currentContext;

    if (context == null) {
      return false;
    }
    switch (action.type) {
      case ActionTypes.UPDATE_HABIT_CARD:
        {
          Bloc? bloc = action.bloc;
          if (bloc != null) {
            bloc.add(UpdateHabitEvent(
                habitId: action.meta!['userHabitId'],
                status: action.meta!['status']));
          }
          return true;
        }
      case ActionTypes.UPDATE_USER_METRICS:
        this
            .repository
            .postPageData("v2/transform/updateUserMetric", action.meta!);
        return true;
      case ActionTypes.UPDATE_METRIC_INPUT:
        this
            .repository
            .postPageData("v2/transform/updateMetricInput", action.meta!);
        return true;
      case ActionTypes.UPDATE_EXERCISE_METRIC:
        this
            .repository
            .postPageData("v2/transform/updateExerciseMetric", action.meta!);
        return true;
      case ActionTypes.CANCEL_TC:
        {
          if (action.meta?['tcBookingId'] == null ||
              action.meta?['productId'] == null) {
            return false;
          }
          showDialog(
              context: context,
              builder: (dialogContext) => AlertDialog(
                    title: Text(
                      "Are you sure you want to ${action.title?.toLowerCase() ?? ""}?",
                      style:
                          TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                    ),
                    actions: [
                      TextButton(
                          onPressed: () {
                            BookingBloc bookingBloc =
                                BlocProvider.of<BookingBloc>(context);
                            bookingBloc.add(CancelBookingEvent(
                                bookingId:
                                    action.meta!['tcBookingId'].toString(),
                                productId: action.meta?['productId']));
                            Navigator.of(dialogContext, rootNavigator: true)
                                .pop();
                          },
                          child: Text("Yes")),
                      TextButton(
                          onPressed: () {
                            Navigator.of(dialogContext, rootNavigator: true)
                                .pop();
                          },
                          child: Text("No"))
                    ],
                  ));
          return true;
        }
      case ActionTypes.JOIN_CALL:
      case ActionTypes.TC_JOIN_CALL:
        {
          actionBloc.add(CloseApplicationEvent());
          actionBloc.add(UnsupportedActionEvent(
              ActionHandler.Action(
                  url: action.url,
                  type: ActionTypes.TC_JOIN_CALL,
                  canRedirect: false),
              false));
          return true;
        }
      case ActionTypes.SHOW_TRANSFORM_WAITLIST_MODAL:
        if (action.meta?['waitlistInfo'] != null) {
          showWaitlistModalScreen(
              context, WaitlistInfo.fromJson(action.meta!['waitlistInfo']));
          return true;
        }
        break;
      case ActionTypes.SCROLL_TO_CATEGORY:
        final coachCLPBloc =
            BlocProvider.of<CoachCLPBloc>(navigatorKey.currentState!.context);
        double? scrollFactor;
        if (action.meta?['scrollFactor'] != null) {
          scrollFactor = double.parse(action.meta!['scrollFactor']);
          coachCLPBloc.add(ScrollCLPBottomEvent(scrollFactor: scrollFactor));
        } else {
          coachCLPBloc.add(ScrollCLPBottomEvent());
        }
        return true;
      case ActionTypes.SHOW_RECORDING_CONSENT_MODAL:
        ActionHandler.Action? joinCallAction;
        if (action.meta?['joinAction'] != null) {
          joinCallAction =
              ActionHandler.Action.fromJson(action.meta?['joinAction']);
          if (joinCallAction.type == null &&
              joinCallAction.meta?['action'] != null) {
            joinCallAction =
                ActionHandler.Action.fromJson(joinCallAction.meta!['action']);
          }
        }
        showRecordingConsentModal(
          context: navigatorKey.currentContext!,
          title: action.meta?['title'],
          description: action.meta?['description'],
          image: action.meta?['image'],
          firstButtonTitle: action.meta?['firstButtonTitle'],
          secondButtonTitle: action.meta?['secondButtonTitle'],
          joinCallAction: joinCallAction,
          patientId: action.meta?['patientId'],
          appointmentId: action.meta?['appointmentId'],
        );
        return true;
      case ActionTypes.SHOW_WEIGHT_LOGGING_MODAL:
        showWeightLoggingModal(
          context: navigatorKey.currentContext!,
          modalInfo: action.meta != null ? action.meta : null,
        );
        return true;
      case ActionTypes.SHOW_FITNESS_DEVICE_PERMISSION_MODAL:
        if (action.meta?['modalData'] != null) {
          FDModalData fdModalData =
              FDModalData.fromJson(action.meta?['modalData']);
          showFdPermissionModalScreen(
              context: navigatorKey.currentContext!, fdModalData: fdModalData);
        }
        return true;
      case ActionTypes.POST_FITNESS_DEVICE_METRICS:
          List<HealthDataType> metrics = [];
          List<HealthDataAccess> permissions = [];
          if (action.meta?["metrics"] != null) {
            metrics = action.meta?['metrics'].map<HealthDataType>((metric) {
              return EnumToString.fromString(HealthDataType.values, metric) ??
                  HealthDataType.STEPS;
            }).toList();
          }
          if (action.meta?["metricPermissions"] != null) {
            permissions = action.meta?['metricPermissions'].map<HealthDataAccess>((permission) {
              return EnumToString.fromString(HealthDataAccess.values, permission) ??
                  HealthDataAccess.READ;
            }).toList();
          }
          int numberOfDays = action.meta?['numberOfDays'] ?? 4;
          fetchData(navigatorKey.currentContext!,
              metrics: metrics, numberOfDays: numberOfDays, permissions: permissions);
        return true;
      case ActionTypes.GET_FITNESS_DEVICE_METRICS:
        getFitnessData(navigatorKey.currentContext!);
        return true;
      case ActionTypes.SHOW_COACH_ACTIONS_MODAL:
        showCoachActionsModal(
          context: navigatorKey.currentContext!,
          modalInfo: action.meta != null ? action.meta : null,
        );
        return true;
      case ActionTypes.UPLOAD_USER_IMAGE:
        showUploadImageModal(
          context: navigatorKey.currentContext!,
          modalInfo: action.meta != null ? action.meta : null,
        );
        return true;
      case ActionTypes.SHOW_CANCEL_ALERT_MODAL:
        final CanvasTheme? themeType = action.meta?['themeType'] != null
            ? EnumToString.fromString(
                CanvasTheme.values, action.meta?['themeType'])
            : null;
        showCancelAlertModal(
          context: navigatorKey.currentContext!,
          title: action.meta?['title'],
          description: action.meta?['description'],
          image: action.meta?['image'],
          firstButtonTitle: action.meta?['firstButtonTitle'],
          secondButtonTitle: action.meta?['secondButtonTitle'],
          bookingId: (action.meta?['tcBookingId'] ?? "").toString(),
          orderId: (action.meta?['orderId'] ?? "").toString(),
          productId: action.meta?['productId'],
          code: action.meta?['code'],
          themeType: themeType,
        );
        return true;
      case ActionTypes.SHOW_TF_SLOT_SELECTION_MODAL:
        CenterSelectorScreenData? modalData =
            BlocProvider.of<CenterSelectorBloc>(
                    navigatorKey.currentState!.context)
                .screenData;
        showSlotSelectionModal(
          context: navigatorKey.currentContext!,
          modalData: modalData,
        );
        return true;
      case ActionTypes.SHOW_INFO_ACTION_MODAL:
        List<ActionHandler.Action>? actionsList;
        if (action.meta?['actionsList'] != null) {
          actionsList =
              action.meta?['actionsList'].map<ActionHandler.Action>((action) {
            return ActionHandler.Action.fromJson(action);
          }).toList();
        }
        final CanvasTheme? themeType = action.meta?['themeType'] != null
            ? EnumToString.fromString(
                CanvasTheme.values, action.meta?['themeType'])
            : null;
        showInfoActionModal(
          context: navigatorKey.currentContext!,
          title: action.meta?['title'],
          subtitle: action.meta?['subtitle'],
          description: action.meta?['description'],
          imageUrl: action.meta?['imageUrl'],
          lottieUrl: action.meta?['lottieUrl'],
          actionsList: actionsList,
          themeType: themeType,
        );
        return true;
      case ActionTypes.SHOW_CENTER_DETAILS_MODAL:
        final CanvasTheme? themeType = action.meta?['themeType'] != null
            ? EnumToString.fromString(
                CanvasTheme.values, action.meta?['themeType'])
            : null;
        List<ActionHandler.Action>? actionsList;
        if (action.meta?['actionsList'] != null) {
          actionsList =
              action.meta?['actionsList'].map<ActionHandler.Action>((action) {
            return ActionHandler.Action.fromJson(action);
          }).toList();
        }
        showCenterDetailsModal(
          context: navigatorKey.currentContext!,
          title: action.meta?['title'],
          subtitle: action.meta?['subtitle'],
          imageUrl: action.meta?['imageUrl'],
          facilities: action.meta?['facilities'] != null
              ? action.meta!['facilities'].map<String>((facility) {
                  return facility.toString();
                }).toList()
              : null,
          iconList: action.meta?['iconList'] != null
              ? action.meta!['iconList'].map<String>((icon) {
                  return icon.toString();
                }).toList()
              : null,
          enableDayNightTheme: action.meta?['enableDayNightTheme'] ?? true,
          action: action.meta?['action'] != null
              ? ActionHandler.Action.fromJson(action.meta?['action'])
              : null,
          redirectionAction: action.meta?['redirectionAction'] != null
              ? ActionHandler.Action.fromJson(action.meta?['redirectionAction'])
              : null,
          coachInfoCardData: action.meta?['coachInfoCard'] != null
              ? CoachInfoCardData.fromJson(action.meta?['coachInfoCard'])
              : null,
          actionsList: actionsList,
          themeType: themeType,
        );
        return true;
      case ActionTypes.SHOW_STORIES_MODAL:
        List<ActionHandler.Action>? actionsList;
        if (action.meta?['actionsList'] != null) {
          actionsList =
              action.meta?['actionsList'].map<ActionHandler.Action>((action) {
            return ActionHandler.Action.fromJson(action);
          }).toList();
        }
        final CanvasTheme? themeType = action.meta?['themeType'] != null
            ? EnumToString.fromString(
                CanvasTheme.values, action.meta?['themeType'])
            : null;
        showStoriesModal(
          context: navigatorKey.currentContext!,
          title: action.meta?['title'],
          subtitle: action.meta?['subtitle'],
          imageUrl: action.meta?['imageUrl'],
          description: action.meta?['description'],
          storyText: action.meta?['storyText'],
          isCenterAligned: action.meta?['isCenterAligned'],
          quotationText: action.meta?['quotationText'],
          actionsList: actionsList,
          enableDayNightTheme: action.meta?['enableDayNightTheme'] ?? true,
          themeType: themeType,
        );
        return true;
      case ActionTypes.SHOW_EXERCISE_UPDATION_MODAL:
        if (action.meta?['workoutModalInfo'] != null) {
          WorkoutModalData workoutModalData =
              WorkoutModalData.fromJson(action.meta?['workoutModalInfo']);
          exerciseUpdationModal(
            context: navigatorKey.currentContext!,
            modalData: workoutModalData,
          );
          return true;
        } else {
          return false;
        }
      case ActionTypes.SHOW_TF_START_DATE_SELECTION_MODAL:
        if (action.meta != null) {
          showStartDateSelectionModal(
            context: navigatorKey.currentContext!,
            startDateEpoch: action.meta?['startDateEpoch'],
            endDateEpoch: action.meta?['endDateEpoch'],
            errorMessage: action.meta?['errorMessage'],
          );
          return true;
        } else {
          return false;
        }
      case ActionTypes.SHOW_PRODUCT_INFO_MODAL:
        if (action.meta != null) {
          ProductInfoModalData productInfoModalData =
              ProductInfoModalData.fromJson(
                  action.meta?['productInfoModalData']);
          final CanvasTheme? themeType = action.meta?['themeType'] != null
              ? EnumToString.fromString(
                  CanvasTheme.values, action.meta?['themeType'])
              : null;
          showProductInfoModal(
            context: navigatorKey.currentContext!,
            productInfoModalData: productInfoModalData,
            themeType: themeType,
          );
          return true;
        } else {
          return false;
        }
      case ActionTypes.SAVE_PROGRAM_SLOT_ID:
        if (action.meta != null) {
          CheckoutV2Bloc checkoutBloc =
              BlocProvider.of<CheckoutV2Bloc>(context);
          checkoutBloc.selectedSlotId = action.meta?['slotId'].toString();
          checkoutBloc.selectedCenterId = action.meta?['centerId'];
          checkoutBloc.selectedBatchId = action.meta?['batchId'];
          RepositoryProvider.of<AnalyticsRepository>(context)
              .logButtonClickEvent(extraInfo: {
            "trigger": "SAVE_BATCH",
            "selected": true,
            "slotId": action.meta?['slotId'],
            "centerId": action.meta?['centerId'],
            "batchId": action.meta?['batchId'],
            "actionType": "SAVE_PROGRAM_SLOT_ID",
          });
          return true;
        }
    }
    return false;
  }
}

extension StringExtension on String {
  String capitalize() {
    return "${this[0].toUpperCase()}${this.substring(1)}";
  }
}

import 'package:collection/collection.dart';
import 'package:common/action/action_handler.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/header/card_header.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:common/util/action_util.dart';
import 'package:flutter/material.dart' hide Action;

class SmallThumbnailListWidgetData implements IWidgetData {
  @override
  WidgetInfo? widgetInfo;
  @override
  WidgetTypes widgetType;

  final List<SmallThumbnailCardData> items;
  final WidgetHeaderData? headerData;

  SmallThumbnailListWidgetData(
    this.widgetType, {
    this.items = const [],
    this.headerData,
    this.widgetInfo,
  });

  factory SmallThumbnailListWidgetData.fromJson(
      WidgetTypes widgetType, dynamic payload,
      {WidgetInfo? widgetInfo}) {
    return SmallThumbnailListWidgetData(
      widgetType,
      items: payload['items'] != null
          ? payload['items']
              .map<SmallThumbnailCardData>(
                  (item) => SmallThumbnailCardData.fromJson(item))
              .toList()
          : null,
      headerData: payload['header'] != null
          ? WidgetHeaderData.fromJson(payload['header'])
          : null,
      widgetInfo: widgetInfo,
    );
  }
}

class SmallThumbnailCardData {
  final String? imageUrl;
  final String? title;
  final String? subtitle;
  final bool showPlayButton;
  final Action? action;

  SmallThumbnailCardData(
      {this.imageUrl,
      this.title,
      this.subtitle,
      this.showPlayButton = true,
      this.action});

  factory SmallThumbnailCardData.fromJson(dynamic payload) {
    return SmallThumbnailCardData(
      imageUrl: payload['imageUrl'],
      title: payload['title'],
      subtitle: payload['subtitle'],
      action:
          payload['action'] != null ? Action.fromJson(payload['action']) : null,
      showPlayButton: payload['showPlayButton'] ?? true,
    );
  }
}

class SmallThumbnailListWidget extends StatelessWidget {
  final SmallThumbnailListWidgetData widgetData;

  const SmallThumbnailListWidget({required this.widgetData});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: Spacings.x4),
      child: Column(children: [
        if (widgetData.headerData != null)
          Padding(
            padding: const EdgeInsets.only(bottom: Spacings.x5),
            child: WidgetHeader(cardHeaderData: widgetData.headerData!),
          ),
        ...widgetData.items
            .mapIndexed<Widget>(
              (index, item) => Padding(
                padding: EdgeInsets.only(
                    bottom:
                        index == widgetData.items.length - 1 ? 0 : Spacings.x5),
                child: SmallThumbnailCard(cardData: item),
              ),
            )
            .toList(),
      ]),
    );
  }
}

class SmallThumbnailCard extends StatelessWidget {
  final SmallThumbnailCardData cardData;

  const SmallThumbnailCard({required this.cardData});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        if (cardData.action != null) {
          clickActionWithAnalytics(cardData.action!, context, null, {});
        }
      },
      child: Container(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (cardData.imageUrl != null)
              Padding(
                padding: const EdgeInsets.only(right: Spacings.x3),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(10),
                  child: CFNetworkImage(
                    fit: BoxFit.cover,
                    imageUrl: getImageUrl(context,
                        imagePath: cardData.imageUrl ?? ""),
                    height: 70,
                    width: 70,
                  ),
                ),
              ),
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (cardData.title != null)
                    Padding(
                      padding: const EdgeInsets.only(bottom: Spacings.x1),
                      child: Text(
                        cardData.title ?? "",
                        style: AuroraTheme.of(context)
                            .textStyle(TypescaleValues.P1),
                      ),
                    ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      if (cardData.showPlayButton)
                        Padding(
                          padding: const EdgeInsets.only(right: Spacings.x1),
                          child: Icon(
                            Icons.play_arrow_rounded,
                            color: Colors.white60,
                            size: 20,
                          ),
                        ),
                      if (cardData.subtitle != null)
                        Text(
                          cardData.subtitle ?? "",
                          style: AuroraTheme.of(context).textStyle(
                              TypescaleValues.P8,
                              color: Colors.white60),
                        ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/ui/alert_view.dart';
import 'package:common/ui/page/basic_page_container.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit/ui_toolkit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:common/action/action_handler.dart' as ActionHandler;
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:transform/blocs/video_content/events.dart';
import 'package:transform/blocs/video_content/models.dart';
import 'package:transform/blocs/video_content/states.dart';
import 'package:transform/blocs/video_content/video_content_bloc.dart';

class VideoContentScreenArguments {
  String? pillarId;
  String? subCategoryCode;

  VideoContentScreenArguments(Map<String, dynamic> payload) {
    this.pillarId = payload["pillarId"];
    this.subCategoryCode =
    payload["subCategoryCode"] != null ? payload["subCategoryCode"] : "";
  }
}

class VideoContentScreen extends StatefulWidget {
  const VideoContentScreen({Key? key}) : super(key: key);

  @override
  State<VideoContentScreen> createState() => _VideoContentScreenState();
}

class _VideoContentScreenState extends State<VideoContentScreen> {
  late VideoContentScreenData videoContentScreenData;

  VideoContentScreenArguments? videoContentScreenArguments() {
    final ActionHandler.ScreenArguments? args = ModalRoute.of(context)!
        .settings
        .arguments as ActionHandler.ScreenArguments?;
    if (args != null) {
      return VideoContentScreenArguments(args.params);
    }
    return null;
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      VideoContentScreenArguments? arguments = videoContentScreenArguments();
      String pillarId = arguments?.pillarId ?? "";
      String subCategoryCode = arguments?.subCategoryCode ?? "";
      final videoContentBloc = BlocProvider.of<VideoContentViewBloc>(context);
      videoContentBloc.add(LoadVideoContentViewEvent(pillarId: pillarId, subCategoryCode: subCategoryCode));
    });
  }

  CanvasTheme draftTheme() {
    TimeOfDay now = TimeOfDay.now();
    return now.hour > 4 && now.hour < 19 ? CanvasTheme.TEAL : CanvasTheme.NIGHT;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      body: BlocListener<VideoContentViewBloc, VideoContentViewState>(
        listener: (context, state) {
          if (state is VideoContentViewFailedState) {
            showErrorAlert(
              context: context,
              title: "Something went wrong",
              onClose: () {
                Navigator.pop(context);
              },
            );
          }
        },
        child: BlocBuilder<VideoContentViewBloc, VideoContentViewState>(
          builder: (context, state) {
            VideoContentScreenData? screenData;
            if (state is VideoContentViewLoadedState) {
              screenData = state.videoContentScreenData;
            }
            return BasicPageContainer(
              canvasTheme: screenData?.themeType ?? draftTheme(),
              onBackPressed: () {
                if (Navigator.canPop(context)) {
                  Navigator.pop(context);
                } else if (!Navigator.canPop(context)) {
                  ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
                  actionBloc.add(CloseApplicationEvent(shouldReset: false));
                }
              },
              title: screenData?.title ?? "",
              titleBarRightActions: screenData?.action != null
                  ? [
                      InkWell(
                        onTap: () {
                          ActionBloc actionBloc =
                              BlocProvider.of<ActionBloc>(context);
                          PerformActionEvent event =
                              PerformActionEvent(screenData!.action!);
                          actionBloc.add(event);
                        },
                        child: Text(
                          screenData?.action!.title ?? "",
                          style: AuroraTheme.of(context)
                              .textStyle(TypescaleValues.P6),
                        ),
                      ),
                    ]
                  : null,
              showLoader: state is VideoContentViewLoadingState,
              itemBuilder: (context, widget, index) {
                return AnimationConfiguration.staggeredList(
                    position: index, child: applySlideFade(widget, context));
              },
              widgetData: screenData?.widgets ?? [],
            );
          },
        ),
      ),
    );
  }
}

import 'package:common/util/color.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class LifestyleIndicatorLegend extends StatelessWidget {
  String hexColor;

  LifestyleIndicatorLegend(this.hexColor);

  @override
  Widget build(BuildContext context) {
      return Container(
          width: 20,
          height: 20,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: HexColor.fromHex(hexColor),
          )
      );
  }
}

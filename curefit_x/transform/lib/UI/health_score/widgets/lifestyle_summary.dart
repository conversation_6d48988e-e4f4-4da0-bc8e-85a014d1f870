import 'package:common/ui/molecules/bottom_tray.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/util/color.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:transform/UI/health_score/widgets/lifestyle_area_habit_score.dart';
import 'package:transform/UI/health_score/widgets/lifestyle_indicator_legend.dart';
import 'package:transform/UI/progress/components/lifestyle_water_animation.dart';
import 'package:transform/UI/progress/components/water_view.dart';
import 'package:transform/blocs/user_progress/models.dart';
import 'package:transform/util/utils.dart';

class LifestyleScoreSummaryView extends StatefulWidget {
  LifestyleScoreSummaryWidgetData summaryWidgetData;

  LifestyleScoreSummaryView(this.summaryWidgetData);

  @override
  State<StatefulWidget> createState() => _LifestyleScoreSummaryViewState();
}

class _LifestyleScoreSummaryViewState extends State<LifestyleScoreSummaryView>
    with TickerProviderStateMixin {
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );
    _animationController.forward();
  }

  @override
  Widget build(BuildContext context) {
    AuroraThemeData theme = AuroraTheme.of(context);
    LifestyleScoreSummaryWidgetData summaryWidgetData =
        this.widget.summaryWidgetData;

    _onTap() {
      return showBottomTray(
          context: context,
          child: Column(children: [
            Padding(
                padding: const EdgeInsets.only(
                    top: 10, left: 20, right: 0, bottom: 10),
                child: Column(
                  children: [
                    Container(
                      width: 54,
                      decoration: BoxDecoration(
                          border:
                              Border.all(color: Colors.white.withOpacity(0.3))),
                    ),
                    Container(
                      padding: EdgeInsets.symmetric(vertical: 40),
                      child: Text(summaryWidgetData.scoreDescriptionView.title,
                          style: theme.textStyle(TypescaleValues.H2)),
                    ),
                    ...widget.summaryWidgetData.scoreDescriptionView
                        .scoreDescriptions
                        .map((scoreDesc) => Container(
                              child: Container(
                                padding: EdgeInsets.symmetric(vertical: 20),
                                child: Row(
                                  children: [
                                    LifestyleIndicatorLegend(
                                        getIndicatorColorFromLifestyleScore(
                                            scoreDesc.minScore)),
                                    Container(
                                        margin: EdgeInsets.only(
                                            left: 20, right: 20),
                                        width:
                                            MediaQuery.of(context).size.width -
                                                80,
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Text(scoreDesc.title,
                                                style: theme.textStyle(
                                                    TypescaleValues.H4)),
                                            Text(scoreDesc.subTitle,
                                                maxLines: 2,
                                                style: theme.textStyle(
                                                    TypescaleValues.P8,
                                                    color: Colors.white
                                                        .withOpacity(0.5)))
                                          ],
                                        ))
                                  ],
                                ),
                              ),
                            )),
                  ],
                ))
          ]));
    }

    return Container(
        color: Colors.transparent,
        padding: EdgeInsets.symmetric(horizontal: Spacings.x4),
        child: Column(
            // crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Container(
                padding: EdgeInsets.symmetric(vertical: Spacings.x6),
                child: Stack(
                  children: [
                    Container(
                      width: double.infinity,
                      alignment: Alignment.center,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(summaryWidgetData.title,
                              style: theme.textStyle(TypescaleValues.P1,
                                  color: HexColor.fromHex(
                                      summaryWidgetData.titleStyle["fontColor"]))),
                          Text(summaryWidgetData.overallScore.toString(),
                              style: theme.textStyle(TypescaleValues.H9,
                                  color: HexColor.fromHex(getIndicatorColorFromLifestyleScore(summaryWidgetData.overallScore)))),
                        ],
                      ),
                    ),
                    Positioned(
                      child: GestureDetector(
                        onTap: _onTap,
                        child: Icon(Icons.info_outline_rounded,
                            color: Colors.white),
                      ),
                      right: 0,
                      bottom: 0,
                    ),
                  ],
                ),
              ),
              Column(
                children: [
                  if (widget.summaryWidgetData.waterLevelAnimationView != null)
                    LifestyleWaterAnimation(
                      mainScreenAnimationController: _animationController,
                      configData:
                          widget.summaryWidgetData.waterLevelAnimationView,
                    ),
                  // buildWidgets()
                ],
              ),
              Container(
                padding: EdgeInsets.only(
                    left: 20,
                    right: 20,
                    bottom: scaleHeight(context, Spacings.x12),
                    top: scaleHeight(context, Spacings.x8)),
                child: Text(summaryWidgetData.motivationText,
                    style: theme.textStyle(TypescaleValues.P5),
                    textAlign: TextAlign.center),
              )
            ]));
  }
}

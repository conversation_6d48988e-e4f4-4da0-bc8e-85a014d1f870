import 'dart:ui';

import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:flutter/material.dart';
import 'package:transform/blocs/lifestyle_area_summary/models.dart';
import 'package:lottie/lottie.dart';

class LifeStyleRow extends StatelessWidget {
  final List<LifestyleImageData>?
      rowData; // The data about the row images and opacity
  final double horizontalPadding;
  final double verticalPadding;
  final double imageSize;

  const LifeStyleRow(
      {required this.rowData,
      this.imageSize = 65,
      this.horizontalPadding = 20,
      this.verticalPadding = 10});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(
          horizontal: horizontalPadding, vertical: verticalPadding),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: List.generate(rowData!.length, (int index) {
          return Opacity(
            opacity: rowData![index].opacity ?? 1,
            // Add the opacity within the list
            child: Column(
              children: [
                InkWell(
                  child: Container(
                    margin: const EdgeInsets.symmetric(vertical: 10),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: rowData![index].lottieUrl != null
                          ? Colors.transparent
                          : Color.fromRGBO(255, 255, 255, 0.1),
                    ),
                    height: imageSize,
                    width: imageSize,
                    padding: EdgeInsets.all(rowData![index].lottieUrl != null
                        ? 0
                        : imageSize / 3.5),
                    child: rowData![index].lottieUrl != null
                        ? Lottie.network(
                            getMediaUrl(rowData![index].lottieUrl ?? ""),
                            repeat: false,
                          )
                        : CFNetworkImage(
                            imageUrl: getImageUrl(context,
                                imagePath: rowData![index].imageUrl ?? ""),
                            errorWidget: (context, url, error) =>
                                Icon(Icons.error),
                          ),
                  ),
                  onTap: () {},
                ),
                Text(
                  rowData![index].title ?? "",
                  style: AuroraTheme.of(context).textStyle(TypescaleValues.P8),
                ),
              ],
            ),
          );
        }),
      ),
    );
  }
}

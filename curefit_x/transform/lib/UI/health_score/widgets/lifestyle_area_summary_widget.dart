import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/util/color.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:lottie/lottie.dart';
import 'dart:math' as math;
import 'package:transform/UI/health_score/widgets/lifestyle_row.dart';
import 'package:transform/blocs/lifestyle_area_summary/models.dart';
import 'package:transform/util/utils.dart';

class LifestyleAreaSummaryWidget extends StatelessWidget {
  final LifestyleAreaSummaryWidgetData lifestyleAreaSummaryWidgetData;

  const LifestyleAreaSummaryWidget(this.lifestyleAreaSummaryWidgetData,
      {Key? key})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Stack(
        children: [
          SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(
                  height: scale(context, Spacings.x16),
                ),
                Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: Color.fromRGBO(32, 77, 89, 0.06),
                  ),
                  height: 72,
                  width: 72,
                  child: Stack(
                    children: [
                      Center(
                        child: Container(
                          height: 30,
                          width: 30,
                          child: CFNetworkImage(
                            imageUrl: getMediaUrl(
                              lifestyleAreaSummaryWidgetData.imageUrl ?? "",
                            ),
                            errorWidget: (context, url, error) =>
                                Icon(Icons.error),
                          ),
                        ),
                      ),
                      Center(
                        child: AspectRatio(
                          aspectRatio: 1.0,
                          child: CustomPaint(
                            painter: RingPainter(
                              progress: lifestyleAreaSummaryWidgetData.score!
                                      .toDouble() /
                                  100,
                              color: HexColor.fromHex(getIndicatorColorFromLifestyleScore(lifestyleAreaSummaryWidgetData.score!)),
                              backgroundColor: Colors.transparent,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 10),
                  child: Text(
                    lifestyleAreaSummaryWidgetData.title ?? "",
                    style: AuroraTheme.of(context).textStyle(
                      TypescaleValues.H1,
                      color: Colors.white,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(bottom: 5),
                  child: Text(
                    lifestyleAreaSummaryWidgetData.score!.toString(),
                    style: AuroraTheme.of(context).textStyle(
                      TypescaleValues.H7,
                      color: Colors.white,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                Container(
                  padding:
                      const EdgeInsets.only(bottom: 30, left: 50, right: 50),
                  margin: const EdgeInsets.symmetric(horizontal: 10),
                  child: Text(
                    lifestyleAreaSummaryWidgetData.description ?? "",
                    style: AuroraTheme.of(context).textStyle(TypescaleValues.P8,
                      color: Colors.white,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                Container(
                  margin: const EdgeInsets.symmetric(vertical: 20),
                  height: scale(context, 140),
                  child: Lottie.network(
                    getMediaUrl(lifestyleAreaSummaryWidgetData.lottieUrl!),
                  ),
                ),
                if (lifestyleAreaSummaryWidgetData.subtitle != null)
                  Padding(
                    padding: const EdgeInsets.only(bottom: 5),
                    child: Text(
                      lifestyleAreaSummaryWidgetData.subtitle ?? "",
                      style:
                          AuroraTheme.of(context).textStyle(TypescaleValues.P8),
                      textAlign: TextAlign.center,
                    ),
                  ),
                LifeStyleRow(
                  rowData:
                  lifestyleAreaSummaryWidgetData.lifestyleImageDataList,
                  verticalPadding: 1,
                ),
                SizedBox(
                  height: 100,
                ),
              ],
            ),
          ),
          if (lifestyleAreaSummaryWidgetData.action != null)
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 20,vertical: 20),
                child: PrimaryButton(
                  () {
                    if (lifestyleAreaSummaryWidgetData.action!.url != null) {
                      ActionBloc actionBloc =
                          BlocProvider.of<ActionBloc>(context);
                      PerformActionEvent event = PerformActionEvent(
                          lifestyleAreaSummaryWidgetData.action!);
                      actionBloc.add(event);
                    }
                  },
                  lifestyleAreaSummaryWidgetData.action!.title ?? "",
                ),
              ),
            ),
        ],
      ),
    );
  }
}

class RingPainter extends CustomPainter {
  RingPainter({
    required this.progress,
    required this.backgroundColor,
    required this.color,
  });

  // a value between 0 and 1
  final double progress;

  final Color backgroundColor;

  final Color color;

  @override
  void paint(Canvas canvas, Size size) {
    final double strokeWidth = size.width / 15.0;
    final center = Offset(size.width / 2, size.height / 2);
    final double radius = (size.width - strokeWidth) / 2;
    final double sweepAngle = 2 * math.pi * progress;
    final backgroundPaint = Paint()
      ..isAntiAlias = true
      ..strokeWidth = strokeWidth
      ..color = backgroundColor
      ..style = PaintingStyle.stroke;

    canvas.drawCircle(center, radius, backgroundPaint);

    final foregroundPaint = Paint()
      ..isAntiAlias = true
      ..strokeWidth = strokeWidth
      ..color = color
      ..style = PaintingStyle.stroke;

    canvas.drawArc(
      Rect.fromCircle(center: center, radius: radius),
      math.pi / 2,
      sweepAngle,
      false,
      foregroundPaint,
    );
  }

  @override
  bool shouldRepaint(covariant RingPainter oldDelegate) => true;
}

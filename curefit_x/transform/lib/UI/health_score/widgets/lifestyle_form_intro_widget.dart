import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:lottie/lottie.dart';
import 'package:transform/UI/health_score/widgets/lifestyle_row.dart';
import 'package:transform/blocs/lifestyle_area_summary/models.dart';

class LifestyleFormIntroWidget extends StatelessWidget {
  final LifestyleFormIntroWidgetData lifestyleFormIntroWidgetData;

  const LifestyleFormIntroWidget(this.lifestyleFormIntroWidgetData, {Key? key})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Stack(
        children: [
          SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(
                  height: scale(context, 120),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 20),
                  child: Text(
                    lifestyleFormIntroWidgetData.title ?? "",
                    style:
                        AuroraTheme.of(context).textStyle(TypescaleValues.H1),
                    textAlign: TextAlign.center,
                  ),
                ),
                lifestyleFormIntroWidgetData.lottieUrl != null
                    ? Container(
                        margin: const EdgeInsets.symmetric(vertical: 20),
                        width: 150,
                        height: 150,
                        decoration: BoxDecoration(
                          color: Colors.white,
                          shape: BoxShape.circle,
                        ),
                        child: Lottie.network(
                          getMediaUrl(lifestyleFormIntroWidgetData.lottieUrl!),
                        ),
                      )
                    : Container(),
                Padding(
                  padding:
                      const EdgeInsets.symmetric(vertical: 10, horizontal: 40),
                  child: Text(
                    lifestyleFormIntroWidgetData.description ?? "",
                    style:
                        AuroraTheme.of(context).textStyle(TypescaleValues.P8),
                    textAlign: TextAlign.center,
                  ),
                ),
                LifeStyleRow(
                  rowData: lifestyleFormIntroWidgetData.lifestyleImageDataList,
                  verticalPadding: 40,
                ),
                SizedBox(
                  height: 100,
                ),
              ],
            ),
          ),
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20,vertical: 20),
              child: PrimaryButton(
                () {
                  if (lifestyleFormIntroWidgetData.action != null) {
                    ActionBloc actionBloc =
                        BlocProvider.of<ActionBloc>(context);
                    PerformActionEvent event = PerformActionEvent(
                        lifestyleFormIntroWidgetData.action!);
                    actionBloc.add(event);
                  }
                },
                lifestyleFormIntroWidgetData.action!.title ?? "",
              ),
            ),
          ),
        ],
      ),
    );
  }
}

import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/util/color.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/UI/health_score/widgets/lifestyle_indicator_legend.dart';
import 'package:transform/UI/health_score/widgets/lifestyle_area_summary_widget.dart';
import 'package:transform/blocs/user_progress/models.dart';
import 'package:transform/util/utils.dart';

class LifestyleAreaHabitScoreView extends StatefulWidget {
  @override
  State<StatefulWidget> createState() => _LifestyleAreaHabitScoreViewState();

  LifestyleAreaHabitWidgetData areaHabitWidgetData;

  LifestyleAreaHabitScoreView(this.areaHabitWidgetData);
}

class _LifestyleAreaHabitScoreViewState
    extends State<LifestyleAreaHabitScoreView> {
  int activeAreaIndex = 0;

  changeActiveAreaIndex(index) {
    setState(() {
      activeAreaIndex = index;
    });
  }

  @override
  void initState() {
    super.initState();
    activeAreaIndex = this.widget.areaHabitWidgetData.initialSelectedAreaIndex;
  }

  @override
  Widget build(BuildContext context) {
    AuroraThemeData theme = AuroraTheme.of(context);
    List<AreaHabit> areaHabits = this.widget.areaHabitWidgetData.areaHabits;
    AreaHabit activeArea = areaHabits[activeAreaIndex];
    return Container(
      padding: EdgeInsets.symmetric(horizontal: Spacings.x3),
      child: Column(
        children: [
          Stack(children: [
            Positioned.fill(
                bottom: 26,
                child: Container(
                    decoration: BoxDecoration(
                        border: Border(
                  bottom: BorderSide(color: Colors.white, width: 0.1),
                )))),
            Container(
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  ...areaHabits.map(
                    (areaHabit) => areaLevelScores(theme, areaHabit, () {
                      RepositoryProvider.of<AnalyticsRepository>(context)
                          .logWidgetClick(extraInfo: {
                        "Legend Selected": areaHabit.areaName,
                        "Score": areaHabit.score,
                      }, widgetInfo: widget.areaHabitWidgetData.widgetInfo);
                      changeActiveAreaIndex(areaHabits.indexOf(areaHabit));
                    }, activeAreaIndex == areaHabits.indexOf(areaHabit)),
                  )
                ],
              ),
            )
          ]),
          Container(
            margin: EdgeInsets.only(
                bottom: scaleHeight(context, Spacings.x7),
                top: scaleHeight(context, Spacings.x3)),
            child: Row(
              children: [
                Container(
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    color: HexColor.fromHex(
                        getIndicatorColorFromLifestyleScore(activeArea.score)),
                    shape: BoxShape.circle,
                  ),
                  width: 50,
                  height: 50,
                  child: CFNetworkImage(
                    color:
                        getIndicatorColorFromLifestyleScore(activeArea.score) ==
                                "#FFFFFF"
                            ? Colors.black
                            : Colors.white,
                    width: 30,
                    height: 30,
                    imageUrl: getMediaUrl(
                        "image/transform/lifestyle/${activeArea.areaName.toLowerCase()}_bl.png"),
                    errorWidget: (context, url, error) => Icon(Icons.error),
                  ),
                ),
                Container(
                  width: MediaQuery.of(context).size.width - 100,
                  padding: EdgeInsets.only(left: Spacings.x4),
                  child: Text(activeArea.areaMessage,
                      style: theme.textStyle(TypescaleValues.P4)),
                )
              ],
            ),
          ),
          ...activeArea.habitScores
              .map((habitScore) => habitLevelScores(theme, habitScore)),
          Padding(
              padding: EdgeInsets.symmetric(vertical: Spacings.x4),
              child: PrimaryButton(() {
                RepositoryProvider.of<AnalyticsRepository>(context)
                    .logWidgetClick(extraInfo: {
                  "Area": areaHabits[activeAreaIndex].areaName,
                  "Action": "Score Reassessment",
                  "Current Score": areaHabits[activeAreaIndex].score,
                }, widgetInfo: widget.areaHabitWidgetData.widgetInfo);
                ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
                PerformActionEvent event =
                    PerformActionEvent(activeArea.action);
                actionBloc.add(event);
              }, activeArea.action.title ?? ""))
        ],
      ),
    );
  }

  Widget areaLevelScores(AuroraThemeData theme, AreaHabit areaHabit,
      VoidCallback onTap, bool isActiveArea) {
    return GestureDetector(
      onTap: onTap,
      child: Stack(
        children: [
          Container(
            child: Column(children: [
              Container(
                  width: scale(context, Spacings.x10),
                  height: scale(context, Spacings.x10),
                  child: Stack(
                    children: [
                      Container(
                        decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            shape: BoxShape.circle),
                        alignment: Alignment.center,
                        child: Text(areaHabit.score.toString(),
                            style: theme.textStyle(TypescaleValues.H4)),
                      ),
                      AspectRatio(
                        aspectRatio: 1.0,
                        child: CustomPaint(
                          painter: RingPainter(
                            progress: areaHabit.score / 100,
                            color: HexColor.fromHex(
                                getIndicatorColorFromLifestyleScore(
                                    areaHabit.score)),
                            backgroundColor: Colors.white.withOpacity(0.1),
                          ),
                        ),
                      )
                    ],
                  )),
              Container(
                padding: EdgeInsets.symmetric(horizontal: Spacings.x1),
                decoration: isActiveArea
                    ? BoxDecoration(
                        border: Border(
                          bottom: BorderSide(color: Colors.white, width: 2),
                        ),
                      )
                    : BoxDecoration(),
                child: Padding(
                    padding: EdgeInsets.only(top: 10, bottom: 15),
                    child: Text(areaHabit.areaName.toUpperCase(),
                        style: theme.textStyle(
                            isActiveArea
                                ? TypescaleValues.P6
                                : TypescaleValues.P8,
                            color: Colors.white))),
              ),
            ]),
            margin: EdgeInsets.only(bottom: 25),
          ),
          isActiveArea
              ? Positioned.fill(
                  child: Align(
                      alignment: Alignment.bottomCenter,
                      child: Padding(
                          padding: EdgeInsets.only(bottom: 5),
                          child: Icon(Icons.arrow_drop_down_sharp,
                              color: Colors.white))))
              : Container()
        ],
      ),
    );
  }

  Widget habitLevelScores(AuroraThemeData theme, HabitScore habitScore) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: Spacings.x3),
      child: Row(
        children: [
          LifestyleIndicatorLegend(
              getIndicatorColorFromLifestyleScore(habitScore.score)),
          Container(
            padding: EdgeInsets.only(left: Spacings.x4),
            child: Text(habitScore.habitName,
                style: theme.textStyle(TypescaleValues.P5)),
          )
        ],
      ),
    );
  }
}

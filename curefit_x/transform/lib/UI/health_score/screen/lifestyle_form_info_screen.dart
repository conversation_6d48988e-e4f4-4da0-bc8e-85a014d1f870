import 'package:common/action/action_handler.dart' as ActionHandler;
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/blocs/navigation/navigation_bloc.dart';
import 'package:common/blocs/navigation/state.dart';
import 'package:common/constants/action_constants.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/widget_factory.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/lifestyle_area_summary/events.dart';
import 'package:transform/blocs/lifestyle_area_summary/lifestyle_area_summary_bloc.dart';
import 'package:transform/blocs/lifestyle_area_summary/models.dart';
import 'package:transform/blocs/lifestyle_area_summary/state.dart';
import 'package:transform/constants/constants.dart';
import 'package:navigation_history_observer/navigation_history_observer.dart';

class FormInfoScreenArguments {
  String? areaId;
  String? subCategoryCode;
  String? flowType;

  FormInfoScreenArguments(Map<String, dynamic> payload) {
    this.areaId = payload["areaId"];
    this.flowType = payload["flowType"];
    this.subCategoryCode =
        payload["subCategoryCode"] != null ? payload["subCategoryCode"] : "";
  }
}

class LifestyleFormInfoScreen extends StatefulWidget {
  const LifestyleFormInfoScreen({Key? key}) : super(key: key);

  @override
  State<LifestyleFormInfoScreen> createState() =>
      _LifestyleFormInfoScreenState();
}

class _LifestyleFormInfoScreenState extends State<LifestyleFormInfoScreen> {
  late LifestyleFormInfoScreenData lifestyleFormInfoScreenData;

  FormInfoScreenArguments? formInfoScreenArguments() {
    final ActionHandler.ScreenArguments? args = ModalRoute.of(context)!
        .settings
        .arguments as ActionHandler.ScreenArguments?;
    if (args != null) {
      return FormInfoScreenArguments(args.params);
    }
    return null;
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      FormInfoScreenArguments? arguments = formInfoScreenArguments();
      String? areaId = arguments?.areaId;
      String? subCategoryCode = arguments?.subCategoryCode;
      String? flowType = arguments?.flowType;
      final lifestyleAreaSummaryBloc =
          BlocProvider.of<LifestyleAreaSummaryBloc>(context);
      lifestyleAreaSummaryBloc.add(LifestyleAreaSummaryLoadEvent(
          areaId: areaId, subCategoryCode: subCategoryCode, flowType: flowType));
    });
  }

  CanvasTheme draftTheme() {
    TimeOfDay now = TimeOfDay.now();
    return now.hour > 4 && now.hour < 19 ? CanvasTheme.TEAL : CanvasTheme.NIGHT;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        leading: Container(),
        backgroundColor: Colors.transparent,
        iconTheme: IconThemeData(color: Colors.white),
        flexibleSpace: Align(
          alignment: Alignment.topRight,
          child: Padding(
            padding: EdgeInsets.fromLTRB(
                0,
                AuroraTheme.of(context).embeddedSafeArea.top +
                    MediaQuery.of(context).padding.top,
                5,
                0),
            child: CloseButton(
              color: Colors.white,
              onPressed: () {
                ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
                actionBloc.add(PerformActionEvent(
                    ActionHandler.Action.fromAction(
                        lifestyleFormInfoScreenData.onCloseAction, {},
                        canPop: true)));
              },
            ),
          ),
        ),
      ),
      body: LayoutBuilder(
        builder: (context, constraints) {
          AuroraThemeData themeData = AuroraTheme.of(context);
          CanvasTheme canvasTheme = draftTheme();
          themeData.canvasTheme = canvasTheme;
          if (constraints.maxWidth > 0) {
            return Container(
              color: Colors.black,
              child: Stack(
                children: [
                  Aurora(
                    size: constraints.biggest,
                    context: context,
                  ),
                  BlocBuilder<LifestyleAreaSummaryBloc,
                      LifestyleAreaSummaryState>(
                    builder: (context, state) {
                      if (state is LifestyleAreaSummaryLoadingState) {
                        return FancyLoadingIndicator();
                      } else if (state is LifestyleAreaSummaryLoadedState) {
                        WidgetFactory widgetFactory =
                            RepositoryProvider.of<WidgetFactory>(context);
                        lifestyleFormInfoScreenData =
                            state.lifestyleFormInfoScreenData;
                        if (lifestyleFormInfoScreenData.themeType != null) {
                          AuroraTheme.of(context).canvasTheme =
                              lifestyleFormInfoScreenData.themeType!;
                        }
                        return Stack(
                          children: [
                            Container(
                              decoration: BoxDecoration(
                                image: DecorationImage(
                                  image: Image.network(
                                    getImageUrl(context,
                                        imagePath: lifestyleFormInfoScreenData
                                            .backgroundImageUrl),
                                    loadingBuilder: (BuildContext context,
                                        Widget child,
                                        ImageChunkEvent? loadingProgress) {
                                      if (loadingProgress == null) return child;
                                      return Center(
                                        child: CircularProgressIndicator(),
                                      );
                                    },
                                  ).image,
                                  fit: BoxFit.fill,
                                ),
                              ),
                            ),
                            Container(
                              color: Colors.transparent,
                              child: widgetFactory.createWidget(
                                  lifestyleFormInfoScreenData.widget),
                            ),
                          ],
                        );
                      }
                      return Container();
                    },
                  ),
                ],
              ),
            );
          }
          return Container();
        },
      ),
    );
  }
}

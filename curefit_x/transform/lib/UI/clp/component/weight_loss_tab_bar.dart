import 'dart:io';

import 'package:collection/collection.dart';
import 'package:common/action/action_handler.dart';
import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/font/cf_icons.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/atoms/tertiary_button.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/scroll/scroll_app_bar.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/util/color.dart';
import 'package:flutter/material.dart' hide Action;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/weight_loss_tab/models.dart';
import 'package:scroll_to_index/scroll_to_index.dart';
import 'package:common/action/action_handler.dart' as action_handler;

class WeightLossTabBar extends StatefulWidget {
  final bool inTabMode;
  final String? title;
  final List<PageData>? pageDataList;
  AutoScrollController? scrollController;
  TabController? tabController;

  WeightLossTabBar(
      {this.title,
      this.pageDataList,
      this.inTabMode = false,
      this.tabController,
      this.scrollController,
      Key? key})
      : super(key: key);

  @override
  State<WeightLossTabBar> createState() => _WeightLossTabBarState();
}

class _WeightLossTabBarState extends State<WeightLossTabBar> {
  int selectedTab = 0;
  String _tabColor = "#FFFFFF";

  void colorChangeListener() {
    if (widget.scrollController != null &&
        widget.tabController != null &&
        widget.pageDataList != null) {
      setState(() {
        if (widget.scrollController!.positions.last.pixels > 250) {
          _tabColor = "#FFFFFF";
        } else if (widget.scrollController!.positions.last.pixels < 250 &&
            widget.tabController != null) {
          _tabColor = widget.pageDataList!
              .elementAt(widget.tabController!.index)
              .hexColor;
        }
      });
    }
  }

  tabBarChanged() {
    if (widget.tabController != null && widget.tabController!.indexIsChanging) {
      setState(() {
        selectedTab = widget.tabController?.index ?? 0;
        _tabColor = widget.pageDataList!.elementAt(selectedTab).hexColor;
        RepositoryProvider.of<AnalyticsRepository>(context)
            .logButtonClickEvent(extraInfo: {
          "selectedTab": widget.pageDataList?.elementAt(selectedTab).title,
          "previousTab": widget.pageDataList
              ?.elementAt(widget.tabController!.previousIndex)
              .title,
          "action": "tabChanged",
        });
      });
    }
  }

  double toolbarHeight(int size) {
    return Platform.isAndroid
        ? (size == 1)
            ? 80
            : 120
        : (size == 1)
            ? 60
            : 98;
  }

  @override
  void initState() {
    if (widget.pageDataList != null && widget.tabController != null) {
      _tabColor =
          widget.pageDataList!.elementAt(widget.tabController!.index).hexColor;
    }
    widget.scrollController?.addListener(colorChangeListener);
    widget.tabController?.addListener(tabBarChanged);
    super.initState();
  }

  @override
  void dispose() {
    widget.scrollController?.removeListener(colorChangeListener);
    widget.tabController?.removeListener(tabBarChanged);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    AuroraThemeData themeData = AuroraTheme.of(context);
    return ScrollAppBar(
      centerTitle: false,
      scrollControllerPosition: selectedTab,
      primary: Platform.isIOS,
      title: Column(
        children: [
          SizedBox(
            height: Platform.isAndroid
                ? (MediaQuery.of(context).padding.top +
                    AuroraTheme.of(context).embeddedSafeArea.top)
                : 0,
          ),
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              GestureDetector(
                onTap: () {
                  if (Navigator.canPop(context)) {
                    Navigator.pop(context);
                  } else if (!Navigator.canPop(context)) {
                    ActionBloc actionBloc =
                        BlocProvider.of<ActionBloc>(context);
                    actionBloc.add(CloseApplicationEvent(shouldReset: false));
                  }
                },
                child: Padding(
                  padding: const EdgeInsets.only(right: 15),
                  child: Row(children: [
                    if (!widget.inTabMode)
                      Padding(
                        padding: const EdgeInsets.only(
                          right: Spacings.x4,
                        ),
                        child: Icon(
                          CFIcons.chevron_left,
                          color: HexColor.fromHex(_tabColor),
                          size: 20,
                          semanticLabel: "chevron_left",
                        ),
                      ),
                    Text(
                      widget.title ?? "",
                      style: AuroraTheme.of(context).textStyle(
                          TypescaleValues.H3,
                          color: HexColor.fromHex(_tabColor)),
                    ),
                  ]),
                ),
              ),
            ],
          ),
        ],
      ),
      actions: [
        if (widget.tabController != null &&
            widget.pageDataList != null &&
            widget.pageDataList!
                    .elementAt(widget.tabController!.index)
                    .coachContactView !=
                null)
          getCoachHeaderIconsState(
              context,
              widget.pageDataList!
                  .elementAt(widget.tabController!.index)
                  .coachContactView!,
              HexColor.fromHex(_tabColor)),
        if (widget.tabController != null &&
            widget.pageDataList != null &&
            widget.pageDataList!
                    .elementAt(widget.tabController!.index)
                    .rightBarAction !=
                null)
          rightBarActionButton(
              context,
              widget.pageDataList!
                  .elementAt(widget.tabController!.index)
                  .rightBarAction)
      ],
      automaticallyImplyLeading: false,
      toolbarHeight: toolbarHeight(widget.pageDataList!.length),
      controller: widget.scrollController!,
      materialType: MaterialType.transparency,
      bottom: widget.pageDataList!.length > 1
          ? PreferredSize(
              preferredSize: Size.fromHeight(Spacings.x8),
              child: Align(
                  alignment: Alignment.centerLeft,
                  child: widget.tabController != null &&
                          widget.pageDataList!.length > 1
                      ? Container(
                          height: Spacings.x8,
                          width: MediaQuery.of(context).size.width,
                          decoration: BoxDecoration(
                              border: Border(
                                  bottom: BorderSide(
                            color: Colors.grey.withOpacity(0.15),
                            width: 1.0,
                          ))),
                          child: TabBar(
                            tabAlignment: TabAlignment.start,
                            dividerHeight: 0,
                            isScrollable: true,
                            labelPadding: const EdgeInsets.all(0),
                            indicatorColor: HexColor.fromHex(_tabColor),
                            indicatorSize: TabBarIndicatorSize.label,
                            automaticIndicatorColorAdjustment: false,
                            indicatorPadding: const EdgeInsets.only(
                                left: Spacings.x3,
                                right: Spacings.x3,
                                top: Spacings.x1),
                            controller: widget.tabController,
                            tabs: widget.pageDataList!
                                .mapIndexed((index, e) => Semantics(
                                      label: e.title,
                                      explicitChildNodes: true,
                                      container: true,
                                      child: Tab(
                                          child: Padding(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: Spacings.x3,
                                            vertical: 0),
                                        child: Text(e.title ?? "",
                                            style: themeData.textStyle(
                                                TypescaleValues.P3,
                                                color: index ==
                                                        widget.tabController
                                                            ?.index
                                                    ? HexColor.fromHex(
                                                        _tabColor)
                                                    : HexColor.fromHex(
                                                            _tabColor)
                                                        .withOpacity(0.7))),
                                      )),
                                    ))
                                .toList(),
                          ),
                        )
                      : Container()))
          : null,
    );
  }
}

Widget rightBarActionButton(BuildContext context, Action? rightBarAction) {
  return TertiaryButton(
    () {
      ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
      actionBloc.add(PerformActionEvent(rightBarAction!));
    },
    rightBarAction?.title ?? "",
    buttonType: TertiaryButtonType.BIG,
    expanded: false,
  );
}

Widget getCoachHeaderIconsState(
    BuildContext context, CoachContactView coachContactView, Color color) {
  logWidgetClick(BuildContext context, String action) {
    RepositoryProvider.of<AnalyticsRepository>(context)
        .logWidgetClick(extraInfo: {"action": action}, widgetInfo: null);
  }

  Widget createIconButton(String asset, Color color, VoidCallback callback,
      {Key? key}) {
    return Padding(
        key: key,
        padding: EdgeInsets.symmetric(horizontal: 15),
        child: GestureDetector(
          onTap: () {
            callback();
          },
          child: CFNetworkImage(
              width: 25,
              height: 25,
              imageUrl: asset,
              color: color,
              fit: BoxFit.contain),
        ));
  }

  return Row(
    children: [
      if (coachContactView.progressAction != null)
        createIconButton(
            getImageUrl(context,
                imagePath: 'image/transform/icons/progress.png'),
            color, () {
          ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
          PerformActionEvent event =
              PerformActionEvent(coachContactView.progressAction!);
          actionBloc.add(event);
          logWidgetClick(context, "progress");
        }),
      if (coachContactView.callAction != null)
        createIconButton(
            getImageUrl(context,
                imagePath: 'image/transform/icons/schedule_call.png'),
            color, () {
          ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
          PerformActionEvent event =
              PerformActionEvent(coachContactView.callAction!);
          actionBloc.add(event);
          logWidgetClick(context, "call");
        }),
      if (coachContactView.chatAction != null)
        createIconButton(
            getImageUrl(context,
                imagePath: 'image/transform/icons/whatsapp_1.png'),
            color, () {
          ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
          PerformActionEvent event =
              PerformActionEvent(coachContactView.chatAction!);
          actionBloc.add(event);
          logWidgetClick(context, "chat");
        }),
    ],
  );
}

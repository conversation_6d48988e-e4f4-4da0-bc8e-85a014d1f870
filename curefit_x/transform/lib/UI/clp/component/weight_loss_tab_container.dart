import 'dart:io';

import 'package:collection/collection.dart';
import 'package:common/blocs/page/page_bloc.dart';
import 'package:common/ui/page/screen/list_page.dart';
import 'package:common/user/user_repository.dart';
import 'package:scroll_to_index/scroll_to_index.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/UI/clp/component/weight_loss_tab_bar.dart';
import 'package:transform/UI/clp/screens/bootcamp_clp.dart';
import 'package:transform/UI/clp/screens/clp.dart';
import 'package:transform/UI/clp/screens/lift_clp.dart';
import 'package:transform/blocs/weight_loss_tab/models.dart';
import 'package:transform/constants/constants.dart';
import 'package:enum_to_string/enum_to_string.dart';

class WeightLossTabContainer extends StatefulWidget {
  final bool inTabMode;
  final String? title;
  final List<PageData> pageDataList;
  final String? selectedTabPageId;

  WeightLossTabContainer(
      {this.pageDataList = const [],
      this.title,
      this.inTabMode = false,
      this.selectedTabPageId,
      Key? key})
      : super(key: key);

  @override
  State<WeightLossTabContainer> createState() => _WeightLossTabContainerState();
}

class _WeightLossTabContainerState extends State<WeightLossTabContainer>
    with TickerProviderStateMixin {
  late AutoScrollController _scrollController;
  TabController? _tabController;
  int selectedTab = 0;

  double toolbarHeight(int size) {
    return Platform.isAndroid
        ? (size == 1)
            ? 80
            : 120
        : (size == 1)
            ? 60
            : 98;
  }

  tabChanged() {
    if (!_tabController!.indexIsChanging && mounted) {
      setState(() {
        selectedTab = _tabController?.index ?? 0;
      });
    }
  }

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _scrollController.parentController = PrimaryScrollController.of(context);
    });
    _tabController?.removeListener(tabChanged);
    if (widget.selectedTabPageId != null) {
      PageData? initialPageData = widget.pageDataList.firstWhereOrNull(
          (element) => element.pageId == widget.selectedTabPageId);
      if (initialPageData != null) {
        selectedTab = widget.pageDataList.indexOf(initialPageData);
      }
    }
    _tabController = TabController(
        length: widget.pageDataList.length,
        vsync: this,
        initialIndex: selectedTab);
    _tabController?.addListener(tabChanged);
    _scrollController = AutoScrollController();
    super.initState();
  }

  @override
  void didUpdateWidget(covariant WeightLossTabContainer oldWidget) {
    if (mounted) {
      if (widget.selectedTabPageId != null &&
          selectedTab != widget.selectedTabPageId) {
        PageData? initialPageData = widget.pageDataList.firstWhereOrNull(
            (element) => element.pageId == widget.selectedTabPageId);
        if (initialPageData != null) {
          setState(() {
            selectedTab = widget.pageDataList.indexOf(initialPageData);
            if (_tabController != null) {
              _tabController!.animateTo(selectedTab);
            }
          });
        }
      }
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    _tabController?.removeListener(tabChanged);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        extendBodyBehindAppBar: true,
        backgroundColor: widget.inTabMode ? Colors.black : Colors.transparent,
        appBar: PreferredSize(
          preferredSize: Size.fromHeight(
              toolbarHeight(widget.pageDataList.length) +
                  AuroraTheme.of(context).embeddedSafeArea.top),
          child: WeightLossTabBar(
            key: Key(new DateTime.now().millisecond.toString()),
            title: widget.title,
            pageDataList: widget.pageDataList,
            inTabMode: widget.inTabMode,
            scrollController: _scrollController,
            tabController: _tabController,
          ),
        ),
        body: widget.pageDataList.isNotEmpty
            ? TabBarView(
                key: Key(widget.pageDataList.length.toString() +
                    (widget.selectedTabPageId ?? "")),
                physics: NeverScrollableScrollPhysics(),
                controller: _tabController,
                children: widget.pageDataList
                    .map((pageData) => getPage(pageData))
                    .toList())
            : Container());
  }

  Widget getPage(PageData pageData) {
    PageBloc pageBloc = BlocProvider.of<PageBloc>(context);
    return pageData.isListPage
        ? BasicListPage(
            disableFloatingCTAAnimation: true,
            repository: pageBloc.repository,
            showTitleBar: false,
            hideTitle: false,
            inTabMode: widget.inTabMode,
            shouldRefresh: false,
            footerPadding: EdgeInsets.zero,
            scrollController: _scrollController,
            extraButtonPadding: widget.inTabMode ? Spacings.x2 : 0.0,
            pageId: pageData.pageId!)
        : getCustomPages(pageData);
  }

  Widget getCustomPages(PageData pageData) {
    if (pageData.routeName != null) {
      if (pageData.routeName ==
          EnumToString.convertToString(RouteNames.tf_clp)) {
        return CoachCLP(
          tabMode: widget.inTabMode ? TabMode.EMBEDDED : TabMode.FULL,
          scrollController: _scrollController,
          fromCustomTab: true,
        );
      } else if (pageData.routeName ==
          EnumToString.convertToString(RouteNames.tf_bootcamp)) {
        return CultBootcampCLP(
          tabMode: widget.inTabMode ? TabMode.EMBEDDED : TabMode.FULL,
          scrollController: _scrollController,
          fromCustomTab: true,
        );
      } else if (pageData.routeName ==
          EnumToString.convertToString(RouteNames.lift)) {
        return LiftClp(
          tabMode: widget.inTabMode ? TabMode.EMBEDDED : TabMode.FULL,
          showTitleBar: false,
          scrollController: _scrollController,
        );
      }
    }
    return Container();
  }
}

import 'dart:math';
import 'dart:ui';

import 'package:common/ui/ui_toolkit.dart';
import 'package:flutter/material.dart';

class AnimatedLineGraph extends StatefulWidget {
  final double width;
  final double height;
  final List<num> values;
  final String? unit;
  final num? focusValue;

  const AnimatedLineGraph(
      {Key? key,
      required this.values,
      this.height = 150,
      this.width = 335,
      this.unit,
      this.focusValue})
      : super(key: key);

  @override
  State<AnimatedLineGraph> createState() => _AnimatedLineGraphState();
}

class _AnimatedLineGraphState extends State<AnimatedLineGraph>
    with TickerProviderStateMixin {
  bool progressGraphAnimate = false;
  AnimationController? arrowController;
  AnimationController? graphPosController;
  Animation<double>? arrowAnimation;
  Animation<double>? graphPosAnimation;

  @override
  void initState() {
    super.initState();
    arrowController = new AnimationController(
        duration: const Duration(milliseconds: 650), vsync: this);
    graphPosController = new AnimationController(
        duration: const Duration(milliseconds: 800), vsync: this);
    arrowAnimation = Tween<double>(begin: 1, end: 5.0).animate(CurvedAnimation(
        parent: arrowController!, curve: Cubic(0.72, 0.00, 0.82, 1.00)));
    graphPosAnimation = Tween<double>(begin: 15.0, end: 0.0).animate(
        CurvedAnimation(
            parent: graphPosController!, curve: Cubic(0.75, 0.00, 0.25, 1.00)));
    graphPosController?.forward();
  }

  @override
  void dispose() {
    arrowController?.dispose();
    graphPosController?.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        !progressGraphAnimate && graphPosAnimation != null
            ? AnimatedBuilder(
                animation: graphPosAnimation!,
                builder: (BuildContext context, Widget? child) {
                  return Transform.translate(
                    offset: Offset(
                        0,
                        graphPosAnimation != null
                            ? graphPosAnimation!.value
                            : 0.0),
                    child: TweenAnimationBuilder(
                        tween: Tween<double>(begin: -0.2, end: 1.0),
                        duration: Duration(milliseconds: 2000),
                        curve: Cubic(0.61, 0.00, 0.39, 1.00),
                        onEnd: () {
                          if (mounted) {
                            setState(() {
                              progressGraphAnimate = true;
                              arrowController?.forward();
                              arrowController?.repeat(reverse: true);
                            });
                          }
                        },
                        builder: (BuildContext context, double _val, _) {
                          return CustomPaint(
                            size: Size(widget.width, widget.height),
                            painter: LineGraphPainter(
                                values: widget.values,
                                focusValue: widget.focusValue,
                                progress: _val,
                                showDots: true,
                                unit: widget.unit,
                                showText: true),
                          );
                        }),
                  );
                })
            : CustomPaint(
                size: Size(widget.width, widget.height),
                painter: LineGraphPainter(
                    values: widget.values,
                    focusValue: widget.focusValue,
                    progress: 1,
                    showDots: true,
                    unit: widget.unit,
                    showText: true),
              ),
        if (arrowAnimation != null)
          AnimatedBuilder(
              animation: arrowAnimation!,
              builder: (BuildContext context, Widget? child) {
                return Transform.translate(
                  offset: Offset(
                      0, arrowAnimation != null ? arrowAnimation!.value : 0.0),
                  child: AnimatedOpacity(
                    duration: Duration(milliseconds: 400),
                    opacity: progressGraphAnimate ? 1.0 : 0.0,
                    curve: Curves.easeIn,
                    child: CustomPaint(
                      size: Size(widget.width, widget.height),
                      painter: ArrowPainter(
                        values: widget.values,
                        focusValue: widget.focusValue,
                      ),
                    ),
                  ),
                );
              }),
        AnimatedOpacity(
          duration: Duration(milliseconds: 400),
          curve: Curves.easeIn,
          opacity: progressGraphAnimate ? 1.0 : 0.0,
          child: CustomPaint(
            size: Size(widget.width, widget.height),
            painter: LineGraphPainter(
                values: widget.values,
                focusValue: widget.focusValue,
                progress: 1,
                showDots: false,
                unit: widget.unit,
                color: Colors.white60,
                showCompleteGraph: true,
                showFinalValue: true,
                showText: true),
          ),
        ),
      ],
    );
  }
}

class LineGraphPainter extends CustomPainter {
  final List<num> values;
  final double progress;
  final num? focusValue;
  final bool showDots;
  final bool showText;
  final Color color;
  final String? unit;
  final bool showFinalValue;
  final bool showCompleteGraph;

  LineGraphPainter({
    required this.values,
    this.focusValue,
    required this.progress,
    this.color = Colors.white,
    this.showDots = false,
    this.showText = false,
    this.unit,
    this.showCompleteGraph = false,
    this.showFinalValue = false,
  });

  @override
  void paint(Canvas canvas, Size size) {
    num upperLimit = values.reduce(min);
    num lowerLimit = values.reduce(max);

    final Paint paint = new Paint()
      ..color = color
      ..strokeWidth = 2
      ..strokeCap = StrokeCap.round
      ..style = PaintingStyle.stroke;
    final height = size.height;
    final width = size.width;
    final double maxRadius = 10;
    final path = Path();
    final curveRadius = 2;
    List dottedIndex = [0, values.length - 1];
    if (focusValue != null) {
      int idx = values.lastIndexOf(focusValue!);
      if (idx != -1) {
        dottedIndex.add(idx);
      }
    }
    path.moveTo(getXCoordinates(0, values.length, width),
        getYCoordinates(upperLimit, lowerLimit, height, 0, values[0]));
    for (int idx = 1; idx < values.length; idx++) {
      double y0 =
          getYCoordinates(upperLimit, lowerLimit, height, 0, values[idx - 1]);
      double x1 = getXCoordinates(idx, values.length, width);
      double y1 =
          getYCoordinates(upperLimit, lowerLimit, height, 0, values[idx]);
      if (idx == values.length - 1) {
        path.lineTo(x1, y1);
      } else if (y1 > y0) {
        double y2 =
            getYCoordinates(upperLimit, lowerLimit, height, 0, values[idx + 1]);
        if (y1 <= y2) {
          path.lineTo(x1 - curveRadius, y1 - curveRadius);
          path.cubicTo(x1 - curveRadius, y1 - curveRadius, x1, y1,
              x1 + curveRadius, y1 + curveRadius);
        } else {
          path.lineTo(x1 - curveRadius, y1 - curveRadius);
          path.cubicTo(x1 - curveRadius, y1 - curveRadius, x1, y1,
              x1 + curveRadius, y1 - curveRadius);
        }
      } else if (y1 <= y0) {
        double y2 =
            getYCoordinates(upperLimit, lowerLimit, height, 0, values[idx + 1]);
        if (y1 < y2) {
          path.lineTo(x1 - curveRadius, y1 + curveRadius);
          path.cubicTo(x1 - curveRadius, y1 + curveRadius, x1, y1,
              x1 + curveRadius, y1 + curveRadius);
        } else {
          path.lineTo(x1 - curveRadius, y1 + curveRadius);
          path.cubicTo(x1 - curveRadius, y1 + curveRadius, x1, y1,
              x1 + curveRadius, y1 - curveRadius);
        }
      }
      if (focusValue != null && idx == values.lastIndexOf(focusValue!)) {
        if (!showCompleteGraph) {
          break;
        }
      }
    }
    final Paint paint1 = new Paint()..color = color;
    PathMetrics pathMetrics = path.computeMetrics();
    for (PathMetric pathMetric in pathMetrics) {
      if (progress > 0) {
        Path extractPath =
            pathMetric.extractPath(0, pathMetric.length * progress);
        canvas.drawPath(extractPath, paint);
        Tangent? pos =
            pathMetric.getTangentForOffset(pathMetric.length * progress);
        if (showDots && pos != null) {
          for (double radius = 3; radius < maxRadius; radius = radius + 3) {
            canvas.drawCircle(
                pos.position, radius, paint1..color = Colors.white60);
          }
          for (double radius = 5; radius < maxRadius; radius = radius + 4) {
            canvas.drawCircle(
                pos.position, radius, paint1..color = Colors.white60);
          }
        }
        // }
      }
    }

    if (progress >= 0.0) {
      if (showDots) {
        for (double radius = 3; radius < maxRadius; radius = radius + 3) {
          canvas.drawCircle(
              Offset(
                  getXCoordinates(0, values.length, width),
                  getYCoordinates(
                      upperLimit, lowerLimit, height, 0, values[0])),
              radius,
              paint1);
        }
      }

      if (showText) {
        TextSpan startingSpan = new TextSpan(
            style: new TextStyle(
                color: Colors.white, fontSize: 12, fontWeight: FontWeight.w400),
            text: values[0].toString() + " " + (unit ?? ""));
        TextPainter startingTp = new TextPainter(
            text: startingSpan,
            textAlign: TextAlign.center,
            textDirection: TextDirection.ltr);
        startingTp.layout();
        startingTp.paint(
            canvas,
            new Offset(
                getXCoordinates(0, values.length, width) - 55,
                getYCoordinates(upperLimit, lowerLimit, height, 0, values[0]) -
                    10));
      }
    }

    if (progress == 1.0 && showFinalValue) {
      for (double radius = 3; radius < maxRadius; radius = radius + 3) {
        canvas.drawCircle(
            Offset(
                getXCoordinates(dottedIndex[1], values.length, width),
                getYCoordinates(
                    upperLimit, lowerLimit, height, 0, values[dottedIndex[1]])),
            radius,
            paint1);
      }
      if (showText) {
        TextSpan endingSpan = new TextSpan(
            style: new TextStyle(
                color: Colors.white, fontSize: 12, fontWeight: FontWeight.w400),
            text: values[values.length - 1].toString() + " " + (unit ?? ""));
        TextPainter endingTp = new TextPainter(
            text: endingSpan,
            textAlign: TextAlign.center,
            textDirection: TextDirection.ltr);
        endingTp.layout();
        endingTp.paint(
            canvas,
            new Offset(
                getXCoordinates(values.length - 1, values.length, width) + 15,
                getYCoordinates(upperLimit, lowerLimit, height, 0,
                        values[values.length - 1]) -
                    7));
      }
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => true;
}

class ArrowPainter extends CustomPainter {
  final num? focusValue;
  final List<num> values;

  ArrowPainter({
    required this.values,
    this.focusValue,
  });

  @override
  void paint(Canvas canvas, Size size) {
    num upperLimit = values.reduce(min);
    num lowerLimit = values.reduce(max);

    final arrowPaint = Paint()
      ..color = Colors.white
      ..strokeWidth = 2
      ..strokeCap = StrokeCap.round;
    double horizontalCorrection = 1.5;
    double arrowXCoord = getXCoordinates(
        values.lastIndexOf(focusValue!), values.length, size.width);
    double arrowYCoord = getYCoordinates(upperLimit, lowerLimit, size.height, 0,
        values[values.lastIndexOf(focusValue!)]);
    canvas.drawLine(
        Offset(arrowXCoord + horizontalCorrection, arrowYCoord - 15),
        Offset(arrowXCoord + horizontalCorrection, arrowYCoord - 25),
        arrowPaint);
    canvas.drawLine(
        Offset(arrowXCoord + horizontalCorrection, arrowYCoord - 15),
        Offset(arrowXCoord - 5 + horizontalCorrection, arrowYCoord - 20),
        arrowPaint);
    canvas.drawLine(
        Offset(arrowXCoord + horizontalCorrection, arrowYCoord - 15),
        Offset(arrowXCoord + 5 + horizontalCorrection, arrowYCoord - 20),
        arrowPaint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => true;
}

double getYCoordinates(num u1, num l1, num u2, num l2, num value) {
  return ((value - l1) / (u1 - l1)) * (u2 - l2) + l2;
}

double getXCoordinates(num index, num length, num width) {
  return (index + 1) * (width / (length + 1));
}

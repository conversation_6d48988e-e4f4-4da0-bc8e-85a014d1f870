import 'package:common/action/action_handler.dart';
import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/constants/action_constants.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/atoms/secondary_button.dart';
import 'package:common/ui/atoms/tertiary_button.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/Colors.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/material.dart' hide Action;
import 'package:transform/blocs/lift_clp/models.dart';

exerciseUpdationModal({
  required BuildContext context,
  required WorkoutModalData modalData,
  String? title,
}) {
  Map<String, dynamic> updates = new Map<String, dynamic>();

  void updateMetricsData({bool isReset = false}) {
    for (SetDetails setDetail in modalData.setDetails) {
      updates[setDetail.id.toString()] = new ExerciseMetric(
              id: modalData.id,
              reps: isReset ? setDetail.suggestedReps : setDetail.reps,
              weight: isReset ? setDetail.suggestedWeight : setDetail.weight,
              exerciseId: modalData.exerciseId)
          .toJson();
    }
  }

  Widget getInfoWidget() {
    return Padding(
      padding: const EdgeInsets.only(top: Spacings.x4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (modalData.imageUrl != null)
            ClipRRect(
              borderRadius: BorderRadius.all(Radius.circular(10)),
              child: Container(
                height: 70,
                width: 70,
                child: CFNetworkImage(
                  height: 70,
                  width: 70,
                  imageUrl: getImageUrl(context, imagePath: modalData.imageUrl),
                  fit: BoxFit.fitWidth,
                  errorWidget: (context, url, error) => const Icon(Icons.error),
                ),
              ),
            ),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.only(left: Spacings.x7),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    modalData.title ?? "",
                    style:
                        AuroraTheme.of(context).textStyle(TypescaleValues.H4),
                  ),
                  SizedBox(
                    height: Spacings.x1,
                  ),
                  Text(
                    modalData.subtitle ?? "",
                    style: AuroraTheme.of(context)
                        .textStyle(TypescaleValues.P8, color: Colors.white60),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget getInputFieldWidget(
      SetDetails setDetail, bool isRep, bool isCompleted, bool resetData) {
    TextEditingController textEditingController = new TextEditingController();
    String value = isRep ? setDetail.reps! : setDetail.weight!;
    String suggestedValue =
        isRep ? setDetail.suggestedReps! : setDetail.suggestedWeight!;
    String unit = isRep ? setDetail.repUnit! : setDetail.weightUnit!;
    textEditingController.text = value;
    textEditingController.selection = TextSelection.fromPosition(TextPosition(offset: textEditingController.text.length));
    bool isDataEditable =
        isRep ? setDetail.repEditable : setDetail.weightEditable;
    if (resetData) {
      textEditingController.text = isDataEditable ? suggestedValue : value;
    }
    return Row(
      children: [
        !isCompleted
            ? isDataEditable
                ? Container(
                    width: 50,
                    height: 45,
                    decoration: BoxDecoration(
                      color: ColorPalette.white10,
                      borderRadius: BorderRadius.all(
                        Radius.circular(5),
                      ),
                    ),
                    child: Center(
                      child: TextField(
                        controller: textEditingController,
                        keyboardType: TextInputType.number,
                        textInputAction: TextInputAction.next,
                        decoration: InputDecoration(
                          contentPadding: EdgeInsets.fromLTRB(10, 5, 10, 5),
                          enabledBorder: UnderlineInputBorder(
                            borderSide: BorderSide(color: Colors.transparent),
                          ),
                          focusedBorder: UnderlineInputBorder(
                            borderSide: BorderSide(color: Colors.transparent),
                          ),
                          border: UnderlineInputBorder(
                            borderSide: BorderSide(color: Colors.transparent),
                          ),
                        ),
                        style: AuroraTheme.of(context).textStyle(
                            TypescaleValues.P2,
                            color: ColorPalette.white),
                        onChanged: (val) {
                          ExerciseMetric metricData = ExerciseMetric.fromJson(
                              updates[setDetail.id.toString()]);
                          if (isRep) {
                            metricData.reps =
                                val.isEmpty ? setDetail.reps : val;
                            updates[setDetail.id.toString()] =
                                metricData.toJson();
                            setDetail.reps = val.isEmpty ? setDetail.reps : val;
                          } else {
                            metricData.weight =
                                val.isEmpty ? setDetail.weight : val;
                            updates[setDetail.id.toString()] =
                                metricData.toJson();
                            setDetail.weight = val.isEmpty ? setDetail.weight : val;
                          }
                        },
                        textAlign: TextAlign.center,
                        minLines: 1,
                        maxLines: 1,
                        inputFormatters: <TextInputFormatter>[
                          FilteringTextInputFormatter.digitsOnly
                        ],
                      ),
                    ),
                  )
                : Container(
                    width: 50,
                    height: 45,
                    child: Center(
                      child: Text(
                        textEditingController.text,
                        style: AuroraTheme.of(context)
                            .textStyle(TypescaleValues.P2),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  )
            : Container(
                width: 50,
                height: 45,
                child: Center(
                  child: Text(
                    textEditingController.text,
                    style:
                        AuroraTheme.of(context).textStyle(TypescaleValues.P2),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
        Container(
          padding: const EdgeInsets.only(left: Spacings.x2),
          width: 50,
          height: 45,
          child: Center(
            child: Text(
              unit,
              style: AuroraTheme.of(context).textStyle(TypescaleValues.P2),
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ],
    );
  }

  Widget getFieldEditWidget(
      SetDetails setDetail, bool isCompleted, bool resetData) {
    return Container(
      child: Row(
        children: [
          Text(
            setDetail.title ?? "",
            style: AuroraTheme.of(context)
                .textStyle(TypescaleValues.P4, color: Colors.white60),
          ),
          SizedBox(
            width: Spacings.x10,
          ),
          Expanded(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                getInputFieldWidget(setDetail, true, isCompleted, resetData),
                Text(
                  "X",
                  style: AuroraTheme.of(context)
                      .textStyle(TypescaleValues.P8, color: Colors.white38),
                ),
                getInputFieldWidget(setDetail, false, isCompleted, resetData),
              ],
            ),
          ),
        ],
      ),
    );
  }

  bool isCompleted = modalData.isCompleted;
  bool resetData = false;

  showModalBottomSheet<void>(
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(20),
        topRight: Radius.circular(20),
      ),
    ),
    isScrollControlled: true,
    context: context,
    builder: (context) {
      return Padding(
        padding: MediaQuery.of(context).viewInsets,
        child: Wrap(
          children: [
            BlurView(
              backgroundColor: Colors.transparent,
              opacity: 0.8,
              blurType: BlurType.HIGH,
              child: StatefulBuilder(
                builder: (BuildContext context, setState) {
                  updateMetricsData(isReset: resetData);
                  return Padding(
                    padding:
                        const EdgeInsets.symmetric(horizontal: Spacings.x4),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          margin:
                              const EdgeInsets.symmetric(vertical: Spacings.x2),
                          height: 2.5,
                          width: 55,
                          alignment: Alignment.topCenter,
                          color: Colors.white.withOpacity(0.5),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(top: Spacings.x5),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              if (modalData.modalTitle != null)
                                Expanded(
                                  child: Text(
                                    modalData.modalTitle ?? "",
                                    style: AuroraTheme.of(context)
                                        .textStyle(TypescaleValues.H1),
                                  ),
                                ),
                              GestureDetector(
                                onTap: () {
                                  if (Navigator.canPop(context)) {
                                    Navigator.pop(context);
                                  } else if (!Navigator.canPop(context)) {
                                    ActionBloc actionBloc =
                                        BlocProvider.of<ActionBloc>(context);
                                    actionBloc.add(CloseApplicationEvent(
                                        shouldReset: false));
                                  }
                                },
                                child: Icon(
                                  Icons.close,
                                  color: Colors.white,
                                ),
                              ),
                            ],
                          ),
                        ),
                        getInfoWidget(),
                        ...modalData.setDetails.map<Widget>((setDetail) {
                          return Padding(
                            padding: const EdgeInsets.only(top: Spacings.x4),
                            child: getFieldEditWidget(
                                setDetail, isCompleted, resetData),
                          );
                        }).toList(),
                        Padding(
                          padding: const EdgeInsets.only(top: Spacings.x5),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              if (modalData.action != null && !isCompleted)
                                PrimaryButton(() {
                                  ActionBloc actionBloc =
                                      BlocProvider.of<ActionBloc>(context);
                                  PerformActionEvent event = PerformActionEvent(
                                      new Action(
                                          type: ActionTypes
                                              .UPDATE_EXERCISE_METRIC,
                                          meta: {"updates": updates,
                                          "completed": true}));
                                  actionBloc.add(event);
                                  RepositoryProvider.of<AnalyticsRepository>(
                                          context)
                                      .logWidgetClick(extraInfo: {
                                    "action": "Exercise Metric update",
                                    "trigger": "Completed",
                                    "metricType": updates,
                                  }, widgetInfo: null);
                                  Future.delayed(Duration(milliseconds: 250),
                                      () {
                                    Navigator.of(context).pop();
                                  });
                                }, modalData.action!.title ?? ""),
                              if (modalData.primaryAction != null &&
                                  isCompleted)
                                Padding(
                                  padding:
                                      const EdgeInsets.only(top: Spacings.x2),
                                  child: SecondaryButton(() {
                                    RepositoryProvider.of<AnalyticsRepository>(
                                            context)
                                        .logWidgetClick(extraInfo: {
                                      "action": "Exercise Edit",
                                      "trigger": "Edit",
                                    }, widgetInfo: null);
                                    setState(() {
                                      isCompleted = false;
                                    });
                                  }, modalData.primaryAction!.title ?? ""),
                                ),
                              if (modalData.secondaryAction != null &&
                                  isCompleted)
                                Padding(
                                  padding:
                                      const EdgeInsets.only(top: Spacings.x2),
                                  child: TertiaryButton(() {
                                    ActionBloc actionBloc =
                                    BlocProvider.of<ActionBloc>(context);
                                    PerformActionEvent event = PerformActionEvent(
                                        new Action(
                                            type: ActionTypes
                                                .UPDATE_EXERCISE_METRIC,
                                            meta: {"updates": updates,
                                              "completed": false}));
                                    actionBloc.add(event);
                                    RepositoryProvider.of<AnalyticsRepository>(
                                            context)
                                        .logWidgetClick(extraInfo: {
                                      "action": "Exercise Undone",
                                      "trigger": "Undo",
                                    }, widgetInfo: null);
                                    setState(() {
                                      resetData = true;
                                      isCompleted = false;
                                    });
                                  }, modalData.secondaryAction!.title ?? ""),
                                ),
                            ],
                          ),
                        ),
                        SizedBox(
                          height: Spacings.x6,
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      );
    },
  );
}

import 'package:common/action/action_handler.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/atoms/floating_twin_button.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/util/action_util.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:lottie/lottie.dart';
import 'package:flutter/material.dart' hide Action;

showInfoActionModal({
  required BuildContext context,
  String? title,
  String? subtitle,
  String? imageUrl,
  String? lottieUrl,
  String? description,
  List<Action>? actionsList,
  CanvasTheme? themeType,
}) {
  showModalBottomSheet<void>(
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(20),
        topRight: Radius.circular(20),
      ),
    ),
    isScrollControlled: true,
    context: context,
    builder: (context) {
      return Padding(
        padding: MediaQuery.of(context).viewInsets,
        child: Wrap(
          children: [
            BlurView(
              backgroundColor: themeType != null && themeType == CanvasTheme.NIGHT
                  ? Color.fromRGBO(57, 68, 116, 1)
                  : Color.fromRGBO(32, 77, 89, 1),
              opacity: 0.8,
              blurType: BlurType.HIGH,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    margin: const EdgeInsets.symmetric(vertical: Spacings.x2),
                    height: 2.5,
                    width: 55,
                    alignment: Alignment.topCenter,
                    color: Colors.white.withOpacity(0.5),
                  ),
                  if (imageUrl != null || lottieUrl != null)
                    Container(
                        height: scale(context, 200),
                        width: scale(context, 220),
                        padding:
                            const EdgeInsets.symmetric(vertical: Spacings.x2),
                        child: Stack(
                          children: [
                            if (lottieUrl != null)
                              Align(
                                alignment: Alignment.center,
                                child: Container(
                                  alignment: Alignment.center,
                                  height: scale(context, 200),
                                  width: scale(context, 220),
                                  child: Lottie.network(
                                    getMediaUrl(lottieUrl),
                                    height: scale(context, 200),
                                    width: scale(context, 220),
                                  ),
                                ),
                              ),
                            if (imageUrl != null)
                              Align(
                                alignment: Alignment.center,
                                child: Container(
                                  alignment: Alignment.center,
                                  height: scale(context, 100),
                                  width: scale(context, 100),
                                  child: CFNetworkImage(
                                    imageUrl: getImageUrl(context,
                                        imagePath: imageUrl),
                                    fit: BoxFit.contain,
                                    height: scale(context, 100),
                                    width: scale(context, 100),
                                    placeholder:
                                        (BuildContext context, String url) {
                                      return Container();
                                    },
                                  ),
                                ),
                              ),
                          ],
                        )),
                  if (description != null)
                    Padding(
                      padding: const EdgeInsets.only(
                          top: Spacings.x1,
                          left: Spacings.x5,
                          right: Spacings.x5),
                      child: Text(
                        description,
                        style: AuroraTheme.of(context).textStyle(
                          TypescaleValues.TAGTEXT,
                          color: Colors.white60,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  if (title != null)
                    Padding(
                      padding: const EdgeInsets.only(
                          top: Spacings.x1,
                          left: Spacings.x5,
                          right: Spacings.x5),
                      child: Text(
                        title,
                        style: AuroraTheme.of(context).textStyle(
                          TypescaleValues.H2,
                          color: Colors.white,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  if (subtitle != null)
                    Padding(
                      padding: const EdgeInsets.only(
                          top: Spacings.x1,
                          left: Spacings.x5,
                          right: Spacings.x5),
                      child: Text(
                        subtitle,
                        style: AuroraTheme.of(context).textStyle(
                          TypescaleValues.P1,
                          color: Colors.white60,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  SizedBox(
                    height: Spacings.x8,
                  ),
                  if (actionsList != null && actionsList.isNotEmpty)
                    FloatingTwinButton(
                      titleText: actionsList[0].description,
                      data: actionsList,
                      onPress: (Action action) {
                        if (Navigator.canPop(context)) {
                          Navigator.pop(context);
                        } else {
                          ActionBloc actionBloc =
                              BlocProvider.of<ActionBloc>(context);
                          actionBloc
                              .add(CloseApplicationEvent(shouldReset: false));
                        }
                        clickActionWithAnalytics(action, context, null,
                            {"modal": "InfoActionModal"});
                      },
                    ),
                ],
              ),
            ),
          ],
        ),
      );
    },
  );
}

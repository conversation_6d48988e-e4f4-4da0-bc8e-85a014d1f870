import 'package:common/action/action_handler.dart';
import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/blocs/navigation/navigation_bloc.dart';
import 'package:common/blocs/navigation/state.dart';
import 'package:common/blocs/tab/tab_bloc.dart';
import 'package:common/model/error_prop_model.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/page/basic_page_container.dart';
import 'package:common/ui/screens/error_screen.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/widget_factory.dart';
import 'package:common/user/user_repository.dart';
import 'package:common/util/action_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:transform/blocs/weight_loss_clp/events.dart';
import 'package:transform/blocs/weight_loss_clp/state.dart';
import 'package:transform/blocs/weight_loss_clp/weight_loss_clp_bloc.dart';
import 'package:transform/constants/constants.dart';
import 'package:navigation_history_observer/navigation_history_observer.dart';

class WeightLossClpArguments {
  String? subCategoryCode;

  WeightLossClpArguments(Map<String, dynamic> payload) {
    this.subCategoryCode = payload["subCategoryCode"];
  }
}

class WeightLossClp extends StatefulWidget {
  final TabMode tabMode;

  const WeightLossClp({Key? key, this.tabMode = TabMode.FULL})
      : super(key: key);

  @override
  _WeightLossClpState createState() => _WeightLossClpState();
}

class _WeightLossClpState extends State<WeightLossClp>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  final AppTabs tab = AppTabs.TRANSFORM;

  WeightLossClpArguments? getWeightLossClpArguments() {
    final ScreenArguments? args =
        ModalRoute.of(context)!.settings.arguments as ScreenArguments?;
    if (args != null) {
      return WeightLossClpArguments(args.params);
    }
    return null;
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      WeightLossClpArguments? arguments = getWeightLossClpArguments();
      final weightLossClp = BlocProvider.of<WeightLossClpBloc>(context);
      weightLossClp.add(ResetWeightLossClpEvent());
      weightLossClp.add(LoadWeightLossClpEvent(showLoader: true));
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  _getTabBarHeight() {
    if (widget.tabMode == TabMode.FULL) {
      return 0;
    }
    return MediaQuery.of(context).padding.bottom + 55;
  }

  refresh({required BuildContext context, bool showLoader = false}) {
    WeightLossClpArguments? arguments = getWeightLossClpArguments();
    final weightLossClp = BlocProvider.of<WeightLossClpBloc>(context);
    weightLossClp.add(LoadWeightLossClpEvent(showLoader: showLoader));
  }

  onBackPress() {
    if (Navigator.canPop(context)) {
      Navigator.pop(context);
    } else if (!Navigator.canPop(context)) {
      ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
      actionBloc.add(CloseApplicationEvent(shouldReset: true));
    }
  }

  logPageView() {
    AnalyticsRepository analyticsRepository =
        RepositoryProvider.of<AnalyticsRepository>(context);
    analyticsRepository.logPageViewEvent(
        pageId: EnumToString.convertToString(RouteNames.weight_loss),
        eventInfo: {});
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    switch (state) {
      case AppLifecycleState.resumed:
        logPageView();
        refresh(context: context, showLoader: false);
        break;
      case AppLifecycleState.inactive:
        break;
      case AppLifecycleState.paused:
        break;
      case AppLifecycleState.detached:
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    TabBloc tabBloc = BlocProvider.of<TabBloc>(context);

    return Scaffold(
      extendBodyBehindAppBar: true,
      body: Stack(
        children: [
          Aurora(
            context: context,
            canvasTheme: CanvasTheme.CLASSIC,
            size: MediaQuery.of(context).size,
          ),
          if (widget.tabMode == TabMode.EMBEDDED)
            BlocListener<TabBloc, TabState>(
              listener: (context, state) {
                if (state is TabSelected) {
                  if (state.selectedTab.tab == tab) {
                    refresh(context: context);
                    logPageView();
                  }
                }
              },
              child: const SizedBox(),
            ),
          Positioned.fill(
            child: BlocListener<NavigationBloc, NavigationState>(
              listener: (context, state) {
                if (state is NavigationStackUpdated &&
                    state.action == NavigationStackAction.pop &&
                    state.route?.settings.name ==
                        '/${EnumToString.convertToString(RouteNames.weight_loss)}') {
                  refresh(context: context, showLoader: false);
                }
              },
              child: BlocBuilder<WeightLossClpBloc, WeightLossClpState>(
                buildWhen: (previous, current) =>
                    tabBloc.selectedTab?.tab == tab ||
                    widget.tabMode != TabMode.EMBEDDED,
                builder: (context, state) {
                  List<Widget> _widgets = [];
                  WidgetFactory widgetFactory =
                      RepositoryProvider.of<WidgetFactory>(context);
                  if (state is WeightLossClpLoadedState) {
                    List<Widget> widgets = state.screenData.widgets!
                        .map<Widget>(
                            (widget) => widgetFactory.createWidget(widget))
                        .toList();
                    _widgets = widgets;
                  } else if (state is WeightLossClpLoadingState &&
                      state.screenData != null) {
                    List<Widget> widgets = state.screenData!.widgets!
                        .map<Widget>(
                            (widget) => widgetFactory.createWidget(widget))
                        .toList();
                    _widgets = widgets;
                  } else if (state is WeightLossClpErrorState) {
                    return Stack(
                      children: [
                        Container(
                          color: Colors.black,
                          child: ErrorScreen(
                              errorInfo: state.errorInfo ?? UnknownError()),
                        ),
                        Positioned(
                          right: 5,
                          top: 30,
                          child: IconButton(
                            onPressed: () {
                              if (Navigator.canPop(context)) {
                                Navigator.pop(context);
                              } else if (!Navigator.canPop(context)) {
                                ActionBloc actionBloc =
                                    BlocProvider.of<ActionBloc>(context);
                                actionBloc.add(
                                    CloseApplicationEvent(shouldReset: false));
                              }
                            },
                            icon: const Icon(
                              Icons.clear,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    );
                  }
                  return BasicPageContainer(
                    showTitleBar: true,
                    shouldBuildWidget: false,
                    itemBuilder: (BuildContext context, Widget currentWidget,
                        int index) {
                      return _widgets[index];
                    },
                    widgetData: _widgets,
                    hideBackButton: widget.tabMode == TabMode.EMBEDDED,
                    onBackPressed: () {
                      onBackPress();
                    },
                    titleBarPadding: widget.tabMode == TabMode.EMBEDDED
                        ? 12.0
                        : 0.0,
                    footerPadding: EdgeInsets.only(
                        bottom: widget.tabMode == TabMode.EMBEDDED
                            ? _getTabBarHeight()
                            : 0),
                    titleBarRightActions: state is WeightLossClpLoadedState &&
                            state.screenData.rightActionButton != null
                        ? [
                            InkWell(
                              onTap: () {
                                clickActionWithAnalytics(
                                    state.screenData.rightActionButton!,
                                    context,
                                    null,
                                    {"from": "rightActionButton"});
                              },
                              child: Text(
                                state.screenData.rightActionButton!.title ?? "",
                                style: AuroraTheme.of(context)
                                    .textStyle(TypescaleValues.P3),
                              ),
                            ),
                          ]
                        : null,
                    title: state is WeightLossClpLoadedState
                        ? state.screenData.title
                        : "Weight loss",
                    showLoader: false,
                    canvasTheme: CanvasTheme.CLASSIC,
                  );
                },
              ),
            ),
          ),
          Positioned.fill(
            child: BlocBuilder<WeightLossClpBloc, WeightLossClpState>(
              builder: (context, state) {
                if (state is WeightLossClpLoadingState && state.showLoader) {
                  return Center(child: FancyLoadingIndicator());
                }
                return Container();
              },
            ),
          ),
        ],
      ),
    );
  }
}

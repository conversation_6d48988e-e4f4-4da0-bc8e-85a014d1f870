import 'package:common/blocs/booking/booking_bloc.dart';
import 'package:common/blocs/booking/events.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/atoms/secondary_button.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:flutter/material.dart' hide Action;
import 'package:flutter_bloc/flutter_bloc.dart';

showCancelAlertModal({
  required BuildContext context,
  String? description,
  String? title,
  String? image,
  String? firstButtonTitle,
  String? secondButtonTitle,
  String? bookingId,
  String? orderId,
  String? productId,
  String? code,
  CanvasTheme? themeType,
}) {
  showModalBottomSheet<void>(
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(20),
        topRight: Radius.circular(20),
      ),
    ),
    isScrollControlled: true,
    context: context,
    builder: (context) {
      return Padding(
        padding: MediaQuery.of(context).viewInsets,
        child: Wrap(
          children: [
            BlurView(
              backgroundColor: themeType == CanvasTheme.NIGHT
                  ? Color.fromRGBO(57, 68, 116, 1)
                  : Color.fromRGBO(32, 77, 89, 1),
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: Spacings.x4),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      margin: const EdgeInsets.symmetric(vertical: Spacings.x2),
                      height: 2.5,
                      width: 55,
                      alignment: Alignment.topCenter,
                      color: Colors.white.withOpacity(0.5),
                    ),
                    if (image != null)
                      Padding(
                        padding: const EdgeInsets.only(top: Spacings.x10),
                        child: CFNetworkImage(
                          imageUrl: getImageUrl(context, imagePath: image),
                          fit: BoxFit.contain,
                          height: scale(context, 45),
                          width: scale(context, 30),
                          placeholder: (BuildContext context, String url) {
                            return Container();
                          },
                        ),
                      ),
                    Padding(
                      padding: const EdgeInsets.only(top: Spacings.x2),
                      child: Text(
                        title ?? "",
                        style: AuroraTheme.of(context).textStyle(
                          TypescaleValues.H2,
                          color: Colors.white,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(top: Spacings.x1),
                      child: Text(
                        description ?? "",
                        style: AuroraTheme.of(context).textStyle(
                          TypescaleValues.P5,
                          color: Colors.white.withOpacity(0.6),
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(top: Spacings.x8),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Container(
                            width: scale(context, 160),
                            child: SecondaryButton(
                              () {
                                Navigator.pop(context);
                              },
                              firstButtonTitle ?? "NO",
                              height: 50,
                            ),
                          ),
                          Container(
                            width: scale(context, 160),
                            child: SecondaryButton(
                              () {
                                Navigator.pop(context);
                                BookingBloc bookingBloc =
                                BlocProvider.of<BookingBloc>(context);
                                bookingBloc.add(CancelBookingEvent(
                                    bookingId: bookingId ?? "",
                                    productId: productId ?? ""));
                              },
                              secondButtonTitle ?? "YES",
                              height: 50,
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(
                      height: Spacings.x4,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      );
    },
  );
}

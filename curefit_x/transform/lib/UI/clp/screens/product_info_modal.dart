import 'package:common/action/action_handler.dart';
import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/atoms/floating_twin_button.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/util/action_util.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/material.dart' hide Action;

class ProductPoints {
  ProductPoints({
    this.imageUrl,
    this.title,
  });

  final String? imageUrl;
  final String? title;

  static ProductPoints fromJson(json) {
    return ProductPoints(
      imageUrl: json["imageUrl"],
      title: json['title'],
    );
  }
}

class ProductInfoModalData {
  ProductInfoModalData({
    this.headerSubtitle,
    this.headerTitle,
    this.priceSubTitle,
    this.priceTitle,
    this.imageUrl,
    this.title,
    this.description,
    this.actionList,
    this.productType,
    this.productPoints,
  });

  final String? headerSubtitle;
  final String? headerTitle;
  final String? priceSubTitle;
  final String? priceTitle;
  final String? imageUrl;
  final String? title;
  final String? description;
  final String? productType;
  final List<Action>? actionList;
  final List<ProductPoints>? productPoints;

  static ProductInfoModalData fromJson(json) {
    if (json == null) return ProductInfoModalData();
    return ProductInfoModalData(
      productType: json['productType'],
      headerSubtitle: json['headerSubtitle'],
      headerTitle: json['headerTitle'],
      priceSubTitle: json["priceSubTitle"],
      priceTitle: json["priceTitle"],
      description: json["description"],
      imageUrl: json["imageUrl"],
      title: json["title"],
      actionList: json['actionList'] != null
          ? json['actionList'].map<Action>((action) {
              return Action.fromJson(action);
            }).toList()
          : [],
      productPoints: json['productPoints'] != null
          ? json['productPoints'].map<ProductPoints>((point) {
              return ProductPoints.fromJson(point);
            }).toList()
          : [],
    );
  }
}

showProductInfoModal({
  required BuildContext context,
  required ProductInfoModalData productInfoModalData,
  CanvasTheme? themeType,
}) {
  RepositoryProvider.of<AnalyticsRepository>(context).logPageViewEvent(
    pageId: "product_info_modal",
    eventInfo: {
      "action": "Modal Opened",
      "modalFrom": "Product Info Modal",
      "productType": productInfoModalData.productType
    },
  );

  showModalBottomSheet<void>(
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(20),
        topRight: Radius.circular(20),
      ),
    ),
    isScrollControlled: true,
    context: context,
    builder: (context) {
      return Padding(
        padding: MediaQuery.of(context).viewInsets,
        child: Wrap(
          children: [
            BlurView(
              backgroundColor: Colors.transparent,
              opacity: 0.8,
              blurType: BlurType.HIGH,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Align(
                    alignment: Alignment.center,
                    child: Container(
                      margin: const EdgeInsets.symmetric(vertical: Spacings.x2),
                      height: 2.5,
                      width: 55,
                      alignment: Alignment.topCenter,
                      color: Colors.white.withOpacity(0.5),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(
                        top: Spacings.x1,
                        left: Spacings.x4,
                        right: Spacings.x4),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (productInfoModalData.headerSubtitle != null)
                              Text(
                                productInfoModalData.headerSubtitle ?? "",
                                style: AuroraTheme.of(context).textStyle(
                                  TypescaleValues.P6,
                                  color: Colors.white,
                                ),
                                textAlign: TextAlign.left,
                              ),
                            if (productInfoModalData.headerTitle != null)
                              Text(
                                productInfoModalData.headerTitle ?? "",
                                style: AuroraTheme.of(context).textStyle(
                                  TypescaleValues.H9,
                                  color: Color.fromRGBO(253, 214, 80, 1),
                                ),
                                textAlign: TextAlign.left,
                              ),
                          ],
                        ),
                        Column(
                          mainAxisAlignment: MainAxisAlignment.end,
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            if (productInfoModalData.priceSubTitle != null)
                              Text(
                                productInfoModalData.priceSubTitle ?? "",
                                style: AuroraTheme.of(context).textStyle(
                                  TypescaleValues.TAGTEXT,
                                  color: Colors.white60,
                                ),
                                textAlign: TextAlign.left,
                              ),
                            if (productInfoModalData.priceTitle != null)
                              Text(
                                productInfoModalData.priceTitle ?? "",
                                style: AuroraTheme.of(context).textStyle(
                                  TypescaleValues.H2,
                                  color: Colors.white,
                                ),
                                textAlign: TextAlign.left,
                              ),
                          ],
                        ),
                      ],
                    ),
                  ),
                  if (productInfoModalData.imageUrl != null)
                    Padding(
                      padding: const EdgeInsets.only(
                          top: Spacings.x4,
                          left: Spacings.x4,
                          right: Spacings.x4),
                      child: CFNetworkImage(
                        imageUrl: getImageUrl(context,
                            imagePath: productInfoModalData.imageUrl),
                        fit: BoxFit.fitWidth,
                        width: scale(context, 335),
                        errorWidget: (context, url, error) =>
                            const Icon(Icons.error),
                      ),
                    ),
                  if (productInfoModalData.title != null)
                    Padding(
                      padding: const EdgeInsets.only(
                          top: Spacings.x3,
                          left: Spacings.x4,
                          right: Spacings.x4),
                      child: Text(
                        productInfoModalData.title ?? "",
                        style: AuroraTheme.of(context).textStyle(
                          TypescaleValues.H1,
                          color: Colors.white,
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                  if (productInfoModalData.description != null)
                    Padding(
                      padding: const EdgeInsets.only(
                          top: Spacings.x1,
                          left: Spacings.x4,
                          right: Spacings.x4),
                      child: Text(
                        productInfoModalData.description ?? "",
                        style: AuroraTheme.of(context).textStyle(
                          TypescaleValues.P1,
                          color: Colors.white60,
                        ),
                        textAlign: TextAlign.left,
                      ),
                    ),
                  if (productInfoModalData.productPoints != null &&
                      productInfoModalData.productPoints!.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(
                          top: Spacings.x4,
                          left: Spacings.x4,
                          right: Spacings.x4),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: productInfoModalData.productPoints!
                            .map<Widget>((data) {
                          return getBulletPoints(context, data);
                        }).toList(),
                      ),
                    ),
                  SizedBox(
                    height: Spacings.x4,
                  ),
                  if (productInfoModalData.actionList != null &&
                      productInfoModalData.actionList!.isNotEmpty)
                    FloatingTwinButton(
                      titleText:
                          productInfoModalData.actionList![0].description,
                      data: productInfoModalData.actionList,
                      onPress: (Action action) {
                        if (Navigator.canPop(context)) {
                          Navigator.pop(context);
                        } else {
                          ActionBloc actionBloc =
                              BlocProvider.of<ActionBloc>(context);
                          actionBloc
                              .add(CloseApplicationEvent(shouldReset: false));
                        }
                        clickActionWithAnalytics(action, context, null, {
                          "modal": "Product Info Modal",
                          "productType": productInfoModalData.productType,
                        });
                      },
                    )
                ],
              ),
            ),
          ],
        ),
      );
    },
  );
}

Widget getBulletPoints(BuildContext context, ProductPoints data) {
  return Padding(
    padding: const EdgeInsets.only(bottom: Spacings.x4),
    child: Row(
      children: [
        if (data.imageUrl != null)
          CFNetworkImage(
            imageUrl: getImageUrl(context, imagePath: data.imageUrl),
            fit: BoxFit.fill,
            width: 30,
            height: 30,
            errorWidget: (context, url, error) => const Icon(Icons.error),
          ),
        SizedBox(
          width: Spacings.x2,
        ),
        if (data.title != null)
          Expanded(
            child: Text(
              data.title ?? "",
              style: AuroraTheme.of(context).textStyle(TypescaleValues.P1),
            ),
          ),
      ],
    ),
  );
}

import 'package:common/action/action_handler.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/alert_view.dart';
import 'package:common/ui/atoms/secondary_button.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:flutter/material.dart' hide Action;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/clp/clp_bloc.dart';
import 'package:transform/blocs/clp/events.dart';

showRecordingConsentModal(
    {required BuildContext context,
    String? description,
    String? title,
    String? image,
    String? firstButtonTitle,
    String? secondButtonTitle,
    Action? joinCallAction,
    String? patientId,
    String? appointmentId}) {
  showModalBottomSheet<void>(
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(20),
        topRight: Radius.circular(20),
      ),
    ),
    isScrollControlled: true,
    context: context,
    builder: (context) {
      return BlurView(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: Spacings.x4),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                margin: const EdgeInsets.symmetric(vertical: Spacings.x2),
                height: 2.5,
                width: 55,
                alignment: Alignment.topCenter,
                color: Colors.white.withOpacity(0.5),
              ),
              Padding(
                padding: const EdgeInsets.only(top: Spacings.x6),
                child: Text(
                  title ?? "",
                  style: AuroraTheme.of(context).textStyle(
                    TypescaleValues.H2,
                    color: Colors.white,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              if (image != null)
                Padding(
                  padding: const EdgeInsets.only(top: Spacings.x5),
                  child: CFNetworkImage(
                    imageUrl: getImageUrl(context, imagePath: image),
                    fit: BoxFit.contain,
                    height: scale(context, 45),
                    width: scale(context, 30),
                    placeholder: (BuildContext context, String url) {
                      return Container();
                    },
                  ),
                ),
              Padding(
                padding: const EdgeInsets.only(top: Spacings.x5),
                child: Text(
                  description ?? "",
                  style: AuroraTheme.of(context).textStyle(
                    TypescaleValues.P5,
                    color: Colors.white.withOpacity(0.6),
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(top: Spacings.x8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      width: scale(context, 160),
                      child: SecondaryButton(
                        () {
                          final coachBloc =
                              BlocProvider.of<CoachCLPBloc>(context);
                          coachBloc.add(SubmitRecordConsentEvent(
                              preference: false,
                              patientId: patientId ?? "",
                              appointmentId: appointmentId ?? "",
                          ));
                          Navigator.pop(context);
                          if (joinCallAction != null) {
                            ActionBloc actionBloc =
                                BlocProvider.of<ActionBloc>(context);
                            PerformActionEvent event =
                                PerformActionEvent(joinCallAction);
                            actionBloc.add(event);
                          } else {
                            showErrorAlert(
                                context: context,
                                title: "Call To Coach Failed");
                          }
                        },
                        firstButtonTitle ?? "NO",
                        height: 50,
                      ),
                    ),
                    Container(
                      height: 50,
                      width: scale(context, 160),
                      child: PrimaryButton(
                        () {
                          final coachBloc =
                              BlocProvider.of<CoachCLPBloc>(context);
                          coachBloc
                              .add(SubmitRecordConsentEvent(
                              preference: true,
                              patientId: patientId ?? "",
                              appointmentId: appointmentId ?? "",));
                          Navigator.pop(context);
                          if (joinCallAction != null) {
                            ActionBloc actionBloc =
                                BlocProvider.of<ActionBloc>(context);
                            PerformActionEvent event =
                                PerformActionEvent(joinCallAction);
                            actionBloc.add(event);
                          } else {
                            showErrorAlert(
                                context: context,
                                title: "Call To Coach Failed");
                          }
                        },
                        secondButtonTitle ?? "YES, I AGREE",
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(
                height: 20,
              ),
            ],
          ),
        ),
      );
    },
  );
}

import 'package:common/action/action_handler.dart';
import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/blocs/checkout_v2/checkout_v2_bloc.dart';
import 'package:common/blocs/navigation/navigation_bloc.dart';
import 'package:common/blocs/navigation/state.dart';
import 'package:common/model/error_prop_model.dart';
import 'package:common/ui/atoms/floating_twin_button.dart';
import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/page/basic_page_container.dart';
import 'package:common/ui/screens/error_screen.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/user/user_repository.dart';
import 'package:flutter/material.dart' hide Action;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:transform/blocs/bootcamp_pulse/bootcamp_pulse_clp_bloc.dart';
import 'package:transform/blocs/bootcamp_pulse/events.dart';
import 'package:transform/blocs/bootcamp_pulse/state.dart';
import 'package:transform/constants/constants.dart';
import 'package:scroll_to_index/scroll_to_index.dart';
import 'package:navigation_history_observer/navigation_history_observer.dart';

class BootcampPulseClpArguments {
  String? subCategoryCode;
  String? slotId;
  String? centerId;
  String? batchId;
  String? pageId;

  BootcampPulseClpArguments(Map<String, dynamic> payload) {
    this.subCategoryCode = payload["subCategoryCode"];
    this.slotId = payload["slotId"];
    this.centerId = payload["centerId"];
    this.batchId = payload["batchId"];
    this.pageId = payload["pageId"];
  }
}

class BootcampPulseClp extends StatefulWidget {
  final bool showTitleBar;
  final TabMode tabMode;

  const BootcampPulseClp(
      {Key? key, this.tabMode = TabMode.FULL, this.showTitleBar = true})
      : super(key: key);

  @override
  _BootcampPulseClpState createState() => _BootcampPulseClpState();
}

class _BootcampPulseClpState extends State<BootcampPulseClp>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  late final AutoScrollController? scrollController;

  BootcampPulseClpArguments? getBootcampPulseClpArguments() {
    final ScreenArguments? args =
        ModalRoute.of(context)!.settings.arguments as ScreenArguments?;
    if (args != null) {
      return BootcampPulseClpArguments(args.params);
    }
    return null;
  }

  @override
  void initState() {
    super.initState();
    scrollController = AutoScrollController();
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      BootcampPulseClpArguments? arguments = getBootcampPulseClpArguments();
      saveSlots(arguments);
      final bootcampPulseClpBloc =
          BlocProvider.of<BootcampPulseClpBloc>(context);
      bootcampPulseClpBloc.add(ResetBootcampPulseClpEvent());
      bootcampPulseClpBloc.add(
          LoadBootcampPulseClpEvent(showLoader: true, arguments: arguments));
    });
  }

  @override
  void dispose() {
    scrollController?.dispose();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  saveSlots(BootcampPulseClpArguments? arguments) {
    if (arguments != null &&
        arguments.slotId != null &&
        arguments.slotId!.isNotEmpty) {
      CheckoutV2Bloc checkoutBloc = BlocProvider.of<CheckoutV2Bloc>(context);
      checkoutBloc.selectedSlotId = arguments.slotId;
      checkoutBloc.selectedCenterId = arguments.centerId;
      checkoutBloc.selectedBatchId = arguments.batchId;
      RepositoryProvider.of<AnalyticsRepository>(context)
          .logButtonClickEvent(extraInfo: {
        "trigger": "SAVE_BATCH",
        "selected": true,
        "slotId": arguments.slotId,
        "centerId": arguments.centerId,
        "batchId": arguments.batchId,
        "pageId": "bootcamp_pulse_pre"
      });
    }
  }

  refresh({required BuildContext context, bool showLoader = false}) {
    BootcampPulseClpArguments? arguments = getBootcampPulseClpArguments();
    saveSlots(arguments);
    final bootcampPulseClpBloc = BlocProvider.of<BootcampPulseClpBloc>(context);
    bootcampPulseClpBloc.add(LoadBootcampPulseClpEvent(
        showLoader: showLoader, arguments: arguments));
  }

  onBackPress() {
    if (Navigator.canPop(context)) {
      Navigator.pop(context);
    } else if (!Navigator.canPop(context)) {
      ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
      actionBloc.add(CloseApplicationEvent(shouldReset: true));
    }
  }

  logPageView() {
    AnalyticsRepository analyticsRepository =
        RepositoryProvider.of<AnalyticsRepository>(context);
    analyticsRepository.logPageViewEvent(
        pageId: EnumToString.convertToString(RouteNames.lift), eventInfo: {});
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    switch (state) {
      case AppLifecycleState.resumed:
        logPageView();
        refresh(context: context, showLoader: false);
        break;
      case AppLifecycleState.inactive:
        break;
      case AppLifecycleState.paused:
        break;
      case AppLifecycleState.detached:
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      body: Stack(
        children: [
          Aurora(
            context: context,
            size: MediaQuery.of(context).size,
          ),
          Positioned.fill(
            child: BlocListener<NavigationBloc, NavigationState>(
              listener: (context, state) {
                if (state is NavigationStackUpdated &&
                    state.action == NavigationStackAction.pop &&
                    state.route?.settings.name ==
                        '/${EnumToString.convertToString(RouteNames.bootcamp_pulse_pre)}') {
                  refresh(context: context, showLoader: false);
                }
              },
              child: BlocBuilder<BootcampPulseClpBloc, BootcampPulseClpState>(
                builder: (context, state) {
                  List<dynamic> _widgets = [];
                  if (state is BootcampPulseClpLoadedState) {
                    _widgets = state.screenData.widgets ?? [];
                  } else if (state is BootcampPulseClpLoadingState &&
                      state.screenData != null) {
                    _widgets = state.screenData!.widgets ?? [];
                  } else if (state is BootcampPulseClpErrorState) {
                    return Stack(
                      children: [
                        Container(
                          color: Colors.black,
                          child: ErrorScreen(
                              errorInfo: state.errorInfo ?? UnknownError()),
                        ),
                        Positioned(
                          right: 5,
                          top: 30,
                          child: IconButton(
                            onPressed: () {
                              if (Navigator.canPop(context)) {
                                Navigator.pop(context);
                              } else if (!Navigator.canPop(context)) {
                                ActionBloc actionBloc =
                                    BlocProvider.of<ActionBloc>(context);
                                actionBloc.add(
                                    CloseApplicationEvent(shouldReset: false));
                              }
                            },
                            icon: const Icon(
                              Icons.clear,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    );
                  }
                  return BasicPageContainer(
                    shouldBuildWidget: true,
                    showTitleBar: widget.showTitleBar,
                    scrollController: scrollController,
                    widgetData: _widgets,
                    onBackPressed: () {
                      onBackPress();
                    },
                    enableFloatingCTAAnimation: false,
                    title: state is BootcampPulseClpLoadedState
                        ? state.screenData.title
                        : "",
                    titleBarRightActions: state
                                is BootcampPulseClpLoadedState &&
                            state.screenData.rightAction != null
                        ? [
                            InkWell(
                              onTap: () {
                                RepositoryProvider.of<AnalyticsRepository>(
                                        context)
                                    .logWidgetClick(
                                        widgetInfo: null,
                                        extraInfo: {
                                      "pageId": "bootcamp_pulse_pre",
                                      "actionType": "rightAction"
                                    });
                                ActionBloc actionBloc =
                                    BlocProvider.of<ActionBloc>(context);
                                PerformActionEvent event = PerformActionEvent(
                                    state.screenData.rightAction!);
                                actionBloc.add(event);
                              },
                              child: Text(
                                state.screenData.rightAction!.title ?? "",
                                style: AuroraTheme.of(context)
                                    .textStyle(TypescaleValues.P3),
                              ),
                            ),
                          ]
                        : null,
                    floatingCTA: state is BootcampPulseClpLoadedState &&
                            state.screenData.floatingActions != null &&
                            state.screenData.floatingActions!.isNotEmpty
                        ? FloatingTwinButton(
                            titleText: state
                                .screenData.floatingActions![0].description,
                            data: state.screenData.floatingActions,
                            onPress: (Action action) {
                              BootcampPulseClpArguments? arguments =
                              getBootcampPulseClpArguments();
                              saveSlots(arguments);
                              RepositoryProvider.of<AnalyticsRepository>(
                                      context)
                                  .logButtonClickEvent(extraInfo: {
                                "title": action.title,
                                "type": action.type,
                                "actionUrl": action.url,
                                "pageId": "bootcamp_pulse_pre",
                                ...?action.analyticsData,
                              });
                              ActionBloc actionBloc = BlocProvider.of(context);
                              PerformActionEvent event =
                                  PerformActionEvent(action);
                              actionBloc.add(event);
                            })
                        : state is BootcampPulseClpLoadedState &&
                                state.screenData.footerAction != null
                            ? Padding(
                                padding: const EdgeInsets.symmetric(
                                    vertical: Spacings.x4,
                                    horizontal: Spacings.x4),
                                child: PrimaryButton(() {
                                  if (state.screenData.footerAction != null) {
                                    BootcampPulseClpArguments? arguments =
                                        getBootcampPulseClpArguments();
                                    saveSlots(arguments);
                                    RepositoryProvider.of<AnalyticsRepository>(
                                            context)
                                        .logWidgetClick(
                                            widgetInfo: null,
                                            extraInfo: {
                                          "pageId": "bootcamp_pulse_pre",
                                          "title": state
                                              .screenData.footerAction!.title,
                                          "type": state
                                              .screenData.footerAction!.type,
                                          "actionUrl": state
                                              .screenData.footerAction!.url,
                                        });
                                    ActionBloc actionBloc =
                                        BlocProvider.of<ActionBloc>(context);
                                    PerformActionEvent event =
                                        PerformActionEvent(
                                            state.screenData.footerAction!);
                                    actionBloc.add(event);
                                  }
                                }, state.screenData.footerAction?.title ?? ""),
                              )
                            : null,
                    showLoader: false,
                  );
                },
              ),
            ),
          ),
          Positioned.fill(
            child: BlocBuilder<BootcampPulseClpBloc, BootcampPulseClpState>(
              builder: (context, state) {
                if (state is BootcampPulseClpLoadingState && state.showLoader) {
                  return Center(child: FancyLoadingIndicator());
                }
                return Container();
              },
            ),
          ),
        ],
      ),
    );
  }
}

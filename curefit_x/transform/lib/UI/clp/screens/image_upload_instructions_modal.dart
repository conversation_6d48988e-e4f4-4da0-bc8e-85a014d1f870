import 'package:common/ui/alert_view.dart';
import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/toast.dart';
import 'package:flutter/material.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/user_image/events.dart';
import 'package:transform/blocs/user_image/user_image_bloc.dart';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';
import 'package:image_cropper/image_cropper.dart';

showUploadImageModal(
    {required BuildContext context, Map<String, dynamic>? modalInfo}) {
  final CanvasTheme? themeType = modalInfo?['themeType'] != null
      ? EnumToString.fromString(CanvasTheme.values, modalInfo?['themeType'])
      : null;
  final String? title = modalInfo?['title'];
  final String? buttonTitle = modalInfo?['buttonTitle'];
  final String pictureTag = modalInfo?['pictureTag'] ?? "";
  final bool replacePic = modalInfo?['replacePic'] ?? false;
  final List<String> instructions = modalInfo?['instructions'] != null
      ? modalInfo!['instructions'].map<String>((instruction) {
          return instruction.toString();
        }).toList()
      : [];

  void uploadUserImage() async {
    final ImagePicker _picker = ImagePicker();
    String? selectedOption;
    return showModalBottomSheet<void>(
      isScrollControlled: true,
      enableDrag: true,
      context: context,
      builder: (context) {
        return Padding(
          padding: MediaQuery.of(context).viewInsets,
          child: Wrap(
            children: [
              BlurView(
                backgroundColor: themeType == CanvasTheme.NIGHT
                    ? Color.fromRGBO(57, 68, 116, 1)
                    : Color.fromRGBO(32, 77, 89, 1),
                opacity: 0.8,
                blurType: BlurType.HIGH,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: Spacings.x4),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Align(
                        alignment: Alignment.center,
                        child: Container(
                          margin:
                              const EdgeInsets.symmetric(vertical: Spacings.x2),
                          height: 2.5,
                          width: 55,
                          alignment: Alignment.topCenter,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(5),
                            color: Colors.white.withOpacity(0.3),
                          ),
                        ),
                      ),
                      SizedBox(height: Spacings.x3),
                      InkWell(
                        onTap: () {
                          selectedOption = "Gallery";
                          Navigator.pop(context, "Gallery");
                        },
                        child: Container(
                          padding: EdgeInsets.symmetric(vertical: Spacings.x4),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Icon(
                                Icons.image_outlined,
                                color: Colors.white,
                                size: 18,
                              ),
                              SizedBox(
                                width: Spacings.x2,
                              ),
                              Text(
                                "GALLERY",
                                style: AuroraTheme.of(context)
                                    .textStyle(TypescaleValues.P3),
                              ),
                            ],
                          ),
                        ),
                      ),
                      InkWell(
                        onTap: () {
                          selectedOption = "Camera";
                          Navigator.pop(context, "Camera");
                        },
                        child: Container(
                          padding: EdgeInsets.symmetric(vertical: Spacings.x4),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Icon(
                                Icons.camera_alt_outlined,
                                color: Colors.white,
                                size: 18,
                              ),
                              SizedBox(
                                width: Spacings.x2,
                              ),
                              Text(
                                "CAMERA",
                                style: AuroraTheme.of(context)
                                    .textStyle(TypescaleValues.P3),
                              ),
                            ],
                          ),
                        ),
                      ),
                      InkWell(
                        onTap: () {
                          selectedOption = "Cancel";
                          Navigator.pop(context, "Cancel");
                        },
                        child: Container(
                          padding: EdgeInsets.symmetric(vertical: Spacings.x4),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Icon(
                                Icons.close_rounded,
                                color: Colors.white,
                                size: 18,
                              ),
                              SizedBox(
                                width: Spacings.x2,
                              ),
                              Text(
                                "CANCEL",
                                style: AuroraTheme.of(context)
                                    .textStyle(TypescaleValues.P3),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    ).then((value) async {
      XFile? newSelection;
      switch (selectedOption) {
        case "Gallery":
          {
            try {
              newSelection = await _picker.pickImage(
                  source: ImageSource.gallery, maxWidth: 1000, maxHeight: 1000);
              if (newSelection != null &&
                  await newSelection.length() > 10000000) {
                Toast.show(
                    "File's size is too large. Please try uploading a smaller image.",
                    context);
                newSelection = null;
              }
            } on PlatformException catch (exception) {
              showDialog(
                  barrierDismissible: false,
                  context: context,
                  builder: (context) => AlertDialog(
                        title: Text("Error In Selecting An Image",
                            style: AuroraTheme.of(context).textStyle(
                                TypescaleValues.H1,
                                color: Colors.black)),
                        content: Text(
                            exception.message ?? "Please try again later",
                            style: AuroraTheme.of(context).textStyle(
                                TypescaleValues.P1,
                                color: Colors.black)),
                        actions: [
                          TextButton(
                              onPressed: () {
                                Navigator.pop(context);
                              },
                              child: const Text("OK"))
                        ],
                      ));
            } catch (err) {
              showDialog(
                  barrierDismissible: false,
                  context: context,
                  builder: (context) => AlertDialog(
                        title: Text("Error In Selecting An Image",
                            style: AuroraTheme.of(context).textStyle(
                                TypescaleValues.H1,
                                color: Colors.black)),
                        content: Text("Please try again later",
                            style: AuroraTheme.of(context).textStyle(
                                TypescaleValues.P1,
                                color: Colors.black)),
                        actions: [
                          TextButton(
                              onPressed: () {
                                Navigator.pop(context);
                                // if (onClose != null) onClose();
                              },
                              child: const Text("OK"))
                        ],
                      ));
            }
            break;
          }
        case "Camera":
          {
            try {
              newSelection = await _picker.pickImage(
                  source: ImageSource.camera, maxHeight: 1000, maxWidth: 1000);
            } catch (err) {
              Toast.show("Failed to upload: " + err.toString(), context);
            }
            break;
          }
      }
      if (newSelection != null) {
        CroppedFile? updatedImage = await ImageCropper().cropImage(
            sourcePath: newSelection.path,
            aspectRatio: const CropAspectRatio(ratioX: 1, ratioY: 1.5));
        if (updatedImage != null) {
          BlocProvider.of<UserImageBloc>(context)
              .add(UploadUserImageEvent(filePath: updatedImage.path, pictureTag: pictureTag, replacePic: replacePic));
          updatedImage.path;
        } else {
          BlocProvider.of<UserImageBloc>(context)
              .add(UploadUserImageEvent(filePath: newSelection.path, pictureTag: pictureTag, replacePic: replacePic));
        }
      }
    }).catchError((err) {
      showErrorAlert(
          context: context,
          title: "Image Upload Failed",
          onClose: () {
            Navigator.pop(context);
          });
    });
  }

  showModalBottomSheet<void>(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      isScrollControlled: true,
      context: context,
      builder: (context) {
        return Padding(
          padding: MediaQuery.of(context).viewInsets,
          child: Wrap(
            children: [
              BlurView(
                backgroundColor: themeType == CanvasTheme.NIGHT
                    ? Color.fromRGBO(57, 68, 116, 1)
                    : Color.fromRGBO(32, 77, 89, 1),
                opacity: 0.8,
                blurType: BlurType.HIGH,
                child: Container(
                  width: double.infinity,
                  padding: EdgeInsets.symmetric(horizontal: Spacings.x4),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Align(
                        alignment: Alignment.center,
                        child: Container(
                          margin:
                              const EdgeInsets.symmetric(vertical: Spacings.x2),
                          height: 2.5,
                          width: 55,
                          alignment: Alignment.topCenter,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(5),
                            color: Colors.white.withOpacity(0.3),
                          ),
                        ),
                      ),
                      SizedBox(
                        height: Spacings.x6,
                      ),
                      Text(
                        title ?? "Tips to update your picture",
                        style: AuroraTheme.of(context)
                            .textStyle(TypescaleValues.H2),
                        textAlign: TextAlign.left,
                      ),
                      SizedBox(
                        height: Spacings.x4,
                      ),
                      if (instructions.length > 0)
                        ...instructions
                            .map<Widget>(
                              (instruction) => Padding(
                                padding: EdgeInsets.only(bottom: Spacings.x4),
                                child: Row(
                                  children: [
                                    BlurView(
                                      child: Container(
                                        decoration: BoxDecoration(
                                          shape: BoxShape.circle,
                                        ),
                                        width: 35,
                                        height: 35,
                                        child: Icon(
                                          Icons.check_circle_outline_rounded,
                                          color: Colors.white,
                                          size: 20,
                                        ),
                                      ),
                                    ),
                                    SizedBox(
                                      width: Spacings.x2,
                                    ),
                                    Expanded(
                                      child: Text(
                                        instruction,
                                        style: AuroraTheme.of(context)
                                            .textStyle(TypescaleValues.P4,
                                                color: Colors.white60),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            )
                            .toList(),
                      Padding(
                        padding:
                            const EdgeInsets.symmetric(vertical: Spacings.x2),
                        child: PrimaryButton(() {
                          Navigator.of(context).pop();
                          uploadUserImage();
                        }, buttonTitle ?? "UPDATE PICTURE"),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      });
}

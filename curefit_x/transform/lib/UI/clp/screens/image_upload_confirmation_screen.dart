import 'package:common/action/action_handler.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/constants/action_constants.dart';
import 'package:common/font/cf_icons.dart';
import 'package:common/ui/animated_check.dart';
import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/atoms/secondary_button.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/UI/clp/widgets/steps_widget.dart';
import 'package:transform/blocs/user_image/events.dart';
import 'package:transform/blocs/user_image/state.dart';
import 'package:transform/blocs/user_image/user_image_bloc.dart';

class ImageUploadConfirmationScreenArguments {
  String? subCategoryCode;
  String? pictureTag;

  ImageUploadConfirmationScreenArguments(Map<String, dynamic> payload) {
    this.subCategoryCode =
    payload["subCategoryCode"] != null ? payload["subCategoryCode"] : "";
    this.pictureTag =
    payload["pictureTag"] != null ? payload["pictureTag"] : "";
  }
}

class ImageUploadConfirmationScreen extends StatefulWidget {
  const ImageUploadConfirmationScreen({Key? key}) : super(key: key);

  @override
  State<ImageUploadConfirmationScreen> createState() =>
      _ImageUploadConfirmationScreenState();
}

class _ImageUploadConfirmationScreenState
    extends State<ImageUploadConfirmationScreen> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController =
        AnimationController(vsync: this, duration: Duration(seconds: 2));
    _animation = new Tween<double>(begin: 0, end: 1).animate(
        new CurvedAnimation(
            parent: _animationController, curve: Curves.easeInOutCirc));
    _animationController.forward();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      final ScreenArguments? args =
      ModalRoute.of(context)!.settings.arguments as ScreenArguments?;
      String subCategoryCode = "";
      String pictureTag = "";
      if (args != null) {
        ImageUploadConfirmationScreenArguments arguments = ImageUploadConfirmationScreenArguments(args.params);
        subCategoryCode = arguments.subCategoryCode ?? "";
        pictureTag = arguments.pictureTag ?? "";
      }
      BlocProvider.of<UserImageBloc>(context)
          .add(LoadUserImageConfirmationEvent(subCategoryCode: subCategoryCode,pictureTag: pictureTag));
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  CanvasTheme draftTheme() {
    TimeOfDay now = TimeOfDay.now();
    return now.hour > 4 && now.hour < 19 ? CanvasTheme.TEAL : CanvasTheme.NIGHT;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(
            50.0 + AuroraTheme.of(context).embeddedSafeArea.top),
        child: Padding(
          padding: EdgeInsets.only(
              top: AuroraTheme.of(context).embeddedSafeArea.top),
          child: AppBar(
            backgroundColor: Colors.transparent,
            iconTheme: IconThemeData(color: Colors.white),
            titleSpacing: 0,
            centerTitle: false,
            title: Text("Confirmation",
                style: AuroraTheme.of(context).textStyle(TypescaleValues.H1)),
            leading: IconButton(
              icon: const Icon(
                CFIcons.chevron_left,
                color: Colors.white,
                size: 20,
                semanticLabel: "chevron_left",
              ),
              onPressed: () {
                if (Navigator.canPop(context)) {
                  Navigator.pop(context);
                } else if (!Navigator.canPop(context)) {
                  ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
                  actionBloc.add(CloseApplicationEvent(shouldReset: true));
                }
              },
            ),
          ),
        ),
      ),
      body: LayoutBuilder(
        builder: (context, constraints) {
          AuroraThemeData themeData = AuroraTheme.of(context);
          CanvasTheme canvasTheme = draftTheme();
          themeData.canvasTheme = canvasTheme;
          if (constraints.maxWidth > 0) {
            return Container(
              child: Stack(
                children: [
                  Aurora(
                    size: constraints.biggest,
                    context: context,
                  ),
                  BlocBuilder<UserImageBloc, UserImageState>(
                    builder: (context, state) {
                      if (state is UserImageConfirmationLoadingState) {
                        return FancyLoadingIndicator();
                      } else if (state is UserImageConfirmationLoadedState) {
                        return SingleChildScrollView(
                          child: Container(
                            height: MediaQuery.of(context).size.height,
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Container(),
                                Padding(
                                  padding: const EdgeInsets.only(
                                      left: Spacings.x4,
                                      right: Spacings.x4,
                                      top: Spacings.x10),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      if (state.userImageConfirmationViewData
                                              .showIcon ??
                                          true)
                                        ClipRRect(
                                          borderRadius: BorderRadius.all(
                                              Radius.circular(40)),
                                          child: BlurView(
                                            child: Container(
                                              margin: EdgeInsets.all(15),
                                              height: 50,
                                              width: 50,
                                              child: AnimatedCheck(
                                                progress: _animation,
                                                strokeWidth: 4,
                                                size: 70,
                                                color: Color.fromRGBO(
                                                    15, 228, 152, 1),
                                              ),
                                            ),
                                          ),
                                        ),
                                      Padding(
                                        padding: const EdgeInsets.only(
                                            top: Spacings.x4),
                                        child: Text(
                                          state.userImageConfirmationViewData
                                                  .title ??
                                              "",
                                          style: AuroraTheme.of(context)
                                              .textStyle(TypescaleValues.H1),
                                          textAlign: TextAlign.center,
                                        ),
                                      ),
                                      Padding(
                                        padding: const EdgeInsets.only(
                                            top: Spacings.x1),
                                        child: Text(
                                          state.userImageConfirmationViewData
                                                  .subtitle ??
                                              "",
                                          style: AuroraTheme.of(context)
                                              .textStyle(TypescaleValues.P4,
                                                  color: Colors.white60),
                                          textAlign: TextAlign.center,
                                        ),
                                      ),
                                      if (state.userImageConfirmationViewData
                                              .stepCard !=
                                          null)
                                        Padding(
                                          padding: const EdgeInsets.only(
                                              top: Spacings.x8,
                                              left: Spacings.x4,
                                              right: Spacings.x4),
                                          child: BlurView(
                                            borderRadius: 10,
                                            child: Container(
                                              padding: const EdgeInsets.all(
                                                  Spacings.x3),
                                              child: UploadPictureStepCard(state
                                                  .userImageConfirmationViewData
                                                  .stepCard!),
                                            ),
                                          ),
                                        ),
                                    ],
                                  ),
                                ),
                                BlurView(
                                  borderRadius: 0,
                                  child: Container(
                                    padding: const EdgeInsets.all(Spacings.x4),
                                    child: Column(
                                      children: [
                                        if (state.userImageConfirmationViewData
                                                .doneAction !=
                                            null)
                                          PrimaryButton(() {
                                            if (state
                                                    .userImageConfirmationViewData
                                                    .doneAction!
                                                    .type !=
                                                ActionTypes.EMPTY_ACTION) {
                                              ActionBloc actionBloc =
                                                  BlocProvider.of<ActionBloc>(
                                                      context);
                                              PerformActionEvent event =
                                                  PerformActionEvent(state
                                                      .userImageConfirmationViewData
                                                      .doneAction!);
                                              actionBloc.add(event);
                                            } else {
                                              if (Navigator.canPop(context)) {
                                                Navigator.pop(context);
                                              } else if (!Navigator.canPop(
                                                  context)) {
                                                ActionBloc actionBloc =
                                                    BlocProvider.of<ActionBloc>(
                                                        context);
                                                actionBloc.add(
                                                    CloseApplicationEvent());
                                              }
                                            }
                                          },
                                              state.userImageConfirmationViewData
                                                      .doneAction!.title ??
                                                  ""),
                                        if (state.userImageConfirmationViewData
                                                .reUploadAction !=
                                            null)
                                          Padding(
                                            padding: const EdgeInsets.only(
                                                top: Spacings.x2),
                                            child: SecondaryButton(
                                              () {
                                                if (Navigator.canPop(context)) {
                                                  Navigator.pop(context);
                                                } else if (!Navigator.canPop(
                                                    context)) {
                                                  ActionBloc actionBloc =
                                                      BlocProvider.of<
                                                          ActionBloc>(context);
                                                  actionBloc.add(
                                                      CloseApplicationEvent());
                                                }
                                                if (state
                                                        .userImageConfirmationViewData
                                                        .reUploadAction!
                                                        .type !=
                                                    ActionTypes.EMPTY_ACTION) {
                                                  ActionBloc actionBloc =
                                                      BlocProvider.of<
                                                          ActionBloc>(context);
                                                  PerformActionEvent event =
                                                      PerformActionEvent(state
                                                          .userImageConfirmationViewData
                                                          .reUploadAction!);
                                                  actionBloc.add(event);
                                                }
                                              },
                                              state.userImageConfirmationViewData
                                                      .reUploadAction!.title ??
                                                  "",
                                              verticalPadding: Spacings.x3,
                                            ),
                                          ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      }
                      return Container();
                    },
                  ),
                ],
              ),
            );
          }
          return Container();
        },
      ),
    );
  }
}

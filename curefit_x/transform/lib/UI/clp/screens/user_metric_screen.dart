import 'dart:io';

import 'package:common/action/action_handler.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/font/cf_icons.dart';
import 'package:common/ui/alert_view.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/organisms/titlebar/classic_title_bar.dart';
import 'package:common/ui/organisms/titlebar/frosty_app_bar.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/widget_factory.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:transform/blocs/metric_input/events.dart';
import 'package:transform/blocs/metric_input/metric_input_bloc.dart';
import 'package:transform/blocs/metric_input/state.dart';
import 'package:common/ui/widgets/error_widget.dart' as CFError;
import 'package:common/util/theme.dart';
import 'package:transform/constants/constants.dart';

class UserMetricScreenArguments {
  String? subCategoryCode;

  UserMetricScreenArguments(Map<String, dynamic> payload) {
    this.subCategoryCode =
        payload["subCategoryCode"] != null ? payload["subCategoryCode"] : "";
  }
}

class UserMetricScreen extends StatefulWidget {
  @override
  State<StatefulWidget> createState() => _UserMetricScreenState();
}

class _UserMetricScreenState extends State<UserMetricScreen>
    with TickerProviderStateMixin {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      final ScreenArguments? args =
          ModalRoute.of(context)!.settings.arguments as ScreenArguments?;
      String subCategoryCode = "";
      if (args != null) {
        UserMetricScreenArguments arguments =
            UserMetricScreenArguments(args.params);
        subCategoryCode = arguments.subCategoryCode ?? "";
      }
      final metricInputBloc = BlocProvider.of<MetricInputBloc>(context);
      metricInputBloc
          .add(LoadMetricInputEvent(subCategoryCode: subCategoryCode));
    });
  }

  Widget buildWidgets() {
    return BlocListener<MetricInputBloc, MetricInputState>(
        listener: (context, state) {
      if (state is MetricInputNotLoaded) {
        showErrorAlert(
            context: context,
            title: state.error,
            onClose: () {
              Navigator.pop(context);
            });
      }
    }, child: BlocBuilder<MetricInputBloc, MetricInputState>(
            builder: (context, state) {
      if (state is MetricInputLoading) {
        return PageLoadingIndicator();
      }
      if (state is MetricInputLoaded) {
        WidgetFactory widgetFactory =
            RepositoryProvider.of<WidgetFactory>(context);
        return Container(
            child: Padding(
                padding: EdgeInsets.only(
                    top: AuroraTheme.of(context).appBarHeight,
                    left: 20,
                    right: 20),
                child: widgetFactory
                    .createWidget(state.userMetricScreen.widgets[0])));
      }
      return Container();
    }));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        extendBodyBehindAppBar: true,
        appBar: PreferredSize(
            preferredSize: Size.fromHeight(
                50.0 + AuroraTheme.of(context).embeddedSafeArea.top),
            child: Padding(
                padding: EdgeInsets.only(
                    top: AuroraTheme.of(context).embeddedSafeArea.top),
                child: AppBar(
                  titleSpacing: 0,
                  centerTitle: false,
                  title: Text("Update your measurements",
                      style: AuroraTheme.of(context)
                          .textStyle(TypescaleValues.H1)),
                  leading: IconButton(
                    icon: const Icon(
                      CFIcons.chevron_left,
                      color: Colors.white,
                      size: 20,
                      semanticLabel: "chevron_left",
                    ),
                    onPressed: () {
                      if (Navigator.canPop(context)) {
                        Navigator.pop(context);
                      } else if (!Navigator.canPop(context)) {
                        ActionBloc actionBloc =
                            BlocProvider.of<ActionBloc>(context);
                        actionBloc
                            .add(CloseApplicationEvent(shouldReset: true));
                      }
                    },
                  ),
                ))),
        body: LayoutBuilder(
          builder: (context, constraints) {
            if (constraints.maxWidth > 0) {
              return Stack(children: [
                Aurora(
                  size: constraints.biggest,
                  context: context,
                ),
                Positioned.fill(
                  child: buildWidgets(),
                ),
              ]);
            }
            return Container();
          },
        ));
  }
}

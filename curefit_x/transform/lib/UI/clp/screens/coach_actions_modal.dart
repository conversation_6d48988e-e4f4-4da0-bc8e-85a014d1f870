import 'package:collection/collection.dart';
import 'package:common/action/action_handler.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:flutter/material.dart' hide Action;
import 'package:common/ui/theme/spacing.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ActionCardData {
  final String? title;
  final String? subtitle;
  final String? imageUrl;
  final String? icon;
  final String? suffixText;
  final String? prefixText;
  final Action? action;

  ActionCardData({
    this.title,
    this.subtitle,
    this.imageUrl,
    this.icon,
    this.suffixText,
    this.prefixText,
    this.action,
  });

  factory ActionCardData.fromJson(json) {
    return ActionCardData(
      title: json['title'],
      subtitle: json['subtitle'],
      imageUrl: json['imageUrl'],
      icon: json['icon'],
      suffixText: json['suffixText'],
      prefixText: json['prefixText'],
      action: json['action'] != null ? Action.fromJson(json['action']) : null,
    );
  }
}

showCoachActionsModal(
    {required BuildContext context, Map<String, dynamic>? modalInfo}) {
  final List<ActionCardData> actionCardList =
      modalInfo?['actionsCardList'] != null
          ? modalInfo!['actionsCardList'].map<ActionCardData>((cardData) {
              return ActionCardData.fromJson(cardData);
            }).toList()
          : [];

  final CanvasTheme? themeType = modalInfo?['themeType'] != null
      ? EnumToString.fromString(CanvasTheme.values, modalInfo?['themeType'])
      : null;

  final String? title = modalInfo?['title'];

  Widget getCoachActionCard(ActionCardData actionCard) {
    bool showBottomText =
        actionCard.prefixText != null || actionCard.suffixText != null;
    return InkWell(
      onTap: () {
        if (actionCard.action != null) {
          ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
          actionBloc.add(new PerformActionEvent(actionCard.action!));
        }
      },
      child: BlurView(
        child: Padding(
          padding: const EdgeInsets.all(Spacings.x4),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              ClipRRect(
                child: CFNetworkImage(
                  width: showBottomText ? 66 : 50,
                  height: showBottomText ? 66 : 50,
                  imageUrl: getImageUrl(context,
                      imagePath: actionCard.imageUrl ?? ""),
                  fit: BoxFit.cover,
                ),
                borderRadius: BorderRadius.circular(showBottomText ? 33 : 25),
              ),
              SizedBox(width: Spacings.x2),
              Expanded(
                child: Container(
                  padding: EdgeInsets.only(right: Spacings.x2),
                  child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(actionCard.title ?? "",
                            style: AuroraTheme.of(context).textStyle(
                                TypescaleValues.P1,
                                color: Colors.white)),
                        SizedBox(height: Spacings.x1),
                        Text(actionCard.subtitle ?? "",
                            style: AuroraTheme.of(context).textStyle(
                                TypescaleValues.P8,
                                color: Colors.white60)),
                        if (showBottomText) ...[
                          Container(
                            margin: EdgeInsets.symmetric(vertical: Spacings.x1),
                            height: 1,
                            color: Colors.white10,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Text(
                                actionCard.prefixText ?? "",
                                style: AuroraTheme.of(context)
                                    .textStyle(TypescaleValues.P6),
                              ),
                              Text(
                                actionCard.suffixText ?? "",
                                style: AuroraTheme.of(context).textStyle(
                                    TypescaleValues.P10,
                                    color: Colors.white60),
                              ),
                            ],
                          ),
                        ]
                      ]),
                ),
              ),
              Center(
                  child: Icon(Icons.arrow_forward_ios_rounded,
                      size: 20, color: Colors.white)),
            ],
          ),
        ),
      ),
    );
  }

  showModalBottomSheet<void>(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      isScrollControlled: true,
      context: context,
      builder: (context) {
        return Padding(
          padding: MediaQuery.of(context).viewInsets,
          child: Wrap(
            children: [
              BlurView(
                backgroundColor: themeType == CanvasTheme.NIGHT
                    ? Color.fromRGBO(57, 68, 116, 1)
                    : Color.fromRGBO(32, 77, 89, 1),
                opacity: 0.8,
                blurType: BlurType.HIGH,
                child: Container(
                  width: double.infinity,
                  padding: EdgeInsets.symmetric(horizontal: Spacings.x4),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        margin:
                            const EdgeInsets.symmetric(vertical: Spacings.x2),
                        height: 2.5,
                        width: 55,
                        alignment: Alignment.topCenter,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(5),
                          color: Colors.white.withOpacity(0.3),
                        ),
                      ),
                      SizedBox(
                        height: Spacings.x6,
                      ),
                      if (title != null)
                        Padding(
                          padding: const EdgeInsets.only(bottom: Spacings.x4),
                          child: Align(
                            alignment: Alignment.topLeft,
                            child: Text(
                              title,
                              style: AuroraTheme.of(context)
                                  .textStyle(TypescaleValues.H2),
                            ),
                          ),
                        ),
                      if (actionCardList.length > 0)
                        ...actionCardList
                            .mapIndexed<Widget>((index, actionCard) => Padding(
                                  padding: EdgeInsets.only(
                                      bottom: index == actionCardList.length - 1
                                          ? Spacings.x2
                                          : Spacings.x4),
                                  child: getCoachActionCard(actionCard),
                                ))
                            .toList(),
                      SizedBox(
                        height: Spacings.x2,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      });
}

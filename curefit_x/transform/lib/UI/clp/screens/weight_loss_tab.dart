import 'package:common/action/action_handler.dart';
import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/blocs/navigation/navigation_bloc.dart';
import 'package:common/blocs/navigation/state.dart';
import 'package:common/blocs/tab/tab_bloc.dart';
import 'package:common/model/error_prop_model.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/screens/error_screen.dart';
import 'package:common/user/user_repository.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/UI/clp/component/weight_loss_tab_container.dart';
import 'package:transform/blocs/weight_loss_tab/events.dart';
import 'package:transform/blocs/weight_loss_tab/models.dart';
import 'package:transform/blocs/weight_loss_tab/state.dart';
import 'package:transform/blocs/weight_loss_tab/weight_loss_tab_bloc.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:transform/constants/constants.dart';
import 'package:navigation_history_observer/navigation_history_observer.dart';
import 'package:shared_preferences/shared_preferences.dart';

class WeightLossTab extends StatefulWidget {
  final bool inTabMode;
  final AppTabs? tab;

  const WeightLossTab({this.inTabMode = false, this.tab, Key? key})
      : super(key: key);

  @override
  State<WeightLossTab> createState() => _WeightLossTabState();
}

class _WeightLossTabState extends State<WeightLossTab>
    with WidgetsBindingObserver {
  WeightLossTabArguments? screenArguments;
  String? selectedTabPageId = null;

  WeightLossTabArguments? getScreenArguments() {
    final ScreenArguments? args =
        ModalRoute.of(context)!.settings.arguments as ScreenArguments?;
    if (args != null) {
      WeightLossTabArguments arguments = WeightLossTabArguments(args.params);
      return arguments;
    }
    return null;
  }

  Future<void> setTabVisitValue() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.setBool("TRANSFORM_TAB_VISITED", true);
  }

  @override
  void initState() {
    super.initState();
    //setTabVisitValue();
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      screenArguments = getScreenArguments();
      final weightLossTab = BlocProvider.of<WeightLossTabBloc>(context);
      weightLossTab.add(ResetWeightLossTabEvent());
      weightLossTab.add(LoadWeightLossTabEvent(showLoader: true));
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  refresh({required BuildContext context, bool showLoader = false}) {
    screenArguments = getScreenArguments();
    final weightLossTab = BlocProvider.of<WeightLossTabBloc>(context);
    weightLossTab.add(LoadWeightLossTabEvent(showLoader: true));
  }

  onBackPress() {
    if (Navigator.canPop(context)) {
      Navigator.pop(context);
    } else if (!Navigator.canPop(context)) {
      ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
      actionBloc.add(CloseApplicationEvent(shouldReset: true));
    }
  }

  logPageView() {
    AnalyticsRepository analyticsRepository =
        RepositoryProvider.of<AnalyticsRepository>(context);
    analyticsRepository.logPageViewEvent(
        pageId: EnumToString.convertToString(RouteNames.tf_weight_loss_tab),
        eventInfo: {});
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    switch (state) {
      case AppLifecycleState.resumed:
        refresh(context: context, showLoader: false);
        break;
      case AppLifecycleState.inactive:
        break;
      case AppLifecycleState.paused:
        break;
      case AppLifecycleState.detached:
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    TabBloc tabBloc = BlocProvider.of<TabBloc>(context);
    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: widget.inTabMode ? Colors.black : Colors.transparent,
      body: Stack(
        children: [
          Aurora(
            context: context,
            canvasTheme: CanvasTheme.CLASSIC,
            size: MediaQuery.of(context).size,
          ),
          if (widget.inTabMode)
            BlocListener<TabBloc, TabState>(
              listener: (context, state) {
                if (state is TabSelected) {
                  if (state.selectedTab.tab == widget.tab) {
                    if (state.params != null &&
                        state.params!.containsKey("selectedTabPageId")) {
                      selectedTabPageId = state.params!["selectedTabPageId"];
                    }
                    refresh(context: context);
                  } else {
                    selectedTabPageId = "";
                  }
                } else if (state is TabSelectionCompleted) {
                  if (state.selectedTab.tab == widget.tab) {
                    if (state.selectedInnerTabPageId != null) {
                      selectedTabPageId = state.selectedInnerTabPageId;
                    }
                    refresh(context: context);
                  } else {
                    selectedTabPageId = "";
                  }
                }
              },
              child: const SizedBox(),
            ),
          Positioned.fill(
            child: BlocListener<NavigationBloc, NavigationState>(
              listener: (context, state) {
                if (state is NavigationStackUpdated &&
                    state.action == NavigationStackAction.pop &&
                    state.route?.settings.name ==
                        '/${EnumToString.convertToString(RouteNames.tf_weight_loss_tab)}') {
                  refresh(context: context, showLoader: false);
                }
              },
              child: BlocBuilder<WeightLossTabBloc, WeightLossTabState>(
                buildWhen: (previous, current) =>
                    (widget.tab != null &&
                        tabBloc.selectedTab?.tab == widget.tab) ||
                    !widget.inTabMode,
                builder: (context, state) {
                  List<PageData> pageDataList = [];
                  String title = "Weight Loss";
                  if (state is WeightLossTabLoadedState) {
                    pageDataList = state.screenData.pageDatalist ?? [];
                    title = state.screenData.title ?? "Weight Loss";
                  } else if (state is WeightLossTabLoadingState &&
                      state.screenData != null) {
                    pageDataList = state.screenData?.pageDatalist ?? [];
                    title = state.screenData?.title ?? "Weight Loss";
                  } else if (state is WeightLossTabErrorState) {
                    return Stack(
                      children: [
                        Container(
                          color: Colors.black,
                          child: ErrorScreen(
                              errorInfo: state.errorInfo ?? UnknownError()),
                        ),
                        Positioned(
                          right: 5,
                          top: 30,
                          child: IconButton(
                            onPressed: () {
                              if (Navigator.canPop(context)) {
                                Navigator.pop(context);
                              } else if (!Navigator.canPop(context)) {
                                ActionBloc actionBloc =
                                    BlocProvider.of<ActionBloc>(context);
                                actionBloc.add(
                                    CloseApplicationEvent(shouldReset: false));
                              }
                            },
                            icon: const Icon(
                              Icons.clear,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    );
                  }
                  String? selectedTabPageIdVal = getScreenArguments() != null
                      ? (getScreenArguments()!.selectedTabPageId ??
                          selectedTabPageId)
                      : selectedTabPageId;
                  return pageDataList.isNotEmpty
                      ? WeightLossTabContainer(
                          key: Key(selectedTabPageIdVal ?? ""),
                          pageDataList: pageDataList,
                          inTabMode: widget.inTabMode,
                          selectedTabPageId: selectedTabPageIdVal,
                          title: title,
                        )
                      : Container();
                },
              ),
            ),
          ),
          Positioned.fill(
            child: BlocBuilder<WeightLossTabBloc, WeightLossTabState>(
              builder: (context, state) {
                if (state is WeightLossTabLoadingState && state.showLoader) {
                  return Center(child: FancyLoadingIndicator());
                }
                return Container();
              },
            ),
          ),
        ],
      ),
    );
  }
}

class WeightLossTabArguments {
  String? pageName;
  String? pageId;
  String? pageFrom;
  bool? hideTitle;
  bool? swipeEnabled;
  String? selectedTabPageId;
  String? subCategoryCode;

  WeightLossTabArguments(Map<String, dynamic> params) {
    pageName = params['name'];
    pageId = params['pageId'];
    pageFrom = params['pageFrom'];
    hideTitle = (params['hideTitle'] == 'true');
    swipeEnabled = (params['swipeEnabled'] == 'true');
    selectedTabPageId = params['selectedTabPageId'];
    subCategoryCode = params["subCategoryCode"];
  }
}

import 'package:common/action/action_handler.dart';
import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/blocs/navigation/navigation_bloc.dart';
import 'package:common/blocs/navigation/state.dart';
import 'package:common/model/error_prop_model.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/page/basic_page_container.dart';
import 'package:common/ui/screens/error_screen.dart';
import 'package:common/ui/widget_factory.dart';
import 'package:common/user/user_repository.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:transform/blocs/lift_clp/events.dart';
import 'package:transform/blocs/lift_clp/lift_clp_bloc.dart';
import 'package:transform/blocs/lift_clp/state.dart';
import 'package:transform/constants/constants.dart';
import 'package:scroll_to_index/scroll_to_index.dart';
import 'package:navigation_history_observer/navigation_history_observer.dart';

class LiftClpArguments {
  String? subCategoryCode;

  LiftClpArguments(Map<String, dynamic> payload) {
    this.subCategoryCode = payload["subCategoryCode"];
  }
}

class LiftClp extends StatefulWidget {

  final bool showTitleBar;
  final TabMode tabMode;
  final AutoScrollController? scrollController;

  const LiftClp(
      {Key? key,
        this.tabMode = TabMode.FULL,
        this.showTitleBar = true,
        this.scrollController
      })
      : super(key: key);

  @override
  _LiftClpState createState() => _LiftClpState();
}

class _LiftClpState extends State<LiftClp>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  LiftClpArguments? getLiftClpArguments() {
    final ScreenArguments? args =
        ModalRoute.of(context)!.settings.arguments as ScreenArguments?;
    if (args != null) {
      return LiftClpArguments(args.params);
    }
    return null;
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      LiftClpArguments? arguments = getLiftClpArguments();
      final liftClpBloc = BlocProvider.of<LiftClpBloc>(context);
      liftClpBloc.add(ResetLiftClpEvent());
      liftClpBloc.add(LoadLiftClpEvent(showLoader: true));
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  refresh({required BuildContext context, bool showLoader = false}) {
    LiftClpArguments? arguments = getLiftClpArguments();
    final liftClpBloc = BlocProvider.of<LiftClpBloc>(context);
    liftClpBloc.add(LoadLiftClpEvent(showLoader: showLoader));
  }

  onBackPress() {
    if (Navigator.canPop(context)) {
      Navigator.pop(context);
    } else if (!Navigator.canPop(context)) {
      ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
      actionBloc.add(CloseApplicationEvent(shouldReset: true));
    }
  }

  logPageView() {
    AnalyticsRepository analyticsRepository =
        RepositoryProvider.of<AnalyticsRepository>(context);
    analyticsRepository.logPageViewEvent(
        pageId: EnumToString.convertToString(RouteNames.lift), eventInfo: {});
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    switch (state) {
      case AppLifecycleState.resumed:
        logPageView();
        refresh(context: context, showLoader: false);
        break;
      case AppLifecycleState.inactive:
        break;
      case AppLifecycleState.paused:
        break;
      case AppLifecycleState.detached:
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      body: Stack(
        children: [
          Aurora(
            context: context,
            size: MediaQuery.of(context).size,
          ),
          Positioned.fill(
            child: BlocListener<NavigationBloc, NavigationState>(
              listener: (context, state) {
                if (state is NavigationStackUpdated &&
                    state.action == NavigationStackAction.pop &&
                    state.route?.settings.name ==
                        '/${EnumToString.convertToString(RouteNames.lift)}') {
                  refresh(context: context, showLoader: false);
                }
              },
              child: BlocBuilder<LiftClpBloc, LiftClpState>(
                builder: (context, state) {
                  List<Widget> _widgets = [];
                  WidgetFactory widgetFactory =
                      RepositoryProvider.of<WidgetFactory>(context);
                  if (state is LiftClpLoadedState) {
                    List<Widget> widgets = state.screenData.widgets!
                        .map<Widget>(
                            (widget) => widgetFactory.createWidget(widget))
                        .toList();
                    _widgets = widgets;
                  } else if (state is LiftClpLoadingState &&
                      state.screenData != null) {
                    List<Widget> widgets = state.screenData!.widgets!
                        .map<Widget>(
                            (widget) => widgetFactory.createWidget(widget))
                        .toList();
                    _widgets = widgets;
                  } else if (state is LiftClpErrorState) {
                    return Stack(
                      children: [
                        Container(
                          color: Colors.black,
                          child: ErrorScreen(
                              errorInfo: state.errorInfo ?? UnknownError()),
                        ),
                        Positioned(
                          right: 5,
                          top: 30,
                          child: IconButton(
                            onPressed: () {
                              if (Navigator.canPop(context)) {
                                Navigator.pop(context);
                              } else if (!Navigator.canPop(context)) {
                                ActionBloc actionBloc =
                                    BlocProvider.of<ActionBloc>(context);
                                actionBloc.add(
                                    CloseApplicationEvent(shouldReset: false));
                              }
                            },
                            icon: const Icon(
                              Icons.clear,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    );
                  }
                  return BasicPageContainer(
                    shouldBuildWidget: false,
                    showTitleBar: widget.showTitleBar,
                    itemBuilder: (BuildContext context, Widget currentWidget,
                        int index) {
                      return _widgets[index];
                    },
                    scrollController: widget.scrollController,
                    widgetData: _widgets,
                    onBackPressed: () {
                      onBackPress();
                    },
                    title: state is LiftClpLoadedState
                        ? state.screenData.title
                        : "Lift",
                    showLoader: false,
                  );
                },
              ),
            ),
          ),
          Positioned.fill(
            child: BlocBuilder<LiftClpBloc, LiftClpState>(
              builder: (context, state) {
                if (state is LiftClpLoadingState && state.showLoader) {
                  return Center(child: FancyLoadingIndicator());
                }
                return Container();
              },
            ),
          ),
        ],
      ),
    );
  }
}

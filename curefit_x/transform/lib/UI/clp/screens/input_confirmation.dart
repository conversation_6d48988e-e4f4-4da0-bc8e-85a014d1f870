import 'dart:async';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:flutter/material.dart';
import 'package:common/action/action_handler.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/util/theme.dart';
import 'package:transform/constants/constants.dart';
import 'package:async/async.dart';

class InputConfirmationScreen extends StatefulWidget {
  final StreamController<bool> controller;

  InputConfirmationScreen(this.controller, {List? widgets});

  @override
  _InputConfirmationScreenState createState() => _InputConfirmationScreenState();
}

class _InputConfirmationScreenState extends State<InputConfirmationScreen> {


  @override
  void initState() {
    CancelableOperation<Null>? cancelableOperation;
    widget.controller.stream.listen((event) {
      cancelableOperation?.cancel();
    });
    cancelableOperation = CancelableOperation.fromFuture(
      Future.delayed(Duration(seconds: 3), () {
        Navigator.of(context).pop();
      }),
    );
    super.initState();
  }

  @override
  Widget build(BuildContext context) {


    return Scaffold(
        body: LayoutBuilder(
          builder: (context, constraints) {
            if (constraints.maxWidth > 0) {
              return Stack(children: [
                Aurora(size: constraints.biggest,context: context,),
                Container(
                  padding: EdgeInsets.only(top: 50, right: 20),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      GestureDetector(
                        onTap: () {
                          Navigator.pop(context);
                        },
                        child: Container(
                          width: 35,
                          height: 35,
                          decoration: new BoxDecoration(
                              color: Colors.white.withOpacity(0.28), shape: BoxShape.circle),
                          child: Icon(Icons.close, color: Colors.white),
                        ),
                      )
                    ],
                  ),
                ),
                Center(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      SizedBox(height: 200),
                      Container(
                        width: 80,
                        height: 80,
                        decoration: new BoxDecoration(
                            shape: BoxShape.circle,
                          border: Border.all(width: 4, color: Colors.white)
                        ),
                        child: Icon(Icons.check, color: Colors.white, size: 45),
                      ),
                      SizedBox(height: 100),
                      Text("Thank you for updating your measurements",
                          textAlign: TextAlign.center,
                          style: AuroraTheme.of(context).textStyle(TypescaleValues.H9)),
                      SizedBox(height: 18),
                      Text("You will be able to track your progress now!",
                          style: AuroraTheme.of(context).textStyle(TypescaleValues.P5,color: Colors.white.withOpacity(0.6))),
                    ],
                  ),
                ),
              ]);
            }
            return Container();
          },
        )
    );
  }
}

import 'package:common/constants/action_constants.dart';
import 'package:common/ui/fitness_device/fitness_device_util.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/user/user_repository.dart';
import 'package:easy_debounce/easy_debounce.dart';
import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/blocs/action/state.dart';
import 'package:common/blocs/navigation/navigation_bloc.dart';
import 'package:common/blocs/navigation/state.dart';
import 'package:common/blocs/scroll/scroll_bloc.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/alert_view.dart';
import 'package:common/ui/atoms/floating_button.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/ui/ui_toolkit/ui_toolkit.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:common/ui/widget_factory.dart';
import 'package:common/util/action_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:transform/UI/clp/widgets/clp_app_bar.dart';
import 'package:common/blocs/booking/booking_bloc.dart';
import 'package:common/blocs/booking/state.dart';
import 'package:transform/UI/clp/widgets/coach_contact_widget.dart';
import 'package:transform/blocs/clp/clp_bloc.dart';
import 'package:transform/blocs/clp/events.dart';
import 'package:transform/blocs/clp/models.dart';
import 'package:transform/blocs/clp/state.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:lottie/lottie.dart';
import 'package:transform/blocs/content/chapter_bloc.dart';
import 'package:transform/blocs/content/state.dart';
import 'package:scroll_to_index/scroll_to_index.dart';
import 'package:transform/blocs/user_image/state.dart';
import 'package:transform/blocs/user_image/user_image_bloc.dart';
import 'package:transform/blocs/weight_loss_tab/models.dart';
import 'package:transform/constants/constants.dart';
import 'package:navigation_history_observer/navigation_history_observer.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:common/action/action_handler.dart' as ActionHandler;

class FadingEffect extends CustomPainter {
  final Color color;

  FadingEffect(this.color);

  @override
  void paint(Canvas canvas, Size size) {
    Rect rect = Rect.fromPoints(Offset(0, 0), Offset(size.width, size.height));

    LinearGradient lg = LinearGradient(
      begin: Alignment.center,
      end: Alignment.bottomCenter,
      colors: [Colors.transparent, this.color],
    );
    Paint paint = Paint()
      ..shader = lg.createShader(Rect.fromPoints(
          Offset(rect.width / 2, rect.height / 1.7), Offset(0, 0)));
    canvas.drawRect(rect, paint);
  }

  @override
  bool shouldRepaint(FadingEffect linePainter) => false;
}

class CoachCLP extends StatefulWidget {
  final TabMode tabMode;
  final bool showTitleBar;
  final bool fromCustomTab;
  final AutoScrollController? scrollController;

  const CoachCLP(
      {Key? key,
      this.tabMode = TabMode.FULL,
      this.showTitleBar = true,
      this.scrollController,
      this.fromCustomTab = false})
      : super(key: key);

  @override
  State<StatefulWidget> createState() => _CoachCLPState();
}

class _CoachCLPState extends State<CoachCLP>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  late AutoScrollController _scrollController;
  late AnimationController _imageScrollController;

  final itemKey = GlobalKey();
  late bool visible;
  late Animation<double> scaleAnimation;
  late AnimationController _scaleController;
  bool halfScreenAurora = true;
  bool showingError = false;
  double imageOffset = 0;
  double topPadding = 250;
  double prePurchaseTopPadding = 200;
  double previousScrollOffset = 0;
  int feedbackCount = 0;
  int fdMetricsPostCount = 0;
  int storyActionCount = 0;
  Map<String, int> widgetMap = new Map();
  int metricModalViewCount = 0;
  SharedPreferences? prefs;
  bool isFDPermissionGranted = false;
  int coachMarkOverlayCount = 0;
  late AnimationController _floatingCTAAnimController;
  int scrollControllerIndex = 0;

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    if (!visible) return;
    switch (state) {
      case AppLifecycleState.resumed:
        EasyDebounce.debounce('tf_debounce', const Duration(milliseconds: 1500),
            () {
          refresh(context: context);
        });
        break;
      case AppLifecycleState.inactive:
        break;
      case AppLifecycleState.paused:
        break;
      case AppLifecycleState.detached:
        break;
    }
  }

  setTheme(AuroraThemeData themeData) {
    CanvasTheme canvasTheme = draftTheme();
    themeData.canvasTheme = canvasTheme;
  }

  @override
  void initState() {
    super.initState();
    visible = true;
    WidgetsBinding.instance.addObserver(this);
    if (widget.scrollController != null) {
      _scrollController = widget.scrollController!;
    } else {
      _scrollController = AutoScrollController();
    }
    _imageScrollController = AnimationController(
        vsync: this, duration: const Duration(milliseconds: 8000));
    _floatingCTAAnimController =
        AnimationController(vsync: this, duration: Duration.zero);
    _scrollController.addListener(_scrollListener);
    _scrollController.addListener(_ctaListener);
    _scaleController = AnimationController(
        vsync: this, duration: const Duration(milliseconds: 250));

    _scaleController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        _scaleController.reset();
        _scaleController.forward();
      }
    });

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      final coachCLPBloc = BlocProvider.of<CoachCLPBloc>(context);
      coachCLPBloc.add(ResetCoachCLPEvent());
      coachCLPBloc.add(LoadCoachCLPEvent());
      setTheme(AuroraTheme.of(context));
      isFDPermissionGranted = await isPermissionGranted(context);
      prefs = await SharedPreferences.getInstance();
    });
  }

  CanvasTheme draftTheme() {
    TimeOfDay now = TimeOfDay.now();
    return now.hour > 4 && now.hour < 19 ? CanvasTheme.TEAL : CanvasTheme.NIGHT;
  }

  _ctaListener() {
    if (_floatingCTAAnimController.isCompleted) {
      _floatingCTAAnimController.reset();
    }
    _floatingCTAAnimController.forward();
  }

  _getTabBarHeight() {
    if (widget.tabMode == TabMode.FULL) {
      return 0;
    }
    return MediaQuery.of(context).padding.bottom + 55;
  }

  setGlobalTheme() {
    AuroraThemeData themeData = AuroraTheme.of(context);
    CanvasTheme canvasTheme = draftTheme();
    themeData.canvasTheme = canvasTheme;
    if (canvasTheme == CanvasTheme.TEAL) {
      themeData.backgroundTheme = tealCanvas().backgroundColor;
    } else if (canvasTheme == CanvasTheme.NIGHT) {
      themeData.backgroundTheme = nightCanvas().backgroundColor;
    } else {
      themeData.backgroundTheme = themeColor;
    }
    ActionHandler.Action action = ActionHandler.Action(
        type: ActionTypes.SET_THEME,
        meta: {"theme": canvasTheme.name.toString()},
        canRedirect: false);
    ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
    PerformActionEvent event = PerformActionEvent(action);
    actionBloc.add(event);
  }

  resetGlobalTheme() {
    AuroraThemeData themeData = AuroraTheme.of(context);
    themeData.canvasTheme = CanvasTheme.CLASSIC;
    themeData.backgroundTheme = classicCanvas().backgroundColor;
    ActionHandler.Action action = ActionHandler.Action(
        type: ActionTypes.SET_THEME,
        meta: {"theme": "CLASSIC"},
        canRedirect: false);
    ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
    PerformActionEvent event = PerformActionEvent(action);
    actionBloc.add(event);
  }

  _scrollListener() {
    imageOffset = _scrollController.positions.last.pixels > 0
        ? -_scrollController.positions.last.pixels * 0.7
        : 0;
    if (_scrollController.positions.last.pixels > 250 && halfScreenAurora) {
      halfScreenAurora = false;
      _scaleController.stop();
    } else if (_scrollController.positions.last.pixels < 250 &&
        !halfScreenAurora) {
      halfScreenAurora = true;
      _scaleController.forward();
    }
    if (_imageScrollController.value == 1.0) {
      _imageScrollController.reset();
    }
    double delta = 1.0 / MediaQuery.of(context).size.height / 1.7;
    _imageScrollController.animateTo(_imageScrollController.value + delta,
        duration: Duration.zero);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_ctaListener);
    _scrollController.removeListener(_scrollListener);
    if (widget.scrollController == null) {
      _scrollController.dispose();
    }
    _imageScrollController.dispose();
    _scaleController.dispose();
    _floatingCTAAnimController.dispose();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  refresh({required BuildContext context, bool showLoader = false}) async {
    final coachCLPBloc = BlocProvider.of<CoachCLPBloc>(context);
    coachCLPBloc.add(LoadCoachCLPEvent(showLoader: showLoader));
    isFDPermissionGranted = await isPermissionGranted(context);
  }

  Widget buildWidgets() {
    return BlocListener<UserImageBloc, UserImageState>(
      listener: (context, state) {
        if (state is UserImageUploadedState) {
          if (state.action != null) {
            ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
            PerformActionEvent event = PerformActionEvent(state.action!);
            actionBloc.add(event);
          }
        }
      },
      child: BlocListener<ChapterBloc, ContentState>(
          listener: (context, state) {
            if (state is ChapterCompleted) {
              refresh(context: context, showLoader: true);
            }
          },
          child: BlocListener<ActionBloc, ActionState>(
              listener: (context, state) {
                if (state is ApplicationResumed) {
                  logPageView();
                }
              },
              child: BlocListener<BookingBloc, BookingState>(
                  listener: (context, state) {
                    if (state is BookingCancelled) {
                      refresh(context: context);
                    }
                  },
                  child: BlocListener<NavigationBloc, NavigationState>(
                      listener: (context, state) {
                        if (!showingError &&
                            state is NavigationStackUpdated &&
                            state.action == NavigationStackAction.pop &&
                            state.route?.settings.name ==
                                '/${EnumToString.convertToString(RouteNames.tf_clp)}') {
                          visible = true;
                          refresh(context: context, showLoader: false);
                        } else {
                          visible = false;
                        }
                      },
                      child: BlocListener<CoachCLPBloc, CoachCLPState>(
                          listener: (context, state) {
                        if (state is CoachCLPNotLoaded) {
                          showingError = true;
                          showErrorAlert(context: context, title: state.error);
                        }
                        if (state is CoachCLPLoaded) {
                          final feedbackAction = state.coachCLP.feedbackAction;
                          final weightLoggingAction =
                              state.coachCLP.weightLoggingAction;
                          final fitnessDeviceSyncAction =
                              state.coachCLP.fitnessDeviceSyncAction;
                          final weightModalVisibility =
                              state.coachCLP.weightModalVisibility;
                          final clearGrantedPermissionEpoch =
                              state.coachCLP.clearGrantedPermissionEpoch;
                          final coachMarkImageUrl =
                              state.coachCLP.coachMarkImageUrl;
                          final int millisecondsInDay = 1000 * 60 * 60 * 24;
                          int? modalDateEpoch;
                          int? googleFitGrantedEpoch;
                          ActionBloc actionBloc =
                              BlocProvider.of<ActionBloc>(context);
                          if (prefs != null) {
                            modalDateEpoch = prefs?.getInt('modalDateEpoch');
                            googleFitGrantedEpoch =
                                prefs?.getInt('GOOGLEFIT_GRANTED_EPOCH');
                            if (clearGrantedPermissionEpoch != null &&
                                googleFitGrantedEpoch != null &&
                                clearGrantedPermissionEpoch >=
                                    googleFitGrantedEpoch) {
                              prefs?.remove('GOOGLEFIT_GRANTED_EPOCH');
                              prefs?.remove('HealthPermissionState');
                            }
                          }
                          if (feedbackAction != null && feedbackCount == 0) {
                            feedbackCount = 1;
                            PerformActionEvent event =
                                PerformActionEvent(feedbackAction);
                            actionBloc.add(event);
                          } else if (metricModalViewCount == 0 &&
                              weightLoggingAction != null &&
                              prefs != null &&
                              (modalDateEpoch == null ||
                                  DateTime.now().millisecondsSinceEpoch -
                                          modalDateEpoch >=
                                      (weightModalVisibility ?? 7) *
                                          millisecondsInDay)) {
                            metricModalViewCount = 1;
                            Future.delayed(const Duration(seconds: 2), () {
                              if (mounted) {
                                prefs?.setInt('modalDateEpoch',
                                    DateTime.now().millisecondsSinceEpoch);
                                PerformActionEvent event =
                                    PerformActionEvent(weightLoggingAction);
                                actionBloc.add(event);
                              }
                            });
                          }
                          if (fitnessDeviceSyncAction != null &&
                              fdMetricsPostCount == 0 &&
                              isFDPermissionGranted) {
                            fdMetricsPostCount = 1;
                            PerformActionEvent event =
                                PerformActionEvent(fitnessDeviceSyncAction);
                            actionBloc.add(event);
                          }
                          if (coachMarkOverlayCount == 0 &&
                              coachMarkImageUrl != null &&
                              coachMarkImageUrl.isNotEmpty) {
                            coachMarkOverlayCount = 1;
                            Future.delayed(const Duration(seconds: 2), () {
                              if (mounted) {
                                showCoachMarkOverlay(
                                    context, coachMarkImageUrl ?? "");
                              }
                            });
                          }
                          showingError = false;
                          if (state.coachCLP.backgroundTheme != null) {
                            AuroraTheme.of(context).canvasTheme =
                                state.coachCLP.backgroundTheme!.theme;
                          }
                          if (state.scrollToEnd) {
                            // If the element is visible on screen,  it will scroll
                            // to that widget otherwise it will return null and will scroll to end
                            if (itemKey.currentContext != null) {
                              _scrollController.positions.last.ensureVisible(
                                itemKey.currentContext!.findRenderObject()!,
                                alignment: 0,
                                // How far into view the item should be scrolled (between 0 and 1).
                                duration: const Duration(milliseconds: 250),
                              );
                            } else {
                              _scrollController.animateTo(
                                  _scrollController
                                          .positions.last.maxScrollExtent /
                                      state.scrollFactor,
                                  duration: Duration(milliseconds: 250),
                                  curve: Curves.easeIn);
                            }
                          }
                          if (state.coachCLP.storyAction != null &&
                              storyActionCount == 0) {
                            storyActionCount = 1;
                            ActionBloc actionBloc =
                                BlocProvider.of<ActionBloc>(context);
                            PerformActionEvent event =
                                PerformActionEvent(state.coachCLP.storyAction!);
                            actionBloc.add(event);
                          }
                        }
                      }, child: BlocBuilder<ScrollBloc, ScrollState>(
                              builder: (context, state) {
                        if (state is ScrollToWidget) {
                          if (widgetMap[state.widgetId] != null) {
                            _scrollController.scrollToIndex(
                                widgetMap[state.widgetId]!,
                                preferPosition: AutoScrollPosition.begin,
                                duration: Duration(milliseconds: 800));
                            BlocProvider.of<ScrollBloc>(context)
                                .add(ScrollToWidgetEndedEvent());
                          }
                        }
                        return BlocBuilder<CoachCLPBloc, CoachCLPState>(
                            builder: (context, state) {
                          if (state is CoachCLPLoaded) {
                            return buildCoachCLP(state.coachCLP);
                          } else if (state is CoachCLPLoading &&
                              state.coachCLP != null) {
                            return buildCoachCLP(state.coachCLP!);
                          } else if (state is CoachCLPNotLoaded) {
                            return CustomScrollView(
                                controller: _scrollController,
                                slivers: [buildAppBar()]);
                          }
                          return Container();
                        });
                      })))))),
    );
  }

  logPageView() {
    AnalyticsRepository analyticsRepository =
        RepositoryProvider.of<AnalyticsRepository>(context);
    analyticsRepository.logPageViewEvent(
        pageId: EnumToString.convertToString(RouteNames.tf_clp), eventInfo: {});
  }

  buildAppBar({
    CoachContactWidgetData? coachContactWidgetData,
    AppbarActionWidgetData? appbarActionWidgetData,
    String? appBarTitle,
    String? appBarTitleUrl,
  }) {
    return !widget.fromCustomTab
        ? TransitionAppBar(
            coachContactWidgetData: coachContactWidgetData,
            appbarActionWidgetData: appbarActionWidgetData,
            hideBackButton: widget.tabMode == TabMode.EMBEDDED,
            onBackPressed: () {
              if (Navigator.canPop(context)) {
                Navigator.pop(context);
              } else {
                ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
                actionBloc.add(CloseApplicationEvent(shouldReset: false));
              }
            },
            appBarTitle: appBarTitle,
            appBarTitleUrl: appBarTitleUrl,
          )
        : SliverToBoxAdapter(
            child: Container(
              height: Spacings.x24,
            ),
          );
  }

  buildCoachCLP(CoachCLPData data) {
    WidgetFactory widgetFactory = RepositoryProvider.of<WidgetFactory>(context);
    populateWidgetMap(data.widgets);
    return NotificationListener(
        onNotification: (notification) {
          // if (notification is ScrollEndNotification) {
          //   RepositoryProvider.of<VideoPlayerRegistry>(context).dispose();
          // }
          return true;
        },
        child: CustomScrollView(
          controller: _scrollController,
          slivers: [
            buildAppBar(
              coachContactWidgetData: data.coachContactWidgetData,
              appbarActionWidgetData: data.appbarActionWidgetData,
              appBarTitle: data.appBarTitle,
              appBarTitleUrl: data.appBarTitleUrl,
            ),
            SliverPadding(
              padding: EdgeInsets.only(
                  bottom: widget.tabMode == TabMode.EMBEDDED
                      ? _getTabBarHeight()
                      : 0),
              sliver: SliverList(
                  delegate: SliverChildBuilderDelegate(
                (context, index) {
                  Widget child =
                      widgetFactory.createWidget(data.widgets[index]);
                  if (index == 0) {
                    child = Padding(
                        padding: EdgeInsets.only(
                            top: (data.backgroundTheme != null &&
                                    data.backgroundTheme!.lottieUrl != null)
                                ? (data.coachContactWidgetData != null
                                    ? scale(context, topPadding)
                                    : prePurchaseTopPadding)
                                : 0),
                        child: child);
                  }
                  if (isFDPermissionGranted) {
                    if ([
                      WidgetTypes.FITNESS_DEVICE_COLLAPSED_CONNECTION_WIDGET,
                      WidgetTypes.FITNESS_DEVICE_CONNECTION_WIDGET,
                    ].contains(EnumToString.fromString(WidgetTypes.values,
                        data.widgets[index]['widgetType']))) {
                      child = Container();
                    }
                  } else {
                    if (WidgetTypes.FITNESS_DEVICE_METRICS_PROGRESS_WIDGET ==
                        EnumToString.fromString(WidgetTypes.values,
                            data.widgets[index]['widgetType'])) {
                      child = Container();
                    }
                  }
                  if (WidgetTypes.FITNESS_PACK_BROWSE_WIDGET ==
                      EnumToString.fromString(WidgetTypes.values,
                          data.widgets[index]['widgetType'])) {
                    child = Container(
                      //key: itemKey,
                      child: child,
                    );
                  }
                  return AnimationConfiguration.synchronized(
                    child: FadeInAnimation(
                        child: AutoScrollTag(
                            key: ValueKey(index),
                            controller: _scrollController,
                            index: index,
                            child: child)),
                  );
                },
                childCount: data.widgets.length,
              )),
            )
          ],
        ));
  }

  @override
  Widget build(BuildContext context) {
    AuroraThemeData themeData = AuroraTheme.of(context);
    return Scaffold(
        extendBodyBehindAppBar: true,
        body: LayoutBuilder(
          builder: (context, constraints) {
            if (constraints.maxWidth > 0) {
              return Stack(children: [
                BlocBuilder<CoachCLPBloc, CoachCLPState>(
                    builder: (context, state) {
                  if (themeData.canvasTheme != CanvasTheme.TEAL &&
                      themeData.canvasTheme != CanvasTheme.NIGHT) {
                    setTheme(themeData);
                  }
                  return Aurora(
                    context: context,
                    size: constraints.biggest,
                  );
                }),
                Positioned(
                    left: 0,
                    right: 0,
                    top: 0,
                    child: AnimatedBuilder(
                      builder: (context, child) => Transform.translate(
                          offset: Offset(0, imageOffset), child: child),
                      animation: _imageScrollController,
                      child: BlocBuilder<CoachCLPBloc, CoachCLPState>(
                        builder: (context, state) {
                          BackgroundTheme? backgroundTheme;
                          if (state is CoachCLPLoaded) {
                            backgroundTheme = state.coachCLP.backgroundTheme;
                          } else if (state is CoachCLPLoading) {
                            backgroundTheme = state.coachCLP?.backgroundTheme;
                          }
                          return backgroundTheme != null &&
                                  backgroundTheme.lottieUrl != null
                              ? wrapInShaderMask(
                                  context,
                                  Lottie.network(
                                    getMediaUrl(backgroundTheme.lottieUrl!),
                                    frameRate: FrameRate(60),
                                    fit: BoxFit.fill,
                                    height: scale(context,
                                        backgroundTheme.height.toDouble()),
                                    controller: _scaleController,
                                    onLoaded: (p0) {
                                      _scaleController.duration = p0.duration;
                                      _scaleController.forward();
                                    },
                                  ),
                                  top: false)
                              : Container();
                        },
                      ),
                    )),
                Positioned(
                  left: 0,
                  right: 0,
                  top: 0,
                  bottom: 0,
                  child: buildWidgets(),
                ),
                BlocBuilder<CoachCLPBloc, CoachCLPState>(
                  // add loader
                  builder: (context, state) {
                    if (state is CoachCLPLoaded &&
                        (state.coachCLP.footerAction != null ||
                            widget.fromCustomTab)) {
                      return AnimatedBuilder(
                          animation: _floatingCTAAnimController,
                          builder: (context, child) {
                            return Positioned(
                              left: 0,
                              right: 0,
                              bottom: getBottomPadding(),
                              child: widget.fromCustomTab &&
                                      state.coachCLP.coachContactWidgetData !=
                                          null
                                  ? Container(
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.only(
                                          topRight: Radius.circular(15.0),
                                          topLeft: Radius.circular(15.0),
                                        ),
                                        color: AuroraTheme.of(context)
                                                    .canvasTheme ==
                                                CanvasTheme.NIGHT
                                            ? Color.fromRGBO(65, 78, 128, 0.9)
                                            : Color.fromRGBO(40, 90, 100, 0.9),
                                      ),
                                      padding: EdgeInsets.symmetric(
                                          horizontal: Spacings.x4,
                                          vertical: Spacings.x3),
                                      child: CoachContactWidgetView(
                                        state.coachCLP.coachContactWidgetData!,
                                        customTextColor: Colors.white,
                                      ),
                                    )
                                  : state.coachCLP.footerAction != null
                                      ? FloatingButton(
                                          onPress: () {
                                            clickActionWithAnalytics(
                                                state.coachCLP.footerAction!,
                                                context,
                                                null, {
                                              "fromPage": "tf_clp",
                                              "button": "FAB Button"
                                            });
                                          },
                                          buttonText: state.coachCLP
                                                  .footerAction?.title ??
                                              "",
                                          titleText: state
                                              .coachCLP.footerAction?.subtitle,
                                        )
                                      : Container(),
                            );
                          });
                    }
                    if (state is CoachCLPLoading && state.showLoader) {
                      return PageLoadingIndicator();
                    }
                    return Container();
                  },
                ),
              ]);
            }
            return Container();
          },
        ));
  }

  void populateWidgetMap(List widgets) {
    for (int i = 0; i < widgets.length; i++) {
      var element = widgets[i];
      if (element != null &&
          element["widgetMetric"] != null &&
          element["widgetMetric"]["widgetId"] != null) {
        widgetMap.putIfAbsent(element["widgetMetric"]["widgetId"], () => i);
      }
    }
  }

  double getBottomPadding() {
    double tabbarSpacing =
        widget.tabMode == TabMode.EMBEDDED ? _getTabBarHeight() : 0;
    if (_scrollController.hasClients &&
        _scrollController.positions.last.pixels -
                (MediaQuery.of(context).size.height / 5) >
            0) {
      return 0.0 + tabbarSpacing;
    }
    if (_scrollController.hasClients &&
        _scrollController.positions.last.pixels -
                (MediaQuery.of(context).size.height / 5) <
            0) {
      return _scrollController.positions.last.pixels -
          (MediaQuery.of(context).size.height / 5) +
          tabbarSpacing;
    }
    return 0.0 + tabbarSpacing;
  }

  dynamic showCoachMarkOverlay(BuildContext context, String coachMarkImageUrl) {
    return showModalBottomSheet<void>(
      isScrollControlled: true,
      enableDrag: true,
      context: context,
      builder: (context) {
        return BlurView(
          backgroundColor:
              AuroraTheme.of(context).canvasTheme == CanvasTheme.NIGHT
                  ? Color.fromRGBO(57, 68, 116, 1)
                  : Color.fromRGBO(32, 77, 89, 1),
          opacity: 0.8,
          blurType: BlurType.HIGH,
          child: FractionallySizedBox(
            heightFactor: 0.4,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: Spacings.x4),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                    margin: const EdgeInsets.symmetric(vertical: Spacings.x2),
                    height: 2.5,
                    width: 55,
                    alignment: Alignment.topCenter,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(5),
                      color: Colors.white.withOpacity(0.3),
                    ),
                  ),
                  CFNetworkImage(
                    imageUrl: getImageUrl(context,
                        imagePath: coachMarkImageUrl,
                        width: scale(context, 335).toInt()),
                    fit: BoxFit.cover,
                  ),
                  SizedBox(
                    height: Spacings.x4,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

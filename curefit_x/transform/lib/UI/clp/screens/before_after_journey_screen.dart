import 'package:common/action/action_handler.dart';
import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/blocs/tab/tab_bloc.dart';
import 'package:common/model/error_prop_model.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/page/basic_page_container.dart';
import 'package:common/ui/screens/error_screen.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/widget_factory.dart';
import 'package:common/util/action_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:transform/blocs/before_after_journey/before_after_journey_bloc.dart';
import 'package:transform/blocs/before_after_journey/events.dart';
import 'package:transform/blocs/before_after_journey/state.dart';
import 'package:transform/constants/constants.dart';

class BeforeAfterJourneyClpArguments {
  String? subCategoryCode;

  BeforeAfterJourneyClpArguments(Map<String, dynamic> payload) {
    this.subCategoryCode = payload["subCategoryCode"];
  }
}

class BeforeAfterJourneyClp extends StatefulWidget {
  const BeforeAfterJourneyClp({Key? key}) : super(key: key);

  @override
  _BeforeAfterJourneyClpState createState() => _BeforeAfterJourneyClpState();
}

class _BeforeAfterJourneyClpState extends State<BeforeAfterJourneyClp>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  BeforeAfterJourneyClpArguments? getBeforeAfterJourneyClpArguments() {
    final ScreenArguments? args =
        ModalRoute.of(context)!.settings.arguments as ScreenArguments?;
    if (args != null) {
      return BeforeAfterJourneyClpArguments(args.params);
    }
    return null;
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      BeforeAfterJourneyClpArguments? arguments =
          getBeforeAfterJourneyClpArguments();
      final beforeAfterJourneyBloc =
          BlocProvider.of<BeforeAfterJourneyClpBloc>(context);
      beforeAfterJourneyBloc.add(LoadBeforeAfterJourneyClpEvent(
          showLoader: true, subCategoryCode: arguments?.subCategoryCode ?? ""));
    });
  }

  onBackPress() {
    if (Navigator.canPop(context)) {
      Navigator.pop(context);
    } else if (!Navigator.canPop(context)) {
      ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
      actionBloc.add(CloseApplicationEvent(shouldReset: true));
    }
  }

  logPageView() {
    AnalyticsRepository analyticsRepository =
        RepositoryProvider.of<AnalyticsRepository>(context);
    analyticsRepository.logPageViewEvent(
        pageId: EnumToString.convertToString(RouteNames.tf_before_after),
        eventInfo: {});
  }

  CanvasTheme draftTheme() {
    TimeOfDay now = TimeOfDay.now();
    return now.hour > 4 && now.hour < 19 ? CanvasTheme.TEAL : CanvasTheme.NIGHT;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      body: Stack(
        children: [
          Aurora(
            context: context,
            canvasTheme: draftTheme(),
            size: MediaQuery.of(context).size,
          ),
          Positioned.fill(
            child: BlocBuilder<BeforeAfterJourneyClpBloc,
                BeforeAfterJourneyClpState>(
              builder: (context, state) {
                List<Widget> _widgets = [];
                WidgetFactory widgetFactory =
                    RepositoryProvider.of<WidgetFactory>(context);
                if (state is BeforeAfterJourneyClpLoadedState) {
                  List<Widget> widgets = state.screenData.widgets!
                      .map<Widget>(
                          (widget) => widgetFactory.createWidget(widget))
                      .toList();
                  _widgets = widgets;
                } else if (state is BeforeAfterJourneyClpLoadingState &&
                    state.screenData != null) {
                  List<Widget> widgets = state.screenData!.widgets!
                      .map<Widget>(
                          (widget) => widgetFactory.createWidget(widget))
                      .toList();
                  _widgets = widgets;
                } else if (state is BeforeAfterJourneyClpErrorState) {
                  return Stack(
                    children: [
                      Container(
                        color: Colors.black,
                        child: ErrorScreen(
                            errorInfo: state.errorInfo ?? UnknownError()),
                      ),
                      Positioned(
                        right: 5,
                        top: 30,
                        child: IconButton(
                          onPressed: () {
                            if (Navigator.canPop(context)) {
                              Navigator.pop(context);
                            } else if (!Navigator.canPop(context)) {
                              ActionBloc actionBloc =
                                  BlocProvider.of<ActionBloc>(context);
                              actionBloc.add(
                                  CloseApplicationEvent(shouldReset: false));
                            }
                          },
                          icon: const Icon(
                            Icons.clear,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  );
                }
                return BasicPageContainer(
                  showTitleBar: true,
                  shouldBuildWidget: false,
                  itemBuilder:
                      (BuildContext context, Widget currentWidget, int index) {
                    return _widgets[index];
                  },
                  widgetData: _widgets,
                  onBackPressed: () {
                    onBackPress();
                  },
                  titleBarRightActions: state
                              is BeforeAfterJourneyClpLoadedState &&
                          state.screenData.rightActionButton != null
                      ? [
                          InkWell(
                            onTap: () {
                              clickActionWithAnalytics(
                                  state.screenData.rightActionButton!,
                                  context,
                                  null,
                                  {"from": "rightActionButton"});
                            },
                            child: Text(
                              state.screenData.rightActionButton!.title ?? "",
                              style: AuroraTheme.of(context)
                                  .textStyle(TypescaleValues.P3),
                            ),
                          ),
                        ]
                      : null,
                  title: state is BeforeAfterJourneyClpLoadedState
                      ? state.screenData.title
                      : "Weight loss",
                  showLoader: false,
                  canvasTheme: draftTheme(),
                );
              },
            ),
          ),
          Positioned.fill(
            child: BlocBuilder<BeforeAfterJourneyClpBloc,
                BeforeAfterJourneyClpState>(
              builder: (context, state) {
                if (state is BeforeAfterJourneyClpLoadingState &&
                    state.showLoader) {
                  return Center(child: FancyLoadingIndicator());
                }
                return Container();
              },
            ),
          ),
        ],
      ),
    );
  }
}

import 'dart:io';

import 'package:common/action/action_handler.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/blocs/navigation/navigation_bloc.dart';
import 'package:common/blocs/navigation/state.dart';
import 'package:common/font/cf_icons.dart';
import 'package:common/ui/alert_view.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/widget_factory.dart';
import 'package:flutter/material.dart';
import 'package:common/ui/aurora.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/user_progress/events.dart';
import 'package:transform/blocs/user_progress/state.dart';
import 'package:transform/blocs/user_progress/user_progress_bloc.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:transform/constants/constants.dart';
import 'package:navigation_history_observer/navigation_history_observer.dart';

class UserProgressScreenArguments {
  String? subCategoryCode;

  UserProgressScreenArguments(Map<String, dynamic> payload) {
    this.subCategoryCode =
    payload["subCategoryCode"] != null ? payload["subCategoryCode"] : "";
  }
}

class UserProgressScreen extends StatefulWidget {
  @override
  State<StatefulWidget> createState() => _UserProgressScreenState();
}

class _UserProgressScreenState extends State<UserProgressScreen>
    with TickerProviderStateMixin {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
      final ScreenArguments? args =
      ModalRoute.of(context)!.settings.arguments as ScreenArguments?;
      String subCategoryCode = "";
      if (args != null) {
        UserProgressScreenArguments arguments =
        UserProgressScreenArguments(args.params);
        subCategoryCode = arguments.subCategoryCode ?? "";
      }
      final userProgressBloc = BlocProvider.of<UserProgressBloc>(context);
      userProgressBloc.add(LoadUserProgressEvent(subCategoryCode: subCategoryCode));
    });
  }

  refresh({required BuildContext context, bool showLoader = false}) {
    final ScreenArguments? args =
    ModalRoute.of(context)!.settings.arguments as ScreenArguments?;
    String subCategoryCode = "";
    if (args != null) {
      UserProgressScreenArguments arguments =
      UserProgressScreenArguments(args.params);
      subCategoryCode = arguments.subCategoryCode ?? "";
    }
    final userProgressBloc = BlocProvider.of<UserProgressBloc>(context);
    userProgressBloc.add(LoadUserProgressEvent(subCategoryCode: subCategoryCode));
  }

  Widget buildWidgets() {
    return BlocListener<NavigationBloc, NavigationState>(
      listener: (context, state) {
        if (state is NavigationStackUpdated &&
            state.action == NavigationStackAction.pop &&
            state.route?.settings.name ==
                '/${EnumToString.convertToString(RouteNames.tf_user_progress)}') {
          refresh(context: context, showLoader: false);
        }
      },
      child: BlocListener<UserProgressBloc, UserProgressState>(
          listener: (context, state) {
        if (state is UserProgressNotLoaded) {
          showErrorAlert(
              context: context,
              title: state.error,
              onClose: () {
                Navigator.pop(context);
              });
        }
      }, child: BlocBuilder<UserProgressBloc, UserProgressState>(
              builder: (context, state) {
        if (state is UserProgressLoading) {
          return PageLoadingIndicator();
        }
        if (state is UserProgressLoaded &&
            state.userProgressScreen.widgets.isNotEmpty) {
          WidgetFactory widgetFactory =
              RepositoryProvider.of<WidgetFactory>(context);
          Widget child =
              widgetFactory.createWidget(state.userProgressScreen.widgets.first);
          return child;
        }
        return Container();
      })),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.black,
        extendBodyBehindAppBar: true,
        appBar: PreferredSize(
          preferredSize: Size.fromHeight(50.0 + AuroraTheme.of(context).embeddedSafeArea.top),
          child: Padding(
            padding: EdgeInsets.only(
                top: AuroraTheme.of(context).embeddedSafeArea.top),
            child: AppBar(
              backgroundColor: Colors.transparent,
              iconTheme: IconThemeData(color: Colors.white),
              leading: IconButton(
                icon: const Icon(
                  CFIcons.chevron_left,
                  color: Colors.white,
                  size: 20,
                  semanticLabel: "chevron_left",
                ),
                onPressed: () {
                  if (Navigator.canPop(context)) {
                    Navigator.pop(context);
                  } else if (!Navigator.canPop(context)) {
                    ActionBloc actionBloc =
                    BlocProvider.of<ActionBloc>(context);
                    actionBloc
                        .add(CloseApplicationEvent(shouldReset: true));
                  }
                },
              ),
            ),
          ),
        ),
        body: LayoutBuilder(
          builder: (context, constraints) {
            if (constraints.maxWidth > 0) {
              return Stack(children: [
                Aurora(
                  size: constraints.biggest,
                  context: context,
                ),
                Positioned(
                  left: 0,
                  right: 0,
                  top: 0,
                  bottom: 0,
                  child: buildWidgets(),
                ),
              ]);
            }
            return Container();
          },
        ));
  }
}

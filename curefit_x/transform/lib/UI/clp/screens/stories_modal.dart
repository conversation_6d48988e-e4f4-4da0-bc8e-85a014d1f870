import 'package:common/action/action_handler.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/atoms/floating_twin_button.dart';
import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/util/action_util.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/material.dart' hide Action;

showStoriesModal({
  required BuildContext context,
  String? title,
  String? subtitle,
  String? storyText,
  String? description,
  String? quotationText,
  String? imageUrl,
  bool isCenterAligned = false,
  List<Action>? actionsList,
  bool enableDayNightTheme = true,
  CanvasTheme? themeType,
}) {
  showModalBottomSheet<void>(
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(20),
        topRight: Radius.circular(20),
      ),
    ),
    isScrollControlled: true,
    context: context,
    builder: (context) {
      return Padding(
        padding: MediaQuery.of(context).viewInsets,
        child: Wrap(
          children: [
            BlurView(
              backgroundColor: enableDayNightTheme
                  ? themeType != null && themeType == CanvasTheme.NIGHT
                      ? Color.fromRGBO(57, 68, 116, 1)
                      : Color.fromRGBO(32, 77, 89, 1)
                  : Colors.transparent,
              opacity: 0.8,
              blurType: BlurType.HIGH,
              child: Column(
                mainAxisAlignment: isCenterAligned ? MainAxisAlignment.center:MainAxisAlignment.start,
                crossAxisAlignment: isCenterAligned?CrossAxisAlignment.center:CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Align(
                    alignment: Alignment.center,
                    child: Container(
                      margin: const EdgeInsets.symmetric(vertical: Spacings.x2),
                      height: 2.5,
                      width: 55,
                      alignment: Alignment.topCenter,
                      color: Colors.white.withOpacity(0.5),
                    ),
                  ),
                  if (description != null)
                    Padding(
                      padding: const EdgeInsets.only(
                          top: Spacings.x6,
                          left: Spacings.x4,
                          right: Spacings.x4),
                      child: Text(
                        description,
                        style: AuroraTheme.of(context).textStyle(
                          TypescaleValues.TAGTEXT,
                          color: Colors.white60,
                        ),
                        textAlign: isCenterAligned ? TextAlign.center : TextAlign.left,
                      ),
                    ),
                  if (title != null)
                    Padding(
                      padding: const EdgeInsets.only(
                          top: Spacings.x1,
                          left: Spacings.x4,
                          right: Spacings.x4),
                      child: Text(
                        title,
                        style: AuroraTheme.of(context).textStyle(
                          TypescaleValues.H2,
                          color: Colors.white,
                        ),
                        textAlign: isCenterAligned ? TextAlign.center : TextAlign.left,
                      ),
                    ),
                  if(imageUrl != null)
                    Padding(
                      padding: const EdgeInsets.only(
                          top: Spacings.x4,
                          left: Spacings.x4,
                          right: Spacings.x4),
                      child: CFNetworkImage(
                        imageUrl: getImageUrl(context,
                            imagePath: imageUrl),
                        fit: BoxFit.fitWidth,
                        width: scale(context,335),
                        errorWidget: (context, url, error) =>
                        const Icon(Icons.error),
                      ),
                    ),
                  if (subtitle != null)
                    Padding(
                      padding: const EdgeInsets.only(
                          top: Spacings.x1,
                          left: Spacings.x4,
                          right: Spacings.x4),
                      child: Text(
                        subtitle,
                        style: AuroraTheme.of(context).textStyle(
                          TypescaleValues.P1,
                          color: Colors.white60,
                        ),
                        textAlign: isCenterAligned ? TextAlign.center : TextAlign.left,
                      ),
                    ),
                  if (storyText != null)
                    Padding(
                      padding: const EdgeInsets.only(
                          top: Spacings.x4,
                          left: Spacings.x4,
                          right: Spacings.x4),
                      child: Text(
                        storyText,
                        style: AuroraTheme.of(context).textStyle(
                          TypescaleValues.P2,
                          color: Colors.white,
                        ),
                        textAlign: isCenterAligned ? TextAlign.center : TextAlign.left,
                      ),
                    ),
                  if (quotationText != null)
                    Container(
                      height: 250,
                      padding: const EdgeInsets.only(
                          top: Spacings.x6,
                          left: Spacings.x6,
                          right: Spacings.x6),
                      child: SingleChildScrollView(
                        child: Column(
                          children: [
                            Image.asset(
                              'assets/quotes_icon.png',
                              width: scale(context, 42),
                              height: scale(context, 25),
                            ),
                            SizedBox(height: Spacings.x4,),
                            Text(
                              quotationText,
                              style: AuroraTheme.of(context).textStyle(
                                TypescaleValues.P2,
                                color: Colors.white,
                              ),
                              textAlign: isCenterAligned ? TextAlign.center : TextAlign.left,
                            ),
                          ],
                        ),
                      ),
                    ),
                  SizedBox(
                    height: Spacings.x5,
                  ),
                  actionsList != null && actionsList.isNotEmpty
                      ? FloatingTwinButton(
                          titleText: actionsList[0].description,
                          data: actionsList,
                          onPress: (Action action) {
                            if (Navigator.canPop(context)) {
                              Navigator.pop(context);
                            } else {
                              ActionBloc actionBloc =
                                  BlocProvider.of<ActionBloc>(context);
                              actionBloc.add(
                                  CloseApplicationEvent(shouldReset: false));
                            }
                            clickActionWithAnalytics(action, context, null,
                                {"modal": "StoriesModal"});
                          },
                        )
                      : Padding(
                          padding: const EdgeInsets.all(Spacings.x4),
                          child: PrimaryButton(
                            () {
                              if (Navigator.canPop(context)) {
                                Navigator.pop(context);
                              } else {
                                ActionBloc actionBloc =
                                    BlocProvider.of<ActionBloc>(context);
                                actionBloc.add(
                                    CloseApplicationEvent(shouldReset: false));
                              }
                            },
                            "DONE",
                          ),
                        ),
                ],
              ),
            ),
          ],
        ),
      );
    },
  );
}

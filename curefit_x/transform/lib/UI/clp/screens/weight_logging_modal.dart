import 'dart:async';
import 'dart:math';

import 'package:common/action/action_handler.dart';
import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/constants/action_constants.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:lottie/lottie.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:flutter/material.dart' hide Action;
import 'package:transform/UI/clp/component/animated_line_graph.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:async/async.dart';

class WeightLoggingModalInfo {
  final String? title;
  final String? subtitle;
  final String? description;
  final double? focusValue;
  final int? precision;
  final double? creativeHeight;
  final String? unit;
  final int? metricId;
  final String? imageUrl;
  final String? lottieUrl;
  final num? animateToPage;
  final List<num>? chartValues;
  final Action? actionButton;

  WeightLoggingModalInfo({
    this.title,
    this.subtitle,
    this.description,
    this.focusValue,
    this.precision,
    this.unit,
    this.metricId,
    this.imageUrl,
    this.lottieUrl,
    this.creativeHeight,
    this.chartValues,
    this.actionButton,
    this.animateToPage,
  });

  factory WeightLoggingModalInfo.fromJson(json) {
    List<num> chartValues = [];
    if (json['chartValues'] != null) {
      for (num value in json['chartValues']) {
        chartValues.add(value);
      }
    }
    return WeightLoggingModalInfo(
      title: json['title'],
      subtitle: json['subtitle'],
      description: json['description'],
      focusValue: json['focusValue'],
      precision: json['precision'],
      unit: json['unit'],
      metricId: json['metricId'],
      imageUrl: json['imageUrl'],
      lottieUrl: json['lottieUrl'],
      creativeHeight: json['creativeHeight'],
      animateToPage: json['animateToPage'],
      chartValues: json['chartValues'] != null ? chartValues : null,
      actionButton: json['actionButton'] != null
          ? Action.fromJson(json['actionButton'])
          : null,
    );
  }
}

enum ScreenType {
  WEIGHT_LOGGING,
  PLATEAU_STATE,
  WEIGHT_INCREASE,
  TARGET_ACHIEVE,
  ACKNOWLEDGE_STATE,
  TARGET_WEIGHT_ACKNOWLEDGE_STATE
}

enum UserMetricState {
  FEW_WEIGHT_LOGS,
  PLATEAU_WEIGHT_LOGS,
  INCREASE_WEIGHT_LOGS,
  DECREASE_WEIGHT_LOGS,
  WEIGHT_LOGGED_IN,
  TARGET_WEIGHT_ACHIEVED,
  NONE
}

showWeightLoggingModal(
    {required BuildContext context, Map<String, dynamic>? modalInfo}) {
  RepositoryProvider.of<AnalyticsRepository>(context).logPageViewEvent(
    pageId: "tf_weight_modal",
    eventInfo: {"action": "Modal Opened", "modalFrom": "Weight Logging Modal"},
    pageFrom: "/tf_clp",
  );

  int _selectedPageModalIndex = 0;
  final List<WeightLoggingModalInfo> modalInfoList =
      modalInfo?['weightLoggingModalInfo'] != null
          ? modalInfo!['weightLoggingModalInfo']['modalInfoList']
              .map<WeightLoggingModalInfo>((e) {
              return WeightLoggingModalInfo.fromJson(e);
            }).toList()
          : [];

  final CanvasTheme? themeType = modalInfo?['themeType'] != null
      ? EnumToString.fromString(CanvasTheme.values, modalInfo?['themeType'])
      : null;

  final Map<ScreenType, int> screenActionMap = {};
  if (modalInfo?['weightLoggingModalInfo'] != null) {
    modalInfo!['weightLoggingModalInfo']['screenActionMap'].forEach((k, v) {
      screenActionMap[EnumToString.fromString(ScreenType.values, k) ??
          ScreenType.WEIGHT_LOGGING] = v;
    });
  }
  final String subCategoryCode = modalInfo?['subCategoryCode'] ?? "";
  UserMetricState? initialState =
      modalInfo?['weightLoggingModalInfo']['initialState'] != null
          ? EnumToString.fromString(UserMetricState.values,
              modalInfo!['weightLoggingModalInfo']['initialState'])
          : UserMetricState.DECREASE_WEIGHT_LOGS;

  final num? targetWeight = modalInfo?['weightLoggingModalInfo'] != null
      ? modalInfo!['weightLoggingModalInfo']['targetWeight']
      : null;

  final num latestWeight =
      modalInfoList[_selectedPageModalIndex].focusValue ?? 0;

  bool metricUpdated = false;

  TextEditingController textController = new TextEditingController();

  void updateTextController(TextEditingController textController) {
    if (modalInfoList[_selectedPageModalIndex].focusValue != null)
      textController.text =
          modalInfoList[_selectedPageModalIndex].focusValue == 0
              ? "-"
              : modalInfoList[_selectedPageModalIndex].focusValue.toString();
  }

  updateTextController(textController);

  void buttonAction() {
    if (modalInfoList[_selectedPageModalIndex].actionButton!.type != null &&
        modalInfoList[_selectedPageModalIndex].actionButton!.type ==
            ActionTypes.UPDATE_USER_METRICS) {
      List<dynamic> updates = [];
      try {
        double val = double.parse(textController.text);
        if (val > 0)
          updates.add({
            "metricId": modalInfoList[_selectedPageModalIndex].metricId,
            "value": val
          });
      } catch (e) {
        print(e);
      }
      if (updates.isNotEmpty) {
        ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
        PerformActionEvent event = PerformActionEvent(new Action(
            type: ActionTypes.UPDATE_USER_METRICS, meta: {"updates": updates,"subCategoryCode": subCategoryCode}));
        RepositoryProvider.of<AnalyticsRepository>(context).logButtonClickEvent(
          extraInfo: {
            "action": "Metrics update",
            "metricType": updates,
            "modalFrom": "Weight Logging Modal"
          },
        );
        actionBloc.add(event);
      }
    } else if (modalInfoList[_selectedPageModalIndex].actionButton != null) {
      ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
      PerformActionEvent event = PerformActionEvent(
          modalInfoList[_selectedPageModalIndex].actionButton!);
      actionBloc.add(event);
    }
  }

  Widget _renderWidget(int index) {
    return Column(
      key: ValueKey<int>(index),
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            if (modalInfoList[index].description != null)
              Container(
                padding: const EdgeInsets.symmetric(
                    vertical: Spacings.x2, horizontal: Spacings.x8),
                child: Text(
                  modalInfoList[index].description ?? "",
                  style: AuroraTheme.of(context).textStyle(TypescaleValues.P1),
                  textAlign: TextAlign.center,
                ),
              ),
            Container(
              padding: const EdgeInsets.symmetric(
                  vertical: Spacings.x2, horizontal: Spacings.x8),
              child: Text(
                modalInfoList[index].title ?? "",
                style: AuroraTheme.of(context).textStyle(TypescaleValues.H1),
                textAlign: TextAlign.center,
              ),
            ),
            if (modalInfoList[index].imageUrl != null)
              Container(
                alignment: Alignment.center,
                padding: const EdgeInsets.symmetric(vertical: Spacings.x6),
                child: CFNetworkImage(
                  imageUrl: getImageUrl(context,
                      imagePath: modalInfoList[index].imageUrl!),
                  height: scale(
                      context, modalInfoList[index].creativeHeight ?? 150),
                ),
              ),
            if (modalInfoList[index].lottieUrl != null)
              Container(
                alignment: Alignment.center,
                padding: const EdgeInsets.symmetric(vertical: Spacings.x6),
                child: Lottie.network(
                  getMediaUrl(modalInfoList[index].lottieUrl!),
                  height: scale(
                      context, modalInfoList[index].creativeHeight ?? 150),
                ),
              ),
          ],
        ),
        Column(
          children: [
            if (modalInfoList[index].chartValues != null)
              Padding(
                padding: const EdgeInsets.symmetric(vertical: Spacings.x4),
                child: AnimatedLineGraph(
                  values: modalInfoList[index].chartValues!,
                  focusValue: modalInfoList[index].focusValue,
                  unit: modalInfoList[index].unit,
                  width: scale(context, 280),
                  height: scale(context, 150),
                ),
              ),
            if (modalInfoList[index].subtitle != null)
              Container(
                padding: const EdgeInsets.symmetric(
                    vertical: Spacings.x2, horizontal: Spacings.x8),
                child: Text(
                  modalInfoList[index].subtitle ?? "",
                  style: AuroraTheme.of(context)
                      .textStyle(TypescaleValues.TAGTEXT),
                  textAlign: TextAlign.center,
                ),
              ),
            if (modalInfoList[index].focusValue != null &&
                modalInfoList[index].focusValue != 0.0)
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  GestureDetector(
                    child: Container(
                      width: 45,
                      height: 45,
                      margin: EdgeInsets.only(right: Spacings.x6),
                      decoration: new BoxDecoration(
                          color: Colors.white.withOpacity(0.28),
                          shape: BoxShape.circle),
                      child: Icon(Icons.remove, color: Colors.white),
                    ),
                    onTap: () {
                      double val = 0;
                      if (textController.text != "" &&
                          textController.text != "-") {
                        val = double.parse(textController.text);
                      }
                      textController.text =
                          (max(0, val - 0.2)).toStringAsFixed(1);
                    },
                  ),
                  ConstrainedBox(
                    constraints: BoxConstraints(minWidth: 50, maxWidth: 150),
                    child: IntrinsicWidth(
                      child: TextField(
                        textInputAction: TextInputAction.next,
                        style: AuroraTheme.of(context)
                            .textStyle(TypescaleValues.H7),
                        decoration: InputDecoration(
                          border: InputBorder.none,
                        ),
                        controller: textController,
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(
                              RegExp(r'^(\d+)?\.?\d{0,2}')),
                        ],
                      ),
                    ),
                  ),
                  SizedBox(width: Spacings.x1),
                  Text(modalInfoList[index].unit ?? "",
                      style: AuroraTheme.of(context)
                          .textStyle(TypescaleValues.H3, color: Colors.white)),
                  GestureDetector(
                    child: Container(
                      width: 45,
                      height: 45,
                      margin: EdgeInsets.only(left: Spacings.x6),
                      decoration: new BoxDecoration(
                          color: Colors.white.withOpacity(0.28),
                          shape: BoxShape.circle),
                      child: Icon(Icons.add, color: Colors.white),
                    ),
                    onTap: () {
                      double val = 0;
                      if (textController.text != "" &&
                          textController.text != "-") {
                        val = double.parse(textController.text);
                      }
                      textController.text = (val + 0.2).toStringAsFixed(1);
                    },
                  )
                ],
              ),
          ],
        )
      ],
    );
  }

  CancelableOperation<Null>? cancelableOperation;
  StreamController<bool> modalController = StreamController<bool>();

  showModalBottomSheet<void>(
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(20),
        topRight: Radius.circular(20),
      ),
    ),
    isScrollControlled: true,
    context: context,
    builder: (context) {
      return Padding(
        padding: MediaQuery.of(context).viewInsets,
        child: Wrap(
          children: [
            BlurView(
              backgroundColor: themeType == CanvasTheme.NIGHT
                  ? Color.fromRGBO(57, 68, 116, 1)
                  : Color.fromRGBO(32, 77, 89, 1),
              opacity: 0.8,
              blurType: BlurType.HIGH,
              child: StatefulBuilder(builder: (BuildContext context, setState) {
                if (_selectedPageModalIndex ==
                        screenActionMap[ScreenType.ACKNOWLEDGE_STATE] ||
                    _selectedPageModalIndex ==
                        screenActionMap[
                            ScreenType.TARGET_WEIGHT_ACKNOWLEDGE_STATE]) {
                  modalController.stream.listen((event) {
                    cancelableOperation?.cancel();
                  });
                  cancelableOperation = CancelableOperation.fromFuture(
                    Future.delayed(Duration(seconds: 3), () {
                      Navigator.of(context).pop();
                    }),
                  );
                }
                return Container(
                  width: double.infinity,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        margin:
                            const EdgeInsets.symmetric(vertical: Spacings.x2),
                        height: 2.5,
                        width: 55,
                        alignment: Alignment.topCenter,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(5),
                          color: Colors.white.withOpacity(0.3),
                        ),
                      ),
                      AnimatedSwitcher(
                        switchInCurve: Curves.linear,
                        switchOutCurve: Curves.linear,
                        duration: Duration(milliseconds: 500),
                        transitionBuilder:
                            (Widget child, Animation<double> animation) {
                          final inAnimation = Tween<Offset>(
                                  begin: Offset(0.0, 1.0),
                                  end: Offset(0.0, 0.0))
                              .animate(animation);
                          final outAnimation = Tween<Offset>(
                                  begin: Offset(0.0, -1.0),
                                  end: Offset(0.0, 0.0))
                              .animate(animation);
                          if (child.key ==
                              ValueKey<int>(_selectedPageModalIndex)) {
                            return ClipRect(
                                child: SlideTransition(
                                    position: inAnimation, child: child));
                          }
                          return ClipRect(
                              child: SlideTransition(
                                  position: outAnimation, child: child));
                        },
                        child: _renderWidget(_selectedPageModalIndex),
                      ),
                      SizedBox(
                        height: Spacings.x2,
                      ),
                      Padding(
                        padding: EdgeInsets.only(
                            left: Spacings.x4,
                            right: Spacings.x4,
                            bottom: Spacings.x4),
                        child: modalInfoList[_selectedPageModalIndex]
                                    .actionButton !=
                                null
                            ? PrimaryButton(
                                () {
                                  metricUpdated = true;
                                  buttonAction.call();
                                  num userWeightLog = 0.0;
                                  try {
                                    userWeightLog =
                                        double.parse(textController.text);
                                  } catch (e) {
                                    print(e);
                                  }
                                  setState(() {
                                    if (initialState ==
                                        UserMetricState
                                            .TARGET_WEIGHT_ACHIEVED) {
                                      _selectedPageModalIndex = screenActionMap[
                                          ScreenType
                                              .TARGET_WEIGHT_ACKNOWLEDGE_STATE]!;
                                      initialState = UserMetricState.NONE;
                                    } else if (initialState ==
                                        UserMetricState.WEIGHT_LOGGED_IN) {
                                      _selectedPageModalIndex = screenActionMap[
                                          ScreenType.ACKNOWLEDGE_STATE]!;
                                      initialState = UserMetricState.NONE;
                                    } else if (initialState ==
                                        UserMetricState.FEW_WEIGHT_LOGS) {
                                      Navigator.pop(context);
                                      initialState = UserMetricState.NONE;
                                    } else if (initialState ==
                                        UserMetricState.INCREASE_WEIGHT_LOGS) {
                                      print(userWeightLog);
                                      print(latestWeight);
                                      if (userWeightLog >= latestWeight &&
                                          screenActionMap.containsKey(
                                              ScreenType.WEIGHT_INCREASE)) {
                                        _selectedPageModalIndex =
                                            screenActionMap[
                                                ScreenType.WEIGHT_INCREASE]!;
                                        updateTextController(textController);
                                        initialState = UserMetricState.NONE;
                                      } else if (userWeightLog < latestWeight &&
                                          screenActionMap.containsKey(
                                              ScreenType.ACKNOWLEDGE_STATE)) {
                                        _selectedPageModalIndex =
                                            screenActionMap[
                                                ScreenType.ACKNOWLEDGE_STATE]!;
                                        initialState = UserMetricState.NONE;
                                      } else {
                                        Navigator.pop(context);
                                        initialState = UserMetricState.NONE;
                                      }
                                    } else if (initialState ==
                                        UserMetricState.PLATEAU_WEIGHT_LOGS) {
                                      if (userWeightLog > latestWeight &&
                                          screenActionMap.containsKey(
                                              ScreenType.WEIGHT_INCREASE)) {
                                        _selectedPageModalIndex =
                                            screenActionMap[
                                                ScreenType.WEIGHT_INCREASE]!;
                                        updateTextController(textController);
                                        initialState = UserMetricState.NONE;
                                      } else if (userWeightLog ==
                                              latestWeight &&
                                          screenActionMap.containsKey(
                                              ScreenType.PLATEAU_STATE)) {
                                        _selectedPageModalIndex =
                                            screenActionMap[
                                                ScreenType.PLATEAU_STATE]!;
                                        updateTextController(textController);
                                        initialState = UserMetricState.NONE;
                                      } else if (userWeightLog < latestWeight &&
                                          screenActionMap.containsKey(
                                              ScreenType.ACKNOWLEDGE_STATE)) {
                                        _selectedPageModalIndex =
                                            screenActionMap[
                                                ScreenType.ACKNOWLEDGE_STATE]!;
                                        initialState = UserMetricState.NONE;
                                      } else {
                                        Navigator.pop(context);
                                        initialState = UserMetricState.NONE;
                                      }
                                    } else if (initialState ==
                                        UserMetricState.DECREASE_WEIGHT_LOGS) {
                                      if (targetWeight == null) {
                                        if (userWeightLog > latestWeight &&
                                            screenActionMap.containsKey(
                                                ScreenType.WEIGHT_INCREASE)) {
                                          _selectedPageModalIndex =
                                              screenActionMap[
                                                  ScreenType.WEIGHT_INCREASE]!;
                                          updateTextController(textController);
                                          initialState = UserMetricState.NONE;
                                        } else if (userWeightLog <=
                                                latestWeight &&
                                            screenActionMap.containsKey(
                                                ScreenType.ACKNOWLEDGE_STATE)) {
                                          _selectedPageModalIndex =
                                              screenActionMap[ScreenType
                                                  .ACKNOWLEDGE_STATE]!;
                                          initialState = UserMetricState.NONE;
                                        } else {
                                          Navigator.pop(context);
                                          initialState = UserMetricState.NONE;
                                        }
                                      } else {
                                        if (userWeightLog > latestWeight &&
                                            screenActionMap.containsKey(
                                                ScreenType.WEIGHT_INCREASE)) {
                                          _selectedPageModalIndex =
                                              screenActionMap[
                                                  ScreenType.WEIGHT_INCREASE]!;
                                          updateTextController(textController);
                                          initialState = UserMetricState.NONE;
                                        } else if (userWeightLog <
                                                latestWeight &&
                                            userWeightLog <= targetWeight &&
                                            latestWeight > targetWeight &&
                                            screenActionMap.containsKey(
                                                ScreenType.TARGET_ACHIEVE)) {
                                          _selectedPageModalIndex =
                                              screenActionMap[
                                                  ScreenType.TARGET_ACHIEVE]!;
                                          updateTextController(textController);
                                          initialState = UserMetricState
                                              .TARGET_WEIGHT_ACHIEVED;
                                        } else if (userWeightLog <=
                                                latestWeight &&
                                            screenActionMap.containsKey(
                                                ScreenType.ACKNOWLEDGE_STATE)) {
                                          _selectedPageModalIndex =
                                              screenActionMap[ScreenType
                                                  .ACKNOWLEDGE_STATE]!;
                                          initialState = UserMetricState.NONE;
                                        } else {
                                          Navigator.pop(context);
                                          initialState = UserMetricState.NONE;
                                        }
                                      }
                                    } else {
                                      Navigator.pop(context);
                                      initialState = UserMetricState.NONE;
                                    }
                                  });
                                },
                                modalInfoList[_selectedPageModalIndex]
                                        .actionButton
                                        ?.title ??
                                    "UPDATE NOW",
                              )
                            : Container(),
                      ),
                    ],
                  ),
                );
              }),
            ),
          ],
        ),
      );
    },
  ).then((value) {
    modalController.add(true);
  });
}

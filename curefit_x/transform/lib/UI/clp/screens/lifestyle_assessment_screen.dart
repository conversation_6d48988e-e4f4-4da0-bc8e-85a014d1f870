import 'package:common/action/action_handler.dart';
import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/blocs/navigation/navigation_bloc.dart';
import 'package:common/blocs/navigation/state.dart';
import 'package:common/model/error_prop_model.dart';
import 'package:common/ui/atoms/floating_button.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/page/basic_page_container.dart';
import 'package:common/ui/screens/error_screen.dart';
import 'package:common/ui/widget_factory.dart';
import 'package:common/util/action_util.dart';
import 'package:navigation_history_observer/navigation_history_observer.dart';
import 'package:common/ui/aurora.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/lifestyle_assessment/events.dart';
import 'package:transform/blocs/lifestyle_assessment/lifestyle_assessment_bloc.dart';
import 'package:transform/blocs/lifestyle_assessment/state.dart';
import 'package:transform/constants/constants.dart';

class LifestyleAssessmentScreen extends StatefulWidget {
  const LifestyleAssessmentScreen({Key? key}) : super(key: key);

  @override
  State<LifestyleAssessmentScreen> createState() =>
      _LifestyleAssessmentScreenState();
}

class _LifestyleAssessmentScreenState extends State<LifestyleAssessmentScreen>
    with WidgetsBindingObserver {
  LifestyleAssessmentScreenArguments? screenArguments;

  LifestyleAssessmentScreenArguments? getScreenArguments() {
    final ScreenArguments? args =
        ModalRoute.of(context)!.settings.arguments as ScreenArguments?;
    if (args != null) {
      LifestyleAssessmentScreenArguments arguments =
          LifestyleAssessmentScreenArguments(args.params);
      return arguments;
    }
    return null;
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      screenArguments = getScreenArguments();
      final lifestyleBloc =
          BlocProvider.of<LifestyleAssessmentScreenBloc>(context);
      lifestyleBloc.add(ResetLifestyleAssessmentEvent());
      lifestyleBloc.add(LoadLifestyleAssessmentEvent(
          showLoader: true, subCategoryCode: screenArguments?.subCategoryCode));
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  refresh({required BuildContext context, bool showLoader = false}) {
    screenArguments = getScreenArguments();
    final lifestyleBloc =
        BlocProvider.of<LifestyleAssessmentScreenBloc>(context);
    lifestyleBloc.add(LoadLifestyleAssessmentEvent(
        showLoader: true, subCategoryCode: screenArguments?.subCategoryCode));
  }

  onBackPress() {
    if (Navigator.canPop(context)) {
      Navigator.pop(context);
    } else if (!Navigator.canPop(context)) {
      ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
      actionBloc.add(CloseApplicationEvent(shouldReset: true));
    }
  }

  logPageView() {
    AnalyticsRepository analyticsRepository =
        RepositoryProvider.of<AnalyticsRepository>(context);
    analyticsRepository.logPageViewEvent(
        pageId:
            EnumToString.convertToString(RouteNames.tf_lifestyle_assessment),
        eventInfo: {});
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    switch (state) {
      case AppLifecycleState.resumed:
        logPageView();
        refresh(context: context, showLoader: false);
        break;
      case AppLifecycleState.inactive:
        break;
      case AppLifecycleState.paused:
        break;
      case AppLifecycleState.detached:
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: Colors.transparent,
      body: Stack(
        children: [
          Aurora(
            context: context,
            canvasTheme: CanvasTheme.CLASSIC,
            size: MediaQuery.of(context).size,
          ),
          Positioned.fill(
            child: BlocListener<NavigationBloc, NavigationState>(
              listener: (context, state) {
                if (state is NavigationStackUpdated &&
                    state.action == NavigationStackAction.pop &&
                    state.route?.settings.name ==
                        '/${EnumToString.convertToString(RouteNames.tf_referral)}') {
                  refresh(context: context, showLoader: false);
                }
              },
              child: BlocBuilder<LifestyleAssessmentScreenBloc,
                  LifestyleAssessmentScreenState>(
                builder: (context, state) {
                  List<Widget> _widgets = [];
                  WidgetFactory widgetFactory =
                      RepositoryProvider.of<WidgetFactory>(context);
                  if (state is LifestyleAssessmentScreenLoadedState) {
                    List<Widget> widgets = state.screenData.widgets
                        .map<Widget>(
                            (widget) => widgetFactory.createWidget(widget))
                        .toList();
                    _widgets = widgets;
                  } else if (state is LifestyleAssessmentScreenLoadingState &&
                      state.screenData != null) {
                    List<Widget> widgets = state.screenData!.widgets
                        .map<Widget>(
                            (widget) => widgetFactory.createWidget(widget))
                        .toList();
                    _widgets = widgets;
                  } else if (state is LifestyleAssessmentScreenErrorState) {
                    return Stack(
                      children: [
                        Container(
                          color: Colors.black,
                          child: ErrorScreen(
                              errorInfo: state.errorInfo ?? UnknownError()),
                        ),
                        Positioned(
                          right: 5,
                          top: 30,
                          child: IconButton(
                            onPressed: () {
                              if (Navigator.canPop(context)) {
                                Navigator.pop(context);
                              } else if (!Navigator.canPop(context)) {
                                ActionBloc actionBloc =
                                    BlocProvider.of<ActionBloc>(context);
                                actionBloc.add(
                                    CloseApplicationEvent(shouldReset: false));
                              }
                            },
                            icon: const Icon(
                              Icons.clear,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    );
                  }
                  return BasicPageContainer(
                    shouldBuildWidget: false,
                    showTitleBar: true,
                    itemBuilder: (BuildContext context, Widget currentWidget,
                        int index) {
                      return _widgets[index];
                    },
                    widgetData: _widgets,
                    onBackPressed: () {
                      onBackPress();
                    },
                    title: state is LifestyleAssessmentScreenLoadedState
                        ? state.screenData.title
                        : "Your Lifestyle score",
                    showLoader: false,
                    floatingCTA: state
                                is LifestyleAssessmentScreenLoadedState &&
                            state.screenData.action != null
                        ? FloatingButton(
                            onPress: () {
                              clickActionWithAnalytics(
                                  state.screenData.action!, context, null, {});
                            },
                            buttonText: state.screenData.action!.title ?? "",
                            titleText: state.screenData.action?.description,
                          )
                        : null,
                  );
                },
              ),
            ),
          ),
          Positioned.fill(
            child: BlocBuilder<LifestyleAssessmentScreenBloc,
                LifestyleAssessmentScreenState>(
              builder: (context, state) {
                if (state is LifestyleAssessmentScreenLoadingState &&
                    state.showLoader) {
                  return Center(child: FancyLoadingIndicator());
                }
                return Container();
              },
            ),
          ),
        ],
      ),
    );
  }
}

class LifestyleAssessmentScreenArguments {
  String? subCategoryCode;

  LifestyleAssessmentScreenArguments(Map<String, dynamic> params) {
    subCategoryCode = params["subCategoryCode"];
  }
}

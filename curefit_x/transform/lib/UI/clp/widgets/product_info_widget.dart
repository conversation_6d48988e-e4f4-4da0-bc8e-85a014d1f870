import 'package:cached_network_image/cached_network_image.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/clp/models.dart';

class ProductInfoWidget extends StatelessWidget {
  final ProductInfoWidgetData widgetData;

  const ProductInfoWidget({required this.widgetData});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: Spacings.x4),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          if (widgetData.title != null)
            Text(
              widgetData.title ?? "",
              style: AuroraTheme.of(context).textStyle(TypescaleValues.H10,
                  color: Color.fromRGBO(255, 240, 210, 1)),
              textAlign: TextAlign.center,
            ),
          if (widgetData.subtitle != null)
            Text(
              widgetData.subtitle ?? "",
              style: AuroraTheme.of(context).textStyle(TypescaleValues.P4,
                  color: Color.fromRGBO(255, 240, 210, 1)),
              textAlign: TextAlign.center,
            ),
          if (widgetData.cards != null)
            Container(
              padding: (widgetData.title != null || widgetData.subtitle != null)
                  ? EdgeInsets.only(top: Spacings.x8)
                  : null,
              child: Wrap(
                spacing: Spacings.x2,
                runSpacing: Spacings.x2,
                children: widgetData.cards!
                    .map(
                      (card) => InformationCard(
                        infoCard: card,
                      ),
                    )
                    .toList(),
              ),
            ),
          if (widgetData.action != null)
            Padding(
              padding: const EdgeInsets.only(top: Spacings.x2),
              child: PrimaryButton(() {
                ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
                PerformActionEvent event = PerformActionEvent(widgetData.action!);
                actionBloc.add(event);
              }, widgetData.action?.title ?? "NAVIGATE"),
            ),
          if (widgetData.footerText != null)
            Padding(
              padding: const EdgeInsets.only(top: Spacings.x4),
              child: Text(
                widgetData.footerText ?? "",
                style: AuroraTheme.of(context).textStyle(TypescaleValues.P10,
                    color: Color.fromRGBO(255, 240, 210, 1)),
                textAlign: TextAlign.center,
              ),
            ),
        ],
      ),
    );
  }
}

class InformationCard extends StatelessWidget {
  final ProductInfoCard infoCard;

  const InformationCard({required this.infoCard});

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width - 50;
    return InkWell(
      splashColor:
          infoCard.action != null ? Colors.white24 : Colors.transparent,
      onTap: () {
        if (infoCard.action != null) {
          ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
          PerformActionEvent event = PerformActionEvent(infoCard.action!);
          actionBloc.add(event);
        }
      },
      child: Container(
        height: width / 2 - 1,
        width: width / 2 - 1,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(8)),
          image: DecorationImage(
            image: CachedNetworkImageProvider(
              getImageUrl(context, imagePath: infoCard.backgroundImageUrl),
              maxHeight: (width / 2 - 1).toInt(),
              maxWidth: (width / 2 - 1).toInt(),
            ),
            fit: BoxFit.fill,
          ),
        ),
        child: Align(
          alignment: Alignment.bottomCenter,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: Spacings.x2),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                if (infoCard.title != null)
                  Text(
                    infoCard.title ?? "",
                    style: AuroraTheme.of(context).textStyle(
                      TypescaleValues.H7,
                    ),
                    textAlign: TextAlign.center,
                  ),
                if (infoCard.subtitle != null)
                  Text(
                    infoCard.subtitle ?? "",
                    style: AuroraTheme.of(context).textStyle(
                      TypescaleValues.P8,
                    ),
                    textAlign: TextAlign.center,
                  ),
                SizedBox(
                  height: Spacings.x4,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

import 'package:common/ui/header/card_header.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:flutter/material.dart';
import 'package:transform/blocs/clp/models.dart';

class TransformHeaderWidget extends StatelessWidget {
  final TransformHeaderWidgetData widgetData;

  const TransformHeaderWidget({required this.widgetData});

  @override
  Widget build(BuildContext context) {
    Gradient gradient = LinearGradient(stops: const [
      0.0,
      1.0,
    ], colors: widgetData.gradientColors);
    return Container(
      padding: EdgeInsets.symmetric(horizontal: Spacings.x4),
      child: Column(
        crossAxisAlignment:
            widgetData.titleAlignment == HeaderAlignmentType.CENTER
                ? CrossAxisAlignment.center
                : CrossAxisAlignment.start,
        children: [
          if (widgetData.description != null)
            ShaderMask(
              shaderCallback: (bounds) => gradient.createShader(
                Rect.fromLTRB(0, 0, bounds.width, bounds.height),
              ),
              child: Text(
                widgetData.description ?? "",
                style: AuroraTheme.of(context).textStyle(TypescaleValues.P6),
                textAlign:
                    widgetData.titleAlignment == HeaderAlignmentType.CENTER
                        ? TextAlign.center
                        : TextAlign.left,
              ),
            ),
          if (widgetData.title != null)
            Container(
              padding: const EdgeInsets.only(top: Spacings.x1),
              child: Text(
                widgetData.title ?? "",
                style: AuroraTheme.of(context)
                    .textStyle(TypescaleValues.H9, color: Colors.white),
                textAlign:
                    widgetData.titleAlignment == HeaderAlignmentType.CENTER
                        ? TextAlign.center
                        : TextAlign.left,
              ),
            ),
          if (widgetData.subtitle != null)
            Container(
              padding: const EdgeInsets.only(top: Spacings.x1),
              child: Text(
                widgetData.subtitle ?? "",
                style: AuroraTheme.of(context)
                    .textStyle(TypescaleValues.P2, color: Colors.white60),
                textAlign:
                    widgetData.titleAlignment == HeaderAlignmentType.CENTER
                        ? TextAlign.center
                        : TextAlign.left,
              ),
            ),
        ],
      ),
    );
  }
}

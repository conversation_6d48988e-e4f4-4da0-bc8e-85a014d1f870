import 'package:collection/collection.dart';
import 'package:common/action/action_handler.dart';
import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/atoms/radio_button.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/flutter_scale_tap.dart';
import 'package:common/ui/molecules/plan_card.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:common/ui/widget_factory.dart';
import 'package:common/util/action_util.dart';
import 'package:flutter/material.dart' hide Action;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:transform/blocs/clp/models.dart';
import 'package:transform/blocs/pack_purchase/models.dart';

class TransformPackTabWidget extends StatefulWidget {
  final TransformPackTabWidgetData widgetData;

  const TransformPackTabWidget({required this.widgetData, Key? key})
      : super(key: key);

  @override
  State<TransformPackTabWidget> createState() => _TransformPackTabWidgetState();
}

class _TransformPackTabWidgetState extends State<TransformPackTabWidget> {
  int selectedTab = 0;
  int selectedPackTab = 0;
  String selectedProduct = "WEIGHT_LOSS";

  @override
  void initState() {
    super.initState();
    selectedTab = widget.widgetData.initialTab;
    selectedProduct = widget.widgetData.initialProduct;
    selectedPackTab = 0;
    checkForSavedProductType();
  }

  @override
  void dispose() {
    removeSavedProductType();
    super.dispose();
  }

  checkForSavedProductType() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String tabProductType = await prefs.getString('tabProductType') ?? "";
    String selectedProductType =
        await prefs.getString('selectedProductType') ?? "";
    if (widget.widgetData.tabData != null) {
      TransformPackTabData? packTabData = widget.widgetData.tabData
          ?.firstWhereOrNull((element) => element.id == tabProductType);
      if (packTabData != null && mounted && packTabData.tabItems != null) {
        TabItem? tabItem = packTabData.tabItems
            ?.firstWhereOrNull((element) => element.id == selectedProductType);
        ModalData? modalData = packTabData.tabItems!
            .elementAt(packTabData.tabItems!.length - 1)
            .modalData;
        ProductItem? productItem = modalData?.productItems
            ?.firstWhereOrNull((element) => element.id == selectedProductType);
        setState(() {
          selectedTab = widget.widgetData.tabData!.indexOf(packTabData);
          if (tabItem != null) {
            selectedProduct = selectedProductType;
            selectedPackTab = packTabData.tabItems!.indexOf(tabItem);
          }
          if (productItem != null) {
            selectedProduct = selectedProductType;
            selectedPackTab = packTabData.tabItems!.length - 1;
          }
          triggerAnalyticsEvent();
        });
      } else {
        triggerAnalyticsEvent();
      }
    } else {
      triggerAnalyticsEvent();
    }
  }

  triggerAnalyticsEvent() {
    RepositoryProvider.of<AnalyticsRepository>(context)
        .logButtonClickEvent(extraInfo: {
      "productSelected": widget.widgetData.tabData?.elementAt(selectedTab).id,
      "packType": selectedProduct,
      "action": "tabLoaded",
    });
  }

  removeSavedProductType() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.remove('tabProductType');
    await prefs.remove('selectedProductType');
  }

  @override
  Widget build(BuildContext context) {
    return widget.widgetData.tabData != null
        ? Container(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  alignment: Alignment.center,
                  width: scale(context, 335),
                  height: (widget.widgetData.tabData!.first.subtitle != null)
                      ? 120
                      : 0,
                  child: ListView.separated(
                    scrollDirection: Axis.horizontal,
                    separatorBuilder: (context, index) {
                      return SizedBox(
                        width: scale(context, Spacings.x2),
                      );
                    },
                    shrinkWrap: true,
                    padding: const EdgeInsets.only(top: Spacings.x4),
                    itemCount: widget.widgetData.tabData!.length,
                    itemBuilder: (BuildContext context, int index) {
                      return index == selectedTab
                          ? (widget.widgetData.tabData!.first.subtitle != null)
                              ? CustomPaint(
                                  painter: DropDownChevron(
                                    containerColor:
                                        Colors.white.withOpacity(0.2),
                                  ),
                                  child: getTabWidget(
                                      widget.widgetData.tabData!
                                          .elementAt(index),
                                      index),
                                )
                              : Container()
                          : Container(
                              decoration: BoxDecoration(
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(10)),
                                  color: Colors.white10),
                              child: getTabWidget(
                                  widget.widgetData.tabData!.elementAt(index),
                                  index),
                            );
                    },
                  ),
                ),
                SizedBox(
                  height: Spacings.x6,
                ),
                getTabBodyWidget(
                    widget.widgetData.tabData!.elementAt(selectedTab), context),
              ],
            ),
          )
        : Container();
  }

  Widget getTabWidget(TransformPackTabData tabData, int index) {
    return InkWell(
      onTap: () {
        setState(() {
          selectedTab = index;
          selectedProduct = widget.widgetData.initialProduct;
          selectedPackTab = 0;
          RepositoryProvider.of<AnalyticsRepository>(context)
              .logButtonClickEvent(extraInfo: {
            "productSelected": widget.widgetData.tabData?.elementAt(index).id,
            "packType": selectedProduct,
            "action": "tabSelected",
          });
        });
      },
      child: Container(
        padding: EdgeInsets.symmetric(
            horizontal: Spacings.x4, vertical: Spacings.x2),
        child: Column(
          children: [
            if (tabData.titleImageUrl != null)
              CFNetworkImage(
                fit: BoxFit.contain,
                width: 70,
                height: 40,
                imageUrl: getImageUrl(context,
                    imagePath: tabData.titleImageUrl ?? ""),
              ),
            if (tabData.title != null)
              Padding(
                padding: const EdgeInsets.only(top: Spacings.x1),
                child: Text(
                  tabData.title ?? "",
                  style: AuroraTheme.of(context).textStyle(TypescaleValues.H1),
                ),
              ),
            if (tabData.subtitle != null)
              Padding(
                padding: const EdgeInsets.only(top: Spacings.x1),
                child: Text(
                  tabData.subtitle ?? "",
                  style: AuroraTheme.of(context)
                      .textStyle(TypescaleValues.P8, color: Colors.white60),
                ),
              )
          ],
        ),
      ),
    );
  }

  Widget getTabBodyWidget(TransformPackTabData tabData, BuildContext context) {
    WidgetFactory widgetFactory = RepositoryProvider.of<WidgetFactory>(context);
    return Column(
      children: [
        if (tabData.widgets != null && tabData.widgets!.length > 0)
          ...widgetFactory.createWidgets(tabData.widgets!),
        if (tabData.imageUrl != null)
          Padding(
            padding:
                const EdgeInsets.only(left: Spacings.x2, right: Spacings.x2),
            child: CFNetworkImage(
              fit: BoxFit.contain,
              width: scale(context, 335),
              height: 200,
              imageUrl: getImageUrl(context,
                  imagePath: widget.widgetData.tabData!
                          .elementAt(selectedTab)
                          .imageUrl ??
                      ""),
            ),
          ),
        if (tabData.refundImageUrl != null)
          InkWell(
            onTap: () {
              clickActionWithAnalytics(
                  widget.widgetData.tabData!
                      .elementAt(selectedTab)
                      .refundImageAction!,
                  context,
                  null,
                  {});
            },
            child: Padding(
              padding: const EdgeInsets.only(
                  bottom: Spacings.x3, left: Spacings.x2, right: Spacings.x2),
              child: CFNetworkImage(
                fit: BoxFit.contain,
                width: scale(context, 335),
                height: 30,
                imageUrl: getImageUrl(context,
                    imagePath: widget.widgetData.tabData!
                            .elementAt(selectedTab)
                            .refundImageUrl ??
                        ""),
              ),
            ),
          ),
        if (tabData.tabItems != null && tabData.tabItems!.isNotEmpty)
          Padding(
            padding: EdgeInsets.symmetric(horizontal: Spacings.x4),
            child: getTabTogglePackWidget(tabData.tabItems!),
          ),
        if (tabData.packItems != null && tabData.packMappingData != null)
          Padding(
            padding: EdgeInsets.symmetric(horizontal: Spacings.x4),
            child: getFitnessPackCard(
                tabData.packItems!, tabData.packMappingData!),
          ),
      ],
    );
  }

  Widget getTabTogglePackWidget(List<TabItem> packTabData) {
    return Column(
      children: [
        Container(
          height: 36,
          width: scale(context, 335),
          decoration: BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(18)),
              border: Border.all(color: Colors.white24),
              color: Colors.white10),
          child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: packTabData.mapIndexed<Widget>((index, data) {
                return InkWell(
                  onTap: () {
                    setState(() {
                      if (data.showPackSelectionModal &&
                          data.modalData != null) {
                        if (data.id != null &&
                            data.id!.isNotEmpty &&
                            data.modalData!.productItems != null &&
                            !data.modalData!.productItems!
                                .contains(selectedProduct)) {
                          selectedProduct = data.id!;
                        }
                        showModal(context: context, modalData: data.modalData!);
                      } else {
                        selectedProduct = data.id ?? "";
                      }
                      selectedPackTab = index;
                    });
                  },
                  child: Container(
                    height: 30,
                    width: scale(context, 320) / 2,
                    padding: EdgeInsets.symmetric(
                        horizontal: Spacings.x5, vertical: Spacings.x1),
                    decoration: index == selectedPackTab
                        ? BoxDecoration(
                            borderRadius: BorderRadius.all(Radius.circular(15)),
                            color: Colors.white)
                        : null,
                    child: Text(
                      data.title ?? "",
                      style: index == selectedPackTab
                          ? AuroraTheme.of(context).textStyle(
                              TypescaleValues.P3,
                              color: Colors.black)
                          : AuroraTheme.of(context)
                              .textStyle(TypescaleValues.P5),
                      textAlign: TextAlign.center,
                    ),
                  ),
                );
              }).toList()),
        ),
        ...packTabData.mapIndexed<Widget>((index, data) {
          return (data.showPackSelectionModal &&
                  data.modalData != null &&
                  selectedPackTab == index &&
                  data.modalData!.productItems != null &&
                  data.modalData!.productItems!.firstWhereOrNull(
                          (item) => item.id == selectedProduct) !=
                      null)
              ? Container(
                  padding: EdgeInsets.only(top: Spacings.x4),
                  child: InkWell(
                    onTap: () {
                      setState(() {
                        showModal(context: context, modalData: data.modalData!);
                      });
                    },
                    child: Row(
                      children: [
                        Text(
                          "Packs for ",
                          style: AuroraTheme.of(context)
                              .textStyle(TypescaleValues.H2),
                          textAlign: TextAlign.left,
                        ),
                        Text(
                          data.modalData!.productItems!
                                  .firstWhereOrNull(
                                      (item) => item.id == selectedProduct)
                                  ?.title ??
                              "",
                          style: AuroraTheme.of(context).textStyle(
                              TypescaleValues.H2,
                              decoration: TextDecoration.underline),
                          textAlign: TextAlign.left,
                        ),
                        Text(
                          " >",
                          style: AuroraTheme.of(context)
                              .textStyle(TypescaleValues.H2),
                          textAlign: TextAlign.left,
                        ),
                      ],
                    ),
                  ),
                )
              : Container();
        }).toList(),
      ],
    );
  }

  void showModal(
      {required BuildContext context, required ModalData modalData}) {
    RepositoryProvider.of<AnalyticsRepository>(context)
        .logButtonClickEvent(extraInfo: {
      "productSelected": widget.widgetData.tabData?.elementAt(selectedTab).id,
      "packType": selectedProduct,
      "action": "ModalOpen",
    });
    showModalBottomSheet(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      isScrollControlled: true,
      context: context,
      builder: (context) {
        return Padding(
            padding: MediaQuery.of(context).viewInsets,
            child: Wrap(children: [
              BlurView(
                backgroundColor: Colors.black54,
                opacity: 0.7,
                child: StatefulBuilder(
                  builder: (BuildContext context, setModalState) {
                    return Container(
                      padding: EdgeInsets.only(
                          left: Spacings.x4,
                          right: Spacings.x4,
                          bottom: Spacings.x4),
                      width: double.infinity,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Center(
                            child: Container(
                              margin: const EdgeInsets.symmetric(
                                  vertical: Spacings.x2),
                              height: 2.5,
                              width: 55,
                              alignment: Alignment.topCenter,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(5),
                                color: Colors.white.withOpacity(0.3),
                              ),
                            ),
                          ),
                          Text(
                            modalData.title ?? "",
                            style: AuroraTheme.of(context)
                                .textStyle(TypescaleValues.H1),
                            textAlign: TextAlign.left,
                          ),
                          if (modalData.productItems != null)
                            ...modalData.productItems!
                                .map(
                                  (item) => Padding(
                                    padding:
                                        const EdgeInsets.only(top: Spacings.x4),
                                    child: CFRadioButton(
                                      selected: item.id == selectedProduct,
                                      text: item.title,
                                      subText: item.subtitle,
                                      onTap: () {
                                        setModalState(() {
                                          setState(() {
                                            selectedProduct = item.id!;
                                            RepositoryProvider.of<
                                                        AnalyticsRepository>(
                                                    context)
                                                .logButtonClickEvent(
                                                    extraInfo: {
                                                  "productSelected": widget
                                                      .widgetData.tabData
                                                      ?.elementAt(selectedTab)
                                                      .id,
                                                  "packType": selectedProduct,
                                                  "action": "ModalOpen",
                                                });
                                            if (Navigator.canPop(context)) {
                                              Navigator.pop(context);
                                            } else {
                                              ActionBloc actionBloc =
                                                  BlocProvider.of<ActionBloc>(
                                                      context);
                                              actionBloc.add(
                                                  CloseApplicationEvent(
                                                      shouldReset: false));
                                            }
                                          });
                                        });
                                      },
                                    ),
                                  ),
                                )
                                .toList(),
                          if (modalData.benefitsTitle != null)
                            Padding(
                              padding: const EdgeInsets.only(top: Spacings.x5),
                              child: Text(
                                modalData.benefitsTitle ?? "",
                                style: AuroraTheme.of(context)
                                    .textStyle(TypescaleValues.P3),
                                textAlign: TextAlign.left,
                              ),
                            ),
                          if (modalData.benefits != null)
                            ...modalData.benefits!
                                .map(
                                  (benefit) => Padding(
                                    padding:
                                        const EdgeInsets.only(top: Spacings.x4),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Icon(
                                          Icons.check_circle_outline_rounded,
                                          color:
                                              Color.fromRGBO(247, 199, 68, 1),
                                          size: 20,
                                        ),
                                        SizedBox(
                                          width: Spacings.x2,
                                        ),
                                        Expanded(
                                          child: Text(
                                            benefit,
                                            style: AuroraTheme.of(context)
                                                .textStyle(TypescaleValues.P2),
                                            textAlign: TextAlign.left,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                )
                                .toList(),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ]));
      },
    );
  }

  Widget getFitnessPackCard(List<CultPackDetailWidgetBodyData> packItems,
      Map<String, List<String>> packMappingData) {
    List<String>? productIds = packMappingData[selectedProduct];
    List<CultPackDetailWidgetBodyData> filteredItems = packItems
        .where((element) =>
            productIds != null && productIds.contains(element.productId))
        .toList();
    return Padding(
      padding: const EdgeInsets.only(top: Spacings.x4),
      child: Column(
        children: filteredItems
            .map((item) => CultPackDetailWidgetBody(
                  item,
                  widgetInfo: widget.widgetData.widgetInfo,
                ))
            .toList(),
      ),
    );
  }
}

class CultPackDetailWidgetBody extends StatelessWidget {
  final WidgetInfo? widgetInfo;
  final CultPackDetailWidgetBodyData cultPackDetailWidgetBodyData;

  const CultPackDetailWidgetBody(this.cultPackDetailWidgetBodyData,
      {this.widgetInfo});

  @override
  Widget build(BuildContext context) {
    return Semantics(
      label: "FITNESS_PACK_WIDGET_" + cultPackDetailWidgetBodyData.title,
      child: ScaleTap(
          onTap: () {
            ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
            Action action =
                Action.fromAction(cultPackDetailWidgetBodyData.action, {
              "heroMeta": {
                "title": cultPackDetailWidgetBodyData.title,
                "subTitle": cultPackDetailWidgetBodyData.subtitle,
                "hexColor": cultPackDetailWidgetBodyData.textColorVariant
              }
            });
            PerformActionEvent event = PerformActionEvent(action);
            actionBloc.add(event);
            RepositoryProvider.of<AnalyticsRepository>(context)
                .logWidgetClick(widgetInfo: widgetInfo, extraInfo: {
              "type":
                  '${cultPackDetailWidgetBodyData.title.toLowerCase()}_${cultPackDetailWidgetBodyData.subtitle.toLowerCase()}'
            });
          },
          child: PlanCard(
            title: cultPackDetailWidgetBodyData.title,
            subtitle: cultPackDetailWidgetBodyData.subtitle,
            textColorVariant: cultPackDetailWidgetBodyData.textColorVariant,
            priceMeta: cultPackDetailWidgetBodyData.priceMeta,
            price: PlanCardPrice(
                cultPackDetailWidgetBodyData.price.mrp,
                cultPackDetailWidgetBodyData.price.listingPrice,
                cultPackDetailWidgetBodyData.price.currency,
                cultPackDetailWidgetBodyData.price.showPriceCut),
            offers: cultPackDetailWidgetBodyData.offers
                ?.map((e) =>
                    OfferData(title: e.title ?? "", iconType: e.iconType ?? ""))
                .toList(),
            priceBreakup: cultPackDetailWidgetBodyData.priceBreakup,
            actionOffers: cultPackDetailWidgetBodyData.actionOffers
                ?.map((e) => ActionOfferData(
                    action: e.action,
                    prefix: e.prefix,
                    postfix: e.postfix,
                    iconType: e.iconType ?? ""))
                .toList(),
          )),
    );
  }
}

class DropDownChevron extends CustomPainter {
  DropDownChevron({required this.containerColor, this.cornerRadius = 10});

  final Color containerColor;
  final double cornerRadius;

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint1 = new Paint()..color = containerColor;
    final Paint paint2 = new Paint()
      ..color = Colors.white24
      ..strokeCap = StrokeCap.round
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5;
    final height = size.height;
    final width = size.width;
    Path path = Path();
    path.moveTo(cornerRadius, height);
    path.lineTo(width / 2.3, height);
    path.lineTo((width / 2) - 2, (height + height / 6) - 2);
    path.cubicTo((width / 2) - 2, (height + height / 6) - 2, width / 2,
        height + height / 6, (width / 2) + 2, (height + height / 6) - 2);
    path.lineTo((width - width / 2.3), height);
    path.lineTo(width - cornerRadius, height);
    path.cubicTo(width - cornerRadius, height, width, height, width,
        height - cornerRadius);
    path.lineTo(width, cornerRadius);
    path.cubicTo(width, cornerRadius, width, 0, width - cornerRadius, 0);
    path.lineTo(cornerRadius, 0);
    path.cubicTo(cornerRadius, 0, 0, 0, 0, cornerRadius);
    path.lineTo(0, height - cornerRadius);
    path.cubicTo(0, height - cornerRadius, 0, height, cornerRadius, height);
    path.close();
    canvas.drawPath(path, paint1);
    canvas.drawPath(path, paint2);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

import 'dart:math';

import 'package:common/analytics/analytics_repository.dart';
import 'package:common/ui/atoms/date_label.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/ui/widget_factory.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:transform/UI/fitness_plan/widgets/drop_down_chevron.dart';
import 'package:transform/blocs/weekly_workflow_bloc/events.dart';
import 'package:transform/blocs/weekly_workflow_bloc/models.dart';
import 'package:transform/blocs/weekly_workflow_bloc/state.dart';
import 'package:transform/blocs/weekly_workflow_bloc/weekly_workflow_bloc.dart';

class WeeklyWorkflowWidget extends StatefulWidget {
  final WeeklyWorkflowWidgetData widgetData;

  const WeeklyWorkflowWidget({required this.widgetData, Key? key})
      : super(key: key);

  @override
  State<WeeklyWorkflowWidget> createState() => _WeeklyWorkflowWidgetState();
}

class _WeeklyWorkflowWidgetState extends State<WeeklyWorkflowWidget>
    with TickerProviderStateMixin {
  late String _selectedEpoch;
  late WeeklyWorkflowBloc weeklyWorkflowBloc;
  TabController? _tabController;

  bool isSameDate(String first, String second) {
    try {
      var dateFormat = new DateFormat("yMd");
      DateTime firstDateTime =
          new DateTime.fromMillisecondsSinceEpoch(int.parse(first));
      DateTime secondDateTime =
          new DateTime.fromMillisecondsSinceEpoch(int.parse(second));
      String firstDate = dateFormat.format(firstDateTime);
      String secondDate = dateFormat.format(secondDateTime);
      if (firstDate == secondDate) {
        return true;
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  @override
  void initState() {
    super.initState();
    int selectedIndex = 0;
    weeklyWorkflowBloc = BlocProvider.of<WeeklyWorkflowBloc>(context);
    if (weeklyWorkflowBloc.workflowEntries != null &&
        weeklyWorkflowBloc.workflowEntries!.isNotEmpty &&
        weeklyWorkflowBloc.widgets != null &&
        weeklyWorkflowBloc.widgets!.isNotEmpty) {
      weeklyWorkflowBloc.add(WorkflowPreLoadEvent(
          workflowEntries: weeklyWorkflowBloc.workflowEntries,
          widgets: weeklyWorkflowBloc.widgets));
    } else {
      weeklyWorkflowBloc.add(WorkflowPreLoadEvent(
          workflowEntries: widget.widgetData.workflowEntries,
          widgets: widget.widgetData.widgets));
    }
    if (weeklyWorkflowBloc.selectedEpoch != null &&
        !weeklyWorkflowBloc.selectedEpoch!.isEmpty) {
      _selectedEpoch = weeklyWorkflowBloc.selectedEpoch!;
      if (weeklyWorkflowBloc.workflowEntries != null &&
          weeklyWorkflowBloc.workflowEntries!.isNotEmpty) {
        WorkflowEntry currentEntry = weeklyWorkflowBloc.workflowEntries!
            .firstWhere(
                (element) => isSameDate(_selectedEpoch, element.epoch ?? ""),
                orElse: null);
        selectedIndex =
            weeklyWorkflowBloc.workflowEntries!.indexOf(currentEntry);
      }
    } else {
      _selectedEpoch = widget.widgetData.selectedDate;
      if (widget.widgetData.workflowEntries.isNotEmpty) {
        WorkflowEntry currentEntry = widget.widgetData.workflowEntries
            .firstWhere(
                (element) => isSameDate(_selectedEpoch, element.epoch ?? ""),
                orElse: null);
        selectedIndex = widget.widgetData.workflowEntries.indexOf(currentEntry);
      }
    }
    selectedIndex = max(selectedIndex, 0);
    weeklyWorkflowBloc.add(WorkflowDateEvent(selectedEpoch: _selectedEpoch));
    _tabController = TabController(
      vsync: this,
      length: widget.widgetData.workflowEntries.length,
      initialIndex:
          min(selectedIndex, widget.widgetData.workflowEntries.length - 1),
    );
    if (weeklyWorkflowBloc.callScheduled == null) {
      weeklyWorkflowBloc.callScheduled = widget.widgetData.isCallScheduled;
    }
    super.initState();
  }

  @override
  void didUpdateWidget(covariant WeeklyWorkflowWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (weeklyWorkflowBloc.callScheduled != widget.widgetData.isCallScheduled &&
        _selectedEpoch.isNotEmpty) {
      weeklyWorkflowBloc.callScheduled = widget.widgetData.isCallScheduled;
      WeeklyWorkflowBloc workflowBloc =
          BlocProvider.of<WeeklyWorkflowBloc>(context);
      workflowBloc.add(WorkflowDateEvent(selectedEpoch: _selectedEpoch));
      workflowBloc.add(WorkflowLoadEvent(
          epoch: _selectedEpoch,
          subCategoryCode: widget.widgetData.subCategoryCode));
    }
  }

  @override
  Widget build(BuildContext context) {
    WidgetFactory widgetFactory = RepositoryProvider.of<WidgetFactory>(context);
    return BlocBuilder<WeeklyWorkflowBloc, WeeklyWorkflowState>(
        builder: (context, state) {
      return Container(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              // padding: const EdgeInsets.only(top: Spacings.x2),
              child: TabBar(
                tabAlignment: TabAlignment.start,
                dividerHeight: 0,
                indicatorWeight: 14,
                controller: _tabController,
                splashBorderRadius:
                    BorderRadius.all(Radius.circular(Spacings.x2)),
                padding: EdgeInsets.only(left: Spacings.x4, right: Spacings.x4),
                labelPadding: EdgeInsets.symmetric(horizontal: Spacings.x3),
                tabs: widget.widgetData.workflowEntries
                    .map((entry) => Padding(
                          padding: const EdgeInsets.only(
                              top: Spacings.x1, bottom: Spacings.x2),
                          child: DateLabel(
                            date: entry.date,
                            stateChangeEnabled: false,
                            onTapEnabled: false,
                            padding: 0,
                          ),
                        ))
                    .toList(),
                onTap: (int selectedTab) {
                  WorkflowEntry entry =
                      widget.widgetData.workflowEntries[selectedTab];
                  if (_selectedEpoch != entry.epoch!) {
                    _selectedEpoch = entry.epoch!;
                    WeeklyWorkflowBloc workflowBloc =
                        BlocProvider.of<WeeklyWorkflowBloc>(context);
                    workflowBloc
                        .add(WorkflowDateEvent(selectedEpoch: _selectedEpoch));
                    workflowBloc.add(WorkflowLoadEvent(
                        epoch: _selectedEpoch,
                        subCategoryCode: widget.widgetData.subCategoryCode));
                    RepositoryProvider.of<AnalyticsRepository>(context)
                        .logWidgetClick(extraInfo: {
                      "action": "date_change",
                      "value": _selectedEpoch
                    }, widgetInfo: widget.widgetData.widgetInfo);
                  }
                },
                isScrollable: true,
                indicator: DropDownChevronDecoration(
                    containerColor: Colors.white.withOpacity(0.2)),
              ),
            ),
            SizedBox(
              height: Spacings.x2,
            ),
            if (state is WeeklyWorkflowLoading)
              Center(
                child: Center(child: FancyLoadingIndicator()),
              ),
            if (state is WeeklyWorkflowLoaded && state.widgets != null)
              Column(
                key: ValueKey<String>(_selectedEpoch),
                children: applyStaggeredAnimation(
                    widgetFactory.createWidgets(state.widgets!), context),
              ),
            if (state is WeeklyWorkflowNotLoaded)
              Center(
                child: Text(
                  "Workflow Loading Failed",
                  style: AuroraTheme.of(context).textStyle(TypescaleValues.H1),
                ),
              )
          ],
        ),
      );
    });
  }
}

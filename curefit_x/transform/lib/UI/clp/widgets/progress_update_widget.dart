import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/UI/clp/widgets/user_metric_chart_widget.dart';
import 'package:transform/blocs/clp/models.dart';

class ProgressUpdateWidget extends StatelessWidget {
  final ProgressUpdateWidgetData progressUpdateWidgetData;

  const ProgressUpdateWidget(this.progressUpdateWidgetData);

  @override
  Widget build(BuildContext context) {
    return BlurView(
      borderRadius: 15,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.all(
            Radius.circular(15),
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Container(
              margin: EdgeInsets.symmetric(horizontal: 25, vertical: 15),
              alignment: Alignment.topLeft,
              child: Text(
                progressUpdateWidgetData.title,
                style: AuroraTheme.of(context).textStyle(TypescaleValues.H4),
              ),
            ),
            Container(
              width: scale(context, 298),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  if (progressUpdateWidgetData.habitClpWidget != null)
                    createHabitProgressWidget(
                        context, progressUpdateWidgetData.habitClpWidget),
                  if (progressUpdateWidgetData.lifestyleClpWidget != null)
                    createLifeStyleProgressWidget(
                        context, progressUpdateWidgetData.lifestyleClpWidget),
                ],
              ),
            ),
            if (progressUpdateWidgetData.weightGraphWidget != null)
              createProgressGraphWidget(
                  context, progressUpdateWidgetData.weightGraphWidget),
            if (progressUpdateWidgetData.noMeasurementWidget != null)
              createNoMeasurementWidget(
                  context, progressUpdateWidgetData.noMeasurementWidget),
          ],
        ),
      ),
    );
  }

  Widget createHabitProgressWidget(
    BuildContext context,
    HabitProgressClpWidgetData? habitClpWidget,
  ) {
    return GestureDetector(
      onTap: () {
        logWidgetClick(context, "Habit Tab opened from Progress widget");
        ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
        PerformActionEvent event = PerformActionEvent(habitClpWidget!.action!);
        actionBloc.add(event);
      },
      child: Container(
        padding:
            const EdgeInsets.only(top: Spacings.x4, bottom: Spacings.x4, left: Spacings.x4, right: Spacings.x2),
        margin: const EdgeInsets.only(top: 10),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.1),
          borderRadius: BorderRadius.all(
            Radius.circular(10),
          ),
        ),
        width: scale(context, (habitClpWidget!.isCard ?? false) ? 145 : 298),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Row(
                    children: [
                      Container(
                        child: Icon(
                                Icons.wb_twilight_outlined,
                                color: Colors.white,
                                size: scale(context, 24),
                              ),
                      ),
                      Container(
                        padding: const EdgeInsets.only(left: Spacings.x2),
                        child: Text(
                          habitClpWidget.habitCount ?? "",
                          style: AuroraTheme.of(context)
                              .textStyle(TypescaleValues.H7),
                        ),
                      ),
                      if (!(habitClpWidget.isCard ?? false))
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.only(left:5),
                            child: Text(
                              habitClpWidget.description ?? "",
                              style: AuroraTheme.of(context)
                                  .textStyle(TypescaleValues.P5),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
                SizedBox(
                  width: 5,
                ),
                (habitClpWidget.isCard ?? false)
                    ? CircularArrowIcon(
                        size: 25,
                      )
                    : CircularArrowIcon(),
              ],
            ),
            if (habitClpWidget.isCard ?? false)
              Padding(
                padding: const EdgeInsets.only(top: Spacings.x2),
                child: Text(
                  habitClpWidget.description ?? "",
                  style: AuroraTheme.of(context).textStyle(TypescaleValues.P5),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget createLifeStyleProgressWidget(
    BuildContext context,
    LifestyleProgressClpWidgetData? lifestyleClpWidget,
  ) {
    return GestureDetector(
      onTap: () {
        logWidgetClick(
            context, "Lifestyle Score Tab opened from Progress widget");
        ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
        PerformActionEvent event =
            PerformActionEvent(lifestyleClpWidget!.action!);
        actionBloc.add(event);
      },
      child: Container(
        padding:
            const EdgeInsets.only(top: 20, bottom: 20, left: 20, right: 10),
        margin: const EdgeInsets.only(top: 10),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.1),
          borderRadius: BorderRadius.all(
            Radius.circular(10),
          ),
        ),
        width:
            scale(context, (lifestyleClpWidget!.isCard ?? false) ? 145 : 298),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Row(
                    children: [
                      Container(
                        child: lifestyleClpWidget.imageUrl != null
                            ? CFNetworkImage(
                          width: scale(context, 24),
                          height: scale(context, 24),
                          imageUrl: getMediaUrl(lifestyleClpWidget.imageUrl!),
                          errorWidget: (context, url, error) =>
                              Icon(Icons.error),
                        )
                            : Icon(
                          Icons.wb_twilight_outlined,
                          color: Colors.white,
                          size: scale(context, 24),
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.only(left: 10),
                        child: Text(
                          lifestyleClpWidget.lifestyleScore ?? "",
                          style: AuroraTheme.of(context)
                              .textStyle(TypescaleValues.H7),
                        ),
                      ),
                      if (!(lifestyleClpWidget.isCard ?? false))
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.only(left:5),
                            child: Text(
                              lifestyleClpWidget.description ?? "",
                              style: AuroraTheme.of(context)
                                  .textStyle(TypescaleValues.P5),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
                SizedBox(
                  width: 5,
                ),
                (lifestyleClpWidget.isCard ?? false)
                    ? CircularArrowIcon(
                        size: 25,
                      )
                    : CircularArrowIcon(),
              ],
            ),
            if (lifestyleClpWidget.isCard ?? false)
              Padding(
                padding: const EdgeInsets.only(top: 10),
                child: Text(
                  lifestyleClpWidget.description ?? "",
                  style: AuroraTheme.of(context).textStyle(TypescaleValues.P5),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget createProgressGraphWidget(
      BuildContext context, ProgressChartClpData? weightGraphWidget) {
    return Container(
      padding: const EdgeInsets.only(top: Spacings.x4, bottom: Spacings.x4, left: Spacings.x4, right: Spacings.x2),
      margin: const EdgeInsets.only(top: Spacings.x3),
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.1),
        borderRadius: BorderRadius.all(
          Radius.circular(10),
        ),
      ),
      width: scale(context, 298),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GestureDetector(
            onTap: () {
              logWidgetClick(
                  context, "Outcome Tab opened from Progress widget");
              ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
              PerformActionEvent event =
                  PerformActionEvent(weightGraphWidget!.action!);
              actionBloc.add(event);
            },
            child: Container(
              color: Colors.transparent,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Row(
                      children: [
                        if (weightGraphWidget!.progressTextValue != null)
                          Container(
                            child: Icon(
                              Icons.local_fire_department_outlined,
                              color: Colors.white,
                              size: 24,
                            ),
                          ),
                        if (weightGraphWidget.progressTextValue != null)
                          Container(
                            padding: EdgeInsets.symmetric(horizontal: 10),
                            child: Text(
                              weightGraphWidget.progressTextValue ?? "",
                              style: AuroraTheme.of(context)
                                  .textStyle(TypescaleValues.H7),
                            ),
                          ),
                        Expanded(
                          child: Text(
                            weightGraphWidget.subtitle ?? "",
                            style: AuroraTheme.of(context)
                                .textStyle(TypescaleValues.P5),
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(
                    width: 5,
                  ),
                  CircularArrowIcon(),
                ],
              ),
            ),
          ),
          weightGraphWidget.chartItems != null
              ? UserProgressChart(
                  weightGraphWidget.chartItems!,
                  customChartColor: 0xFFFFFFFF,
                )
              : Container(),
        ],
      ),
    );
  }

  Widget createNoMeasurementWidget(
    BuildContext context,
    NoMeasurementProgressWidgetData? noMeasurementWidget,
  ) {
    return GestureDetector(
      onTap: () {
        logWidgetClick(context, "Outcome Tab opened from Progress widget");
        ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
        PerformActionEvent event =
            PerformActionEvent(noMeasurementWidget!.action!);
        actionBloc.add(event);
      },
      child: Container(
        padding:
            const EdgeInsets.only(top: Spacings.x4, bottom: Spacings.x4, left: Spacings.x4, right: Spacings.x2),
        margin: const EdgeInsets.only(top: Spacings.x3),
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.1),
          borderRadius: BorderRadius.all(
            Radius.circular(10),
          ),
        ),
        width: scale(context, 298),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Row(
                children: [
                  Container(
                    child: Icon(
                      Icons.monitor_weight,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                  Expanded(
                    child: Container(
                      padding: EdgeInsets.symmetric(horizontal: 15),
                      child: Text(
                        noMeasurementWidget!.description ?? "",
                        style: AuroraTheme.of(context)
                            .textStyle(TypescaleValues.P5),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(
              width: 5,
            ),
            CircularArrowIcon(),
          ],
        ),
      ),
    );
  }

  logWidgetClick(BuildContext context, String action) {
    RepositoryProvider.of<AnalyticsRepository>(context).logWidgetClick(
        extraInfo: {"action": action},
        widgetInfo: progressUpdateWidgetData.widgetInfo);
  }
}

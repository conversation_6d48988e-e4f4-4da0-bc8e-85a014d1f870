import 'package:collection/collection.dart';
import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/clp/models.dart';

class CoachInfoWidget extends StatelessWidget {
  final CoachInfoWidgetData widgetData;

  const CoachInfoWidget({required this.widgetData, Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding:
          const EdgeInsets.symmetric(horizontal: Spacings.x4),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: widgetData.coachInfoItems!
            .mapIndexed<Widget>(
              (index, cardData) =>
                  index != widgetData.coachInfoItems!.length - 1
                      ? Padding(
                          padding: const EdgeInsets.only(bottom: Spacings.x7),
                          child: CoachInfoCard(
                            cardData: cardData,
                          ),
                        )
                      : CoachInfoCard(
                          cardData: cardData,
                        ),
            )
            .toList(),
      ),
    );
  }
}

class CoachInfoCard extends StatelessWidget {
  final CoachInfoCardData cardData;

  const CoachInfoCard({required this.cardData, Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    AuroraThemeData themeData = AuroraTheme.of(context);
    bool isMultiple = cardData.coachInfoItems!.length > 0;
    return BlurView(
      borderRadius: 20,
      child: Container(
        width: double.infinity,
        child: Padding(
          padding: const EdgeInsets.only(
              top: Spacings.x1,
              left: Spacings.x4,
              right: Spacings.x4,
              bottom: Spacings.x4),
          child: Column(
            children: [
              if (cardData.tagTitle != null)
                Transform.translate(
                  offset: const Offset(0.0, -(Spacings.x1 + 10)),
                  child: BlurView(
                    borderRadius: 20,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: Spacings.x2, vertical: Spacings.x1),
                      child: Text(
                        cardData.tagTitle ?? "",
                        style: themeData.textStyle(TypescaleValues.TAGTEXT),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ),
              if (cardData.coachInfoItems != null)
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: cardData.coachInfoItems!
                      .mapIndexed<Widget>(
                        (index, infoItem) => index !=
                                cardData.coachInfoItems!.length - 1
                            ? Row(
                                children: [
                                  getCoachItemCard(
                                      context, infoItem, isMultiple),
                                  Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: Spacings.x4),
                                    child: Container(
                                      height: scale(
                                          context, 155),
                                      width: 1,
                                      color: Colors.white60,
                                    ),
                                  ),
                                ],
                              )
                            : getCoachItemCard(context, infoItem, isMultiple),
                      )
                      .toList(),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget getCoachItemCard(
      BuildContext context, CoachInfoItem infoItem, bool isHalf) {
    AuroraThemeData themeData = AuroraTheme.of(context);
    return Container(
      width: scale(context, 127),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(40),
            child: CFNetworkImage(
              fit: BoxFit.fill,
              imageUrl:
                  getImageUrl(context, imagePath: infoItem.imageUrl ?? ""),
              placeholder: (BuildContext imageContext, String url) {
                return Container(
                  width: 80,
                  height: 80,
                );
              },
              errorWidget: (context, url, error) => const Icon(Icons.error),
              width: 80,
              height: 80,
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(top: Spacings.x2),
            child: Text(
              infoItem.title ?? "",
              style: themeData.textStyle(TypescaleValues.P3),
              textAlign: TextAlign.center,
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(top: Spacings.x1),
            child: Text(
              infoItem.subtitle ?? "",
              style: themeData.textStyle(
                TypescaleValues.TAGTEXT,
                color: Colors.white60,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget createIconButton(String asset, Color color, VoidCallback callback,
      {Key? key}) {
    return Padding(
        key: key,
        padding: EdgeInsets.all(Spacings.x2),
        child: GestureDetector(
          onTap: () {
            callback();
          },
          child: CFNetworkImage(
              width: 20,
              height: 20,
              imageUrl: asset,
              color: color,
              fit: BoxFit.contain),
        ));
  }

  logWidgetClick(BuildContext context, String action) {
    RepositoryProvider.of<AnalyticsRepository>(context)
        .logWidgetClick(extraInfo: {"action": action}, widgetInfo: null);
  }
}

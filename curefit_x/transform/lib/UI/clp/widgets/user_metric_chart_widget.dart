import 'dart:math';

import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/constants/action_constants.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/util/color.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:transform/blocs/clp/models.dart';

class UserMetricChartWidgetView extends StatelessWidget {
  final UserMetricChartWidgetData userMetricChartWidgetData;

  const UserMetricChartWidgetView(this.userMetricChartWidgetData);

  @override
  Widget build(BuildContext context) {
    return Container(
        margin: EdgeInsets.symmetric(vertical: Spacings.x4),
        child: Column(
          children: [
            (userMetricChartWidgetData.card ?? true)
                ? buildCardWidget(context)
                : buildWidget(context),
          ],
        ));
  }

  Widget buildCardWidget(BuildContext context) {
    return (userMetricChartWidgetData.chartItems != null)
        ? BlurView(child: buildWidget(context))
        : buildCompactWidget(context);
  }

  Widget buildCompactWidget(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8.0),
          child: Text(userMetricChartWidgetData.title!,
              style: AuroraTheme.of(context).textStyle(TypescaleValues.H1)),
        ),
        SizedBox(height: 12),
        BlurView(
            child: Padding(
                padding: EdgeInsets.all(20),
                child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      userMetricChartWidgetData.rightInfo != null
                          ? Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            InfoWidget(
                                info: userMetricChartWidgetData.leftInfo),
                            InfoWidget(
                                info: userMetricChartWidgetData.rightInfo)
                          ])
                          : Container()
                    ])))
      ]),
    );
  }

  Widget buildWidget(BuildContext context) {
    double meterLength = MediaQuery.of(context).size.width * 0.8;
    return userMetricChartWidgetData.title != null
        ? Padding(
      padding: EdgeInsets.all(30),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          userMetricChartWidgetData.title != null
              ? Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                if (userMetricChartWidgetData.title != null)
                  Text(userMetricChartWidgetData.title!,
                      style: AuroraTheme.of(context).textStyle(TypescaleValues.H1)),
                userMetricChartWidgetData.action?.type !=
                    ActionTypes.EMPTY_ACTION
                    ? GestureDetector(
                    onTap: () {
                      ActionBloc actionBloc =
                      BlocProvider.of<ActionBloc>(context);
                      PerformActionEvent event =
                      PerformActionEvent(
                          userMetricChartWidgetData
                              .action!);
                      actionBloc.add(event);
                      RepositoryProvider.of<
                          AnalyticsRepository>(context)
                          .logWidgetClick(
                          extraInfo: {
                            "action": "open progress page",
                            "measurements made": "more than 1",
                          },
                          widgetInfo:
                          userMetricChartWidgetData
                              .widgetInfo);
                    },
                    child: CircularArrowIcon())
                    : Container()
              ])
              : Container(),
          userMetricChartWidgetData.progressText != null
              ? Padding(
            padding: EdgeInsets.only(top: 30),
            child: Row(
              children: [
                CFNetworkImage(
                  width: 70,
                  height: 70,
                  fit: BoxFit.cover,
                  imageUrl: getImageUrl(context,
                      imagePath: userMetricChartWidgetData
                          .progressImageUrl ??
                          ""),
                  errorWidget: (context, url, error) =>
                      Icon(Icons.error),
                ),
                SizedBox(
                  width: 10,
                ),
                Expanded(
                  child: Text(
                    userMetricChartWidgetData.progressText ?? "",
                    style: AuroraTheme.of(context).textStyle(TypescaleValues.H9,
                      color: Color.fromRGBO(15, 228, 152, 1),
                    ),
                  ),
                ),
              ],
            ),
          )
              : Container(),
          userMetricChartWidgetData.rightInfo != null
              ? Padding(
              padding: EdgeInsets.only(top: 40),
              child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    InfoWidget(
                        info: userMetricChartWidgetData.leftInfo),
                    InfoWidget(
                        info: userMetricChartWidgetData.rightInfo)
                  ]))
              : Container(),
          SizedBox(height: 12),
          if (userMetricChartWidgetData.subtitle != null)
            Text(userMetricChartWidgetData.subtitle!,
                style: AuroraTheme.of(context).textStyle(TypescaleValues.P2)),
          userMetricChartWidgetData.chartItems != null
              ? UserProgressChart(userMetricChartWidgetData.chartItems!)
              : Container(),
          userMetricChartWidgetData.bottomInfo != null
              ? Padding(
              padding: EdgeInsets.symmetric(vertical: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(userMetricChartWidgetData.bottomInfo!.title,
                      style: AuroraTheme.of(context).textStyle(TypescaleValues.TAGTEXT,
                          color: HexColor.fromHex("#0FE498"))),
                  Text(
                    userMetricChartWidgetData.bottomInfo!.value
                        .toString(),
                    style: AuroraTheme.of(context).textStyle(TypescaleValues.H2,
                      color: getBmiTextColor(userMetricChartWidgetData
                          .bottomInfo!.value
                          .toDouble()),
                    ),
                  )
                ],
              ))
              : Container(),
          userMetricChartWidgetData.bottomInfo != null &&
              userMetricChartWidgetData.bottomInfo!.title
                  .contains("BMI")
              ? Container(
            height: 70,
            child: Column(
              children: [
                SizedBox(
                  height: 15,
                ),
                Text(
                  'HEALTHY',
                  textAlign: TextAlign.center,
                  style: AuroraTheme.of(context).textStyle(TypescaleValues.TAGTEXT,
                    color: Color.fromRGBO(15, 228, 152, 1),
                  ),
                ),
                Stack(
                  children: [
                    Align(
                      alignment: Alignment.center,
                      child: Container(
                        height: 5,
                        width: meterLength,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(10),
                          color: Color.fromRGBO(255, 255, 255, 0.2),
                        ),
                      ),
                    ),
                    Align(
                      alignment: Alignment.center,
                      child: Container(
                        width: (meterLength) * 0.32,
                        height: 5,
                        color: Color.fromRGBO(15, 228, 152, 1),
                      ),
                    ),
                    Container(
                      alignment: Alignment.center,
                      margin: EdgeInsets.only(
                        left: userMetricChartWidgetData
                            .bottomInfo!.value >
                            21.7
                            ? arrowConfiguration(
                          meterLength,
                          userMetricChartWidgetData
                              .bottomInfo!.value
                              .toDouble(),
                        )
                            : 0,
                        right: userMetricChartWidgetData
                            .bottomInfo!.value <
                            21.7
                            ? arrowConfiguration(
                          meterLength,
                          userMetricChartWidgetData
                              .bottomInfo!.value
                              .toDouble(),
                        )
                            : 0,
                      ),
                      child: Icon(
                        Icons.arrow_drop_up,
                        size: 35,
                        color: getBmiTextColor(
                            userMetricChartWidgetData
                                .bottomInfo!.value
                                .toDouble()),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          )
              : Container(),
          userMetricChartWidgetData.card == false
              ? Container(
            margin: EdgeInsets.only(top: 50),
            height: 1,
            color: Colors.white.withOpacity(0.5),
          )
              : Container()
        ],
      ),
    )
        : Container();
  }

  Color getBmiTextColor(double value) {
    if (value >= 18.5 && value <= 24.9) {
      return Color.fromRGBO(15, 228, 152, 1);
    }
    return Colors.white;
  }

  double arrowConfiguration(double width, double value) {
    double range = 10;
    double pos;
    double initalBmiValue = 21.7;
    if (value <= 11) {
      value = 12;
    }
    if (value >= 31) {
      value = 30;
    }
    pos = ((value - initalBmiValue).abs() * width) / (range);
    return pos.round().toDouble();
  }
}

class InfoWidget extends StatelessWidget {
  final Info? info;

  InfoWidget({this.info});

  @override
  Widget build(BuildContext context) {
    return info != null
        ? Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(info!.title, style: AuroraTheme.of(context).textStyle(TypescaleValues.TAGTEXT)),
        SizedBox(height: 12),
        Row(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Container(
                child: Text(info!.value.toString(),
                    style: AuroraTheme.of(context).textStyle(TypescaleValues.H10))),
            SizedBox(width: 6),
            if (info!.unit != null)
              Container(
                  padding: const EdgeInsets.only(bottom: 5),
                  child: Text(info!.unit!, style: AuroraTheme.of(context).textStyle(TypescaleValues.P3))),
          ],
        )
      ],
    )
        : Container();
  }
}

class UserProgressChart extends StatelessWidget {
  final Color lineColor = HexColor.fromHex("#0FE498");
  final Color areaColor = Colors.white;
  final List<ChartItem> chartItems;
  final int? customChartColor;

  UserProgressChart(this.chartItems, {this.customChartColor});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: <Widget>[
        AspectRatio(
          aspectRatio: 1.70,
          child: Container(
            decoration: const BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(18))),
            child: Padding(
              padding: const EdgeInsets.only(left: 18, right: 18),
              child: LineChart(mainData(context)),
            ),
          ),
        )
      ],
    );
  }

  LineChartData mainData(BuildContext context) {
    return LineChartData(
      gridData: FlGridData(show: false),
      titlesData: FlTitlesData(
        show: true,
        rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
        topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
        leftTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
        bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 22,
              getTitlesWidget: (value, meta) {
                return Container(
                  margin: EdgeInsets.only(top: 8),
                  child: Text((value.toInt() == value && value.toInt() < chartItems.length )? chartItems[value.toInt()].date : "",
                    style: AuroraTheme.of(context).textStyle(TypescaleValues.P10),
                  ),
                );
              },
            )),
      ),
      borderData: FlBorderData(show: false),
      minX: 0,
      maxX: chartItems.length.toDouble() - 1,
      minY: max(0, chartItems.map((e) => e.value).toList().reduce(min) - 1),
      maxY: chartItems.map((e) => e.value).toList().reduce(max) + 1,
      lineBarsData: [
        LineChartBarData(
          spots: [
            for (double i = 0; i < chartItems.length; i++)
              FlSpot(i, chartItems[i.toInt()].value)
          ],
          color:
          customChartColor != null ? Color(customChartColor!) : lineColor,
          barWidth: 3,
          isStrokeCapRound: true,
          dotData: FlDotData(
            show: true,
            checkToShowDot: (spot, barData) {
              if (spot.x == chartItems.length - 1) {
                return true;
              }
              return false;
            },
            getDotPainter: (spot, percent, barData, index) =>
                FlDotCirclePainter(radius: 5, color: Colors.white),
          ),
          belowBarData: BarAreaData(
            show: true,
            color: (customChartColor != null
                ? Color(customChartColor!)
                : areaColor).withOpacity(0.3),
          ),
        ),
      ],
      lineTouchData: LineTouchData(
        touchTooltipData: LineTouchTooltipData(
          tooltipBgColor: HexColor.fromHex("#0FE498"),
          tooltipRoundedRadius: 8,
          getTooltipItems: (List<LineBarSpot> lineBarsSpot) {
            return lineBarsSpot.map((lineBarSpot) {
              return LineTooltipItem(
                lineBarSpot.y.toString(),
                const TextStyle(
                    color: Colors.white, fontWeight: FontWeight.bold),
              );
            }).toList();
          },
        ),
      ),
    );
  }
}

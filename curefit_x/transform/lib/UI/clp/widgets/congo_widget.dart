import 'package:common/font/cf_icons.dart';
import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/util/action_util.dart';
import 'package:common/util/util.dart';
import 'package:flutter/material.dart';

import '../../../blocs/order/models.dart';

class CongoWidget extends StatelessWidget {
  final CongoWidgetData widgetData;
  CongoWidget(this.widgetData);

  @override
  Widget build(BuildContext context) {
    AuroraThemeData themeData = AuroraTheme.of(context);
    return Container(
      height: MediaQuery.of(context).size.height - MediaQuery.of(context).size.height/10,
      child: Column(
        children: [
          Align(
            alignment: Alignment.center,
            child: Blur<PERSON>iew(
              borderRadius: 60,
              child: Container(
                height: 120,
                width: 120,
                decoration: BoxDecoration(shape: BoxShape.circle),
                child: Center(
                  child: Icon(
                    Icons.check_circle_outline_rounded,
                    color: Colors.greenAccent,
                    size: 70,
                  ),
                ),
              ),
            ),
          ),
          SizedBox(
            height: Spacings.x4,
          ),
          Text(
            widgetData.title,
            style: themeData.textStyle(TypescaleValues.H10),
          ),
          SizedBox(
            height: Spacings.x4,
          ),
          Text(
            widgetData.subTitle,
            style: themeData.textStyle(TypescaleValues.P2),
            textAlign: TextAlign.center,
          ),
          if (widgetData.action != null)
          Expanded(child: Container()),
          if (widgetData.action != null)
          Padding(
            padding: const EdgeInsets.only(
                bottom: Spacings.x6, left: Spacings.x2, right: Spacings.x2),
            child: PrimaryButton(() {
              clickActionWithAnalytics(widgetData.action!, context, null, {});
              Navigator.of(context).pop();
            }, "Let’s get started"),
          )
        ],
      ),
    );
  }
}

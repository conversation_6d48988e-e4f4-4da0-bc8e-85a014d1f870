import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/atoms/secondary_button.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/molecules/digital_timer.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/util/action_util.dart';
import 'package:common/util/color.dart';
import 'package:common/util/util.dart';
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:transform/blocs/clp/models.dart';

class TimerCardWidget extends StatelessWidget {
  final TimerCardWidgetData widgetData;

  const TimerCardWidget({required this.widgetData, Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: BlurView(
        borderRadius: 10,
        child: Container(
          height: scale(context, widgetData.cardData != null ? 510 : 375),
          width: scale(context, 335),
          decoration: widgetData.bgImageUrl != null
              ? BoxDecoration(
                  image: DecorationImage(
                    image: NetworkImage(
                        getImageUrl(context, imagePath: widgetData.bgImageUrl)),
                    fit: BoxFit.cover,
                  ),
                  borderRadius: BorderRadius.circular(10),
                )
              : null,
          padding: EdgeInsets.symmetric(horizontal: Spacings.x4),
          child: Column(
            children: [
              Container(
                height: scale(context, 375),
                width: scale(context, 335),
                child: Stack(
                  children: [
                    if (widgetData.lottieUrl != null)
                      Positioned(
                        top: 0,
                        right: 0,
                        left: 0,
                        child: Container(
                          padding: const EdgeInsets.only(bottom: Spacings.x4),
                          child: Lottie.network(
                              getMediaUrl(widgetData.lottieUrl!),
                              frameRate: FrameRate(60),
                              width: scale(context, 300),
                              height: scale(context, 200)),
                        ),
                      ),
                    Positioned(
                      bottom: 0,
                      right: 0,
                      left: 0,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.end,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          if (widgetData.imageUrl != null)
                            Container(
                              padding:
                                  const EdgeInsets.only(bottom: Spacings.x4),
                              child: CFNetworkImage(
                                imageUrl: getImageUrl(context,
                                    imagePath: widgetData.imageUrl!),
                                width: scale(context, 300),
                                height: scale(context, 150),
                              ),
                            ),
                          if (widgetData.title != null)
                            Padding(
                              padding:
                                  const EdgeInsets.only(bottom: Spacings.x1),
                              child: Text(
                                widgetData.title ?? "",
                                style: AuroraTheme.of(context)
                                    .textStyle(TypescaleValues.H1),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          if (widgetData.subtitle != null)
                            Padding(
                              padding:
                                  const EdgeInsets.only(bottom: Spacings.x4),
                              child: Text(
                                widgetData.subtitle ?? "",
                                style: AuroraTheme.of(context).textStyle(
                                    TypescaleValues.P8,
                                    color: Colors.white60),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          if (widgetData.description != null &&
                              widgetData.showGradient)
                            Padding(
                              padding: const EdgeInsets.only(
                                  left: Spacings.x6,
                                  right: Spacings.x6,
                                  top: Spacings.x2),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Expanded(
                                    child: Container(
                                      margin:
                                          EdgeInsets.only(right: Spacings.x1),
                                      height: 1,
                                      decoration: BoxDecoration(
                                        gradient: LinearGradient(
                                          begin: Alignment.centerLeft,
                                          end: Alignment.centerRight,
                                          colors: <Color>[
                                            Colors.white12,
                                            Colors.white60
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                  ConstrainedBox(
                                    constraints: BoxConstraints(
                                        maxWidth: scale(context, 250)),
                                    child: Text(
                                      widgetData.description ?? "",
                                      maxLines: 1,
                                      textAlign: TextAlign.center,
                                      overflow: TextOverflow.ellipsis,
                                      style: AuroraTheme.of(context)
                                          .textStyle(TypescaleValues.TAGTEXT),
                                    ),
                                  ),
                                  Expanded(
                                    child: Container(
                                      margin:
                                          EdgeInsets.only(left: Spacings.x1),
                                      height: 1,
                                      decoration: BoxDecoration(
                                        gradient: LinearGradient(
                                          begin: Alignment.centerLeft,
                                          end: Alignment.centerRight,
                                          colors: <Color>[
                                            Colors.white60,
                                            Colors.white12
                                          ],
                                          tileMode: TileMode.mirror,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            )
                          else if (widgetData.description != null)
                            Padding(
                              padding: const EdgeInsets.only(top: Spacings.x2),
                              child: Text(
                                widgetData.description ?? "",
                                style: AuroraTheme.of(context)
                                    .textStyle(TypescaleValues.TAGTEXT),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          if (widgetData.timer != null &&
                              widgetData.timer!.timerEndTimeWithTz != null)
                            DigitalTimer(
                              endEpochInMilliseconds: convertDateToEpoch(
                                widgetData.timer!.timerEndTimeWithTz,
                                showFullTime: true,
                              ),
                              showDays: true,
                              width: scale(context, 180),
                              height: scale(context, 60),
                            ),
                          if (widgetData.action != null)
                            Padding(
                              padding: const EdgeInsets.only(
                                  top: Spacings.x2, bottom: Spacings.x3),
                              child: SecondaryButton(
                                () {
                                  clickActionWithAnalytics(widgetData.action!,
                                      context, widgetData.widgetInfo, {});
                                  if (widgetData.action!.completionAction !=
                                      null) {
                                    clickActionWithAnalytics(
                                        widgetData.action!.completionAction!,
                                        context,
                                        null, {"trigger":"secondaryAction"});
                                  }
                                },
                                widgetData.action!.title ?? "RENEW NOW",
                                expanded: false,
                              ),
                            )
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              if (widgetData.cardData != null) getWidgetsSeparator(context),
              if (widgetData.cardData != null) getBottomCardWidget(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget getWidgetsSeparator(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: Spacings.x1, bottom: Spacings.x3),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Expanded(
            child: Container(
              margin: EdgeInsets.only(right: Spacings.x1),
              height: 1,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                  colors: <Color>[Colors.white12, Colors.white60],
                ),
              ),
            ),
          ),
          ConstrainedBox(
            constraints: BoxConstraints(maxWidth: scale(context, 300)),
            child: Text(
              " OR ",
              maxLines: 1,
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
              style: AuroraTheme.of(context)
                  .textStyle(TypescaleValues.P3, color: Colors.white60),
            ),
          ),
          Expanded(
            child: Container(
              margin: EdgeInsets.only(left: Spacings.x1),
              height: 1,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                  colors: <Color>[Colors.white60, Colors.white12],
                  tileMode: TileMode.mirror,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget getBottomCardWidget(BuildContext context) {
    return InkWell(
      onTap: () {
        if (widgetData.cardData!.action != null)
          clickActionWithAnalytics(widgetData.cardData!.action!, context,
              widgetData.widgetInfo, {"trigger": "bottomAction"});
        if (widgetData.cardData!.action != null && widgetData.cardData!.action!.completionAction !=
            null) {
          clickActionWithAnalytics(
              widgetData.cardData!.action!.completionAction!,
              context,
              null, {"trigger":"secondaryAction"});
        }
      },
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          if (widgetData.cardData!.imageUrl != null)
            Container(
              padding: const EdgeInsets.only(right: Spacings.x3),
              child: CFNetworkImage(
                imageUrl: getImageUrl(context,
                    imagePath: widgetData.cardData!.imageUrl),
                width: scale(context, 65),
                height: scale(context, 65),
              ),
            ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                if (widgetData.cardData!.title != null)
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(
                        widgetData.cardData!.title ?? "",
                        style: AuroraTheme.of(context)
                            .textStyle(TypescaleValues.P3),
                      ),
                      if (widgetData.cardData!.action != null)
                        Icon(
                          Icons.chevron_right,
                          color: Colors.white,
                          size: 25,
                        ),
                    ],
                  ),
                if (widgetData.cardData!.suffix != null ||
                    widgetData.cardData!.prefix != null)
                  Row(
                    children: [
                      Text(
                        widgetData.cardData!.suffix ?? "",
                        style: AuroraTheme.of(context).textStyle(
                            TypescaleValues.TAGTEXT,
                            color: Colors.white60),
                      ),
                      SizedBox(
                        width: Spacings.x1,
                      ),
                      Text(
                        widgetData.cardData!.prefix ?? "",
                        style: AuroraTheme.of(context).textStyle(
                            TypescaleValues.TAGTEXT,
                            color: HexColor.fromHex(
                                widgetData.cardData!.color ?? "#FFFFFF")),
                      ),
                    ],
                  ),
                if (widgetData.cardData!.description != null)
                  Padding(
                    padding: const EdgeInsets.only(top: Spacings.x1),
                    child: Text(
                      widgetData.cardData!.description ?? "",
                      style: AuroraTheme.of(context)
                          .textStyle(TypescaleValues.P5, color: Colors.white60),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

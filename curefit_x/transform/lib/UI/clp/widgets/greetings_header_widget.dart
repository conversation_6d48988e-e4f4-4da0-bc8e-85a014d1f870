import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:flutter/material.dart';
import 'package:transform/blocs/clp/models.dart';
import 'package:lottie/lottie.dart';

class GreetingsHeaderWidget extends StatelessWidget {
  final GreetingsHeaderWidgetData widgetData;

  const GreetingsHeaderWidget({required this.widgetData, Key? key})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: Spacings.x4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        // crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (widgetData.title != null)
                  Text(
                    widgetData.title ?? "",
                    style: AuroraTheme.of(context).textStyle(TypescaleValues.H2),
                  ),
                if (widgetData.subtitle != null)
                  Text(
                    widgetData.subtitle ?? "",
                    style: AuroraTheme.of(context)
                        .textStyle(TypescaleValues.P7, color: Colors.white70),
                  ),
                if (widgetData.prefix != null || widgetData.suffix != null)
                  RichText(
                    text: TextSpan(children: [
                      TextSpan(
                        text: widgetData.prefix ?? "",
                        style: AuroraTheme.of(context).textStyle(
                          TypescaleValues.P7,
                          color: Colors.white60,
                        ),
                      ),
                      const TextSpan(text: " "),
                      TextSpan(
                        text: widgetData.suffix ?? "",
                        style: AuroraTheme.of(context).textStyle(
                          TypescaleValues.P6,
                          color: Colors.white60,
                          decoration: TextDecoration.underline,
                        ),
                      )
                    ]),
                  ),
              ],
            ),
          ),
          if (widgetData.imageUrl != null)
            CFNetworkImage(
              imageUrl: getImageUrl(context, imagePath:widgetData.imageUrl),
              width: scale(context, 65),
              height: scale(context, 45),
              fit: BoxFit.cover,
            ),
          if (widgetData.lottieUrl != null)
            Lottie.network(
              getMediaUrl(widgetData.lottieUrl!),
              width: scale(context, 65),
              height: scale(context, 45),
            ),
        ],
      ),
    );
  }
}

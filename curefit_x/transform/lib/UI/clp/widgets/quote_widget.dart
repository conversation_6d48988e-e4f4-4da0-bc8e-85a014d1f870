import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/constants/action_constants.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:common/util/color.dart';
import 'package:flutter/material.dart';
import 'package:common/util/theme.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/clp/models.dart';

class QuoteWidgetView extends StatelessWidget {
  final QuoteWidgetData quoteWidgetData;

  const QuoteWidgetView(this.quoteWidgetData);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(bottom: Spacings.x8, top: Spacings.x10, left: Spacings.x4, right: Spacings.x4),
      alignment: Alignment.center,
      child: Column(
        children: [
          Text(quoteWidgetData.title ?? "",
              textAlign: TextAlign.center,
              style: AuroraTheme.of(context).textStyle(TypescaleValues.H7)),
          if (quoteWidgetData.subtitle != null)
            Padding(
                padding: EdgeInsets.only(top: 20),
                child: Text(quoteWidgetData.subtitle!,
                    textAlign: TextAlign.center,
                    style: AuroraTheme.of(context).textStyle(TypescaleValues.H3))),
          quoteWidgetData.action?.type != ActionTypes.EMPTY_ACTION
              ? quoteWidgetData.buttonTheme == CTAButtonTheme.GRADIENT
                  ? Padding(
                      padding: EdgeInsets.only(top: 60.0),
                      child: PrimaryButton(
                        () {
                          performAction(context);
                        },
                        quoteWidgetData.action?.title ?? "",
                        horizontalPadding: 0,
                      ))
                  : GestureDetector(
                      onTap: () {
                        performAction(context);
                      },
                      child: Padding(
                        padding: EdgeInsets.only(top: 60.0),
                        child: BlurView(
                            borderRadius: 5.0,
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                  vertical: 14, horizontal: 26),
                              child: Text(quoteWidgetData.action!.title ?? "",
                                  style: AuroraTheme.of(context).textStyle(TypescaleValues.P3)),
                            )),
                      ))
              : Container()
        ],
      ),
    );
  }

  performAction(BuildContext context) {
    WidgetInfo? widgetInfo = quoteWidgetData.widgetInfo;
    RepositoryProvider.of<AnalyticsRepository>(context)
        .logWidgetClick(widgetInfo: widgetInfo);
    ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
    PerformActionEvent event = PerformActionEvent(quoteWidgetData.action!);
    actionBloc.add(event);
  }
}

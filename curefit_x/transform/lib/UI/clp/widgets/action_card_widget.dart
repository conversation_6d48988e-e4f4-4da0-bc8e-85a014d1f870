import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/UI/clp/widgets/steps_widget.dart';
import 'package:transform/blocs/clp/models.dart';
import 'package:common/util/theme.dart';

class ActionCardWidgetView extends StatelessWidget {
  final ActionCardWidgetData actionCard;

  const ActionCardWidgetView(this.actionCard);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: Spacings.x4),
      child: Column(
        children: [
          ...this.actionCard.cards.map((card) =>
              Container(
                padding: EdgeInsets.only(bottom: Spacings.x3),
                child: StepCardView(card),
          ))
        ],
      ),
    );
  }
}

import 'package:common/ui/atoms/horizontal_progress_indicator.dart';
import 'package:common/ui/atoms/number_hook_single_digit.dart';
import 'package:common/ui/atoms/secondary_button.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/theme/Colors.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/util/action_util.dart';
import 'package:flutter/material.dart';
import 'package:transform/blocs/clp/models.dart';

class ContentProgressWidget extends StatelessWidget {
  final ContentProgressModel data;

  const ContentProgressWidget({
    Key? key,
    required this.data,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      child: progressCard(context),
    );
  }

  Widget progressCard(BuildContext context) {
    AuroraThemeData themeData = AuroraTheme.of(context);
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: Spacings.x4),
      child: InkWell(
        onTap: () {
          if (this.data.viewAllAction != null) {
            clickActionWithAnalytics(
                this.data.viewAllAction!, context, this.data.widgetInfo, {});
          }
        },
        child: ClipRRect(
          borderRadius: BorderRadius.circular(themeData.cornerRadius),
          child: BlurView(
            borderRadius: themeData.cornerRadius,
            child: Container(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  Container(
                    padding: const EdgeInsets.all(20),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              if (data.currentProgressCount != null)
                                NumberHookSingleDigit(
                                    text: data.currentProgressCount.toString(),
                                    numberHookColor: NumberHookColors.NONE),
                              SizedBox(
                                height: Spacings.x2,
                              ),
                              if (data.currentProgressCount != null)
                                Text(
                                  data.currentProgressCount! < 2
                                      ? "Concept learnt"
                                      : "Concepts learnt",
                                  style: AuroraTheme.of(context)
                                      .textStyle(TypescaleValues.P10),
                                )
                            ]),
                        SizedBox(
                          width: Spacings.x4,
                        ),
                        Expanded(
                          child: Column(
                            children: [
                              Text(
                                data.progressStatusText ??
                                    "Your learning progress is Excellent!",
                                style: AuroraTheme.of(context)
                                    .textStyle(TypescaleValues.H4),
                              ),
                              SizedBox(height: Spacings.x4),
                              if (data.currentProgressCount != null &&
                                  data.totalChaptersCount != null)
                                HorizontalProgressIndicator(
                                  value: data.currentProgressCount! /
                                      data.totalChaptersCount!,
                                  outerColor: ColorPalette.white20,
                                  innerColor: ColorPalette.white,
                                )
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                  SecondaryButton(
                    () {
                      if (this.data.viewAllAction != null) {
                        clickActionWithAnalytics(this.data.viewAllAction!,
                            context, this.data.widgetInfo, {});
                      }
                    },
                    this.data.viewAllAction?.title ?? "VIEW ALL MODULES",
                    expanded: true,
                    buttonType: SecondaryButtonType.BIG,
                    borderRadius: 0,
                    height: 50,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/atoms/secondary_button.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/util/action_util.dart';
import 'package:flutter/material.dart';
import 'package:transform/UI/clp/component/dashed_rect.dart';
import 'package:transform/blocs/clp/models.dart';

class TransformImageUploadWidget extends StatelessWidget {
  final TransformImageUploadWidgetData widgetData;

  const TransformImageUploadWidget({required this.widgetData});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: Spacings.x4),
      child: Column(
        children: [
          if (widgetData.showUploadWidget)
            InkWell(
              onTap: () {
                if (widgetData.action != null) {
                  clickActionWithAnalytics(
                      widgetData.action!, context, widgetData.widgetInfo, {});
                }
              },
              child: Padding(
                padding: const EdgeInsets.only(bottom: Spacings.x3),
                child: BlurView(
                  borderRadius: 10,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(
                            top: Spacings.x3,
                            bottom: Spacings.x1,
                            left: Spacings.x5,
                            right: Spacings.x5),
                        child: Text(
                          "Add a recent picture of yourself",
                          style: AuroraTheme.of(context)
                              .textStyle(TypescaleValues.H4),
                          textAlign: TextAlign.left,
                        ),
                      ),
                      SizedBox(
                        height: Spacings.x2,
                      ),
                      Container(
                        margin: EdgeInsets.only(
                            left: Spacings.x5,
                            right: Spacings.x5,
                            bottom: Spacings.x4),
                        height: 135,
                        width: double.infinity,
                        child: DashedRect(
                          color: Colors.white54,
                          strokeWidth: 1.0,
                          gap: 10.0,
                          child: Center(
                            child: Icon(
                              Icons.camera_alt_outlined,
                              color: Colors.white54,
                              size: 40,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          BlurView(
            borderRadius: 10,
            child: InkWell(
              onTap: () {
                if (widgetData.action != null && !widgetData.showUploadWidget) {
                  clickActionWithAnalytics(
                      widgetData.action!, context, widgetData.widgetInfo, {});
                }
              },
              child: Container(
                padding: const EdgeInsets.all(Spacings.x3),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widgetData.title ?? "",
                      style:
                          AuroraTheme.of(context).textStyle(TypescaleValues.H4),
                    ),
                    SizedBox(
                      height: Spacings.x2,
                    ),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (widgetData.imageCards != null &&
                            widgetData.imageCards!.isNotEmpty)
                          Expanded(
                            child: getImageCard(
                                context, widgetData.imageCards!.first),
                          ),
                        SizedBox(
                          width: Spacings.x2,
                        ),
                        if (widgetData.imageCards != null &&
                            widgetData.imageCards!.isNotEmpty)
                          Expanded(
                            child: getImageCard(
                                context, widgetData.imageCards!.last),
                          ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
          if (widgetData.journeyAction != null)
            Padding(
              padding: EdgeInsets.only(top: Spacings.x3),
              child: SecondaryButton(
                () {
                  if (widgetData.journeyAction != null) {
                    clickActionWithAnalytics(widgetData.journeyAction!, context,
                        widgetData.widgetInfo, {});
                  }
                },
                widgetData.journeyAction?.title ?? "",
                verticalPadding: Spacings.x3,
              ),
            ),
        ],
      ),
    );
  }

  Widget getImageCard(BuildContext context, ImageCard imageCard) {
    return BlurView(
      borderRadius: 15,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(
          15,
        ),
        child: Container(
          height: scale(context, 140),
          foregroundDecoration: BoxDecoration(
            borderRadius: BorderRadius.all(
              Radius.circular(15),
            ),
            border: Border.all(color: Colors.white24),
          ),
          child: Stack(
            children: [
              Positioned.fill(
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(
                    15,
                  ),
                  child: CFNetworkImage(
                    imageUrl: getImageUrl(context,
                        imagePath: imageCard.imageUrl ?? ""),
                    height: scale(context, 140),
                    width: scale(context, 140),
                    fit: BoxFit.fill,
                  ),
                ),
              ),
              Positioned(
                top: 0,
                left: 0,
                child: BlurView(
                  borderRadius: 15,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                        horizontal: Spacings.x2, vertical: 2),
                    child: Text(
                      imageCard.tagText ?? "",
                      style: AuroraTheme.of(context)
                          .textStyle(TypescaleValues.P10),
                    ),
                  ),
                ),
              ),
              Positioned(
                bottom: 0,
                right: 0,
                left: 0,
                child: imageCard.subtitle != null ? getBottomText(context, imageCard) : BlurView(
                  blurType: imageCard.subtitle != null ? BlurType.MIN: BlurType.LOW,
                  borderRadius: 0,
                  child: getBottomText(context, imageCard)
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget getBottomText(BuildContext context, ImageCard imageCard){
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
          bottomRight: Radius.circular(5),
          bottomLeft: Radius.circular(5),
        ),
      ),
      child: Column(
        children: [
          Padding(
            padding:
            const EdgeInsets.symmetric(vertical: Spacings.x1),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (imageCard.showIcon)
                  Icon(
                    Icons.camera_alt_outlined,
                    color: Colors.white,
                    size: 12,
                  ),
                SizedBox(
                  width: Spacings.x1,
                ),
                Text(
                  imageCard.title ?? "",
                  style: AuroraTheme.of(context)
                      .textStyle(TypescaleValues.TAGTEXT),
                ),
              ],
            ),
          ),
          if (imageCard.subtitle != null)
            Padding(
              padding: const EdgeInsets.only(
                  bottom: Spacings.x1),
              child: Text(
                imageCard.subtitle ?? "",
                style: AuroraTheme.of(context).textStyle(
                    TypescaleValues.P10,
                    color: Colors.white60),
              ),
            ),
        ],
      ),
    );
  }
}

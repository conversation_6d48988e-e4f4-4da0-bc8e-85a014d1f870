import 'package:common/action/action_handler.dart';
import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/constants/action_constants.dart';
import 'package:common/ui/atoms/secondary_button.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'dart:math' as math;
import 'package:common/util/action_util.dart';
import 'package:flutter/material.dart' hide Action;
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/lift_clp/models.dart';

class MetricInputWidget extends StatefulWidget {
  final MetricInputWidgetData widgetData;

  const MetricInputWidget({required this.widgetData});

  @override
  State<MetricInputWidget> createState() => _MetricInputWidgetState();
}

class _MetricInputWidgetState extends State<MetricInputWidget> {
  final TextEditingController textController = new TextEditingController();
  String currentState = "";

  @override
  void initState() {
    this.currentState = widget.widgetData.initialState ?? "";
    if (widget.widgetData.value == 0) {
      textController.text = "00";
    } else {
      textController.text = widget.widgetData.value.toString();
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: widget.widgetData.usePadding ? Spacings.x4 : 0.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.widgetData.title != null)
            Padding(
              padding: const EdgeInsets.only(bottom: Spacings.x3),
              child: Text(
                widget.widgetData.title ?? "",
                style: AuroraTheme.of(context)
                    .textStyle(TypescaleValues.P6, color: Colors.white60),
              ),
            ),
          BlurView(
            child: Container(
              padding: EdgeInsets.only(
                  top: Spacings.x2,
                  bottom: Spacings.x4,
                  left: Spacings.x4,
                  right: Spacings.x4),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  if (currentState == "EDIT")
                    Container(
                      width: widget.widgetData.editAction != null ? 120 : 100,
                      height: 50,
                      child: Align(
                        alignment: Alignment.bottomCenter,
                        child: Text(
                          textController.text,
                          style: AuroraTheme.of(context)
                              .textStyle(TypescaleValues.H7),
                          textAlign: TextAlign.center,
                          maxLines: 1,
                          overflow: TextOverflow.clip,
                        ),
                      ),
                    )
                  else if (currentState == "UPDATE")
                    Container(
                      width: 120,
                      height: 50,
                      child: Center(
                        child: ConstrainedBox(
                          constraints:
                              BoxConstraints(minWidth: 100, maxWidth: 120),
                          child: IntrinsicWidth(
                              child: TextField(
                            textAlign: TextAlign.center,
                            textInputAction: TextInputAction.next,
                            decoration: InputDecoration(
                              isDense: true,
                              enabledBorder: UnderlineInputBorder(
                                borderSide: BorderSide(color: Colors.white),
                              ),
                              focusedBorder: UnderlineInputBorder(
                                borderSide: BorderSide(color: Colors.white),
                              ),
                              border: UnderlineInputBorder(
                                borderSide: BorderSide(color: Colors.white),
                              ),
                            ),
                            style: AuroraTheme.of(context)
                                .textStyle(TypescaleValues.H7),
                            controller: textController,
                            inputFormatters: [
                              DecimalTextInputFormatter(
                                  decimalRange:
                                      widget.widgetData.precision ?? 2)
                            ],
                          )),
                        ),
                      ),
                    )
                  else
                    Container(),
                  Padding(
                    padding: EdgeInsets.only(
                        left: widget.widgetData.editAction != null ? Spacings.x2 : 0.0, bottom: Spacings.x2),
                    child: Text(
                      widget.widgetData.unit ?? "",
                      style: AuroraTheme.of(context)
                          .textStyle(TypescaleValues.P1, color: Colors.white60),
                    ),
                  ),
                  Spacer(),
                  if (currentState == "EDIT" &&
                      widget.widgetData.editAction != null)
                    Container(
                      width: 100,
                      child: SecondaryButton(
                        () {
                          clickActionWithAnalytics(
                              widget.widgetData.editAction!,
                              context,
                              widget.widgetData.widgetInfo,
                              {"state": currentState});
                          setState(() {
                            currentState = "UPDATE";
                          });
                        },
                        widget.widgetData.editAction?.title ?? "",
                        buttonType: SecondaryButtonType.SMALL,
                      ),
                    )
                  else if (currentState == "UPDATE" &&
                      widget.widgetData.updateAction != null)
                    Container(
                      width: 100,
                      child: SecondaryButton(
                        () {
                          List<dynamic> updates = [];
                          try {
                            double val = double.parse(textController.text);
                            if (val <= 0) return;
                            updates.add({
                              "metricId": widget.widgetData.metricId,
                              "value": val,
                              "subCategoryCode":
                                  widget.widgetData.subCategoryCode ?? "",
                            });
                          } catch (e) {
                            print(e);
                          }

                          if (updates.isNotEmpty) {
                            ActionBloc actionBloc =
                                BlocProvider.of<ActionBloc>(context);
                            PerformActionEvent event = PerformActionEvent(
                                new Action(
                                    type: ActionTypes.UPDATE_METRIC_INPUT,
                                    meta: {"updates": updates}));
                            RepositoryProvider.of<AnalyticsRepository>(context)
                                .logWidgetClick(extraInfo: {
                              "action": "Metrics update",
                              "metricType": updates,
                              "state": currentState,
                              "subCategoryCode":
                                  widget.widgetData.subCategoryCode,
                            }, widgetInfo: widget.widgetData.widgetInfo);
                            actionBloc.add(event);
                          }
                          setState(() {
                            currentState = "EDIT";
                          });
                        },
                        widget.widgetData.updateAction?.title ?? "",
                        buttonType: SecondaryButtonType.SMALL,
                      ),
                    )
                  else
                    Container(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class DecimalTextInputFormatter extends TextInputFormatter {
  DecimalTextInputFormatter({this.decimalRange})
      : assert(decimalRange == null || decimalRange > 0);

  final int? decimalRange;

  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue, // unused.
    TextEditingValue newValue,
  ) {
    TextSelection newSelection = newValue.selection;
    String truncated = newValue.text;
    try {
      if (truncated == "") {
        newSelection = newValue.selection.copyWith(
          baseOffset: math.min(truncated.length, truncated.length + 1),
          extentOffset: math.min(truncated.length, truncated.length + 1),
        );
        return TextEditingValue(
          text: truncated,
          selection: newSelection,
          composing: TextRange.empty,
        );
      }
      double val = double.parse(truncated);
      if (val < 0) return oldValue;
    } catch (e) {
      return oldValue;
    }

    if (decimalRange != null) {
      String value = newValue.text;

      if (value.contains(".") &&
          value.substring(value.indexOf(".") + 1).length > decimalRange!) {
        truncated = oldValue.text;
        newSelection = oldValue.selection;
      } else if (value == ".") {
        truncated = "0.";

        newSelection = newValue.selection.copyWith(
          baseOffset: math.min(truncated.length, truncated.length + 1),
          extentOffset: math.min(truncated.length, truncated.length + 1),
        );
      }

      return TextEditingValue(
        text: truncated,
        selection: newSelection,
        composing: TextRange.empty,
      );
    }
    return newValue;
  }
}

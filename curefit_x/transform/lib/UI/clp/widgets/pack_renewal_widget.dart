import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/constants/colors.dart';
import 'package:common/ui/atoms/secondary_button.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:common/util/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/clp/models.dart';

class PackRenewalWidgetView extends StatelessWidget {
  final PackRenewalWidgetData packRenewalWidgetData;

  const PackRenewalWidgetView({Key? key, required this.packRenewalWidgetData})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: Spacings.x4),
      child: Column(
        children: [
          Text(packRenewalWidgetData.title ?? "",
              textAlign: TextAlign.center,
              style: AuroraTheme.of(context).textStyle(TypescaleValues.H9,color: lightGrayishGreen)),
          SizedBox(
            height: 30,
          ),
          Text(
            packRenewalWidgetData.subtitle ?? "",
            textAlign: TextAlign.center,
            style: AuroraTheme.of(context).textStyle(TypescaleValues.H3),
          ),
          SizedBox(
            height: 30,
          ),
          SecondaryButton(() {
            performAction(context);
          }, packRenewalWidgetData.action?.title ?? "", expanded: false)
        ],
      ),
    );
  }

  performAction(BuildContext context) {
    WidgetInfo? widgetInfo = packRenewalWidgetData.widgetInfo;
    RepositoryProvider.of<AnalyticsRepository>(context)
        .logWidgetClick(widgetInfo: widgetInfo);
    ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
    PerformActionEvent event =
        PerformActionEvent(packRenewalWidgetData.action!);
    actionBloc.add(event);
  }
}

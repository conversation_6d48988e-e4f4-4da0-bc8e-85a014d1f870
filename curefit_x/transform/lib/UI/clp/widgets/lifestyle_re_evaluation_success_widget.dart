import 'package:common/analytics/analytics_repository.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/atoms/secondary_button.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/lifestyle_re_evaluation/events.dart';
import 'package:transform/blocs/lifestyle_re_evaluation/lifestyle_re_evaluation_bloc.dart';
import 'package:transform/blocs/lifestyle_re_evaluation/state.dart';
import 'package:common/ui/widget_builder.dart';

class LifestyleReEvaluationSuccessView extends StatefulWidget {
  @override
  _LifestyleReEvaluationSuccessView createState() => _LifestyleReEvaluationSuccessView();

}

class _LifestyleReEvaluationSuccessView extends State<LifestyleReEvaluationSuccessView> {
  @override
  void initState() {
    super.initState();
    final reEvaluationBloc = BlocProvider.of<LifestyleReEvaluationBloc>(context);
    reEvaluationBloc.add(LifestyleReEvaluationSuccessLoadEvent());
  }
  @override
  Widget build(BuildContext context) {
    return Container(
      child: BlocBuilder<LifestyleReEvaluationBloc, LifestyleReEvaluationState>(
        builder: (context, state) {
          if (state is LifestyleReEvaluationSuccessLoadingState) {
            return FancyLoadingIndicator();
          }
          else if (state is LifestyleReEvaluationSuccessState) {
            return Column(
              children: [
                Container(
                  height: 220,
                  // decoration: BoxDecoration(
                  //   shape: BoxShape.circle,
                  //   color: Colors.white
                  // ),
                  margin: EdgeInsets.only(top: Spacings.x4),
                  child: Lottie.network(
                    getMediaUrl(state.data.heroImage)
                  ),
                ),

                Expanded(
                    child: Container(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Padding(
                            padding: EdgeInsets.symmetric(vertical: Spacings.x2),
                            child: Text(state.data.title, style: AuroraTheme.of(context).textStyle(TypescaleValues.H1)),

                          ),
                          Container(
                            // padding: EdgeInsets.symmetric(vertical: Spacings.x2),
                            child: Text(state.data.subTitle, style: AuroraTheme.of(context).textStyle(TypescaleValues.P5), textAlign: TextAlign.center),

                          )],
                      )
                    )
                ),
                SecondaryButton(() {
                  RepositoryProvider.of<AnalyticsRepository>(context)
                      .logWidgetClick(
                      widgetInfo: WidgetInfo(
                          widgetMetric: WidgetMetric(),
                          widgetType: WidgetTypes.LIFESTYLE_RE_EVALUATION_FORM_WIDGET)
                  );
                  ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
                  PerformActionEvent event = PerformActionEvent(state.data.action);
                  actionBloc.add(event);
                }, state.data.action.title!,
                  expanded: false,
                )
              ],
            );
          }
          return Container();

      })
    );
  }
}

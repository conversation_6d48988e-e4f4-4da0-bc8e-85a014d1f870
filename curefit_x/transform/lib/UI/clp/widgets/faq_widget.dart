import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/constants/action_constants.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/clp/models.dart';
import 'package:common/action/action_handler.dart' as action_handler;

class FAQWidgetView extends StatelessWidget {
  final FAQWidgetData faqWidgetData;

  const FAQWidgetView(this.faqWidgetData);

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white.withOpacity(0.1),
      padding: EdgeInsets.symmetric(vertical: 22, horizontal: 20),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          SizedBox(
              width: 200,
              child: Text(faqWidgetData.title,
                  textAlign: TextAlign.left,
                  style:
                      AuroraTheme.of(context).textStyle(TypescaleValues.H2))),
          GestureDetector(
            onTap: () {
              ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
              PerformActionEvent event = PerformActionEvent(action_handler.Action(
                  type: ActionTypes.NAVIGATION,
                  url:
                      "curefit://webview?uri=${faqWidgetData.action.url ?? "https://cult.fit"}"));
              actionBloc.add(event);
            },
            child: CircularArrowIcon(),
          )
        ],
      ),
    );
  }
}

import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/header/card_header.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/util/action_util.dart';
import 'package:common/util/color.dart';
import 'package:flutter/material.dart';
import 'package:transform/blocs/clp/models.dart';

class ProgressCardsWidget extends StatelessWidget {
  final ProgressCardsWidgetData widgetData;

  const ProgressCardsWidget({required this.widgetData, Key? key})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (widgetData.header != null)
          Padding(
            padding: EdgeInsets.only(
                bottom: Spacings.x6, left: Spacings.x4, right: Spacings.x4),
            child: WidgetHeader(
              cardHeaderData: widgetData.header!,
            ),
          ),
        if (widgetData.progressCards != null)
          Container(
            child: Wrap(
              alignment: WrapAlignment.spaceBetween,
              runAlignment: WrapAlignment.spaceBetween,
              spacing: 10,
              runSpacing: 10,
              children: [
                ...widgetData.progressCards!
                    .map((cardData) => getProgressCard(context, cardData))
                    .toList()
              ],
            ),
          ),
      ],
    );
  }

  Widget getProgressCard(BuildContext context, ProgressV2Card cardData) {
    return InkWell(
      onTap: () {
        if (cardData.action != null) {
          clickActionWithAnalytics(cardData.action!, context,
              widgetData.widgetInfo, {'cardClicked': cardData.id ?? ""});
        }
      },
      child: Container(
        width: scale(context, 160),
        height: scale(context, 150),
        padding: EdgeInsets.symmetric(
            horizontal: Spacings.x3, vertical: Spacings.x2),
        decoration: cardData.imageUrl != null
            ? BoxDecoration(
          image: DecorationImage(
            image: NetworkImage(
                getImageUrl(context, imagePath: cardData.imageUrl)),
            fit: BoxFit.fill,
          ),
        )
            : null,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: 12,
                  height: 12,
                  child: CFNetworkImage(
                      imageUrl:
                      getImageUrl(context, imagePath: cardData.iconUrl)),
                ),
                SizedBox(
                  width: Spacings.x1,
                ),
                Flexible(
                  child: Text(
                    cardData.heading ?? "",
                    style: AuroraTheme.of(context).textStyle(
                        TypescaleValues.TAGTEXT,
                        color: Colors.white38),
                  ),
                ),
              ],
            ),
            Padding(
              padding: const EdgeInsets.only(top: Spacings.x2),
              child: RichText(
                textAlign: TextAlign.start,
                text: TextSpan(children: [
                  if (cardData.metricText != null)
                    TextSpan(
                      text: cardData.metricText ?? "",
                      style: AuroraTheme.of(context)
                          .textStyle(TypescaleValues.H10),
                    ),
                  TextSpan(
                    text: " ",
                    style: AuroraTheme.of(context)
                        .textStyle(TypescaleValues.H10),
                  ),
                  if (cardData.differenceText != null)
                    TextSpan(
                        text: cardData.differenceText ?? "",
                        style: AuroraTheme.of(context).textStyle(
                            TypescaleValues.P6,
                            color: HexColor.fromHex(
                                cardData.color ?? "#FFFFFF"))),
                ]),
              ),
            ),
            if (cardData.subtitle != null)
              Text(
                cardData.subtitle ?? "",
                style: AuroraTheme.of(context)
                    .textStyle(TypescaleValues.P3, color: Colors.white),
              ),
            Spacer(),
            if (cardData.description != null)
              Text(
                cardData.description ?? "",
                style: AuroraTheme.of(context)
                    .textStyle(TypescaleValues.P8, color: Colors.white),
              ),
          ],
        ),
      ),
    );
  }
}

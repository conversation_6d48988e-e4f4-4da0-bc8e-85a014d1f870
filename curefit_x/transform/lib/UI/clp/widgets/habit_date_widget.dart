import 'package:common/analytics/analytics_repository.dart';
import 'package:common/ui/atoms/date_label.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/clp/models.dart';
import 'package:transform/blocs/habit/events.dart';
import 'package:transform/blocs/habit/habit_bloc.dart';
import 'package:intl/intl.dart';
import 'package:common/ui/flutter_scale_tap.dart';

class HabitDateWidgetView extends StatefulWidget {
  final HabitDateWidgetData habitDateWidgetData;

  const HabitDateWidgetView(this.habitDateWidgetData, {Key? key})
      : super(key: key);

  @override
  _HabitDateWidgetViewState createState() => _HabitDateWidgetViewState();
}

class _HabitDateWidgetViewState extends State<HabitDateWidgetView>
    with TickerProviderStateMixin {
  late String _selectedDate;

  @override
  void initState() {
    super.initState();
    if (widget.habitDateWidgetData.habitDates != null &&
        widget.habitDateWidgetData.habitDates!.length > 0) {
      HabitBloc habitBloc = BlocProvider.of<HabitBloc>(context);
      if (habitBloc.selectedDate != null && !habitBloc.selectedDate!.isEmpty) {
        _selectedDate = habitBloc.selectedDate!;
      } else if (isToday(widget.habitDateWidgetData.habitDates![0])) {
        _selectedDate = widget.habitDateWidgetData.habitDates![0];
      } else {
        _selectedDate = "";
      }
      habitBloc.add(HabitsDateEvent(selectedDate: _selectedDate));
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.habitDateWidgetData.habitDates == null ||
        widget.habitDateWidgetData.habitDates!.length == 0) {
      return Container();
    }
    _selectedDate =
        BlocProvider.of<HabitBloc>(context).selectedDate ?? _selectedDate;
    return Container(
      padding: EdgeInsets.only(
          left: Spacings.x4,
          right: Spacings.x4,
          top: (widget.habitDateWidgetData.widgetInfo?.tenant != null &&
                  widget.habitDateWidgetData.widgetInfo?.tenant == "CULT")
              ? Spacings.x12
              : 0),
      child: Column(
        children: [
          SizedBox(
            height: 20,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [Expanded(child: getRichText())],
          ),
          SizedBox(
            height: 20,
          ),
          AnimatedSize(
            duration: Duration(milliseconds: 250),
            child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: widget.habitDateWidgetData.habitDates != null
                    ? createDates(widget.habitDateWidgetData.habitDates!)
                    : []),
          ),
        ],
      ),
    );
  }

  String getText() {
    return 'Your';
  }

  Widget getRichText() {
    return Center(
      child: Text(
        'Your actions',
        style: AuroraTheme.of(context).textStyle(TypescaleValues.H2),
      ),
    );
    return RichText(
      textAlign: TextAlign.center,
      text: new TextSpan(
        style: AuroraTheme.of(context).textStyle(TypescaleValues.H1),
        children: <TextSpan>[
          new TextSpan(
              text: getText(),
              style: new TextStyle(
                  fontWeight: FontWeight.w900,
                  decoration: TextDecoration.underline)),
          new TextSpan(
              text: ' Actions',
              style: new TextStyle(
                  fontWeight: FontWeight.normal,
                  decoration: TextDecoration.none)),
        ],
      ),
    );
  }

  List<Widget> createDates(List<String> habitDates) {
    habitDates.sort((a, b) => a.compareTo(b));
    return habitDates.map((value) {
      return getHabitDateWidget(
        value,
      );
    }).toList();
  }

  bool isToday(String assignDate) {
    if (assignDate.isEmpty) {
      return false;
    }
    var dateFormat = new DateFormat("yMd");

    DateTime date =
        new DateTime.fromMillisecondsSinceEpoch(int.parse(assignDate));
    var dateString = dateFormat.format(date);
    var todayDate = dateFormat.format(DateTime.now());

    if (todayDate == dateString) {
      return true;
    }
    return false;
  }

  bool isSameDate(DateTime first, DateTime second) {
    var dateFormat = new DateFormat("yMd");
    var firstDate = dateFormat.format(first);
    var secondDate = dateFormat.format(second);
    if (firstDate == secondDate) {
      return true;
    }
    return false;
  }

  Widget getHabitDateWidget(String dateEpoch) {
    final DateTime firstDateTime =
        DateTime.fromMillisecondsSinceEpoch(int.parse(dateEpoch));
    bool isSelected = false;
    if (_selectedDate.isNotEmpty) {
      final DateTime secondDateTime =
          DateTime.fromMillisecondsSinceEpoch(int.parse(_selectedDate));
      isSelected = isSameDate(firstDateTime, secondDateTime);
    }
    return DateLabel(
      date: firstDateTime.toString(),
      title: isToday(dateEpoch) ? "TODAY" : null,
      stateChangeEnabled: false,
      initialState: isSelected
          ? DateLabelStatus.SelectedHighAttention
          : DateLabelStatus.Default,
      onTap: () {
        if (_selectedDate != dateEpoch) {
          HabitBloc habitBloc = BlocProvider.of<HabitBloc>(context);
          habitBloc.add(HabitsDateEvent(selectedDate: dateEpoch));
          //TODO: add default tenant in cf-api java
          habitBloc.add(HabitsLoadEvent(
              epoch: dateEpoch,
              tenant: widget.habitDateWidgetData.widgetInfo?.tenant ??
                  "TRANSFORM"));

          RepositoryProvider.of<AnalyticsRepository>(context)
              .logWidgetClick(extraInfo: {
            "action": "date_change",
            "value": firstDateTime.millisecondsSinceEpoch.toString()
          }, widgetInfo: widget.habitDateWidgetData.widgetInfo);
          setState(() {
            _selectedDate = dateEpoch;
          });
        }
      },
    );
  }
}

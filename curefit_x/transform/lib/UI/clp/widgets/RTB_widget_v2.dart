import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/ui/ui_toolkit/ui_toolkit.dart';
import 'package:common/ui/widgets/RTB_widget.dart';
import 'package:common/util/action_util.dart';
import 'package:flutter/material.dart';
import 'package:common/ui/organisms/rtb_card.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:lottie/lottie.dart';

class ReasonsToBuyWidgetV2 extends StatelessWidget {
  final ReasonsToBuyWidgetData reasonsToBuyWidgetData;

  const ReasonsToBuyWidgetV2({required this.reasonsToBuyWidgetData});

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        if (reasonsToBuyWidgetData.headerImageUrl != null)
          CFNetworkImage(
            imageUrl: getImageUrl(context,
                imagePath: reasonsToBuyWidgetData.headerImageUrl!),
            width: scale(context, 375),
            height: scale(context, 100),
          ),
        ...reasonsToBuyWidgetData.items
            .map((data) => ReasonToBuyCard(offering: data))
            .toList(),
        if (reasonsToBuyWidgetData.title != null)
          Container(
            padding: EdgeInsets.symmetric(
                horizontal: Spacings.x8, vertical: Spacings.x2),
            child: Text(
              reasonsToBuyWidgetData.title ?? "",
              style: AuroraTheme.of(context).textStyle(
                TypescaleValues.P2,
                color: Color.fromRGBO(255, 240, 210, 1),
              ),
              textAlign: TextAlign.center,
            ),
          ),
        if (reasonsToBuyWidgetData.footerAction != null &&
            reasonsToBuyWidgetData.footerAction?.url != null)
          Container(
            padding: EdgeInsets.symmetric(
                horizontal: Spacings.x4, vertical: Spacings.x2),
            child: PrimaryButton(() {
              ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
              PerformActionEvent event =
                  PerformActionEvent(reasonsToBuyWidgetData.footerAction!);
              actionBloc.add(event);
              clickActionWithAnalytics(
                  reasonsToBuyWidgetData.footerAction!, context, reasonsToBuyWidgetData.widgetInfo, {});
            }, reasonsToBuyWidgetData.footerAction?.title ?? "EXPLORE PLANS"),
          ),
        if (reasonsToBuyWidgetData.footerText != null)
          Container(
            padding: EdgeInsets.symmetric(horizontal: Spacings.x8),
            child: Text(
              reasonsToBuyWidgetData.footerText ?? "",
              style: AuroraTheme.of(context).textStyle(TypescaleValues.P8),
              textAlign: TextAlign.center,
            ),
          ),
      ],
    );
  }
}

class ReasonToBuyCard extends StatelessWidget {
  final Offering offering;

  ReasonToBuyCard({required this.offering});

  Widget background(BuildContext context, {double? width, double? height}) {
    if (offering.lottieUrl != null) {
      return wrapInShaderMask(
        context,
        ClipRect(
          child: Lottie.network(
            getMediaUrl(offering.lottieUrl!),
            width: width ?? scale(context, 375),
            height: height ?? scale(context, 550),
            fit: BoxFit.cover,
            frameRate: FrameRate(60),
          ),
        ),
        top: true,
        bottom: true,
        disableGlassMorphism: offering.disableGlassMorphism,
      );
    } else if (offering.image != null) {
      return wrapInShaderMask(
        context,
        CFNetworkImage(
          imageUrl: getImageUrl(context, imagePath: offering.image),
          width: width ?? scale(context, 375),
          height: height ?? scale(context, 550),
          fit: BoxFit.cover,
        ),
        top: true,
        bottom: true,
        disableGlassMorphism: offering.disableGlassMorphism,
      );
    }
    return Container();
  }

  @override
  Widget build(BuildContext context) {
    final width = scale(context, 375);
    final height = scale(context, 550);
    return Container(
      height: height,
      width: width,
      child: Stack(
        children: [
          Positioned.fill(
            child: Visibility(
              child: background(
                context,
                width: width,
                height: height,
              ),
            ),
          ),
          Positioned(
            bottom: scale(context, Spacings.x9),
            left: 0,
            right: 0,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  padding: EdgeInsets.symmetric(
                      horizontal: 100, vertical: Spacings.x2),
                  child: Text(
                    offering.title ?? "",
                    style:
                        AuroraTheme.of(context).textStyle(TypescaleValues.H6),
                    textAlign: TextAlign.center,
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(
                      horizontal: Spacings.x8, vertical: Spacings.x2),
                  child: Text(
                    offering.text ?? "",
                    style: AuroraTheme.of(context)
                        .textStyle(TypescaleValues.P2, color: Colors.white60),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

import 'dart:math';

import 'package:common/font/cf_icons.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/atoms/context_tags/regular_tag.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/organisms/tinder_swap_card.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/util/action_util.dart';
import 'package:common/util/color.dart';
import 'package:flutter/material.dart';
import 'package:transform/blocs/clp/models.dart';

class ContentForTodaysReadWidget extends StatefulWidget {
  final ContentTodayReadModel data;

  const ContentForTodaysReadWidget({Key? key, required this.data})
      : super(key: key);

  @override
  State<ContentForTodaysReadWidget> createState() =>
      _ContentForTodaysReadWidgetState();
}

class _ContentForTodaysReadWidgetState
    extends State<ContentForTodaysReadWidget> {
  final cardController = new CardController();
  final textColor = HexColor.fromHex("#1D2025");
  final random = new Random();
  late ContentTodayReadModel data;
  _ContentForTodaysReadWidgetState();

  @override
  void initState() {
    super.initState();
    this.data = widget.data;
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      child: stackCards(context),
      height: scale(context, 435),
    );
  }

  Widget contentCard(BuildContext context, int index) {
    LevelCardChapter cardItem = this.data.cards[index];
    return InkWell(
      onTap: () {
        if (cardItem.action != null) {
          clickActionWithAnalytics(
              cardItem.action!, context, this.data.widgetInfo, {});
        }
      },
      child: Container(
        decoration: BoxDecoration(
            borderRadius:
                BorderRadius.circular(AuroraTheme.of(context).cornerRadius)),
        child: Stack(
          children: [
            CFNetworkImage(
                fit: BoxFit.cover,
                width: scale(context, 335),
                height: scale(context, 435),
                imageUrl: getImageUrl(context,
                    imagePath: cardItem.imageBackground, width: 335)),
            Positioned.fill(
                child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              mainAxisSize: MainAxisSize.max,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                RegularTag(cardItem.tagName ?? "TODAY'S READ"),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 25),
                  child: Column(
                    children: [
                      Text(
                        cardItem.title!,
                        style: AuroraTheme.of(context)
                            .textStyle(TypescaleValues.H10, color: textColor),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
                Column(
                  children: [
                    Text(cardItem.subtitle!,
                        style: AuroraTheme.of(context)
                            .textStyle(TypescaleValues.P8)),
                    SizedBox(height: 20),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text("READ NOW",
                            style: AuroraTheme.of(context)
                                .textStyle(TypescaleValues.P6)),
                        const Icon(
                          CFIcons.chevron_right,
                          size: 14,
                          color: Colors.white,
                        )
                      ],
                    ),
                  ],
                ),
              ],
            ))
          ],
        ),
      ),
    );
  }

  Widget stackCards(BuildContext context) {
    return TinderSwapCard(
      orientation: AmassOrientation.bottom,
      totalNum: this.data.cards.length,
      stackNum: 3,
      allowSwiping: data.isSwippable ?? false,
      maxWidth: scale(context, 355),
      maxHeight: scale(context, 435),
      minWidth: scale(context, 300),
      minHeight: scale(context, 400),
      cardController: cardController,
      cardBuilder: (context, index) => Card(
        child: contentCard(context, index),
        clipBehavior: Clip.hardEdge,
        color: Colors.black,
        shadowColor: Colors.black,
        shape: RoundedRectangleBorder(
            borderRadius:
                BorderRadius.circular(AuroraTheme.of(context).cornerRadius)),
        elevation: 5,
      ),
      swipeUpdateCallback: (DragUpdateDetails details, Alignment align) {
        /// Get swiping card's alignment
        if (align.x < 0) {
          //Card is LEFT swiping
        } else if (align.x > 0) {
          //Card is RIGHT swiping
        }
      },
      swipeCompleteCallback: (CardSwipeOrientation orientation, int index) {
        /// Get orientation & index of swiped card!
      },
    );
  }

  @override
  void didUpdateWidget(covariant ContentForTodaysReadWidget oldWidget) {
    super.didUpdateWidget(oldWidget);

    try {
      var firstItem = this.widget.data.cards.first;
      var oldIndex = oldWidget.data.cards
          .indexWhere((element) => element.title == firstItem.title);
      int numberOfItemsToSwap = oldIndex + 1;
      final randomNumberGenerator = Random();

      while (oldIndex > 0) {
        final randomBoolean = randomNumberGenerator.nextBool();
        (randomBoolean == true)
            ? this.cardController.triggerRight()
            : this.cardController.triggerLeft();
        oldIndex--;
      }
      Future.delayed(Duration(milliseconds: 3000), () {
        if (this.mounted && oldWidget.data != this.widget.data) {
          setState(() {
            this.data = this.widget.data;
          });
        }
      });
    } on Exception {
      if (this.mounted && oldWidget.data != this.widget.data) {
        setState(() {
          this.data = this.widget.data;
        });
      }
    }
  }
}

import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/header/card_header.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:flutter/material.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:transform/blocs/pack_purchase/models.dart' as PackPurchaseModels;

enum Orientation { TOP, LEFT, RIGHT, BOTTOM, VERTICAL }

class InstructionsWidgetData implements IWidgetData {
  InstructionsWidgetData(this.widgetType,
      {this.header, this.items, this.orientation, this.showHeader = false, this.blurEnabled = false});

  @override
  WidgetInfo? widgetInfo;

  factory InstructionsWidgetData.fromJson(widget, WidgetTypes widgetType) {
    if (widget['instructionMap'] != null &&
        widget['instructionMap'].isNotEmpty) {
      return InstructionsWidgetData(widgetType,
          orientation: widget['orientation'] != null
              ? EnumToString.fromString(
              Orientation.values, widget['orientation'])
              : null,
          blurEnabled: widget['blurEnabled'] ?? false,
          header: widget['instructionMap'][0]['title'] != null
              ? PackPurchaseModels.WidgetHeader(title: widget['instructionMap'][0]['title'])
              : PackPurchaseModels.WidgetHeader(
            title:
            widget['header'] != null ? widget['header']['title'] : "",
            subtitle: widget['header'] != null
                ? widget['header']['subtitle']
                : "",
          ),
          items: widget['instructionMap'][0]['instructionResponseList']
              .map<InstructionItem>((e) =>
              InstructionItem(text: e['text'], iconURL: e['iconURL']))
              .toList(),
          showHeader: widget['showHeader'] ?? false);
    } else {
      return InstructionsWidgetData(widgetType,
          header: null,
          blurEnabled: widget['blurEnabled'] ?? false,
          orientation: widget['orientation'] != null
              ? EnumToString.fromString(
              Orientation.values, widget['orientation'])
              : null,
          items: widget['instructions']
              .map<InstructionItem>((e) =>
              InstructionItem(text: e['text'], iconType: e['iconType']))
              .toList(),
          showHeader: widget['showHeader'] ?? false);
    }
  }

  @override
  WidgetTypes widgetType;

  PackPurchaseModels.WidgetHeader? header;
  Orientation? orientation;
  bool showHeader;
  bool blurEnabled;
  List<InstructionItem>? items;
}

class InstructionItem {
  InstructionItem({this.text, this.iconURL, this.iconType});

  String? text;
  String? iconURL;
  String? iconType;
}


class InstructionsWidgetView extends StatelessWidget {
  final InstructionsWidgetData instructionsWidgetDate;

  const InstructionsWidgetView(this.instructionsWidgetDate);

  Widget getVerticalOrientationLayout(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // StepWidgetV2(),
        for (var item in instructionsWidgetDate.items!)
          Padding(
            padding: EdgeInsets.only(top: Spacings.x3),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: EdgeInsets.only(top: Spacings.x1),
                  child: Icon(
                    Icons.check_circle_outline_rounded,
                    color: Colors.white,
                    size: 15,
                  ),
                ),
                SizedBox(width: Spacings.x2),
                Flexible(
                  child: Text(
                    item.text ?? "",
                    style: AuroraTheme.of(context)
                        .textStyle(TypescaleValues.P4, color: Colors.white60),
                  ),
                )
              ],
            ),
          ),
      ],
    );
  }

  Widget getTopOrientationLayout(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        // StepWidgetV2(),
        for (var item in instructionsWidgetDate.items!)
          Padding(
              padding: EdgeInsets.symmetric(vertical: 10),
              child: Column(
                children: [
                  item.iconURL != null
                      ? CFNetworkImage(
                          fit: BoxFit.cover,
                          height: scale(context, 80),
                          width: scale(context, 80),
                          imageUrl:
                              getImageUrl(context, imagePath: item.iconURL),
                          errorWidget: (context, url, error) =>
                              Icon(Icons.wifi),
                        )
                      : Icon(
                          getIcon(item.iconType),
                          color: Colors.white,
                        ),
                  SizedBox(height: 10),
                  SizedBox(
                      width: scale(context, 102),
                      child: Text(item.text ?? "",
                          textAlign: TextAlign.center,
                          style: AuroraTheme.of(context)
                              .textStyle(TypescaleValues.P4)))
                ],
              )),
      ],
    );
  }

  Widget getDefaultLayout(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
          bottom: AuroraTheme.of(context).verticalWidgetSpacing),
      child: Column(
        children: [
          StepWidgetV2(instructionsWidgetDate: instructionsWidgetDate,),
        ],
      ),
    );
  }

  Widget getLayoutForOrientation(
      BuildContext context, Orientation? orientation) {
    if (orientation == null) return getDefaultLayout(context);
    switch (orientation) {
      case Orientation.TOP:
        return getTopOrientationLayout(context);
      case Orientation.VERTICAL:
        return getVerticalOrientationLayout(context);
      default:
        return getDefaultLayout(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    String? title = instructionsWidgetDate.header?.title ?? "";
    String? subtitle = instructionsWidgetDate.header?.subtitle ?? "";
    return instructionsWidgetDate.blurEnabled
        ? BlurView(
            borderRadius: 0,
            child: Container(
              padding: EdgeInsets.symmetric(
                  vertical: AuroraTheme.of(context).verticalWidgetSpacing,
                  horizontal: 20),
              alignment: Alignment.centerLeft,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Offstage(
                    offstage: !((title != null && title.isEmpty) ||
                        instructionsWidgetDate.showHeader),
                    child: WidgetHeader(
                        cardHeaderData:
                            WidgetHeaderData(title: title, subTitle: subtitle)),
                  ),
                  title != null || title.isNotEmpty
                      ? SizedBox(height: 15)
                      : SizedBox.shrink(),
                  instructionsWidgetDate.items != null
                      ? getLayoutForOrientation(
                          context, instructionsWidgetDate.orientation)
                      : Container()
                ],
              ),
            ),
          )
        : Container(
            padding: EdgeInsets.symmetric(
                vertical: AuroraTheme.of(context).verticalWidgetSpacing,
                ),
            alignment: Alignment.centerLeft,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Offstage(
                  offstage: !((title != null && title.isEmpty) ||
                      instructionsWidgetDate.showHeader),
                  child: WidgetHeader(
                      cardHeaderData:
                          WidgetHeaderData(title: title, subTitle: subtitle)),
                ),
                title != null || title.isNotEmpty
                    ? SizedBox(height: 15)
                    : SizedBox.shrink(),
                instructionsWidgetDate.items != null
                    ? getLayoutForOrientation(
                        context, instructionsWidgetDate.orientation)
                    : Container()
              ],
            ),
          );
  }

  IconData getIcon(String? iconType) {
    switch (iconType) {
      case "DATE":
        return Icons.calendar_today_sharp;
      case "REPORT":
        return Icons.assignment_rounded;
      case "NETWORK":
        return Icons.wifi;
      case "BRIGHTNESS":
        return Icons.brightness_high_rounded;
    }
    return Icons.done;
  }
}


class StepWidgetV2 extends StatefulWidget {
  final InstructionsWidgetData instructionsWidgetDate;

  const StepWidgetV2({Key? key, required this.instructionsWidgetDate})
      : super(key: key);

  @override
  _StepWidgetV2State createState() => _StepWidgetV2State();
}

class _StepWidgetV2State extends State<StepWidgetV2> {
  bool isDropdownOpen = true;

  IconData getIcon(String? iconType) {
    switch (iconType) {
      case "DATE":
        return Icons.calendar_today_sharp;
      case "REPORT":
        return Icons.assignment_rounded;
      case "NETWORK":
        return Icons.wifi;
      case "BRIGHTNESS":
        return Icons.brightness_high_rounded;
    }
    return Icons.done;
  }

  @override
  Widget build(BuildContext context) {
    AuroraThemeData themeData = AuroraTheme.of(context);

    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Divider(height: 1, color: Colors.grey),
        SizedBox(height: 15,),
        InkWell(
          onTap: () {
            setState(() {
              isDropdownOpen = !isDropdownOpen;
            });
          },
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              SizedBox(width: 20,),
              Text(
                "Tips for video consultation",
                style: themeData.textStyle(TypescaleValues.P3, color: Colors.white),
              ),
              Expanded(child: Container(),),
              Icon(isDropdownOpen ? Icons.arrow_drop_up : Icons.arrow_drop_down, color: Colors.white,),
            ],
          ),
        ),
        SizedBox(height: 15,),
        if(!isDropdownOpen)
          Divider(height: 1, color: Colors.grey,),
        if (isDropdownOpen)
          for (var item in widget.instructionsWidgetDate.items!)
            Padding(
                padding: EdgeInsets.symmetric(vertical: 10, horizontal: 20),
                child: Row(
                  children: [
                    item.iconURL != null
                        ? BlurView(
                      borderRadius: 50,
                          child: CFNetworkImage(
                                                fit: BoxFit.cover,
                                                height: scale(context, 35),
                                                width: scale(context, 35),
                                                imageUrl:
                                                getImageUrl(context, imagePath: item.iconURL),
                                                errorWidget: (context, url, error) =>
                            Icon(Icons.wifi),
                                              ),
                        )
                        : Icon(
                      getIcon(item.iconType),
                      color: Colors.white,
                    ),
                    SizedBox(width: 10),
                    Flexible(
                        child: Text(item.text ?? "",
                            textAlign: TextAlign.left,
                            style: AuroraTheme.of(context)
                                .textStyle(TypescaleValues.P4)))
                  ],
                )),
      ],
    );
  }
}

import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:readmore/readmore.dart';
import '../../../blocs/clp/models.dart';

class ReviewCardV2 extends StatelessWidget {
  final ReviewCardData cardData;

  const ReviewCardV2({required this.cardData, Key? key})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlurView(
      child: Container(
        width: scale(context, cardData.cardWidth ?? 300),
        padding: EdgeInsets.symmetric(
            vertical: Spacings.x3, horizontal: Spacings.x3),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(20),
                  child: CFNetworkImage(
                    imageUrl: getImageUrl(context,
                        imagePath: cardData.imageUrl ?? ""),
                    fit: BoxFit.cover,
                    width: 40,
                    height: 40,
                  ),
                ),
                SizedBox(
                  width: Spacings.x2,
                ),
                Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(cardData.title ?? "",
                        style: AuroraTheme.of(context)
                            .textStyle(TypescaleValues.P3)),
                    RichText(
                      textAlign: TextAlign.center,
                      text: TextSpan(children: [
                        TextSpan(
                          text: cardData.prefix,
                          style: AuroraTheme.of(context).textStyle(
                            TypescaleValues.P8,
                          ),
                        ),
                        if (cardData.suffix != null &&
                            cardData.prefix != null)
                          TextSpan(
                            text: " • ",
                            style: AuroraTheme.of(context).textStyle(
                                TypescaleValues.P8,
                                color: Colors.white60),
                          ),
                        TextSpan(
                          text: cardData.suffix,
                          style: AuroraTheme.of(context).textStyle(
                              TypescaleValues.P8,
                              color: Colors.white60),
                        ),
                      ]),
                    ),
                  ],
                ),
              ],
            ),
            SizedBox(
              height: Spacings.x1,
            ),
            ReadMoreText(
              cardData.description ?? "",
              trimLines: 5,
              colorClickableText: Colors.white,
              trimMode: TrimMode.Line,
              trimCollapsedText: 'Read more',
              trimExpandedText: 'Read less',
              style: AuroraTheme.of(context).textStyle(TypescaleValues.P9),
              lessStyle: TextStyle(fontWeight: FontWeight.bold),
              moreStyle: TextStyle(fontWeight: FontWeight.bold),
            )

          ],
        ),
      ),
    );
  }
}

import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:transform/UI/clp/widgets/congo_widget.dart';
import 'package:transform/UI/clp/widgets/workflow_widget_v2.dart';
import 'package:transform/blocs/order/models.dart';

import '../../../blocs/clp/models.dart';

class OnboardingStepWidgetView extends StatefulWidget {
  final OnboardingStepWidgetData onboardingStepWidgetData;

  const OnboardingStepWidgetView({required this.onboardingStepWidgetData});

  @override
  _OnboardingStepWidgetViewState createState() =>
      _OnboardingStepWidgetViewState();
}

class _OnboardingStepWidgetViewState extends State<OnboardingStepWidgetView>
    with SingleTickerProviderStateMixin {
  late int openIndex = -1;
  late OnboardingStepWidgetData onboardingStepWidgetData;

  @override
  void initState() {
    onboardingStepWidgetData = widget.onboardingStepWidgetData;
    super.initState();
  }

  @override
  void didUpdateWidget(OnboardingStepWidgetView oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.onboardingStepWidgetData != widget.onboardingStepWidgetData) {
      onboardingStepWidgetData = widget.onboardingStepWidgetData;
    }
  }

  @override
  Widget build(BuildContext context) {
    onboardingStepWidgetData = widget.onboardingStepWidgetData;
    for (int i = 0; i < onboardingStepWidgetData.cardsV2!.length; i++) {
      if (onboardingStepWidgetData.cardsV2![i].stepCardV2State ==
          StepCardV2State.NOT_COMPLETED) {
        openIndex = i;
        break;
      }
    }
    if (openIndex == -1) openIndex = 0;

    return Theme(
      data: ThemeData(brightness: Brightness.light),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (onboardingStepWidgetData.cardsV2 != null)
            for (int i = 0;
            i < onboardingStepWidgetData.cardsV2!.length;
            i++)
              if (onboardingStepWidgetData.cardsV2![i].stepCardV2State ==
                  StepCardV2State.NOT_COMPLETED)
                StepWidgetV2(
                  stepCardV2: onboardingStepWidgetData.cardsV2![i],
                  isOpen: openIndex ==
                      i,
                  onTap: () {
                    setState(() {
                      openIndex = i;
                    });
                  },
                ),
          if (onboardingStepWidgetData.cardsV2 != null)
            for (int i = 0;
            i < onboardingStepWidgetData.cardsV2!.length;
            i++)
              if (onboardingStepWidgetData.cardsV2![i].stepCardV2State ==
                  StepCardV2State.COMPLETED)
                StepWidgetV2(
                  stepCardV2: onboardingStepWidgetData.cardsV2![i],
                  isOpen: openIndex == i,
                  onTap: () {
                    setState(() {
                      openIndex = i;
                    });
                  },
                ),
        ],
      ),
    );
  }
}

class StepWidgetV2 extends StatefulWidget {
  final StepCardV2 stepCardV2;
  final bool isOpen;
  final Function onTap;

  const StepWidgetV2({
    Key? key,
    required this.stepCardV2,
    required this.isOpen,
    required this.onTap,
  }) : super(key: key);

  @override
  _StepWidgetV2State createState() => _StepWidgetV2State();
}

class _StepWidgetV2State extends State<StepWidgetV2> {
  bool isDropdownOpen = false;
  late StepCardV2 stepCardV2;

  @override
  void initState() {
    super.initState();
    isDropdownOpen = widget.isOpen;
  }

  @override
  void didUpdateWidget(StepWidgetV2 oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.stepCardV2 != widget.stepCardV2) {
      stepCardV2 = widget.stepCardV2;
    }
  }

  @override
  Widget build(BuildContext context) {
    AuroraThemeData themeData = AuroraTheme.of(context);
    stepCardV2 = widget.stepCardV2;

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Divider(
          color: Colors.grey,
          thickness: scale(context, 0.5),
        ),
        SizedBox(height: scale(context, 5)),
        InkWell(
          onTap: () {
            setState(() {
              isDropdownOpen = !isDropdownOpen;
            });
            widget.onTap();
          },
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              SizedBox(width: scale(context, 20)),
              if (stepCardV2.title != null)
                Text(
                  stepCardV2.title!,
                  style: themeData.textStyle(
                    (isDropdownOpen) ? TypescaleValues.P3 : TypescaleValues.P5,
                    color: (stepCardV2.stepCardV2State ==
                        StepCardV2State.COMPLETED)
                        ? Colors.white.withOpacity(0.4)
                        : Colors.white,
                  ),
                ),
              Expanded(child: Container()),
              Icon(
                isDropdownOpen
                    ? Icons.keyboard_arrow_up_outlined
                    : Icons.keyboard_arrow_down_outlined,
                color: Colors.white,
              ),
              SizedBox(width: scale(context, 20)),
            ],
          ),
        ),
        SizedBox(height: scale(context, 15)),
        // Divider(height: 1, color: Colors.grey, thickness: 0.5,),
        if (isDropdownOpen)
          WorkflowWidgetV2View(
            stepCardV2: stepCardV2,
          ),
      ],
    );
  }
}

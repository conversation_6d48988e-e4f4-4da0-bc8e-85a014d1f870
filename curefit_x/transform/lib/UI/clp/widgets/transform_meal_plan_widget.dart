import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/util/action_util.dart';
import 'package:flutter/material.dart';
import 'package:transform/blocs/clp/models.dart';

class TransformMealPlanWidget extends StatelessWidget {
  final TransformMealPlanWidgetData widgetData;

  const TransformMealPlanWidget({required this.widgetData, Key? key})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: Spacings.x4),
      child: GestureDetector(
        onTap: () {
          if (widgetData.action != null)
            clickActionWithAnalytics(
                widgetData.action!, context, widgetData.widgetInfo, {});
        },
        child: BlurView(
          child: Container(
            padding: EdgeInsets.symmetric(
                vertical: Spacings.x3, horizontal: Spacings.x4),
            child: Row(
              children: [
                CFNetworkImage(
                    imageUrl: getImageUrl(
                      context,
                      imagePath: widgetData.imageUrl,
                    ),
                    width: 75,
                    height: 75,
                    fit: BoxFit.fill),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(left: Spacings.x3),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (widgetData.title != null)
                          Text(
                            widgetData.title ?? "",
                            style: AuroraTheme.of(context)
                                .textStyle(TypescaleValues.P3),
                          ),
                        if (widgetData.subtitle != null)
                          Padding(
                            padding: const EdgeInsets.only(top: Spacings.x1),
                            child: Text(
                              widgetData.subtitle ?? "",
                              style: AuroraTheme.of(context).textStyle(
                                  TypescaleValues.P5,
                                  color: Colors.white60),
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
                if (widgetData.action != null)
                  Padding(
                    padding: const EdgeInsets.only(left: Spacings.x2),
                    child: Icon(
                      Icons.chevron_right_rounded,
                      color: Colors.white,
                      size: 20,
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

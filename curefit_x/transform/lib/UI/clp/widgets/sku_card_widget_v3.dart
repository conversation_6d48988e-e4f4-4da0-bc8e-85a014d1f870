import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/util/action_util.dart';
import 'package:flutter/material.dart';
import 'package:transform/blocs/weight_loss_clp/models.dart';

class SKUCardWidgetV3 extends StatelessWidget {
  final SKUCardWidgetV3Data widgetData;

  const SKUCardWidgetV3({required this.widgetData, Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: Spacings.x4),
      child: GestureDetector(
        onTap: () {
          if (widgetData.action != null)
            clickActionWithAnalytics(
                widgetData.action!, context, widgetData.widgetInfo, {});
        },
        child: BlurView(
          child: Column(
            children: [
              if (widgetData.isActivePack && widgetData.activePackText != null)
                ClipRRect(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(15.0),
                    topRight: Radius.circular(15.0),
                    bottomLeft: Radius.zero,
                    bottomRight: Radius.zero,
                  ),
                  child: BlurView(
                    blurType: BlurType.LOW,
                    borderRadius: 0,
                    child: Container(
                      width: double.infinity,
                      padding: EdgeInsets.symmetric(
                          vertical: Spacings.x2, horizontal: Spacings.x3),
                      child: Text(
                        widgetData.activePackText ?? "",
                        style: AuroraTheme.of(context)
                            .textStyle(TypescaleValues.P3),
                      ),
                    ),
                  ),
                ),
              Container(
                padding: EdgeInsets.only(
                    left: Spacings.x2,
                    right: Spacings.x3,
                    top: Spacings.x3,
                    bottom: Spacings.x3),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Align(
                      alignment: Alignment.centerLeft,
                      child: CFNetworkImage(
                          imageUrl: getImageUrl(
                            context,
                            imagePath: widgetData.imageUrl,
                          ),
                          width: 75,
                          height: 75,
                          fit: BoxFit.contain),
                    ),
                    SizedBox(
                      width: Spacings.x2,
                    ),
                    Expanded(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (widgetData.titleImageUrl != null)
                            CFNetworkImage(
                                imageUrl: getImageUrl(
                                  context,
                                  imagePath: widgetData.titleImageUrl,
                                ),
                                height: 24,
                                fit: BoxFit.fitHeight)
                          else if (widgetData.title != null)
                            Text(
                              widgetData.title ?? "",
                              style: AuroraTheme.of(context)
                                  .textStyle(TypescaleValues.H1),
                            ),
                          if (widgetData.subtitle != null)
                            Padding(
                              padding: const EdgeInsets.only(
                                  top: 2, bottom: Spacings.x1),
                              child: Text(
                                widgetData.subtitle ?? "",
                                style: AuroraTheme.of(context).textStyle(
                                    TypescaleValues.P3,
                                    color: Colors.white),
                              ),
                            ),
                          if (widgetData.offerings != null &&
                              widgetData.offerings!.isNotEmpty)
                            ...widgetData.offerings!
                                .map<Widget>((offering) => Padding(
                                      padding: const EdgeInsets.only(
                                          top: Spacings.x1),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.start,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            "•",
                                            style: AuroraTheme.of(context)
                                                .textStyle(TypescaleValues.P5,
                                                    color: Colors.white60),
                                          ),
                                          SizedBox(
                                            width: 10,
                                          ),
                                          Expanded(
                                            child: Text(
                                              offering,
                                              style: AuroraTheme.of(context)
                                                  .textStyle(TypescaleValues.P5,
                                                      color: Colors.white60),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ))
                                .toList(),
                          if (widgetData.footerTitle != null ||
                              widgetData.footerPrefix != null ||
                              widgetData.footerSuffix != null)
                            Container(
                              margin: const EdgeInsets.symmetric(
                                  vertical: Spacings.x3),
                              height: 1,
                              color: Colors.white12,
                            ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Expanded(
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    if (widgetData.footerTitle != null)
                                      Padding(
                                        padding: const EdgeInsets.only(
                                            top: Spacings.x1),
                                        child: Text(
                                          widgetData.footerTitle ?? "",
                                          style: AuroraTheme.of(context)
                                              .textStyle(TypescaleValues.P10,
                                                  color: Colors.white60),
                                        ),
                                      ),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.start,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        if (widgetData.footerPrefix != null)
                                          Text(
                                            widgetData.footerPrefix ?? "",
                                            style: AuroraTheme.of(context)
                                                .textStyle(TypescaleValues.P3,
                                                    color: Colors.white),
                                          ),
                                        if (widgetData.footerSuffix != null)
                                          Padding(
                                            padding: const EdgeInsets.only(
                                                left: Spacings.x1),
                                            child: Text(
                                              widgetData.footerSuffix ?? "",
                                              style: AuroraTheme.of(context)
                                                  .textStyle(
                                                      TypescaleValues.P10,
                                                      color: Colors.white60),
                                            ),
                                          ),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                              if (widgetData.action != null)
                                Padding(
                                  padding:
                                      const EdgeInsets.only(left: Spacings.x2),
                                  child: Icon(
                                    Icons.chevron_right_rounded,
                                    color: Colors.white,
                                    size: 30,
                                  ),
                                ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

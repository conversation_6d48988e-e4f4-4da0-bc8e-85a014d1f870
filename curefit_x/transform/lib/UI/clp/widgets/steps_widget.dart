import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/constants/widget_constants.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/Colors.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:common/ui/widgets/card/card_list_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/UI/health_score/widgets/lifestyle_row.dart';
import 'package:transform/blocs/clp/models.dart';
import 'package:common/util/theme.dart';
import 'package:common/action/action_handler.dart' as ActionHandler;
import 'package:common/ui/clever_stepper.dart' as CleverStepper;
import 'package:common/ui/ui_toolkit.dart';
import 'package:lottie/lottie.dart';

class StepsWidgetView extends StatelessWidget {
  final StepsWidgetData stepsWidgetData;

  const StepsWidgetView(this.stepsWidgetData);

  @override
  Widget build(BuildContext context) {
    if (stepsWidgetData.isVmWidget != null &&
        stepsWidgetData.isVmWidget == true) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: Spacings.x4),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Container(
            padding: EdgeInsets.only(top: 20, bottom: 30),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                stepsWidgetData.tagText.isNotEmpty
                    ? Container(
                        margin: const EdgeInsets.only(bottom: 6),
                        padding: const EdgeInsets.symmetric(
                            vertical: 4, horizontal: 8),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Text(
                          stepsWidgetData.tagText,
                          style: CFTextStyles.bold10(color: Colors.black),
                          textAlign: TextAlign.start,
                        ),
                      )
                    : Container(),
                stepsWidgetData.title.isNotEmpty
                    ? Text(
                        stepsWidgetData.title,
                        style: CFTextStyles.bold18(),
                        textAlign: TextAlign.start,
                      )
                    : Container(),
                stepsWidgetData.description.isNotEmpty
                    ? Padding(
                        padding: EdgeInsets.only(top: 10),
                        child: Text(
                          stepsWidgetData.description,
                          style: CFTextStyles.regular14(
                              color: ColorPalette.white60),
                        ))
                    : Container(),
              ],
            ),
          ),
          getCardView(cards: stepsWidgetData.cards),
          if (stepsWidgetData.cardLists != null)
            ...stepsWidgetData.cardLists!
                .map((cards) => getCardView(cards: cards))
          else
            Container(),
        ]),
      );
    }
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: Spacings.x4),
      child: Theme(
        data: ThemeData(brightness: Brightness.light),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          stepsWidgetData.title.isNotEmpty
              ? Padding(
                  padding: EdgeInsets.only(top: Spacings.x4, bottom: Spacings.x6),
                  child: Text(
                    stepsWidgetData.title,
                    style: AuroraTheme.of(context).textStyle(TypescaleValues.H1),
                    textAlign: TextAlign.start,
                  ),
                )
              : Container(),
          getCardView(cards: stepsWidgetData.cards),
          if (stepsWidgetData.cardLists != null)
            ...stepsWidgetData.cardLists!
                .map((cards) => getCardView(cards: cards))
          else
            Container(),
        ]),
      ),
    );
  }

  Widget getCardView({List<StepCard>? cards}) {
    if (cards == null) {
      return Container();
    }
    return CleverStepper.CleverStepper(
      physics: NeverScrollableScrollPhysics(),
      controlsBuilder: (context,
              {isStepActive = true,
              onStepCancel,
              onStepContinue,
              stepIndex = 0,
              stepState = CleverStepper.CleverStepState.complete}) =>
          Container(),
      steps: cards
          .map((e) => CleverStepper.CleverStep(
              state: getIcon(e.state),
              title: Container(),
              content: Padding(
                  padding: EdgeInsets.only(bottom: 35),
                  child: Opacity(
                      opacity: e.state == StepCardState.LOCKED ? 0.5 : 1,
                      child: e.showUpcomingCard
                          ? CardListWidgetView(
                              widgetData: getCardListWidgetData(e))
                          : StepCardView(e))),
              isActive: true))
          .toList(),
    );
  }

  CleverStepper.CleverStepState getIcon(StepCardState state) {
    switch (state) {
      case StepCardState.COMPLETED:
        return CleverStepper.CleverStepState.complete;
      case StepCardState.COMPLETED_CLICKABLE:
        return CleverStepper.CleverStepState.complete;
      case StepCardState.LOCKED:
        return CleverStepper.CleverStepState.locked;
      case StepCardState.ACTIVE:
        return CleverStepper.CleverStepState.editing;
    }
  }

  CardListWidgetData getCardListWidgetData(StepCard e) {
    List<CardListData> listData = <CardListData>[];
    listData.add(CardListData(
        title: e.title,
        subTitle: e.subtite,
        noteView: CardListNoteView(
            text: e.description, iconData: Icons.location_on_outlined),
        leftInfo:
            e.image != null ? CardListLeftInfo(images: [e.image ?? ""]) : null,
        rightInfo: CardListInfo(action: e.action)));
    return CardListWidgetData(
        widgetType: WidgetTypes.STEPS_WIDGET,
        title: "UPCOMING",
        showBorder: false,
        data: listData);
  }
}

class StepCardView extends StatelessWidget {
  final StepCard stepCard;

  const StepCardView(this.stepCard);

  @override
  Widget build(BuildContext context) {
    return BlurView(
        borderRadius: 10,
        child: InkWell(
            onTap: () {
              if ((stepCard.state == StepCardState.ACTIVE ||
                      stepCard.state == StepCardState.COMPLETED_CLICKABLE) &&
                  stepCard.action != null) {
                if (stepCard.lottieUrl != null) {
                  RepositoryProvider.of<AnalyticsRepository>(context)
                      .logWidgetClick(extraInfo: {
                    "Step": "LifeStyle",
                  }, widgetInfo: stepCard.widgetInfo);
                }
                ActionHandler.Action action = stepCard.action!;
                if (stepCard.image != null) {
                  action = ActionHandler.Action(
                      type: stepCard.action?.type,
                      url: stepCard.action?.url,
                      title: stepCard.action?.title,
                      meta: stepCard.action?.meta != null
                          ? {
                              ...stepCard.action!.meta!,
                              "coachImage": stepCard.image
                            }
                          : {"coachImage": stepCard.image});
                }
                ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
                PerformActionEvent event = PerformActionEvent(action);
                actionBloc.add(event);
              }
            },
            child: Container(
                padding: const EdgeInsets.all(Spacings.x3),
                child: (stepCard.imageCards != null &&
                        stepCard.imageCards!.isNotEmpty)
                    ? UploadPictureStepCard(stepCard)
                    : stepCard.image != null
                        ? CoachCallStepCard(stepCard)
                        : stepCard.lottieUrl != null
                            ? LifeStyleStepCard(stepCard)
                            : ActionStepCard(stepCard))));
  }
}

class ActionStepCard extends StatelessWidget {
  final StepCard stepCard;

  const ActionStepCard(this.stepCard);

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
                child: Text(stepCard.title,
                    style:
                        AuroraTheme.of(context).textStyle(TypescaleValues.H4))),
            SizedBox(
              width: 10,
            ),
            stepCard.state == StepCardState.ACTIVE
                ? CircularArrowIcon()
                : Container()
          ],
        ),
        stepCard.description != null
            ? Padding(
                padding: EdgeInsets.only(right: 15, top: 5, bottom: 10),
                child: Text(stepCard.description!,
                    style:
                        AuroraTheme.of(context).textStyle(TypescaleValues.P8)))
            : Container()
      ],
    );
  }
}

class CoachCallStepCard extends StatelessWidget {
  final StepCard stepCard;

  const CoachCallStepCard(this.stepCard);

  @override
  Widget build(BuildContext context) {
    return Container(
        child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Padding(
                padding: const EdgeInsets.only(right: 10),
                child: Text(
                  stepCard.subtite ?? "Schedule a call",
                  style: AuroraTheme.of(context).textStyle(TypescaleValues.H4),
                ),
              ),
            ),
            stepCard.state == StepCardState.ACTIVE
                ? CircularArrowIcon()
                : Container()
          ],
        ),
        SizedBox(
          height: 20,
        ),
        Hero(
            flightShuttleBuilder: flightShuttleBuilder,
            tag: "hero_coach" + (stepCard.subtite ?? ""),
            child: Row(children: [
              ClipRRect(
                  borderRadius: BorderRadius.circular(25.0),
                  child: CFNetworkImage(
                    width: 50,
                    height: 50,
                    imageUrl: getImageUrl(context, imagePath: stepCard.image),
                    fit: BoxFit.cover,
                  )),
              SizedBox(width: 14),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  ConstrainedBox(
                      constraints: BoxConstraints(maxWidth: 150),
                      child: Text(stepCard.title,
                          maxLines: 2,
                          style: AuroraTheme.of(context)
                              .textStyle(TypescaleValues.P3))),
                  SizedBox(
                    height: 10,
                  ),
                  Text(
                    stepCard.description ?? "Your Habit Coach",
                    style:
                        AuroraTheme.of(context).textStyle(TypescaleValues.P8),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 4,
                  ),
                ],
              )
            ])),
        if (stepCard.lockText != null) ...[
          SizedBox(
            height: Spacings.x3,
          ),
          Container(
            width: double.infinity,
            height: 1,
            color: Colors.white60,
          ),
          Padding(
            padding: const EdgeInsets.only(top: Spacings.x3),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline_rounded,
                  color: Colors.white,
                  size: 20,
                ),
                SizedBox(
                  width: Spacings.x2,
                ),
                Expanded(
                  child: Text(
                    stepCard.lockText ?? "",
                    style: AuroraTheme.of(context)
                        .textStyle(TypescaleValues.TAGTEXT),
                  ),
                ),
              ],
            ),
          ),
        ]
      ],
    ));
  }
}

class LifeStyleStepCard extends StatelessWidget {
  final StepCard stepCard;

  const LifeStyleStepCard(this.stepCard);

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.only(right: 10),
                  child: Text(
                    stepCard.title,
                    style:
                        AuroraTheme.of(context).textStyle(TypescaleValues.H4),
                  ),
                ),
              ),
              stepCard.state == StepCardState.ACTIVE
                  ? CircularArrowIcon()
                  : Container()
            ],
          ),
          SizedBox(
            height: 10,
          ),
          stepCard.lifestyleImageDataList != null
              ? LifeStyleRow(
                  rowData: stepCard.lifestyleImageDataList,
                  imageSize: 50,
                  verticalPadding: 0,
                  horizontalPadding: 0,
                )
              : Row(
                  children: [
                    Container(
                      width: 70,
                      height: 70,
                      child: Lottie.network(
                        getMediaUrl(stepCard.lottieUrl!),
                      ),
                    ),
                    SizedBox(width: 14),
                    Expanded(
                      child: Padding(
                        padding: EdgeInsets.only(right: 5),
                        child: Text(
                          stepCard.description ?? "",
                          style: AuroraTheme.of(context)
                              .textStyle(TypescaleValues.P8),
                        ),
                      ),
                    ),
                  ],
                ),
        ],
      ),
    );
  }
}

class UploadPictureStepCard extends StatelessWidget {
  final StepCard stepCard;

  const UploadPictureStepCard(this.stepCard);

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.only(right: 10),
                  child: Text(
                    stepCard.title,
                    style:
                        AuroraTheme.of(context).textStyle(TypescaleValues.H4),
                  ),
                ),
              ),
              stepCard.state == StepCardState.ACTIVE
                  ? CircularArrowIcon()
                  : Container()
            ],
          ),
          SizedBox(
            height: Spacings.x2,
          ),
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Expanded(
                child: BlurView(
                  borderRadius: 15,
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(
                      15,
                    ),
                    child: Container(
                      height: scale(context, 128),
                      foregroundDecoration: BoxDecoration(
                        borderRadius: BorderRadius.all(
                          Radius.circular(15),
                        ),
                        border: Border.all(color: Colors.white24),
                      ),
                      child: Stack(
                        children: [
                          Positioned.fill(
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(
                                15,
                              ),
                              child: CFNetworkImage(
                                imageUrl: getImageUrl(context,
                                    imagePath:
                                        stepCard.imageCards?.first.imageUrl ??
                                            ""),
                                height: scale(context, 128),
                                width: scale(context, 128),
                                fit: BoxFit.fill,
                              ),
                            ),
                          ),
                          Positioned(
                            top: 0,
                            left: 0,
                            child: BlurView(
                              borderRadius: 15,
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: Spacings.x2, vertical: 2),
                                child: Text(
                                  stepCard.imageCards?.first.tagText ?? "",
                                  style: AuroraTheme.of(context)
                                      .textStyle(TypescaleValues.P10),
                                ),
                              ),
                            ),
                          ),
                          Positioned(
                            bottom: 0,
                            right: 0,
                            left: 0,
                            child: BlurView(
                              borderRadius: 0,
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                    vertical: Spacings.x1),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    if (stepCard.imageCards?.first.showIcon ?? false)
                                      Icon(
                                        Icons.camera_alt_outlined,
                                        color: Colors.white,
                                        size: 12,
                                      ),
                                    SizedBox(
                                      width: Spacings.x1,
                                    ),
                                    Text(
                                      stepCard.imageCards?.first.title ?? "",
                                      style: AuroraTheme.of(context)
                                          .textStyle(TypescaleValues.TAGTEXT),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              SizedBox(
                width: Spacings.x2,
              ),
              Expanded(
                child: BlurView(
                  borderRadius: 15,
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(
                      15,
                    ),
                    child: Container(
                      height: scale(context, 128),
                      foregroundDecoration: BoxDecoration(
                        borderRadius: BorderRadius.all(
                          Radius.circular(15),
                        ),
                        border: Border.all(color: Colors.white24),
                      ),
                      child: Stack(
                        children: [
                          Positioned.fill(
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(
                                15,
                              ),
                              child: CFNetworkImage(
                                imageUrl: getImageUrl(context,
                                    imagePath:
                                        stepCard.imageCards?.last.imageUrl ??
                                            ""),
                                height: scale(context, 128),
                                width: scale(context, 128),
                                fit: BoxFit.fill,
                              ),
                            ),
                          ),
                          Positioned(
                            top: 0,
                            left: 2,
                            child: BlurView(
                              borderRadius: 15,
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: Spacings.x2, vertical: 2),
                                child: Text(
                                  stepCard.imageCards?.last.tagText ?? "",
                                  style: AuroraTheme.of(context)
                                      .textStyle(TypescaleValues.P10),
                                ),
                              ),
                            ),
                          ),
                          Positioned(
                            bottom: 0,
                            right: 0,
                            left: 0,
                            child: Column(
                              children: [
                                Text(
                                  stepCard.imageCards?.last.title ?? "",
                                  style: AuroraTheme.of(context).textStyle(
                                      TypescaleValues.TAGTEXT,
                                      color: Colors.white),
                                ),
                                Padding(
                                  padding: const EdgeInsets.symmetric(
                                      vertical: Spacings.x1),
                                  child: Text(
                                    stepCard.imageCards?.last.subtitle ?? "",
                                    style: AuroraTheme.of(context).textStyle(
                                        TypescaleValues.P10,
                                        color: Colors.white60),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

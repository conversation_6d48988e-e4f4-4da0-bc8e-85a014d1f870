import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/util/color.dart';
import 'package:flutter/material.dart' hide Action;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/clp/models.dart';

class ChallengesActionWidget extends StatelessWidget {
  final ChallengesActionWidgetData widgetData;

  const ChallengesActionWidget({required this.widgetData, Key? key})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    AuroraThemeData themeData = AuroraTheme.of(context);
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: Spacings.x4),
      child: BlurView(
        borderRadius: 10,
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.all(Radius.circular(10)),
          ),
          child: InkWell(
            onTap: () {
              if (widgetData.action != null) {
                ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
                PerformActionEvent event = PerformActionEvent(widgetData.action!);
                actionBloc.add(event);
              }
            },
            child: Padding(
              padding: const EdgeInsets.all(Spacings.x4),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Row(
                      children: [
                        CFNetworkImage(
                          fit: BoxFit.fill,
                          imageUrl:
                              getImageUrl(context, imagePath: widgetData.iconUrl),
                          placeholder: (BuildContext imageContext, String url) {
                            return Container(
                              width: 40.0,
                              height: 40.0,
                            );
                          },
                          width: 40.0,
                          height: 40.0,
                        ),
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.only(left: Spacings.x3),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                if (widgetData.title != null)
                                  ShaderMask(
                                    blendMode: BlendMode.srcIn,
                                    shaderCallback: (bounds) => LinearGradient(
                                            colors: widgetData.gradientColors
                                                .map((color) =>
                                                    HexColor.fromHex(color))
                                                .toList(),
                                            begin: Alignment.topCenter,
                                            end: Alignment.bottomCenter)
                                        .createShader(
                                      Rect.fromLTWH(
                                          0, 0, bounds.width, bounds.height),
                                    ),
                                    child: Text(
                                      widgetData.title ?? "",
                                      style:
                                          themeData.textStyle(TypescaleValues.H4),
                                    ),
                                  ),
                                if (widgetData.subtitle != null)
                                  Padding(
                                    padding:
                                        const EdgeInsets.only(top: Spacings.x1),
                                    child: Text(
                                      widgetData.subtitle ?? "",
                                      style: themeData.textStyle(
                                        TypescaleValues.P8,
                                      ),
                                    ),
                                  ),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Center(
                    child: Icon(Icons.arrow_forward_ios_rounded,
                        size: 15, color: Colors.white),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

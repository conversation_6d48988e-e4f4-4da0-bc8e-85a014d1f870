import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/util/action_util.dart';
import 'package:flutter/material.dart' hide Action;
import 'package:transform/blocs/lift_clp/models.dart';

class WorkoutListWidget extends StatelessWidget {
  final WorkoutListWidgetData widgetData;

  const WorkoutListWidget({required this.widgetData, Key? key})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            widgetData.title ?? "",
            style: AuroraTheme.of(context)
                .textStyle(TypescaleValues.P6, color: Colors.white60),
            textAlign: TextAlign.left,
          ),
          ...widgetData.workoutCards
              .map<Widget>(
                  (workoutCard) => getWorkoutCard(workoutCard, context))
              .toList(),
        ],
      ),
    );
  }

  getWorkoutCard(WorkoutCard workoutCard, BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.only(top: Spacings.x4),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (workoutCard.imageUrl != null)
                ClipRRect(
                  borderRadius: BorderRadius.all(Radius.circular(10)),
                  child: Container(
                    height: 70,
                    width: 70,
                    child: Stack(
                      children: [
                        CFNetworkImage(
                          height: 70,
                          width: 70,
                          imageUrl: getImageUrl(context,
                              imagePath: workoutCard.imageUrl),
                          fit: BoxFit.fitWidth,
                          errorWidget: (context, url, error) =>
                              const Icon(Icons.error),
                        ),
                        if (workoutCard.isCompleted)
                          Center(
                            child: Container(
                              height: 30,
                              width: 30,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                color: Colors.green,
                              ),
                              child: Icon(
                                Icons.check_rounded,
                                color: Colors.white,
                                size: 20,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.only(left: Spacings.x3),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        workoutCard.title ?? "",
                        style: AuroraTheme.of(context)
                            .textStyle(TypescaleValues.H4),
                      ),
                      SizedBox(
                        height: Spacings.x1,
                      ),
                      Text(
                        workoutCard.subtitle ?? "",
                        style: AuroraTheme.of(context).textStyle(
                            TypescaleValues.P8,
                            color: Colors.white60),
                      ),
                    ],
                  ),
                ),
              ),
              workoutCard.action != null
                  ? GestureDetector(
                      onTap: () {
                        clickActionWithAnalytics(
                            workoutCard.action!, context, null, {});
                      },
                      child: Padding(
                        padding: const EdgeInsets.only(left: Spacings.x3),
                        child: Icon(
                          Icons.edit_outlined,
                          color: Colors.white,
                          size: 20,
                        ),
                      ),
                    )
                  : Container(),
            ],
          ),
        ),
      ],
    );
  }
}

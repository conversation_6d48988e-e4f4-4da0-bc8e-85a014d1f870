import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/header/card_header.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:flutter/material.dart';
import 'package:transform/blocs/clp/models.dart';

class ProductOfferingWidget extends StatelessWidget {
  final ProductOfferingWidgetData widgetData;

  const ProductOfferingWidget({required this.widgetData});

  @override
  Widget build(BuildContext context) {
    AuroraThemeData themeData = AuroraTheme.of(context);
    return Container(
      padding: EdgeInsets.symmetric(horizontal: Spacings.x4),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (widgetData.widgetHeaderData != null)
            WidgetHeader(
              cardHeaderData: widgetData.widgetHeaderData!,
            ),
          ...widgetData.productOfferingCards!
              .map<Widget>(
                (offeringData) => Padding(
                  padding: const EdgeInsets.only(top: Spacings.x8),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      CFNetworkImage(
                        fit: BoxFit.fill,
                        width: 55,
                        height: 60,
                        imageUrl: getImageUrl(
                          context,
                          imagePath: offeringData.imageUrl ?? "",
                        ),
                      ),
                      SizedBox(width: Spacings.x4),
                      Expanded(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              offeringData.title ?? "",
                              style: themeData.textStyle(
                                  TypescaleValues.P1,
                                  color: Colors.white),
                            ),
                            Text(
                              offeringData.subtitle ?? "",
                              style: themeData.textStyle(TypescaleValues.P4, color: Colors.white60),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              )
              .toList(),
        ],
      ),
    );
  }
}

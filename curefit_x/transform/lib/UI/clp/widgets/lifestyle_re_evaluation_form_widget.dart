import 'package:common/blocs/form/events.dart';
import 'package:common/blocs/form/form_action_bloc.dart';
import 'package:common/blocs/form/form_bloc.dart';
import 'package:common/blocs/form/state.dart' as FormState;
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/UI/clp/widgets/lifestyle_re_evaluation_success_widget.dart';
import 'package:transform/blocs/clp/models.dart';
import 'package:common/ui/form/form_screen_inline.dart';


class LifestyleReEvaluationFormWidget extends StatefulWidget {
  final LifestyleReEvaluationFormWidgetData lifestyleReEvaluationFormWidgetData;
  LifestyleReEvaluationFormWidget(this.lifestyleReEvaluationFormWidgetData);


  @override
  _LifestyleFormWidgetState createState() => _LifestyleFormWidgetState();
}

class _LifestyleFormWidgetState extends State<LifestyleReEvaluationFormWidget> {


  @override
  void initState() {
    super.initState();
    final formBloc = BlocProvider.of<FormBloc>(context);
    if (formBloc.currentFormResponse == null || !identical(formBloc.currentFormResponse, widget.lifestyleReEvaluationFormWidgetData.currentFormResponse)) {
      formBloc.add(InitializeFormResponseEvent(widget.lifestyleReEvaluationFormWidgetData.currentFormResponse));
    }
  }



  @override
  Widget build(BuildContext context) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: Spacings.x4),
        height: 520,
        child: BlurView(
          borderRadius: AuroraTheme.of(context).cornerRadius,
            child: Padding(
              padding: EdgeInsets.all(20),
              child: BlocBuilder<FormBloc, FormState.FormState>(
                builder: (context, state) {
                  if ((state is FormState.FormSubmitted)) {
                    return LifestyleReEvaluationSuccessView();
                  } else {
                    return Column(
                      children: [
                        !(state is FormState.FormSubmitting) ?
                        Padding(
                          padding: EdgeInsets.symmetric(vertical: Spacings.x1),
                          child: Text(widget.lifestyleReEvaluationFormWidgetData.title, style: AuroraTheme.of(context).textStyle(TypescaleValues.P8, color: Colors.white.withOpacity(0.6))),
                        ) : Container(),
                        SizedBox(
                          height: 450,
                          child: FormScreenInline(),
                        )
                      ],
                    );
                  }
                },
              )
            )
        )
      );
  }
}

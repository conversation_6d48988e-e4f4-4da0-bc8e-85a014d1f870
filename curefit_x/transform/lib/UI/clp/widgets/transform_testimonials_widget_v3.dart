import 'dart:ui' as UI;

import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/atoms/paginated_dot_progress_bar.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/components/testimonials_widget.dart';
import 'package:common/ui/header/card_header.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/util/action_util.dart';
import 'package:flutter/material.dart';
import 'package:transform/blocs/clp/models.dart';

class TransformTestimonialsWidgetV3 extends StatefulWidget {
  final TransformTestimonialsWidgetV3Data widgetData;

  const TransformTestimonialsWidgetV3({required this.widgetData, Key? key})
      : super(key: key);

  @override
  State<TransformTestimonialsWidgetV3> createState() =>
      _TransformTestimonialsWidgetV3State();
}

class _TransformTestimonialsWidgetV3State
    extends State<TransformTestimonialsWidgetV3> {
  int initialIndex = 0;

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(
        children: [
          if (widget.widgetData.header != null)
            Stack(
            alignment: Alignment.center,
            children: [
                new Container(
                  height: 140,
                  width: scale(context, 375),
                  child: CustomPaint(
                    painter: LinePainter(),
                    child: BlurView(
                      backgroundColor: Colors.transparent,
                      borderRadius: 10,
                      child: Container(
                        width: 200,
                      ),
                    ),
                  ),
                ),
              Container(
                padding: EdgeInsets.only(
                    bottom: Spacings.x4, left: Spacings.x4, right: Spacings.x4),
                width: 300,
                child: WidgetHeader(
                  cardHeaderData: widget.widgetData.header!,
                ),
              ),
            ],
          ),
          if (widget.widgetData.data != null)
            CarouselSlider(
              items: widget.widgetData.data!.map<Widget>((testimonial) {
                return getTestimonialCard(context, testimonial,
                    widget.widgetData.data!.indexOf(testimonial));
              }).toList(),
              options: CarouselOptions(
                  height: 405,
                  initialPage: initialIndex,
                  enlargeCenterPage: true,
                  viewportFraction: 0.70,
                  onPageChanged: (index, _) {
                    setState(() {
                      initialIndex = index;
                    });
                  }),
            ),
          if (widget.widgetData.data != null)
            Padding(
              padding: const EdgeInsets.only(top: Spacings.x4),
              child: PaginatedDotProgressBar(
                totalCount: widget.widgetData.data!.length,
                currentIndex: initialIndex,
              ),
            ),
        ],
      ),
    );
  }

  Widget getTestimonialCard(
      BuildContext context, Testimonial testimonial, int index) {
    bool selectedCard = initialIndex == index;
    return InkWell(
      onTap: () {
        if (testimonial.action != null) {
          clickActionWithAnalytics(
              testimonial.action!, context, widget.widgetData.widgetInfo, {});
        }
      },
      child: BlurView(
        borderRadius: Spacings.x3,
        child: Container(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (testimonial.imageUrl != null)
                ClipRRect(
                  borderRadius: BorderRadius.only(
                      topRight: Radius.circular(Spacings.x3),
                      topLeft: Radius.circular(Spacings.x3)),
                  child: CFNetworkImage(
                    width: !selectedCard ? double.infinity : null,
                    imageUrl:
                    getImageUrl(context, imagePath: testimonial.imageUrl!),
                    fit: BoxFit.fill,
                    errorWidget: (context, url, error) =>
                    const Icon(Icons.error),
                    placeholder: (BuildContext context, String url) {
                      return Container(
                        height: 200,
                      );
                    },
                  ),
                ),
              if (testimonial.title != null)
                Padding(
                  padding: EdgeInsets.only(
                      top: selectedCard ? Spacings.x1 : 0.0,
                      left: Spacings.x2,
                      right: Spacings.x2),
                  child: Text(
                    testimonial.title ?? "",
                    style: AuroraTheme.of(context).textStyle(!selectedCard
                        ? TypescaleValues.P5
                        : TypescaleValues.H4),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 2,
                  ),
                ),
              if (testimonial.description != null)
                Padding(
                  padding: EdgeInsets.only(
                      top: selectedCard ? Spacings.x1 : 0.0,
                      left: Spacings.x2,
                      right: Spacings.x2,
                      bottom: selectedCard ? Spacings.x4 : Spacings.x2),
                  child: Text(
                    testimonial.description ?? "",
                    style: AuroraTheme.of(context).textStyle(TypescaleValues.P3,
                        decoration: TextDecoration.underline),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}

class RectPainter {}

class LinePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    double offset = 75, lineWidth = size.width - 2 * offset;
    // Inverted trapezium
    var pathInvTrapezium = Path();
    pathInvTrapezium.moveTo(offset, 0);
    pathInvTrapezium.lineTo(lineWidth / 3 + offset, size.height);
    pathInvTrapezium.lineTo(2 * lineWidth / 3 + offset, size.height);
    pathInvTrapezium.lineTo(lineWidth + offset, 0);
    pathInvTrapezium.lineTo(-(lineWidth + offset), 0);
    final paintInvTrapezium = Paint()
      ..shader = UI.Gradient.linear(Offset.zero, Offset(0, size.height), [
        Color.fromRGBO(0, 195, 195, 0.6),
        Color.fromRGBO(0, 195, 195, 0.5),
        Color.fromRGBO(0, 0, 0, 0.1)
      ], [
        0.1,
        0.5,
        1.0
      ]);
    canvas.drawPath(pathInvTrapezium, paintInvTrapezium);

    // Trapezium
    var pathTrapezium = Path();
    pathTrapezium.lineTo(size.width - offset, 0);
    pathTrapezium.lineTo((3 * size.width) / 2 - offset, size.height);
    pathTrapezium.lineTo(-size.width / 2 + offset, size.height);
    pathTrapezium.lineTo(offset, 0);

    final paintTrapezium = Paint()
      ..shader = UI.Gradient.linear(Offset.zero, Offset(0, size.height), [
        Color.fromRGBO(0, 195, 195, 0.2),
        Color.fromRGBO(0, 0, 0, 0.1)
      ], [
        0.1,
        1.0
      ]);
    canvas.drawPath(pathTrapezium, paintTrapezium);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

import 'package:collection/collection.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:flutter/material.dart';
import 'package:transform/blocs/clp/models.dart';
import 'package:lottie/lottie.dart';

class ProductProgressTabWidget extends StatefulWidget {
  final ProductProgressTabWidgetData widgetData;

  const ProductProgressTabWidget({required this.widgetData, Key? key})
      : super(key: key);

  @override
  State<ProductProgressTabWidget> createState() =>
      _ProductProgressTabWidgetState();
}

class _ProductProgressTabWidgetState extends State<ProductProgressTabWidget>
    with TickerProviderStateMixin {
  int selectedTab = 0;
  int totalLength = 0;
  late AnimationController _animationController;

  @override
  void initState() {
    totalLength = widget.widgetData.tabItems!.length;
    _animationController = AnimationController(
      vsync: this,
      duration: Duration(seconds: widget.widgetData.duration),
    );
    _animationController.addListener(() {
      if (_animationController.isCompleted && mounted) {
        setState(() {
          selectedTab = (selectedTab + 1) % totalLength;
          _animationController.reset();
          _animationController.forward();
        });
      }
    });
    _animationController.forward();
    super.initState();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: Spacings.x4),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.white24, width: 2),
        borderRadius: BorderRadius.all(Radius.circular(15)),
      ),
      child: BlurView(
        child: Padding(
          padding: const EdgeInsets.symmetric(
              horizontal: Spacings.x4, vertical: Spacings.x4),
          child: Column(
            children: [
              if (widget.widgetData.tabItems != null)
                getTabCardWidget(widget.widgetData.tabItems!),
              SizedBox(
                height: Spacings.x5,
              ),
              if (widget.widgetData.tabItems != null)
                getTabDataWidget(widget.widgetData.tabItems!),
            ],
          ),
        ),
      ),
    );
  }

  Widget getTabCardWidget(List<ProgressTabProductItem> tabItems) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: tabItems
          .mapIndexed<Widget>(
            (index, item) => Column(
              children: [
                InkWell(
                  onTap: () {
                    if (mounted) {
                      setState(() {
                        selectedTab = index;
                        _animationController.reset();
                        _animationController.forward();
                      });
                    }
                  },
                  child: Padding(
                    padding: EdgeInsets.only(
                        right: index != tabItems.length - 1 ? Spacings.x2 : 0),
                    child: BlurView(
                      borderRadius: 10.0,
                      child: Container(
                        width: scale(context, 140),
                        decoration: BoxDecoration(
                          boxShadow: selectedTab == index
                              ? [
                                  BoxShadow(
                                      blurRadius: 5.0,
                                      spreadRadius: 2.0,
                                      color: Colors.white12)
                                ]
                              : null,
                          border: Border.all(color: Colors.white12, width: 1),
                          borderRadius: BorderRadius.all(Radius.circular(10)),
                        ),
                        padding: EdgeInsets.symmetric(
                            horizontal: Spacings.x2, vertical: Spacings.x2),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            CFNetworkImage(
                              imageUrl:
                                  getImageUrl(context, imagePath: item.iconUrl),
                              fit: BoxFit.cover,
                              width: 25,
                              height: 25,
                            ),
                            SizedBox(
                              height: Spacings.x2,
                            ),
                            Text(
                              item.title ?? "",
                              style: AuroraTheme.of(context)
                                  .textStyle(TypescaleValues.P7),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            )
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
                if (widget.widgetData.showTabProgress && selectedTab == index)
                  getLinearProgressBar(),
              ],
            ),
          )
          .toList(),
    );
  }

  Widget getLinearProgressBar() {
    return Transform.translate(
      offset: Offset(0, -3),
      child: Align(
          alignment: Alignment.centerLeft,
          child: Container(
            height: 5,
            width: scale(context, 140),
            child: Stack(
              children: [
                Align(
                  alignment: Alignment.centerLeft,
                  child: AnimatedBuilder(
                    animation: this._animationController,
                    builder: (BuildContext context, Widget? child) {
                      return Container(
                        width: (scale(context, 140) - 10) *
                            this._animationController.value,
                        height: 1,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [Colors.white12, Colors.white],
                            stops: [
                              0.3,
                              1.0,
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
                Align(
                  alignment: Alignment.centerLeft,
                  child: AnimatedBuilder(
                    animation: this._animationController,
                    builder: (BuildContext context, Widget? child) {
                      return Padding(
                        padding: EdgeInsets.only(
                            left: this._animationController.value *
                                (scale(context, 140) - 10)),
                        child: Container(
                          height: 3,
                          width: 8,
                          decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.centerLeft,
                                end: Alignment.centerRight,
                                stops: [0.1, 1.0],
                                colors: [
                                  Colors.white30,
                                  Colors.white,
                                ],
                              ),
                              boxShadow: [
                                BoxShadow(
                                    color: Colors.white54,
                                    offset: Offset(0.0, 0.0),
                                    spreadRadius: 2,
                                    blurRadius: 5),
                              ],
                              borderRadius: BorderRadius.all(Radius.circular(2))),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          )),
    );
  }

  Widget getTabDataWidget(List<ProgressTabProductItem> productItems) {
    return Container(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (productItems[selectedTab].productItem!.lottieUrl != null)
            ClipRRect(
              borderRadius: BorderRadius.circular(5),
              child: Lottie.network(
                getMediaUrl(
                    productItems[selectedTab].productItem!.lottieUrl ?? ""),
                alignment: Alignment.center,
                fit: BoxFit.cover,
                width: double.infinity,
                height: 150,
              ),
            ),
          if (productItems[selectedTab].productItem!.imageUrl != null)
            ClipRRect(
              borderRadius: BorderRadius.circular(5),
              child: CFNetworkImage(
                imageUrl: getImageUrl(context,
                    imagePath:
                        productItems[selectedTab].productItem!.imageUrl ?? ""),
                fit: BoxFit.cover,
                width: double.infinity,
                height: 150,
              ),
            ),
          if (productItems[selectedTab].productItem!.tagText != null)
            Padding(
              padding:
                  const EdgeInsets.only(top: Spacings.x2, bottom: Spacings.x1),
              child: BlurView(
                child: Container(
                  padding: EdgeInsets.symmetric(
                      horizontal: Spacings.x2, vertical: Spacings.x1),
                  child: ShaderMask(
                    blendMode: BlendMode.srcIn,
                    shaderCallback: (bounds) => LinearGradient(colors: [
                      Color.fromRGBO(1, 191, 255, 1),
                      Color.fromRGBO(253, 214, 0, 1),
                    ]).createShader(
                      Rect.fromLTWH(0, 0, bounds.width, bounds.height),
                    ),
                    child: Text(
                      productItems[selectedTab].productItem!.tagText ?? "",
                      style: AuroraTheme.of(context)
                          .textStyle(TypescaleValues.P6),
                    ),
                  ),
                ),
              ),
            ),
          if (productItems[selectedTab].productItem!.offerings != null &&
              productItems[selectedTab].productItem!.offerings!.isNotEmpty)
            ...productItems[selectedTab]
                .productItem!
                .offerings!
                .map<Widget>(
                  (offering) => Padding(
                    padding: const EdgeInsets.only(top: 7.5),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "•",
                          style: AuroraTheme.of(context).textStyle(
                              TypescaleValues.P2,
                              color: Colors.white60),
                        ),
                        SizedBox(
                          width: 10,
                        ),
                        Expanded(
                          child: Text(
                            offering,
                            style: AuroraTheme.of(context).textStyle(
                              TypescaleValues.P2,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                )
                .toList(),
        ],
      ),
    );
  }
}

import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/constants/ble_constants.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/showcase/highlighter_coachmark.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/util/action_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/UI/clp/widgets/coach_contact_widget.dart';
import 'package:transform/blocs/clp/clp_bloc.dart';
import 'package:transform/blocs/clp/events.dart';
import 'package:transform/blocs/clp/models.dart';
import 'package:common/util/color.dart';

class TransitionAppBar extends StatelessWidget {
  final double extent;
  final VoidCallback? onBackPressed;
  final Widget? expandedContent;
  final CoachContactWidgetData? coachContactWidgetData;
  final AppbarActionWidgetData? appbarActionWidgetData;
  final String? appBarTitle;
  final String? appBarTitleUrl;
  final bool hideBackButton;

  TransitionAppBar(
      {this.extent = 90,
      this.onBackPressed,
      this.expandedContent,
      this.coachContactWidgetData,
      this.appbarActionWidgetData,
      this.appBarTitle,
      this.appBarTitleUrl,
      this.hideBackButton = false});

  @override
  Widget build(BuildContext context) {
    final topPadding = MediaQuery.of(context).padding.top +
        AuroraTheme.of(context).embeddedSafeArea.top;
    return SliverPersistentHeader(
      pinned: true,
      delegate: _TransitionAppBarDelegate(
          startingColor: AuroraTheme.of(context).themeTextColor(),
          expandedContent: this.expandedContent,
          extent: extent,
          coachContactWidgetData: this.coachContactWidgetData,
          appbarActionWidgetData: this.appbarActionWidgetData,
          onBackPressed: this.onBackPressed,
          topPadding: topPadding,
          appBarTitle: this.appBarTitle,
          appBarTitleUrl: this.appBarTitleUrl,
          hideBackButton: this.hideBackButton),
    );
  }
}

class _TransitionAppBarDelegate extends SliverPersistentHeaderDelegate {
  late EdgeInsetsTween _avatarMarginTween;
  late EdgeInsetsTween _backButtonTween;
  final _avatarAlignTween =
      AlignmentTween(begin: Alignment.center, end: Alignment.center);
  late ColorTween _colorTween;
  final VoidCallback? onBackPressed;
  final double extent;
  final double topPadding;
  final Widget? expandedContent;
  final CoachContactWidgetData? coachContactWidgetData;
  final AppbarActionWidgetData? appbarActionWidgetData;
  final Color startingColor;
  final String? appBarTitle;
  final String? appBarTitleUrl;
  final bool? hideBackButton;

  _TransitionAppBarDelegate(
      {this.expandedContent,
      this.extent = 250,
      required this.startingColor,
      this.coachContactWidgetData,
      this.appbarActionWidgetData,
      this.onBackPressed,
      this.appBarTitle,
      this.appBarTitleUrl,
      this.topPadding = 0,
      this.hideBackButton = false}) {
    _colorTween = ColorTween(begin: this.startingColor, end: Colors.white);
    _backButtonTween = EdgeInsetsTween(
        begin: EdgeInsets.only(top: this.topPadding),
        end: EdgeInsets.only(top: this.topPadding));
    _avatarMarginTween = EdgeInsetsTween(
        begin: EdgeInsets.only(left: 0.0, top: topPadding),
        end: EdgeInsets.only(left: 0.0, top: topPadding));
  }

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    double colorTempVal = 80 * maxExtent / 100;
    final colorProgress =
        shrinkOffset > colorTempVal ? 1.0 : shrinkOffset / colorTempVal;
    final color = _colorTween.lerp(colorProgress);
    final appBarSize = maxExtent - shrinkOffset;
    final proportion = 2 - (maxExtent / appBarSize);
    final percent = proportion < 0 || proportion > 1 ? 0.0 : proportion;

    double tempVal = 32 * maxExtent / 100;
    final progress = shrinkOffset > tempVal ? 1.0 : shrinkOffset / tempVal;
    final avatarMargin = _avatarMarginTween.lerp(progress);
    final backButtonMargin = _backButtonTween.lerp(progress);
    final avatarAlign = _avatarAlignTween.lerp(progress);
    return Stack(
      children: <Widget>[
        Opacity(
            opacity: 1 - percent,
            child: Container(
              height: shrinkOffset * 2,
              child: BlurView(
                blurType: BlurType.HIGH,
                borderRadius: 0,
              ),
              constraints: BoxConstraints(maxHeight: minExtent),
            )),
        if (this.coachContactWidgetData != null)
          Padding(
              padding: EdgeInsets.only(left: 20, right: 20),
              child: CoachContactWidgetView(this.coachContactWidgetData!,
                  opacity: percent, iconColor: color!)),
        Container(
            height: minExtent,
            child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(children: [
                    Padding(
                        padding: backButtonMargin,
                        child: (this.hideBackButton == false)
                            ? BackButton(
                                color: color,
                                onPressed: this.onBackPressed,
                              )
                            : SizedBox(
                                width: 20,
                              )),
                    Padding(
                        padding: avatarMargin,
                        child: Align(
                            alignment: avatarAlign,
                            child: appBarTitleUrl != null
                                ? CFNetworkImage(
                                    imageUrl: getImageUrl(context,
                                        imagePath: appBarTitleUrl),
                                    width: 120,
                                    color: color,
                                    placeholder:
                                        (BuildContext context, String url) {
                                      return Container();
                                    },
                                  )
                                : appBarTitle != null
                                    ? Text(
                                        appBarTitle!,
                                        style: AuroraTheme.of(context)
                                            .textStyle(TypescaleValues.H1,
                                                color: color),
                                      )
                                    : Image.asset('assets/transform_logo.png',
                                        width: 120, color: color))),
                  ]),
                  Container(
                      padding: avatarMargin,
                      alignment: Alignment.center,
                      child: CoachHeaderIcons(
                        color!,
                        coachContactWidgetData: coachContactWidgetData,
                        appbarActionWidgetData: appbarActionWidgetData,
                      ))
                ])),
      ],
    );
  }

  @override
  double get maxExtent => this.coachContactWidgetData != null ? 160 : 90;

  @override
  double get minExtent => 90;

  @override
  bool shouldRebuild(_TransitionAppBarDelegate oldDelegate) {
    return true;
  }
}

class CoachHeader extends SliverPersistentHeaderDelegate {
  final Widget child;

  CoachHeader(this.child);

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Padding(padding: EdgeInsets.symmetric(horizontal: 20), child: child);
  }

  @override
  double get maxExtent => 80;

  @override
  double get minExtent => 0;

  @override
  bool shouldRebuild(SliverPersistentHeaderDelegate oldDelegate) {
    return true;
  }
}

class CoachHeaderIcons extends StatefulWidget {
  final bool showProgressCoachmark;
  final CoachContactWidgetData? coachContactWidgetData;
  final AppbarActionWidgetData? appbarActionWidgetData;
  final Color color;

  CoachHeaderIcons(this.color,
      {this.coachContactWidgetData,
      this.appbarActionWidgetData,
      this.showProgressCoachmark = false});

  @override
  CoachHeaderIconsState createState() => CoachHeaderIconsState();
}

class CoachHeaderIconsState extends State<CoachHeaderIcons> {
  late GlobalKey progressIconKey;

  @override
  void didUpdateWidget(covariant CoachHeaderIcons oldWidget) {
    // TODO: implement didUpdateWidget
    super.didUpdateWidget(oldWidget);
    if (oldWidget.coachContactWidgetData?.coachmarkInfo !=
        widget.coachContactWidgetData?.coachmarkInfo) {
      if (widget.coachContactWidgetData?.coachmarkInfo != null) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          showCoachMarkSliders();
        });
      }
    }
  }

  @override
  void initState() {
    super.initState();
    progressIconKey = GlobalObjectKey(widget.hashCode.toString());
    if (widget.coachContactWidgetData?.coachmarkInfo != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        showCoachMarkSliders();
      });
    }
  }

  void showCoachMarkSliders() {
    List<CoachmarkTextInfo>? textItems =
        widget.coachContactWidgetData?.coachmarkInfo?.textSpans;
    if (textItems == null) return;
    BlocProvider.of<CoachCLPBloc>(context).add(ProgressCoachmarkShownEvent());
    CoachMark coachMarkSlider = CoachMark();
    RenderBox? target =
        progressIconKey.currentContext?.findRenderObject() as RenderBox;
    Rect markRect = target.localToGlobal(Offset.zero) & target.size;
    coachMarkSlider.show(
        targetContext: progressIconKey.currentContext!,
        markRect: markRect.inflate(Spacings.x2),
        markShape: BoxShape.rectangle,
        children: [
          Positioned(
              top: markRect.top + Spacings.x6,
              left: Spacings.x6,
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                        width:
                            (MediaQuery.of(context).size.width - Spacings.x6) -
                                ((MediaQuery.of(context).size.width) -
                                    (markRect.center.dx + Spacings.x1)),
                        height: 80,
                        child: arrow()),
                    Padding(
                        padding: EdgeInsets.only(right: Spacings.x6),
                        child: Container(
                            constraints: BoxConstraints(
                                maxWidth: MediaQuery.of(context).size.width -
                                    Spacings.x6),
                            width: MediaQuery.of(context).size.width,
                            child: RichText(
                              text: TextSpan(
                                text: '',
                                style: AuroraTheme.of(context)
                                    .textStyle(TypescaleValues.P1),
                                children: textItems
                                    .map<TextSpan>((e) => TextSpan(
                                        text: e.title,
                                        style: AuroraTheme.of(context)
                                            .textStyle(
                                                e.typescale ??
                                                    TypescaleValues.H1,
                                                color: HexColor.fromHex(
                                                    e.hexColor))))
                                    .toList(),
                              ),
                            )))
                  ])),
        ],
        duration: Duration.zero,
        onClose: () {});
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        if (widget.coachContactWidgetData?.progressAction != null)
          createIconButton(
              getImageUrl(context,
                  imagePath: 'image/transform/icons/progress.png'),
              widget.color, () {
            ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
            PerformActionEvent event = PerformActionEvent(
                widget.coachContactWidgetData!.progressAction!);
            actionBloc.add(event);
            logWidgetClick(context, "progress");
          }, key: progressIconKey),
        if (widget.coachContactWidgetData?.callAction != null)
          createIconButton(
              getImageUrl(context,
                  imagePath: 'image/transform/icons/schedule_call.png'),
              widget.color, () {
            ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
            PerformActionEvent event =
                PerformActionEvent(widget.coachContactWidgetData!.callAction!);
            actionBloc.add(event);
            logWidgetClick(context, "call");
          }),
        if (widget.coachContactWidgetData?.chatAction != null)
          createIconButton(
              getImageUrl(context,
                  imagePath: 'image/transform/icons/whatsapp_1.png'),
              widget.color, () {
            ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
            PerformActionEvent event =
                PerformActionEvent(widget.coachContactWidgetData!.chatAction!);
            actionBloc.add(event);
            logWidgetClick(context, "chat");
          }),
        if (widget.appbarActionWidgetData?.textAction != null)
          InkWell(
            onTap: () {
              clickActionWithAnalytics(
                  widget.appbarActionWidgetData!.textAction!,
                  context,
                  widget.appbarActionWidgetData!.widgetInfo!, {});
            },
            child: Padding(
              padding: const EdgeInsets.only(right: Spacings.x4),
              child: Text(
                widget.appbarActionWidgetData!.textAction!.title ??
                    "EXPLORE PLANS",
                style: AuroraTheme.of(context).textStyle(TypescaleValues.P3),
              ),
            ),
          ),
      ],
    );
  }

  logWidgetClick(BuildContext context, String action) {
    RepositoryProvider.of<AnalyticsRepository>(context).logWidgetClick(
        extraInfo: {"action": action},
        widgetInfo: widget.coachContactWidgetData!.widgetInfo);
  }

  Widget createIconButton(String asset, Color color, VoidCallback callback,
      {Key? key}) {
    return Padding(
        key: key,
        padding: EdgeInsets.symmetric(horizontal: 15),
        child: GestureDetector(
          onTap: () {
            callback();
          },
          child: CFNetworkImage(
              width: 25,
              height: 25,
              imageUrl: asset,
              color: color,
              fit: BoxFit.contain),
        ));
  }
}

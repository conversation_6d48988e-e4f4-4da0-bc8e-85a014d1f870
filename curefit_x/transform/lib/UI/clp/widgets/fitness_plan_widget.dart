import 'package:common/ui/atoms/date_label.dart';
import 'package:common/ui/header/card_header.dart';
import 'package:common/ui/slot_selection/screens/slot_selection_screen.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/ui/widget_factory.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/lift_clp/models.dart';

class FitnessPlanWidget extends StatefulWidget {
  final FitnessPlanWidgetData widgetData;

  const FitnessPlanWidget({required this.widgetData, Key? key})
      : super(key: key);

  @override
  State<FitnessPlanWidget> createState() => _FitnessPlanWidgetState();
}

class _FitnessPlanWidgetState extends State<FitnessPlanWidget>
    with TickerProviderStateMixin {
  TabController? _tabController;
  var currentTabIndex = 0;

  @override
  void initState() {
    _tabController = TabController(
        vsync: this,
        length: widget.widgetData.fitnessDetailEntryList.length,
        initialIndex: widget.widgetData.currentIndex);
    currentTabIndex = widget.widgetData.currentIndex;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    WidgetFactory widgetFactory = RepositoryProvider.of<WidgetFactory>(context);
    return Container(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (widget.widgetData.header != null)
            Padding(
              padding: EdgeInsets.symmetric(horizontal: Spacings.x4),
              child: WidgetHeader(
                cardHeaderData: widget.widgetData.header!,
              ),
            ),
          Container(
            padding: const EdgeInsets.only(top: Spacings.x2),
            child: TabBar(
              tabAlignment: TabAlignment.start,
              dividerHeight: 0,
              indicatorWeight: 14,
              controller: _tabController,
              splashBorderRadius:
                  BorderRadius.all(Radius.circular(Spacings.x2)),
              padding: EdgeInsets.only(
                  top: Spacings.x1, left: Spacings.x4, right: Spacings.x4),
              labelPadding: EdgeInsets.symmetric(horizontal: Spacings.x3),
              tabs: widget.widgetData.fitnessDetailEntryList
                  .map((entry) => Padding(
                        padding: const EdgeInsets.only(
                            top: Spacings.x4, bottom: Spacings.x2),
                        child: DateLabel(
                          date: entry.date,
                          stateChangeEnabled: false,
                          onTapEnabled: false,
                          padding: 0,
                        ),
                      ))
                  .toList(),
              isScrollable: true,
              indicator: DropDownChevronDecoration(
                  containerColor: Colors.white.withOpacity(0.2)),
              onTap: (index) {
                setState(() {
                  currentTabIndex = index;
                });
              },
            ),
          ),
          SizedBox(
            height: Spacings.x2,
          ),
          if (widget
                  .widgetData.fitnessDetailEntryList[currentTabIndex].widgets !=
              null)
            Padding(
              padding: EdgeInsets.symmetric(horizontal: Spacings.x4),
              child: Column(
                key: ValueKey<String>(widget.widgetData
                        .fitnessDetailEntryList[currentTabIndex].id ??
                    ""),
                children: applyStaggeredAnimation(
                    widgetFactory.createWidgets(widget.widgetData
                        .fitnessDetailEntryList[currentTabIndex].widgets!),
                    context),
              ),
            ),
        ],
      ),
    );
  }
}

import 'package:common/ui/theme/aurora_theme.dart';
import 'package:flutter/material.dart';
import 'package:transform/blocs/clp/models.dart';
import 'package:transform/constants/constants.dart';

class CLPHeaderWidgetView extends StatelessWidget {
  final CLPHeaderWidgetData clpHeaderWidgetData;

  const CLPHeaderWidgetView(this.clpHeaderWidgetData);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
        height: 200,
        child: Stack(children: [
          Positioned.fill(
              child: Center(
            child: Text(clpHeaderWidgetData.title,
                textAlign: TextAlign.center,
                style: TextStyle(
                    color: AuroraTheme.of(context).themeTextColor(),
                    fontSize: 40,
                    fontWeight: FontWeight.w700)),
          ))
        ]));
  }
}

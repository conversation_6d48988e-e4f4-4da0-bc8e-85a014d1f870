import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:flutter/material.dart';
import 'package:transform/blocs/metric_input/models.dart';

class ConfirmationStatusWidget extends StatelessWidget {
  final ConfirmationStatusWidgetData widgetData;

  const ConfirmationStatusWidget({required this.widgetData, Key? key})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: Spacings.x4),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          if (widgetData.imageUrl != null)
            Padding(
              padding: const EdgeInsets.only(bottom: Spacings.x4),
              child: CFNetworkImage(
                imageUrl: getImageUrl(context, imagePath: widgetData.imageUrl),
                fit: BoxFit.cover,
                width: 60,
                height: 60,
              ),
            ),
          if (widgetData.isConfirmed)
            Padding(
              padding: const EdgeInsets.only(bottom: Spacings.x4),
              child: BlurView(
                borderRadius: 35,
                child: Container(
                  height: 70,
                  width: 70,
                  decoration: BoxDecoration(shape: BoxShape.circle),
                  child: Center(
                    child: Icon(
                      Icons.check_circle_outline_rounded,
                      color: Colors.greenAccent,
                      size: 45,
                    ),
                  ),
                ),
              ),
            ),
          if (widgetData.description != null)
            Padding(
              padding: EdgeInsets.only(top: Spacings.x1),
              child: Text(
                widgetData.description ?? "",
                style: AuroraTheme.of(context).textStyle(TypescaleValues.P2),
                textAlign: TextAlign.center,
              ),
            ),
          if (widgetData.title != null)
            Padding(
              padding: EdgeInsets.only(top: Spacings.x1),
              child: Text(
                widgetData.title ?? "",
                style: AuroraTheme.of(context).textStyle(TypescaleValues.H1),
                textAlign: TextAlign.center,
              ),
            ),
          if (widgetData.subtitle != null)
            Padding(
              padding: EdgeInsets.only(top: Spacings.x1),
              child: Text(
                widgetData.subtitle ?? "",
                style: AuroraTheme.of(context)
                    .textStyle(TypescaleValues.P4, color: Colors.white60),
                textAlign: TextAlign.center,
              ),
            ),
        ],
      ),
    );
  }
}

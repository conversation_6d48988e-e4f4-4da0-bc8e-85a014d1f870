import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/atoms/secondary_button.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/header/card_header.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/util/action_util.dart';
import 'package:flutter/material.dart' hide Action;
import 'package:transform/blocs/clp/models.dart';

class ReviewCardsWidget extends StatelessWidget {
  final ReviewCardsWidgetData widgetData;

  const ReviewCardsWidget({required this.widgetData, Key? key})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        if (widgetData.header != null)
          Padding(
            padding: EdgeInsets.only(
                bottom: Spacings.x3, left: Spacings.x4, right: Spacings.x4),
            child: WidgetHeader(
              cardHeaderData: widgetData.header!,
            ),
          ),
        if (widgetData.reviewCards != null)
          Container(
            height: 190,
            child: ListView(
              padding: EdgeInsets.symmetric(horizontal: Spacings.x4),
              scrollDirection: Axis.horizontal,
              children: widgetData.reviewCards!
                  .map<Widget>((cardData) => Padding(
                    padding: const EdgeInsets.only(right: Spacings.x3),
                    child: ReviewCard(
                          cardData: cardData,
                        ),
                  ))
                  .toList(),
            ),
          ),
        if (widgetData.action != null)
          Padding(
            padding: const EdgeInsets.only(
                top: Spacings.x3, left: Spacings.x4, right: Spacings.x4),
            child: SecondaryButton(() {
              clickActionWithAnalytics(
                  widgetData.action!, context, widgetData.widgetInfo, {});
            }, widgetData.action?.title ?? ""),
          ),
      ],
    );
  }
}

class ReviewCard extends StatelessWidget {
  final ReviewCardData cardData;

  const ReviewCard({required this.cardData, Key? key})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlurView(
      child: Container(
        width: scale(context, cardData.cardWidth ?? 300),
        padding: EdgeInsets.symmetric(
            vertical: Spacings.x3, horizontal: Spacings.x3),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.circular(20),
                  child: CFNetworkImage(
                    imageUrl: getImageUrl(context,
                        imagePath: cardData.imageUrl ?? ""),
                    fit: BoxFit.cover,
                    width: 40,
                    height: 40,
                  ),
                ),
                SizedBox(
                  width: Spacings.x2,
                ),
                Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(cardData.title ?? "",
                        style: AuroraTheme.of(context)
                            .textStyle(TypescaleValues.P3)),
                    RichText(
                      textAlign: TextAlign.center,
                      text: TextSpan(children: [
                        TextSpan(
                          text: cardData.prefix,
                          style: AuroraTheme.of(context).textStyle(
                            TypescaleValues.P8,
                          ),
                        ),
                        if (cardData.suffix != null &&
                            cardData.prefix != null)
                          TextSpan(
                            text: " • ",
                            style: AuroraTheme.of(context).textStyle(
                                TypescaleValues.P8,
                                color: Colors.white60),
                          ),
                        TextSpan(
                          text: cardData.suffix,
                          style: AuroraTheme.of(context).textStyle(
                              TypescaleValues.P8,
                              color: Colors.white60),
                        ),
                      ]),
                    ),
                  ],
                ),
              ],
            ),
            SizedBox(
              height: Spacings.x1,
            ),
            Text(
              cardData.description ?? "",
              style: AuroraTheme.of(context).textStyle(TypescaleValues.P9),
              overflow: TextOverflow.ellipsis,
              maxLines: 5,
            ),
          ],
        ),
      ),
    );
  }
}

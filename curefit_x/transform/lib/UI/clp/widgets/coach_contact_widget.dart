import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:flutter/material.dart' hide Action;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:collection/collection.dart';
import 'package:transform/blocs/clp/models.dart';
import 'package:transform/constants/constants.dart';

class CoachContactWidgetView extends StatelessWidget {
  final CoachContactWidgetData coachContactWidgetData;
  final double opacity;
  final Color iconColor;
  final Color? customTextColor;

  const CoachContactWidgetView(this.coachContactWidgetData,
      {this.opacity = 1.0, this.iconColor = themeTextColor, this.customTextColor});

  @override
  Widget build(BuildContext context) {
    Color textColor = AuroraTheme.of(context).themeTextColor();
    int numberOfImages = coachContactWidgetData.imagesList?.length ?? 0;
    return Opacity(
        opacity: this.opacity,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            if (coachContactWidgetData.imagesList != null &&
                coachContactWidgetData.imagesList!.isNotEmpty)
              InkWell(
                onTap: () {
                  if (coachContactWidgetData.action != null) {
                    ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
                    PerformActionEvent event =
                    PerformActionEvent(coachContactWidgetData.action!);
                    actionBloc.add(event);
                  }
                },
                child: Container(
                  width: (numberOfImages - 1) * 21 + 42,
                  height: 42,
                  child: Stack(
                    children: coachContactWidgetData.imagesList!.reversed
                        .mapIndexed<Widget>(
                          (index, image) => Transform.translate(
                            offset:
                                Offset((numberOfImages - index - 1) * 20, 0),
                            child: Container(
                              width: 40,
                              height: 40,
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(21),
                                  border: Border.all(
                                      color: Colors.white70, width: 2)),
                              child: ClipRRect(
                                child: CFNetworkImage(
                                  width: 40,
                                  height: 40,
                                  imageUrl: getImageUrl(context,
                                      width: 40, imagePath: image),
                                  fit: BoxFit.cover,
                                ),
                                borderRadius: BorderRadius.circular(20),
                              ),
                            ),
                          ),
                        )
                        .toList(),
                  ),
                ),
              ),
            SizedBox(width: 10),
            Container(
              child: Column(
                  mainAxisAlignment: MainAxisAlignment.end,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(coachContactWidgetData.title ?? "",
                        style: AuroraTheme.of(context)
                            .textStyle(TypescaleValues.P3, color: customTextColor ?? textColor)),
                    SizedBox(height: 4),
                    Text(coachContactWidgetData.subtitle ?? "",
                        style: AuroraTheme.of(context)
                            .textStyle(TypescaleValues.P5, color: customTextColor ?? textColor)),
                    SizedBox(height: 2),
                  ]),
            )
          ],
        ));
  }
}

import 'package:cached_network_image/cached_network_image.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/widget_factory.dart';
import 'package:flutter/material.dart';
import 'package:common/util/theme.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/clp/models.dart';

class PackOfferingsWidgetView extends StatefulWidget {
  final PackOfferingsWidgetData packOfferingsWidgetData;

  const PackOfferingsWidgetView(this.packOfferingsWidgetData);

  @override
  _PackOfferingsWidgetViewState createState() =>
      _PackOfferingsWidgetViewState();
}

class _PackOfferingsWidgetViewState extends State<PackOfferingsWidgetView>
    with TickerProviderStateMixin {
  late AnimationController lottieController;

  @override
  void initState() {
    super.initState();
    lottieController =
        AnimationController(vsync: this, duration: Duration(seconds: 3));
  }

  @override
  Widget build(BuildContext context) {
    WidgetFactory widgetFactory = RepositoryProvider.of<WidgetFactory>(context);
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: Spacings.x4),
      child: Column(
        children: [
          widgetFactory.createWidget(widget.packOfferingsWidgetData.widget),
        ],
      ),
    );
  }
}

import 'package:common/font/cf_icons.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/util/color.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:transform/UI/progress/components/bar_graph.dart';
import 'package:transform/UI/progress/components/base_chart.dart';
import 'package:transform/UI/progress/components/calendar_view.dart';
import 'package:transform/UI/progress/components/pie_chart.dart';
import 'package:transform/blocs/clp/models.dart';

class UserMetricChartWidgetV2 extends StatefulWidget {
  final UserMetricChartWidgetV2Data widgetData;

  const UserMetricChartWidgetV2({required this.widgetData, Key? key})
      : super(key: key);

  @override
  State<UserMetricChartWidgetV2> createState() =>
      _UserMetricChartWidgetV2State();
}

class _UserMetricChartWidgetV2State extends State<UserMetricChartWidgetV2> {
  bool isExpanded = false;

  @override
  void initState() {
    isExpanded = widget.widgetData.isExpanded;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
          left: Spacings.x3, right: Spacings.x3, top: Spacings.x2),
      child: Container(
        padding: EdgeInsets.symmetric(
            horizontal: Spacings.x3, vertical: Spacings.x2),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            InkWell(
              onTap: () {
                if (widget.widgetData.expandable) {
                  setState(() {
                    isExpanded = !isExpanded;
                  });
                }
              },
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        widget.widgetData.title ?? "",
                        style: AuroraTheme.of(context).textStyle(
                            TypescaleValues.P3,
                            color: Colors.white60),
                      ),
                      if (widget.widgetData.expandable)
                        Padding(
                          padding: const EdgeInsets.only(top: Spacings.x1),
                          child: BlurView(
                            child: Container(
                              margin: EdgeInsets.all(8),
                              decoration: BoxDecoration(shape: BoxShape.circle),
                              child: Center(
                                child: Icon(
                                  isExpanded
                                      ? CFIcons.chevron_up
                                      : CFIcons.chevron_down,
                                  size: 13,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                  Padding(
                    padding: const EdgeInsets.only(top: Spacings.x2),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          widget.widgetData.metricText ?? "",
                          style: AuroraTheme.of(context)
                              .textStyle(TypescaleValues.H10),
                        ),
                        SizedBox(
                          width: Spacings.x1,
                        ),
                        Padding(
                          padding: const EdgeInsets.only(bottom: Spacings.x1),
                          child: Text(
                            widget.widgetData.differenceText ?? "",
                            style: AuroraTheme.of(context).textStyle(
                                TypescaleValues.P6,
                                color: HexColor.fromHex(
                                    widget.widgetData.color ?? "#FFFFFF")),
                          ),
                        ),
                        SizedBox(
                          width: Spacings.x1,
                        ),
                        if (widget.widgetData.description != null)
                          Padding(
                            padding: const EdgeInsets.only(bottom: Spacings.x1),
                            child: Text(
                              widget.widgetData.description ?? "",
                              style: AuroraTheme.of(context).textStyle(
                                  TypescaleValues.P10,
                                  color: Colors.white60),
                            ),
                          ),
                      ],
                    ),
                  ),
                  if (widget.widgetData.subtitle != null)
                    Text(
                      widget.widgetData.subtitle ?? "",
                      style: AuroraTheme.of(context)
                          .textStyle(TypescaleValues.P3, color: Colors.white),
                    ),
                ],
              ),
            ),
            if (isExpanded)
              Padding(
                padding: const EdgeInsets.only(
                    top: Spacings.x3, bottom: Spacings.x2),
                child: getGraphView(),
              ),
            Container(
              margin: EdgeInsets.only(top: Spacings.x6),
              width: double.infinity,
              height: 1,
              color: Colors.white24,
            ),
          ],
        ),
      ),
    );
  }

  Widget getGraphView() {
    switch (widget.widgetData.graph) {
      case Graph.BAR_CHART:
        return BarGraph(
            graphViewList: widget.widgetData.graphViewList,
            initialGraph: widget.widgetData.initialGraph,
            action: widget.widgetData.action);
      case Graph.PIE_CHART:
        return PieChartView(pieChartValue: widget.widgetData.pieChartValue);
      case Graph.BASE_CHART:
        return BaseChart(
            graphViewList: widget.widgetData.graphViewList,
            initialGraph: widget.widgetData.initialGraph,
            action: widget.widgetData.action);
      case Graph.CALENDAR:
        return CalendarView(
            calendarChartData: widget.widgetData.calendarChartData);
      default:
        return Container();
    }
  }
}

import 'dart:io';
import 'dart:typed_data';

import 'package:collection/collection.dart';
import 'package:common/analytics/analytics_repository.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/atoms/secondary_button.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/util/action_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/clp/models.dart';
import 'package:share_plus/share_plus.dart';
import 'package:screenshot/screenshot.dart';
import 'package:path_provider/path_provider.dart';

class WorkflowWidgetView extends StatelessWidget {
  final WorkflowWidgetData workflowWidgetData;

  const WorkflowWidgetView({required this.workflowWidgetData});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: Spacings.x4),
      child: Theme(
        data: ThemeData(brightness: Brightness.light),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          getCardView(context, cards: workflowWidgetData.cards),
        ]),
      ),
    );
  }

  Widget getCardView(BuildContext context, {List<WorkflowCard>? cards}) {
    if (cards == null) {
      return Container();
    }
    return Column(
      children: [
        ...cards.mapIndexed((index, card) {
          bool isLast = index == cards.length - 1;
          return CustomClever(context, workflowCard: card, isLast: isLast);
        }).toList()
      ],
    );
  }

  Widget CustomClever(
    BuildContext context, {
    WorkflowCard? workflowCard,
    bool isLast = false,
  }) {
    return workflowCard != null
        ? Padding(
            padding: EdgeInsets.only(left: scale(context, Spacings.x1)),
            child: Stack(
              children: [
                if (!isLast)
                  PositionedDirectional(
                    top: 35,
                    bottom: 0,
                    start: 15,
                    child: CustomPaint(
                      painter: DashedLinePainter(),
                    ),
                  ),
                Center(
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Stack(
                        alignment: Alignment.center,
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(top: 5),
                            child: Container(
                              width: 30,
                              height: 30,
                              child: CFNetworkImage(
                                width: 30,
                                height: 30,
                                imageUrl: getImageUrl(context,
                                    imagePath: workflowCard.iconUrl),
                                fit: BoxFit.cover,
                              ),
                            ),
                          ),
                        ],
                      ),
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.only(
                              left: Spacings.x1, bottom: Spacings.x4),
                          child: WorkflowCardView(workflowCard),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          )
        : Container();
  }
}

class WorkflowCardView extends StatefulWidget {
  final WorkflowCard workflowCard;

  const WorkflowCardView(this.workflowCard);

  @override
  State<WorkflowCardView> createState() => _WorkflowCardViewState();
}

class _WorkflowCardViewState extends State<WorkflowCardView> {
  final ScreenshotController screenshotController = ScreenshotController();

  @override
  Widget build(BuildContext context) {
    if (widget.workflowCard.workoutCardList != null &&
        widget.workflowCard.workoutCardList!.isNotEmpty) {
      return getWorkOutListCard(context, widget.workflowCard);
    }
    return Screenshot(
        controller: screenshotController,
        child: getSingleCard(context, widget.workflowCard));
  }

  Widget getWorkOutListCard(BuildContext context, WorkflowCard workflowCard) {
    return Column(
      children: workflowCard.workoutCardList!.mapIndexed<Widget>((index, data) {
        bool isLast = index == workflowCard.workoutCardList!.length - 1;
        if (data.showOnlyAction && data.action != null) {
          return Column(
            children: [
              SecondaryButton(() {
                clickActionWithAnalytics(data.action!, context, null, {});
              }, data.action!.title ?? ""),
              if (!isLast) getWidgetsSeparator(context),
            ],
          );
        }
        WorkflowCard newCard = new WorkflowCard(
            title: data.title,
            subtitle: data.subtitle,
            description: data.description,
            image: data.imageUrl,
            action: data.action,
            iconUrl: workflowCard.iconUrl,
            state: workflowCard.state);
        return Column(
          children: [
            getSingleCard(context, newCard),
            if (!isLast) getWidgetsSeparator(context),
          ],
        );
      }).toList(),
    );
  }

  Widget getSingleCard(BuildContext context, WorkflowCard workflowCard) {
    return BlurView(
      borderRadius: 15,
      child: InkWell(
        onTap: () {
          if ((workflowCard.state == WorkflowCardState.ACTIVE ||
                  workflowCard.state ==
                      WorkflowCardState.COMPLETED_CLICKABLE) &&
              workflowCard.captureScreenShot) {
            screenshotController.capture().then((Uint8List? image) async {
              if (image != null) {
                Directory appDocDir = await getApplicationDocumentsDirectory();
                String appDocPath = appDocDir.path;
                var millis = DateTime.now().millisecondsSinceEpoch;
                File imagePath = await File('$appDocPath/$millis.png').create();
                imagePath.writeAsBytesSync(image);
                await Share.shareFiles([imagePath.path]).then((response) => {
                      RepositoryProvider.of<AnalyticsRepository>(context)
                          .logShareEvent(extraInfo: {
                        "subType": "share_screenshot",
                      })
                    });
              }
            }).catchError((onError) {
              print(onError);
            });
          } else if ((workflowCard.state == WorkflowCardState.ACTIVE ||
                  workflowCard.state ==
                      WorkflowCardState.COMPLETED_CLICKABLE) &&
              workflowCard.action != null) {
            if (workflowCard.secondaryAction != null) {
              clickActionWithAnalytics(workflowCard.secondaryAction!, context,
                  null, {"trigger": "secondaryAction"});
            }
            clickActionWithAnalytics(workflowCard.action!, context, null,
                {"trigger": "primaryAction"});
          }
        },
        child: Container(
          height: 190,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Padding(
                padding: EdgeInsets.all(Spacings.x3),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                        child: ClipRRect(
                            borderRadius: BorderRadius.circular(15.0),
                            child: CFNetworkImage(
                              width: 95,
                              height: 95,
                              imageUrl: getImageUrl(context,
                                  imagePath: workflowCard.image),
                              fit: BoxFit.cover,
                            ))),
                    SizedBox(width: Spacings.x2),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          if (workflowCard.title != null)
                            Text(
                              workflowCard.title ?? "",
                              maxLines: 2,
                              style: AuroraTheme.of(context).textStyle(
                                  TypescaleValues.P1,
                                  color: Colors.white),
                              overflow: TextOverflow.ellipsis,
                            ),
                          SizedBox(height: Spacings.x1),
                          if (workflowCard.subtitle != null)
                            Text(
                              workflowCard.subtitle ?? "",
                              maxLines: 2,
                              style: AuroraTheme.of(context).textStyle(
                                  TypescaleValues.P5,
                                  color: Colors.white60),
                              overflow: TextOverflow.ellipsis,
                            ),
                          SizedBox(height: 2),
                          if (workflowCard.description != null)
                            Text(
                              workflowCard.description ?? "",
                              maxLines: 2,
                              style: AuroraTheme.of(context).textStyle(
                                  TypescaleValues.P8,
                                  color: Colors.white60),
                              overflow: TextOverflow.ellipsis,
                            ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              Spacer(),
              if (workflowCard.action != null)
                ClipRRect(
                  borderRadius: BorderRadius.only(
                      bottomLeft: Radius.circular(Spacings.x3),
                      bottomRight: Radius.circular(Spacings.x3)),
                  child: SecondaryButton(
                    () {
                      clickActionWithAnalytics(
                          workflowCard.action!, context, null, {});
                    },
                    workflowCard.action?.title ?? "",
                    borderRadius: 0,
                    verticalPadding: 18,
                    textColor: Colors.white70,
                    enabled: workflowCard.state == WorkflowCardState.ACTIVE ||
                        workflowCard.state ==
                            WorkflowCardState.COMPLETED_CLICKABLE,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget getWidgetsSeparator(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: Spacings.x2, bottom: Spacings.x2),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Expanded(
            child: Container(
              margin: EdgeInsets.only(right: Spacings.x1),
              height: 1,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                  colors: <Color>[Colors.white12, Colors.white60],
                ),
              ),
            ),
          ),
          ConstrainedBox(
            constraints: BoxConstraints(maxWidth: scale(context, 300)),
            child: Text(
              " OR ",
              maxLines: 1,
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
              style: AuroraTheme.of(context)
                  .textStyle(TypescaleValues.P3, color: Colors.white60),
            ),
          ),
          Expanded(
            child: Container(
              margin: EdgeInsets.only(left: Spacings.x1),
              height: 1,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                  colors: <Color>[Colors.white60, Colors.white12],
                  tileMode: TileMode.mirror,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class DashedLinePainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    double dashHeight = 9, dashSpace = 5, startY = 0;
    final paint = Paint()
      ..color = Colors.grey
      ..strokeWidth = 1;
    while (startY < size.height) {
      canvas.drawLine(Offset(0, startY), Offset(0, startY + dashHeight), paint);
      startY += dashHeight + dashSpace;
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

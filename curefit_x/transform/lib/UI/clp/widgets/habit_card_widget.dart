import 'dart:ui';

import 'package:common/action/action_handler.dart' as ActionHandler;
import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/showcase/highlighter_coachmark.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:common/util/color.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_swipe_action_cell/core/cell.dart';
import 'package:transform/blocs/clp/models.dart';
import 'package:transform/blocs/habit/events.dart';
import 'package:transform/blocs/habit/habit_bloc.dart';
import 'package:transform/blocs/habit/state.dart';
import 'package:common/util/theme.dart';
import 'package:lottie/lottie.dart';
import 'package:transform/blocs/habit/update_habit_bloc.dart';
import 'package:enum_to_string/enum_to_string.dart';

enum HabitCardType { HABIT, WOD, LIVE_CLASS, ONE_TIME_ACTION }

class HabitCardWidgetView extends StatefulWidget {
  final HabitCardWidgetData habitCardWidgetData;

  HabitCardWidgetView(this.habitCardWidgetData);

  @override
  State<StatefulWidget> createState() => _HabitCardWidgetViewState();
}

class _HabitCardWidgetViewState extends State<HabitCardWidgetView> {
  late HabitBloc habitBloc;


  @override
  void initState() {
    super.initState();
    habitBloc = BlocProvider.of<HabitBloc>(context);
    if (habitBloc.habitCards != null && habitBloc.habitCards!.isNotEmpty) {
      habitBloc.add(HabitsPreLoadEvent(habitCards: habitBloc.habitCards));
    } else {
      habitBloc.add(HabitsPreLoadEvent(
          habitCards: widget.habitCardWidgetData.habitCards));
    }
  }

  @override
  void dispose() {
    habitBloc.add(ResetHabitCardsEvent());
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: Spacings.x4),
      child: BlocBuilder<HabitBloc, HabitState>(builder: (context, state) {
        if (state is HabitsLoading) {
          return Center(
            child: FancyLoadingIndicator(),
          );
        }
        if (state is HabitsLoaded) {
          return SizedBox(
              width: double.infinity,
              child: Padding(
                padding: EdgeInsets.only(
                    bottom: this.widget.habitCardWidgetData.bottomPadding ?? 0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    for (var habitCard in state.cards)
                      HabitCardView(habitCard,
                          removeCardCallback: (status) {},
                          widgetInfo:
                              this.widget.habitCardWidgetData.widgetInfo,
                          isLast: (state.cards.indexOf(habitCard) ==
                                  state.cards.length - 1 ||
                              state.cards.isEmpty)),
                  ],
                ),
              ));
        }
        if (state is HabitsNotLoaded) {
          return Container(
            child: Text(
              "Habits Loading Failed",
              style: AuroraTheme.of(context).textStyle(TypescaleValues.H1),
            ),
          );
        }
        return Container();
      }),
    );
  }
}

class HabitCardView extends StatefulWidget {
  final HabitCard habitCard;
  final Function removeCardCallback;
  final WidgetInfo? widgetInfo;
  final bool isLast;

  const HabitCardView(this.habitCard,
      {required this.removeCardCallback, this.widgetInfo, this.isLast = false});

  @override
  State<StatefulWidget> createState() => _HabitCardViewState();
}

class _HabitCardViewState extends State<HabitCardView>
    with TickerProviderStateMixin {
  UserResponse? draftUserResponse;
  UserResponse userResponse = UserResponse.NO_RESPONSE;
  GlobalKey? habitCardKey;

  late AnimationController lottieController;
  late AnimationController completionController;

  late Animation<double> cardOpacity;
  late Animation<double> cardYPosition;
  late Animation<double> actionYPosition;
  late Animation<double> finalTextYPosition;
  late Animation<double> textOpacity;
  late Animation<double> textReverseOpacity;
  late Animation<double> lottieOpacity;
  late Animation<double> lottieStartOpacity;
  late Animation<double> lottieStartYPosition;

  late Animation<double> completionCardYPosition;
  late Animation<double> completionCardOpacity;

  late Animation<double> cardClipBorder;
  bool addBorder = true;
  double scaleValue = 2.0;
  bool shownCoachMark = false;

  HabitCardAnimation? selectedCardAnimation;

  late Color headerBackgroundColor;

  @override
  void didUpdateWidget(covariant HabitCardView oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.habitCard.response != this.widget.habitCard.response ||
        oldWidget.habitCard.id != oldWidget.habitCard.id) {
      userResponse = this.widget.habitCard.response;
      resetCard();
    }
  }

  resetCard() {
    completionController.stop(canceled: true);
    lottieController.stop(canceled: true);
    completionController.reset();
    lottieController.reset();
    setupAnimations();
  }

  @override
  void dispose() {
    completionController.dispose();
    lottieController.dispose();
    super.dispose();
  }

  setupAnimations() {
    addBorder = userResponse == UserResponse.NO_RESPONSE ? true : false;
    cardOpacity = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(
      CurvedAnimation(
        parent: completionController,
        curve: Interval(
          0.0,
          0.1,
          curve: Curves.ease,
        ),
      ),
    );

    cardClipBorder = Tween<double>(
      begin: userResponse == UserResponse.NO_RESPONSE ? 0.8488 : 1.0,
      end: userResponse == UserResponse.NO_RESPONSE ? 1.0 : 1.0,
    ).animate(
      CurvedAnimation(
        parent: completionController,
        curve: Interval(
          0.4,
          0.5,
          curve: Curves.linearToEaseOut,
        ),
      ),
    );

    textOpacity = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(
      CurvedAnimation(
        parent: completionController,
        curve: Interval(
          0.0,
          0.1,
          curve: Curves.ease,
        ),
      ),
    );

    lottieStartYPosition = Tween<double>(
      begin: 80.0,
      end: -40.0,
    ).animate(
      CurvedAnimation(
        parent: completionController,
        curve: Interval(
          0.0,
          0.2,
          curve: Curves.linearToEaseOut,
        ),
      ),
    );
    lottieOpacity = Tween<double>(
      begin: 1.0,
      end: 0,
    ).animate(
      CurvedAnimation(
        parent: completionController,
        curve: Interval(
          0.3,
          0.4,
          curve: Curves.ease,
        ),
      ),
    );

    lottieStartOpacity = Tween<double>(
      begin: 0,
      end: 1.0,
    ).animate(
      CurvedAnimation(
        parent: completionController,
        curve: Interval(
          0,
          0.2,
          curve: Curves.ease,
        ),
      ),
    );

    completionCardOpacity = Tween<double>(
      begin: userResponse == UserResponse.NO_RESPONSE ? 0.0 : 1.0,
      end: userResponse == UserResponse.NO_RESPONSE ? 1.0 : 1.0,
    ).animate(
      CurvedAnimation(
        parent: completionController,
        curve: Interval(
          0.3,
          0.35,
          curve: Curves.easeIn,
        ),
      ),
    );

    textReverseOpacity = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(
      CurvedAnimation(
        parent: completionController,
        curve: Interval(
          0.3,
          0.4,
          curve: Curves.ease,
        ),
      ),
    );

    cardYPosition = Tween<double>(
      begin: 0.0,
      end: -20.0,
    ).animate(
      CurvedAnimation(
        parent: completionController,
        curve: Interval(
          0.0,
          1.0,
          curve: Curves.linearToEaseOut,
        ),
      ),
    );

    finalTextYPosition = Tween<double>(
      begin: 0.0,
      end: -50.0,
    ).animate(
      CurvedAnimation(
        parent: completionController,
        curve: Interval(
          0,
          0.1,
          curve: Curves.linearToEaseOut,
        ),
      ),
    );
  }

  @override
  initState() {
    super.initState();

    if (widget.habitCard.coachmarkInfo != null) {
      habitCardKey = GlobalObjectKey("${widget.habitCard.id}-habitCardKey");
    }

    headerBackgroundColor = Colors.white;
    userResponse = widget.habitCard.response;
    lottieController =
        AnimationController(vsync: this, duration: Duration(seconds: 3));
    completionController =
        AnimationController(vsync: this, duration: Duration(seconds: 3));

    setupAnimations();

    completionController.addListener(() {
      if (completionController.value >= 0 &&
          lottieController.status != AnimationStatus.completed) {
        lottieController.forward();
      }
      if (completionController.value >= 0.38 &&
          userResponse == UserResponse.NO_RESPONSE) {
        addBorder = false;
        userResponse = draftUserResponse ?? UserResponse.NO_RESPONSE;
      }
    });

    completionController.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        widget.removeCardCallback(userResponse);

        if (widget.habitCard.coachmarkInfo != null && !shownCoachMark) {
          showCardCoachmark();
        }
      }
    });
  }

  void showCardCoachmark() {
    List<CoachmarkTextInfo>? textItems =
        widget.habitCard.coachmarkInfo?.textSpans;
    if (textItems == null) return;
    //   BlocProvider.of<CoachCLPBloc>(context).add(ProgressCoachmarkShownEvent());
    CoachMark coachMarkSlider = CoachMark();
    RenderBox? target =
        this.habitCardKey!.currentContext?.findRenderObject() as RenderBox;
    Rect markRect = target.localToGlobal(Offset.zero) & target.size;
    coachMarkSlider.show(
        targetContext: this.habitCardKey!.currentContext!,
        markRect: markRect.inflate(Spacings.x1),
        markShape: BoxShape.rectangle,
        children: [
          Positioned(
              top: markRect.bottom + Spacings.x1,
              left: Spacings.x6,
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                        width:
                            (MediaQuery.of(context).size.width - Spacings.x6) -
                                ((MediaQuery.of(context).size.width) -
                                    (markRect.centerRight.dx - Spacings.x4)),
                        height: 80,
                        child: arrow()),
                    Padding(
                        padding: EdgeInsets.only(right: Spacings.x6),
                        child: Container(
                            constraints: BoxConstraints(
                                maxWidth: MediaQuery.of(context).size.width -
                                    Spacings.x6),
                            width: MediaQuery.of(context).size.width,
                            child: RichText(
                              text: TextSpan(
                                text: '',
                                style: AuroraTheme.of(context)
                                    .textStyle(TypescaleValues.P1),
                                children: textItems
                                    .map<TextSpan>((e) => TextSpan(
                                        text: e.title,
                                        style: AuroraTheme.of(context)
                                            .textStyle(
                                                e.typescale ??
                                                    TypescaleValues.H1,
                                                color: HexColor.fromHex(
                                                    e.hexColor))))
                                    .toList(),
                              ),
                            )))
                  ])),
        ],
        duration: Duration.zero,
        onClose: () {});
    setState(() {
      shownCoachMark = true;
    });
  }

  Widget currentCardState() {
    switch (userResponse) {
      case UserResponse.NO_RESPONSE:
        return buildPendingState();
      default:
        return Container(
          height: 30,
        );
    }
  }

  Widget completedState() {
    Widget completionIcon() {
      switch (userResponse) {
        case UserResponse.DONE:
          return Icon(Icons.check, size: 25, color: Colors.white);
        case UserResponse.NOT_DONE:
          return Icon(Icons.circle, size: 12, color: Colors.white);
        default:
          return Icon(Icons.circle, size: 25, color: Colors.transparent);
      }
    }

    Widget leftSwipeWidget() {
      return Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.horizontal(right: Radius.circular(14)),
            color: Colors.redAccent,
          ),
          child: Column(
              mainAxisSize: MainAxisSize.max,
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                    padding: EdgeInsets.all(20),
                    child: Row(
                      children: [
                        Icon(Icons.undo, color: Colors.white),
                        SizedBox(width: 10),
                        Text(
                          "UNDO",
                          style: AuroraTheme.of(context)
                              .textStyle(TypescaleValues.P6),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ))
              ]));
    }

    return SwipeActionCell(
        key: ObjectKey(widget.habitCard.id),
        trailingActions: <SwipeAction>[
          SwipeAction(
              content: leftSwipeWidget(),
              widthSpace: 120,
              onTap: (CompletionHandler handler) async {
                handler(false);
                HabitActionButton button = widget.habitCard.undoAction;
                setState(() {
                  userResponse = UserResponse.NO_RESPONSE;
                  submitAction(button);
                });
              },
              color: Colors.transparent),
        ],
        backgroundColor: Colors.transparent,
        child: GestureDetector(
          onTap: () {
            RepositoryProvider.of<AnalyticsRepository>(context).logWidgetClick(
                extraInfo: {"action": "Calendar Screen opened from Habit Card"},
                widgetInfo: widget.widgetInfo);
            ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
            PerformActionEvent event =
                PerformActionEvent(widget.habitCard.tapAction!);
            actionBloc.add(event);
          },
          child: Container(
              color: Colors.transparent,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        widget.habitCard.imageUrl != null
                            ? Container(
                                margin: EdgeInsets.symmetric(horizontal: 10),
                                width: 60,
                                height: 60,
                                child: CFNetworkImage(
                                  imageUrl: getMediaUrl(widget
                                          .habitCard.imageUrl ??
                                      "image/transform/progress/perfect.png"),
                                  errorWidget: (context, url, error) =>
                                      Icon(Icons.error),
                                ),
                              )
                            : Padding(
                                padding: EdgeInsets.symmetric(horizontal: 20),
                                child: Opacity(
                                    opacity: 1.0, child: completionIcon()),
                              ),
                        Expanded(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  if (widget.habitCard.habitStatusText != null)
                                    Text(
                                      widget.habitCard.habitStatusText ?? "",
                                      textAlign: TextAlign.start,
                                      style: AuroraTheme.of(context).textStyle(
                                        TypescaleValues.P6,
                                        color: getHabitTextColor(
                                            widget.habitCard.habitStatusText ??
                                                ""),
                                      ),
                                    ),
                                  if (widget.habitCard.streakText != null &&
                                      widget.habitCard.habitStatusText != null)
                                    Padding(
                                      padding:
                                          EdgeInsets.symmetric(horizontal: 8),
                                      child: Text(
                                        "•",
                                        textAlign: TextAlign.start,
                                        style: AuroraTheme.of(context)
                                            .textStyle(TypescaleValues.P6),
                                      ),
                                    ),
                                  if (widget.habitCard.streakText != null)
                                    Padding(
                                      padding: const EdgeInsets.only(right: 5),
                                      child: Icon(
                                        Icons.bolt_sharp,
                                        color: Colors.white,
                                        size: 12,
                                      ),
                                    ),
                                  if (widget.habitCard.streakText != null)
                                    Text(
                                      widget.habitCard.streakText ?? "",
                                      textAlign: TextAlign.start,
                                      style: AuroraTheme.of(context)
                                          .textStyle(TypescaleValues.P6),
                                    ),
                                  if ((widget.habitCard.metricsText != null &&
                                          widget.habitCard.habitStatusText !=
                                              null) ||
                                      (widget.habitCard.metricsText != null &&
                                          widget.habitCard.streakText != null))
                                    Padding(
                                      padding:
                                          EdgeInsets.symmetric(horizontal: 8),
                                      child: Text(
                                        "•",
                                        textAlign: TextAlign.start,
                                        style: AuroraTheme.of(context)
                                            .textStyle(TypescaleValues.P6),
                                      ),
                                    ),
                                  if (widget.habitCard.metricsText != null)
                                    Padding(
                                      padding: const EdgeInsets.only(right: 5),
                                      child: Icon(
                                        Icons.check_rounded,
                                        color: Colors.white,
                                        size: 12,
                                      ),
                                    ),
                                  if (widget.habitCard.metricsText != null)
                                    Text(
                                      widget.habitCard.metricsText ?? "",
                                      textAlign: TextAlign.start,
                                      style: AuroraTheme.of(context)
                                          .textStyle(TypescaleValues.P6),
                                    ),
                                ],
                              ),
                              Container(
                                padding: EdgeInsets.only(top: 1),
                                child: Text(
                                  widget.habitCard.subtitle ?? "",
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                  textAlign: TextAlign.start,
                                  style: AuroraTheme.of(context).textStyle(
                                      TypescaleValues.P4,
                                      color: Colors.white),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Padding(
                      padding: EdgeInsets.only(right: 20, left: 5),
                      child: Icon(Icons.arrow_forward_ios,
                          color: Colors.white, size: 20))
                ],
              )),
        ));
  }

  Widget buildCardActions() {
    switch (userResponse) {
      case UserResponse.NO_RESPONSE:
        return Transform.translate(
            offset: Offset(0, cardYPosition.value),
            child: Opacity(
                opacity: cardOpacity.value,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    for (int i = 0;
                        i < widget.habitCard.habitActions.length;
                        i++)
                      ...habitActionWidgets(widget.habitCard.habitActions[i],
                          i != widget.habitCard.habitActions.length - 1)
                  ],
                )));
      default:
        return Container();
    }
  }

  List<Widget> habitActionWidgets(HabitActionButton button, bool addDivider) {
    if (addDivider) {
      return [
        habitActionButton(button),
        Container(
          height: 64,
          width: 0.5,
          color: Colors.white24,
        )
      ];
    } else {
      return [habitActionButton(button)];
    }
  }

  void submitAction(HabitActionButton button) {
    ActionHandler.Action? buttonAction = button.action;
    if (buttonAction?.meta?["status"] != null) {
      UserResponse? response = EnumToString.fromString(
          UserResponse.values, buttonAction?.meta?["status"]);
      if (response != null)
        setState(() {
          draftUserResponse = response;
        });
      if (draftUserResponse == UserResponse.NO_RESPONSE) {
        resetCard();
      } else {
        completionController.forward();
        lottieController.forward();
      }
    }
    switch (draftUserResponse) {
      case UserResponse.NOT_DONE:
        selectedCardAnimation = widget.habitCard.notDoneAnimation;
        break;
      case UserResponse.DONE:
        selectedCardAnimation = widget.habitCard.doneAnimation;
        break;
    }
    if (buttonAction != null) {
      ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
      buttonAction.bloc = BlocProvider.of<UpdateHabitBloc>(context);
      PerformActionEvent event = PerformActionEvent(buttonAction);
      actionBloc.add(event);
      RepositoryProvider.of<AnalyticsRepository>(context).logWidgetClick(
          widgetInfo: widget.widgetInfo,
          extraInfo: {
            "response": EnumToString.convertToString(draftUserResponse)
          });
    }
  }

  Widget habitActionButton(HabitActionButton button) {
    return Expanded(
        child: Container(
            child: Material(
                color: Colors.transparent,
                child: InkWell(
                    splashColor: Colors.white.withOpacity(0.2),
                    child: Padding(
                        padding: EdgeInsets.all(40),
                        child: Icon(
                          button.text == "CANCEL" ? Icons.close : Icons.check,
                          color: Colors.white,
                          size: 30.0,
                        )),
                    onTap: () {
                      submitAction(button);
                    }))));
  }

  Widget buildPendingState() {
    return InkWell(
      onTap: () {},
      child: InkWell(
        onTap: () {
          ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
          PerformActionEvent event =
              PerformActionEvent(widget.habitCard.tapAction!);
          actionBloc.add(event);
        },
        child: Padding(
            padding: EdgeInsets.only(left: 20, right: 20),
            child: Stack(children: [
              Column(
                children: [
                  if (widget.habitCard.streakText != null)
                    Padding(
                      padding: const EdgeInsets.only(bottom: 15),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(right: 5),
                            child: Icon(
                              Icons.bolt_sharp,
                              color: Colors.white,
                              size: 20,
                            ),
                          ),
                          Text(
                            widget.habitCard.streakText ?? "",
                            textAlign: TextAlign.start,
                            style: AuroraTheme.of(context)
                                .textStyle(TypescaleValues.P6),
                          ),
                        ],
                      ),
                    ),
                  Container(
                      decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(20)),
                      child: Padding(
                          padding:
                              EdgeInsets.symmetric(vertical: 5, horizontal: 10),
                          child: Text(
                              widget.habitCard.header
                                      ?.toUpperCase()
                                      .split('_')
                                      .join(" ") ??
                                  "",
                              style: AuroraTheme.of(context).textStyle(
                                  TypescaleValues.TAGTEXT,
                                  color: Colors.white60)))),
                  SizedBox(height: 30),
                  Text(widget.habitCard.title ?? "",
                      textAlign: TextAlign.center,
                      style: AuroraTheme.of(context).textStyle(
                          TypescaleValues.P5,
                          color: Colors.white.withOpacity(0.71))),
                  SizedBox(height: 10),
                  Text(
                    widget.habitCard.subtitle ?? "",
                    style:
                        AuroraTheme.of(context).textStyle(TypescaleValues.H10),
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(height: 28),
                  if (widget.habitCard.description != null)
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.star,
                                color: Colors.white,
                                size: 16.0,
                              ),
                              SizedBox(width: 8),
                              ConstrainedBox(
                                  constraints: BoxConstraints(
                                      maxWidth:
                                          MediaQuery.of(context).size.width /
                                              2),
                                  child: Text(
                                      widget.habitCard.description ?? "",
                                      overflow: TextOverflow.fade,
                                      style: AuroraTheme.of(context)
                                          .textStyle(TypescaleValues.P5)))
                            ])
                      ],
                    ),
                  buildClassView(),
                  SizedBox(height: 80)
                ],
              ),
            ])),
      ),
    );
  }

  Widget buildClassView() {
    switch (widget.habitCard.type) {
      case HabitCardType.LIVE_CLASS:
      case HabitCardType.WOD:
        workoutInfo() {
          return Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Container(
                height: 95,
                width: 95,
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.all(Radius.circular(10))),
                child: CFNetworkImage(
                  width: 95,
                  height: 95,
                  fit: BoxFit.cover,
                  imageUrl: getImageUrl(context,
                      imagePath: widget.habitCard.workoutInfo!.image),
                  errorWidget: (context, url, error) => Icon(Icons.error),
                ),
              ),
              SizedBox(width: 15),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                      width: 150,
                      child: Text(
                        widget.habitCard.workoutInfo!.name ?? "",
                        style: AuroraTheme.of(context)
                            .textStyle(TypescaleValues.H3),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 2,
                      )),
                  if (widget.habitCard.workoutInfo!.duration != null)
                    Container(
                      width: 150,
                      padding: const EdgeInsets.only(top: 5),
                      child: Text(
                        widget.habitCard.workoutInfo!.duration ?? "",
                        style: AuroraTheme.of(context)
                            .textStyle(TypescaleValues.P8),
                      ),
                    ),
                  SizedBox(height: 5),
                  if (!widget.habitCard.workoutInfo!.completed)
                    Container(
                      width: 22,
                      height: 22,
                      decoration: new BoxDecoration(
                          color: Colors.white.withOpacity(0.28),
                          shape: BoxShape.circle),
                      child: Icon(Icons.arrow_forward_ios,
                          color: Colors.white, size: 8),
                    )
                ],
              )
            ],
          );
        }

        return widget.habitCard.workoutInfo != null
            ? Container(
                margin: EdgeInsets.only(top: 50, bottom: 20),
                padding: EdgeInsets.symmetric(horizontal: 20),
                child: widget.habitCard.workoutInfo!.completed
                    ? workoutInfo()
                    : GestureDetector(
                        onTap: () {
                          ActionHandler.Action? action =
                              widget.habitCard.workoutInfo?.action;
                          if (action != null) {
                            ActionBloc actionBloc =
                                BlocProvider.of<ActionBloc>(context);
                            PerformActionEvent event =
                                PerformActionEvent(action);
                            actionBloc.add(event);
                          }
                        },
                        child: workoutInfo()))
            : Container();
      default:
        return Container();
    }
  }

  Widget buildLottieWidget(HabitCardAnimation cardAnimation, bool show) {
    return Positioned(
        left: 0,
        right: 0,
        bottom: 100,
        child: Opacity(
            opacity: show ? lottieOpacity.value : 0,
            child: Opacity(
              opacity: lottieStartOpacity.value,
              child: Transform.translate(
                offset: Offset(0, -40),
                child: SizedBox(
                    height: 250,
                    child: Transform.translate(
                        offset: Offset(0, 100),
                        child: Transform.scale(
                          scale: 1.5,
                          child: Lottie.network(
                              getMediaUrl(cardAnimation.lottieUrl),
                              controller: lottieController,
                              frameRate: FrameRate(60)),
                        ))),
              ),
            )));
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<UpdateHabitBloc, UpdateHabitState>(
        listener: (context, state) {
          if (state is UpdateHabitsLoaded &&
              state.card.id == widget.habitCard.id) {
            if (completionController.isCompleted) {
              userResponse = state.card.response;
            } else {
              AnimationStatusListener statusListener = (status) {
                if (status == AnimationStatus.completed) {
                  userResponse = state.card.response;
                }
              };
              completionController.addStatusListener(statusListener);
            }
          }
        },
        child: AnimatedBuilder(
            animation: completionController,
            builder: (context, child) => Container(
                margin:
                    EdgeInsets.only(bottom: (widget.isLast ? 0 : Spacings.x10)),
                child: Stack(children: [
                  Positioned.fill(
                      key: habitCardKey,
                      child: Stack(children: [
                        BlurView(
                          borderRadius: 14,
                        ),
                        Positioned(
                          left: 0,
                          right: 0,
                          bottom: 0,
                          child: Opacity(
                              opacity: userResponse == UserResponse.NO_RESPONSE
                                  ? cardOpacity.value
                                  : 0,
                              child: Container(
                                decoration: BoxDecoration(
                                    color: Colors.white.withOpacity(0.1),
                                    borderRadius: BorderRadius.only(
                                        bottomLeft: Radius.circular(14.0),
                                        bottomRight: Radius.circular(14.0))),
                                height: 100,
                              )),
                        )
                      ])),
                  Positioned(
                      bottom: 0,
                      left: 0,
                      right: 0,
                      child: Transform.translate(
                        offset: Offset(0, finalTextYPosition.value),
                        child: Opacity(
                            opacity: textReverseOpacity.value,
                            child: Opacity(
                                opacity: textOpacity.value,
                                child: Padding(
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 20, vertical: 10),
                                    child: Text(
                                      selectedCardAnimation?.title ?? "",
                                      textAlign: TextAlign.center,
                                      style: AuroraTheme.of(context)
                                          .textStyle(TypescaleValues.H9),
                                    )))),
                      )),
                  Positioned.fill(
                      child: Opacity(
                          opacity: completionCardOpacity.value,
                          child: completedState())),
                  if (widget.habitCard.doneAnimation != null)
                    buildLottieWidget(
                        widget.habitCard.doneAnimation!,
                        selectedCardAnimation ==
                            widget.habitCard.doneAnimation),
                  if (widget.habitCard.notDoneAnimation != null)
                    buildLottieWidget(
                        widget.habitCard.notDoneAnimation!,
                        selectedCardAnimation ==
                            widget.habitCard.notDoneAnimation),
                  Transform.translate(
                      offset: Offset(0, cardYPosition.value),
                      child: Opacity(
                          opacity: cardOpacity.value,
                          child: Padding(
                              padding: EdgeInsets.symmetric(vertical: 36),
                              child: AnimatedSize(
                                  curve: Curves.easeIn,
                                  duration: Duration(milliseconds: 350),
                                  child: currentCardState())))),
                  Positioned(
                    left: 0,
                    right: 0,
                    bottom: 0,
                    child: Opacity(
                        opacity: userResponse == UserResponse.NO_RESPONSE
                            ? cardOpacity.value
                            : 0,
                        child: Container(
                          height: 100,
                          child: buildCardActions(),
                        )),
                  )
                ]))));
  }

  Color getHabitTextColor(String text) {
    if (text == "PERFECT") {
      return Color.fromRGBO(15, 228, 152, 1);
    } else if (text == "CAN BE IMPROVED") {
      return Color.fromRGBO(255, 89, 66, 1);
    } else if (text == "ON TRACK") {
      return Color.fromRGBO(247, 199, 68, 1);
    }
    return Colors.white;
  }
}

class HabitCardClipper extends CustomClipper<Path> {
  final double borderValue;

  HabitCardClipper(this.borderValue, {required Animation listenable})
      : super(reclip: listenable);

  @override
  Path getClip(Size size) {
    Path path_0 = Path();
    path_0.moveTo(0, 0);

    //  path_0.lineTo(size.width * 0.42, 0);
    // path_0.quadraticBezierTo(
    //     size.width * 0.5, size.height * 0.114, size.width * 0.58, 0);
    path_0.lineTo(size.width, 0);
    path_0.lineTo(size.width, size.height);
    path_0.lineTo(size.width * 0.67, size.height);
    path_0.quadraticBezierTo(size.width * 0.5, size.height * this.borderValue,
        size.width * 0.33, size.height);
    path_0.lineTo(0, size.height);
    path_0.close();
    return path_0;
  }

  @override
  bool shouldReclip(covariant HabitCardClipper oldClipper) {
    return false;
  }
}

class HabitCardPainter extends CustomPainter {
  final bool addBorder;

  HabitCardPainter({
    this.addBorder = true,
    required Animation listenable,
  }) : super(repaint: listenable);

  @override
  void paint(Canvas canvas, Size size) {
    if (addBorder) {
      Paint paint_0 = new Paint()
        ..color = Colors.white.withOpacity(0.5)
        ..style = PaintingStyle.stroke
        ..strokeWidth = 1;
      Path path_0 = Path();
      path_0.moveTo(size.width * 0.04, 0);

      path_0.lineTo(size.width * 0.94, 0);

      path_0.cubicTo(size.width, 0, size.width * 1, size.height * 0.01,
          size.width, size.height * 0.06);

      path_0.lineTo(size.width, size.height * 0.7);

      drawFadingPathForBorder(Offset(size.width, size.height * 0.7),
          Offset(size.width, size.height * 0.75), 0.45, canvas);
      drawFadingPathForBorder(Offset(size.width, size.height * 0.75),
          Offset(size.width, size.height * 0.8), 0.40, canvas);
      drawFadingPathForBorder(Offset(size.width, size.height * 0.8),
          Offset(size.width, size.height * 0.85), 0.35, canvas);

      drawFadingPathForBorder(Offset(size.width, size.height * 0.85),
          Offset(size.width, size.height * 0.9), 0.30, canvas);

      drawFadingPathForBorder(Offset(size.width, size.height * 0.9),
          Offset(size.width, size.height * 0.95), 0.25, canvas);

      path_0.moveTo(0, size.height * 0.7);

      path_0.lineTo(0, size.height * 0.04);
      path_0.quadraticBezierTo(0, 0, size.width * 0.04, 0);
      canvas.drawPath(path_0, paint_0);

      drawFadingPathForBorder(Offset(0, size.height * 0.7),
          Offset(0, size.height * 0.75), 0.45, canvas);
      drawFadingPathForBorder(Offset(0, size.height * 0.75),
          Offset(0, size.height * 0.8), 0.40, canvas);
      drawFadingPathForBorder(Offset(0, size.height * 0.8),
          Offset(0, size.height * 0.85), 0.35, canvas);

      drawFadingPathForBorder(Offset(0, size.height * 0.85),
          Offset(0, size.height * 0.9), 0.30, canvas);

      drawFadingPathForBorder(Offset(0, size.height * 0.9),
          Offset(0, size.height * 0.95), 0.25, canvas);
    }
  }

  drawFadingPathForBorder(
      Offset start, Offset end, double opacity, Canvas canvas) {
    Paint paint_2 = new Paint()
      ..color = Colors.white.withOpacity(opacity)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;
    Path path_2 = Path();
    path_2.moveTo(start.dx, start.dy);
    path_2.lineTo(end.dx, end.dy);
    canvas.drawPath(path_2, paint_2);
  }

  @override
  bool shouldRepaint(covariant HabitCardPainter oldDelegate) {
    return false;
  }
}

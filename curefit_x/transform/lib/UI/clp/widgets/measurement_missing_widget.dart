import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/user_progress/models.dart';
import 'package:common/util/theme.dart';

class MeasurementMissingWidgetView extends StatelessWidget {
  final MeasurementMissingWidgetData measurementMissingWidgetData;

  const MeasurementMissingWidgetView(this.measurementMissingWidgetData);

  @override
  Widget build(BuildContext context) {
    return Container(
        padding: EdgeInsets.symmetric(horizontal: 20),
        child: Container(
            margin: EdgeInsets.symmetric(vertical: 20),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(15)),
                color: Colors.white.withOpacity(0.1)),
            padding: const EdgeInsets.all(20.0),
            child:
                Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
              Text(measurementMissingWidgetData.title,
                  style: AuroraTheme.of(context).textStyle(TypescaleValues.H1)),
              SizedBox(height: 12),
              Row(children: [
                Icon(
                  Icons.warning,
                  color: Colors.white,
                  size: 32.0,
                ),
                SizedBox(width: 30),
                Flexible(
                    child: Text(measurementMissingWidgetData.description,
                        style: AuroraTheme.of(context).textStyle(TypescaleValues.P5))),
              ]),
              SizedBox(height: 22),
              Container(
                  alignment: Alignment.centerRight,
                  child: GestureDetector(
                    child: Container(
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.all(Radius.circular(5)),
                          color: Colors.white.withOpacity(0.1)),
                      child: Padding(
                        padding:
                            EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                        child: Text(
                            measurementMissingWidgetData.action.title ??
                                "Input measurement",
                            style: AuroraTheme.of(context).textStyle(TypescaleValues.P5)),
                      ),
                    ),
                    onTap: () {
                      ActionBloc actionBloc =
                          BlocProvider.of<ActionBloc>(context);
                      PerformActionEvent event = PerformActionEvent(
                          measurementMissingWidgetData.action);
                      actionBloc.add(event);
                    },
                  ))
            ])));
  }
}

import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/components/testimonials_widget.dart';
import 'package:common/ui/header/card_header.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:common/ui/molecules/bottom_tray.dart';
import 'package:common/ui/progress_indicator_carousel.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/util/util.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';

class TransformTestimonialsV2Widget extends StatelessWidget {
  final TestimonialsWidgetData widgetData;

  const TransformTestimonialsV2Widget({required this.widgetData});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: Spacings.x4),
      child: Column(
        children: [
          if (widgetData.widgetHeaderData != null)
            Padding(
              padding: const EdgeInsets.only(bottom: Spacings.x4),
              child: WidgetHeader(
                cardHeaderData: widgetData.widgetHeaderData!,
              ),
            ),
          ProgesssIndicatorCarousel(
            autoPlay: widgetData.autoPlay,
            carouselOptions: CarouselOptions(),
            aspectRatio: widgetData.aspectRatio,
            allowTapToNext: false,
            showProgressBar: widgetData.showProgressBar,
            items: widgetData.testimonials
                .map<Widget>((testimonial) => TestimonialCard(
                      testimonial: testimonial,
                      showQuotes: widgetData.showQuotes,
                    ))
                .toList(),
          ),
        ],
      ),
    );
  }
}

class TestimonialCard extends StatelessWidget {
  final Testimonial testimonial;
  final bool showQuotes;

  const TestimonialCard({required this.testimonial, this.showQuotes = true});

  @override
  Widget build(BuildContext context) {
    AuroraThemeData themeData = AuroraTheme.of(context);
    int descriptionLength = 0;
    descriptionLength = testimonial.text?.length ?? 0;
    return Stack(
      children: [
        Positioned.fill(
          child: Align(
            alignment: Alignment.center,
            child: Image.asset(
              'assets/quotes_icon_opaque.png',
              width: scale(context, 70),
              height: scale(context, 70),
            ),
          ),
        ),
        Container(
          child: Column(
            children: [
              if (testimonial.imageUrl != null)
                ClipRRect(
                  borderRadius: BorderRadius.circular(scale(context, 30)),
                  child: CFNetworkImage(
                    imageUrl: getImageUrl(context,
                        imagePath: testimonial.imageUrl, width: 64),
                    fit: BoxFit.cover,
                    width: scale(context, 60),
                    height: scale(context, 60),
                    errorWidget: (context, url, error) =>
                        const Icon(Icons.error),
                  ),
                ),
              if (testimonial.title != null)
                Padding(
                  padding: const EdgeInsets.only(bottom: Spacings.x1),
                  child: Text(testimonial.title ?? "",
                      style: themeData.textStyle(TypescaleValues.H4,
                          color: Colors.white)),
                ),
              if (testimonial.subTitle != null)
                Padding(
                  padding: const EdgeInsets.only(bottom: Spacings.x10),
                  child: Text(testimonial.subTitle ?? "",
                      style: themeData.textStyle(TypescaleValues.P8,
                          color: Colors.white60)),
                ),
              if (testimonial.text != null && descriptionLength > 0)
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: Spacings.x8),
                  child: RichText(
                    textAlign: TextAlign.center,
                    text: TextSpan(children: [
                      TextSpan(
                        text: descriptionLength > 160
                            ? testimonial.text?.substring(0, 159)
                            : testimonial.text,
                        style: themeData.textStyle(TypescaleValues.P9,
                            color: Colors.white.withOpacity(0.6)),
                      ),
                      TextSpan(
                          text: descriptionLength > 160 ? '... more' : '',
                          recognizer: TapGestureRecognizer()
                            ..onTap = () {
                              onPress(context, themeData);
                            })
                    ]),
                  ),
                ),
            ],
          ),
        ),
      ],
    );
  }

  onPress(BuildContext context, AuroraThemeData themeData) {
    showBottomTray(
        context: context,
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.only(
                  top: 10, left: 20, right: 20, bottom: 40),
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Align(
                        alignment: Alignment.center,
                        child: renderModalNotch(
                            Colors.white.withOpacity(0.3), context)),
                    if (testimonial.title != null)
                      Padding(
                        padding: const EdgeInsets.only(top: Spacings.x3),
                        child: Text(testimonial.title ?? "",
                            style: themeData.textStyle(TypescaleValues.P5,
                                color: Colors.white.withOpacity(0.6))),
                      ),
                    if (testimonial.subTitle != null)
                      Padding(
                        padding: const EdgeInsets.only(top: Spacings.x1),
                        child: Text(testimonial.subTitle ?? "",
                            style: themeData.textStyle(
                              TypescaleValues.H2,
                            )),
                      ),
                    if (testimonial.text != null)
                      Padding(
                        padding:
                            const EdgeInsets.symmetric(vertical: Spacings.x2),
                        child: Divider(
                          color: Colors.white.withOpacity(0.1),
                          thickness: 1,
                        ),
                      ),
                    if (testimonial.text != null)
                      Text(testimonial.text ?? "",
                          style: themeData.textStyle(TypescaleValues.P9,
                              color: Colors.white.withOpacity(0.6))),
                  ]),
            ),
          ],
        ));
  }
}

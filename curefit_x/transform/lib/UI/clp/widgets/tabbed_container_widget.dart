import 'package:common/action/action_handler.dart';
import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/widget_factory.dart';
import 'package:common/util/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:transform/UI/progress/widgets/habits_progress_widget.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:transform/blocs/user_progress/events.dart';
import 'package:transform/blocs/user_progress/models.dart';
import 'package:transform/blocs/user_progress/user_progress_bloc.dart';

class ProgressScreenArguments {
  String? initialTab;

  ProgressScreenArguments(Map<String, dynamic> payload) {
    this.initialTab = payload["initialTab"];
  }
}

class TabbedContainerWidget extends StatefulWidget {
  final TabbedContainerWidgetData tabbedContainerWidgetData;

  TabbedContainerWidget(this.tabbedContainerWidgetData);

  @override
  State<TabbedContainerWidget> createState() => _TabbedContainerWidgetState();
}

class _TabbedContainerWidgetState extends State<TabbedContainerWidget> {
  late int updateAlertBoxCount;

  ProgressScreenArguments? progressScreenArguments(BuildContext context) {
    final ScreenArguments? args =
        ModalRoute.of(context)!.settings.arguments as ScreenArguments?;
    if (args != null) {
      return ProgressScreenArguments(args.params);
    }
    return null;
  }

  @override
  void initState() {
    updateAlertBoxCount = 0;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    AuroraThemeData themeData = AuroraTheme.of(context);
    int initialTab = 0;
    ProgressScreenArguments? arguments = progressScreenArguments(context);
    if (arguments != null && arguments.initialTab != null) {
      initialTab = int.parse(arguments.initialTab!);
      if (initialTab >= widget.tabbedContainerWidgetData.tabs.length) {
        initialTab = 0;
      }
    }
    WidgetFactory widgetFactory = RepositoryProvider.of<WidgetFactory>(context);
    double width = MediaQuery.of(context).size.width;
    int totalTabCharacters = 0;
    for (String tab in widget.tabbedContainerWidgetData.tabs) {
      totalTabCharacters += tab.length;
    }
    return SafeArea(
        bottom: false,
        child: DefaultTabController(
            length: widget.tabbedContainerWidgetData.tabs.length,
            // length of tabs
            initialIndex: initialTab,
            child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: <Widget>[
                  Container(
                    decoration: BoxDecoration(
                      border: Border(
                        bottom: BorderSide(color: Colors.white, width: 0.1),
                      ),
                    ),
                    child: TabBar(
                      tabAlignment: TabAlignment.start,
                      dividerHeight: 0,
                      indicatorSize: TabBarIndicatorSize.label,
                      indicatorColor: Colors.white,
                      indicatorWeight: 4,
                      isScrollable: true,
                      labelColor: Colors.white,
                      unselectedLabelStyle:
                          themeData.textStyle(TypescaleValues.P5),
                      labelStyle: themeData.textStyle(TypescaleValues.P3),
                      padding: EdgeInsets.symmetric(horizontal: 16),
                      labelPadding: EdgeInsets.symmetric(horizontal: 5),
                      unselectedLabelColor: Colors.white.withOpacity(0.7),
                      onTap: (int) {
                        RepositoryProvider.of<AnalyticsRepository>(context)
                            .logWidgetClick(
                                extraInfo: {
                              "tabSelected":
                                  widget.tabbedContainerWidgetData.tabs[int],
                            },
                                widgetInfo: widget
                                    .tabbedContainerWidgetData.widgetInfo);
                      },
                      tabs: [
                        for (String tab
                            in widget.tabbedContainerWidgetData.tabs)
                          Container(
                            margin: EdgeInsets.only(left: 10, right: 10),
                            // width: ((width - 40) * tab.length/totalTabCharacters) - 10,
                            child: Tab(
                              child: Text(tab),
                            ),
                          ),
                      ],
                    ),
                  ),
                  Expanded(
                      child: TabBarView(children: <Widget>[
                    for (var widgets
                        in widget.tabbedContainerWidgetData.widgets)
                      AnimationLimiter(
                        child: ListView(children: [
                          (widget.tabbedContainerWidgetData.showAlertBox ??
                                      false) &&
                                  (widgets[0]['widgetType'].toString() ==
                                      "USER_METRIC_CHART_WIDGET") &&
                                  (updateAlertBoxCount == 0)
                              ? Padding(
                                  padding: const EdgeInsets.only(
                                      left: 10, right: 10, top: 30),
                                  child: BlurView(
                                    child: Container(
                                      padding: EdgeInsets.all(20),
                                      height: 150,
                                      child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceAround,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.center,
                                        children: [
                                          Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceAround,
                                            children: [
                                              Container(
                                                child: Icon(
                                                  Icons.warning_rounded,
                                                  color: Colors.white,
                                                  size: 35,
                                                ),
                                              ),
                                              SizedBox(
                                                width: width * 0.04,
                                              ),
                                              Container(
                                                alignment: Alignment.center,
                                                width: width * 0.65,
                                                child: Text(
                                                  widget.tabbedContainerWidgetData
                                                          .measurementUpdateTitle ??
                                                      "",
                                                  style: AuroraTheme.of(context)
                                                      .textStyle(
                                                    TypescaleValues.P5,
                                                    color: Colors.white
                                                        .withOpacity(0.54),
                                                  ),
                                                  //textAlign: TextAlign.center,
                                                ),
                                              )
                                            ],
                                          ),
                                          Container(
                                            width: width * 0.40,
                                            decoration: BoxDecoration(
                                              borderRadius: BorderRadius.all(
                                                  Radius.circular(5)),
                                              color:
                                                  Colors.white.withOpacity(0.2),
                                            ),
                                            alignment: Alignment.center,
                                            child: TextButton(
                                              onPressed: () {
                                                if (widget
                                                        .tabbedContainerWidgetData
                                                        .action !=
                                                    null) {
                                                  ActionBloc actionBloc =
                                                      BlocProvider.of<
                                                          ActionBloc>(context);
                                                  PerformActionEvent event =
                                                      PerformActionEvent(widget
                                                          .tabbedContainerWidgetData
                                                          .action!);
                                                  actionBloc.add(event);
                                                  setState(() {
                                                    updateAlertBoxCount = 1;
                                                  });
                                                }
                                              },
                                              child: Text(
                                                widget.tabbedContainerWidgetData
                                                        .updateButtonTitle ??
                                                    "",
                                                style: AuroraTheme.of(context)
                                                    .textStyle(
                                                        TypescaleValues.P3),
                                                textAlign: TextAlign.center,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                )
                              : Container(),
                          ...applyStaggeredAnimation(
                              widgetFactory.createWidgets(widgets), context)
                        ]),
                      )
                  ]))
                ])));
  }
}

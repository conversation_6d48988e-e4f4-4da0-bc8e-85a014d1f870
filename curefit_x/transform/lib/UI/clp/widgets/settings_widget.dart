import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/atoms/secondary_button.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/molecules/listview_action_button.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/clp/models.dart';
import 'package:transform/blocs/settings/events.dart';
import 'package:transform/blocs/settings/models.dart';
import 'package:transform/blocs/settings/settings_bloc.dart';
import 'package:transform/blocs/settings/state.dart';
import 'package:common/action/action_handler.dart' as ActionHandler;

class SettingsWidget extends StatefulWidget {
  final SettingsWidgetData settingsWidgetData;

  const SettingsWidget(this.settingsWidgetData, {Key? key}) : super(key: key);

  @override
  State<SettingsWidget> createState() => _SettingsWidgetState();
}

class _SettingsWidgetState extends State<SettingsWidget> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: Spacings.x4),
      child: ListViewActionButton(
          data: [
            ListViewActionButtonData(
              title: widget.settingsWidgetData.title ?? "",
              icon: Icons.notifications_outlined ,
            )
          ],
          onPress: (ActionHandler.Action? action, int index) {
            if (widget.settingsWidgetData.showModal ?? false) {
              final settingsBloc = BlocProvider.of<SettingsBloc>(context);
              settingsBloc.add(SettingsLoadEvent());
              showSettingsModalScreen(context);
            }
          }),
    );
  }
  dynamic showSettingsModalScreen(BuildContext context) {
    return showModalBottomSheet<void>(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      isScrollControlled: true,
      context: context,
      builder: (context) {
        return BlurView(
          borderRadius: 20,
          child: FractionallySizedBox(
            heightFactor: 0.45,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
                color: Color.fromRGBO(45, 54, 54, 0.8),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: BlocBuilder<SettingsBloc, SettingsState>(
                builder: (context, state) {
                  if (state is SettingsLoading) {
                    return Center(
                      child: CircularProgressIndicator(),
                    );
                  }
                  if (state is SettingsFailed) {
                    return getSubmitStateWidget(false);
                  }
                  if (state is SettingsSuccess) {
                    return getSubmitStateWidget(true);
                  }
                  if (state is SettingsLoaded) {
                    return getLoadedStateWidget(state.settingsModalScreenData);
                  }
                  return Container();
                },
              ),
            ),
          ),
        );
      },
    );
  }

  Widget getSubmitStateWidget(bool success) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            height: 70,
            width: 70,
            decoration: BoxDecoration(
              color: Color.fromRGBO(61, 113, 117, 1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              success ? Icons.check_outlined : Icons.cancel_outlined,
              size: 30,
              color: Color.fromRGBO(15, 228, 152, 1),
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(vertical: 20),
            width: 200,
            child: Text(
              success
                  ? "We have saved your preference! To change it, you can either reach out to your coach or go to Notification Settings on the Cult.fit app"
                  : "Preference Submission Failed",
              textAlign: TextAlign.center,
              style: AuroraTheme.of(context).textStyle(TypescaleValues.P4),
            ),
          ),
        ],
      ),
    );
  }

  Widget getLoadedStateWidget(SettingsModalScreenData settingsModalScreenData) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      mainAxisSize: MainAxisSize.min,
      children: [
        Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Container(
              margin: const EdgeInsets.symmetric(vertical: 10),
              height: 2.5,
              width: 55,
              alignment: Alignment.topCenter,
              color: Colors.white.withOpacity(0.5),
            ),
            Padding(
              padding: const EdgeInsets.only(top: 30),
              child: Text(
                settingsModalScreenData.title ?? "",
                style: AuroraTheme.of(context).textStyle(
                  TypescaleValues.H2,
                  color: Colors.white,
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 10),
              child: Text(
                settingsModalScreenData.subtitle ?? "",
                style: AuroraTheme.of(context)
                    .textStyle(TypescaleValues.P5, color: Colors.white),
              ),
            ),
          ],
        ),
        Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Padding(
              padding: const EdgeInsets.only(
                bottom: 20,
              ),
              child: Text(
                settingsModalScreenData.description ?? "",
                style: AuroraTheme.of(context)
                    .textStyle(TypescaleValues.P8, color: Colors.white),
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Container(
                  width: scale(context, 160),
                  child: SecondaryButton(
                    () {
                      final settingsBloc =
                          BlocProvider.of<SettingsBloc>(context);
                      settingsBloc.add(SettingsSubmitEvent(preference: false));
                    },
                    settingsModalScreenData.buttonTitles!.first ?? "",
                    height: 50,
                  ),
                ),
                Container(
                  height: 50,
                  width: scale(context, 160),
                  child: PrimaryButton(
                    () {
                      final settingsBloc =
                          BlocProvider.of<SettingsBloc>(context);
                      settingsBloc.add(SettingsSubmitEvent(preference: true));
                    },
                    settingsModalScreenData.buttonTitles!.last ?? "",
                  ),
                ),
              ],
            ),
            SizedBox(
              height: 20,
            ),
          ],
        ),
      ],
    );
  }
}

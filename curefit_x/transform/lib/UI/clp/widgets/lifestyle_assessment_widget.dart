import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/atoms/secondary_button.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/header/card_header.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/util/action_util.dart';
import 'package:flutter/material.dart' hide Action;
import 'package:transform/UI/progress/components/lifestyle_water_animation.dart';
import 'package:transform/blocs/lifestyle_assessment/models.dart';

class LifestyleAssessmentWidget extends StatefulWidget {
  final LifestyleAssessmentWidgetData widgetData;

  const LifestyleAssessmentWidget({required this.widgetData, Key? key})
      : super(key: key);

  @override
  State<LifestyleAssessmentWidget> createState() =>
      _LifestyleAssessmentWidgetState();
}

class _LifestyleAssessmentWidgetState extends State<LifestyleAssessmentWidget>
    with TickerProviderStateMixin {
  late AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: Spacings.x4),
      child: Column(
        children: [
          if (widget.widgetData.headerData != null)
            WidgetHeader(
              cardHeaderData: widget.widgetData.headerData!,
            ),
          if (widget.widgetData.headerImage != null)
            CFNetworkImage(
              fit: BoxFit.fill,
              imageUrl: getImageUrl(
                context,
                imagePath: widget.widgetData.headerImage ?? "",
              ),
              height: scale(context, widget.widgetData.imageHeight ?? 80),
              width: scale(context, widget.widgetData.imageWidth ?? 335),
            ),
          SizedBox(
            height: Spacings.x2,
          ),
          if (widget.widgetData.image != null &&
              !widget.widgetData.showWaterView)
            CFNetworkImage(
              fit: BoxFit.fill,
              imageUrl: getImageUrl(
                context,
                imagePath: widget.widgetData.image ?? "",
              ),
              width: scale(context, 335),
              height: scale(context, 220),
            ),
          if (widget.widgetData.action != null &&
              !widget.widgetData.showWaterView)
            Padding(
              padding: const EdgeInsets.only(top: Spacings.x2),
              child: SecondaryButton(
                () {
                  clickActionWithAnalytics(widget.widgetData.action!, context,
                      widget.widgetData.widgetInfo, {});
                },
                widget.widgetData.action!.title ?? "",
                expanded: false,
              ),
            ),
          if (widget.widgetData.showWaterView &&
              widget.widgetData.waterLevelAnimationView != null)
            BlurView(
              child: Center(
                child: Column(
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(top: Spacings.x4),
                      child: LifestyleWaterAnimation(
                        mainScreenAnimationController: _animationController,
                        configData: widget.widgetData.waterLevelAnimationView,
                      ),
                    ),
                    if (widget.widgetData.action != null)
                      Padding(
                        padding:
                            const EdgeInsets.symmetric(vertical: Spacings.x4),
                        child: SecondaryButton(
                          () {
                            clickActionWithAnalytics(widget.widgetData.action!,
                                context, widget.widgetData.widgetInfo, {});
                          },
                          widget.widgetData.action!.title ?? "",
                          expanded: false,
                        ),
                      ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }
}

import 'dart:async';

import 'package:common/analytics/analytics_repository.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/util/action_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:transform/UI/clp/screens/input_confirmation.dart';
import 'package:transform/blocs/metric_input/models.dart';
import 'dart:math' as math;
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/constants/action_constants.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/action/action_handler.dart' as ActionHandler;

class UserMetricInputView extends StatefulWidget {
  final UserMetricInputWidgetData userMetricInputWidgetData;

  const UserMetricInputView(this.userMetricInputWidgetData);

  @override
  UserMetricInputViewState createState() {
    return UserMetricInputViewState(userMetricInputWidgetData);
  }
}

class UserMetricInputViewState extends State<UserMetricInputView> {
  final UserMetricInputWidgetData userMetricInputWidgetData;
  final List<TextEditingController> textControllers = [];
  final List<bool> showUnitIncrement = [];

  UserMetricInputViewState(this.userMetricInputWidgetData) {
    for (int i = 0; i < userMetricInputWidgetData.metricCards.length; i++) {
      TextEditingController controller = new TextEditingController();
      controller.text = userMetricInputWidgetData.metricCards[i].value == 0
          ? "-"
          : userMetricInputWidgetData.metricCards[i].value.toString();
      textControllers.add(controller);
      showUnitIncrement.add(controller.text == "_" ? false : true);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        SingleChildScrollView(
          child: Column(
            children: [
              for (int i = 0;
                  i < userMetricInputWidgetData.metricCards.length;
                  i++)
                MetricInputCardView(userMetricInputWidgetData.metricCards[i],
                    textControllers[i], showUnitIncrement[i]),
              SizedBox(
                height: 100,
              ),
            ],
          ),
        ),
        Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              padding: EdgeInsets.only(bottom: 30),
              child: PrimaryButton(() {
                List<dynamic> updates = [];
                for (int i = 0; i < textControllers.length; i++) {
                  TextEditingController textController = textControllers[i];
                  try {
                    double val = double.parse(textController.text);
                    if (val <= 0) continue;
                    updates.add({
                      "metricId":
                          userMetricInputWidgetData.metricCards[i].metricId,
                      "value": val
                    });
                  } catch (e) {
                    print(e);
                  }
                }
                if (updates.isNotEmpty) {
                  ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
                  PerformActionEvent event = PerformActionEvent(
                      new ActionHandler.Action(
                          type: ActionTypes.UPDATE_USER_METRICS,
                          meta: {
                        "updates": updates,
                        "subCategoryCode":
                            widget.userMetricInputWidgetData.subCategoryCode ??
                                ""
                      }));
                  RepositoryProvider.of<AnalyticsRepository>(context)
                      .logWidgetClick(extraInfo: {
                    "action": "Metrics update",
                    "metricType": updates
                  }, widgetInfo: userMetricInputWidgetData.widgetInfo);
                  actionBloc.add(event);
                  var metricValue = updates.firstWhere((e) {
                    num metricId = e["metricId"];
                    return widget.userMetricInputWidgetData.metricIdToCompare !=
                            null &&
                        metricId ==
                            widget.userMetricInputWidgetData.metricIdToCompare;
                  }, orElse: () => null);
                  bool isDecreaseInMetricValue = metricValue != null &&
                      widget.userMetricInputWidgetData.currentMetricIdValue !=
                          null &&
                      metricValue['value'] <
                          widget.userMetricInputWidgetData.currentMetricIdValue;
                  if (widget.userMetricInputWidgetData.referralModalEnabled &&
                      widget.userMetricInputWidgetData.action != null &&
                      isDecreaseInMetricValue) {
                    clickActionWithAnalytics(
                        widget.userMetricInputWidgetData.action!,
                        context,
                        widget.userMetricInputWidgetData.widgetInfo,
                        {"openReferralModal": true});
                  } else if (widget.userMetricInputWidgetData.confirmationAction != null){
                    clickActionWithAnalytics(
                        widget.userMetricInputWidgetData.confirmationAction!,
                        context,
                        widget.userMetricInputWidgetData.widgetInfo,
                        {"confirmationPage": true});
                  }
                   else {
                    StreamController<bool> screenController =
                        StreamController<bool>();
                    Navigator.pushReplacement(
                        context,
                        MaterialPageRoute(
                          builder: (BuildContext context) =>
                              InputConfirmationScreen(screenController),
                        )).then((value) => screenController.add(true));
                  }
                } else {
                  // Show prompt no change made
                }
              }, 'SAVE'),
            )),
      ],
    );
  }
}

class MetricInputCardView extends StatefulWidget {
  final MeasurementInputCard measurementInputCard;
  final TextEditingController textController;
  final bool showUnitIncrement;

  const MetricInputCardView(
      this.measurementInputCard, this.textController, this.showUnitIncrement);

  @override
  MetricInputCardViewState createState() {
    return MetricInputCardViewState(
        this.measurementInputCard, this.textController);
  }
}

class MetricInputCardViewState extends State<MetricInputCardView> {
  final MeasurementInputCard measurementInputCard;
  final TextEditingController textController;
  final FocusNode _focus = new FocusNode();

  @override
  void initState() {
    super.initState();
    _focus.addListener(_onFocusChange);
  }

  void _onFocusChange() {
    if (_focus.hasFocus) {
      if (textController.text == "-") {
        textController.text = "";
      }
    } else {
      if (textController.text == "") {
        textController.text = "-";
      }
    }
  }

  MetricInputCardViewState(this.measurementInputCard, this.textController);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
        width: double.infinity,
        child: Container(
          margin: const EdgeInsets.only(top: 20),
          padding: const EdgeInsets.all(20.0),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.all(Radius.circular(10)),
            color: Colors.white.withOpacity(0.1),
            border: Border.all(width: 1, color: Colors.white.withOpacity(0.6)),
          ),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(measurementInputCard.title,
                      style: AuroraTheme.of(context)
                          .textStyle(TypescaleValues.P5)),
                  measurementInputCard.info != null
                      ? GestureDetector(
                          child: Icon(
                            Icons.info_outline,
                            color: Colors.white,
                            size: 20,
                          ),
                          onTap: () {
                            showModalBottomSheet<void>(
                              context: context,
                              builder: (BuildContext context) {
                                return Container(
                                  height: 400,
                                  padding: EdgeInsets.all(20),
                                  alignment: Alignment.topCenter,
                                  decoration: BoxDecoration(
                                      color: Colors.black,
                                      borderRadius: BorderRadius.only(
                                          topLeft: Radius.circular(15),
                                          topRight: Radius.circular(15))),
                                  child: SingleChildScrollView(
                                      child: Column(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: <Widget>[
                                      Stack(
                                        children: [
                                          Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                Text(
                                                    measurementInputCard
                                                        .info!.title,
                                                    style: AuroraTheme.of(
                                                            context)
                                                        .textStyle(
                                                            TypescaleValues.P4))
                                              ]),
                                          Row(
                                            children: [
                                              GestureDetector(
                                                child: Icon(
                                                  Icons.arrow_back_ios,
                                                  color: Colors.white,
                                                  size: 20,
                                                ),
                                                onTap: () {
                                                  Navigator.pop(context);
                                                },
                                              )
                                            ],
                                          )
                                        ],
                                      ),
                                      SizedBox(height: 20),
                                      CFNetworkImage(
                                          fit: BoxFit.cover,
                                          errorWidget: (context, url, error) =>
                                              Container(color: Colors.black38),
                                          imageUrl: getImageUrl(context,
                                              imagePath: measurementInputCard
                                                  .info!.image),
                                          height: 182),
                                      SizedBox(height: 20),
                                      Text(measurementInputCard.info!.text,
                                          style: AuroraTheme.of(context)
                                              .textStyle(TypescaleValues.P5))
                                    ],
                                  )),
                                );
                              },
                            );
                          },
                        )
                      : Container()
                ],
              ),
              SizedBox(height: 10),
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      ConstrainedBox(
                        constraints:
                            BoxConstraints(minWidth: 48, maxWidth: 150),
                        child: IntrinsicWidth(
                            child: TextField(
                          focusNode: _focus,
                          textInputAction: TextInputAction.next,
                          decoration: InputDecoration(
                            enabledBorder: UnderlineInputBorder(
                              borderSide: BorderSide(color: Colors.white),
                            ),
                            focusedBorder: UnderlineInputBorder(
                              borderSide: BorderSide(color: Colors.white),
                            ),
                            border: UnderlineInputBorder(
                              borderSide: BorderSide(color: Colors.white),
                            ),
                          ),
                          style: AuroraTheme.of(context)
                              .textStyle(TypescaleValues.H7),
                          controller: textController,
                          inputFormatters: [
                            DecimalTextInputFormatter(
                                decimalRange: measurementInputCard.precision)
                          ],
                        )),
                      ),
                      SizedBox(width: 6),
                      Text(measurementInputCard.unit,
                          style: AuroraTheme.of(context).textStyle(
                              TypescaleValues.P2,
                              color: Colors.white.withOpacity(0.6)))
                    ],
                  ),
                  widget.showUnitIncrement
                      ? Row(
                          children: [
                            GestureDetector(
                              child: Container(
                                width: 40,
                                height: 40,
                                margin: EdgeInsets.only(right: 15),
                                decoration: new BoxDecoration(
                                    color: Colors.white.withOpacity(0.28),
                                    shape: BoxShape.circle),
                                child: Icon(Icons.remove, color: Colors.white),
                              ),
                              onTap: () {
                                double val = 0;
                                if (textController.text != "" &&
                                    textController.text != "-") {
                                  val = double.parse(textController.text);
                                }
                                textController.text =
                                    (math.max(0, val - 0.5)).toString();
                              },
                            ),
                            GestureDetector(
                              child: Container(
                                width: 40,
                                height: 40,
                                decoration: new BoxDecoration(
                                    color: Colors.white.withOpacity(0.28),
                                    shape: BoxShape.circle),
                                child: Icon(Icons.add, color: Colors.white),
                              ),
                              onTap: () {
                                double val = 0;
                                if (textController.text != "" &&
                                    textController.text != "-") {
                                  val = double.parse(textController.text);
                                }
                                textController.text = (val + 0.5).toString();
                              },
                            )
                          ],
                        )
                      : Container()
                ],
              )
            ],
          ),
        ));
  }
}

class DecimalTextInputFormatter extends TextInputFormatter {
  DecimalTextInputFormatter({this.decimalRange})
      : assert(decimalRange == null || decimalRange > 0);

  final int? decimalRange;

  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue, // unused.
    TextEditingValue newValue,
  ) {
    TextSelection newSelection = newValue.selection;
    String truncated = newValue.text;
    try {
      if (truncated == "") {
        newSelection = newValue.selection.copyWith(
          baseOffset: math.min(truncated.length, truncated.length + 1),
          extentOffset: math.min(truncated.length, truncated.length + 1),
        );
        return TextEditingValue(
          text: truncated,
          selection: newSelection,
          composing: TextRange.empty,
        );
      }
      double val = double.parse(truncated);
      if (val < 0) return oldValue;
    } catch (e) {
      return oldValue;
    }

    if (decimalRange != null) {
      String value = newValue.text;

      if (value.contains(".") &&
          value.substring(value.indexOf(".") + 1).length > decimalRange!) {
        truncated = oldValue.text;
        newSelection = oldValue.selection;
      } else if (value == ".") {
        truncated = "0.";

        newSelection = newValue.selection.copyWith(
          baseOffset: math.min(truncated.length, truncated.length + 1),
          extentOffset: math.min(truncated.length, truncated.length + 1),
        );
      }

      return TextEditingValue(
        text: truncated,
        selection: newSelection,
        composing: TextRange.empty,
      );
    }
    return newValue;
  }
}

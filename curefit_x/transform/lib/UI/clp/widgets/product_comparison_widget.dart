import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/header/card_header.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/util/action_util.dart';
import 'package:flutter/material.dart';
import 'package:transform/blocs/clp/models.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ProductComparisonWidget extends StatelessWidget {
  final ProductComparisonWidgetData widgetData;

  const ProductComparisonWidget({required this.widgetData, Key? key})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: Spacings.x2),
      child: Column(
        children: [
          if (widgetData.header != null)
            Padding(
              padding: EdgeInsets.only(bottom: Spacings.x6),
              child: WidgetHeader(
                cardHeaderData: widgetData.header!,
              ),
            ),
          if (widgetData.data != null)
            Table(
              columnWidths: widgetData.numberOfColumns != 0
                  ? {
                      for (int column = 0;
                          column < widgetData.numberOfColumns;
                          column++)
                        column: FlexColumnWidth(column == 0 ? 1.3 : 1.0),
                    }
                  : null,
              border: TableBorder(
                verticalInside: BorderSide(color: Colors.white24, width: 1),
              ),
              children: [
                for (ProductComparisonWidgetRowItem rowData in widgetData.data!)
                  if (rowData.rowItems != null)
                    TableRow(
                      children: [
                        for (RowItems rowItems in rowData.rowItems!)
                          getTableCell(context, rowItems, rowData.isHeaderRow,
                              rowData.rowItems!.indexOf(rowItems) == 0)
                      ],
                    ),
              ],
            ),
          if (widgetData.footerData != null)
            Table(
              columnWidths: widgetData.numberOfColumns != 0
                  ? {
                      for (int column = 0;
                          column < widgetData.numberOfColumns;
                          column++)
                        column: FlexColumnWidth(column == 0 ? 1.3 : 1.0),
                    }
                  : null,
              children: [
                for (ProductComparisonWidgetRowItem rowData
                    in widgetData.footerData!)
                  if (rowData.rowItems != null)
                    TableRow(
                      children: [
                        for (RowItems rowItems in rowData.rowItems!)
                          getFooterTableCell(context, rowItems)
                      ],
                    ),
              ],
            ),
        ],
      ),
    );
  }

  Widget getTableCell(
      BuildContext context, RowItems data, bool isHeader, bool isFirstColumn) {
    return InkWell(
      onTap: () {
        if (data.action != null) {
          clickActionWithAnalytics(
              data.action!, context, widgetData.widgetInfo, {});
        }
      },
      child: Padding(
        padding: EdgeInsets.symmetric(
          vertical: isHeader ? 0 : Spacings.x2,
          horizontal: Spacings.x2,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: isFirstColumn
              ? CrossAxisAlignment.start
              : CrossAxisAlignment.center,
          children: [
            if (data.imageUrl != null)
              CFNetworkImage(
                  imageUrl: getImageUrl(context, imagePath: data.imageUrl),
                  width: data.imageWidth.toDouble(),
                  height: data.imageHeight.toDouble(),
                  fit: BoxFit.contain),
            if (data.title != null)
              Text(
                data.title ?? "",
                style: AuroraTheme.of(context).textStyle(TypescaleValues.P7),
              ),
          ],
        ),
      ),
    );
  }

  Widget getFooterTableCell(BuildContext context, RowItems data) {
    return Padding(
      padding: EdgeInsets.symmetric(
        vertical: Spacings.x2,
        horizontal: Spacings.x2,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          if (data.action != null)
            PrimaryButton(
              () async {
                if (data.productType != null) {
                  await saveProductSelection(data.productType!);
                }
                clickActionWithAnalytics(data.action!, context,
                    widgetData.widgetInfo, {"productType": data.productType ?? ""});
              },
              data.action!.title ?? "",
              buttonType: PrimaryButtonType.SMALL,
              horizontalPadding: Spacings.x2,
            ),
          if (data.imageUrl != null)
            CFNetworkImage(
                imageUrl: getImageUrl(context, imagePath: data.imageUrl),
                width: data.imageWidth.toDouble(),
                height: data.imageHeight.toDouble(),
                fit: BoxFit.contain),
          if (data.title != null)
            Padding(
              padding: EdgeInsets.only(top: Spacings.x1),
              child: Text(
                data.title ?? "",
                style: AuroraTheme.of(context).textStyle(TypescaleValues.P3),
              ),
            ),
          if (data.subtitle != null)
            Padding(
              padding: const EdgeInsets.only(top: Spacings.x1),
              child: Text(
                data.subtitle ?? "",
                style: AuroraTheme.of(context)
                    .textStyle(TypescaleValues.P8, color: Colors.white60),
              ),
            ),
        ],
      ),
    );
  }

  Future<void> saveProductSelection(String productType) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString('tabProductType', productType);
  }
}

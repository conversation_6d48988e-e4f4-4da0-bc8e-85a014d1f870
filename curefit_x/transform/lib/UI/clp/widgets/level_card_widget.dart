import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/ui/alert_view.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/clp/models.dart';
import 'package:common/util/theme.dart';
import 'package:common/action/action_handler.dart' as ActionHandler;
import 'package:flutter_svg/flutter_svg.dart';

class LevelCardWidgetView extends StatelessWidget {
  final LevelCardWidgetData levelCardWidgetData;

  const LevelCardWidgetView(this.levelCardWidgetData);

  @override
  Widget build(BuildContext context) {
    final List cardIndexes =
        Iterable<int>.generate(levelCardWidgetData.cards.length).toList();

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: Spacings.x4),
      child: BlurView(
          child: Padding(
              padding: EdgeInsets.all(15),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Padding(
                          padding: EdgeInsets.only(left: 18, top: 10),
                          child: Text(levelCardWidgetData.title,
                              style: AuroraTheme.of(context).textStyle(TypescaleValues.H1)),
                        ),
                        Padding(
                            padding: EdgeInsets.only(top: 5),
                            child: TextButton(
                                child: Text(
                                  "VIEW ALL",
                                  style: AuroraTheme.of(context).textStyle(TypescaleValues.P6),
                                ),
                                onPressed: () {
                                  ActionHandler.Action? action =
                                      levelCardWidgetData.viewAllAction;
                                  if (action != null) {
                                    ActionBloc actionBloc =
                                        BlocProvider.of<ActionBloc>(context);
                                    PerformActionEvent event =
                                        PerformActionEvent(action);
                                    actionBloc.add(event);
                                  }
                                }))
                      ]),
                  ...cardIndexes
                      .map((index) => LevelCardView(
                          levelCardWidgetData.cards[index],
                          lockDialogData: levelCardWidgetData.lockDialogData,
                          showDivider: index < cardIndexes.length - 1))
                      .toList(),
                ],
              ))),
    );
  }
}

class LevelCardView extends StatelessWidget {
  final LevelCardChapter levelCardChapter;
  final bool showDivider;
  final LockDialogData? lockDialogData;

  const LevelCardView(this.levelCardChapter,
      {this.lockDialogData, this.showDivider = false});

  lockIcon() {
    return Container(
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(22),
          color: Colors.white.withOpacity(0.2)),
      child: Padding(
        padding: EdgeInsets.all(15),
        child: Transform.translate(
          offset: Offset(0, 0),
          child: Padding(
            padding: EdgeInsets.all(0),
            child: SvgPicture.asset(
              'assets/lock.svg',
              width: 15,
              height: 15,
              fit: BoxFit.fitWidth,
            ),
          ),
        ),
      ),
    );
  }

  Widget icon() {
    if (!levelCardChapter.isLocked && !levelCardChapter.complete) {
      return Transform.translate(
          offset: Offset(0, 0),
          child: SvgPicture.asset(
            'assets/level_circle.svg',
            width: 55,
            height: 55,
            fit: BoxFit.fitWidth,
          ));
    }
    return levelCardChapter.isLocked
        ? lockIcon()
        : Container(
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                color: Colors.white.withOpacity(0.2)),
            child: Padding(
                padding: EdgeInsets.all(5),
                child: Icon(
                  Icons.check,
                  size: 30.0,
                  color: Colors.white,
                )));
  }

  Widget buildCard(BuildContext context) {
    return InkWell(
        splashColor:
            levelCardChapter.isLocked ? Colors.transparent : Colors.grey,
        onTap: () {
          RepositoryProvider.of<AnalyticsRepository>(context).logWidgetClick(
              extraInfo: {
                "action": "Chapter view",
                "chapterName": levelCardChapter.title,
                "chapterLocked": levelCardChapter.isLocked,
                "chapterCompleted": levelCardChapter.complete
              },
              widgetInfo: levelCardChapter.widgetInfo);
          ActionHandler.Action? action = levelCardChapter.action;
          if (levelCardChapter.isLocked) {
            showModal(
              context: context,
              title: lockDialogData?.title,
              description: lockDialogData?.description,
              buttonTitle: lockDialogData?.buttonText,
            );
          } else if (action != null) {
            ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
            PerformActionEvent event = PerformActionEvent(action);
            actionBloc.add(event);
          }
        },
        child: Column(children: [
          Transform.translate(
              offset: Offset(
                  (!levelCardChapter.isLocked && !levelCardChapter.complete)
                      ? -6
                      : 0,
                  0),
              child: Container(
                padding: const EdgeInsets.only(
                    right: 20.0, bottom: 20, top: 30, left: 10),
                child:
                    Row(mainAxisAlignment: MainAxisAlignment.start, children: [
                  icon(),
                  Padding(
                      padding: EdgeInsets.only(left: (!levelCardChapter.isLocked && !levelCardChapter.complete)? 15: 20),
                      child: Container(
                          width: MediaQuery.of(context).size.width / 1.8,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(levelCardChapter.title ?? "",
                                  style: AuroraTheme.of(context).textStyle(TypescaleValues.P4)),
                              SizedBox(height: 10),
                              Text(levelCardChapter.subtitle ?? "",
                                  style: AuroraTheme.of(context).textStyle(TypescaleValues.P5))
                            ],
                          )))
                ]),
              )),
          if (showDivider)
            Padding(
                padding: EdgeInsets.symmetric(horizontal: 25),
                child: Container(
                    height: 0.5, color: Colors.white.withOpacity(0.5)))
        ]));
  }

  @override
  Widget build(BuildContext context) {
    return levelCardChapter.isLocked
        ? buildCard(context)
        : Material(color: Colors.transparent, child: buildCard(context));
  }
}

import 'package:common/ui/blur_view.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:flutter/material.dart';

class BatchSelectorTab extends StatelessWidget {
  final String? title;
  final String? subtitle;

  const BatchSelectorTab({this.title, this.subtitle});

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Padding(
        padding: const EdgeInsets.all(Spacings.x1),
        child: Column(
          children: [
            Text(
              title ?? "",
              style: AuroraTheme.of(context).textStyle(TypescaleValues.P3),
            ),
            Text(
              subtitle ?? "",
              style: AuroraTheme.of(context).textStyle(
                  TypescaleValues.TAGTEXT,
                  color: Colors.white60),
            ),
          ],
        ),
      ),
    );
  }
}

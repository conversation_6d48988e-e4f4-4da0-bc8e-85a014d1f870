import 'package:flutter/widgets.dart';

class TabBarChevronDecoration extends Decoration {
  TabBarChevronDecoration(
      {required this.containerColor, this.cornerRadius = 5});

  final Color containerColor;
  final double cornerRadius;

  @override
  BoxPainter createBoxPainter([VoidCallback? onChanged]) {
    return TabbarChevronPainter(
        containerColor: containerColor, cornerRadius: cornerRadius);
  }
}

class TabbarChevronPainter extends BoxPainter {
  TabbarChevronPainter({required this.containerColor, this.cornerRadius = 5});

  final Color containerColor;
  final double cornerRadius;

  @override
  void paint(Canvas canvas, Offset offset, ImageConfiguration configuration) {
    final Paint paint = new Paint()..color = containerColor;
    final height = 50.0;
    final width = 120.0;
    final startingLabelXCoordinated =
        ((configuration.size?.width ?? 50.0) - width) / 2;
    final startingLabelYCoordinated = -3;
    Path path = Path();
    path.moveTo(cornerRadius, height);
    path.lineTo(width / 3, height);
    path.lineTo(width / 2, height + height / 7);
    path.lineTo(2 * width / 3, height);
    path.lineTo(width - cornerRadius, height);
    path.cubicTo(width - cornerRadius, height, width, height, width,
        height - cornerRadius);
    path.lineTo(width, cornerRadius);
    path.cubicTo(width, cornerRadius, width, 0, width - cornerRadius, 0);
    path.lineTo(cornerRadius, 0);
    path.cubicTo(cornerRadius, 0, 0, 0, 0, cornerRadius);
    path.lineTo(0, height - cornerRadius);
    path.cubicTo(0, height - cornerRadius, 0, height, cornerRadius, height);
    path.close();
    canvas.translate(offset.dx + startingLabelXCoordinated,
        offset.dy + startingLabelYCoordinated);
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:common/action/action_handler.dart' as ActionHandler;


class CardClickableWidgetData implements IWidgetData {
  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  String? title;
  String? subtitle;
  ActionHandler.Action? action;

  CardClickableWidgetData(this.widgetType,
      {this.title, this.subtitle, this.action});

  factory CardClickableWidgetData.fromJson(widget, WidgetTypes widgetType) {
    return CardClickableWidgetData(
      widgetType,
      title: widget['title'],
      subtitle: widget['subtitle'],
      action:
      widget['action'] != null ? ActionHandler.Action.fromJson(widget['action']) : null,
    );
  }
}

class CardClickableWidget extends StatelessWidget {
  final CardClickableWidgetData widgetData;

  const CardClickableWidget({required this.widgetData});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: Spacings.x4),
      child: InkWell(
        onTap: () {
          if (widgetData.action != null) {
            ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
            PerformActionEvent event = PerformActionEvent(widgetData.action!);
            actionBloc.add(event);
          }
        },
        child: BlurView(
          borderRadius: 10,
          child: Container(
            padding: EdgeInsets.symmetric(
                vertical: Spacings.x3, horizontal: Spacings.x4),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widgetData.title ?? "",
                        style: AuroraTheme.of(context)
                            .textStyle(TypescaleValues.P1),
                      ),
                      Text(
                        widgetData.subtitle ?? "",
                        style: AuroraTheme.of(context).textStyle(
                            TypescaleValues.P8,
                            color: Colors.white60),
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(
                      left: Spacings.x2),
                  child: Icon(
                    Icons.chevron_right_rounded,
                    size: 24,
                    color: Colors.white,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

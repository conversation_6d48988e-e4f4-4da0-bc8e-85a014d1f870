import 'package:collection/collection.dart';
import 'package:common/blocs/navigation/navigation_bloc.dart';
import 'package:common/blocs/navigation/state.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/atoms/checkbox.dart';
import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/util/action_util.dart';
import 'package:flutter/material.dart' hide Action;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/UI/checkout_v2/widgets/checkout_actions_widget.dart';
import 'package:transform/blocs/center_selector/center_selector_bloc.dart';
import 'package:transform/blocs/center_selector/models.dart';
import 'package:common/blocs/checkout_v2/checkout_v2_bloc.dart';
import 'package:common/blocs/checkout_v2/checkout_v2_events.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:navigation_history_observer/navigation_history_observer.dart';
import 'package:transform/constants/constants.dart';

class CheckoutActionsWidgetV2 extends StatefulWidget {
  final CheckoutActionsWidgetData widgetData;

  const CheckoutActionsWidgetV2({required this.widgetData});

  @override
  State<CheckoutActionsWidgetV2> createState() =>
      _CheckoutActionsWidgetV2State();
}

class _CheckoutActionsWidgetV2State extends State<CheckoutActionsWidgetV2>
    with WidgetsBindingObserver {
  String currentStepID = "";
  bool consentProvided = true;
  String batchName = "";
  String centerName = "";
  String slotName = "";

  @override
  void initState() {
    currentStepID = widget.widgetData.stepsItem?.first.id ?? "";
    consentProvided = true;
    performStepCompletedAction();
    WidgetsBinding.instance.addObserver(this);
    super.initState();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  void performStepCompletedAction() {
    CheckoutV2Bloc checkoutBloc = BlocProvider.of<CheckoutV2Bloc>(context);
    CenterSelectorBloc centerSelectorBloc =
        BlocProvider.of<CenterSelectorBloc>(context);

    if (checkoutBloc.selectedSlotId != null &&
        checkoutBloc.selectedSlotId!.isNotEmpty &&
        checkoutBloc.selectedCenterId != null &&
        checkoutBloc.selectedCenterId!.isNotEmpty &&
        checkoutBloc.selectedBatchId != null &&
        checkoutBloc.selectedBatchId!.isNotEmpty &&
        centerSelectorBloc.batchSelectorScreenData != null) {
      BatchData batchData = centerSelectorBloc
          .batchSelectorScreenData!.batchesData!
          .firstWhere((element) => element.id == checkoutBloc.selectedBatchId);
      batchName = batchData.displayText ?? "";
      CenterData centerData = batchData.centersData!
          .firstWhere((element) => element.id == checkoutBloc.selectedCenterId);
      centerName = centerData.title ?? "";
      Slots? selectedSlot = centerData.slots!
          .firstWhereOrNull((element) => element.id == checkoutBloc.selectedSlotId);
      if (currentStepID == "BATCH_SELECTION" && selectedSlot != null) {
        slotName = selectedSlot.title ?? "";
        currentStepID = widget.widgetData.stepsItem!
                .firstWhere((element) => element.id == currentStepID)
                .nextId ??
            "";
      }
    } else {
      currentStepID = "BATCH_SELECTION";
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<NavigationBloc, NavigationState>(
      listener: (context, state) {
        if (state is NavigationStackUpdated &&
            state.action == NavigationStackAction.pop &&
            state.route?.settings.name ==
                '/${EnumToString.convertToString(RouteNames.tf_new_checkout)}') {
          setState(() {
            performStepCompletedAction();
          });
        }
      },
      child: BlurView(
        borderRadius: 0,
        blurType: BlurType.HIGH,
        child: Container(
          padding: EdgeInsets.symmetric(vertical: Spacings.x4),
          child: Column(
            children: [
              if (widget.widgetData.stepsItem != null &&
                  currentStepID != "BATCH_SELECTION")
                ...widget.widgetData.stepsItem!
                    .mapIndexed<Widget>((index, step) {
                  return step.visible
                      ? InkWell(
                          onTap: () {
                            if (widget.widgetData.actionsMap != null &&
                                widget.widgetData.actionsMap!
                                    .containsKey(step.id)) {
                              clickActionWithAnalytics(
                                  widget.widgetData.actionsMap![step.id]!,
                                  context,
                                  null, {});
                              setState(() {
                                performStepCompletedAction();
                              });
                            }
                          },
                          child: Container(
                            decoration: BoxDecoration(
                              border: Border(
                                bottom: BorderSide(
                                    color: step.showDivider
                                        ? Colors.white24
                                        : Colors.transparent),
                              ),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.only(
                                  left: Spacings.x4,
                                  right: Spacings.x4,
                                  bottom: Spacings.x2),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  CFNetworkImage(
                                    imageUrl: getImageUrl(context,
                                        imagePath: step.imageUrl),
                                    width: 24,
                                    height: 24,
                                  ),
                                  SizedBox(
                                    width: Spacings.x2,
                                  ),
                                  Expanded(
                                    child: Text(
                                      batchName +
                                          " | " +
                                          centerName +
                                          " | " +
                                          slotName,
                                      style: AuroraTheme.of(context)
                                          .textStyle(TypescaleValues.P4),
                                    ),
                                  ),
                                  SizedBox(
                                    width: Spacings.x2,
                                  ),
                                  Icon(
                                    Icons.chevron_right_rounded,
                                    size: 24,
                                    color: Colors.white,
                                  )
                                ],
                              ),
                            ),
                          ),
                        )
                      : Container();
                }),
              if (widget.widgetData.actionsMap != null &&
                  widget.widgetData.actionsMap!.isNotEmpty)
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: Spacings.x4),
                  child: getCurrentAction(),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget getCurrentAction() {
    return widget.widgetData.actionsMap!.containsKey(currentStepID)
        ? Column(
            children: [
              if (widget.widgetData.actionsMap![currentStepID]!.meta != null &&
                  widget.widgetData.actionsMap![currentStepID]!.meta!
                      .containsKey('consentInfo'))
                Padding(
                  padding: const EdgeInsets.only(
                      bottom: Spacings.x2, top: Spacings.x2),
                  child: CFCheckbox(
                    selected: consentProvided,
                    onTap: () {
                      setState(() {
                        consentProvided = !consentProvided;
                      });
                    },
                    widget: Expanded(
                      child: Text(
                        widget.widgetData.actionsMap![currentStepID]!
                            .meta!['consentInfo']['message'],
                        style: AuroraTheme.of(context).textStyle(
                            TypescaleValues.P5,
                            color: Colors.white60),
                      ),
                    ),
                  ),
                ),
              PrimaryButton(
                () {
                  if (currentStepID == "SUBMISSION") {
                    CheckoutV2Bloc checkoutBloc =
                        BlocProvider.of<CheckoutV2Bloc>(context);
                    checkoutBloc.add(SubmitCenterPreferenceEvent(
                        subCategoryCode: widget.widgetData.subCategoryCode));
                  }
                  clickActionWithAnalytics(
                      widget.widgetData.actionsMap![currentStepID]!,
                      context,
                      null, {});
                  setState(() {
                    performStepCompletedAction();
                  });
                },
                widget.widgetData.actionsMap![currentStepID]!.title ?? "",
                enabled: consentProvided,
              ),
            ],
          )
        : Container();
  }
}

import 'package:common/action/action_handler.dart';
import 'package:common/blocs/navigation/navigation_bloc.dart';
import 'package:common/blocs/navigation/state.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/atoms/checkbox.dart';
import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:common/util/action_util.dart';
import 'package:flutter/material.dart' hide Action;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:transform/blocs/center_selector/center_selector_bloc.dart';
import 'package:transform/blocs/center_selector/models.dart';
import 'package:common/blocs/checkout_v2/checkout_v2_bloc.dart';
import 'package:common/blocs/checkout_v2/checkout_v2_events.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:navigation_history_observer/navigation_history_observer.dart';
import 'package:collection/collection.dart';
import 'package:transform/constants/constants.dart';

class CheckoutActionsWidgetData implements IWidgetData {
  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  List<StepsItems>? stepsItem;
  Map<String, Action>? actionsMap;
  String? subCategoryCode;
  bool isTransformProduct;

  CheckoutActionsWidgetData(this.widgetType,
      {this.stepsItem,
      this.actionsMap,
      this.subCategoryCode,
      this.isTransformProduct = true});

  factory CheckoutActionsWidgetData.fromJson(widget, WidgetTypes widgetType) {
    Map<String, Action> actionsMap = {};
    widget['actionsMap'] != null
        ? widget['actionsMap'].forEach((id, action) {
            actionsMap[id] = Action.fromJson(action);
          })
        : null;
    return CheckoutActionsWidgetData(
      widgetType,
      stepsItem: widget['stepItems'] != null
          ? widget['stepItems'].map<StepsItems>((item) {
              return StepsItems.fromJson(item);
            }).toList()
          : null,
      actionsMap: actionsMap,
      subCategoryCode: widget['subCategoryCode'],
      isTransformProduct: widget['isTransformProduct'] ?? true,
    );
  }
}

class StepsItems {
  StepsItems(
      {this.id,
      this.imageUrl,
      this.hintText,
      this.nextId,
      this.visible = false,
      this.showDivider = false});

  String? id;
  String? imageUrl;
  String? hintText;
  String? nextId;
  bool visible;
  bool showDivider;

  factory StepsItems.fromJson(json) {
    return StepsItems(
      id: json['id'],
      imageUrl: json['imageUrl'],
      hintText: json['hintText'],
      nextId: json['nextId'],
      visible: json['visible'] ?? false,
      showDivider: json['showDivider'] ?? false,
    );
  }
}

class CheckoutActionsWidget extends StatefulWidget {
  final CheckoutActionsWidgetData widgetData;

  const CheckoutActionsWidget({required this.widgetData});

  @override
  State<CheckoutActionsWidget> createState() => _CheckoutActionsWidgetState();
}

class _CheckoutActionsWidgetState extends State<CheckoutActionsWidget>
    with WidgetsBindingObserver {
  String currentStepID = "";
  bool consentProvided = true;
  String centerName = "";
  String slotName = "";
  String addressName = "";
  String startDateValue = "";
  bool isTransformProduct = true;
  bool clickable = true;

  @override
  void initState() {
    currentStepID = widget.widgetData.stepsItem?.first.id ?? "";
    isTransformProduct = widget.widgetData.isTransformProduct;
    checkForReset();
    performStepCompletedAction();
    WidgetsBinding.instance.addObserver(this);
    super.initState();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  void checkForReset() {
    CheckoutV2Bloc checkoutBloc = BlocProvider.of<CheckoutV2Bloc>(context);
    if (checkoutBloc.reset ?? false) {
      checkoutBloc.reset = false;
      currentStepID = widget.widgetData.stepsItem?.first.id ?? "";
      consentProvided = true;
      slotName = "";
      centerName = "";
      addressName = "";
      startDateValue = "";
    }
  }

  String convertEpochToString(String epoch) {
    DateTime date = DateTime.fromMillisecondsSinceEpoch(int.parse(epoch));
    var suffix = "th";
    var digit = date.day % 10;
    if ((digit > 0 && digit < 4) && (date.day < 11 || date.day > 13)) {
      suffix = ["st", "nd", "rd"][digit - 1];
    }
    return new DateFormat("d'$suffix' MMMM yyyy").format(date);
  }

  void performStepCompletedAction() {
    CheckoutV2Bloc checkoutBloc = BlocProvider.of<CheckoutV2Bloc>(context);
    CenterSelectorBloc centerSelectorBloc =
        BlocProvider.of<CenterSelectorBloc>(context);
    if (isTransformProduct) {
      if (currentStepID == "ADDRESS_SELECTION") {
        if (checkoutBloc.selectedAddressId != null &&
            checkoutBloc.selectedAddressId!.isNotEmpty &&
            centerSelectorBloc.addressSelectorScreenData != null) {
          AddressData? addressData = centerSelectorBloc
              .addressSelectorScreenData!.addressesData
              ?.firstWhere(
                  (element) => element.id == checkoutBloc.selectedAddressId);
          if (addressData != null) {
            addressName = (addressData.subtitle ?? "");
            currentStepID = widget.widgetData.stepsItem!
                    .firstWhere((element) => element.id == currentStepID)
                    .nextId ??
                "";
          }
        }
      } else if (currentStepID == "START_DATE_SELECTION" ||
          currentStepID == "SUBMISSION") {
        if (checkoutBloc.selectedStartdate != null &&
            checkoutBloc.selectedStartdate!.isNotEmpty) {
          startDateValue = "Starts on " +
              convertEpochToString(checkoutBloc.selectedStartdate ?? "");
          if (currentStepID == "START_DATE_SELECTION") {
            currentStepID = widget.widgetData.stepsItem!
                    .firstWhere((element) => element.id == currentStepID)
                    .nextId ??
                "";
          }
        }
      }
    } else {
      if (currentStepID == "CENTER_SELECTION") {
        if (checkoutBloc.selectedCenterId != null &&
            checkoutBloc.selectedCenterId!.isNotEmpty &&
            centerSelectorBloc.screenData != null) {
          CenterData? centerData = centerSelectorBloc.screenData!.centersData
              ?.firstWhere(
                  (element) => element.id == checkoutBloc.selectedCenterId);
          if (centerData != null) {
            centerName = "Preferred center: " + (centerData.title ?? "");
            currentStepID = widget.widgetData.stepsItem!
                    .firstWhere((element) => element.id == currentStepID)
                    .nextId ??
                "";
          }
        }
      } else if (currentStepID == "SLOT_SELECTION" ||
          currentStepID == "SUBMISSION") {
        if (checkoutBloc.selectedSlotId != null &&
            checkoutBloc.selectedSlotId!.isNotEmpty &&
            centerSelectorBloc.screenData != null) {
          List<Slots>? slotsList = centerSelectorBloc
              .screenData!.centerSlotsMap![checkoutBloc.selectedCenterId];
          Slots selectedSlot = slotsList!.firstWhere(
              (element) => element.id == checkoutBloc.selectedSlotId);
          slotName = "Preferred slot: " + (selectedSlot.title ?? "");
          if (currentStepID == "SLOT_SELECTION") {
            currentStepID = widget.widgetData.stepsItem!
                    .firstWhere((element) => element.id == currentStepID)
                    .nextId ??
                "";
          }
        } else {
          currentStepID = "SLOT_SELECTION";
          slotName = "";
        }
      }
    }
  }

  String getHintText(String hintText, String step) {
    if (step == "CENTER_SELECTION") {
      return centerName.isNotEmpty ? centerName : hintText;
    } else if (step == "SLOT_SELECTION") {
      return slotName.isNotEmpty ? slotName : hintText;
    } else if (step == "ADDRESS_SELECTION") {
      return addressName.isNotEmpty ? addressName : hintText;
    } else if (step == "START_DATE_SELECTION") {
      return startDateValue.isNotEmpty ? startDateValue : hintText;
    }
    return hintText;
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<NavigationBloc, NavigationState>(
      listener: (context, state) {
        if (state is NavigationStackUpdated &&
            state.action == NavigationStackAction.pop &&
            state.route?.settings.name ==
                '/${EnumToString.convertToString(RouteNames.tf_new_checkout)}') {
          setState(() {
            checkForReset();
            performStepCompletedAction();
          });
        }
      },
      child: BlurView(
        borderRadius: 0,
        blurType: BlurType.HIGH,
        child: Container(
          padding: EdgeInsets.only(bottom: Spacings.x4, top: Spacings.x1),
          child: Column(
            children: [
              if (widget.widgetData.stepsItem != null)
                ...widget.widgetData.stepsItem!
                    .mapIndexed<Widget>((index, step) {
                  return step.visible
                      ? InkWell(
                          onTap: () {
                            if (widget.widgetData.actionsMap != null &&
                                widget.widgetData.actionsMap!
                                    .containsKey(step.id)) {
                              clickActionWithAnalytics(
                                  widget.widgetData.actionsMap![step.id]!,
                                  context,
                                  null, {});
                              setState(() {
                                performStepCompletedAction();
                              });
                            }
                          },
                          child: Container(
                            decoration: BoxDecoration(
                              border: Border(
                                bottom: BorderSide(
                                    color: step.showDivider
                                        ? Colors.white24
                                        : Colors.transparent),
                              ),
                            ),
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: Spacings.x4,
                                  vertical: Spacings.x2),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  CFNetworkImage(
                                    imageUrl: getImageUrl(context,
                                        imagePath: step.imageUrl),
                                    width: 24,
                                    height: 24,
                                  ),
                                  SizedBox(
                                    width: Spacings.x2,
                                  ),
                                  Expanded(
                                    child: Text(
                                      getHintText(
                                          step.hintText ?? "", step.id ?? ""),
                                      style: AuroraTheme.of(context)
                                          .textStyle(TypescaleValues.P4),
                                      overflow: TextOverflow.ellipsis,
                                      maxLines: 1,
                                    ),
                                  ),
                                  Icon(
                                    Icons.chevron_right_rounded,
                                    size: 24,
                                    color: Colors.white,
                                  )
                                ],
                              ),
                            ),
                          ),
                        )
                      : Container();
                }).toList(),
              SizedBox(
                height: Spacings.x1,
              ),
              if (widget.widgetData.actionsMap != null &&
                  widget.widgetData.actionsMap!.isNotEmpty)
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: Spacings.x4),
                  child: getCurrentAction(),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget getCurrentAction() {
    return widget.widgetData.actionsMap!.containsKey(currentStepID)
        ? Column(
            children: [
              if (widget.widgetData.actionsMap![currentStepID]!.meta != null &&
                  widget.widgetData.actionsMap![currentStepID]!.meta!
                      .containsKey('consentInfo'))
                Padding(
                  padding: const EdgeInsets.only(bottom: Spacings.x2),
                  child: CFCheckbox(
                    selected: consentProvided,
                    onTap: () {
                      setState(() {
                        consentProvided = !consentProvided;
                      });
                    },
                    widget: Expanded(
                      child: Text(
                        widget.widgetData.actionsMap![currentStepID]!
                            .meta!['consentInfo']['message'],
                        style: AuroraTheme.of(context).textStyle(
                            TypescaleValues.P5,
                            color: Colors.white60),
                      ),
                    ),
                  ),
                ),
              PrimaryButton(
                () {
                  if (clickable) {
                    if (currentStepID == "SUBMISSION") {
                      CheckoutV2Bloc checkoutBloc =
                          BlocProvider.of<CheckoutV2Bloc>(context);
                      if (isTransformProduct) {
                        Action? action =
                            widget.widgetData.actionsMap![currentStepID];
                        if (action != null &&
                            action.meta != null &&
                            (action.meta!['freePayment'] ?? false)) {
                          clickable = false;
                        }
                        if (action != null && action.rnAction != null) {
                          Action rnAction =
                              Action(type: action.rnAction!.type, meta: {
                            ...?action.rnAction!.meta,
                            "addressId": checkoutBloc.selectedAddressId ?? "",
                            "startDate": checkoutBloc.selectedStartdate ?? ""
                          });
                          action.rnAction = rnAction;
                          clickActionWithAnalytics(action, context, null, {});
                        }
                      } else {
                        if (checkoutBloc.selectedSlotId != null &&
                            checkoutBloc.selectedSlotId!.isNotEmpty) {
                          checkoutBloc.add(SubmitCenterPreferenceEvent(
                              subCategoryCode:
                                  widget.widgetData.subCategoryCode));
                          clickActionWithAnalytics(
                              widget.widgetData.actionsMap![currentStepID]!,
                              context,
                              null, {});
                        } else {
                          showError(context, "Issue while selecting the slot");
                        }
                      }
                    } else {
                      clickActionWithAnalytics(
                          widget.widgetData.actionsMap![currentStepID]!,
                          context,
                          null, {});
                    }
                    setState(() {
                      performStepCompletedAction();
                    });
                  }
                },
                widget.widgetData.actionsMap![currentStepID]!.title ?? "",
                enabled: consentProvided,
              ),
            ],
          )
        : Container();
  }

  void showError(BuildContext context, String message) async {
    showDialog(
        context: context,
        builder: (BuildContext context) {
          Widget closeButton = TextButton(
            child: Text("Close"),
            onPressed: () {
              Navigator.pop(context);
            },
          );

          // set up the AlertDialog
          return AlertDialog(
              title: Text('Error'),
              actions: [closeButton],
              content: SingleChildScrollView(
                  scrollDirection: Axis.vertical, child: Text(message)));
        });
  }
}

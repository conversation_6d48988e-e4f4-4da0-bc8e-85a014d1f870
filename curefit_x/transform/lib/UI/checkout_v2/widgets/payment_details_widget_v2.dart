import 'package:common/ui/blur_view.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/util/color.dart';
import 'package:flutter/material.dart';
import 'package:transform/UI/checkout/widgets/payment_details_widget.dart';

class PaymentDetailWidgetV2 extends StatelessWidget {
  PaymentDetailWidgetV2(this.paymentDetailWidgetData);

  final PaymentDetailsWidgetData paymentDetailWidgetData;

  @override
  Widget build(BuildContext context) {
    Color titleColor = paymentDetailWidgetData.hexColor != null
        ? HexColor.fromHex(paymentDetailWidgetData.hexColor!)
        : Colors.white;
    List<PriceRow> priceWidgets = [];
    for (int i = 0; i < paymentDetailWidgetData.priceDetails.length; i++)
      priceWidgets.add(PriceRow(paymentDetailWidgetData.priceDetails[i],
          i == paymentDetailWidgetData.priceDetails.length - 1));
    return BlurView(
      borderRadius: 0,
      child: Container(
        padding: EdgeInsets.only(
            top: Spacings.x12,
            bottom: Spacings.x5,
            left: Spacings.x8,
            right: Spacings.x8),
        child: Column(
          children: [
            paymentDetailWidgetData.packDuration.value.length > 0
                ? Column(
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Text(paymentDetailWidgetData.packDuration.value,
                                  style: AuroraTheme.of(context).textStyle(
                                      TypescaleValues.H5,
                                      color: titleColor)),
                              Text(paymentDetailWidgetData.footer ?? "",
                                  style: AuroraTheme.of(context).textStyle(
                                      TypescaleValues.TAGTEXT,
                                      color: titleColor))
                            ],
                          ),
                          Padding(
                            padding: const EdgeInsets.only(
                                left: Spacings.x2, top: 8),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  paymentDetailWidgetData.packDuration.type,
                                  style: AuroraTheme.of(context)
                                      .textStyle(TypescaleValues.H3),
                                ),
                                if (paymentDetailWidgetData.packHeader != null)
                                  Text(
                                    paymentDetailWidgetData.packHeader ?? "",
                                    style: AuroraTheme.of(context)
                                        .textStyle(TypescaleValues.H2),
                                  ),
                              ],
                            ),
                          )
                        ],
                      ),
                      SizedBox(height: Spacings.x8),
                    ],
                  )
                : Container(),
            Column(
              children: applyStaggeredAnimation(priceWidgets, context),
            ),
          ],
        ),
      ),
    );
  }
}

class PriceRow extends StatelessWidget {
  final PriceDetail priceDetail;
  final bool isTotal;

  PriceRow(this.priceDetail, this.isTotal);

  @override
  Widget build(BuildContext context) {
    return Padding(
        padding: EdgeInsets.only(bottom: 10),
        child: Column(children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(priceDetail.title,
                  style: !this.isTotal
                      ? AuroraTheme.of(context).textStyle(TypescaleValues.P5,
                          color: Colors.white.withOpacity(0.7))
                      : AuroraTheme.of(context).textStyle(TypescaleValues.H2)),
              Text(
                  (priceDetail.isDiscount ? "-" : "") +
                      (priceDetail.symbol ?? "") +
                      priceDetail.value,
                  style: !this.isTotal
                      ? AuroraTheme.of(context).textStyle(TypescaleValues.P5,
                          color: Colors.white.withOpacity(0.7))
                      : AuroraTheme.of(context).textStyle(TypescaleValues.H2))
            ],
          )
        ]));
  }
}

Iterable<E> mapIndexed<E, T>(
    Iterable<T> items, E Function(int index, T item) f) sync* {
  var index = 0;

  for (final item in items) {
    yield f(index, item);
    index = index + 1;
  }
}

import 'package:common/ui/alert_view.dart';
import 'package:common/ui/atoms/chips.dart';
import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/util/color.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/center_selector/models.dart';
import 'package:common/blocs/checkout_v2/checkout_v2_bloc.dart';

showSlotSelectionModal(
    {required BuildContext context, CenterSelectorScreenData? modalData}) {
  CanvasTheme draftTheme() {
    TimeOfDay now = TimeOfDay.now();
    return now.hour > 4 && now.hour < 19 ? CanvasTheme.TEAL : CanvasTheme.NIGHT;
  }

  CheckoutV2Bloc checkoutBloc = BlocProvider.of<CheckoutV2Bloc>(context);
  String? centerId = checkoutBloc.selectedCenterId;
  if (modalData == null || centerId == null || centerId.isEmpty) {
    showErrorAlert(
      context: context,
      title: "Please select a center in order to view the available slots",
      barrierDismissible: true,
      onClose: () {},
    );
  } else {
    showModalBottomSheet<void>(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        isScrollControlled: true,
        context: context,
        builder: (context) {
          return Padding(
            padding: MediaQuery.of(context).viewInsets,
            child: Wrap(
              children: [
                BlurView(
                  backgroundColor: draftTheme() == CanvasTheme.NIGHT
                      ? Color.fromRGBO(57, 68, 116, 1)
                      : Color.fromRGBO(32, 77, 89, 1),
                  opacity: 0.8,
                  blurType: BlurType.HIGH,
                  child: StatefulBuilder(
                      builder: (BuildContext context, setState) {
                    String slotId = checkoutBloc.selectedSlotId ?? "";
                    return Container(
                      width: double.infinity,
                      padding: EdgeInsets.symmetric(horizontal: Spacings.x4),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Center(
                            child: Container(
                              margin: const EdgeInsets.symmetric(
                                  vertical: Spacings.x2),
                              height: 2.5,
                              width: 55,
                              alignment: Alignment.topCenter,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(5),
                                color: Colors.white.withOpacity(0.3),
                              ),
                            ),
                          ),
                          SizedBox(
                            height: Spacings.x6,
                          ),
                          if (modalData.slotModalTitle != null)
                            Padding(
                              padding:
                                  const EdgeInsets.only(bottom: Spacings.x1),
                              child: Align(
                                alignment: Alignment.topLeft,
                                child: Text(
                                  modalData.slotModalTitle ?? "",
                                  style: AuroraTheme.of(context)
                                      .textStyle(TypescaleValues.H2),
                                ),
                              ),
                            ),
                          (centerId.isNotEmpty &&
                                  slotId.isNotEmpty &&
                                  modalData.centerSlotsMap != null &&
                                  modalData.centerSlotsMap!
                                      .containsKey(centerId))
                              ? Padding(
                                  padding: const EdgeInsets.only(
                                      bottom: Spacings.x4),
                                  child: Align(
                                    alignment: Alignment.topLeft,
                                    child: Text(
                                      modalData.centerSlotsMap![centerId]!
                                              .firstWhere((element) =>
                                                  element.id == slotId)
                                              .slotText ??
                                          "",
                                      style: AuroraTheme.of(context).textStyle(
                                        TypescaleValues.P8,
                                        color: HexColor.fromHex(
                                            modalData.modalTextHexColor ??
                                                "#FFFFFF"),
                                      ),
                                    ),
                                  ),
                                )
                              : SizedBox(height: Spacings.x4),
                          if (modalData.centerSlotsMap != null)
                            Wrap(
                              key: Key(slotId),
                              spacing: Spacings.x2,
                              runSpacing: Spacings.x2,
                              children: modalData.centerSlotsMap![centerId]!
                                  .map<Widget>((slot) {
                                return (slot.enabled ?? true)
                                    ? Chips(
                                        text: slot.title,
                                        initialState: slotId.isNotEmpty &&
                                                slotId == slot.id
                                            ? Status.Selected
                                            : Status.Default,
                                        finalState: slotId.isNotEmpty &&
                                                slotId == slot.id
                                            ? Status.Default
                                            : Status.Selected,
                                        stateChangeEnabled: true,
                                        onSelected: (bool selected) {
                                          setState(() {
                                            if (slotId.isNotEmpty &&
                                                slotId == slot.id) {
                                              if (selected) {
                                                checkoutBloc.selectedSlotId =
                                                    "";
                                              } else {
                                                checkoutBloc.selectedSlotId =
                                                    slot.id;
                                              }
                                            } else {
                                              if (selected) {
                                                checkoutBloc.selectedSlotId =
                                                    slot.id;
                                              } else {
                                                checkoutBloc.selectedSlotId =
                                                    "";
                                              }
                                            }
                                          });
                                        },
                                      )
                                    : Opacity(
                                        opacity: 0.4,
                                        child: Chips(
                                            text: slot.title,
                                            initialState: Status.Default,
                                            stateChangeEnabled: false),
                                      );
                              }).toList(),
                            ),
                          SizedBox(
                            height: Spacings.x18,
                          ),
                          PrimaryButton(() {
                            Navigator.of(context).pop();
                          }, modalData.buttonTitle ?? "SAVE"),
                          SizedBox(
                            height: Spacings.x4,
                          ),
                        ],
                      ),
                    );
                  }),
                ),
              ],
            ),
          );
        });
  }
}

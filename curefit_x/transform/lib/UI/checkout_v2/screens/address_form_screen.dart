import 'package:common/action/action_handler.dart';
import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/model/error_prop_model.dart';
import 'package:common/ui/atoms/chips.dart';
import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/page/basic_page_container.dart';
import 'package:common/ui/screens/error_screen.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:transform/blocs/address_form/address_form_bloc.dart';
import 'package:transform/blocs/address_form/events.dart';
import 'package:transform/blocs/address_form/models.dart';
import 'package:transform/blocs/address_form/state.dart';
import 'package:transform/constants/constants.dart';
import 'package:common/ui/atoms/input_fields/input_field.dart';

class AddressFormScreenArguments {
  String? subCategoryCode;
  String? vertical;

  AddressFormScreenArguments(Map<String, dynamic> payload) {
    this.subCategoryCode = payload["subCategoryCode"];
    this.vertical = payload["vertical"];
  }
}

class AddressFormScreen extends StatefulWidget {
  const AddressFormScreen({Key? key}) : super(key: key);

  @override
  _AddressFormScreenState createState() => _AddressFormScreenState();
}

class _AddressFormScreenState extends State<AddressFormScreen>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  AddressFormScreenArguments? getAddressFormScreenArguments() {
    final ScreenArguments? args =
        ModalRoute.of(context)!.settings.arguments as ScreenArguments?;
    if (args != null) {
      return AddressFormScreenArguments(args.params);
    }
    return null;
  }

  late GlobalKey<FormState> _formKey;
  Map<String, dynamic> formValues = {
    'latLong': [0, 0],
  };

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _formKey = GlobalKey<FormState>();
      AddressFormScreenArguments? arguments = getAddressFormScreenArguments();
      final addressFormScreenBloc =
          BlocProvider.of<AddressFormScreenBloc>(context);
      formValues["vertical"] = arguments?.vertical ?? "";
      addressFormScreenBloc.add(LoadAddressFormScreenEvent(
          showLoader: true, subCategoryCode: arguments?.subCategoryCode ?? ""));
    });
  }

  onBackPress() {
    if (Navigator.canPop(context)) {
      Navigator.pop(context);
    } else if (!Navigator.canPop(context)) {
      ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
      actionBloc.add(CloseApplicationEvent(shouldReset: true));
    }
  }

  logPageView() {
    AnalyticsRepository analyticsRepository =
        RepositoryProvider.of<AnalyticsRepository>(context);
    analyticsRepository.logPageViewEvent(
        pageId: EnumToString.convertToString(RouteNames.tf_address_form),
        eventInfo: {});
  }

  CanvasTheme draftTheme() {
    TimeOfDay now = TimeOfDay.now();
    return now.hour > 4 && now.hour < 19 ? CanvasTheme.TEAL : CanvasTheme.NIGHT;
  }

  Widget getForm(List<Widget> widgets) {
    return Padding(
      padding: const EdgeInsets.only(
        left: Spacings.x4,
        right: Spacings.x4,
        bottom: Spacings.x16,
      ),
      child: Form(
        key: _formKey,
        child: Wrap(
          spacing: 15,
          runSpacing: 15,
          alignment: WrapAlignment.spaceBetween,
          children: widgets,
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      body: Stack(
        children: [
          Aurora(
            context: context,
            canvasTheme: draftTheme(),
            size: MediaQuery.of(context).size,
          ),
          Positioned.fill(
            child: BlocBuilder<AddressFormScreenBloc, AddressFormScreenState>(
              builder: (context, state) {
                List<Widget> _widgets = [];
                if (state is AddressFormScreenLoadedState &&
                    state.screenData.formFields != null) {
                  List<Widget> widgets =
                      state.screenData.formFields!.map<Widget>((field) {
                    return getFormField(field);
                  }).toList();
                  _widgets = [getForm(widgets)];
                } else if (state is AddressFormScreenLoadingState &&
                    state.screenData != null &&
                    state.screenData!.formFields != null) {
                  List<Widget> widgets =
                      state.screenData!.formFields!.map<Widget>((field) {
                    return getFormField(field);
                  }).toList();
                  _widgets = [getForm(widgets)];
                } else if (state is AddressFormScreenErrorState) {
                  return Stack(
                    children: [
                      Container(
                        color: Colors.black,
                        child: ErrorScreen(
                            errorInfo: state.errorInfo ?? UnknownError()),
                      ),
                      Positioned(
                        right: 5,
                        top: 30,
                        child: IconButton(
                          onPressed: () {
                            if (Navigator.canPop(context)) {
                              Navigator.pop(context);
                            } else if (!Navigator.canPop(context)) {
                              ActionBloc actionBloc =
                                  BlocProvider.of<ActionBloc>(context);
                              actionBloc.add(
                                  CloseApplicationEvent(shouldReset: false));
                            }
                          },
                          icon: const Icon(
                            Icons.clear,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  );
                } else if (state is AddressAddedSuccessfullyState) {
                  //onBackPress();
                }
                return BasicPageContainer(
                  showTitleBar: true,
                  shouldBuildWidget: false,
                  footerPadding: EdgeInsets.only(top: 120),
                  itemBuilder:
                      (BuildContext context, Widget currentWidget, int index) {
                    return _widgets[index];
                  },
                  widgetData: _widgets,
                  onBackPressed: () {
                    onBackPress();
                  },
                  title: state is AddressFormScreenLoadedState
                      ? state.screenData.pageTitle
                      : "Enter your address",
                  showLoader: false,
                  canvasTheme: draftTheme(),
                  enableFloatingCTAAnimation: false,
                  floatingCTA: state is AddressFormScreenLoadedState &&
                          state.screenData.action != null
                      ? Padding(
                          padding: const EdgeInsets.all(Spacings.x4),
                          child: PrimaryButton(() {
                            if (_formKey.currentState!.validate()) {
                              final addressFormScreenBloc =
                                  BlocProvider.of<AddressFormScreenBloc>(
                                      context);
                              addressFormScreenBloc.add(SaveAddressScreenEvent(
                                  addressFields: formValues));
                              Future.delayed(Duration(milliseconds: 200), () {
                                onBackPress();
                              });
                            }
                          }, state.screenData.action!.title ?? ""),
                        )
                      : null,
                );
              },
            ),
          ),
          Positioned.fill(
            child: BlocBuilder<AddressFormScreenBloc, AddressFormScreenState>(
              builder: (context, state) {
                if (state is AddressFormScreenLoadingState &&
                    state.showLoader) {
                  return Center(child: FancyLoadingIndicator());
                }
                return Container();
              },
            ),
          ),
        ],
      ),
    );
  }

  _containsEmoji(String? input) {
    final RegExp emojiRegEx = RegExp(
        r'(\u00a9|\u00ae|[\u2000-\u3300]|\ud83c[\ud000-\udfff]|\ud83d[\ud000-\udfff]|\ud83e[\ud000-\udfff])');
    if (emojiRegEx.hasMatch(input ?? "")) {
      return true;
    } else {
      return false;
    }
  }

  _checkPhoneNumber(String? input) {
    final RegExp phoneNumberRegEx = RegExp(r'^[6-9]\d{9}$');
    print(input);
    if (phoneNumberRegEx.hasMatch(input ?? "")) {
      return false;
    } else {
      return true;
    }
  }

  isValidTextField(String? input, String errorString, bool isPhoneNumber) {
    if (input != null && input.isNotEmpty) {
      if (_containsEmoji(input)) {
        return "Emojis are not allowed";
      } else if (isPhoneNumber && _checkPhoneNumber(input)) {
        return "Enter valid 10 digits number";
      } else {
        return null;
      }
    } else {
      return errorString;
    }
  }

  Widget getFormField(FormFields field) {
    if (field.values != null && field.values!.isNotEmpty) {
      String selectedValue = field.values!.first;
      formValues[field.id ?? ""] = selectedValue;
      return Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            field.placeholder ?? "",
            style: AuroraTheme.of(context)
                .textStyle(TypescaleValues.P6, color: Colors.white60),
          ),
          SizedBox(
            height: Spacings.x3,
          ),
          SingleSelectChip(field: field, formValues: formValues),
        ],
      );
    }
    return InputField(
      inputType: field.type,
      validator: field.mandatory
          ? (value) {
              return isValidTextField(
                value,
                field.errorMessage ?? "Wrong Input",
                field.id == "phoneNumber",
              );
            }
          : null,
      onChanged: (value) {
        if (field.nested != null && field.nested!.isNotEmpty) {
          if (formValues[field.nested] != null) {
            formValues[field.nested][field.id ?? ""] = value;
            if (field.otherId != null && field.otherId!.isNotEmpty) {
              formValues[field.nested][field.otherId ?? ""] = value;
            }
          } else {
            formValues[field.nested!] = {};
            formValues[field.nested][field.id ?? ""] = value;
            if (field.otherId != null && field.otherId!.isNotEmpty) {
              formValues[field.nested][field.otherId ?? ""] = value;
            }
          }
        } else {
          formValues[field.id ?? ""] = value;
          if (field.otherId != null && field.otherId!.isNotEmpty) {
            formValues[field.otherId ?? ""] = value;
          }
        }
      },
      width: field.expanded ? scale(context, 335) : scale(context, 280) / 2,
      placeholderText: field.placeholder ?? "",
      borderType: BorderType.OUTLINE,
      onCompleted: (value) {},
    );
  }
}

class SingleSelectChip extends StatefulWidget {
  SingleSelectChip({required this.field, required this.formValues});

  final FormFields field;
  Map<String, dynamic> formValues;

  @override
  State<SingleSelectChip> createState() => _SingleSelectChipState();
}

class _SingleSelectChipState extends State<SingleSelectChip> {
  @override
  Widget build(BuildContext context) {
    String selectedValue = widget.formValues[widget.field.id];
    return Row(
      key: Key(selectedValue),
      children: [
        for (String value in widget.field.values!)
          Padding(
            padding: const EdgeInsets.only(left: Spacings.x2),
            child: Chips(
              key: Key(value),
              text: value,
              initialState:
                  (selectedValue == value ? Status.Selected : Status.Default),
              stateChangeEnabled: false,
              onTap: () {
                if (selectedValue != value) {
                  setState(() {
                    selectedValue = value;
                    widget.formValues[widget.field.id ?? ""] = value;
                  });
                }
              },
            ),
          )
      ],
    );
  }
}

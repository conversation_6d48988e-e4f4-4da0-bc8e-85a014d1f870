import 'package:common/action/action_handler.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/blocs/navigation/navigation_bloc.dart';
import 'package:common/blocs/navigation/state.dart';
import 'package:common/ui/alert_view.dart';
import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/atoms/radio_button.dart';
import 'package:common/ui/atoms/secondary_button.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/page/basic_page_container.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/util/action_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/center_selector/center_selector_bloc.dart';
import 'package:transform/blocs/center_selector/events.dart';
import 'package:transform/blocs/center_selector/models.dart';
import 'package:transform/blocs/center_selector/state.dart';
import 'package:common/blocs/checkout_v2/checkout_v2_bloc.dart';
import 'package:navigation_history_observer/navigation_history_observer.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:transform/constants/constants.dart';

class AddressSelectionScreenArguments {
  String? subCategoryCode;

  AddressSelectionScreenArguments(Map<String, dynamic> payload) {
    this.subCategoryCode =
        payload["subCategoryCode"] != null ? payload["subCategoryCode"] : "";
  }
}

class AddressSelectionScreen extends StatefulWidget {
  const AddressSelectionScreen({Key? key}) : super(key: key);

  @override
  State<AddressSelectionScreen> createState() => _AddressSelectionScreenState();
}

class _AddressSelectionScreenState extends State<AddressSelectionScreen> {
  String selectedAddressId = "";

  CanvasTheme draftTheme() {
    TimeOfDay now = TimeOfDay.now();
    return now.hour > 4 && now.hour < 19 ? CanvasTheme.TEAL : CanvasTheme.NIGHT;
  }

  onBackPress() {
    if (Navigator.canPop(context)) {
      Navigator.pop(context);
    } else if (!Navigator.canPop(context)) {
      ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
      actionBloc.add(CloseApplicationEvent(shouldReset: true));
    }
  }

  void refresh() {
    final ScreenArguments? args =
        ModalRoute.of(context)!.settings.arguments as ScreenArguments?;
    String subCategoryCode = "";
    if (args != null) {
      AddressSelectionScreenArguments arguments =
          AddressSelectionScreenArguments(args.params);
      subCategoryCode = arguments.subCategoryCode ?? "";
    }
    final centerSelectorBloc = BlocProvider.of<CenterSelectorBloc>(context);
    centerSelectorBloc
        .add(LoadAddressSelectorContentEvent(subCategoryCode: subCategoryCode));
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      refresh();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      body: Stack(
        children: [
          BlocListener<NavigationBloc, NavigationState>(
            listener: (context, state) {
              if (state is NavigationStackUpdated &&
                  state.action == NavigationStackAction.pop &&
                  state.route?.settings.name ==
                      '/${EnumToString.convertToString(RouteNames.tf_address_selector)}') {
                refresh();
              }
            },
            child: BlocListener<CenterSelectorBloc, CenterSelectorState>(
              listener: (context, state) {
                if (state is CenterSelectorErrorState) {
                  showErrorAlert(
                    context: context,
                    title: "Something went Wrong",
                    onClose: () {
                      Navigator.pop(context);
                    },
                  );
                }
              },
              child: BlocBuilder<CenterSelectorBloc, CenterSelectorState>(
                builder: (context, state) {
                  List<Widget> _widgets = [];
                  if (state is AddressSelectorLoadedState &&
                      state.screenData.addressesData != null) {
                    CheckoutV2Bloc checkoutBloc =
                        BlocProvider.of<CheckoutV2Bloc>(context);
                    if (checkoutBloc.selectedAddressId != null &&
                        checkoutBloc.selectedAddressId!.isNotEmpty) {
                      selectedAddressId = checkoutBloc.selectedAddressId ?? "";
                    } else {
                      selectedAddressId =
                          state.screenData.selectedAddressId ?? "";
                      checkoutBloc.selectedAddressId = state.screenData.selectedAddressId ?? "";;
                    }
                    _widgets = state.screenData.addressesData!
                        .map<Widget>((addressData) {
                      return Padding(
                        padding:
                            const EdgeInsets.symmetric(horizontal: Spacings.x4),
                        child: getAddressDetailCard(
                          addressData: addressData,
                          selectedAddressId: selectedAddressId,
                        ),
                      );
                    }).toList();
                    if (state.screenData.addAction != null) {
                      _widgets.add(
                        Padding(
                          padding: const EdgeInsets.symmetric(
                              horizontal: Spacings.x4, vertical: Spacings.x4),
                          child: SecondaryButton(() {
                            clickActionWithAnalytics(
                                state.screenData.addAction!,
                                context,
                                null,
                                {"trigger": "Add Action"});
                          }, state.screenData.addAction!.title ?? ""),
                        ),
                      );
                    }
                  }
                  if (state is AddressSelectorLoadedState &&
                      (state.screenData.addressesData == null ||
                          state.screenData.addressesData!.isEmpty)) {
                    _widgets = [
                      Padding(
                        padding: const EdgeInsets.only(top: 100),
                        child: Center(
                          child: Text(
                            state.screenData.errorMessage ??
                                "No Addresses Added",
                            style: AuroraTheme.of(context)
                                .textStyle(TypescaleValues.H1),
                          ),
                        ),
                      ),
                    ];
                    if (state.screenData.addAction != null) {
                      _widgets.add(Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: Spacings.x4, vertical: Spacings.x4),
                        child: SecondaryButton(() {
                          clickActionWithAnalytics(state.screenData.addAction!,
                              context, null, {"trigger": "Add Action"});
                        }, state.screenData.addAction!.title ?? ""),
                      ));
                    }
                  }
                  _widgets.add(SizedBox(
                    height: 100,
                  ));

                  return BasicPageContainer(
                    onBackPressed: () {
                      if (Navigator.canPop(context)) {
                        Navigator.pop(context);
                      } else if (!Navigator.canPop(context)) {
                        ActionBloc actionBloc =
                            BlocProvider.of<ActionBloc>(context);
                        actionBloc.add(CloseApplicationEvent());
                      }
                    },
                    footerPadding: EdgeInsets.only(top: 100),
                    title: state is AddressSelectorLoadedState
                        ? state.screenData.pageTitle
                        : "Select billing address",
                    showLoader: state is CenterSelectorLoadingState,
                    canvasTheme: draftTheme(),
                    shouldBuildWidget: false,
                    itemBuilder: (BuildContext context, Widget currentWidget,
                        int index) {
                      return _widgets[index];
                    },
                    widgetData: state is AddressSelectorLoadedState &&
                            state.screenData.addressesData != null
                        ? _widgets
                        : [],
                    enableFloatingCTAAnimation: false,
                    floatingCTA: state is AddressSelectorLoadedState &&
                            state.screenData.action != null
                        ? Padding(
                            padding: const EdgeInsets.symmetric(
                                vertical: Spacings.x4, horizontal: Spacings.x4),
                            child: PrimaryButton(
                              () {
                                onBackPress();
                                clickActionWithAnalytics(
                                    state.screenData.action!,
                                    context,
                                    null,
                                    {"trigger": "Save Address Action"});
                              },
                              state.screenData.action!.title ?? "CONTINUE",
                            ),
                          )
                        : null,
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget getAddressDetailCard(
      {required AddressData addressData, required String selectedAddressId}) {
    return InkWell(
      onTap: () {
        setState(() {
          BlocProvider.of<CheckoutV2Bloc>(context).selectedAddressId =
              addressData.id;
          BlocProvider.of<CheckoutV2Bloc>(context).selectedSlotId = "";
          BlocProvider.of<CheckoutV2Bloc>(context).reset = true;
        });
      },
      child: Padding(
        padding: EdgeInsets.only(top: Spacings.x5),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CFRadioButton(
              selected: selectedAddressId == addressData.id,
              text: addressData.title,
              subText: addressData.subtitle,
              subTextColor:
                  selectedAddressId == addressData.id ? Colors.white : null,
            ),
            Padding(
              padding: EdgeInsets.only(top: Spacings.x5),
              child: Container(
                height: 1,
                width: double.infinity,
                color: Colors.white24,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

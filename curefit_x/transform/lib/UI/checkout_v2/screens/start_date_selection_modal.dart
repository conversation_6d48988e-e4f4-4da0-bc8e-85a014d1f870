import 'package:common/ui/alert_view.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:common/blocs/checkout_v2/checkout_v2_bloc.dart';

showStartDateSelectionModal(
    {required BuildContext context,
    int? startDateEpoch,
    int? endDateEpoch,
    String? errorMessage}) {

  CheckoutV2Bloc checkoutBloc = BlocProvider.of<CheckoutV2Bloc>(context);
  String? addressId = checkoutBloc.selectedAddressId;
  DateTime getStartDate() {
    if (checkoutBloc.selectedStartdate != null) {
      return DateTime.fromMillisecondsSinceEpoch(
          int.parse(checkoutBloc.selectedStartdate!));
    } else if (startDateEpoch != null) {
      return DateTime.fromMillisecondsSinceEpoch(startDateEpoch);
    }
    return DateTime.now();
  }

  DateTime getMaximumDate() {
    if (endDateEpoch != null) {
      return DateTime.fromMillisecondsSinceEpoch(endDateEpoch);
    }
    return DateTime.now();
  }

  DateTime getMinimumDate() {
    if (startDateEpoch != null) {
      return DateTime.fromMillisecondsSinceEpoch(startDateEpoch);
    }
    return DateTime.now();
  }

  String convertEpochToString(int epoch) {
    DateTime date = DateTime.fromMillisecondsSinceEpoch(epoch);
    var suffix = "th";
    var digit = date.day % 10;
    if ((digit > 0 && digit < 4) && (date.day < 11 || date.day > 13)) {
      suffix = ["st", "nd", "rd"][digit - 1];
    }
    return new DateFormat("d'$suffix' MMMM yyyy").format(date);
  }

  if (startDateEpoch == null ||
      endDateEpoch == null) {
    showErrorAlert(
      context: context,
      title: errorMessage ??
          "Please pick the billing address first in order to purchase the pack",
      barrierDismissible: true,
      onClose: () {},
    );
  } else {
    String selectedDate = getStartDate().millisecondsSinceEpoch.toString();
    showModalBottomSheet<void>(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        isScrollControlled: true,
        context: context,
        builder: (context) {
          return Padding(
            padding: MediaQuery.of(context).viewInsets,
            child: Wrap(
              children: [
                BlurView(
                  opacity: 0.8,
                  blurType: BlurType.HIGH,
                  child: StatefulBuilder(
                      builder: (BuildContext context, setState) {
                    return Container(
                      width: double.infinity,
                      padding: EdgeInsets.symmetric(horizontal: Spacings.x4),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            padding: EdgeInsets.only(top: Spacings.x4),
                            child: Text(
                              "Pick a start date between " +
                                  convertEpochToString(startDateEpoch) +
                                  " and " +
                                  convertEpochToString(endDateEpoch),
                              style: AuroraTheme.of(context).textStyle(
                                  TypescaleValues.P2,
                                  color: Colors.black),
                            ),
                          ),
                          Container(
                            height:
                                MediaQuery.of(context).copyWith().size.height /
                                    4,
                            child: CupertinoDatePicker(
                              initialDateTime: getStartDate(),
                              onDateTimeChanged: (DateTime newdate) {
                                selectedDate =
                                    newdate.millisecondsSinceEpoch.toString();
                              },
                              minimumDate: getMinimumDate(),
                              maximumDate: getMaximumDate(),
                              dateOrder: DatePickerDateOrder.dmy,
                              mode: CupertinoDatePickerMode.date,
                            ),
                          ),
                          GestureDetector(
                            onTap: () {
                              checkoutBloc.selectedStartdate = selectedDate;
                              Navigator.pop(context);
                            },
                            child: Center(
                              child: Container(
                                width: scale(context, 200),
                                padding: EdgeInsets.only(
                                    bottom: Spacings.x6, top: Spacings.x2),
                                child: Text(
                                  "CONFIRM",
                                  style: AuroraTheme.of(context).textStyle(
                                      TypescaleValues.P1,
                                      color: Colors.blue),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  }),
                ),
              ],
            ),
          );
        });
  }
}

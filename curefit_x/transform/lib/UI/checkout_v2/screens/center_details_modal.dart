import 'package:common/action/action_handler.dart';
import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/atoms/floating_twin_button.dart';
import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/util/action_util.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/material.dart' hide Action;
import 'package:transform/UI/clp/widgets/coach_info_widget.dart';
import 'package:transform/blocs/clp/models.dart';

showCenterDetailsModal({
  required BuildContext context,
  String? title,
  String? subtitle,
  String? imageUrl,
  List<String>? facilities,
  List<String>? iconList,
  Action? action,
  Action? redirectionAction,
  bool enableDayNightTheme = true,
  CanvasTheme? themeType,
  List<Action>? actionsList,
  CoachInfoCardData? coachInfoCardData,
}) {
  showModalBottomSheet<void>(
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(20),
        topRight: Radius.circular(20),
      ),
    ),
    isScrollControlled: true,
    context: context,
    builder: (context) {
      return Padding(
        padding: MediaQuery.of(context).viewInsets,
        child: Wrap(
          children: [
            BlurView(
              backgroundColor: enableDayNightTheme
                  ? themeType != null && themeType == CanvasTheme.NIGHT
                      ? Color.fromRGBO(57, 68, 116, 1)
                      : Color.fromRGBO(32, 77, 89, 1)
                  : Colors.transparent,
              opacity: 0.8,
              blurType: BlurType.HIGH,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    margin: const EdgeInsets.symmetric(vertical: Spacings.x2),
                    height: 2.5,
                    width: 55,
                    alignment: Alignment.topCenter,
                    color: Colors.white.withOpacity(0.5),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(top: Spacings.x5, left: Spacings.x4, right: Spacings.x4),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (imageUrl != null)
                          Padding(
                            padding:
                                const EdgeInsets.only(right: Spacings.x3),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(5),
                              child: Container(
                                height: scale(context, 80),
                                width: scale(context, 80),
                                child: CFNetworkImage(
                                  imageUrl: getImageUrl(context,
                                      imagePath: imageUrl),
                                  fit: BoxFit.cover,
                                  height: scale(context, 80),
                                  width: scale(context, 80),
                                  placeholder:
                                      (BuildContext context, String url) {
                                    return Container();
                                  },
                                ),
                              ),
                            ),
                          ),
                        Expanded(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              if (title != null)
                                Text(
                                  title,
                                  style: AuroraTheme.of(context).textStyle(
                                    TypescaleValues.H4,
                                    color: Colors.white,
                                  ),
                                  textAlign: TextAlign.left,
                                ),
                              if (subtitle != null)
                                Padding(
                                  padding: const EdgeInsets.only(
                                    top: Spacings.x1,
                                  ),
                                  child: Text(
                                    subtitle,
                                    style: AuroraTheme.of(context).textStyle(
                                      TypescaleValues.P8,
                                      color: Colors.white60,
                                    ),
                                    textAlign: TextAlign.left,
                                  ),
                                ),
                            ],
                          ),
                        ),
                        if (action != null)
                          Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: Spacings.x1,
                                vertical: Spacings.x1),
                            child: InkWell(
                              onTap: () {
                                clickActionWithAnalytics(action, context,
                                    null, {"modal": "CenterDetailsModal"});
                              },
                              child: Text(
                                action.title ?? "",
                                style: AuroraTheme.of(context)
                                    .textStyle(TypescaleValues.P6),
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                  if (facilities != null && facilities.isNotEmpty)
                    Padding(
                      padding: EdgeInsets.only(top: Spacings.x7, left: Spacings.x4, right: Spacings.x4),
                      child: Text(
                        "Facilities",
                        style: AuroraTheme.of(context)
                            .textStyle(TypescaleValues.P5),
                      ),
                    ),
                  if (facilities != null && facilities.isNotEmpty)
                    Padding(
                      padding: EdgeInsets.only(top: Spacings.x3, left: Spacings.x4, right: Spacings.x4),
                      child: Wrap(
                        spacing: Spacings.x4,
                        runSpacing: Spacings.x4,
                        children: [
                          for (int idx = 0; idx < facilities.length; idx++)
                            Container(
                              width: scale(context, 150),
                              height: 20,
                              child: Row(
                                children: [
                                  if (iconList != null &&
                                      iconList.length > idx)
                                    Padding(
                                      padding: const EdgeInsets.only(
                                          right: Spacings.x2),
                                      child: CFNetworkImage(
                                        imageUrl: getImageUrl(context,
                                            imagePath: iconList[idx]),
                                        fit: BoxFit.contain,
                                        height: scale(context, 18),
                                        width: scale(context, 18),
                                        placeholder: (BuildContext context,
                                            String url) {
                                          return Container();
                                        },
                                      ),
                                    ),
                                  Text(
                                    facilities[idx],
                                    style: AuroraTheme.of(context).textStyle(
                                        TypescaleValues.P5,
                                        color: Colors.white60),
                                  ),
                                ],
                              ),
                            ),
                        ],
                      ),
                    ),
                  if (coachInfoCardData != null)
                    Padding(
                      padding: const EdgeInsets.only(top: Spacings.x8, left: Spacings.x4, right: Spacings.x4),
                      child: CoachInfoCard(cardData: coachInfoCardData),
                    ),
                  SizedBox(
                    height: Spacings.x2,
                  ),
                  Padding(
                    padding:
                        const EdgeInsets.symmetric(vertical: Spacings.x4),
                    child: actionsList != null && actionsList.isNotEmpty
                        ? FloatingTwinButton(
                            titleText: actionsList[0].description,
                            data: actionsList,
                            horizontalPadding: Spacings.x4,
                            onPress: (Action action) {
                              RepositoryProvider.of<AnalyticsRepository>(
                                      context)
                                  .logButtonClickEvent(extraInfo: {
                                "slotSelectedFrom": "centerDetailModal",
                              });
                              ActionBloc actionBloc =
                                  BlocProvider.of(context);
                              PerformActionEvent event =
                                  PerformActionEvent(action);
                              actionBloc.add(event);
                              Navigator.of(context).pop();
                              if (action.completionAction != null) {
                                clickActionWithAnalytics(
                                    action.completionAction!,
                                    context,
                                    null, {});
                              } else if (redirectionAction != null) {
                                clickActionWithAnalytics(
                                    redirectionAction, context, null, {});
                              }
                            },
                            bottomPadding: 0)
                        : Padding(
                          padding: const EdgeInsets.symmetric(horizontal: Spacings.x4),
                          child: PrimaryButton(
                              () {
                                if (Navigator.canPop(context)) {
                                  Navigator.pop(context);
                                } else {
                                  ActionBloc actionBloc =
                                      BlocProvider.of<ActionBloc>(context);
                                  actionBloc.add(CloseApplicationEvent(
                                      shouldReset: false));
                                }
                              },
                              "DONE",
                            ),
                        ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    },
  );
}

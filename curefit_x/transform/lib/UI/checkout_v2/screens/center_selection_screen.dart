import 'package:common/action/action_handler.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/alert_view.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/page/basic_page_container.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/util/color.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/center_selector/center_selector_bloc.dart';
import 'package:transform/blocs/center_selector/events.dart';
import 'package:transform/blocs/center_selector/models.dart';
import 'package:transform/blocs/center_selector/state.dart';
import 'package:common/blocs/checkout_v2/checkout_v2_bloc.dart';

class CenterSelectionScreenArguments {
  String? subCategoryCode;

  CenterSelectionScreenArguments(Map<String, dynamic> payload) {
    this.subCategoryCode =
    payload["subCategoryCode"] != null ? payload["subCategoryCode"] : "";
  }
}

class CenterSelectionScreen extends StatefulWidget {
  const CenterSelectionScreen({Key? key}) : super(key: key);

  @override
  State<CenterSelectionScreen> createState() => _CenterSelectionScreenState();
}

class _CenterSelectionScreenState extends State<CenterSelectionScreen> {
  CanvasTheme draftTheme() {
    TimeOfDay now = TimeOfDay.now();
    return now.hour > 4 && now.hour < 19 ? CanvasTheme.TEAL : CanvasTheme.NIGHT;
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      final ScreenArguments? args =
      ModalRoute.of(context)!.settings.arguments as ScreenArguments?;
      String subCategoryCode = "";
      if (args != null) {
        CenterSelectionScreenArguments arguments = CenterSelectionScreenArguments(args.params);
        subCategoryCode = arguments.subCategoryCode ?? "";
      }
      final centerSelectorBloc = BlocProvider.of<CenterSelectorBloc>(context);
      centerSelectorBloc.add(LoadCenterSelectorContentEvent(subCategoryCode: subCategoryCode));
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      body: Stack(
        children: [
          BlocListener<CenterSelectorBloc, CenterSelectorState>(
            listener: (context, state) {
              if (state is CenterSelectorErrorState) {
                showErrorAlert(
                  context: context,
                  title: "Something Went Wrong",
                  onClose: () {
                    Navigator.pop(context);
                  },
                );
              }
            },
            child: BlocBuilder<CenterSelectorBloc, CenterSelectorState>(
              builder: (context, state) {
                List<Widget> _widgets = [];
                if (state is CenterSelectorLoadedState &&
                    state.screenData.centersData != null) {
                  _widgets =
                      state.screenData.centersData!.map<Widget>((centerData) {
                    return Padding(
                      padding: const EdgeInsets.only(
                          bottom: Spacings.x5,
                          left: Spacings.x4,
                          right: Spacings.x4),
                      child: CenterDetailCard(centerData: centerData),
                    );
                  }).toList();
                }
                return BasicPageContainer(
                  onBackPressed: () {
                    if (Navigator.canPop(context)) {
                      Navigator.pop(context);
                    } else if (!Navigator.canPop(context)) {
                      ActionBloc actionBloc =
                          BlocProvider.of<ActionBloc>(context);
                      actionBloc.add(CloseApplicationEvent());
                    }
                  },
                  footerPadding: EdgeInsets.only(top: 100),
                  title: state is CenterSelectorLoadedState
                      ? state.screenData.centerPageTitle
                      : "Pick your center",
                  showLoader: state is CenterSelectorLoadingState,
                  canvasTheme: draftTheme(),
                  shouldBuildWidget: false,
                  itemBuilder:
                      (BuildContext context, Widget currentWidget, int index) {
                    return _widgets[index];
                  },
                  widgetData: state is CenterSelectorLoadedState &&
                          state.screenData.centersData != null
                      ? state.screenData.centersData!
                          .map<CenterDetailCard>((centerData) {
                          return CenterDetailCard(centerData: centerData);
                        }).toList()
                      : [],
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

class CenterDetailCard extends StatelessWidget {
  final CenterData centerData;

  const
  CenterDetailCard({required this.centerData});

  @override
  Widget build(BuildContext context) {
    return Opacity(
      opacity: (centerData.enabled ?? true) ? 1 : 0.6,
      child: InkWell(
        onTap: () {
          if (centerData.enabled ?? true) {
            BlocProvider.of<CheckoutV2Bloc>(context).selectedCenterId =
                centerData.id;
            BlocProvider.of<CheckoutV2Bloc>(context).selectedSlotId = "";
            BlocProvider.of<CheckoutV2Bloc>(context).reset = true;
            if (Navigator.canPop(context)) {
              Navigator.pop(context);
            } else if (!Navigator.canPop(context)) {
              ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
              actionBloc.add(CloseApplicationEvent());
            }
          }
        },
        child: Container(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (centerData.imageUrl != null)
                Padding(
                  padding: const EdgeInsets.only(right: Spacings.x3),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(10),
                    child: CFNetworkImage(
                      fit: BoxFit.cover,
                      imageUrl: getImageUrl(context,
                          imagePath: centerData.imageUrl ?? ""),
                      height: 70,
                      width: 70,
                    ),
                  ),
                ),
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (centerData.slotsText != null)
                      Padding(
                        padding: const EdgeInsets.only(bottom: 2),
                        child: Text(
                          centerData.slotsText ?? "",
                          style: AuroraTheme.of(context).textStyle(
                            TypescaleValues.TAGTEXT,
                            color: HexColor.fromHex(
                                centerData.hexColor ?? "#FFFFFF"),
                          ),
                        ),
                      ),
                    if (centerData.title != null)
                      Padding(
                        padding: const EdgeInsets.only(bottom: 2),
                        child: Text(
                          centerData.title ?? "",
                          style: AuroraTheme.of(context)
                              .textStyle(TypescaleValues.P1),
                        ),
                      ),
                    if (centerData.subtitle != null)
                      Text(
                        centerData.subtitle ?? "",
                        style: AuroraTheme.of(context).textStyle(
                            TypescaleValues.P8,
                            color: Colors.white60),
                      ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

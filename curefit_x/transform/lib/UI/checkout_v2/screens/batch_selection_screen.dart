import 'dart:io';

import 'package:collection/collection.dart';
import 'package:common/action/action_handler.dart';
import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/blocs/checkout_v2/checkout_v2_bloc.dart';
import 'package:common/blocs/navigation/navigation_bloc.dart';
import 'package:common/blocs/navigation/state.dart';
import 'package:common/font/cf_icons.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/model/error_prop_model.dart';
import 'package:common/ui/atoms/floating_button.dart';
import 'package:common/ui/atoms/secondary_button.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/screens/error_screen.dart';
import 'package:common/ui/scroll/scroll_app_bar.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/util/action_util.dart';
import 'package:common/util/color.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:flutter/material.dart' hide Action;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:navigation_history_observer/navigation_history_observer.dart';
import 'package:transform/blocs/center_selector/center_selector_bloc.dart';
import 'package:transform/blocs/center_selector/events.dart';
import 'package:transform/blocs/center_selector/models.dart';
import 'package:transform/blocs/center_selector/state.dart';
import 'package:transform/constants/constants.dart';

class BatchSelectionScreenArguments {
  String? subCategoryCode;
  String? filter;

  BatchSelectionScreenArguments(Map<String, dynamic> payload) {
    this.subCategoryCode =
        payload["subCategoryCode"] != null ? payload["subCategoryCode"] : "";
    this.filter = payload["filter"] != null ? payload["filter"] : "";
  }
}

class BatchSelectionScreen extends StatefulWidget {
  const BatchSelectionScreen({Key? key}) : super(key: key);

  @override
  State<BatchSelectionScreen> createState() => _BatchSelectionScreenState();
}

class _BatchSelectionScreenState extends State<BatchSelectionScreen>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  TabController? _tabController;
  int _currentIndex = 0;
  String batchId = "";
  String centerId = "";
  String slotId = "";
  bool modalOpened = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      refresh(context: context);
    });
  }

  refresh({required BuildContext context, bool showLoader = false}) {
    final ScreenArguments? args =
        ModalRoute.of(context)!.settings.arguments as ScreenArguments?;
    String subCategoryCode = "";
    String filter = "";
    if (args != null) {
      BatchSelectionScreenArguments arguments =
          BatchSelectionScreenArguments(args.params);
      subCategoryCode = arguments.subCategoryCode ?? "";
      filter = arguments.filter ?? "";
    }
    final centerSelectorBloc = BlocProvider.of<CenterSelectorBloc>(context);
    centerSelectorBloc.add(LoadBatchSelectorContentEvent(
        subCategoryCode: subCategoryCode, filter: filter));
  }

  @override
  void dispose() {
    _tabController?.dispose();
    super.dispose();
  }

  onBackPress() {
    if (Navigator.canPop(context)) {
      Navigator.pop(context);
    } else if (!Navigator.canPop(context)) {
      ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
      actionBloc.add(CloseApplicationEvent(shouldReset: true));
    }
  }

  _handleTabSelection() {
    if (_tabController != null) {
      setState(() {
        _currentIndex = _tabController!.index;
      });
    }
  }

  double toolbarHeight() {
    return Platform.isAndroid ? 120 : 98;
  }

  void saveSlot(BatchSelectorScreenData screenData, Action? action) {
    CheckoutV2Bloc checkoutBloc = BlocProvider.of<CheckoutV2Bloc>(context);
    checkoutBloc.selectedSlotId = slotId;
    checkoutBloc.selectedCenterId = centerId;
    checkoutBloc.selectedBatchId = screenData.batchesData![_currentIndex].id;
    if (slotId.isEmpty && centerId.isEmpty) {
      RepositoryProvider.of<AnalyticsRepository>(context)
          .logButtonClickEvent(extraInfo: {
        "trigger": "SAVE_BATCH",
        "selected": false,
      });
    } else {
      RepositoryProvider.of<AnalyticsRepository>(context)
          .logButtonClickEvent(extraInfo: {
        "trigger": "SAVE_BATCH",
        "selected": true,
        "slotId": slotId,
        "centerId": centerId,
        "batchId": screenData.batchesData![_currentIndex].id
      });
      if (action != null) {
        clickActionWithAnalytics(action, context, null, {});
      } else if (screenData.action!.url != null) {
        clickActionWithAnalytics(screenData.action!, context, null, {});
      } else {
        onBackPress();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    AuroraThemeData themeData = AuroraTheme.of(context);
    return BlocListener<NavigationBloc, NavigationState>(
      listener: (context, state) {
        if (state is NavigationStackUpdated &&
            state.action == NavigationStackAction.pop &&
            state.route?.settings.name ==
                '/${EnumToString.convertToString(RouteNames.tf_batch_selector)}') {
          if (modalOpened) {
            modalOpened = false;
          } else {
            refresh(context: context, showLoader: false);
          }
        }
      },
      child: BlocListener<CenterSelectorBloc, CenterSelectorState>(
        listener: (context, state) {
          if (state is BatchSelectorLoadedState) {
            if (state.screenData.batchesData != null &&
                state.screenData.batchesData!.isNotEmpty) {
              CheckoutV2Bloc checkoutBloc =
                  BlocProvider.of<CheckoutV2Bloc>(context);
              if (checkoutBloc.selectedBatchId != null &&
                  checkoutBloc.selectedBatchId!.isNotEmpty) {
                centerId = checkoutBloc.selectedCenterId ?? "";
                slotId = checkoutBloc.selectedSlotId ?? "";
                batchId = checkoutBloc.selectedBatchId ?? "";
                int index = state.screenData.batchesData!
                    .indexWhere((element) => element.id == batchId);
                if (index == -1) {
                  index = 0;
                }
                _tabController = TabController(
                    length: state.screenData.batchesData!.length,
                    vsync: this,
                    initialIndex: index);
              } else {
                _tabController = TabController(
                    length: state.screenData.batchesData!.length,
                    vsync: this,
                    initialIndex: state.screenData.selectedIndex ?? 0);
                batchId = state.screenData.batchesData?.first.id ?? "";
              }
              _tabController?.addListener(_handleTabSelection);
            }
          }
        },
        child: Stack(
          children: [
            Aurora(
              size: MediaQuery.of(context).size,
              context: context,
            ),
            BlocBuilder<CenterSelectorBloc, CenterSelectorState>(
              builder: (context, state) {
                BatchSelectorScreenData? screenData = null;
                if (state is BatchSelectorLoadedState) {
                  screenData = state.screenData;
                }
                return Scaffold(
                  backgroundColor: Colors.transparent,
                  appBar: ScrollAppBar(
                    centerTitle: false,
                    scrollControllerPosition: _currentIndex,
                    primary: Platform.isIOS,
                    title: Column(
                      children: [
                        SizedBox(
                          height: Platform.isAndroid
                              ? (MediaQuery.of(context).padding.top +
                                  AuroraTheme.of(context).embeddedSafeArea.top)
                              : 0,
                        ),
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            GestureDetector(
                                onTap: () {
                                  if (Navigator.canPop(context)) {
                                    Navigator.pop(context);
                                  } else if (!Navigator.canPop(context)) {
                                    ActionBloc actionBloc =
                                        BlocProvider.of<ActionBloc>(context);
                                    actionBloc.add(CloseApplicationEvent(
                                        shouldReset: false));
                                  }
                                },
                                child: Padding(
                                  padding: const EdgeInsets.only(right: 15),
                                  child: Row(children: [
                                    Padding(
                                      padding: const EdgeInsets.only(
                                        right: Spacings.x4,
                                      ),
                                      child: Icon(
                                        CFIcons.chevron_left,
                                        color: Colors.white,
                                        size: 20,
                                        semanticLabel: "chevron_left",
                                      ),
                                    ),
                                    Text(
                                      screenData != null
                                          ? (screenData.pageTitle ?? "")
                                          : "",
                                      style: AuroraTheme.of(context).textStyle(
                                          TypescaleValues.H1,
                                          color: Colors.white),
                                    ),
                                  ]),
                                )),
                          ],
                        ),
                      ],
                    ),
                    automaticallyImplyLeading: false,
                    toolbarHeight: toolbarHeight(),
                    controller: new ScrollController(),
                    materialType: MaterialType.transparency,
                    actions: screenData != null &&
                            (screenData.localitySelectorAction != null)
                        ? [
                            InkWell(
                                onTap: () {
                                  clickActionWithAnalytics(
                                      screenData!.localitySelectorAction!,
                                      context,
                                      null,
                                      {"trigger": "localitySelector"});
                                },
                                child: Padding(
                                  padding:
                                      const EdgeInsets.only(right: Spacings.x2),
                                  child: Icon(
                                    CFIcons.location_small,
                                    size: 17,
                                    color: Colors.white,
                                    semanticLabel: "location_small",
                                  ),
                                ))
                          ]
                        : [],
                    bottom: screenData != null &&
                            (screenData.batchesData?.length ?? 0) >= 1
                        ? PreferredSize(
                            preferredSize: Size.fromHeight(Spacings.x8),
                            child: Align(
                                alignment: Alignment.centerLeft,
                                child: _tabController != null &&
                                        _tabController!.length > 0
                                    ? Container(
                                        height: Spacings.x8,
                                        width:
                                            MediaQuery.of(context).size.width,
                                        decoration: BoxDecoration(
                                            border: Border(
                                                bottom: BorderSide(
                                          color: Colors.grey.withOpacity(0.15),
                                          width: 1.0,
                                        ))),
                                        child: TabBar(
                                          tabAlignment: TabAlignment.start,
                                          dividerHeight: 0,
                                          isScrollable: true,
                                          labelPadding: const EdgeInsets.all(0),
                                          indicatorColor: Colors.white,
                                          indicatorSize:
                                              TabBarIndicatorSize.label,
                                          automaticIndicatorColorAdjustment:
                                              false,
                                          indicatorPadding:
                                              const EdgeInsets.only(
                                                  left: Spacings.x3,
                                                  right: Spacings.x3,
                                                  top: Spacings.x1),
                                          controller: _tabController,
                                          tabs: screenData.batchesData!
                                              .mapIndexed((index, e) {
                                            return Semantics(
                                              label: e.title,
                                              explicitChildNodes: true,
                                              container: true,
                                              child: Tab(
                                                  child: Padding(
                                                padding:
                                                    const EdgeInsets.symmetric(
                                                        horizontal: Spacings.x3,
                                                        vertical: 0),
                                                child: Column(
                                                  mainAxisAlignment:
                                                      MainAxisAlignment.center,
                                                  crossAxisAlignment:
                                                      CrossAxisAlignment.center,
                                                  children: [
                                                    Text(e.title ?? "",
                                                        style: themeData.textStyle(
                                                            TypescaleValues.P3,
                                                            color: index ==
                                                                    _tabController
                                                                        ?.index
                                                                ? Colors.white
                                                                : Colors.white
                                                                    .withOpacity(
                                                                        0.7))),
                                                    if (e.subtitle != null)
                                                      Padding(
                                                        padding:
                                                            const EdgeInsets
                                                                .only(
                                                                top: Spacings
                                                                    .x1),
                                                        child: Text(
                                                            e.subtitle ?? "",
                                                            style: themeData.textStyle(
                                                                TypescaleValues
                                                                    .P10,
                                                                color: index ==
                                                                        _tabController
                                                                            ?.index
                                                                    ? Colors
                                                                        .white60
                                                                    : Colors
                                                                        .white60
                                                                        .withOpacity(
                                                                            0.7))),
                                                      ),
                                                  ],
                                                ),
                                              )),
                                            );
                                          }).toList(),
                                        ),
                                      )
                                    : Container()))
                        : null,
                  ),
                  body: BlocBuilder<CenterSelectorBloc, CenterSelectorState>(
                      builder: (context, state) {
                    if (state is BatchSelectorLoadedState) {
                      BatchSelectorScreenData screenData = state.screenData;
                      if (screenData.batchesData != null &&
                          screenData.batchesData!.isNotEmpty &&
                          _tabController != null) {
                        return Stack(
                          children: [
                            Positioned.fill(
                              child: TabBarView(
                                controller: _tabController,
                                children: screenData.batchesData!.map((batch) {
                                  return batch.centersData != null &&
                                          batch.centersData!.isNotEmpty
                                      ? getTabView(
                                          screenData,
                                          batch.centersData!,
                                          batch.id,
                                          batch,
                                          state.screenData.bannerFormData)
                                      : Container();
                                }).toList(),
                              ),
                            ),
                            // if (screenData.action != null)
                            //   Positioned(
                            //     left: Spacings.x4,
                            //     right: Spacings.x4,
                            //     bottom: Spacings.x4,
                            //     child: PrimaryButton(() {
                            //       saveSlot(screenData);
                            //     }, screenData.action!.title ?? ""),
                            //   ),
                          ],
                        );
                      } else {
                        return Center(
                          child: Container(
                            child: Text(
                              state.screenData.errorMessage ??
                                  "No center available in your city",
                              style: AuroraTheme.of(context)
                                  .textStyle(TypescaleValues.H1),
                            ),
                          ),
                        );
                      }
                    } else if (state is CenterSelectorLoadingState) {
                      return Center(
                        child: FancyLoadingIndicator(color: Colors.white),
                      );
                    } else if (state is CenterSelectorErrorState) {
                      return ErrorScreen(
                          errorInfo: state.errorInfo ?? UnknownError());
                    }
                    return SizedBox();
                  }),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget getTabView(
      BatchSelectorScreenData screenData,
      List<CenterData> centerData,
      String? tabBatchId,
      BatchData batch,
      BannerFormData? bannerFormData) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Stack(
        children: [
          SingleChildScrollView(
            child: Container(
              child: Column(
                children: [
                  if (batch.revisedTimingText != null)
                    SizedBox(
                      height: 20,
                    ),
                  if (batch.revisedTimingText != null)
                    BlurView(
                      borderRadius: 10,
                      child: Container(
                        width: scale(context, 331),
                        height: scale(context, 52),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SizedBox(
                              width: 15,
                            ),
                            Icon(
                              const IconData(0xf02a1,
                                  fontFamily: 'MaterialIcons'),
                              size: 24,
                              color: Colors.white,
                            ),
                            SizedBox(
                              width: 10,
                            ),
                            Flexible(
                              child: Text(
                                batch.revisedTimingText!,
                                maxLines: 2,
                                style: AuroraTheme.of(context)
                                    .textStyle(TypescaleValues.P8),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ...centerData
                      .map<Widget>(
                        (center) => Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            centerData.indexOf(center) != 0
                                ? Container(
                                    margin: EdgeInsets.symmetric(
                                        vertical: Spacings.x5,
                                        horizontal: Spacings.x4),
                                    width: double.infinity,
                                    height: 1,
                                    color: Colors.white12,
                                  )
                                : Container(
                                    margin: EdgeInsets.only(top: Spacings.x5)),
                            getCenterSlotsView(screenData, center),
                          ],
                        ),
                      )
                      .toList(),
                  if (bannerFormData != null)
                    Padding(
                      padding: const EdgeInsets.only(
                          left: Spacings.x4,
                          right: Spacings.x4,
                          top: Spacings.x8),
                      child: InkWell(
                        onTap: () {
                          if (bannerFormData.action != null) {
                            clickActionWithAnalytics(
                                bannerFormData.action!,
                                context,
                                null,
                                {"trigger": "Banner Action for center"});
                          }
                        },
                        child: BlurView(
                          child: Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: Spacings.x4, vertical: Spacings.x3),
                            child: Row(
                              children: [
                                Icon(
                                  Icons.error,
                                  color: Colors.white,
                                  size: 20,
                                ),
                                Expanded(
                                  child: Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: Spacings.x3),
                                    child: Text(
                                      bannerFormData.title ?? "",
                                      style: AuroraTheme.of(context)
                                          .textStyle(TypescaleValues.P4),
                                    ),
                                  ),
                                ),
                                Icon(
                                  Icons.chevron_right,
                                  color: Colors.white,
                                  size: 20,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  SizedBox(
                    height: Spacings.x18,
                  ),
                ],
              ),
            ),
          ),
          if (screenData.action != null)
            Positioned(
              bottom: 16.0,
              left: 16.0,
              right: 16.0,
              child: FloatingButton(
                onPress: () {
                  ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
                  PerformActionEvent event =
                      PerformActionEvent(screenData.action!);
                  actionBloc.add(event);
                },
                buttonText: 'TALK TO US',
              ),
            ),
        ],
      ),
    );
  }

  Widget getCenterSlotsView(
      BatchSelectorScreenData screenData, CenterData center) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: Spacings.x4),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              InkWell(
                onTap: () {
                  if (center.action != null) {
                    modalOpened = true;
                    clickActionWithAnalytics(center.action!, context, null,
                        {"centerId": center.id, "page": "batch selection"});
                  }
                },
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(scale(context, 10)),
                  child: CFNetworkImage(
                    imageUrl:
                        getImageUrl(context, imagePath: center.imageUrl ?? ""),
                    fit: BoxFit.cover,
                    width: scale(context, 100),
                    height: scale(context, 100),
                  ),
                ),
              ),
              SizedBox(
                width: Spacings.x3,
              ),
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (center.tagsImage != null &&
                        center.tagsImage!.length > 0)
                      Padding(
                        padding: const EdgeInsets.only(bottom: Spacings.x1),
                        child: Wrap(
                          spacing: 5,
                          runSpacing: 5,
                          children: center.tagsImage!.map<Widget>((tag) {
                            return CFNetworkImage(
                              imageUrl: getImageUrl(context, imagePath: tag),
                              height: 20,
                              fit: BoxFit.cover,
                            );
                          }).toList(),
                        ),
                      ),
                    if (center.title != null)
                      InkWell(
                        onTap: () {
                          if (center.action != null) {
                            modalOpened = true;
                            clickActionWithAnalytics(
                                center.action!, context, null, {
                              "centerId": center.id,
                              "page": "batch selection"
                            });
                          }
                        },
                        child: RichText(
                          textAlign: TextAlign.start,
                          text: TextSpan(children: [
                            if (center.title != null)
                              TextSpan(
                                text: center.title,
                                style: AuroraTheme.of(context)
                                    .textStyle(TypescaleValues.H4),
                              ),
                            if (center.action != null)
                              TextSpan(
                                text: " ›",
                                style: AuroraTheme.of(context)
                                    .textStyle(TypescaleValues.H1),
                              )
                          ]),
                        ),
                      ),
                    Padding(
                      padding: EdgeInsets.only(top: Spacings.x1),
                      child: Row(
                        children: [
                          if (center.distanceText != null)
                            Container(
                              child: Text(
                                center.distanceText ?? "",
                                style: AuroraTheme.of(context).textStyle(
                                    TypescaleValues.P6,
                                    color: Colors.white60),
                              ),
                            ),
                          if (center.distanceText != null &&
                              center.slotsText != null)
                            Container(
                              child: Text(
                                " • ",
                                style: AuroraTheme.of(context).textStyle(
                                    TypescaleValues.P6,
                                    color: Colors.white60),
                              ),
                            ),
                          if (center.slotsText != null)
                            Container(
                              child: Text(
                                center.slotsText ?? "",
                                style: AuroraTheme.of(context).textStyle(
                                  TypescaleValues.P6,
                                  color: HexColor.fromHex(
                                      center.hexColor ?? "#FFFFFF"),
                                ),
                              ),
                            ),
                        ],
                      ),
                    ),
                    if (center.slots != null)
                      Padding(
                        padding: EdgeInsets.only(top: Spacings.x2),
                        child: Wrap(
                          key: Key(batchId + centerId + slotId),
                          spacing: 10,
                          runSpacing: 10,
                          children: [
                            for (Slots slot in center.slots!)
                              (slot.enabled ?? true)
                                  ? (slot.isActionSlot)
                                      ? SecondaryButton(
                                          () {
                                            if (slot.action != null) {
                                              clickActionWithAnalytics(
                                                  slot.action!,
                                                  context,
                                                  null,
                                                  {"trigger": "slotAction"});
                                            }
                                          },
                                          slot.title ?? "",
                                          expanded: false,
                                          enabled: true,
                                          buttonType: SecondaryButtonType.SMALL,
                                        )
                                      : SecondaryButton(
                                          () {
                                            slotId = slot.id ?? "";
                                            centerId = center.id ?? "";
                                            RepositoryProvider.of<
                                                        AnalyticsRepository>(
                                                    context)
                                                .logButtonClickEvent(
                                                    extraInfo: {
                                                  "trigger": "SLOT_SELECTION",
                                                  "slotId": slotId,
                                                  "centerId": centerId,
                                                });
                                            saveSlot(screenData, slot.action);
                                          },
                                          slot.title ?? "",
                                          expanded: false,
                                          enabled: true,
                                          buttonType: SecondaryButtonType.SMALL,
                                        )
                                  : Opacity(
                                      opacity: 0.4,
                                      child: SecondaryButton(
                                        () {},
                                        slot.title ?? "",
                                        expanded: false,
                                        enabled: false,
                                        buttonType: SecondaryButtonType.SMALL,
                                      ),
                                    ),
                          ],
                        ),
                      ),
                    if (center.revisedTimingText != null)
                      SizedBox(
                        height: 5,
                      ),
                    if (center.revisedTimingText != null)
                      Text(
                        center.revisedTimingText ?? "asc",
                        style: AuroraTheme.of(context).textStyle(
                            TypescaleValues.P10,
                            color: Colors.white.withOpacity(0.6)),
                      )
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

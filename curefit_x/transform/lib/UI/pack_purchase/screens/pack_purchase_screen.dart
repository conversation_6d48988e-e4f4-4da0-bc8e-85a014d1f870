import 'package:common/ui/alert_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:transform/UI/pack_purchase/widgets/product_summary_widget.dart';
import 'package:transform/blocs/pack_purchase/events.dart';
import 'package:transform/blocs/pack_purchase/pack_purchase_bloc.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/pack_purchase/state.dart';
import 'package:common/ui/widget_factory.dart';
import 'package:common/ui/loading_indicator.dart';

class PackPurchaseScreen extends StatefulWidget {
  @override
  _PackPurchaseScreenState createState() => _PackPurchaseScreenState();
}

class _PackPurchaseScreenState extends State<PackPurchaseScreen> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final packPurchaseBloc = BlocProvider.of<PackPurchaseBloc>(context);
    packPurchaseBloc.add(LoadPackPurchaseEvent());
    WidgetFactory widgetFactory = RepositoryProvider.of<WidgetFactory>(context);
    return Scaffold(
        backgroundColor: Colors.black,
        body: BlocListener<PackPurchaseBloc, PackPurchaseState>(
            listener: (context, state) {
          if (state is PackPurchaseNotLoaded) {
            showErrorAlert(
                context: context,
                title: state.error,
                onClose: () {
                  Navigator.pop(context);
                });
          }
        }, child: BlocBuilder<PackPurchaseBloc, PackPurchaseState>(
          builder: (context, state) {
            if (state is PackPurchaseLoaded) {
              List widgets = state.packPurchasePage.widgets!;
              final widgetViews = widgetFactory.createWidgets(widgets);
              return Padding(
                padding: EdgeInsets.symmetric(horizontal: 20),
                child: AnimationLimiter(
                    child: ListView.builder(
                        itemCount: widgetViews.length,
                        itemBuilder: (context, index) {
                          Widget widget = widgetViews[index];
                          if (widget is ProductSummaryWidgetView)
                            return Container();
                          return AnimationConfiguration.staggeredList(
                            position: index,
                            duration: const Duration(milliseconds: 350),
                            child: SlideAnimation(
                              horizontalOffset:
                                  MediaQuery.of(context).size.width / 2,
                              child: FadeInAnimation(
                                child: widget,
                              ),
                            ),
                          );
                        })),
              );
            } else {
              return Center(
                  child: PageLoadingIndicator(
                color: Colors.black,
              ));
            }
          },
        )));
  }
}

import 'package:common/action/action_handler.dart' as ActionHandler;
import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/flutter_scale_tap.dart';
import 'package:common/data/cf_text_data.dart';
import 'package:common/ui/molecules/plan_card.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit/ui_utils.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/pack_purchase/models.dart';

class CultPackBrowseWidgetView extends StatelessWidget {
  final CultPackBrowseWidgetData cultPackBrowseWidgetData;

  const CultPackBrowseWidgetView(this.cultPackBrowseWidgetData);

  @override
  Widget build(BuildContext context) {
    return Padding(
        padding: EdgeInsets.symmetric(horizontal: Spacings.x4),
        child: Column(children: [
          for (var item in cultPackBrowseWidgetData.items)
            CultPackBrowseWidgetItemView(
              item,
              widgetInfo: cultPackBrowseWidgetData.widgetInfo,
            )
        ]));
  }
}

class CultPackBrowseWidgetItemView extends StatelessWidget {
  final CultPackBrowseWidgetItemData cultPackBrowseWidgetItemData;
  final WidgetInfo? widgetInfo;

  const CultPackBrowseWidgetItemView(this.cultPackBrowseWidgetItemData,
      {this.widgetInfo});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: cultPackBrowseWidgetItemData.simplifyHeader == true
          ? CrossAxisAlignment.start
          : CrossAxisAlignment.center,
      children: [
        if (cultPackBrowseWidgetItemData.simplifyHeader == true &&
            cultPackBrowseWidgetItemData.header.title != null)
          Padding(
            padding: const EdgeInsets.only(top: Spacings.x2),
            child: UiUtils.getRichText(
                context,
                cultPackBrowseWidgetItemData.header.title ?? "",
                "#ffffff",
                "H2"),
          ),
        if (cultPackBrowseWidgetItemData.simplifyHeader != true) ...[
          if (cultPackBrowseWidgetItemData.header.title != null)
            Padding(
                child: UiUtils.getRichText(
                    context,
                    cultPackBrowseWidgetItemData.header.title ?? "",
                    "#ffffff",
                    "H9"),
                padding: EdgeInsets.only(top: 30)),
          if (cultPackBrowseWidgetItemData.header.subtitle != null)
            Padding(
                child: Text(cultPackBrowseWidgetItemData.header.subtitle ?? "",
                    textAlign: TextAlign.center,
                    style:
                        AuroraTheme.of(context).textStyle(TypescaleValues.P5)),
                padding: EdgeInsets.only(top: 10)),
          if (cultPackBrowseWidgetItemData.header.background != null &&
              cultPackBrowseWidgetItemData.header.background!.isNotEmpty)
            Padding(
                padding: EdgeInsets.only(top: Spacings.x4),
                child: CFNetworkImage(
                    imageUrl: getImageUrl(context,
                        imagePath:
                            cultPackBrowseWidgetItemData.header.background ??
                                "")))
        ],
        SizedBox(
          height: cultPackBrowseWidgetItemData.topSpacing != null
              ? double.tryParse(cultPackBrowseWidgetItemData.topSpacing ?? "10")
              : cultPackBrowseWidgetItemData.simplifyHeader == true
                  ? Spacings.x6
                  : Spacings.x9,
        ),
        Column(children: [
          for (var item in cultPackBrowseWidgetItemData.packs)
            CultPackDetailWidgetBody(
              item,
              widgetInfo: this.widgetInfo,
            )
        ])
      ],
    );
  }
}

class CultPackDetailWidgetBody extends StatelessWidget {
  final WidgetInfo? widgetInfo;
  final CultPackDetailWidgetBodyData cultPackDetailWidgetBodyData;

  const CultPackDetailWidgetBody(this.cultPackDetailWidgetBodyData,
      {this.widgetInfo});

  @override
  Widget build(BuildContext context) {
    return Semantics(
      label: "FITNESS_PACK_WIDGET_" + cultPackDetailWidgetBodyData.title,
      child: ScaleTap(
          onTap: () {
            ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
            ActionHandler.Action action = ActionHandler.Action.fromAction(
                cultPackDetailWidgetBodyData.action, {
              "heroMeta": {
                "title": cultPackDetailWidgetBodyData.title,
                "subTitle": cultPackDetailWidgetBodyData.subtitle,
                "hexColor": cultPackDetailWidgetBodyData.textColorVariant
              }
            });
            PerformActionEvent event = PerformActionEvent(action);
            actionBloc.add(event);
            RepositoryProvider.of<AnalyticsRepository>(context)
                .logWidgetClick(widgetInfo: widgetInfo, extraInfo: {
              "type":
                  '${cultPackDetailWidgetBodyData.title.toLowerCase()}_${cultPackDetailWidgetBodyData.subtitle.toLowerCase()}',
              ...?action.analyticsData
            });
          },
          child: PlanCard(
              title: cultPackDetailWidgetBodyData.title,
              subtitle: cultPackDetailWidgetBodyData.subtitle,
              textColorVariant: cultPackDetailWidgetBodyData.textColorVariant,
              priceMeta: cultPackDetailWidgetBodyData.priceMeta,
              price: PlanCardPrice(
                  cultPackDetailWidgetBodyData.price.mrp,
                  cultPackDetailWidgetBodyData.price.listingPrice,
                  cultPackDetailWidgetBodyData.price.currency,
                  cultPackDetailWidgetBodyData.price.showPriceCut),
              offers: cultPackDetailWidgetBodyData.offers
                  ?.map((e) => OfferData(
                      title: e.title ?? "", iconType: e.iconType ?? ""))
                  .toList(),
              actionOffers: cultPackDetailWidgetBodyData.actionOffers
                  ?.map((e) => ActionOfferData(
                      action: e.action,
                      prefix: e.prefix,
                      postfix: e.postfix,
                      iconType: e.iconType ?? ""))
                  .toList(),
              priceBreakup: cultPackDetailWidgetBodyData.priceBreakup,
            additionalPrice: cultPackDetailWidgetBodyData.additionalPrice,
          ),),
    );
  }
}

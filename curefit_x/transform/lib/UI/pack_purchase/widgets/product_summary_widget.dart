import 'package:common/ui/components/cf_network_image.dart';
import 'package:flutter/material.dart';
import 'package:transform/factory/widget_data.dart';
import 'package:common/image/image_url_generator.dart';

class ProductSummaryWidgetView extends StatelessWidget {
  ProductSummaryWidgetView(this.productSummaryWidget);

  final ProductSummaryWidgetData productSummaryWidget;

  @override
  Widget build(BuildContext context) {
    return CFNetworkImage(
      fit: BoxFit.cover,
      imageUrl: getImageUrl(context, imagePath: productSummaryWidget.image!),
      errorWidget: (context, url, error) => Icon(Icons.error),
    );
  }
}

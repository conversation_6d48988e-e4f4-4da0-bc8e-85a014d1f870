import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:flutter/material.dart';

class GradientText extends StatelessWidget {
  GradientText(this.text, this.gradient);

  final String text;
  final Gradient gradient;

  @override
  Widget build(BuildContext context) {
    return ShaderMask(
      shaderCallback: (bounds) => gradient.createShader(
        Rect.fromLTRB(0, 0, bounds.width, bounds.height),
      ),
      child: Text(
        text,
        textAlign: TextAlign.center,
        style: AuroraTheme.of(context).textStyle(TypescaleValues.P3,
          // The color must be set to white for this to work
          color: Colors.white,
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';

class ActionButtonData {
  ActionButtonData(this.title, this.meta);

  String? title;
  Map<String, String> meta;
}

class ActionButtonView extends StatelessWidget {
  ActionButtonView(this.actionButton);

  final ActionButtonData actionButton;

  @override
  Widget build(BuildContext context) {
    return ConstrainedBox(
        constraints: const BoxConstraints(minWidth: double.infinity),
        child: Container(
            padding: EdgeInsets.all(20),
            child: ElevatedButton(
              child: Text(actionButton.title!,
                  style: TextStyle(
                      fontWeight: FontWeight.w700,
                      fontSize: 15,
                      color: Colors.white,
                      decoration: TextDecoration.none)),
              onPressed: () {
                Navigator.pushNamed(context, "/tf_checkout");
              },
              style: ElevatedButton.styleFrom(
                foregroundColor: Colors.white,
                backgroundColor: Color.fromRGBO(34, 34, 34, 1),
                padding: EdgeInsets.all(15),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10.0),
                ),
              ),
            )));
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/pack_purchase/events.dart';
import 'package:transform/blocs/pack_purchase/pack_purchase_bloc.dart';
import 'package:transform/factory/widget_data.dart';
import 'package:common/ui/flutter_scale_tap.dart';

class ProductPricingWidgetView extends StatelessWidget {
  ProductPricingWidgetView(this.productPricingWidget);

  final ProductPricingWidgetData productPricingWidget;

  @override
  Widget build(BuildContext context) {
    return Container(
        transform: Matrix4.translationValues(0.0, -20.0, 0.0),
        child: Column(children: [
          for (var section in productPricingWidget.sections!)
            SectionView(section)
        ]));
  }
}

class SectionView extends StatefulWidget {
  SectionView(this.section);

  final Section section;

  @override
  State<SectionView> createState() => _SectionViewState();
}

class _SectionViewState extends State<SectionView> {
  Pack? selectedPack;

  @override
  void initState() {
    selectedPack =
        widget.section.packs!.firstWhere((element) => element.selected!);
    // TODO: implement initState
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
        padding: EdgeInsets.only(top: 0, left: 20, right: 20),
        child: Column(children: [
          for (var pack in widget.section.packs!)
            PackView(pack, selectedPack, (pack) {
              setState(() {
                PackPurchaseBloc packBloc =
                    BlocProvider.of<PackPurchaseBloc>(context);
                selectedPack = pack;
                packBloc.add(SelectPackEvent(pack));
              });
            })
        ]));
  }
}

class PackView extends StatelessWidget {
  final Pack? selectedPack;
  final Function onPackSelected;

  PackView(this.pack, this.selectedPack, this.onPackSelected);

  final Pack pack;

  Widget buildValuePoints() {
    final valueProps = [
      "Personalised Workout and Nutrition plans",
      "Weekly catchup with your coach",
    ];

    List<Widget> widgets = [];
    for (int count = 0; count < valueProps.length; count++) {
      Icon? icon;
      switch (count) {
        case 0:
          icon = Icon(
            Icons.check_rounded,
            color: Colors.black,
            size: 20,
          );
          break;
        case 1:
          icon = Icon(Icons.check_rounded, color: Colors.black, size: 20);
          break;
        case 2:
          icon = Icon(
            Icons.flag,
            color: Colors.pink,
          );
      }
      widgets.add(Padding(
          padding: EdgeInsets.only(top: 10),
          child: Row(children: [
            icon!,
            Padding(
                padding: EdgeInsets.only(left: 10),
                child: Text(
                  valueProps[count],
                  style: TextStyle(fontSize: 13),
                ))
          ])));
    }

    return Column(children: widgets);
  }

  @override
  Widget build(BuildContext context) {
    return ScaleTap(
        onTap: () {
          this.onPackSelected(this.pack);
        },
        child: Container(
            padding: const EdgeInsets.only(bottom: 15),
            child: Column(children: [
              Container(
                padding: const EdgeInsets.only(
                    top: 15, bottom: 15, left: 25, right: 25),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10.0),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.2),
                      spreadRadius: 2,
                      blurRadius: 2,
                      offset: Offset(0, 3),
                    ),
                  ],
                ),
                child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(children: [
                              Container(
                                decoration: BoxDecoration(
                                    border: Border.all(
                                      width: 1,
                                      color: pack == selectedPack
                                          ? Colors.pinkAccent
                                          : Colors.grey,
                                      style: BorderStyle.solid,
                                    ),
                                    borderRadius: BorderRadius.circular(20),
                                    //  shape: BoxShape.circle,
                                    color: pack == selectedPack
                                        ? Colors.pinkAccent
                                        : Colors.white),
                                child: Padding(
                                  padding: const EdgeInsets.all(2.0),
                                  child: pack == selectedPack
                                      ? Icon(
                                          Icons.check,
                                          color: Colors.white,
                                          size: 15,
                                        )
                                      : SizedBox(
                                          height: 15,
                                          width: 15,
                                        ),
                                ),
                              ),
                              SizedBox(
                                width: 15,
                              ),
                              Text(
                                pack.subtitle!.split(' ').sublist(1).join(' '),
                                style: TextStyle(
                                    fontWeight: FontWeight.w700,
                                    fontSize: 20,
                                    color: Color.fromRGBO(34, 34, 34, 1),
                                    decoration: TextDecoration.none),
                              )
                            ]),
                            Column(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  Text(
                                    Price.displayPrice(
                                        pack.price.listingPrice.toString(),
                                        pack.price.currency),
                                    style: TextStyle(
                                        fontWeight: FontWeight.w700,
                                        fontSize: 20,
                                        color: Color.fromRGBO(34, 34, 34, 1),
                                        decoration: TextDecoration.none),
                                  ),
                                  SizedBox(
                                    height: 5,
                                  ),
                                  Text(
                                    Price.displayPrice(
                                        pack.price.mrp.toString(),
                                        pack.price.currency),
                                    style: TextStyle(
                                        fontWeight: FontWeight.w300,
                                        fontSize: 14,
                                        color: Color.fromRGBO(132, 146, 158, 1),
                                        decorationColor:
                                            Color.fromRGBO(132, 146, 158, 1),
                                        decoration: TextDecoration.lineThrough),
                                  ),
                                ]),
                          ]),
                      SizedBox(
                        height: 15,
                      ),
                      buildValuePoints()
                    ]),
              ),

              /*  Container(
          alignment: Alignment.topLeft,
          padding: const EdgeInsets.only(top: 22, left: 5, right: 5),
          child:
              Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Text(
              "Offers",
              style: TextStyle(
                  fontWeight: FontWeight.w700,
                  fontSize: 20,
                  color: Color.fromRGBO(34, 34, 34, 1),
                  decoration: TextDecoration.none),
            ),
            for (var offer in [
              "Get FREE 1 Month Extension",
              "No Cost EMI Available"
            ])
              OfferView(offer),
            SizedBox(height: 30)
          ]))*/
            ])));
  }
}

import 'package:common/ui/theme/Colors.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:flutter/material.dart';
import 'package:transform/blocs/content/models.dart';
import 'package:transform/blocs/content_history/models.dart';

import 'chapter_status_item.dart';

class CoachArchiveModuleWidget extends StatefulWidget {
  final CoachArchiveModuleModel item;

  const CoachArchiveModuleWidget({
    Key? key,
    required this.item,
  }) : super(key: key);

  @override
  State<CoachArchiveModuleWidget> createState() => _CoachArchiveModuleWidgetState();
}

class _CoachArchiveModuleWidgetState extends State<CoachArchiveModuleWidget> {
  final double STATUS_ICON_SIZE = 28;
  bool? isExpanded = false;

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: Theme.of(context).copyWith(dividerColor: Colors.transparent),
      child: Stack(
        children: [
          Container(
            padding: const EdgeInsets.only(
                right: Spacings.x4, top: Spacings.x2, left: Spacings.x4),
            decoration: BoxDecoration(
                border: Border(bottom: BorderSide(color: Colors.white24))),
            child: ExpansionTile(
                iconColor: Colors.white,
                tilePadding: const EdgeInsets.all(0),
                childrenPadding: const EdgeInsets.all(0),
                collapsedIconColor: Colors.white,
                initiallyExpanded: widget.item.isCurrentWeek == true,
                onExpansionChanged: (expanded) {
                  setState(() {
                    isExpanded = expanded;
                  });
                },
                key: PageStorageKey<CoachArchiveModuleModel>(widget.item),
                title: Container(
                  height: 60,
                  child: Row(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      _getWeekStatus(),
                      SizedBox(width: Spacings.x3),
                      Expanded(
                        child: Row(
                          mainAxisSize: MainAxisSize.max,
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Expanded(
                              child: Text(
                                widget.item.title,
                                overflow: TextOverflow.ellipsis,
                                style: AuroraTheme.of(context)
                                    .textStyle(TypescaleValues.H4),
                              ),
                            ),
                            SizedBox(
                              width: 10,
                            ),
                            Text(
                              widget.item.subTitle ?? "",
                              overflow: TextOverflow.ellipsis,
                              style: AuroraTheme.of(context)
                                  .textStyle(TypescaleValues.P8),
                            )
                          ],
                        ),
                      )
                    ],
                  ),
                ),
                children: (widget.item.chapters != null)
                    ? widget.item.chapters!
                        .map((e) => ChapterStatusItem(item: e))
                        .toList()
                    : []),
          ),
          _treeLine()
        ],
      ),
    );
  }

  Widget _getWeekStatus() {
    if (this.widget.item.isCurrentWeek == true &&
        this.widget.item.status != UserChapterStatus.COMPLETED) {
      return Stack(
        alignment: Alignment.center,
        children: [
          Container(
            width: STATUS_ICON_SIZE,
            height: STATUS_ICON_SIZE,
            decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: Colors.white.withOpacity(0.2))),
          ),
          Container(
            width: STATUS_ICON_SIZE - 9,
            height: STATUS_ICON_SIZE - 9,
            decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: Colors.white.withOpacity(0.5))),
          ),
          Container(
            width: STATUS_ICON_SIZE - 17,
            height: STATUS_ICON_SIZE - 17,
            decoration: BoxDecoration(
                color: Colors.white12,
                shape: BoxShape.circle,
                border: Border.all(color: Colors.white, width: 1.5)),
          )
        ],
      );
    } else if (this.widget.item.status == UserChapterStatus.COMPLETED) {
      return Container(
          width: STATUS_ICON_SIZE,
          height: STATUS_ICON_SIZE,
          child: Icon(Icons.check,
              size: STATUS_ICON_SIZE - 8, color: ColorPalette.statusPositive));
    }
    return Stack(
        alignment: Alignment.center,
        clipBehavior: Clip.antiAlias,
        children: [
          Container(
            width: STATUS_ICON_SIZE,
            height: STATUS_ICON_SIZE,
            alignment: Alignment.center,
            child: Container(
                width: STATUS_ICON_SIZE - 17,
                height: STATUS_ICON_SIZE - 17,
                decoration: BoxDecoration(
                    color: Colors.white12,
                    shape: BoxShape.circle,
                    border: Border.all(color: Colors.white.withOpacity(0.5)))),
          )
        ]);
  }

  Widget _treeLine() {
    return isExpanded == true &&
            this.widget.item.status != UserChapterStatus.COMPLETED
        ? Positioned(
            top: 50,
            left: 33,
            child: Container(
                width: 1,
                height: 29,
                decoration: BoxDecoration(
                  color: Colors.white24,
                )),
          )
        : SizedBox();
  }
}

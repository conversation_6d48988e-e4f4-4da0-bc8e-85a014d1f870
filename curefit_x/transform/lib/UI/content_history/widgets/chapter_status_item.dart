import 'package:common/ui/theme/Colors.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/util/action_util.dart';
import 'package:flutter/material.dart';
import 'package:transform/blocs/content/models.dart';
import 'package:transform/blocs/content_history/models.dart';

class ChapterStatusItem extends StatelessWidget {
  final ChapterTileInfo item;
  final double STATUS_ICON_SIZE = 7;
  const ChapterStatusItem({
    Key? key,
    required this.item,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    var isProgress = item.status != UserChapterStatus.COMPLETED;
    return ListTile(
        contentPadding: const EdgeInsets.all(0),
        minVerticalPadding: 0,
        title: InkWell(
          onTap: () {
            if (item.action != null) {
              clickActionWithAnalytics(
                  item.action!, context, item.widgetInfo, {});
            }
          },
          child: Container(
            height: 90,
            child: Stack(
              children: [
                isProgress
                    ? Positioned(
                        top: 18,
                        left: 14,
                        child: Container(
                          width: 22,
                          height: 1,
                          decoration: BoxDecoration(color: Colors.white24),
                        ),
                      )
                    : SizedBox(),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    isProgress
                        ? Container(
                            width: 27,
                            height: double.infinity,
                            alignment: Alignment.topCenter,
                            child: Container(
                              width: 1,
                              height:
                                  item.isLastUnread ? 19.5 : double.infinity,
                              decoration: BoxDecoration(color: Colors.white24),
                            ),
                          )
                        : SizedBox(width: 30),
                    Container(
                      width: 28,
                      height: 28,
                      padding: const EdgeInsets.only(top: 15),
                      alignment: Alignment.topCenter,
                      child: _getChapterStatus(),
                    ),
                    SizedBox(width: 10),
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.only(top: 8.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Flexible(
                                  child: Text(
                                    item.title ?? "",
                                    overflow: TextOverflow.ellipsis,
                                    maxLines: 2,
                                    style: AuroraTheme.of(context).textStyle(
                                        isProgress
                                            ? TypescaleValues.P3
                                            : TypescaleValues.P5),
                                  ),
                                ),
                                isProgress
                                    ? Padding(
                                        padding:
                                            const EdgeInsets.only(left: 5.0),
                                        child: Icon(
                                          Icons.arrow_forward,
                                          size: 15,
                                          color: Colors.white,
                                        ),
                                      )
                                    : SizedBox()
                              ],
                            ),
                            item.subTitle != null
                                ? Padding(
                                    padding:
                                        const EdgeInsets.only(top: Spacings.x1),
                                    child: Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.center,
                                      children: [
                                        Icon(Icons.arrow_right,
                                            color: Colors.white54, size: 22),
                                        SizedBox(width: 2),
                                        Text(
                                          item.subTitle!,
                                          overflow: TextOverflow.ellipsis,
                                          style: AuroraTheme.of(context)
                                              .textStyle(TypescaleValues.P8,
                                                  color: Colors.white54),
                                        )
                                      ],
                                    ),
                                  )
                                : SizedBox(),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(
                      width: 10,
                    )
                  ],
                )
              ],
            ),
          ),
        ));
  }

  Widget _getChapterStatus() {
    if (this.item.status != UserChapterStatus.COMPLETED) {
      return Container(
        width: STATUS_ICON_SIZE,
        height: STATUS_ICON_SIZE,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: Colors.white,
        ),
      );
    }
    return Container(
        width: STATUS_ICON_SIZE + 5,
        height: STATUS_ICON_SIZE + 5,
        child: Icon(Icons.check,
            size: STATUS_ICON_SIZE + 5, color: ColorPalette.statusPositive));
  }
}

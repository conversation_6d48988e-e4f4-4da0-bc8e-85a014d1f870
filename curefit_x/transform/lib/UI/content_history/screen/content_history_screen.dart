import 'dart:io';

import 'package:common/action/action_handler.dart' as ActionHandler;
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/constants/action_constants.dart';
import 'package:common/font/cf_icons.dart';
import 'package:common/ui/alert_view.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/widget_factory.dart';
import 'package:flutter/material.dart' hide Action;
import 'package:enum_to_string/enum_to_string.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/content_history/content_history_bloc.dart';
import 'package:transform/blocs/content_history/events.dart';

class ContentHistoryPageArguments {
  String? courseId;
  String? subCategoryCode;

  ContentHistoryPageArguments(Map<String, dynamic> payload) {
    this.courseId = payload["courseId"];
    this.subCategoryCode =
    payload["subCategoryCode"] != null ? payload["subCategoryCode"] : "";
  }
}

class ContentHistoryPage extends StatefulWidget {
  ContentHistoryPage({Key? key}) : super(key: key);

  @override
  State<ContentHistoryPage> createState() => _ContentHistoryPageState();
}

class _ContentHistoryPageState extends State<ContentHistoryPage> {
  bool showingError = false;

  ContentHistoryPageArguments? contentHistoryPageArguments() {
    final ActionHandler.ScreenArguments? args = ModalRoute
        .of(context)!
        .settings
        .arguments as ActionHandler.ScreenArguments?;
    if (args != null) {
      return ContentHistoryPageArguments(args.params);
    }
    return null;
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ContentHistoryPageArguments? arguments = contentHistoryPageArguments();
      String? courseId = arguments?.courseId;
      String? subCategoryCode = arguments?.subCategoryCode;
      ContentHistoryBloc bloc = BlocProvider.of<ContentHistoryBloc>(context);
      bloc.add(LoadContentHistoryEvent(courseId: courseId, subCategoryCode: subCategoryCode));
    });
  }

  Widget buildWidgets() {
    return BlocListener<ContentHistoryBloc, ContentHistoryBlocState>(
        listener: (context, state) {
          if (state is ContentHistoryErrorState && state.exception != null) {
            if (state.exception != null &&
                ((state.exception!.title ?? "").isNotEmpty ||
                    (state.exception!.subTitle ?? "").isNotEmpty)) {
              showNetworkExceptionAlert(
                  context: context,
                  networkException: state.exception!,
                  onPress: (Map<String, dynamic>? action) {
                    if (action != null) {
                      performAlertButtonAction(context, action);
                    }
                  });
            } else {
              showErrorAlert(context: context, title: state.exception?.title);
            }
          }
        }, child: BlocBuilder<ContentHistoryBloc, ContentHistoryBlocState>(
        builder: (context, state) {
          if (state is ContentHistoryLoadingState) {
            return FancyLoadingIndicator();
          }
          if (state is ContentHistoryLoadedState &&
              state.screenData.widgets.isNotEmpty) {
            WidgetFactory widgetFactory =
            RepositoryProvider.of<WidgetFactory>(context);
            Widget child = widgetFactory.createWidget(
                state.screenData.widgets.first);
            return child;
          }
          return Container();
        }));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.black,
        extendBodyBehindAppBar: true,
        appBar: PreferredSize(
          preferredSize: Size.fromHeight(50.0 + AuroraTheme.of(context).embeddedSafeArea.top),
          child: Padding(
            padding: EdgeInsets.only(
                top: AuroraTheme.of(context).embeddedSafeArea.top),
            child: AppBar(
              backgroundColor: Colors.transparent,
              iconTheme: IconThemeData(color: Colors.white),
              leading: IconButton(
                icon: const Icon(
                  CFIcons.chevron_left,
                  color: Colors.white,
                  size: 20,
                  semanticLabel: "chevron_left",
                ),
                onPressed: () {
                  if (Navigator.canPop(context)) {
                    Navigator.pop(context);
                  } else if (!Navigator.canPop(context)) {
                    ActionBloc actionBloc =
                    BlocProvider.of<ActionBloc>(context);
                    actionBloc
                        .add(CloseApplicationEvent(shouldReset: true));
                  }
                },
              ),
            ),
          ),
        ),
        body: LayoutBuilder(
          builder: (context, constraints) {
            if (constraints.maxWidth > 0) {
              return Stack(children: [
                Aurora(
                  size: constraints.biggest,
                  context: context,
                ),
                Positioned(
                  left: 0,
                  right: 0,
                  top: 0,
                  bottom: 0,
                  child: buildWidgets(),
                ),
              ]);
            }
            return Container();
          },
        ));
  }
}

void performAlertButtonAction(context, action) {
  ActionTypes? type = action['actionType'] != null
      ? EnumToString.fromString(ActionTypes.values, action['actionType'])
      : null;
  ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
  if (type != null) {
    // Action supported in flutter
    PerformActionEvent event =
        PerformActionEvent(ActionHandler.Action.fromJson(action));
    actionBloc.add(event);
  } else if (action['actionType'] != "HIDE_ALERT_MODAL") {
    // Alert is showing in flutter so no need to send event in react native when actionType is HIDE_ALERT_MODAL
    actionBloc.add(UnsupportedActionEvent(null, false, actionJson: action));
  }
}

import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/gradient_border.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:transform/blocs/cult_habit_building/models.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/util/color.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:common/ui/theme/typescale.dart';

class HabitCardGeneric extends StatelessWidget {

  HabitCard card;
  Function onClick;

  HabitCardGeneric({Key? key,required this.card,required this.onClick}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        onClick(card);
      },
      child: Stack(
        children: [
          Container(
              width: scale(context,335),
              height: scale(context, 120),
              decoration: BoxDecoration(
                  borderRadius:
                  const BorderRadius.all(
                      Radius.circular(10)),
                  border: GradientBorder.uniform(
                      width: 1,
                      gradient: LinearGradient(
                        colors: <Color>[
                          Colors.white30.withOpacity(0.6),
                          Colors.white30.withOpacity(0.0),
                          Colors.white30.withOpacity(0.5)
                        ],
                        stops: const [0.0, 0.5, 1.0],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      )),
                  gradient: LinearGradient(
                      begin:
                      Alignment.topCenter,
                      end: Alignment
                          .bottomCenter,
                      colors: [
                        HexColor.fromHex(
                            card.backgroundColors[0]),
                        HexColor.fromHex(
                            card.backgroundColors[1])
                      ]),
                  // border: card.selected ? Border.all(
                  //     color: Colors.white,
                  //     width: 1
                  // ): null
              ),
              child: Padding(
                padding: EdgeInsets.all(scale(context, 15)),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment:
                  CrossAxisAlignment.start,
                  children: [
                    Text(
                      card.header.toUpperCase(),
                      textAlign:
                      TextAlign.center,
                      style: AuroraTheme.of(context).textStyle(TypescaleValues.P8, color: Colors.white60),
                    ),
                    Container(
                      padding: EdgeInsets.only(
                          top: 10, bottom: 10),
                      width: scale(context, 200),
                      child: Text(
                        card.subHeader,
                        style: AuroraTheme.of(context).textStyle(TypescaleValues.H2, color: Colors.white),
                      ),
                    ),
                    // Container(
                    //   width: 250,
                    //   height: 30,
                    //   child: Text(
                    //     card.secondaryText,
                    //     style: const TextStyle(
                    //       color: Colors.white60,
                    //       fontSize: 12,
                    //     ),
                    //   ),
                    // ),
                  ],
                ),
              )
          ),
          Positioned(
              top:0,
              right:0,
              child: CFNetworkImage(
                height: scale(context, 100),
                width: scale(context, 100),
                fit: BoxFit.fitWidth,
                imageUrl: getImageUrl(context,imagePath:card.url),
              )
          ),
        ],
      ),
    );
  }
}

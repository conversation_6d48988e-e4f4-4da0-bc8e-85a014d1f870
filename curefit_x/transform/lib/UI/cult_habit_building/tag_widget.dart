import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:flutter/material.dart';

class Tag extends StatefulWidget {
  final String text;
  final String code;
  List<String> selectedCodes;
  Function onClickTag;
  Tag(
      {Key? key,
        required this.text,
        required this.code,
        required this.selectedCodes,
        required this.onClickTag})
      : super(key: key);

  @override
  _TagPageState createState() => _TagPageState();
}

class _TagPageState extends State<Tag> {
  BoxDecoration selectedTagDecoration = BoxDecoration(
      boxShadow: const [BoxShadow(color: Color(0x1AFFFFFF))],
      color: Colors.white12,
      borderRadius: BorderRadius.circular(200.0),
      border: Border.all(color: Colors.white));

  BoxDecoration unSelectedTagDecoration = BoxDecoration(
      boxShadow: const [BoxShadow(color: Color(0x1AFFFFFF))],
      borderRadius: BorderRadius.circular(100.0));

  BoxDecoration tagDecoration = const BoxDecoration();

  @override
  void initState() {
    super.initState();
  }

  _changeTagState(String code) {
    widget.onClickTag(code);
  }

  @override
  Widget build(BuildContext context) {
    bool selected = widget.selectedCodes.contains(widget.code);
    return GestureDetector(
      onTap: () => _changeTagState(widget.code),
      child: Container(
        margin: const EdgeInsets.only(bottom: 10.0, right: 7),
        padding: EdgeInsets.symmetric(vertical: 8, horizontal: Spacings.x3),
        decoration: selected ? selectedTagDecoration : unSelectedTagDecoration,
        child: Text(widget.text,
            style:selected ? AuroraTheme.of(context)
                .textStyle(TypescaleValues.P3, color: Colors.white):
                  AuroraTheme.of(context)
                .textStyle(TypescaleValues.P5, color: Colors.white)),
      ),
    );
  }
}

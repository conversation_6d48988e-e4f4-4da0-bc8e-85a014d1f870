import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:numberpicker/numberpicker.dart';

class DateTimePicker extends StatefulWidget {

  // final dynamic picketObject;

  String type;
  List<int>? current;
  Function? onChange;

  DateTimePicker({Key? key,
    required this.type,
    this.current,
    this.onChange
  }) : super(key: key);

  @override
  State<DateTimePicker> createState() => _DateTimePickerState();
}

class _DateTimePickerState extends State<DateTimePicker> {

  List<int> _value1 = [];

  @override
  void initState() {
    if(widget.type == "time" && widget.current?.length != null) {
      for(int i=0 ; i<widget.current!.length ; i++) {
        List<int> currentState = _value1;
        currentState.add(widget.current![i]);
        _value1 = currentState;
      }
    }
  }

  String mapper(String s,int index) {
    if(index == 0 || index == 1) {
      if(int.parse(s) < 10) {
        return "0"+s;
      }
      return s;
    } else if(index == 2) {
      if(s=="0") {
        return "AM";
      } else if(s=="1") {
        return "PM";
      } else {
        return "";
      }
    }
    return s;
  }

  getMaxVal(String type,int index) {
    if(index==0) {
      return 12;
    } else if(index == 1) {
      return 59;
    }
    return 2;
  }

  onDataChange(int index,int value) {
    if(_value1[index] != value) {
      if(index==2 && value == 2) {
        setState(() {
          _value1[index] = 1;
        });
      } else {
        setState(() {
          _value1[index] = value;
        });
      }
    }
    if(widget.onChange != null) {
      widget.onChange!(_value1);
    }
  }

  Widget getTimerWidgets() {
    List<Widget> list = [];
    for(int i=0 ; i< _value1.length ; i++) {
      dynamic picker = _value1[i];
      list.add(
        NumberPicker(
          minValue: 0,
          maxValue: getMaxVal(widget.type, i),
          step: 1,
          value: _value1[i],
          itemCount: 3,
          haptics: true,
          // infiniteLoop: true,
          onChanged: (int value) => {
            onDataChange(i, value)
          },
          textMapper: (s) {
            return mapper(s, i);
          },
          textStyle: const TextStyle(
            color: Colors.white60,
            fontSize: 20,
          ),
          selectedTextStyle: AuroraTheme.of(context).textStyle(TypescaleValues.H1, color: Colors.white),
          itemHeight: 60,
          itemWidth: 100,
          decoration: const BoxDecoration(
            border: Border.symmetric(
              horizontal: BorderSide(
                  width: 1, color: Colors.white24),
            ),
          ),
        ),
      );
    }
    return Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: list
    );
  }

  @override
  Widget build(BuildContext context) {
    return getTimerWidgets();
  }
}

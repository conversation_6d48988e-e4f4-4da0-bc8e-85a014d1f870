import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/atoms/secondary_button.dart';
import 'package:common/ui/storyview/story_page_view.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/util/color.dart';
import 'package:transform/UI/cult_habit_building/date_time_picker_widget.dart';
import 'package:flutter/material.dart';
import 'package:transform/blocs/cult_habit_building/models.dart';

class HabitBuildEditPage extends StoryPageView {

  Pages page;
  Function responseForTimer;
  Function onTimeChange;
  Function onSave;
  Function? onNext;
  Function? onPrev;

  HabitBuildEditPage({
    Key? key,required String identifier,
    required this.page,
    required this.onTimeChange,
    required this.responseForTimer,
    required this.onSave,
    this.onNext,
    this.onPrev,
    String? backgroundColorStart,
    String? backgroundColorEnd}) :
        super(key: key, identifier: identifier, backgroundColorStart: backgroundColorStart, backgroundColorEnd: backgroundColorEnd);

  @override
  State<HabitBuildEditPage> createState() => _HabitBuildEditPageState();
}

class _HabitBuildEditPageState extends State<HabitBuildEditPage> {

  List<int> timerData = [];

  @override
  void initState() {
    timerData = widget.responseForTimer(widget.identifier,widget.page);
  }

  Widget footer() {
    List<Widget> buttons = [];
    if(widget.page.nextButton != null) {
      buttons.add(PrimaryButton(
            () {
          HabitResponse habitResponse = HabitResponse(isSelected: true, habitId: widget.identifier, response: timerData,actionID: widget.page.actionID);
          widget.onNext!(habitResponse);
        },
        widget.page.nextButton!["text"].toUpperCase(),
        iconSize: 60,
      ));
    }
    if (buttons.isNotEmpty) {
      return Container(child: PrimaryButton(
            () {
          HabitResponse habitResponse = HabitResponse(isSelected: true, habitId: widget.identifier, response: timerData,actionID: widget.page.actionID);
          widget.onNext!(habitResponse);
        },
        widget.page.nextButton!["text"].toUpperCase(),
        iconSize: 60,
      ),);
    }
    return Container();
  }

  onTimerChangeForDateTimePicker(List<int> response) {
    timerData = response;
    setState(() {
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Stack(
            children: [
              Container(
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.all(Radius.circular(10)),
                  gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        HexColor.fromHex(widget.page.backgroundColors[0]),
                        HexColor.fromHex(widget.page.backgroundColors[1])
                      ]),
                ),
              ),
              Positioned(
                left:scale(context, 300),
                top:scale(context, 50),
                child: CloseButton(
                  color: Colors.white,
                  onPressed: (){
                    if(widget.onPrev != null) {
                      widget.onPrev!();
                    }
                  },
              ),),
              Padding(
                padding: const EdgeInsets.all(30),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    SizedBox(height: scale(context, 50)),
                    Text(
                      widget.page.header.toUpperCase(),
                      textAlign: TextAlign.center,
                      style: AuroraTheme.of(context).textStyle(TypescaleValues.P6, color: Colors.white60),
                    ),
                    SizedBox(height: scale(context, 10)),
                    Text(
                      widget.page.subHeader,
                      textAlign: TextAlign.center,
                      style: AuroraTheme.of(context).textStyle(TypescaleValues.H1, color: Colors.white)),
                    SizedBox(height: scale(context, 40)),
                    DateTimePicker(
                      type: "time",
                      current: widget.responseForTimer(widget.identifier,widget.page),
                      onChange: onTimerChangeForDateTimePicker,
                    ),
                  ],
                ),
              ),
              Align(
                  alignment: Alignment.bottomCenter,
                  child: Padding(
                    padding: const EdgeInsets.only(
                        left: Spacings.x8,
                        right: Spacings.x8,
                        bottom: Spacings.x8),
                    child: footer(),
                  )),
            ],
          ),
        ],
      ),
    );
  }
}

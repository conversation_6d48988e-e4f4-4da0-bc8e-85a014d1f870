// import 'dart:html';

import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/blocs/navigation/navigation_bloc.dart';
import 'package:common/blocs/navigation/state.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/alert_view.dart';
import 'package:common/ui/atoms/floating_button.dart';
import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/storyview/story_view.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/calendar/state.dart';
import 'package:transform/blocs/cult_habit_building/cult_habit_building_bloc.dart';
import 'package:transform/blocs/cult_habit_building/cult_habit_building_event.dart';
import 'package:transform/blocs/cult_habit_building/cult_habit_building_state.dart';
import 'package:transform/blocs/cult_habit_building/models.dart';
import 'package:transform/constants/constants.dart';
import 'edit_date_time_page.dart';
import 'package:navigation_history_observer/navigation_history_observer.dart';
import 'package:common/action/action_handler.dart' as action_handler;
import 'package:enum_to_string/enum_to_string.dart';
import 'habit_card_widget.dart';
import 'package:lottie/lottie.dart';

class HabitBuiling extends StatefulWidget {
  const HabitBuiling({Key? key}) : super(key: key);

  @override
  State<HabitBuiling> createState() => _HabitBuilingState();
}

class _HabitBuilingState extends State<HabitBuiling> with TickerProviderStateMixin, WidgetsBindingObserver{

  List<HabitResponse> _cardSelected = [];
  List<HabitResponse> habitResponses = [];
  late AnimationController _animationController;



  final StoryController storyController = StoryController();

  @override
  void initState() {
    final HabitBuildingBloc habitBuildingBloc =
    BlocProvider.of<HabitBuildingBloc>(context);
    habitBuildingBloc.add(LoadHabitBuildingEvent(showLoader: true));
    _animationController = AnimationController(vsync: this,duration: const Duration(milliseconds: 5000),);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  getInitialValue(String key,Pages page) {
    if(page.current != null && page.current!.isNotEmpty) {
      return page.current;
    }
    return page.initial;
  }

  onTimeChange(Pages page,List<int> array,String key) {
    for(int i=0 ; i<_cardSelected.length ; i++) {
      if(page.habitID == _cardSelected[i].habitId) {
        _cardSelected[i].response = array;
      }
    }
    setState(() {
    });
  }

  onPrevButtonPress() {
    final HabitBuildingBloc habitBuildingBloc =
    BlocProvider.of<HabitBuildingBloc>(context);
    Navigator.pop(context);
  }

  onNextButtonPress(HabitResponse habitResponse) {
    final HabitBuildingBloc habitBuildingBloc =
    BlocProvider.of<HabitBuildingBloc>(context);
    habitBuildingBloc.add(SaveHabitResponseEvent(responses: habitResponse));
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colors.white,
        body: MultiBlocListener(
          listeners: [
              BlocListener<NavigationBloc,NavigationState> (
              listener: (context,navState) {
                if(navState is NavigationStackUpdated && navState.action == NavigationStackAction.pop &&
                    navState.route?.settings.name == ('/${EnumToString.convertToString(RouteNames.habit_building)}')
                ) {
                  final HabitBuildingBloc habitBuildingBloc =
                  BlocProvider.of<HabitBuildingBloc>(context);
                  if(navState.newRoute?.settings.name != null) {
                    habitBuildingBloc.add(LoadHabitBuildingEvent());
                  }
                }
              }),
            BlocListener<HabitBuildingBloc,HabitBuildingState> (
                listener: (context,state) {
                  if(state is HabitBuildingFailedState) {
                    showErrorAlert(
                        context: context,
                        title: "Error while fetching habits.",
                        onClose: () {
                          Navigator.pop(context);
                        });
                  }

                  if(state is HabitBuildingSaveHabitLoadedState) {
                    _animationController.reset();
                    Future.delayed(Duration(milliseconds: 500),(){
                      if(mounted) _animationController.forward();
                    });
                  }
                }),
            ],
            child: BlocBuilder<HabitBuildingBloc, HabitBuildingState>(
                builder: (context, state) {
                  if (state is HabitBuildingIdleState) {
                    return Container();
                  }

                  if(state is HabitBuildingFailedState) {
                    return Stack(
                        children: [
                          Aurora(size: MediaQuery.of(context).size, canvasTheme: CanvasTheme.CLASSIC,),
                        ]
                    );
                  }

                  if (state is HabitBuildingLoadingState) {
                    return Stack(
                        children: [
                          Aurora(size: MediaQuery.of(context).size, canvasTheme: CanvasTheme.CLASSIC,),
                          const FancyLoadingIndicator(color: Colors.white)
                        ]
                    );
                  }

                  if(state is HabitBuildingSaveHabitLoadingState) {
                    return const FancyLoadingIndicator(color: Colors.black);
                  }

                  if(state is HabitBuildingSaveHabitLoadedState) {
                    SaveDataPage page = state.page;
                    HabitCard? card = state.card;
                    return Stack(
                        children: [
                          Aurora(size: MediaQuery.of(context).size, canvasTheme: CanvasTheme.CLASSIC),
                          Positioned(
                            left:scale(context, 325),
                            top:scale(context, 40),
                            child: CloseButton(
                              color: Colors.white,
                              onPressed: (){
                                ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
                                if(page.action.actionTypeString == "MOVE_BACK") {
                                  final HabitBuildingBloc habitBuildingBloc =
                                  BlocProvider.of<HabitBuildingBloc>(context);
                                  habitBuildingBloc.add(LoadHabitBuildingEvent());
                                } else {
                                  actionBloc.add(PerformActionEvent(page.action));
                                }
                              },
                            ),),
                          Padding(padding: const EdgeInsets.symmetric(vertical:50,horizontal: 20),
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  page.url != null ? Lottie.network(
                                    getMediaUrl(page.url!),
                                    frameRate: FrameRate(60),
                                    controller:_animationController,
                                    width: scale(context, 325),
                                    height: scale(context, 325),
                                    repeat: false,
                                  ) : SizedBox(height: scale(context, 270),),
                                  Text(
                                    page.header.toUpperCase(),
                                    textAlign: TextAlign.center,
                                    style: AuroraTheme.of(context).textStyle(TypescaleValues.H9, color: Colors.white)
                                  ),
                                  SizedBox(height: scale(context, 20),),
                                  Text(
                                    page.subHeader,
                                    textAlign: TextAlign.center,
                                    style: AuroraTheme.of(context).textStyle(TypescaleValues.P2, color: Colors.white)
                                  ),
                                  SizedBox(height: scale(context, 30),),
                                  Container(
                                    height: scale(context, 1),
                                    width: scale(context, 80),
                                    decoration: BoxDecoration(
                                      color: Colors.white38
                                    ),
                                  ),
                                ],
                              )
                          ),
                          card!=null && page.showNextHabit! ?
                          Positioned(
                            top:scale(context, 580),
                            left:scale(context, 20),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Text("More habit to build ?",
                                  textAlign: TextAlign.center,
                                  style: AuroraTheme.of(context).textStyle(TypescaleValues.H2, color: Colors.white),
                                ),
                                SizedBox(height: scale(context,20),),
                                HabitCardGeneric(card: card, onClick: handleRadio)
                              ],
                            ),
                          ) :
                          Container(),
                          card == null ? Positioned.fill(
                            top: scale(context, 725),
                            child: FloatingButton(
                                buttonText: "DONE",
                                onPress: (){
                                  ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
                                  if(page.action.actionTypeString == "MOVE_BACK") {
                                    final HabitBuildingBloc habitBuildingBloc =
                                    BlocProvider.of<HabitBuildingBloc>(context);
                                    habitBuildingBloc.add(LoadHabitBuildingEvent());
                                  } else {
                                    actionBloc.add(PerformActionEvent(page.action));
                                  }
                                }
                            ),
                          ): Container()
                        ]
                    );
                  }

                  if (state is HabitBuildingLoadedState) {
                    HabitBuildingFirstPage data = state.data.habitBuildingFirstPage!;
                    String habitHeader = data.habitsHeader.isNotEmpty ? data.habitsHeader :
                    "Pick a habit you want build";

                    return Stack(
                      children: [
                        Aurora(size: MediaQuery.of(context).size, canvasTheme: CanvasTheme.CLASSIC),
                        Positioned.fill(
                          child: ListView(
                            children: [
                              Padding(
                                padding: EdgeInsets.all(scale(context, 20)),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.end,
                                      children: [
                                        CloseButton(
                                          color: Colors.white,
                                          onPressed: (){
                                            Navigator.pop(context);
                                          },
                                        )
                                      ],
                                    ),
                                    data.header != null && data.header.length > 0 ? Container(
                                      height: scale(context, 120),
                                      child: Center(
                                          child: Text(
                                            data.header,
                                            textAlign: TextAlign.center,
                                            style: AuroraTheme.of(context).textStyle(TypescaleValues.H3, color: Colors.white),
                                          )),
                                    ): Container(),
                                    data.habitCardsSelected.length > 0 ?
                                    Text(data.habitsHeader,
                                      style: AuroraTheme.of(context).textStyle(TypescaleValues.H2, color: Colors.white),
                                    ) : Container(),
                                    SizedBox(height: scale(context, 10),),
                                    Column(
                                        children: data.habitCardsSelected.asMap().entries.map((entry) =>
                                            Container(
                                                padding:  EdgeInsets.only(
                                                    bottom:  scale(context, 10), top: scale(context, 10)),
                                                child: Row(
                                                  children: [
                                                    HabitCardGeneric(card: entry.value, onClick: handleRadio)
                                                  ],
                                                ))
                                        ).toList()),
                                    data.habitCardsSelected.isNotEmpty && data.habitCardsNotSelected.isNotEmpty?
                                      SizedBox(height: scale(context, scale(context, 70)),) : Container(),
                                    data.habitCardsNotSelected.length > 0 ?
                                      Text(data.editHeader,
                                        style: AuroraTheme.of(context).textStyle(TypescaleValues.H2, color: Colors.white),
                                    ) : Container(),
                                    SizedBox(height: scale(context, 10),),
                                    Column(
                                        children: data.habitCardsNotSelected.asMap().entries.map((entry) =>
                                            Container(
                                                padding: EdgeInsets.only(
                                                    bottom: scale(context, 10), top: scale(context, 10)),
                                                child: Row(
                                                  children: [
                                                    HabitCardGeneric(card: entry.value, onClick: handleRadio)
                                                  ],
                                                ))
                                        ).toList()),
                                    SizedBox(height: scale(context, 100),)
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                        data.habitCardsSelected.isNotEmpty && state.data.todayHabitPageButton != null ?
                        Align(
                            alignment: Alignment.bottomCenter,
                            child: Padding(
                              padding: const EdgeInsets.only(
                                  left: Spacings.x4,
                                  right: Spacings.x4,
                                  bottom: Spacings.x8),
                              child: PrimaryButton(() {handleGoToHabit(state.data.todayHabitPageButton!.action!); },state.data.todayHabitPageButton!.title),
                            )) : Container(),
                      ],
                    );
                  }
                  return Container();
                }),
          ),
        );
  }

  void handleGoToHabit(action_handler.Action action) {
    ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
    actionBloc.add(PerformActionEvent(action));
  }

  void handleRadio(HabitCard card) {
    if(card.selected) {
      ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
      actionBloc.add(PerformActionEvent(card.action!));
    } else {
      final HabitBuildingBloc habitBuildingBloc =
      BlocProvider.of<HabitBuildingBloc>(context);
      Pages editPage = card.page;

      Navigator.of(context).push(MaterialPageRoute(builder: (context) => HabitBuildEditPage(
        identifier: editPage.habitID,
        page:  editPage,
        responseForTimer: getInitialValue,
        onTimeChange: (){},
        onSave: (){},
        backgroundColorStart: editPage.backgroundColors[0],
        backgroundColorEnd:  editPage.backgroundColors[1],
        onNext: onNextButtonPress,
        onPrev: onPrevButtonPress,
      )));
    }
  }
}

import 'package:common/analytics/analytics_repository.dart';
import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/UI/cult_habit_building/tag_widget.dart';

class DeleteButtonWidget extends StatefulWidget {

  Function buttonPressed;
  List<String> codes;
  String? actionID;

  DeleteButtonWidget({Key? key,required this.buttonPressed,
    required this.codes,this.actionID})
      : super(key: key);
  @override
  State<DeleteButtonWidget> createState() => _DeleteButtonWidgetState();
}

class _DeleteButtonWidgetState extends State<DeleteButtonWidget> {

  List<String> selectedCodes = [];

  onConfirmButtonPress(List<String>? codes) {
    if(codes != null && widget.actionID != null) {
      List<String> selectedStrings = [];
      for(int i=0 ; i< codes.length ; i++) {
        if(selectedCodes.contains(i.toString())) {
          selectedStrings.add(codes[i]);
        }
      }
      AnalyticsRepository analyticsRepository =
      RepositoryProvider.of<AnalyticsRepository>(context);
      analyticsRepository.logHabitDeleteButtonPressed(extraInfo: {
        "habitActionId": widget.actionID,
        "selectedCodes":selectedStrings
      });
    }
    widget.buttonPressed();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: scale(context, 400),
      child: Padding(
        padding: EdgeInsets.all(scale(context, 30)),
        child: Stack(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text("Are you sure you want to delete ?",
                  style: AuroraTheme.of(context).textStyle(TypescaleValues.H2, color: Colors.white),
                ),
                SizedBox(height: scale(context, 20),),
                Text("Your all data will get lost if your delete your habit. Select a reason to Continue.",
                  style:AuroraTheme.of(context).textStyle(TypescaleValues.P5, color: Colors.white60),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: scale(context, 40),),
                Wrap(
                  children: widget.codes.asMap().entries.map((entry) =>
                    Tag(text: entry.value, code: entry.key.toString(), selectedCodes: selectedCodes, onClickTag: (String code){
                      if(selectedCodes.contains(code)) {
                        selectedCodes.remove(code);
                      } else {
                        selectedCodes.add(code);
                      }
                      setState(() {});
                    })
                  ).toList(),
                )
              ],
            ),
            Align(
              alignment: Alignment.bottomCenter,
              child: Padding(
                  padding: EdgeInsets.only(bottom: scale(context, 30)),
                  child: PrimaryButton((){
                    onConfirmButtonPress(widget.codes);
                  },
                    "DELETE",
                   enabled:  selectedCodes.length > 0,)
              ),
            ),
          ],
        ),
      ),
    );
  }
}

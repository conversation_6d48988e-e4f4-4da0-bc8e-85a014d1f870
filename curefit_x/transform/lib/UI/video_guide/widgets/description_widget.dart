import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/theme/Colors.dart';

class DescriptionWidgetData {
  final String? title;
  final String? subtitle;

  DescriptionWidgetData({this.title, this.subtitle});

  factory DescriptionWidgetData.fromJson(dynamic payload) {
    return DescriptionWidgetData(
        title: payload['title'], subtitle: payload['subtitle']);
  }
}

class DescriptionWidget extends StatelessWidget {
  final DescriptionWidgetData descriptionWidgetData;

  const DescriptionWidget({Key? key, required this.descriptionWidgetData})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    AuroraThemeData themeData = AuroraTheme.of(context);

    return Container(
      margin: EdgeInsets.fromLTRB(20, 30, 20, 0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(

            child: Text(
              descriptionWidgetData.title != null ? descriptionWidgetData.title! : "",
              style: themeData.textStyle(TypescaleValues.H2,
                  color: ColorPalette.white),
            ),
          ),
          Container(
            margin: EdgeInsets.fromLTRB(0, 5, 0, 0),
            child: Text(
              descriptionWidgetData.subtitle != null ? descriptionWidgetData.subtitle! : "",
              style: themeData.textStyle(TypescaleValues.P5,
                  color: ColorPalette.white60),
            ),
          ),
        ],
      ),
    );
  }
}

import 'dart:math';

import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:video_player_cf/video_player.dart';
import 'package:common/util/orientation.dart';
import 'custom_track_shape.dart';

class TfVideoPlayerLandscape extends StatefulWidget {
  final VideoPlayerController videoPlayerController;
  final double aspectRatio;
  final String videoUrl;
  final Function() onFullScreenClick;

  const TfVideoPlayerLandscape(
      {Key? key,
        required this.videoPlayerController,
        required this.aspectRatio,
        required this.videoUrl,
        required this.onFullScreenClick})
      : super(key: key);

  @override
  State<StatefulWidget> createState() => _TfVideoPlayerLandscapeState();
}

class _TfVideoPlayerLandscapeState extends State<TfVideoPlayerLandscape>
    with WidgetsBindingObserver {
  VideoPlayerController? videoPlayerController;
  late double aspectRatio;
  late int startPosition = 0;
  late double _progress = 0;

  observePlayer() {
    Duration currentPosition = videoPlayerController!.value.position;
    Duration totalDuration = videoPlayerController!.value.duration;
    setState(() {
      _progress = currentPosition.inMilliseconds / totalDuration.inMilliseconds;
    });
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    switch (state) {
      case AppLifecycleState.resumed:
        if (!videoPlayerController!.value.isPlaying) {
          videoPlayerController?.addListener(observePlayer);
          videoPlayerController?.play();
        }
        break;
      case AppLifecycleState.inactive:
      case AppLifecycleState.paused:
      case AppLifecycleState.detached:
        if (videoPlayerController!.value.isPlaying) {
          videoPlayerController?.removeListener(observePlayer);
          videoPlayerController?.pause();
        }
    }
  }

  @override
  void dispose() {
    videoPlayerController?.removeListener(observePlayer);
    unlockAllOrientations();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();

    lockToLandscape();

    videoPlayerController = widget.videoPlayerController;
    aspectRatio = widget.aspectRatio;
    videoPlayerController?.addListener(observePlayer);

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      startPosition = videoPlayerController?.value.position.inSeconds ?? 0;
      if(startPosition == 0){
        videoPlayerController?.play();
      }
      if (videoPlayerController != null) {
        _progress = videoPlayerController!.value.position.inSeconds /
            videoPlayerController!.value.duration.inSeconds;
      }
    });

    WidgetsBinding.instance.addObserver(this);
  }

  video() {
    return VideoPlayer(videoPlayerController!);
  }

  closeVideoPlayer() {
    if (Navigator.canPop(context)) {
      Navigator.pop(context);
    } else if (!Navigator.canPop(context)) {
      ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
      actionBloc.add(CloseApplicationEvent(shouldReset: false));
    }
  }

  playPauseVideo() {
    if (videoPlayerController != null) {
      setState(() {
        if (videoPlayerController!.value.isPlaying) {
          videoPlayerController?.pause();
        } else {
          videoPlayerController?.play();
        }
      });
    }
  }

  Widget getPlayIcon() {
    return Container(
      padding: const EdgeInsets.fromLTRB(20, 0, 10, 0),
      child: Icon(
        (videoPlayerController != null &&
                videoPlayerController!.value.isPlaying)
            ? Icons.pause
            : Icons.play_arrow,
        color: Colors.white,
        size: Spacings.x5,
      ),
    );
  }

  Widget getFullScreenIcon() {
    return GestureDetector(
      onTap: () {
        widget.onFullScreenClick();
      },
      child: Container(
        padding: const EdgeInsets.fromLTRB(10, 0, 20, 0),
        child: Icon(
          Icons.fullscreen_exit,
          color: Colors.white,
          size: Spacings.x5,
        ),
      ),
    );
  }

  Widget getSliderView() {
    if (videoPlayerController == null) {
      return Container();
    }

    return Flexible(
      flex: 1,
      fit: FlexFit.tight,
      child: Container(
        height: 20,
        child: SliderTheme(
            data: SliderTheme.of(context).copyWith(
                activeTrackColor: Colors.white,
                inactiveTrackColor: Colors.black38,
                overlayShape: SliderComponentShape.noThumb,
                trackShape: CustomTrackShape(),
                trackHeight: 4,
                thumbColor: Colors.white,
                thumbShape: RoundSliderThumbShape(enabledThumbRadius: 3.0)),
            child: Slider(
              value: max(0, min(_progress * 100, 100)),
              min: 0,
              max: 100,
              onChanged: (value) {
                setState(() {
                  _progress = value * 0.01;
                });
              },
              onChangeStart: (value) {
                videoPlayerController?.pause();
              },
              onChangeEnd: (value) {
                final duration = videoPlayerController?.value.duration;
                if (duration != null) {
                  var newValue = max(0, min(value, 99)) * 0.01;
                  var millis = (duration.inMilliseconds * newValue).toInt();
                  videoPlayerController?.seekTo(Duration(milliseconds: millis));
                  videoPlayerController?.play();
                }
              },
            )),
      ),
    );
  }

  Widget getVideoView() {
    return Container(
        child: Center(
            child: videoPlayerController != null
                ? AspectRatio(
                    aspectRatio: videoPlayerController!.value.aspectRatio,
                    child:
                        videoPlayerController != null ? (video()) : Container())
                : Container(
                    height: MediaQuery.of(context).size.height,
                    width: MediaQuery.of(context).size.width,
                    color: Colors.grey,
                    child: FancyLoadingIndicator(),
                  )));
  }

  Future<bool> _onBackPressed() {
    widget.onFullScreenClick();
    return Future.value(false);
  }

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    return WillPopScope(
      onWillPop: _onBackPressed,
      child: Scaffold(
          backgroundColor: Colors.transparent,
          body: Stack(
            children: [
              Positioned.fill(
                child: GestureDetector(
                    onTap: () {
                      playPauseVideo();
                    },
                    child: getVideoView()),
              ),
              Positioned(
                  bottom: 0,
                  width: width,
                  child: Container(
                    padding: const EdgeInsets.fromLTRB(0, 0, 0, 30),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        getPlayIcon(),
                        getSliderView(),
                        getFullScreenIcon()
                      ],
                    ),
                  )),
            ],
          )),
    );
  }
}

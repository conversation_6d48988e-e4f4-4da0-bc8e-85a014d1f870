import 'dart:async';

import 'package:common/action/action_handler.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/video_guide/video_guides_bloc.dart';
import 'package:video_player_cf/video_player.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/ui/page/basic_page_container.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/video/video_player_registry.dart';
import 'package:transform/UI/video_guide/components/tf_video_player_portrait.dart';
import 'package:transform/UI/video_guide/components/tf_video_player_landscape.dart';
import 'package:transform/UI/video_guide/widgets/description_widget.dart';

import '../../../blocs/video_guide/event.dart';

class VideoGuideScreenArguments {
  String? contentId;
  String? videoUrl;
  String? title;
  String? subtitle;
  String? aspectRatio;
  String? subCategoryCode;

  VideoGuideScreenArguments(Map<String, dynamic> payload) {
    this.contentId = payload["contentId"];
    this.videoUrl = payload["videoUrl"];
    this.title = payload["title"];
    this.subtitle = payload["subtitle"];
    this.aspectRatio = payload["aspectRatio"];
    this.subCategoryCode =
    payload["subCategoryCode"] != null ? payload["subCategoryCode"] : "";
  }
}

class VideoGuideScreen extends StatefulWidget {
  const VideoGuideScreen({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() => VideoGuideScreenState();
}

class VideoGuideScreenState extends State<VideoGuideScreen>
    with WidgetsBindingObserver {
  VideoPlayerController? videoPlayerController;
  String? contentId;
  String? videoUrl;
  String? title;
  String? subtitle;
  double? aspectRatio = 1.77;
  bool isPortrait = true;
  int duration = 0;
  late Timer durationTimer;

  void increaseDuration() {
    if (videoPlayerController != null) {
      if (videoPlayerController!.value.isPlaying) {
        duration++;
      }
    }
  }

  VideoGuideScreenArguments? videoGuideScreenArguments() {
    final ScreenArguments? args =
        ModalRoute.of(context)!.settings.arguments as ScreenArguments?;
    if (args != null) {
      return VideoGuideScreenArguments(args.params);
    }
    return null;
  }

  void changeOrientation() {
    setState(() {
      isPortrait = !isPortrait;
    });
  }

  @override
  void dispose() {
    durationTimer.cancel();
    videoPlayerController?.dispose();
    videoPlayerController = null;
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void initState() {
    durationTimer = Timer.periodic(new Duration(seconds: 1), (timer) {
      increaseDuration();
    });
    super.initState();
    Future.delayed(Duration.zero, () {
      VideoGuideScreenArguments? arguments = videoGuideScreenArguments();
      videoUrl = arguments?.videoUrl;
      title = arguments?.title;
      subtitle = arguments?.subtitle;
      contentId = arguments?.contentId;
      if (arguments?.aspectRatio != null) {
        Size arSize =
            getSize(context: context, aspectRatio: (arguments?.aspectRatio)!);
        aspectRatio = arSize.aspectRatio;
      }

      WidgetsBinding.instance.addPostFrameCallback((timeStamp) async {
        RepositoryProvider.of<VideoPlayerRegistry>(context)
            .getVideoPlayerController(videoUrl!,
                playFromLocalFile: false, useCache: false)
            .then((controller) {
          setState(() {
            videoPlayerController = controller;
          });
        });
      });
      WidgetsBinding.instance.addObserver(this);
    });
  }

  CanvasTheme draftTheme() {
    TimeOfDay now = TimeOfDay.now();
    return now.hour > 4 && now.hour < 19 ? CanvasTheme.TEAL : CanvasTheme.NIGHT;
  }

  Widget getPortraitPlayer(BuildContext context) {
    double width = MediaQuery.of(context).size.width;

    List<Widget> _widgets = [];
    _widgets.add(SizedBox(
        height: MediaQuery.of(context).padding.top +
            AuroraTheme.of(context).embeddedSafeArea.top));
    _widgets.add(Container(
        child: videoPlayerController != null
            ? Container(
                height: width / aspectRatio!,
                color: Colors.black,
                child: TfVideoPlayerPortrait(
                  videoPlayerController: videoPlayerController!,
                  videoUrl: videoUrl!,
                  onFullScreenClick: changeOrientation,
                  aspectRatio: aspectRatio!,
                ),
              )
            : Container(
                height: MediaQuery.of(context).size.width / aspectRatio!,
                width: MediaQuery.of(context).size.width,
                color: Colors.black,
                child: FancyLoadingIndicator(),
              )));
    if (title != null) {
      _widgets.add(DescriptionWidget(
          descriptionWidgetData:
              DescriptionWidgetData(title: title, subtitle: subtitle)));
    }

    return Scaffold(
      extendBodyBehindAppBar: true,
      body: BasicPageContainer(
        footerPadding: EdgeInsets.fromLTRB(0, 0, 0, 0),
        shouldBuildWidget: false,
        itemBuilder: (BuildContext context, Widget currentWidget, int index) {
          return _widgets[index];
        },
        widgetData: _widgets,
        onBackPressed: () {
          onBackPress();
          if (Navigator.canPop(context)) {
            Navigator.pop(context);
          } else if (!Navigator.canPop(context)) {
            ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
            actionBloc.add(CloseApplicationEvent(shouldReset: false));
          }
        },
        title: "",
        showLoader: false,
        canvasTheme: draftTheme(),
      ),
    );
  }

  Widget getLandscapePlayer(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    double height = MediaQuery.of(context).size.height;
    return Container(
        child: videoPlayerController != null
            ? Container(
                height: height,
                width: width,
                color: Colors.black,
                child: TfVideoPlayerLandscape(
                  videoPlayerController: videoPlayerController!,
                  videoUrl: videoUrl!,
                  onFullScreenClick: changeOrientation,
                  aspectRatio: aspectRatio!,
                ),
              )
            : Container());
  }

  void onBackPress() {
    final preprogramBloc = BlocProvider.of<VideoGuideBloc>(context);
    int duration = 0;
    if (videoPlayerController != null) {
      duration = videoPlayerController!.value.position.inMilliseconds;
    }
    preprogramBloc
        .add(LoadVideoGuideExitEvent(contentId: contentId, duration: duration));
  }

  Future<bool> _onBackPressed() {
    onBackPress();
    return Future.value(true);
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        child: isPortrait
            ? getPortraitPlayer(context)
            : getLandscapePlayer(context),
        onWillPop: _onBackPressed);
  }
}

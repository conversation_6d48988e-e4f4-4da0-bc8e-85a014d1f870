import 'dart:io';

import 'package:collection/collection.dart';
import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/font/cf_icons.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/util/color.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:scroll_to_index/scroll_to_index.dart';
import 'package:transform/blocs/referral/models.dart';

class ReferralTabBar extends StatefulWidget {
  final bool inTabMode;
  final String? title;
  final List<PageData>? pageDataList;
  AutoScrollController? scrollController;
  TabController? tabController;

  ReferralTabBar(
      {this.title,
      this.pageDataList,
      this.inTabMode = false,
      this.tabController,
      this.scrollController,
      Key? key})
      : super(key: key);

  @override
  State<ReferralTabBar> createState() => _ReferralTabBarState();
}

class _ReferralTabBarState extends State<ReferralTabBar> {
  int selectedTab = 0;
  String _tabColor = "#FFFFFF";
  bool makeBlur = false;

  void colorChangeListener() {
    if (widget.scrollController != null &&
        widget.tabController != null &&
        widget.pageDataList != null) {
      setState(() {
        if (widget.scrollController!.positions.last.pixels > 250) {
          _tabColor = "#FFFFFF";
        } else if (widget.scrollController!.positions.last.pixels < 250 &&
            widget.tabController != null) {
          _tabColor = widget.pageDataList!
              .elementAt(widget.tabController!.index)
              .hexColor;
        }
        if (widget.tabController != null &&
            widget.tabController!.indexIsChanging) {
          makeBlur = false;
        } else if (widget.scrollController!.positions.last.pixels > 50) {
          makeBlur = true;
        } else if (widget.scrollController!.positions.last.pixels < 50) {
          makeBlur = false;
        }
      });
    }
  }

  tabBarChanged() {
    if (widget.tabController != null && widget.tabController!.indexIsChanging) {
      setState(() {
        makeBlur = false;
        selectedTab = widget.tabController?.index ?? 0;
        _tabColor = widget.pageDataList!.elementAt(selectedTab).hexColor;
        RepositoryProvider.of<AnalyticsRepository>(context)
            .logButtonClickEvent(extraInfo: {
          "selectedTab": widget.pageDataList?.elementAt(selectedTab).title,
          "previousTab": widget.pageDataList
              ?.elementAt(widget.tabController!.previousIndex)
              .title,
          "action": "tabChanged",
        });
      });
    }
  }

  double toolbarHeight(int size) {
    return Platform.isAndroid
        ? (size == 1)
            ? 80
            : 120
        : (size == 1)
            ? 60
            : 98;
  }

  @override
  void initState() {
    if (widget.pageDataList != null && widget.tabController != null) {
      _tabColor =
          widget.pageDataList!.elementAt(widget.tabController!.index).hexColor;
    }
    widget.scrollController?.addListener(colorChangeListener);
    widget.tabController?.addListener(tabBarChanged);
    super.initState();
  }

  @override
  void dispose() {
    widget.scrollController?.removeListener(colorChangeListener);
    widget.tabController?.removeListener(tabBarChanged);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return makeBlur
        ? BlurView(
            blurType: BlurType.HIGH, borderRadius: 0, child: getAppBar(context))
        : getAppBar(context);
  }

  Widget getAppBar(BuildContext context) {
    AuroraThemeData themeData = AuroraTheme.of(context);
    return AppBar(
      centerTitle: false,
      primary: Platform.isIOS,
      title: Column(
        children: [
          SizedBox(
            height: Platform.isAndroid
                ? (MediaQuery.of(context).padding.top +
                    AuroraTheme.of(context).embeddedSafeArea.top)
                : 0,
          ),
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              GestureDetector(
                  onTap: () {
                    if (Navigator.canPop(context)) {
                      Navigator.pop(context);
                    } else if (!Navigator.canPop(context)) {
                      ActionBloc actionBloc =
                          BlocProvider.of<ActionBloc>(context);
                      actionBloc.add(CloseApplicationEvent(shouldReset: false));
                    }
                  },
                  child: Padding(
                    padding: const EdgeInsets.only(right: 15),
                    child: Row(children: [
                      if (!widget.inTabMode)
                        Padding(
                          padding: const EdgeInsets.only(
                            right: Spacings.x4,
                          ),
                          child: Icon(
                            CFIcons.chevron_left,
                            color: HexColor.fromHex(_tabColor),
                            size: 20,
                            semanticLabel: "chevron_left",
                          ),
                        ),
                      Text(
                        widget.title ?? "",
                        style: AuroraTheme.of(context).textStyle(
                            TypescaleValues.H3,
                            color: HexColor.fromHex(_tabColor)),
                      ),
                    ]),
                  )),
            ],
          ),
        ],
      ),
      actions: [],
      automaticallyImplyLeading: false,
      toolbarHeight: toolbarHeight(widget.pageDataList!.length),
      bottom: widget.pageDataList!.length > 1
          ? PreferredSize(
              preferredSize: Size.fromHeight(Spacings.x8),
              child: Align(
                  alignment: Alignment.centerLeft,
                  child: widget.tabController != null &&
                          widget.pageDataList!.length > 1
                      ? Container(
                          height: Spacings.x8,
                          width: MediaQuery.of(context).size.width,
                          decoration: BoxDecoration(
                              border: Border(
                                  bottom: BorderSide(
                            color: Colors.grey.withOpacity(0.15),
                            width: 1.0,
                          ))),
                          child: TabBar(
                            tabAlignment: TabAlignment.start,
                            dividerHeight: 0,
                            isScrollable: true,
                            labelPadding: const EdgeInsets.all(0),
                            indicatorColor: HexColor.fromHex(_tabColor),
                            indicatorSize: TabBarIndicatorSize.label,
                            automaticIndicatorColorAdjustment: false,
                            indicatorPadding: const EdgeInsets.only(
                                left: Spacings.x3,
                                right: Spacings.x3,
                                top: Spacings.x1),
                            controller: widget.tabController,
                            tabs: widget.pageDataList!
                                .mapIndexed((index, e) => Semantics(
                                      label: e.title,
                                      explicitChildNodes: true,
                                      container: true,
                                      child: Tab(
                                          child: Padding(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: Spacings.x3,
                                            vertical: 0),
                                        child: Text(e.title ?? "",
                                            style: themeData.textStyle(
                                                TypescaleValues.P3,
                                                color: index ==
                                                        widget.tabController
                                                            ?.index
                                                    ? HexColor.fromHex(
                                                        _tabColor)
                                                    : HexColor.fromHex(
                                                            _tabColor)
                                                        .withOpacity(0.7))),
                                      )),
                                    ))
                                .toList(),
                          ),
                        )
                      : Container()))
          : null,
    );
  }
}

import 'dart:io';

import 'package:collection/collection.dart';
import 'package:common/blocs/page/page_bloc.dart';
import 'package:common/ui/page/screen/list_page.dart';
import 'package:scroll_to_index/scroll_to_index.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/UI/referral/component/referral_tab_bar.dart';
import 'package:transform/blocs/referral/models.dart';

class ReferralTabContainer extends StatefulWidget {
  final String? title;
  final List<PageData> pageDataList;
  final String? selectedTabPageId;

  ReferralTabContainer(
      {this.pageDataList = const [],
        this.title,
        this.selectedTabPageId,
        Key? key})
      : super(key: key);

  @override
  State<ReferralTabContainer> createState() => _ReferralTabContainerState();
}

class _ReferralTabContainerState extends State<ReferralTabContainer>
    with TickerProviderStateMixin {
  late AutoScrollController _scrollController;
  TabController? _tabController;
  int selectedTab = 0;

  double toolbarHeight(int size) {
    return Platform.isAndroid
        ? (size == 1)
        ? 80
        : 120
        : (size == 1)
        ? 60
        : 98;
  }

  tabChanged() {
    if (!_tabController!.indexIsChanging) {
      setState(() {
        selectedTab = _tabController?.index ?? 0;
      });
    }
  }

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _scrollController.parentController = PrimaryScrollController.of(context);
    });
    _tabController?.removeListener(tabChanged);
    if(widget.selectedTabPageId != null){
      PageData? initialPageData = widget.pageDataList.firstWhereOrNull((element) => element.pageId == widget.selectedTabPageId);
      if(initialPageData != null){
        selectedTab = widget.pageDataList.indexOf(initialPageData);
      }
    }
    _tabController =
        TabController(length: widget.pageDataList.length, vsync: this, initialIndex: selectedTab);
    _tabController?.addListener(tabChanged);
    _scrollController = AutoScrollController();
    super.initState();
  }

  @override
  void dispose() {
    _tabController?.removeListener(tabChanged);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    AuroraThemeData themeData = AuroraTheme.of(context);
    return Scaffold(
        extendBodyBehindAppBar: true,
        backgroundColor: Colors.transparent,
        appBar: PreferredSize(
          preferredSize: Size.fromHeight(toolbarHeight(widget.pageDataList.length) +
              AuroraTheme.of(context).embeddedSafeArea.top),
          child: ReferralTabBar(
            title: widget.title,
            pageDataList: widget.pageDataList,
            inTabMode: false,
            scrollController: _scrollController,
            tabController: _tabController,
          ),
        ),
        body: widget.pageDataList.isNotEmpty
            ? TabBarView(
            physics: NeverScrollableScrollPhysics(),
            controller: _tabController,
            children: widget.pageDataList
                .map((pageData) => getPage(pageData))
                .toList())
            : Container());
  }

  Widget getPage(PageData pageData) {
    PageBloc pageBloc = BlocProvider.of<PageBloc>(context);
    return BasicListPage(
        disableFloatingCTAAnimation: true,
        repository: pageBloc.repository,
        showTitleBar: false,
        hideTitle: false,
        inTabMode: false,
        shouldRefresh: false,
        footerPadding: EdgeInsets.zero,
        scrollController: _scrollController,
        pageId: pageData.pageId!);
  }
}

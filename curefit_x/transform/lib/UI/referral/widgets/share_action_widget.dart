import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/header/card_header.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/util/action_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:transform/blocs/referral/models.dart';

class ShareActionWidget extends StatelessWidget {
  final ShareActionWidgetData widgetData;

  const ShareActionWidget({required this.widgetData, Key? key})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: Spacings.x4),
      child: Column(
        children: [
          if (widgetData.header != null)
            Padding(
              padding: EdgeInsets.only(bottom: Spacings.x3),
              child: WidgetHeader(cardHeaderData: widgetData.header!),
            ),
          if (widgetData.title != null)
            CustomPaint(
              painter: TicketContainer(containerColor: Colors.white24),
              child: Container(
                padding: EdgeInsets.symmetric(
                    vertical: Spacings.x1, horizontal: Spacings.x3),
                child: Row(
                  children: [
                    Container(
                      width: scale(context, 220),
                      padding: EdgeInsets.only(left: Spacings.x2),
                      child: Text(
                        widgetData.title ?? "",
                        style: AuroraTheme.of(context)
                            .textStyle(TypescaleValues.P2),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 1,
                      ),
                    ),
                    SizedBox(width: Spacings.x1,),
                    Container(
                      width: scale(context, 80),
                      child: PrimaryButton(
                        () {
                          String copyText =
                              widgetData.copyText ?? widgetData.title ?? "";
                          Clipboard.setData(ClipboardData(text: copyText))
                              .then((_) {
                            ScaffoldMessenger.of(context).showSnackBar(SnackBar(
                              content:
                                  Text("Referral link copied to the clipboard"),
                              duration: Duration(milliseconds: 2000),
                            ));
                          });
                        },
                        "COPY",
                        iconData: Icons.copy_rounded,
                        buttonType: PrimaryButtonType.SMALL,
                        horizontalPadding: 5,
                        verticalPadding: 8,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          if (widgetData.actionList != null &&
              widgetData.actionList!.length > 0)
            Padding(
              padding: const EdgeInsets.only(top: Spacings.x4),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  ...widgetData.actionList!.map<Widget>((item) {
                    return InkWell(
                      onTap: () {
                        if (item.action != null)
                          clickActionWithAnalytics(
                              item.action!, context, widgetData.widgetInfo, {
                            "trigger": "referralShareButton",
                            "option": item.title
                          });
                      },
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CFNetworkImage(
                            fit: BoxFit.cover,
                            imageUrl: getImageUrl(
                              context,
                              imagePath: item.imageUrl ?? "",
                            ),
                            height: 80,
                            width: 80,
                          ),
                          SizedBox(
                            height: Spacings.x1,
                          ),
                          Text(
                            item.title ?? "",
                            style: AuroraTheme.of(context)
                                .textStyle(TypescaleValues.P6),
                          ),
                        ],
                      ),
                    );
                  }).toList()
                ],
              ),
            ),
        ],
      ),
    );
  }
}

class TicketContainer extends CustomPainter {
  TicketContainer({required this.containerColor, this.cornerRadius = 5});

  final Color containerColor;
  final double cornerRadius;

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = new Paint()..color = containerColor;
    final height = size.height;
    final width = size.width;
    Path path = Path();
    path.moveTo(0, height);
    path.lineTo(0, 3 * height / 4);
    path.cubicTo(0, 3 * height / 4, 23, (height / 2), 0, height / 4);
    path.lineTo(0, 0);
    path.lineTo(width, 0);
    path.lineTo(width, height / 4);
    path.cubicTo(
        width, height / 4, width - 23, (height / 2), width, 3 * height / 4);
    path.lineTo(width, height);
    path.lineTo(0, height);
    path.close();
    canvas.drawPath(path, paint);
    paint..style = PaintingStyle.stroke;
    paint..color = Colors.white70;
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

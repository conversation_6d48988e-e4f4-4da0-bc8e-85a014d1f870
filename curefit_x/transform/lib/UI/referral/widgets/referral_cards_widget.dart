import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/util/action_util.dart';
import 'package:flutter/material.dart';
import 'package:transform/blocs/referral/models.dart';

class ReferralCardsWidget extends StatelessWidget {
  final ReferralCardsWidgetData widgetData;

  const ReferralCardsWidget({required this.widgetData, Key? key})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: Spacings.x4),
      child: Column(
        children: [
          if (widgetData.summaryCard != null)
            getSummaryCard(context, widgetData.summaryCard!),
          if (widgetData.errorMessage != null) getErrorMessageView(context),
          if (widgetData.items != null) getReferralCards(context),
        ],
      ),
    );
  }

  Widget getSummaryCard(BuildContext context, SummaryCard summaryCard) {
    return Container(
      padding: EdgeInsets.only(bottom: Spacings.x4),
      child: CustomPaint(
        painter: TicketContainerV2(
            containerColor: Colors.greenAccent.withOpacity(0.1)),
        child: Container(
          padding: EdgeInsets.symmetric(
              vertical: Spacings.x2, horizontal: Spacings.x4),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              if(summaryCard.imageUrl != null)
              CFNetworkImage(
                fit: BoxFit.cover,
                imageUrl: getImageUrl(
                  context,
                  imagePath: summaryCard.imageUrl ?? "",
                ),
                width: 40,
                height: 40,
              ),
              if (summaryCard.items != null)
                ...summaryCard.items!.map<Widget>((item) {
                  return Padding(
                    padding: const EdgeInsets.only(left: Spacings.x4),
                    child: Column(
                      children: [
                        Text(
                          item.title ?? "",
                          style: AuroraTheme.of(context).textStyle(
                              TypescaleValues.H2,
                              color: Color.fromRGBO(15, 228, 152, 1)),
                        ),
                        Text(
                          item.subtitle ?? "",
                          style: AuroraTheme.of(context).textStyle(
                              TypescaleValues.TAGTEXT,
                              color: Colors.white60),
                        ),
                      ],
                    ),
                  );
                }).toList()
            ],
          ),
        ),
      ),
    );
  }

  Widget getReferralCards(BuildContext context) {
    return Container(
      child: Column(
          children: widgetData.items!.map<Widget>((item) {
        return InkWell(
          onTap: () {
            clickActionWithAnalytics(item.action!, context,
                widgetData.widgetInfo, {"itemReferee": item.title ?? ""});
          },
          child: Container(
            padding: EdgeInsets.only(bottom: Spacings.x3),
            child: BlurView(
              child: Padding(
                padding: const EdgeInsets.symmetric(
                    vertical: Spacings.x3, horizontal: Spacings.x3),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CFNetworkImage(
                          fit: BoxFit.cover,
                          imageUrl: getImageUrl(
                            context,
                            imagePath: item.imageUrl ?? "",
                          ),
                          height: 45,
                          width: 45,
                        ),
                        SizedBox(
                          width: Spacings.x3,
                        ),
                        Expanded(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              if (item.title != null)
                                Text(
                                  item.title ?? "",
                                  style: AuroraTheme.of(context)
                                      .textStyle(TypescaleValues.H4),
                                ),
                              if (item.subTitle != null)
                                Padding(
                                  padding:
                                      const EdgeInsets.only(top: Spacings.x1),
                                  child: Text(
                                    item.subTitle ?? "",
                                    style: AuroraTheme.of(context).textStyle(
                                        TypescaleValues.P8,
                                        color: Colors.white60),
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    Container(
                      width: double.infinity,
                      height: 1,
                      color: Colors.white24,
                      margin: EdgeInsets.symmetric(vertical: Spacings.x3),
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (item.description != null)
                              Padding(
                                padding:
                                    const EdgeInsets.only(top: Spacings.x1),
                                child: Text(
                                  item.description ?? "",
                                  style: AuroraTheme.of(context)
                                      .textStyle(TypescaleValues.P6),
                                ),
                              ),
                            if (item.footerTitle != null)
                              Padding(
                                padding:
                                    const EdgeInsets.only(top: Spacings.x1),
                                child: Text(
                                  item.footerTitle ?? "",
                                  style: AuroraTheme.of(context)
                                      .textStyle(TypescaleValues.P10),
                                ),
                              ),
                            if (item.footerSubTitle != null)
                              Padding(
                                padding:
                                    const EdgeInsets.only(top: Spacings.x1),
                                child: Text(
                                  item.footerSubTitle ?? "",
                                  style: AuroraTheme.of(context)
                                      .textStyle(TypescaleValues.P3),
                                ),
                              ),
                          ],
                        ),
                        if (item.action != null)
                          Icon(
                            Icons.chevron_right,
                            color: Colors.white,
                            size: 30,
                          ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      }).toList()),
    );
  }

  Widget getErrorMessageView(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        top: Spacings.x12,
      ),
      child: Text(
        widgetData.errorMessage ?? "",
        style: AuroraTheme.of(context).textStyle(TypescaleValues.H1),
      ),
    );
  }
}

class TicketContainerV2 extends CustomPainter {
  TicketContainerV2({required this.containerColor, this.cornerRadius = 5});

  final Color containerColor;
  final double cornerRadius;

  @override
  void paint(Canvas canvas, Size size) {
    final Paint paint = new Paint()..color = containerColor;
    final height = size.height;
    final width = size.width;
    final section = height / 10;
    Path path = Path();
    path.moveTo(0, height);
    path.lineTo(0, 9 * section);
    path.cubicTo(0, 9 * section, 15, 16 * section / 2, 0, 7 * section);
    path.lineTo(0, 6 * section);
    path.cubicTo(0, 6 * section, 15, 10 * section / 2, 0, 4 * section);
    path.lineTo(0, 3 * section);
    path.cubicTo(0, 3 * section, 15, 4 * section / 2, 0, section);
    path.lineTo(0, 0);
    path.lineTo(width, 0);
    path.lineTo(width, section);
    path.cubicTo(
        width, section, width - 15, 4 * section / 2, width, 3 * section);
    path.lineTo(width, 4 * section);
    path.cubicTo(
        width, 4 * section, width - 15, 10 * section / 2, width, 6 * section);
    path.lineTo(width, 7 * section);
    path.cubicTo(
        width, 7 * section, width - 15, 16 * section / 2, width, 9 * section);
    path.lineTo(width, height);
    path.close();
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

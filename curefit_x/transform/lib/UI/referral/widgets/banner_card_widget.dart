import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/atoms/secondary_button.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/util/action_util.dart';
import 'package:common/util/color.dart';
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:transform/blocs/referral/models.dart';

class BannerCardWidget extends StatelessWidget {
  final BannerCardWidgetData widgetData;

  const BannerCardWidget({required this.widgetData, Key? key})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        height: scale(context, 335),
        width: scale(context, 335),
        decoration: widgetData.bgImageUrl != null
            ? BoxDecoration(
                image: DecorationImage(
                  image: NetworkImage(
                      getImageUrl(context, imagePath: widgetData.bgImageUrl)),
                  fit: BoxFit.contain,
                ),
              )
            : null,
        padding: EdgeInsets.symmetric(horizontal: Spacings.x4),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.end,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            if (widgetData.lottieUrl != null)
              Container(
                padding: const EdgeInsets.only(bottom: Spacings.x4),
                child: Lottie.network(getMediaUrl(widgetData.lottieUrl!),
                    frameRate: FrameRate(60),
                    width: scale(context, 300),
                    height: scale(context, 150)),
              ),
            if (widgetData.imageUrl != null)
              Container(
                padding: const EdgeInsets.only(bottom: Spacings.x4),
                child: CFNetworkImage(
                  imageUrl:
                      getImageUrl(context, imagePath: widgetData.imageUrl!),
                  width: scale(context, 300),
                  height: scale(context, 150),
                ),
              ),
            if (widgetData.description != null)
              Padding(
                padding: const EdgeInsets.only(bottom: Spacings.x1),
                child: Text(
                  widgetData.description ?? "",
                  style: AuroraTheme.of(context)
                      .textStyle(TypescaleValues.TAGTEXT),
                  textAlign: TextAlign.center,
                ),
              ),
            if (widgetData.title != null)
              Padding(
                padding: const EdgeInsets.only(bottom: Spacings.x1),
                child: Text(
                  widgetData.title ?? "",
                  style: AuroraTheme.of(context).textStyle(TypescaleValues.H1),
                  textAlign: TextAlign.center,
                ),
              ),
            if (widgetData.subTitle != null)
              Padding(
                padding: const EdgeInsets.only(bottom: Spacings.x4),
                child: Text(
                  widgetData.subTitle ?? "",
                  style: AuroraTheme.of(context)
                      .textStyle(TypescaleValues.P8, color: Colors.white60),
                  textAlign: TextAlign.center,
                ),
              ),
            if (widgetData.action != null)
              Padding(
                padding: const EdgeInsets.only(bottom: Spacings.x6),
                child: SecondaryButton(
                  () {
                    clickActionWithAnalytics(
                        widgetData.action!, context, widgetData.widgetInfo, {});
                  },
                  widgetData.action!.title ?? "",
                  expanded: false,
                ),
              ),
          ],
        ),
      ),
    );
  }
}

import 'package:collection/collection.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/header/card_header.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:flutter/material.dart';
import 'package:transform/blocs/referral/models.dart';

class LevelPointsWidget extends StatelessWidget {
  final LevelPointsWidgetData widgetData;

  const LevelPointsWidget({required this.widgetData, Key? key})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: Spacings.x4),
      child: Column(
        children: [
          if (widgetData.header != null)
            Padding(
              padding: const EdgeInsets.only(bottom: Spacings.x3),
              child: WidgetHeader(cardHeaderData: widgetData.header!),
            ),
          if (widgetData.items != null)
            BlurView(
              child: Container(
                padding: EdgeInsets.only(
                    left: Spacings.x3, right: Spacings.x3, top: Spacings.x3),
                child: Column(
                  children: [
                    if (widgetData.items != null)
                      ...widgetData.items!.mapIndexed<Widget>((index, item) {
                        bool isLast = index == widgetData.items!.length - 1;
                        return Padding(
                          padding: EdgeInsets.only(
                              left: scale(context, Spacings.x1)),
                          child: Stack(
                            children: [
                              if (!isLast)
                                PositionedDirectional(
                                  top: 48,
                                  bottom: 0,
                                  start: 23,
                                  child: Container(
                                    decoration: BoxDecoration(
                                      color: Colors.white30,
                                    ),
                                    width: 1,
                                  ),
                                ),
                              Center(
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Stack(
                                      alignment: Alignment.center,
                                      children: [
                                        Container(
                                          width: 48,
                                          height: 48,
                                          decoration: BoxDecoration(
                                            shape: BoxShape.circle,
                                          ),
                                          child: Padding(
                                            padding: const EdgeInsets.all(4),
                                            child: BlurView(
                                              borderRadius: 25,
                                              child: Container(
                                                alignment: Alignment.center,
                                                decoration: BoxDecoration(
                                                    shape: BoxShape.circle,
                                                    border: Border.all(
                                                        color: Colors.white60)),
                                                padding: EdgeInsets.all(8),
                                                child: CFNetworkImage(
                                                  fit: BoxFit.cover,
                                                  imageUrl: getImageUrl(
                                                    context,
                                                    imagePath:
                                                        item.imageUrl ?? "",
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                    Expanded(
                                      child: Container(
                                        height: isLast ? 95 : 110,
                                        padding: EdgeInsets.only(
                                          left: scale(context, Spacings.x3),
                                        ),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            if (item.tagText != null)
                                              BlurView(
                                                child: Container(
                                                  padding: EdgeInsets.symmetric(
                                                      vertical: 2,
                                                      horizontal: Spacings.x1),
                                                  child: Text(
                                                    item.tagText ?? "",
                                                    style: AuroraTheme.of(
                                                            context)
                                                        .textStyle(
                                                            TypescaleValues
                                                                .TAGTEXT,
                                                            color:
                                                                Color.fromRGBO(
                                                                    15,
                                                                    228,
                                                                    152,
                                                                    1)),
                                                  ),
                                                ),
                                              ),
                                            if (item.title != null)
                                              Text(
                                                item.title ?? "",
                                                style: AuroraTheme.of(context)
                                                    .textStyle(
                                                  TypescaleValues.P4,
                                                ),
                                                overflow: TextOverflow.ellipsis,
                                                maxLines: 3,
                                              ),
                                            SizedBox(
                                              height: !isLast
                                                  ? Spacings.x4
                                                  : Spacings.x2,
                                            )
                                          ],
                                        ),
                                      ),
                                    ),
                                    SizedBox(width: scale(context, Spacings.x1),),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        );
                      }).toList(),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }
}

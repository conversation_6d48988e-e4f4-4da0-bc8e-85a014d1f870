import 'package:common/action/action_handler.dart';
import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/blocs/navigation/navigation_bloc.dart';
import 'package:common/blocs/navigation/state.dart';
import 'package:common/model/error_prop_model.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/screens/error_screen.dart';
import 'package:navigation_history_observer/navigation_history_observer.dart';
import 'package:common/ui/aurora.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/UI/referral/component/referral_tab_container.dart';
import 'package:transform/blocs/referral/events.dart';
import 'package:transform/blocs/referral/models.dart';
import 'package:transform/blocs/referral/referral_screen_bloc.dart';
import 'package:transform/blocs/referral/state.dart';
import 'package:transform/constants/constants.dart';

class ReferralScreen extends StatefulWidget {
  const ReferralScreen({Key? key}) : super(key: key);

  @override
  State<ReferralScreen> createState() => _ReferralScreenState();
}

class _ReferralScreenState extends State<ReferralScreen>
    with WidgetsBindingObserver {
  ReferralScreenArguments? screenArguments;

  ReferralScreenArguments? getScreenArguments() {
    final ScreenArguments? args =
        ModalRoute.of(context)!.settings.arguments as ScreenArguments?;
    if (args != null) {
      ReferralScreenArguments arguments = ReferralScreenArguments(args.params);
      return arguments;
    }
    return null;
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      screenArguments = getScreenArguments();
      final weightLossTab = BlocProvider.of<ReferralScreenBloc>(context);
      weightLossTab.add(ResetReferralScreenEvent());
      weightLossTab.add(LoadReferralScreenEvent(
          showLoader: true, subCategoryCode: screenArguments?.subCategoryCode));
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  refresh({required BuildContext context, bool showLoader = false}) {
    screenArguments = getScreenArguments();
    final weightLossTab = BlocProvider.of<ReferralScreenBloc>(context);
    weightLossTab.add(LoadReferralScreenEvent(
        showLoader: true, subCategoryCode: screenArguments?.subCategoryCode));
  }

  onBackPress() {
    if (Navigator.canPop(context)) {
      Navigator.pop(context);
    } else if (!Navigator.canPop(context)) {
      ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
      actionBloc.add(CloseApplicationEvent(shouldReset: true));
    }
  }

  logPageView() {
    AnalyticsRepository analyticsRepository =
        RepositoryProvider.of<AnalyticsRepository>(context);
    analyticsRepository.logPageViewEvent(
        pageId: EnumToString.convertToString(RouteNames.tf_referral),
        eventInfo: {});
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    switch (state) {
      case AppLifecycleState.resumed:
        logPageView();
        refresh(context: context, showLoader: false);
        break;
      case AppLifecycleState.inactive:
        break;
      case AppLifecycleState.paused:
        break;
      case AppLifecycleState.detached:
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: Colors.transparent,
      body: Stack(
        children: [
          Aurora(
            context: context,
            canvasTheme: CanvasTheme.CLASSIC,
            size: MediaQuery.of(context).size,
          ),
          Positioned.fill(
            child: BlocListener<NavigationBloc, NavigationState>(
              listener: (context, state) {
                if (state is NavigationStackUpdated &&
                    state.action == NavigationStackAction.pop &&
                    state.route?.settings.name ==
                        '/${EnumToString.convertToString(RouteNames.tf_referral)}') {
                  refresh(context: context, showLoader: false);
                }
              },
              child: BlocBuilder<ReferralScreenBloc, ReferralScreenState>(
                builder: (context, state) {
                  List<PageData> pageDataList = [];
                  if (state is ReferralScreenLoadedState) {
                    pageDataList = state.screenData.pageDataList ?? [];
                  } else if (state is ReferralScreenLoadingState &&
                      state.screenData != null) {
                    pageDataList = state.screenData?.pageDataList ?? [];
                  } else if (state is ReferralScreenErrorState) {
                    return Stack(
                      children: [
                        Container(
                          color: Colors.black,
                          child: ErrorScreen(
                              errorInfo: state.errorInfo ?? UnknownError()),
                        ),
                        Positioned(
                          right: 5,
                          top: 30,
                          child: IconButton(
                            onPressed: () {
                              if (Navigator.canPop(context)) {
                                Navigator.pop(context);
                              } else if (!Navigator.canPop(context)) {
                                ActionBloc actionBloc =
                                    BlocProvider.of<ActionBloc>(context);
                                actionBloc.add(
                                    CloseApplicationEvent(shouldReset: false));
                              }
                            },
                            icon: const Icon(
                              Icons.clear,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    );
                  }
                  return pageDataList.isNotEmpty
                      ? ReferralTabContainer(
                          pageDataList: pageDataList,
                          selectedTabPageId:
                              getScreenArguments()?.selectedTabPageId,
                          title: "Refer and Earn",
                        )
                      : Container();
                },
              ),
            ),
          ),
          Positioned.fill(
            child: BlocBuilder<ReferralScreenBloc, ReferralScreenState>(
              builder: (context, state) {
                if (state is ReferralScreenLoadingState && state.showLoader) {
                  return Center(child: FancyLoadingIndicator());
                }
                return Container();
              },
            ),
          ),
        ],
      ),
    );
  }
}

class ReferralScreenArguments {
  String? pageName;
  String? pageId;
  String? pageFrom;
  bool? hideTitle;
  bool? swipeEnabled;
  String? selectedTabPageId;
  String? subCategoryCode;

  ReferralScreenArguments(Map<String, dynamic> params) {
    pageName = params['name'];
    pageId = params['pageId'];
    pageFrom = params['pageFrom'];
    hideTitle = (params['hideTitle'] == 'true');
    swipeEnabled = (params['swipeEnabled'] == 'true');
    selectedTabPageId = params['selectedTabPageId'];
    subCategoryCode = params["subCategoryCode"];
  }
}

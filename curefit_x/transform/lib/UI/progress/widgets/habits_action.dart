import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/util/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:timeline_tile/timeline_tile.dart';
import 'package:transform/blocs/clp/models.dart';

class HabitsAction extends StatefulWidget {
  final ProgressHabitsCard progressHabitsCard;

  const HabitsAction(this.progressHabitsCard, {Key? key}) : super(key: key);

  @override
  _HabitsActionState createState() => _HabitsActionState();
}

class _HabitsActionState extends State<HabitsAction>
    with TickerProviderStateMixin {
  bool _visible = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                width: 60,
                height: 60,
                child: CFNetworkImage(
                  imageUrl: getMediaUrl(widget.progressHabitsCard.imageUrl),
                  errorWidget: (context, url, error) => Icon(Icons.error),
                ),
              ),
              Expanded(
                child: Container(
                  padding: EdgeInsets.only(left: 15),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.progressHabitsCard.subtitle,
                        style: AuroraTheme.of(context)
                            .textStyle(TypescaleValues.TAGTEXT),
                      ),
                      Text(
                        widget.progressHabitsCard.title,
                        style: AuroraTheme.of(context)
                            .textStyle(TypescaleValues.H4),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          SizedBox(
            height: 5,
          ),
          widget.progressHabitsCard.activeActionCards!.length > 0
              ? ActionProgress(widget.progressHabitsCard.activeActionCards!,
                  showLastActiveElement:
                      widget.progressHabitsCard.inActiveActionCards!.length > 0)
              : Container(),
          !_visible && widget.progressHabitsCard.inActiveActionCards!.length > 0
              ? Container(
                  padding: EdgeInsets.only(left: 27),
                  alignment: Alignment.center,
                  child: TimelineTile(
                    alignment: TimelineAlign.start,
                    isLast: true,
                    endChild: Container(
                      constraints: BoxConstraints(minHeight: 75),
                      padding: EdgeInsets.only(left: 45),
                      alignment: Alignment.centerLeft,
                      child: TweenAnimationBuilder(
                        tween: Tween<double>(begin: 0.0, end: 1.0),
                        duration: Duration(milliseconds: 500),
                        builder: (BuildContext context, double _val, _) {
                          return Opacity(
                            opacity: _val,
                            child: GestureDetector(
                              onTap: () {
                                setState(() {
                                  _visible = !_visible;
                                });
                              },
                              child: Container(
                                color: Colors.transparent,
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Expanded(
                                      child: Container(
                                        child: Text(
                                          widget.progressHabitsCard
                                              .pastActionText!,
                                          style: AuroraTheme.of(context)
                                              .textStyle(TypescaleValues.P1,
                                                  color: Color.fromRGBO(
                                                      255, 255, 255, 0.5)),
                                        ),
                                      ),
                                    ),
                                    Icon(
                                      _visible
                                          ? Icons.keyboard_arrow_up
                                          : Icons.keyboard_arrow_down,
                                      color: Colors.white,
                                      size: 30.0,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                    indicatorStyle: const IndicatorStyle(
                      width: 5,
                      color: Color.fromRGBO(255, 255, 255, 0.5),
                    ),
                    beforeLineStyle: const LineStyle(
                      color: Color.fromRGBO(255, 255, 255, 0.5),
                      thickness: 1,
                    ),
                    afterLineStyle: const LineStyle(
                      color: Color.fromRGBO(255, 255, 255, 0.5),
                      thickness: 1,
                    ),
                  ),
                )
              : Container(),
          _visible && widget.progressHabitsCard.inActiveActionCards!.length > 0
              ? Container(
                  alignment: Alignment.centerLeft,
                  padding: EdgeInsets.symmetric(vertical: 20),
                  child: TweenAnimationBuilder(
                    tween: Tween<double>(begin: 0.0, end: 1.0),
                    duration: Duration(milliseconds: 500),
                    builder: (BuildContext context, double _val, _) {
                      return Opacity(
                        opacity: _val,
                        child: GestureDetector(
                          onTap: () {
                            setState(() {
                              _visible = !_visible;
                            });
                          },
                          child: Container(
                            color: Colors.transparent,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Expanded(
                                  child: Container(
                                    child: Text(
                                      widget.progressHabitsCard.pastActionText!,
                                      style: AuroraTheme.of(context).textStyle(
                                          TypescaleValues.P1,
                                          color: Color.fromRGBO(
                                              255, 255, 255, 0.5)),
                                    ),
                                  ),
                                ),
                                Icon(
                                  _visible
                                      ? Icons.keyboard_arrow_up
                                      : Icons.keyboard_arrow_down,
                                  color: Colors.white,
                                  size: 30.0,
                                ),
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                )
              : Container(),
          AnimatedSize(
            duration: Duration(milliseconds: 250),
            child: _visible &&
                    widget.progressHabitsCard.inActiveActionCards!.length > 0
                ? ActionProgress(widget.progressHabitsCard.inActiveActionCards!,
                    opacity: 0.5)
                : Container(),
          ),
          if (widget.progressHabitsCard.showDivider ?? false)
            Container(
              padding: EdgeInsets.symmetric(
                vertical: 20,
              ),
              child: Divider(
                thickness: 0.5,
                color: Colors.white.withOpacity(0.5),
              ),
            ),
        ],
      ),
    );
  }
}

class ActionProgress extends StatelessWidget {
  final List<HabitActionCard> habitActionCards;
  final bool showLastActiveElement;
  final double opacity;

  const ActionProgress(this.habitActionCards,
      {this.showLastActiveElement = false, this.opacity = 1, Key? key})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    return Column(
      children: applyVerticalStaggeredAnimation(
          habitActionCards.map((actionCard) {
            return Container(
              padding: EdgeInsets.only(left: 27),
              alignment: Alignment.center,
              child: TimelineTile(
                alignment: TimelineAlign.start,
                isLast: habitActionCards.last == actionCard &&
                    !showLastActiveElement,
                endChild: Container(
                  constraints: BoxConstraints(minHeight: 75),
                  padding: EdgeInsets.only(left: 45),
                  alignment: Alignment.centerLeft,
                  child: Column(
                    children: [
                      SizedBox(
                        height: 30,
                      ),
                      GestureDetector(
                        onTap: () {
                          if (actionCard.action != null &&
                              actionCard.action!.url != null) {
                            ActionBloc actionBloc =
                                BlocProvider.of<ActionBloc>(context);
                            PerformActionEvent event =
                                PerformActionEvent(actionCard.action!);
                            actionBloc.add(event);
                          }
                        },
                        child: Container(
                          color: Colors.transparent,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Container(
                                      padding: EdgeInsets.only(right: 5),
                                      child: Text(
                                        actionCard.subtitle,
                                        style: AuroraTheme.of(context)
                                            .textStyle(TypescaleValues.P3,
                                                color: Colors.white
                                                    .withOpacity(opacity)),
                                      ),
                                    ),
                                    Container(
                                      padding: EdgeInsets.only(right: 5),
                                      child: Text(
                                        actionCard.title,
                                        style: AuroraTheme.of(context)
                                            .textStyle(TypescaleValues.P8,
                                                color: Colors.white
                                                    .withOpacity(opacity)),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              actionCard.action != null &&
                                      actionCard.action!.url != null
                                  ? Container(
                                      padding: EdgeInsets.only(right: 5),
                                      child: Container(
                                        height: 22,
                                        width: 22,
                                        decoration: BoxDecoration(
                                          color: Color.fromRGBO(
                                              255, 255, 255, 0.18),
                                          shape: BoxShape.circle,
                                        ),
                                        child: Icon(
                                          Icons.arrow_forward_ios,
                                          color: Colors.white,
                                          size: 10,
                                        ),
                                      ),
                                    )
                                  : Container(),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                indicatorStyle: const IndicatorStyle(
                  width: 5,
                  color: Color.fromRGBO(255, 255, 255, 0.5),
                ),
                beforeLineStyle: const LineStyle(
                  color: Color.fromRGBO(255, 255, 255, 0.5),
                  thickness: 1,
                ),
                afterLineStyle: const LineStyle(
                  color: Color.fromRGBO(255, 255, 255, 0.5),
                  thickness: 1,
                ),
              ),
            );
          }).toList(),
          context),
    );
  }
}

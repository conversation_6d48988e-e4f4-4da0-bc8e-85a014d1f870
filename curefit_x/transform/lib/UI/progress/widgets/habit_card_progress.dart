import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/util/theme.dart';
import 'package:flutter/material.dart';
import 'package:transform/UI/progress/widgets/habits_action.dart';
import 'package:transform/blocs/clp/models.dart';

class HabitCardProgress extends StatelessWidget {
  final ProgressCard progressCard;
  HabitCardProgress(this.progressCard);


  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                progressCard.title,
                style: AuroraTheme.of(context).textStyle(TypescaleValues.H2),
              ),
              Text(
                progressCard.consistencyScoreText,
                style:
                AuroraTheme.of(context).textStyle(TypescaleValues.TAGTEXT,color: getConsistencyScoreColor(progressCard.key)),
              ),
            ],
          ),
          SizedBox(height: 30),
          ...progressCard.progresHabitCards.map((data) => HabitsAction(data)).toList(),
        ],
      ),
    );
  }

  Color getConsistencyScoreColor(String key) {
    if(key == "perfect"){
      return Color.fromRGBO(15,228,152,1);
    }
    else if(key == "can_be_improved"){
      return Color.fromRGBO(255, 89, 66, 1);
    }
    else if(key == "on_track"){
      return Color.fromRGBO(247, 199, 68, 1);
    }
    return Colors.white;
  }
}

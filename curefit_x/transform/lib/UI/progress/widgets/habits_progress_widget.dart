import 'package:cached_network_image/cached_network_image.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/util/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:lottie/lottie.dart';
import 'package:transform/UI/progress/widgets/coach_detail_footer.dart';
import 'package:transform/UI/progress/widgets/habit_card_progress.dart';
import 'package:transform/blocs/clp/clp_bloc.dart';
import 'package:transform/blocs/clp/models.dart';
import 'package:transform/blocs/clp/state.dart';

class HabitsProgressWidget extends StatelessWidget {
  final HabitsProgressWidgetData habitsProgressWidgetData;

  HabitsProgressWidget(this.habitsProgressWidgetData);

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    return Column(
      children: [
        SizedBox(
          height: 40,
        ),
        Container(
          width: width * 0.4,
          height: width * 0.4,
          decoration: BoxDecoration(
            color: Colors.transparent,
            shape: BoxShape.circle,
          ),
          child: Lottie.network(
            getMediaUrl(habitsProgressWidgetData.lottieUrl!),
          ),
        ),
        SizedBox(
          height: 40,
        ),
        Container(
          alignment: Alignment.center,
          padding: const EdgeInsets.only(bottom: 5),
          width: width * 0.75,
          child: Text(
            habitsProgressWidgetData.title ?? "",
            style: AuroraTheme.of(context).textStyle(TypescaleValues.H1),
          ),
        ),
        Container(
          alignment: Alignment.center,
          width: width * 0.75,
          child: Text(
            habitsProgressWidgetData.subtitle?? "",
            style: AuroraTheme.of(context).textStyle(TypescaleValues.P8),
            textAlign: TextAlign.center,
          ),
        ),
        SizedBox(
          height: 80,
        ),
        ...habitsProgressWidgetData.progressCards!
            .map((data) => Column(
                  children: [
                    HabitCardProgress(data),
                    SizedBox(
                      height: 80,
                    ),
                  ],
                ))
            .toList(),
        habitsProgressWidgetData.showCoachMessage ?? false
            ? BlocBuilder<CoachCLPBloc, CoachCLPState>(
                builder: (context, state) {
                  if (state is CoachCLPLoaded) {
                    CoachContactWidgetData? coachDetail =
                        state.coachCLP.coachContactWidgetData;
                    if (coachDetail != null && coachDetail.callAction != null)
                      return CoachDetailFooter(coachDetail, coachMessage: habitsProgressWidgetData.coachMessage,);
                  }
                  return Container();
                },
              )
            : Container(),
      ],
    );
  }
}

import 'package:collection/collection.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/util/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/clp/models.dart';

class CoachDetailFooter extends StatelessWidget {
  final CoachContactWidgetData coachDetail;
  final String? coachMessage;

  CoachDetailFooter(this.coachDetail, {this.coachMessage});

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    int numberOfImages = coachDetail.imagesList?.length ?? 0;
    return BlurView(
      borderRadius: 0,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 10),
        height: 227,
        width: double.infinity,
        color: Color.fromRGBO(255, 255, 255, 0.1),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: <Widget>[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                if (coachDetail.imagesList != null &&
                    coachDetail.imagesList!.isNotEmpty)
                  Container(
                    width: (numberOfImages - 1) * 21 + 42,
                    height: 42,
                    child: Stack(
                      children: coachDetail.imagesList!.reversed
                          .mapIndexed<Widget>(
                            (index, image) => Transform.translate(
                              offset:
                                  Offset((numberOfImages - index - 1) * 20, 0),
                              child: Container(
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(21),
                                    border: Border.all(
                                        color: Colors.white60, width: 2)),
                                child: ClipRRect(
                                  child: CFNetworkImage(
                                    width: 40,
                                    height: 40,
                                    imageUrl: getImageUrl(context,
                                        width: 40, imagePath: image),
                                    fit: BoxFit.cover,
                                  ),
                                  borderRadius: BorderRadius.circular(20),
                                ),
                              ),
                            ),
                          )
                          .toList(),
                    ),
                  ),
                Container(
                  width: width * 0.65,
                  child: Text(
                    coachMessage ?? "",
                    style: TextStyle(
                      fontWeight: FontWeight.w400,
                      color: Colors.white.withOpacity(0.54),
                      height: 1.57,
                      fontSize: 14,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ),
              ],
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Container(
                  width: width * 0.44,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(5),
                    color: Color.fromRGBO(255, 255, 255, 1),
                  ),
                  child: TextButton(
                    onPressed: () {
                      if (coachDetail.chatAction != null) {
                        ActionBloc actionBloc =
                            BlocProvider.of<ActionBloc>(context);
                        PerformActionEvent event =
                            PerformActionEvent(coachDetail.chatAction!);
                        actionBloc.add(event);
                      }
                    },
                    child: Text(
                      "CHAT",
                      style: AuroraTheme.of(context).textStyle(
                          TypescaleValues.P3,
                          color: Colors.red.withOpacity(0.8)),
                    ),
                  ),
                ),
                Container(
                  width: width * 0.44,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(5),
                    color: Color.fromRGBO(255, 255, 255, 0.2),
                  ),
                  child: TextButton(
                    onPressed: () {
                      if (coachDetail.callAction != null) {
                        ActionBloc actionBloc =
                            BlocProvider.of<ActionBloc>(context);
                        PerformActionEvent event =
                            PerformActionEvent(coachDetail.callAction!);
                        actionBloc.add(event);
                      }
                    },
                    child: Text(
                      "SCHEDULE CALL",
                      style:
                          AuroraTheme.of(context).textStyle(TypescaleValues.P3),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
    ;
  }
}

import 'package:collection/collection.dart';
import 'package:common/action/action_handler.dart';
import 'package:common/font/cf_icons.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/util/color.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart' hide Action;
import 'package:transform/blocs/clp/models.dart';

class BarGraph extends StatefulWidget {
  final List<GraphViewData>? graphViewList;
  final Action? action;
  final int initialGraph;

  BarGraph({this.graphViewList, this.initialGraph = 0, this.action, Key? key})
      : super(key: key);

  @override
  State<BarGraph> createState() => _BarGraphState();
}

class _BarGraphState extends State<BarGraph> {
  int selectedIndex = 0;
  int totalSize = 0;

  @override
  void initState() {
    totalSize = widget.graphViewList!.length;
    selectedIndex = widget.initialGraph;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return widget.graphViewList != null && widget.graphViewList!.length > 0 ? BlurView(
      borderRadius: 5,
      child: Container(
        padding: EdgeInsets.only(
            top: Spacings.x4,
            bottom: Spacings.x1,
            right: Spacings.x3,
            left: Spacings.x2),
        child: Column(
          children: [
            AspectRatio(
              aspectRatio: 1.0,
              child: BarChart(
                BarChartData(
                  barTouchData: getBarTouchData(context),
                  titlesData: getTitlesData(context),
                  borderData: borderData,
                  barGroups: getBarGroups(
                      widget.graphViewList![selectedIndex].graphData!),
                  gridData: FlGridData(
                      show: true,
                      drawHorizontalLine: true,
                      drawVerticalLine: false,
                      horizontalInterval: 2),
                  alignment: BarChartAlignment.spaceAround,
                  maxY: 11,
                  minY: 0,
                ),
                swapAnimationDuration: Duration(milliseconds: 150), // Optional
                swapAnimationCurve: Curves.linear, // Optional
              ),
            ),
            getBottomBar(context),
          ],
        ),
      ),
    ) : Container();
  }

  Widget getBottomBar(BuildContext context) {
    return Center(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Padding(
            padding: EdgeInsets.only(
                top: Spacings.x4, bottom: Spacings.x4, left: Spacings.x8),
            child: selectedIndex > 0
                ? InkWell(
                    onTap: () {
                      if (selectedIndex > 0) {
                        setState(() {
                          selectedIndex = selectedIndex - 1;
                        });
                      }
                    },
                    child: BlurView(
                      borderRadius: 4,
                      child: Container(
                          margin: EdgeInsets.all(Spacings.x1),
                          child: Icon(
                            CFIcons.chevron_left,
                            color: Colors.white,
                            size: 10,
                            semanticLabel: "chevron_left",
                          )),
                    ),
                  )
                : Container(),
          ),
          SizedBox(
            width: Spacings.x2,
          ),
          Column(
            children: [
              if (widget.graphViewList![selectedIndex].title != null)
                Text(
                  widget.graphViewList![selectedIndex].title ?? "",
                  style: AuroraTheme.of(context).textStyle(TypescaleValues.P3),
                ),
              if (widget.graphViewList![selectedIndex].subtitle != null)
                Text(
                  widget.graphViewList![selectedIndex].subtitle ?? "",
                  style: AuroraTheme.of(context)
                      .textStyle(TypescaleValues.P6, color: Colors.white60),
                )
            ],
          ),
          SizedBox(
            width: Spacings.x2,
          ),
          Padding(
            padding: EdgeInsets.only(
                top: Spacings.x4, bottom: Spacings.x4, right: Spacings.x8),
            child: selectedIndex < totalSize - 1
                ? InkWell(
                    onTap: () {
                      if (selectedIndex < totalSize - 1) {
                        setState(() {
                          selectedIndex = selectedIndex + 1;
                        });
                      }
                    },
                    child: BlurView(
                      borderRadius: 4,
                      child: Container(
                          margin: EdgeInsets.all(Spacings.x1),
                          child: Icon(
                            CFIcons.chevron_right,
                            color: Colors.white,
                            size: 10,
                          )),
                    ),
                  )
                : Container(),
          ),
        ],
      ),
    );
  }

  BarTouchData getBarTouchData(BuildContext context) {
    return BarTouchData(
      enabled: false,
      touchTooltipData: BarTouchTooltipData(
        tooltipBgColor: Colors.transparent,
        tooltipPadding: EdgeInsets.zero,
        tooltipMargin: 0,
        getTooltipItem: (
          BarChartGroupData group,
          int groupIndex,
          BarChartRodData rod,
          int rodIndex,
        ) {
          String unit = widget.graphViewList![selectedIndex].unit ?? "";
          return BarTooltipItem(
            rod.toY.toString() + unit,
            AuroraTheme.of(context).textStyle(TypescaleValues.P10),
          );
        },
      ),
    );
  }

  List<BarChartGroupData> getBarGroups(GraphData graphData) {
    return graphData.points.map<BarChartGroupData>((data) {
      return BarChartGroupData(
        x: (data.x ?? 0.0).toInt(),
        barRods: [
          BarChartRodData(
            toY: data.y ?? 0.0,
            color: HexColor.fromHex(data.color ?? "#FFFFFF"),
            width: 15,
            borderRadius: BorderRadius.only(
              topRight: Radius.circular(10),
              topLeft: Radius.circular(10),
            ),
          )
        ],
        showingTooltipIndicators: [0],
      );
    }).toList();
  }

  FlTitlesData getTitlesData(BuildContext context) {
    return FlTitlesData(
      show: true,
      bottomTitles: AxisTitles(sideTitles: SideTitles(
        showTitles: true,
        reservedSize: 22,
        getTitlesWidget: (value, meta) {
          GraphPoint? point = widget
              .graphViewList![selectedIndex].graphData!.points
              .firstWhereOrNull((element) => element.x == value);
          return Container(
            child: Text(point != null ? (point.day ?? "") : "",
              style: AuroraTheme.of(context)
                  .textStyle(TypescaleValues.P7, color: Colors.white60),
            ),
          );
        },
      )),
      leftTitles: AxisTitles(sideTitles: SideTitles(
        showTitles: true,
        reservedSize: 35,
        interval: 2,
        getTitlesWidget: (value, meta) {
          String unit = widget.graphViewList![selectedIndex].unit ?? "";
          return Container(
            margin: EdgeInsets.only(top: 5),
            child: Text(value.toInt().toString()  + unit,
              style: AuroraTheme.of(context)
                  .textStyle(TypescaleValues.P7, color: Colors.white60),
            ),
          );
        },
      )),
      topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
      rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
    );
  }

  FlBorderData get borderData => FlBorderData(
      show: true,
      border: Border(
        bottom: BorderSide(color: Colors.white60),
        left: BorderSide(color: Colors.white60),
        right: BorderSide(color: Colors.transparent),
        top: BorderSide(color: Colors.transparent),
      ));
}

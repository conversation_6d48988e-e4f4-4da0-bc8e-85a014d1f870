import 'package:common/ui/gradient_text.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/util/color.dart';
import 'package:flutter/material.dart';
import 'package:transform/UI/progress/components/water_view.dart';
import 'package:transform/blocs/user_progress/models.dart';
import 'package:transform/util/utils.dart';

class LifestyleWaterAnimation extends StatefulWidget {
  const LifestyleWaterAnimation({
    Key? key,
    this.mainScreenAnimationController,
    this.height = 330, // minimum height should be 300
    this.width = 100,
    this.configData,
  }) : super(key: key);

  final AnimationController? mainScreenAnimationController;
  final double height;
  final double width;
  final WaterLevelAnimationView? configData;

  @override
  _LifestyleWaterAnimationState createState() =>
      _LifestyleWaterAnimationState();
}

class _LifestyleWaterAnimationState extends State<LifestyleWaterAnimation>
    with TickerProviderStateMixin {
  late int percentValue;
  late int deltaValue;
  List defaultValues = [80, 60, 40];

  Future<bool> getData() async {
    await Future<dynamic>.delayed(const Duration(milliseconds: 50));
    return true;
  }

  @override
  void initState() {
    percentValue = widget.configData!.score!;
    deltaValue = widget.configData!.deltaScore!;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    percentValue = widget.configData!.score!;
    deltaValue = widget.configData!.deltaScore!;
    return AnimatedBuilder(
      animation: widget.mainScreenAnimationController!,
      builder: (BuildContext context, Widget? child) {
        return Column(
          children: [
            if (widget.configData != null &&
                widget.configData!.headerTitle != null &&
                widget.configData!.score != null)
              Padding(
                padding: const EdgeInsets.only(bottom: Spacings.x1),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(bottom: 2),
                      child: Text(
                        widget.configData!.headerTitle ?? "",
                        style: AuroraTheme.of(context)
                            .textStyle(TypescaleValues.P1),
                      ),
                    ),
                    Text(
                      (widget.configData!.score ?? "").toString(),
                      style:
                          AuroraTheme.of(context).textStyle(TypescaleValues.H1),
                    ),
                  ],
                ),
              ),
            if (widget.configData != null &&
                widget.configData!.idealScore != null &&
                widget.configData!.idealScore != null)
              Padding(
                padding: const EdgeInsets.only(bottom: Spacings.x2),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      widget.configData!.idealScoreTitle ?? "",
                      style:
                          AuroraTheme.of(context).textStyle(TypescaleValues.P4),
                    ),
                    Text(
                      (widget.configData!.idealScore ?? "").toString(),
                      style:
                          AuroraTheme.of(context).textStyle(TypescaleValues.P5),
                    ),
                  ],
                ),
              ),
            Container(
              padding: const EdgeInsets.only(
                left: Spacings.x2,
                right: Spacings.x2,
              ),
              child: Container(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Stack(
                      alignment: Alignment.topRight,
                      children: [
                        Row(
                          children: [
                            Container(
                              margin: EdgeInsets.only(
                                top: getScorePosition(percentValue),
                              ),
                              height: 70,
                              width: 100,
                              color: Colors.transparent,
                              child: Text(
                                "",
                                style: AuroraTheme.of(context).textStyle(
                                  TypescaleValues.P10,
                                ),
                              ),
                            ),
                          ],
                        ),
                        ...getDisplayScoreWidgets(),
                      ],
                    ),
                    Stack(
                      children: [
                        Container(
                          width: widget.width,
                          height: widget.height,
                          decoration: BoxDecoration(
                            color: Color.fromRGBO(255, 255, 255, 0.21),
                            borderRadius: const BorderRadius.only(
                                topLeft: Radius.circular(80.0),
                                bottomLeft: Radius.circular(80.0),
                                bottomRight: Radius.circular(80.0),
                                topRight: Radius.circular(80.0)),
                            boxShadow: <BoxShadow>[],
                          ),
                          child: WaterView(
                            percentValue: percentValue,
                            deltaWaveHeight: getWaveHeight(deltaValue),
                            waveHeight: getWaveHeight(percentValue),
                            height: widget.height,
                            width: widget.width,
                          ),
                        ),
                        ...getDividerWidgets(),
                        ...getDisplayTextWidgets(),
                        if (widget.configData != null &&
                            widget.configData!.idealScore != null)
                          getIdealScoreWidgets(),
                      ],
                    ),
                    Stack(
                      alignment: Alignment.topLeft,
                      children: [
                        TweenAnimationBuilder(
                          tween: Tween<double>(begin: 0, end: 1),
                          duration: Duration(seconds: 4),
                          builder: (BuildContext context, double _val, _) {
                            return Opacity(
                              opacity: _val == 1 ? 1 : 0,
                              child: getRightTextData(),
                            );
                          },
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  double getScorePosition(int percentValue) {
    if (percentValue <= 90) {
      return getWaveHeight(percentValue) - 35;
    }
    return 0;
  }

  double getWaveHeight(int value) {
    if (value <= (widget.configData!.displayScores![2] ?? 40)) {
      return getScaledHeight(
          value, 0.40, 0, widget.configData!.displayScores![2], 0);
    } else if (value <= (widget.configData!.displayScores![1] ?? 60) &&
        value > (widget.configData!.displayScores![2] ?? 40)) {
      return getScaledHeight(
          value,
          0.57,
          0.40,
          widget.configData!.displayScores![1],
          widget.configData!.displayScores![2]);
    } else if (value <= (widget.configData!.displayScores![0] ?? 80) &&
        value > (widget.configData!.displayScores![1] ?? 60)) {
      return getScaledHeight(
          value,
          0.84,
          0.57,
          widget.configData!.displayScores![0],
          widget.configData!.displayScores![1]);
    } else if (value > (widget.configData!.displayScores![0] ?? 80)) {
      return getScaledHeight(
          value, 1, 0.84, 100, widget.configData!.displayScores![0]);
    }
    return 0;
  }

  double getScaledHeight(
      int valueV2, double upperV1, double lowerV1, int upperV2, int lowerV2) {
    double height = widget.height -
        ((((widget.height * upperV1 - widget.height * lowerV1) /
                    (upperV2 - lowerV2)) *
                (valueV2 - lowerV2)) +
            widget.height * lowerV1);
    return height;
  }

  List<Widget> getDisplayScoreWidgets() {
    List<double> marginPercent = [0.13, 0.40, 0.57];
    return marginPercent.map((e) {
      int index = marginPercent.indexOf(e);
      return Container(
        margin: EdgeInsets.only(top: widget.height * e),
        child: Text(
          (widget.configData!.displayScores![index] ?? defaultValues[index])
                  .toString() +
              "  ",
          style: AuroraTheme.of(context).textStyle(TypescaleValues.H4),
        ),
      );
    }).toList();
  }

  List<Widget> getDividerWidgets() {
    List<double> marginPercent = [0.60, 0.43, 0.16];
    return marginPercent
        .map(
          (e) => Container(
            margin: EdgeInsets.only(top: widget.height * e),
            height: 0.5,
            width: widget.width,
            decoration: BoxDecoration(
              border: Border.all(
                color: Color.fromRGBO(255, 255, 255, 0.26),
              ),
            ),
          ),
        )
        .toList();
  }

  Widget getIdealScoreWidgets() {
    double idealMargin = 1 - (widget.configData!.idealScore ?? 0) / 100.0;
    return Column(
      children: [
        Container(
          margin: EdgeInsets.only(top: widget.height * idealMargin),
          height: 0.5,
          width: widget.width,
          decoration: BoxDecoration(
            border: Border.all(
              color: Color.fromRGBO(255, 255, 255, 0.26),
            ),
          ),
        ),
        Transform.translate(
          offset: Offset(0, -10),
          child: GradientText(
            "IDEAL FOR YOU",
            LinearGradient(stops: [
              0,
              1
            ], colors: <Color>[
              HexColor.fromHex("#FFDB17"),
              HexColor.fromHex("#02BFFF"),
            ], begin: Alignment.topLeft, end: Alignment(0.75, 0.0)),
            style: TextStyle(
                fontSize: 8, fontWeight: FontWeight.bold, color: Colors.white, letterSpacing: 0.5),
          ),
        ),
      ],
    );
  }

  List<Widget> getDisplayTextWidgets() {
    List<double> marginPercent = [0.08, 0.28, 0.50, 0.80];
    return marginPercent.map((e) {
      int index = marginPercent.indexOf(e);
      bool selected = false;
      if (index == 0) {
        selected = (percentValue >=
            (widget.configData!.displayScores![0] ?? defaultValues[0]));
      } else if (index == marginPercent.length - 1) {
        selected = (percentValue <
            (widget.configData!.displayScores![2] ?? defaultValues[2]));
      } else {
        selected = (percentValue >=
                (widget.configData!.displayScores![index] ??
                    defaultValues[index]) &&
            percentValue <
                (widget.configData!.displayScores![index - 1] ??
                    defaultValues[index - 1]));
      }
      return Container(
        width: widget.width,
        alignment: Alignment.center,
        margin: EdgeInsets.only(top: widget.height * e),
        child: Text(
          selected
              ? (widget.configData!.displayTexts![index])
                  .toString()
                  .toUpperCase()
              : (widget.configData!.displayTexts![index]).toString(),
          textAlign: TextAlign.center,
          style: selected
              ? AuroraTheme.of(context).textStyle(TypescaleValues.P6)
              : AuroraTheme.of(context).textStyle(TypescaleValues.P8),
        ),
      );
    }).toList();
  }

  Widget getRightTextData() {
    if (percentValue != deltaValue) {
      return Container(
        margin: EdgeInsets.only(
          top: getWaveHeight(deltaValue),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              margin: EdgeInsets.only(
                left: 5,
              ),
              height: (getWaveHeight(deltaValue) - getWaveHeight(percentValue))
                  .abs(),
              width: 10,
              decoration: BoxDecoration(
                border: Border(
                  right: BorderSide(
                    color: Colors.white,
                    width: 3.0,
                  ),
                  top: BorderSide(
                    color: Colors.white,
                    width: 3.0,
                  ),
                  bottom: BorderSide(
                    color: Colors.white,
                    width: 3.0,
                  ),
                ),
              ),
            ),
            Container(
              margin: EdgeInsets.only(
                top: ((getWaveHeight(deltaValue) - getWaveHeight(percentValue))
                                .abs() /
                            2 -
                        1)
                    .abs(),
                right: 7,
              ),
              height: 2,
              width: 10,
              decoration: BoxDecoration(
                border: Border.all(
                  color: Colors.white,
                  width: 1,
                ),
              ),
            ),
            Container(
              alignment: Alignment.center,
              margin: EdgeInsets.only(
                top: (getWaveHeight(deltaValue) - getWaveHeight(percentValue))
                            .abs() /
                        2 -
                    (((getWaveHeight(deltaValue) - getWaveHeight(percentValue))
                                    .abs() /
                                2) >=
                            5
                        ? 5
                        : 0),
              ),
              width: 80,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    child: Text(
                      ((widget.configData?.isPositive ?? true) ? "+ " : "- ") +
                          (deltaValue - percentValue).abs().toString() +
                          " POINTS",
                      style: AuroraTheme.of(context).textStyle(
                        TypescaleValues.TAGTEXT,
                        color: HexColor.fromHex(
                            getIndicatorColorFromLifestyleScore(percentValue)),
                      ),
                    ),
                  ),
                  Container(
                    child: Text(
                      widget.configData!.title ?? "since last week",
                      style: AuroraTheme.of(context).textStyle(
                          TypescaleValues.P10,
                          color: Colors.white54),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    }
    return Row(
      children: [
        Container(
          margin: EdgeInsets.only(
            top: getScorePosition(percentValue),
            left: 5,
            right: 7,
          ),
          height: 2,
          width: 10,
          decoration: BoxDecoration(
            border: Border.all(
              color: Colors.white,
            ),
          ),
        ),
        Container(
          alignment: Alignment.center,
          margin: EdgeInsets.only(
            top: getScorePosition(percentValue),
          ),
          height: 70,
          width: 80,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                child: Text(
                  widget.configData!.title ?? "Your Lifestyle Score",
                  style: AuroraTheme.of(context).textStyle(
                    TypescaleValues.P10,
                  ),
                ),
              ),
              Container(
                child: Text(
                  percentValue.round().toString(),
                  style: AuroraTheme.of(context).textStyle(
                    TypescaleValues.H9,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

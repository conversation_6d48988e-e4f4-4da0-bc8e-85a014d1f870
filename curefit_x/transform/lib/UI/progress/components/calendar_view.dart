import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/util/color.dart';
import 'package:flutter/material.dart';
import 'package:transform/blocs/clp/models.dart';

class CalendarView extends StatelessWidget {
  final CalendarChartData? calendarChartData;

  const CalendarView({this.calendarChartData, Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return calendarChartData != null && calendarChartData!.points.length > 0
        ? Table(
            children: <TableRow>[
              for (List<CalendarChartPoints> chartRow
                  in calendarChartData!.points)
                getTableRow(context, chartRow)
            ],
          )
        : Container();
  }

  TableRow getTableRow(
      BuildContext context, List<CalendarChartPoints> chartRow) {
    int emptyCellsCount =
        (calendarChartData?.numberOfColumns ?? 0) - chartRow.length;
    return TableRow(
      children: [
        for (CalendarChartPoints point in chartRow)
          Padding(
            padding: EdgeInsets.only(top: Spacings.x2),
            child: getCell(context, point),
          ),
        if (emptyCellsCount > 0)
          for (int cell = 0; cell < emptyCellsCount; cell++)
            Container(
              padding: EdgeInsets.only(top: Spacings.x2),
            )
      ],
    );
  }

  Widget getCell(BuildContext context, CalendarChartPoints point) {
    IconData iconData = Icons.check_circle_outline_rounded;
    switch (point.status) {
      case CalendarDayStatus.COMPLETED:
        iconData = Icons.check_circle_outline_rounded;
        break;
      case CalendarDayStatus.NOT_COMPLETED:
        iconData = Icons.check_circle_outline_rounded;
        break;
      case CalendarDayStatus.MISSED:
        iconData = Icons.cancel_outlined;
        break;
    }
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(iconData,
            size: 30, color: HexColor.fromHex(point.color ?? "#FFFFFF")),
        Text(
          point.title ?? "",
          style: AuroraTheme.of(context).textStyle(TypescaleValues.P8),
        ),
      ],
    );
  }
}

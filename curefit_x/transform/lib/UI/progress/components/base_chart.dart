import 'dart:math';

import 'package:collection/collection.dart';
import 'package:common/action/action_handler.dart';
import 'package:common/font/cf_icons.dart';
import 'package:common/ui/atoms/secondary_button.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/util/action_util.dart';
import 'package:common/util/color.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart' hide Action;
import 'package:transform/blocs/clp/models.dart';

class BaseChart extends StatefulWidget {
  final List<GraphViewData>? graphViewList;
  final int initialGraph;
  final Action? action;

  const BaseChart(
      {this.action, this.graphViewList, this.initialGraph = 0, Key? key})
      : super(key: key);

  @override
  State<BaseChart> createState() => _BaseChartState();
}

class _BaseChartState extends State<BaseChart> {
  int selectedIndex = 0;
  int totalSize = 0;

  @override
  void initState() {
    totalSize = widget.graphViewList!.length;
    selectedIndex = widget.initialGraph;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return widget.graphViewList != null && widget.graphViewList!.length > 0
        ? Column(
            children: [
              BlurView(
                borderRadius: 5,
                child: Container(
                  padding: EdgeInsets.only(
                      top: Spacings.x4,
                      bottom: Spacings.x1,
                      right: Spacings.x3,
                      left: Spacings.x2),
                  child: Column(
                    children: [
                      AspectRatio(
                        aspectRatio: 1.70,
                        child: Container(
                          decoration: const BoxDecoration(
                              borderRadius:
                                  BorderRadius.all(Radius.circular(18))),
                          child: Padding(
                            padding: const EdgeInsets.only(left: 18, right: 18),
                            child: LineChart(getDataPoints(widget
                                .graphViewList![selectedIndex].graphData!)),
                          ),
                        ),
                      ),
                      getBottomBar(context),
                    ],
                  ),
                ),
              ),
              if (widget.action != null)
                Padding(
                  padding: const EdgeInsets.only(top: Spacings.x3),
                  child: SecondaryButton(() {
                    clickActionWithAnalytics(widget.action!, context, null,
                        {"actionFrom": "progressPage"});
                  }, widget.action?.title ?? ""),
                ),
            ],
          )
        : Container();
  }

  Widget getBottomBar(BuildContext context) {
    return Center(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Padding(
            padding: EdgeInsets.only(
                top: Spacings.x4, bottom: Spacings.x4, left: Spacings.x8),
            child: selectedIndex > 0
                ? InkWell(
                    onTap: () {
                      if (selectedIndex > 0) {
                        setState(() {
                          selectedIndex = selectedIndex - 1;
                        });
                      }
                    },
                    child: BlurView(
                      borderRadius: 4,
                      child: Container(
                          margin: EdgeInsets.all(Spacings.x1),
                          child: Icon(
                            CFIcons.chevron_left,
                            color: Colors.white,
                            size: 10,
                            semanticLabel: "chevron_left",
                          )),
                    ),
                  )
                : Container(),
          ),
          SizedBox(
            width: Spacings.x2,
          ),
          Column(
            children: [
              if (widget.graphViewList![selectedIndex].title != null)
                Text(
                  widget.graphViewList![selectedIndex].title ?? "",
                  style: AuroraTheme.of(context).textStyle(TypescaleValues.P3),
                ),
              if (widget.graphViewList![selectedIndex].subtitle != null)
                Text(
                  widget.graphViewList![selectedIndex].subtitle ?? "",
                  style: AuroraTheme.of(context)
                      .textStyle(TypescaleValues.P6, color: Colors.white60),
                )
            ],
          ),
          SizedBox(
            width: Spacings.x2,
          ),
          Padding(
            padding: EdgeInsets.only(
                top: Spacings.x4, bottom: Spacings.x4, right: Spacings.x8),
            child: selectedIndex < totalSize - 1
                ? InkWell(
                    onTap: () {
                      if (selectedIndex < totalSize - 1) {
                        setState(() {
                          selectedIndex = selectedIndex + 1;
                        });
                      }
                    },
                    child: BlurView(
                      borderRadius: 4,
                      child: Container(
                          margin: EdgeInsets.all(Spacings.x1),
                          child: Icon(
                            CFIcons.chevron_right,
                            color: Colors.white,
                            size: 10,
                          )),
                    ),
                  )
                : Container(),
          ),
        ],
      ),
    );
  }

  LineChartData getDataPoints(GraphData graphData) {
    return LineChartData(
      gridData: FlGridData(show: false),
      titlesData: FlTitlesData(
        show: true,
        rightTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
        topTitles: AxisTitles(sideTitles: SideTitles(showTitles: false)),
        leftTitles: AxisTitles(sideTitles: SideTitles(showTitles: false,)),
        bottomTitles: AxisTitles(sideTitles: SideTitles(
          showTitles: true,
          reservedSize: 22,
          getTitlesWidget: (value, meta) {
            GraphPoint? point = widget
                .graphViewList![selectedIndex].graphData!.points
                .firstWhereOrNull((element) => element.x == value);
            return Container(
              margin: EdgeInsets.only(top: 8),
              child: Text(
                point != null ? (point.day ?? "") : "",
                style: AuroraTheme.of(context)
                    .textStyle(TypescaleValues.P10, color: Colors.white60),
              ),
            );
          },
        )),
      ),
      borderData: borderData,
      minX: 0,
      maxX: graphData.points.length.toDouble() - 1,
      minY: max(
          0, graphData.points.map((e) => e.x ?? 0.0).toList().reduce(min) - 1),
      maxY: graphData.points.map((e) => e.y ?? 0.0).toList().reduce(max) + 1,
      lineBarsData: [
        LineChartBarData(
          spots: [
            ...graphData.points
                .map(
                  (e) => FlSpot(e.x ?? 0.0, e.y ?? 0.0),
                )
                .toList()
          ],
          color: Color.fromRGBO(245, 190, 83, 1),
          barWidth: 3,
          isStrokeCapRound: true,
          dotData: FlDotData(
            show: true,
            checkToShowDot: (spot, barData) {
              return true;
            },
            getDotPainter: (spot, percent, barData, index) =>
                FlDotCirclePainter(
                    radius: 3,
                    color: Color.fromRGBO(245, 190, 83, 1),
                    strokeColor: Colors.white,
                    strokeWidth: 3),
          ),
          belowBarData: BarAreaData(
              show: true,
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                  colors: [
                Color.fromRGBO(245, 190, 83, 0.9),
                Color.fromRGBO(245, 190, 83, 0)
              ]

              ),
              // gradientFrom: Offset(1, 0),
              // gradientTo: Offset(1, 1),
              // colors: [
              //   Color.fromRGBO(245, 190, 83, 0.9),
              //   Color.fromRGBO(245, 190, 83, 0)
              // ]
          ),
        ),
      ],
      lineTouchData: LineTouchData(
        touchTooltipData: LineTouchTooltipData(
          tooltipBgColor: HexColor.fromHex("#F5BE53"),
          tooltipRoundedRadius: 8,
          getTooltipItems: (List<LineBarSpot> lineBarsSpot) {
            return lineBarsSpot.map((lineBarSpot) {
              String unit = widget.graphViewList![selectedIndex].unit ?? "";
              return LineTooltipItem(
                lineBarSpot.y.toString() + " " + unit,
                const TextStyle(
                    color: Colors.white, fontWeight: FontWeight.bold),
              );
            }).toList();
          },
        ),
      ),
    );
  }

  FlBorderData get borderData => FlBorderData(
      show: true,
      border: Border(
        bottom: BorderSide(color: Colors.white60),
        left: BorderSide(color: Colors.white60),
        right: BorderSide(color: Colors.transparent),
        top: BorderSide(color: Colors.transparent),
      ));
}

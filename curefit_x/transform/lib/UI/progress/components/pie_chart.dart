import 'dart:math';

import 'package:common/ui/blur_view.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/util/color.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:transform/blocs/clp/models.dart';

class PieChartView extends StatelessWidget {
  final PieChartValue? pieChartValue;

  const PieChartView({this.pieChartValue, Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return pieChartValue != null && pieChartValue!.points.length > 0 ? BlurView(
      borderRadius: 5,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: Spacings.x4),
        child: Column(
          children: [
            AspectRatio(
              aspectRatio: 1.6,
              child: Pie<PERSON>hart(
                PieChartData(
                  sections: showingSections(pieChartValue!.points),
                  centerSpaceRadius: 50,
                  startDegreeOffset: 90,
                  sectionsSpace: 5,
                ),
                swapAnimationDuration: Duration(milliseconds: 150), // Optional
                swapAnimationCurve: Curves.linear, // Optional
              ),
            ),
            SizedBox(
              height: Spacings.x3,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: <Widget>[
                for (PieChartPoints data in pieChartValue!.points)
                  getBottomBarCell(context, data)
              ],
            ),
          ],
        ),
      ),
    ): Container();
  }

  List<PieChartSectionData> showingSections(List<PieChartPoints> points) {
    return points.map<PieChartSectionData>((data) {
      return PieChartSectionData(
        color: HexColor.fromHex(data.color ?? "#FFFFFF"),
        value: data.percentage,
        title: '',
        radius: 30,
      );
    }).toList();
  }

  Widget getBottomBarCell(BuildContext context, PieChartPoints data) {
    return Container(
      child: Column(
        children: [
          Row(
            children: [
              Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: HexColor.fromHex(data.color ?? "#FFFFFF"),
                ),
                height: 8,
                width: 8,
              ),
              SizedBox(
                width: 5,
              ),
              Text(
                data.title ?? "",
                style: AuroraTheme.of(context)
                    .textStyle(TypescaleValues.TAGTEXT, color: Colors.white60),
              ),
            ],
          ),
          Text(
            (data.value?.toInt() ?? "").toString() +
                (data.value != null && data.total != null ? "/" : "") +
                (data.total?.toInt() ?? "").toString(),
            style: AuroraTheme.of(context).textStyle(TypescaleValues.H4),
          ),
        ],
      ),
    );
  }
}

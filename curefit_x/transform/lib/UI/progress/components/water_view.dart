import 'package:flutter/material.dart';
import './wave_view.dart';

class WaterView extends StatefulWidget {
  const WaterView({
    Key? key,
    required this.percentValue,
    required this.waveHeight,
    required this.deltaWaveHeight,
    this.height = 330, // minimum height should be 300
    this.width = 100,
  }) : super(key: key);

  final int percentValue;
  final double waveHeight;
  final double deltaWaveHeight;
  final double height;
  final double width;

  @override
  _WaterViewState createState() => _WaterViewState();
}

class _WaterViewState extends State<WaterView> with TickerProviderStateMixin {
  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.width,
      height: widget.height,
      decoration: BoxDecoration(
        color: Color.fromRGBO(255, 255, 255, 0.1),
        borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(80.0),
            bottomLeft: Radius.circular(80.0),
            bottomRight: Radius.circular(80.0),
            topRight: Radius.circular(80.0)),
        boxShadow: <BoxShadow>[],
      ),
      child: <PERSON><PERSON>(
        children: [
          TweenAnimationBuilder(
            tween: Tween<double>(
                begin: widget.height, end: widget.deltaWaveHeight),
            duration: Duration(seconds: 4),
            builder: (BuildContext context, double _val, _) {
              return WaveView(
                height: _val,
                score: widget.percentValue,
                showPrimary: false,
              );
            },
          ),
          TweenAnimationBuilder(
            tween: Tween<double>(begin: widget.height, end: widget.waveHeight),
            duration: Duration(seconds: 4),
            builder: (BuildContext context, double _val, _) {
              return WaveView(
                height: _val,
                score: widget.percentValue,
                showPrimary: true,
              );
            },
          ),
        ],
      ),
    );
  }
}

import 'dart:io';

import 'package:collection/collection.dart';
import 'package:common/action/action_handler.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/font/cf_icons.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/page/basic_page_container.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/testimonials/events.dart';
import 'package:transform/blocs/testimonials/state.dart';
import 'package:transform/blocs/testimonials/testimonials_bloc.dart';
import 'package:common/ui/widgets/testimonial_card_widget.dart';

class TestimonialScreenArguments {
  String? subCategoryCode;

  TestimonialScreenArguments(Map<String, dynamic> payload) {
    this.subCategoryCode =
        payload["subCategoryCode"] != null ? payload["subCategoryCode"] : "";
  }
}

class TestimonialsScreen extends StatefulWidget {
  @override
  _TestimonialsScreenState createState() => _TestimonialsScreenState();
}

class _TestimonialsScreenState extends State<TestimonialsScreen> {
  final _controller = ScrollController();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final ScreenArguments? args =
          ModalRoute.of(context)!.settings.arguments as ScreenArguments?;
      String subCategoryCode = "";
      if (args != null) {
        TestimonialScreenArguments arguments =
            TestimonialScreenArguments(args.params);
        subCategoryCode = arguments.subCategoryCode ?? "";
      }
      final testimonialsBloc = BlocProvider.of<TestimonialsBloc>(context);
      testimonialsBloc
          .add(TestimonialsLoadEvent(subCategoryCode: subCategoryCode));
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<TestimonialsBloc, TestimonialsState>(
        builder: (context, state) {
      List<Widget> _widgets = [];
      if (state is TestimonialsLoadedState) {
        _widgets = state.testimonialsScreenData.testimonials
            .mapIndexed((index, testimonial) => Padding(
                padding: EdgeInsets.fromLTRB(20, index == 0 ? 100 : 10, 20, 10),
                child: TestimonialCard(testimonial)))
            .toList();
      } else if (state is TestimonialsFailedState) {
        _widgets = [
          Padding(
            padding:
                EdgeInsets.only(top: MediaQuery.of(context).size.width / 2),
            child: Center(
              child: Text(
                "Something Went Wrong",
                style: AuroraTheme.of(context).textStyle(TypescaleValues.H1),
              ),
            ),
          )
        ];
      }
      return Scaffold(
        extendBodyBehindAppBar: true,
        body: BasicPageContainer(
          shouldBuildWidget: false,
          itemBuilder: (BuildContext context, Widget currentWidget, int index) {
            return _widgets[index];
          },
          widgetData: _widgets,
          onBackPressed: () {
            if (Navigator.canPop(context)) {
              Navigator.pop(context);
            } else if (!Navigator.canPop(context)) {
              ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
              actionBloc.add(CloseApplicationEvent(shouldReset: false));
            }
          },
          title: state is TestimonialsLoadedState
              ? state.testimonialsScreenData.title
              : "",
          showLoader: state is TestimonialsLoadingState,
        ),
      );
      return Scaffold(
          extendBodyBehindAppBar: true,
          appBar: PreferredSize(
              preferredSize: Size.fromHeight(
                  50.0 + AuroraTheme.of(context).embeddedSafeArea.top),
              child: Padding(
                  padding: EdgeInsets.only(
                      top: AuroraTheme.of(context).embeddedSafeArea.top),
                  child: AppBar(
                    titleSpacing: 0,
                    centerTitle: false,
                    title: Text(
                        state is TestimonialsLoadedState
                            ? state.testimonialsScreenData.title
                            : "",
                        style: AuroraTheme.of(context)
                            .textStyle(TypescaleValues.H1)),
                    leading: IconButton(
                      icon: const Icon(
                        CFIcons.chevron_left,
                        color: Colors.white,
                        size: 20,
                      ),
                      onPressed: () {
                        if (Navigator.canPop(context)) {
                          Navigator.pop(context);
                        } else if (!Navigator.canPop(context)) {
                          ActionBloc actionBloc =
                              BlocProvider.of<ActionBloc>(context);
                          actionBloc
                              .add(CloseApplicationEvent(shouldReset: true));
                        }
                      },
                    ),
                  ))),
          body: Stack(
            children: [
              Aurora(
                size: MediaQuery.of(context).size,
                context: context,
              ),
              BlocBuilder<TestimonialsBloc, TestimonialsState>(
                  builder: (context, state) {
                if (state is TestimonialsLoadingState) {
                  return PageLoadingIndicator();
                } else if (state is TestimonialsLoadedState) {
                  return Padding(
                      padding: EdgeInsets.only(top: 40),
                      child: ListView(
                        controller: _controller,
                        children: state.testimonialsScreenData.testimonials
                            .map((testimonial) => Padding(
                                padding: EdgeInsets.symmetric(
                                    horizontal: 20, vertical: 10),
                                child: TestimonialCard(testimonial)))
                            .toList(),
                      ));
                } else if (state is TestimonialsFailedState) {
                  return Center(
                    child: Text(
                      "Something Went Wrong",
                      style:
                          AuroraTheme.of(context).textStyle(TypescaleValues.H1),
                    ),
                  );
                }
                return Container();
              })
            ],
          ));
    });
  }
}

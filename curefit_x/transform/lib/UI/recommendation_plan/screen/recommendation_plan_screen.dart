import 'package:common/action/action_handler.dart';
import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/blocs/navigation/navigation_bloc.dart';
import 'package:common/blocs/navigation/state.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/model/error_prop_model.dart';
import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/atoms/secondary_button.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/page/basic_page_container.dart';
import 'package:common/ui/screens/error_screen.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/util/action_util.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:transform/blocs/recommendation_plan/events.dart';
import 'package:transform/blocs/recommendation_plan/models.dart';
import 'package:transform/blocs/recommendation_plan/recommendation_plan_bloc.dart';
import 'package:transform/blocs/recommendation_plan/state.dart';
import 'package:transform/constants/constants.dart';
import 'package:navigation_history_observer/navigation_history_observer.dart';

class RecommendationPlanClpArguments {
  String? subCategoryCode;

  RecommendationPlanClpArguments(Map<String, dynamic> payload) {
    this.subCategoryCode = payload["subCategoryCode"];
  }
}

class RecommendationPlanClp extends StatefulWidget {
  @override
  _RecommendationPlanClpState createState() => _RecommendationPlanClpState();
}

class _RecommendationPlanClpState extends State<RecommendationPlanClp>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  RecommendationPlanClpArguments? getRecommendationPlanClpArguments() {
    final ScreenArguments? args =
        ModalRoute.of(context)!.settings.arguments as ScreenArguments?;
    if (args != null) {
      return RecommendationPlanClpArguments(args.params);
    }
    return null;
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      RecommendationPlanClpArguments? arguments =
          getRecommendationPlanClpArguments();
      final recommendationPlanBloc =
          BlocProvider.of<RecommendationPlanBloc>(context);
      recommendationPlanBloc.add(ResetRecommendationPlanEvent());
      recommendationPlanBloc.add(LoadRecommendationPlanEvent(showLoader: true));
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  refresh({required BuildContext context, bool showLoader = false}) {
    RecommendationPlanClpArguments? arguments =
        getRecommendationPlanClpArguments();
    final recommendationPlanBloc =
        BlocProvider.of<RecommendationPlanBloc>(context);
    recommendationPlanBloc
        .add(LoadRecommendationPlanEvent(showLoader: showLoader));
  }

  onBackPress() {
    if (Navigator.canPop(context)) {
      Navigator.pop(context);
    } else if (!Navigator.canPop(context)) {
      ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
      actionBloc.add(CloseApplicationEvent(shouldReset: true));
    }
  }

  logPageView() {
    AnalyticsRepository analyticsRepository =
        RepositoryProvider.of<AnalyticsRepository>(context);
    analyticsRepository.logPageViewEvent(
        pageId: EnumToString.convertToString(RouteNames.tf_recommendation_plan),
        eventInfo: {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      body: Stack(
        children: [
          Aurora(
            context: context,
            size: MediaQuery.of(context).size,
          ),
          Positioned.fill(
            child: BlocListener<NavigationBloc, NavigationState>(
              listener: (context, state) {
                if (state is NavigationStackUpdated &&
                    state.action == NavigationStackAction.pop &&
                    state.route?.settings.name ==
                        '/${EnumToString.convertToString(RouteNames.tf_recommendation_plan)}') {
                  refresh(context: context, showLoader: false);
                }
              },
              child:
                  BlocBuilder<RecommendationPlanBloc, RecommendationPlanState>(
                builder: (context, state) {
                  RecommendationPlanData? screenData = null;
                  List<Widget> widgets = [];
                  if (state is RecommendationPlanLoadedState) {
                    screenData = state.screenData;
                    widgets = [getRecommendationWidget(screenData)];
                  } else if (state is RecommendationPlanLoadingState &&
                      state.showLoader) {
                    widgets = [getLoadingStateWidget()];
                  } else if (state is RecommendationPlanLoadingState &&
                      state.screenData != null) {
                    screenData = state.screenData;
                    widgets = [getRecommendationWidget(screenData!)];
                  } else if (state is RecommendationPlanErrorState) {
                    return Stack(
                      children: [
                        Container(
                          color: Colors.black,
                          child: ErrorScreen(
                              errorInfo: state.errorInfo ?? UnknownError()),
                        ),
                        Positioned(
                          right: 5,
                          top: 30,
                          child: IconButton(
                            onPressed: () {
                              if (Navigator.canPop(context)) {
                                Navigator.pop(context);
                              } else if (!Navigator.canPop(context)) {
                                ActionBloc actionBloc =
                                    BlocProvider.of<ActionBloc>(context);
                                actionBloc.add(
                                    CloseApplicationEvent(shouldReset: false));
                              }
                            },
                            icon: const Icon(
                              Icons.clear,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    );
                  }
                  return BasicPageContainer(
                    shouldBuildWidget: false,
                    itemBuilder: (BuildContext context, Widget currentWidget,
                        int index) {
                      return widgets[index];
                    },
                    widgetData: widgets,
                    onBackPressed: () {
                      onBackPress();
                    },
                    title: screenData != null
                        ? screenData.pageTitle
                        : "Recommended Plan",
                    showLoader: false,
                    enableFloatingCTAAnimation: false,
                    floatingCTA: screenData != null
                        ? getFloatingButton(screenData)
                        : null,
                  );
                },
              ),
            ),
          ),
          // Positioned.fill(
          //   child: BlocBuilder<RecommendationPlanBloc, RecommendationPlanState>(
          //     builder: (context, state) {
          //       if (state is RecommendationPlanLoadingState &&
          //           state.showLoader) {
          //         return Center(child: FancyLoadingIndicator());
          //       }
          //       return Container();
          //     },
          //   ),
          // ),
        ],
      ),
    );
  }

  Widget getFloatingButton(RecommendationPlanData screenData) {
    return Padding(
      padding: const EdgeInsets.only(left: Spacings.x4, right: Spacings.x4),
      child: Column(
        children: [
          if (screenData.primaryAction != null)
            PrimaryButton(() {
              clickActionWithAnalytics(
                  screenData.primaryAction!, context, null, {});
            }, screenData.primaryAction!.title ?? ""),
          SizedBox(
            height: Spacings.x4,
          ),
          if (screenData.secondaryAction != null)
            SecondaryButton(() {
              clickActionWithAnalytics(
                  screenData.secondaryAction!, context, null, {});
            }, screenData.secondaryAction!.title ?? ""),
          SizedBox(
            height: Spacings.x6,
          ),
        ],
      ),
    );
  }

  Widget getRecommendationWidget(RecommendationPlanData screenData) {
    return Container(
      padding: EdgeInsets.only(
          top: Spacings.x16, left: Spacings.x4, right: Spacings.x4),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (screenData.subtitle != null)
            Text(
              screenData.subtitle ?? "",
              style: AuroraTheme.of(context).textStyle(TypescaleValues.P6,
                  color: Color.fromRGBO(15, 228, 152, 1)),
            ),
          if (screenData.titleImageUrl != null)
            Padding(
              padding: const EdgeInsets.only(top: Spacings.x1),
              child: CFNetworkImage(
                fit: BoxFit.fill,
                height: 28,
                imageUrl:
                    getImageUrl(context, imagePath: screenData.titleImageUrl),
                placeholder: (BuildContext context, String url) {
                  return Container();
                },
              ),
            ),
          if (screenData.title != null)
            Padding(
              padding: const EdgeInsets.only(top: Spacings.x2),
              child: Text(
                screenData.title ?? "",
                style: AuroraTheme.of(context).textStyle(TypescaleValues.H11),
              ),
            ),
          if (screenData.description != null)
            Padding(
              padding: const EdgeInsets.only(top: Spacings.x2),
              child: Text(
                screenData.description ?? "",
                style: AuroraTheme.of(context).textStyle(TypescaleValues.P5),
              ),
            ),
          if (screenData.imageUrl != null)
            Padding(
              padding: const EdgeInsets.only(top: Spacings.x16),
              child: CFNetworkImage(
                fit: BoxFit.fill,
                width: double.infinity,
                imageUrl: getImageUrl(context, imagePath: screenData.imageUrl),
                placeholder: (BuildContext context, String url) {
                  return Container();
                },
              ),
            ),
        ],
      ),
    );
  }

  Widget getLoadingStateWidget() {
    return Center(
      child: Container(
        padding: EdgeInsets.only(left: Spacings.x4, right: Spacings.x4,top: scale(context,250)),
        child: Column(
          children: [
            CFNetworkImage(
              width: scale(context, 200),
              height: scale(context, 100),
              imageUrl: getImageUrl(context,
                  imagePath: "image/transform/search_animation.gif"),
              fit: BoxFit.cover,
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: Spacings.x8, vertical: Spacings.x4),
              child: Text(
                "Perfect! The right fit plan for you is just around the corner...",
                style: AuroraTheme.of(context).textStyle(TypescaleValues.H9),
                textAlign: TextAlign.center,
              ),
            )
          ],
        ),
      ),
    );
  }
}

import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/gradient_border.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:flutter/material.dart';
import 'package:transform/blocs/challenges/models.dart';

class ChallengersListWidget extends StatelessWidget {
  final ChallengersListWidgetData widgetData;

  const ChallengersListWidget({required this.widgetData});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: Spacings.x4,
      ),
      child: Column(
        children: [
          if (widgetData.title != null)
            Text(
              widgetData.title ?? "",
              style: AuroraTheme.of(context).textStyle(TypescaleValues.H2),
              textAlign: TextAlign.center,
            ),
          if (widgetData.subtitle != null)
            Text(
              widgetData.subtitle ?? "",
              style: AuroraTheme.of(context).textStyle(
                TypescaleValues.P5,
                color: Colors.white60,
              ),
              textAlign: TextAlign.center,
            ),
          if (widgetData.userCard != null)
            Padding(
              padding: EdgeInsets.only(top: Spacings.x4),
              child: Container(
                foregroundDecoration: BoxDecoration(
                  border: GradientBorder.uniform(
                    width: 1.0,
                    gradient: LinearGradient(
                      colors: <Color>[
                        Colors.white54,
                        Colors.transparent,
                        Colors.white54
                      ],
                      stops: [0.0, 0.5, 1.0],
                      begin: Alignment.topRight,
                      end: Alignment.bottomLeft,
                    ),
                  ),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: ChallengerCard(cardData: widgetData.userCard!),
              ),
            ),
          if (widgetData.challengersList != null)
            ...widgetData.challengersList!
                .map<Widget>(
                  (cardData) => Padding(
                    padding: EdgeInsets.only(top: Spacings.x2),
                    child: ChallengerCard(
                      cardData: cardData,
                    ),
                  ),
                )
                .toList(),
        ],
      ),
    );
  }
}

class ChallengerCard extends StatelessWidget {
  final ChallengerCardData cardData;

  const ChallengerCard({required this.cardData});

  @override
  Widget build(BuildContext context) {
    return BlurView(
      borderRadius: 10,
      child: Padding(
        padding: const EdgeInsets.all(
          Spacings.x4,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            if (cardData.rank != null)
              Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: Colors.white60,
                  ),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(
                    Spacings.x1,
                  ),
                  child: Text(
                    cardData.rank ?? "",
                    style: AuroraTheme.of(context)
                        .textStyle(TypescaleValues.TAGTEXT),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            if (cardData.profileImageUrl != null)
              Padding(
                padding: const EdgeInsets.only(left: Spacings.x2),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(scale(context, 45),),
                  child: CFNetworkImage(
                    fit: BoxFit.fill,
                    imageUrl: getImageUrl(context,
                        imagePath: cardData.profileImageUrl),
                    placeholder: (BuildContext imageContext, String url) {
                      return Container(
                        width: scale(context, 45),
                        height: scale(context, 45),
                      );
                    },
                    width: scale(context, 45),
                    height: scale(context, 45),
                  ),
                ),
              ),
            if (cardData.profileImageUrl == null)
              Container(
                width: scale(context, 45),
                height: scale(context, 45),
              ),
            Expanded(
              child: Container(
                padding: EdgeInsets.only(
                  left: Spacings.x2,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (cardData.title != null)
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Flexible(
                            child: Text(
                              cardData.title ?? "",
                              style: AuroraTheme.of(context).textStyle(
                                TypescaleValues.H4,
                              ),
                            ),
                          ),
                          if (cardData.tagUrl != null)
                            Container(
                              padding: const EdgeInsets.only(left: Spacings.x1),
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(10)),
                              width: scale(context, 45),
                              height: scale(context, 15),
                              child: CFNetworkImage(
                                fit: BoxFit.fill,
                                imageUrl: getImageUrl(context,
                                    imagePath: cardData.tagUrl),
                                placeholder:
                                    (BuildContext imageContext, String url) {
                                  return Container(
                                    width: scale(context, 45),
                                    height: scale(context, 15),
                                  );
                                },
                                width: scale(context, 45),
                                height: scale(context, 15),
                              ),
                            ),
                        ],
                      ),
                    if (cardData.subtitle != null)
                      Padding(
                        padding: const EdgeInsets.only(top: Spacings.x1),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Flexible(
                              child: Text(
                                cardData.subtitle ?? "",
                                style: AuroraTheme.of(context).textStyle(
                                    TypescaleValues.P8,
                                    color: Colors.white60),
                              ),
                            ),
                            Text(
                              cardData.emoji ?? "",
                              style: AuroraTheme.of(context).textStyle(
                                TypescaleValues.P8,
                              ),
                            ),
                          ],
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/util/color.dart';
import 'package:flutter/material.dart';
import 'dart:ui' as ui;

import 'package:transform/blocs/challenges/models.dart';

class PodiumWidget extends StatelessWidget {
  final PodiumWidgetData widgetData;

  const PodiumWidget({required this.widgetData});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.end,
      children: widgetData.podiumItems!
          .map<Widget>(
            (item) => PodiumStand(
              item: item,
            ),
          )
          .toList(),
    );
  }
}

class PodiumStand extends StatelessWidget {
  final PodiumItem item;

  const PodiumStand({
    required this.item,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Stack(
          children: [
            // Positioned(
            //   bottom: 0,
            //   left: 0,
            //   right: 0,
            //   top: 0,
            //   child: CustomPaint(
            //     painter: UpwardRay(
            //       containerColor: Colors.transparent,
            //       showLeftSide: item.leftCornerRadius > 0,
            //       showRightSide: item.rightCornerRadius > 0,
            //     ),
            //     child: Container(
            //       height: 80.0,
            //       width: ((item.width ?? 100) / 1.5),
            //     ),
            //   ),
            // ),
            Padding(
              padding: const EdgeInsets.only(bottom: Spacings.x4),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.end,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Stack(
                    children: [
                      ClipRRect(
                        borderRadius: BorderRadius.circular(scale(context, 60),),
                        child: CFNetworkImage(
                          fit: BoxFit.fill,
                          imageUrl: getImageUrl(context,
                              imagePath: item.userProfilePicUrl),
                          placeholder: (BuildContext imageContext, String url) {
                            return Container(
                              width: scale(context, 60),
                              height: scale(context, 60),
                            );
                          },
                          width: scale(context, 60),
                          height: scale(context, 60),
                        ),
                      ),
                      Positioned(
                        left: 0,
                        bottom: 0,
                        child: CFNetworkImage(
                          fit: BoxFit.fill,
                          imageUrl: getImageUrl(context, imagePath: item.rankTag),
                          placeholder: (BuildContext imageContext, String url) {
                            return Container(
                              width: scale(context, (20)),
                              height: scale(context, (20)),
                            );
                          },
                          width: scale(context, (20)),
                          height: scale(context, (20)),
                        ),
                      ),
                      if (item.crownTag != null)
                        Positioned(
                          right: 0,
                          top: 0,
                          child: CFNetworkImage(
                            fit: BoxFit.fill,
                            imageUrl:
                                getImageUrl(context, imagePath: item.crownTag),
                            placeholder: (BuildContext imageContext, String url) {
                              return Container(
                                width: scale(context, (20)),
                                height: scale(context, (20)),
                              );
                            },
                            width: scale(context, (20)),
                            height: scale(context, (20)),
                          ),
                        ),
                    ],
                  ),
                  if (item.userName != null)
                    Padding(
                      padding: const EdgeInsets.only(top: Spacings.x1),
                      child: Text(
                        item.userName ?? "",
                        style: AuroraTheme.of(context)
                            .textStyle(TypescaleValues.TAGTEXT),
                        textAlign: TextAlign.center,
                      ),
                    ),
                ],
              ),
            ),
          ],
        ),
        Transform.translate(
          offset: Offset(
              (item.leftCornerRadius > 0
                  ? item.rightCornerRadius > 0
                      ? 0.0
                      : 0.5
                  : -0.5),
              0.5),
          child: CustomPaint(
            painter: SlantedStand(
              containerColor: HexColor.fromHex(item.standColor),
              leftCornerRadius: item.leftCornerRadius,
              rightCornerRadius: item.rightCornerRadius,
            ),
            child: Container(
              height: item.standHeight,
              width: item.width,
            ),
          ),
        ),
        Transform.translate(
          offset: Offset(
              (item.leftCornerRadius > 0
                  ? item.rightCornerRadius > 0
                      ? 0.0
                      : 0.5
                  : -0.5),
              0.3),
          child: Container(
            height: item.height,
            width: item.width,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: item.gradientColors
                    .map((color) => HexColor.fromHex(color))
                    .toList(),
                stops: [0.5, 1.0],
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class SlantedStand extends CustomPainter {
  SlantedStand(
      {required this.containerColor,
      this.leftCornerRadius = 0,
      this.rightCornerRadius = 0});

  final Color containerColor;
  final double leftCornerRadius;
  final double rightCornerRadius;

  @override
  void paint(Canvas canvas, Size size) {
    double slantWidth = 10.0;
    final Paint paint = new Paint()..color = containerColor;
    final height = size.height;
    final width = size.width;
    Path path = Path();
    path.moveTo(0, height);
    path.lineTo(width, height);
    path.lineTo(
        width - (rightCornerRadius > 0 ? slantWidth : 0), rightCornerRadius);
    path.quadraticBezierTo(
        width - (rightCornerRadius > 0 ? slantWidth : 0) - rightCornerRadius,
        0,
        width -
            2 * rightCornerRadius -
            (rightCornerRadius > 0 ? slantWidth : 0),
        0);
    path.lineTo(
        2 * leftCornerRadius + (leftCornerRadius > 0 ? slantWidth : 0), 0);
    path.quadraticBezierTo(
        (leftCornerRadius > 0 ? slantWidth : 0) + leftCornerRadius,
        0,
        (leftCornerRadius > 0 ? slantWidth : 0),
        leftCornerRadius);
    path.lineTo(0, height);
    path.close();
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

class UpwardRay extends CustomPainter {
  UpwardRay(
      {required this.containerColor,
      this.showLeftSide = true,
      this.showRightSide = true});

  final Color containerColor;
  final bool showLeftSide;
  final bool showRightSide;

  @override
  void paint(Canvas canvas, Size size) {
    double outwardExpand = 40.0;
    final Paint paint = new Paint()
      ..shader = ui.Gradient.linear(
        Offset(0.0, size.height),
        Offset(0.0, 0.0),
        [
          containerColor,
          containerColor.withOpacity(0),
        ],
        [0.5, 1.0],
      );
    final height = size.height;
    final width = size.width;
    final extraWidth = width;
    Path path = Path();
    path.moveTo((showLeftSide ? 0 : -extraWidth), height);
    path.lineTo(width + (showRightSide ? 0 : extraWidth), height);
    path.lineTo(width + (showRightSide ? outwardExpand : extraWidth), 0);
    path.lineTo((showLeftSide ? -outwardExpand : -extraWidth), 0);
    path.lineTo((showLeftSide ? 0 : -extraWidth), height);
    path.close();
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

import 'package:common/action/action_handler.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/page/basic_page_container.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/ui/widget_factory.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/challenges/challenges_bloc.dart';
import 'package:transform/blocs/challenges/events.dart';
import 'package:transform/blocs/challenges/state.dart';

class ChallengesArguments {
  String? challengeId;
  String? subCategoryCode;

  ChallengesArguments(Map<String, dynamic> payload) {
    this.challengeId = payload["challengeId"];
    this.subCategoryCode =
    payload["subCategoryCode"] != null ? payload["subCategoryCode"] : "";
  }
}

class ChallengesScreen extends StatefulWidget {
  @override
  _ChallengesScreenState createState() => _ChallengesScreenState();
}

class _ChallengesScreenState extends State<ChallengesScreen> {
  ChallengesArguments? getChallengesArguments() {
    final ScreenArguments? args =
        ModalRoute.of(context)!.settings.arguments as ScreenArguments?;
    if (args != null) {
      return ChallengesArguments(args.params);
    }
    return null;
  }

  CanvasTheme draftTheme() {
    TimeOfDay now = TimeOfDay.now();
    return now.hour > 4 && now.hour < 19 ? CanvasTheme.TEAL : CanvasTheme.NIGHT;
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ChallengesArguments? arguments = getChallengesArguments();
      String? challengeId = arguments?.challengeId;
      String? subCategoryCode = arguments?.subCategoryCode;
      final challengeBoc = BlocProvider.of<ChallengesBloc>(context);
      challengeBoc
          .add(LoadChallengesContentEvent(challengeId: challengeId ?? "", subCategoryCode: subCategoryCode));
    });
  }

  @override
  Widget build(BuildContext context) {
    CanvasTheme canvasTheme = draftTheme();
    return BlocBuilder<ChallengesBloc, ChallengesBlocState>(
      builder: (context, state) {
        List<Widget> _widgets = [];
        if (state is ChallengesBlocLoadedState) {
          WidgetFactory widgetFactory =
              RepositoryProvider.of<WidgetFactory>(context);
          List<Widget> widgets = state.challengesScreenData.widgets!
              .map<Widget>(
                  (widget) => widgetFactory.createWidgets(widget).first)
              .toList();
          _widgets = applyStaggeredAnimation(widgets, context);
        } else if (state is ChallengesBlocErrorState) {
          _widgets = [
            Padding(
              padding:
                  EdgeInsets.only(top: MediaQuery.of(context).size.width / 2),
              child: Center(
                child: Text(
                  "Something Went Wrong",
                  style: AuroraTheme.of(context).textStyle(TypescaleValues.H1),
                ),
              ),
            )
          ];
        }
        return Scaffold(
          extendBodyBehindAppBar: true,
          body: BasicPageContainer(
            shouldBuildWidget: false,
            itemBuilder:
                (BuildContext context, Widget currentWidget, int index) {
              return _widgets[index];
            },
            widgetData: _widgets,
            onBackPressed: () {
              if (Navigator.canPop(context)) {
                Navigator.pop(context);
              } else if (!Navigator.canPop(context)) {
                ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
                actionBloc.add(CloseApplicationEvent(shouldReset: false));
              }
            },
            title: state is ChallengesBlocLoadedState
                ? state.challengesScreenData.title
                : "",
            showLoader: state is ChallengesBlocLoadingState,
            canvasTheme: state is ChallengesBlocLoadedState
                ? state.challengesScreenData.themeType
                : canvasTheme,
          ),
        );
      },
    );
  }
}

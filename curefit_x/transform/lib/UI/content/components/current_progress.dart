import 'dart:async';

import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:collection/collection.dart';

class CurrentProgress extends StatefulWidget {
  final int completedChaptersCount;
  final String progressAnimation;
  CurrentProgress(this.completedChaptersCount, { required this.progressAnimation });

  @override
  _CurrentProgressState createState() => _CurrentProgressState();
}

class _CurrentProgressState extends State<CurrentProgress> with TickerProviderStateMixin {
  late AnimationController _controller;
  late AnimationController _lottieController;
  late Animation<double> textSize;

  @override
  void initState() {
    super.initState();
    _controller = new AnimationController(
        vsync: this,
        duration: Duration(milliseconds: 1000)
    );
    _lottieController = new AnimationController(
        vsync: this,
        duration: Duration(milliseconds: 1000)
    );
    _controller.addListener(() {
      if (_controller.value > 0.8) {
        _lottieController.forward();
      }
    });
    textSize = TweenSequence(
      [
        TweenSequenceItem(tween: Tween(begin: 38.0, end: 30.0), weight: 1),
        TweenSequenceItem(tween: Tween(begin: 30.0, end: 38.0), weight: 1),
      ],
    ).animate(CurvedAnimation(
        parent: _controller,
        curve: Interval(
            0.6,
            1.0
        )));
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      Timer(Duration(milliseconds: 400), () =>  _controller.forward());
    });
  }

  Animation<double> getFlipTween(int indexFromLast) {
    double begin = indexFromLast/10;
    double end = 0.3 + begin;
    return Tween(begin: 0.0, end: 60.0).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Interval(
            begin,
            end,
            curve: Curves.linearToEaseOut
        ))
    );
  }
  @override
  Widget build(BuildContext context) {
    AuroraThemeData themeData = AuroraTheme.of(context);
    List<String> currentProg = widget.completedChaptersCount.toString().split("");
    List<String> prevProg = (widget.completedChaptersCount - 1).toString().padLeft(currentProg.length,"0").split("");
    return Stack(
      alignment: Alignment.center,
      children: [
        AnimatedBuilder(
            animation: _controller,
            builder: (BuildContext ctx, Widget? child) {
              return Container(
                height: 145,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: currentProg.mapIndexed<Widget>((index, ele) {
                    if (ele == prevProg[index]) {
                      return SingleDigit(ele, textSize.value, 60.0);
                    } else {
                      return _SingleDigitAnimation(currentDigit: ele, prevDigit:prevProg[index], height: getFlipTween(currentProg.length - index - 1), textSize: textSize);
                    }
                  }).toList(),
                ),
              );
            }
        ),
        Lottie.network(
            getMediaUrl(widget.progressAnimation),
            height: 145,
            controller: _lottieController
        )
      ],
    )
    ;
  }
}

class _SingleDigitAnimation extends StatelessWidget {
  final String currentDigit;
  final String prevDigit;
  final Animation<double> height;
  final Animation<double> textSize;

  const _SingleDigitAnimation({Key? key, required this.currentDigit, required this.prevDigit, required this.height, required this.textSize}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    AuroraThemeData themeData = AuroraTheme.of(context);
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SingleDigit(prevDigit, textSize.value, 60 - height.value),
        SingleDigit(currentDigit, textSize.value, height.value)
      ],
    );
  }
}

Widget SingleDigit(digit, textSize, height) {
  return Container(
    height: height ?? 60.0,
    padding: EdgeInsets.symmetric(vertical: 10.0),
    child: Text(digit,  style: TextStyle(fontSize: textSize, color: Colors.white, fontWeight: FontWeight.w900, letterSpacing: -0.3, height: 1.1, )),
  );
}

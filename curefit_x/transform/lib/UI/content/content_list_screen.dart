import 'dart:io';

import 'package:common/action/action_handler.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/font/cf_icons.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/widget_factory.dart';
import 'package:common/util/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/content/chapter_bloc.dart';
import 'package:transform/blocs/content/content_bloc.dart';
import 'package:transform/blocs/content/events.dart';
import 'package:transform/blocs/content/state.dart';

import 'package:common/ui//ui_toolkit.dart';

class ContentListScreenArguments {
  String? courseId;
  String? subCategoryCode;

  ContentListScreenArguments(Map<String, dynamic> payload) {
    this.courseId = payload["courseId"];
    this.subCategoryCode = payload["subCategoryCode"] != null ? payload["subCategoryCode"] : "";
  }
}

class ContentListScreen extends StatefulWidget {
  @override
  State<StatefulWidget> createState() => _ContentListScreenState();
}

class _ContentListScreenState extends State<ContentListScreen>
    with TickerProviderStateMixin {
  @override
  void initState() {
    super.initState();
    Future.delayed(Duration.zero, () {
      loadCourse();
    });
  }

  loadCourse() {
    final ScreenArguments? args =
    ModalRoute
        .of(context)!
        .settings
        .arguments as ScreenArguments?;
    if (args != null) {
      ContentListScreenArguments arguments =
      ContentListScreenArguments(args.params);
      if (arguments.courseId != null) {
        final contentBloc = BlocProvider.of<ContentBloc>(context);
        contentBloc.add(LoadCourseEvent(courseId: arguments.courseId!, subCategoryCode: arguments.subCategoryCode));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        extendBodyBehindAppBar: true,
        appBar: PreferredSize(
          preferredSize: Size.fromHeight(50.0 + AuroraTheme.of(context).embeddedSafeArea.top),
          child: Padding(
            padding: EdgeInsets.only(
                top: AuroraTheme
                    .of(context)
                    .embeddedSafeArea
                    .top),
            child: AppBar(
              backgroundColor: Colors.transparent,
              iconTheme: IconThemeData(color: Colors.white),
              titleSpacing: 0,
              centerTitle: false,
              title: Text("Content Library",
                style: AuroraTheme.of(context).textStyle(
                    TypescaleValues.H9),),
              leading: IconButton(
                icon: const Icon(
                  CFIcons.chevron_left,
                  color: Colors.white,
                  size: 20,
                  semanticLabel: "chevron_left",
                ),
                onPressed: () {
                  if (Navigator.canPop(context)) {
                    Navigator.pop(context);
                  } else if (!Navigator.canPop(context)) {
                    ActionBloc actionBloc =
                    BlocProvider.of<ActionBloc>(context);
                    actionBloc
                        .add(CloseApplicationEvent(shouldReset: true));
                  }
                },
              ),
            ),
          ),
        ),
        body: LayoutBuilder(builder: (context, constraints) {
          if (constraints.maxWidth > 0) {
            return Container(
                color: Colors.black,
                child: Stack(
                  children: [
                    Aurora(size: constraints.biggest, context: context,),
                    Positioned.fill(
                      child: BlocListener<ChapterBloc, ContentState>(
                        listener: (context, state) {
                          if (state is ChapterCompleted) {
                            loadCourse();
                          }
                        },
                        child: BlocBuilder<ContentBloc, ContentState>(
                          builder: (context, state) {
                            if (state is CourseLoading) {
                              return FancyLoadingIndicator(color: Colors.white);
                            }
                            if (state is CourseLoaded) {
                              WidgetFactory widgetFactory =
                              RepositoryProvider.of<WidgetFactory>(context);
                              final widgets = widgetFactory
                                  .createWidgets(state.courseContent.widgets!);
                              return ListView(
                                children:
                                applyStaggeredAnimation(widgets, context),
                              );
                            }
                            return Container();
                          },
                        ),
                      ),
                    ),
                  ],
                ));
          }
          return Container();
        }));
  }
}

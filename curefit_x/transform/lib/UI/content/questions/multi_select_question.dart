import 'package:common/util/color.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:transform/blocs/content/chapter_bloc.dart';
import 'package:transform/blocs/content/events.dart';
import 'package:common/ui/flutter_scale_tap.dart';
import 'package:collection/collection.dart' show IterableExtension;
import 'package:common/ui/components/checkbox.dart' as CFCheckbox;

import 'package:common/ui/ui_toolkit.dart';

class MultiSelectOption {
  final String id;
  final String text;
  final bool? isCorrect;
  final bool? isSelected;

  MultiSelectOption(
      {required this.id, required this.text, this.isCorrect, this.isSelected});
}

class MultiSelectQuestion extends StatefulWidget {
  final List<MultiSelectOption> options;
  final String pageId;
  final double fontSize;
  final Color textColor;

  const MultiSelectQuestion(
      {Key? key,
      required this.options,
      required this.pageId,
      this.fontSize = 16,
      this.textColor = Colors.black})
      : super(key: key);

  @override
  State<MultiSelectQuestion> createState() => _MultiSelectQuestionState();
}

class _MultiSelectQuestionState extends State<MultiSelectQuestion> {
  List<MultiSelectOption> selectedOptions = [];

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return _renderOptions();
  }

  _renderOptions() {
    List<Widget> options = [];
    options.add(Padding(padding: EdgeInsets.only(top: 20)));
    for (MultiSelectOption option in widget.options) {
      bool optionSelected = selectedOptions.length > 0 &&
          selectedOptions
                  .firstWhereOrNull((element) => element.id == option.id) !=
              null;
      options.add(ScaleTap(
          onTap: () {
            _checkboxValueChanged(option);
          },
          child: Padding(
              padding: EdgeInsets.symmetric(vertical: 10),
              child: Container(
                  child: Row(
                children: [
                  Padding(
                      padding: EdgeInsets.only(right: 20),
                      child: CFCheckbox.Checkbox(
                        selected: optionSelected,
                      )),
                  Flexible(
                      child: Text(option.text,
                          style: TextStyle(
                              color: widget.textColor,
                              fontSize: widget.fontSize,
                              fontWeight: optionSelected
                                  ? FontWeight.w900
                                  : FontWeight.w400)))
                ],
              )))));
    }
    return AnimationLimiter(
        child: ListView(
            physics: NeverScrollableScrollPhysics(),
            children: applyStaggeredAnimation(options, context),
            shrinkWrap: true,
            padding: EdgeInsets.zero));
  }

  _checkboxValueChanged(MultiSelectOption option) {
    setState(() {
      int index =
          selectedOptions.indexWhere((element) => element.id == option.id);
      if (index != -1)
        selectedOptions.removeAt(index);
      else
        selectedOptions.add(option);
    });
    ChapterBloc chapterBloc = BlocProvider.of<ChapterBloc>(context);
    UpdateQuestionAnswer updateQuestionAnswer = UpdateQuestionAnswer(
        pageId: widget.pageId,
        selectedOptionIds: selectedOptions.map<String>((e) => e.id).toSet());
    chapterBloc.add(updateQuestionAnswer);
  }
}

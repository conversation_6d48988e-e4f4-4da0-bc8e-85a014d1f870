import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/content/chapter_bloc.dart';
import 'package:transform/blocs/content/events.dart';

class TextInputQuestion extends StatelessWidget {
  final String pageId;

  const TextInputQuestion({Key? key, required this.pageId}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    return TextField(
      onChanged: (String value) {
        ChapterBloc chapterBloc = BlocProvider.of<ChapterBloc>(context);
        UpdateQuestionAnswer updateQuestionAnswer =
            UpdateQuestionAnswer(pageId: this.pageId, answer: value);
        chapterBloc.add(updateQuestionAnswer);
      },
      decoration: InputDecoration(
          filled: true,
          fillColor: Colors.white,
          border: OutlineInputBorder(),
          hintText: 'Enter your answer'),
    );
  }
}

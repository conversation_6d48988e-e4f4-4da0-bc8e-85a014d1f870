import 'package:common/action/action_handler.dart';
import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/ui/atoms/action_button.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/storyview/story_view.dart';
import 'package:common/util/color.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/UI/content/templates/container_page_view.dart';
import 'package:transform/blocs/content/chapter_bloc.dart';
import 'package:transform/blocs/content/events.dart';
import 'package:transform/blocs/content/models.dart';
import 'package:transform/blocs/content/state.dart';
import 'package:transform/factory/content_widget_builder.dart';
import 'package:async/async.dart';
import 'constants.dart';

class ChapterScreenArguments {
  String? courseId;
  String? chapterId;
  bool? reset;
  bool? isReviewer;

  ChapterScreenArguments(Map<String, dynamic> payload) {
    this.courseId = payload["courseId"];
    this.chapterId = payload["chapterId"];
    this.reset = payload["reset"] != null ? true : false;
    this.isReviewer = payload['isReviewer'];
  }
}

class ChapterScreen extends StatefulWidget {
  final ContentWidgetBuilder widgetBuilder;

  const ChapterScreen({Key? key, required this.widgetBuilder})
      : super(key: key);

  @override
  State<StatefulWidget> createState() => _ChapterScreenState();
}

class _ChapterScreenState extends State<ChapterScreen>
    with TickerProviderStateMixin {
  final StoryController storyController = StoryController();
  void Function()? onCompletion;
  CancelableOperation<Null>? cancelableOperation;

  ChapterScreenArguments? chapterScreenArguments() {
    final ScreenArguments? args =
        ModalRoute.of(context)!.settings.arguments as ScreenArguments?;
    if (args != null) {
      return ChapterScreenArguments(args.params);
    }
    return null;
  }

  @override
  void initState() {
    super.initState();
    Future.delayed(Duration.zero, () {
      ChapterScreenArguments? arguments = chapterScreenArguments();
      String? courseId = arguments?.courseId;
      String? chapterId = arguments?.chapterId;
      bool? isReviewer = arguments?.isReviewer;
      if ((courseId != null || isReviewer == true) && chapterId != null) {
        final chapterBloc = BlocProvider.of<ChapterBloc>(context);
        chapterBloc
            .add(LoadChapterEvent(courseId: courseId, chapterId: chapterId, isReviewer: isReviewer));
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () async {
          if (storyController.moveToPreviousPage != null) {
            return !storyController.moveToPreviousPage!();
          }
          return true;
        },
        child: Scaffold(
            extendBodyBehindAppBar: true,
            backgroundColor: Colors.black,
            body: BlocListener<ChapterBloc, ContentState>(
                listener: (context, state) {
              if (state is ChapterLoaded && state.completionAction != null) {
                onCompletion = () {
                  ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
                  PerformActionEvent event =
                      PerformActionEvent(state.completionAction!);
                  actionBloc.add(event);
                };
              }
              if (state is ChapterCompleted) {
                if (this.onCompletion != null) {
                  cancelableOperation?.cancel();
                  this.onCompletion!();
                }
              }
            }, child: BlocBuilder<ChapterBloc, ContentState>(
              builder: (context, state) {
                if (state is ChapterNotLoaded) {
                  return Container();
                }
                if (state is ChapterLoading || state is ChapterCompleteLoading) {
                  return FancyLoadingIndicator(color: Colors.white);
                }
                if (state is ChapterLoaded) {
                  ChapterScreenArguments? arguments =
                  chapterScreenArguments();
                  return backgroundWrapper(
                      context: context,
                      chapterTheme: state.chapter.theme,
                      child: StoryView(
                        onPageChange: (int index, int prevPageIndex) {
                          ChapterPage chapterPage = state.chapter.pages[index];
                          String pageId = chapterPage.id;
                          RepositoryProvider.of<AnalyticsRepository>(context)
                              .logPageViewEvent(pageId: pageId, eventInfo: {
                            "pagedetails.detail1": index.toString(),
                            "pagedetails.detail2": state.chapter.code
                          });
                        },
                        storyController: storyController,
                        onCompletion: () {
                          String? courseId = arguments?.courseId;
                          String? chapterId = arguments?.chapterId;
                          if (courseId != null && chapterId != null) {
                            final chapterBloc =
                                BlocProvider.of<ChapterBloc>(context);
                            chapterBloc.add(UpdateChapterEvent(
                                courseId: courseId, chapterId: chapterId));
                            RepositoryProvider.of<AnalyticsRepository>(context)
                                .logPageClickEvent(
                                    pageId: chapterId,
                                    extraInfo: {
                                  "course_code": courseId,
                                  "completed": "true"
                                });
                          }
                        },
                        nextPage: (String fromPageIdentifier) {
                          ChapterPage? page = state.chapter.pages.firstWhere(
                              (element) => element.id == fromPageIdentifier);
                          return page.nextPageId;
                        },
                        pages: state.chapter.pages
                            .map<ContainerPageView>(
                                (page) => createPage(page, state.chapter.theme))
                            .toList(),
                        resetAction: (arguments?.reset ?? false) ? state.resetAction : null ,
                      ));
                }
                return Container();
              },
            ))));
  }

  ContainerPageView createPage(ChapterPage chapterPage, ChapterTheme? theme) {
    List<dynamic>? widgets = chapterPage.widgets;
    List<Widget> widgetViews = [];
    if (widgets != null) {
      final List widgetIndexes =
          Iterable<int>.generate(widgets.length).toList();

      widgetViews = widgetIndexes
          .map<Widget>((index) => widget.widgetBuilder
              .buildWidget({...widgets[index], "index": index}))
          .toList();
    }
    return ContainerPageView(
      identifier: chapterPage.id,
      children: widgetViews,
      ctaText: chapterPage.ctaText,
      ctaTheme: theme?.ctaTheme,
      loaderDelayOperation: (operation) {
        cancelableOperation = operation;
      },
    );
  }
}

import 'package:flutter/material.dart';
import 'package:common/util/theme.dart';

class TitleWidgetData {
  final double? verticalPadding;

  TitleWidgetData({this.verticalPadding});
}

class TitleWidget extends StatelessWidget {
  final TitleWidgetData titleWidgetData;

  const TitleWidget({Key? key, required this.titleWidgetData})
      : super(key: key);
  @override
  Widget build(BuildContext context) {
    double? verticalPadding = this.titleWidgetData.verticalPadding;
    return Padding(
        padding: EdgeInsets.symmetric(
            vertical: verticalPadding != null ? verticalPadding : 0),
        child: Text(
          "Lorem Ipsum Dolor Sit Amet",
          style: CFTextStyles.bebas40(),
        ));
  }
}

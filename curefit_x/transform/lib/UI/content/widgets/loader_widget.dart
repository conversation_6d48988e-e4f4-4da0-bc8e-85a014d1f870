import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';

class LoaderWidgetData {
  final int durationInSeconds;
  final String? type;
  final String? imageUrl;
  LoaderWidgetData({this.durationInSeconds = 5, this.type, this.imageUrl});

  factory LoaderWidgetData.fromJson(dynamic payload) {
    return LoaderWidgetData(
        durationInSeconds: payload['durationInSeconds'],
        type: payload['type'],
        imageUrl: payload['imageURL']);
  }
}

class LoaderWidget extends StatelessWidget {
  final LoaderWidgetData loaderWidgetData;

  const LoaderWidget({Key? key, required this.loaderWidgetData})
      : super(key: key);
  @override
  Widget build(BuildContext context) {
    String? imageUrl = this.loaderWidgetData.imageUrl;
    String? type = this.loaderWidgetData.type;
    if (imageUrl != null) {
      switch (type) {
        case "lottie":
          return Container(
            child: Lottie.network(getMediaUrl(imageUrl)),
          );
        case "image":
          return Container(
              child: CFNetworkImage(
            height: MediaQuery.of(context).size.height / 3,
            imageUrl: getImageUrl(context, imagePath: imageUrl),
            // height: height != null ? height : 200,
          ));
      }
    }
    return Container();
  }
}

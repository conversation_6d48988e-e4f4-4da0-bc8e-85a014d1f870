import 'package:common/util/color.dart';
import 'package:flutter/material.dart';
import 'package:transform/UI/content/constants.dart';
import 'package:transform/UI/content/questions/multi_select_question.dart';
import 'package:common/ui/components/single_select.dart';
import 'package:transform/UI/content/questions/text_input_question.dart';
import 'package:transform/blocs/content/chapter_bloc.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/content/events.dart';

enum QuestionType { TEXT, SINGLE_SELECT, MULTI_SELECT }

class QuestionWidgetData {
  final String pageId;
  final QuestionType? type;
  final String? text;
  final double questionTextSize;
  final double optionTextSize;

  final String? questionTextColor;
  final String? optionTextColor;

  final dynamic options;
  QuestionWidgetData(
      {required this.pageId,
      required this.type,
      this.text,
      this.options,
      this.questionTextSize = 18,
      this.questionTextColor,
      this.optionTextSize = 16,
      this.optionTextColor});
}

class QuestionWidget extends StatelessWidget {
  final QuestionWidgetData questionWidgetData;

  const QuestionWidget({Key? key, required this.questionWidgetData})
      : super(key: key);
  @override
  Widget build(BuildContext context) {
    String? text = this.questionWidgetData.text;
    return Padding(
        padding: EdgeInsets.symmetric(
            vertical: 0, horizontal: defaultHorizontalPadding),
        child: Column(children: [
          Text(
            text != null ? text : "",
            style: TextStyle(
                fontSize: questionWidgetData.questionTextSize,
                color: questionWidgetData.questionTextColor != null
                    ? HexColor.fromHex(questionWidgetData.questionTextColor!)
                    : Colors.black,
                fontWeight: FontWeight.bold),
          ),
          SizedBox(
            height: 20,
          ),
          createQuestionWidget(context)
        ]));
  }

  Widget createQuestionWidget(BuildContext context) {
    QuestionType? questionType = this.questionWidgetData.type;
    String pageId = this.questionWidgetData.pageId;
    dynamic options = this.questionWidgetData.options;

    if (questionType == null) return Container();
    switch (questionType) {
      case QuestionType.TEXT:
        return TextInputQuestion(pageId: pageId);
      case QuestionType.SINGLE_SELECT:
        if (options == null) return Container();
        return SingleSelectQuestion(
            showCorrectInfo: true,
            textColor: questionWidgetData.optionTextColor != null
                ? HexColor.fromHex(questionWidgetData.optionTextColor!)
                : Colors.black,
            fontSize: questionWidgetData.optionTextSize,
            onValueSelected: (value) {
              ChapterBloc chapterBloc = BlocProvider.of<ChapterBloc>(context);
              UpdateQuestionAnswer updateQuestionAnswer = UpdateQuestionAnswer(
                  pageId: pageId, selectedOptionIds: [value.id].toSet());
              chapterBloc.add(updateQuestionAnswer);
            },
            options: options
                .map<SingleSelectOption>((e) => SingleSelectOption(
                    id: e['id'].toString(),
                    title: e['text'],
                    isCorrect: e['isCorrect']))
                .toList());
      case QuestionType.MULTI_SELECT:
        if (options == null) return Container();
        return MultiSelectQuestion(
            fontSize: questionWidgetData.optionTextSize,
            textColor: questionWidgetData.optionTextColor != null
                ? HexColor.fromHex(questionWidgetData.optionTextColor!)
                : Colors.black,
            pageId: pageId,
            options: options
                .map<MultiSelectOption>((e) =>
                    MultiSelectOption(id: e['id'].toString(), text: e['text']))
                .toList());

      default:
        return Container();
    }
  }
}

import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/content_stepper.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/content/models.dart';
import 'package:common/util/theme.dart';
import 'package:common/action/action_handler.dart' as ActionHandler;

class ChapterSectionWidgetView extends StatelessWidget {
  final ChapterSectionWidgetData chapterSectionWidgetData;

  const ChapterSectionWidgetView(this.chapterSectionWidgetData);

  @override
  Widget build(BuildContext context) {
    String? title = chapterSectionWidgetData.title;
    List<ChapterTileData>? chapters = chapterSectionWidgetData.chapters;
    return Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          (title != null)
              ? Padding(
                  padding: EdgeInsets.only(left: 35, top: 50, bottom: 30),
                  child: Text(
                    title,
                    style: AuroraTheme.of(context).textStyle(TypescaleValues.H1),
                  ))
              : Container(),
          chapters != null && chapters.isNotEmpty
              ? ContentStepper(
                  physics: NeverScrollableScrollPhysics(),
                  controlsBuilder: (context,
                          {isStepActive = true,
                          onStepCancel,
                          onStepContinue,
                          stepIndex = 0,
                          stepType = ContentStepType.circle}) =>
                      Container(),
                  steps: chapters
                      .map((e) => ContentStep(
                          date: e.completedDate,
                          type: (chapterSectionWidgetData.current ?? true)
                              ? ContentStepType.circle
                              : ContentStepType.date,
                          title: Container(),
                          content: GestureDetector(
                              onTap: () {
                                ActionHandler.Action? action = e.action;
                                if (action != null && e.isLocked != true) {
                                  RepositoryProvider.of<AnalyticsRepository>(
                                          context)
                                      .logWidgetClick(
                                          extraInfo: {
                                        "action": "Chapter view",
                                        "chapterName": e.title,
                                        "chapterLocked": e.isLocked,
                                        "chapterStatus": e.status.toString(),
                                      },
                                          widgetInfo: chapterSectionWidgetData
                                              .widgetInfo);
                                  ActionBloc actionBloc =
                                      BlocProvider.of<ActionBloc>(context);
                                  PerformActionEvent event =
                                      PerformActionEvent(action);
                                  actionBloc.add(event);
                                }
                              },
                              child: ChapterTile(tileData: e)),
                          isActive: true))
                      .toList(),
                )
              : Container()
        ],
      ),
    );
  }
}

class ChapterTile extends StatelessWidget {
  final ChapterTileData tileData;

  const ChapterTile({Key? key, required this.tileData}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    String? title = tileData.title;
    String? subTitle = tileData.subTitle;
    UserChapterStatus? status = tileData.status;
    bool? isLocked = tileData.isLocked;
    return Container(
        color: Colors.transparent,
        child: Padding(
            padding: EdgeInsets.symmetric(vertical: 20, horizontal: 20),
            child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(
                              width: 45,
                              height: 45,
                              child: Stack(
                                children: [
                                  Positioned.fill(
                                    child: BlurView(
                                      borderRadius: 25,
                                      opacity: 0.2,
                                    ),
                                  ),
                                  Align(
                                      alignment: Alignment.center,
                                      child: Container(
                                        child: Icon(
                                          isLocked == true
                                              ? Icons.lock
                                              : status ==
                                                      UserChapterStatus
                                                          .IN_PROGRESS
                                                  ? Icons.timelapse
                                                  : (status ==
                                                          UserChapterStatus
                                                              .COMPLETED
                                                      ? Icons.check
                                                      : Icons.fitness_center),
                                          size: 24.0,
                                          color: Colors.white,
                                        ),
                                      ))
                                ],
                              )),
                          SizedBox(
                            width: 20,
                          ),
                          Flexible(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                title != null
                                    ? Text(
                                        title,
                                        style: AuroraTheme.of(context).textStyle(TypescaleValues.P4,
                                            color:
                                                Colors.white.withOpacity(0.5)),
                                      )
                                    : Container(),
                                subTitle != null
                                    ? Padding(
                                        padding:
                                            EdgeInsets.symmetric(vertical: 10),
                                        child: Text(subTitle,
                                            textAlign: TextAlign.left,
                                            style: AuroraTheme.of(context).textStyle(TypescaleValues.P8,
                                                color: Colors.white
                                                    .withOpacity(0.5))))
                                    : Container()
                              ],
                            ),
                          )
                        ]),
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 16.0,
                    color: Colors.white,
                  )
                ])));
  }
}

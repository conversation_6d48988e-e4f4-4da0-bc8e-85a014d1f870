import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:flutter/material.dart';
import 'package:transform/UI/content/templates/container_page_view.dart';
import 'package:flutter_svg/flutter_svg.dart';
import "dart:math";

import 'package:transform/blocs/content/models.dart';

const double defaultHorizontalPadding = 30;

Widget backgroundWrapper(
    {required BuildContext context,
    Widget? child,
    ChapterTheme? chapterTheme}) {
  final _random = new Random();
  List<String> backgrounds = [
    "image/transform/content_warm_bg.png",
    "image/transform/content_cool_bg.png"
  ];
  var asset = backgrounds[_random.nextInt(backgrounds.length)];
  return Container(
      color: Colors.white,
      child: Stack(children: [
        Positioned.fill(
            child: CFNetworkImage(
                imageUrl: getImageUrl(context,
                    imagePath: chapterTheme?.backgroundImageUrl != null
                        ? chapterTheme!.backgroundImageUrl
                        : asset),
                fit: BoxFit.fill)),
        if (child != null) Positioned.fill(child: child)
      ]));
}

import 'dart:async';

import 'package:common/action/action_handler.dart' as ActionHandler;
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/UI/content/components/current_progress.dart';
import 'package:transform/UI/content/content_list_screen.dart';
import 'package:transform/blocs/content/progress/content_progress_bloc.dart';
import 'package:transform/blocs/content/progress/events.dart';
import 'package:transform/blocs/content/progress/models.dart';
import 'package:transform/blocs/content/progress/state.dart';

class ContentProgressScreen extends StatefulWidget {
  @override
  State<StatefulWidget> createState() => _ContentProgressScreenState();
}

class _ContentProgressScreenState extends State<ContentProgressScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      final ActionHandler.ScreenArguments? args = ModalRoute.of(context)!
          .settings
          .arguments as ActionHandler.ScreenArguments?;
      if (args != null && args.params != null) {
        ContentListScreenArguments params =
            ContentListScreenArguments(args.params);
        if (params.courseId != null) {
          final contentProgressBloc =
              BlocProvider.of<ContentProgressBloc>(context);
          contentProgressBloc
              .add(LoadUserContentProgress(courseId: params.courseId!, subCategoryCode: params.subCategoryCode ?? ""));
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    AuroraThemeData themeData = AuroraTheme.of(context);
    void onCloseAction(closeAction) {
      ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
      PerformActionEvent event = PerformActionEvent(
          ActionHandler.Action.fromAction(closeAction, {}, canPop: true));
      actionBloc.add(event);
    }

    ;
    return BlocBuilder<ContentProgressBloc, ContentProgressState>(
        builder: (context, state) {
      if (state is ContentProgressStateLoading) {
        return FancyLoadingIndicator();
      } else if (state is ContentProgressStateLoaded) {
        return WillPopScope(
            onWillPop: () async {
              onCloseAction(state.contentProgressData.closeAction);
              return false;
            },
            child: Scaffold(
                extendBodyBehindAppBar: true,
                backgroundColor: themeData.backgroundTheme,
                appBar: AppBar(
                  backgroundColor: Colors.transparent,
                  iconTheme: IconThemeData(color: Colors.white),
                  leading: Container(),
                  flexibleSpace: Align(
                    alignment: Alignment.topRight,
                    child: Padding(
                      padding: EdgeInsets.fromLTRB(
                          0,
                          AuroraTheme.of(context).embeddedSafeArea.top +
                              MediaQuery.of(context).padding.top,
                          5,
                          0),
                      child: CloseButton(
                        color: Colors.white,
                        onPressed: () {
                          onCloseAction(state.contentProgressData.closeAction);
                        },
                      ),
                    ),
                  ),
                ),
                bottomSheet: IntrinsicHeight(
                  child: BlurView(
                    opacity: 0.25,
                    child: Container(
                      padding: EdgeInsets.all(Spacings.x4),
                      child: state.contentProgressData.isUnreadChapterPresent
                          ? nextChapterDetailsWidget(
                              state.contentProgressData, themeData, context)
                          : showExitWidget(
                              state.contentProgressData, themeData, context),
                    ),
                  ),
                ),
                body: Stack(
                  children: [
                    Aurora(
                      size: MediaQuery.of(context).size,
                      context: context,
                    ),
                    Container(
                      height: MediaQuery.of(context).size.height - 200,
                      padding: EdgeInsets.symmetric(horizontal: 20),
                      child: Align(
                        alignment: Alignment.center,
                        child: IntrinsicHeight(
                          child: Column(
                            children: [
                              CurrentProgress(
                                  state.contentProgressData
                                      .completedChaptersCount,
                                  progressAnimation: state
                                      .contentProgressData.progressAnimation),
                              Container(
                                padding: EdgeInsets.only(bottom: Spacings.x2),
                                child: Text(state.contentProgressData.title,
                                    style: themeData.textStyle(
                                        TypescaleValues.H1,
                                        color: Colors.white)),
                              ),
                              Text(state.contentProgressData.subTitle,
                                  style: themeData.textStyle(TypescaleValues.P1,
                                      color: Colors.white.withOpacity(0.7)),
                                  textAlign: TextAlign.center)
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                )));
      }
      return Container();
    });
  }
}

Widget nextChapterDetailsWidget(ContentProgress progressData,
    AuroraThemeData themeData, BuildContext context) {
  return Column(
    children: [
      Text(progressData.nextHeading!,
          style: themeData.textStyle(TypescaleValues.TAGTEXT,
              color: Colors.white.withOpacity(0.6))),
      Container(
        padding: EdgeInsets.symmetric(vertical: Spacings.x2),
        child: Text(progressData.nextChapterTitle!,
            style: themeData.textStyle(TypescaleValues.H1, color: Colors.white),
            textAlign: TextAlign.center),
      ),
      Container(
        padding: EdgeInsets.symmetric(vertical: Spacings.x6),
        child: Text(progressData.duration!,
            style: themeData.textStyle(TypescaleValues.TAGTEXT,
                color: Colors.white)),
      ),
      PrimaryButton(() {
        ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
        PerformActionEvent event = PerformActionEvent(progressData.nextAction!);
        actionBloc.add(event);
      }, progressData.nextAction!.title!)
    ],
  );
}

Widget showExitWidget(
    ContentProgress progress, AuroraThemeData themeData, BuildContext context) {
  return Column(
    children: [
      Container(
        padding: EdgeInsets.symmetric(vertical: Spacings.x6),
        child: Text(progress.chaptersFinishedMessage!,
            style: themeData.textStyle(TypescaleValues.P1, color: Colors.white),
            textAlign: TextAlign.center),
      ),
      PrimaryButton(() {
        ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
        PerformActionEvent event = PerformActionEvent(
            ActionHandler.Action.fromAction(progress.nextAction!, {},
                canPop: true));
        actionBloc.add(event);
      }, progress.nextAction!.title!)
    ],
  );
}

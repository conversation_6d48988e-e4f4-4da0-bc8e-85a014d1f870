import 'package:common/ui/atoms/action_button.dart';
import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/storyview/story_page_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:common/ui/storyview/widgets/media_widget.dart';
import 'package:transform/UI/content/widgets/loader_widget.dart';
import 'package:transform/blocs/content/models.dart';
import 'package:async/async.dart';

class ContainerPageView extends StoryPageView {
  final List<Widget>? children;
  final Widget? backgroundWidget;
  final CTATheme? ctaTheme;
  final double verticalPadding = 150;
  void Function(CancelableOperation<Null>)? loaderDelayOperation;

  ContainerPageView(
      {this.children,
      this.loaderDelayOperation,
      this.backgroundWidget,
      this.ctaTheme,
      required String identifier,
      String? ctaText})
      : super(identifier: identifier, ctaText: ctaText);

  @override
  State<StatefulWidget> createState() => _ContainerPageViewState();
}

class _ContainerPageViewState extends State<ContainerPageView> {
  LoaderWidget? findLoaderWidget() {
    int index =
        widget.children!.indexWhere((element) => element is LoaderWidget);
    return index != -1 ? widget.children![index] as LoaderWidget : null;
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    LoaderWidget? loaderWidget = findLoaderWidget();
    if (loaderWidget != null) {
      CancelableOperation<Null> cancelableOperation =
          CancelableOperation.fromFuture(Future.delayed(
              Duration(
                  seconds: loaderWidget.loaderWidgetData.durationInSeconds),
              () {
        if (widget.moveToNext != null) widget.moveToNext!();
      }));
      if (widget.loaderDelayOperation != null) {
        widget.loaderDelayOperation!(cancelableOperation);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    bool hasImageWidgetFirst = widget.children != null &&
        widget.children!.length > 0 &&
        widget.children!.first is MediaWidget;
    return Container(
        color: Colors.transparent,
        child: Stack(
          children: [
            Positioned.fill(
                child: widget.backgroundWidget != null
                    ? widget.backgroundWidget!
                    : Container(color: Colors.transparent)),
            AnimationLimiter(
                child: ListView.builder(
                    padding: EdgeInsets.only(
                        top: hasImageWidgetFirst ? 0 : widget.verticalPadding,
                        bottom: MediaQuery.of(context).viewInsets.bottom + 120),
                    itemCount:
                        widget.children != null ? widget.children?.length : 0,
                    itemBuilder: (context, index) {
                      Widget? child = widget.children?[index];
                      return AnimationConfiguration.staggeredList(
                        position: index,
                        duration: const Duration(milliseconds: 500),
                        child: SlideAnimation(
                          curve: Curves.linearToEaseOut,
                          verticalOffset:
                              MediaQuery.of(context).size.height / 2,
                          child: FadeInAnimation(
                              child: child != null ? child : Container()),
                        ),
                      );
                    })),
            findLoaderWidget() == null
                ? SafeArea(
                    child: Align(
                        alignment: Alignment.bottomCenter,
                        child: Padding(
                            padding: EdgeInsets.only(
                                left: 20, right: 20, bottom: 20),
                            child: footer(
                                ctaTheme: widget.ctaTheme ?? CTATheme.DARK,
                                text: widget.ctaText ?? "NEXT",
                                onPress: () {
                                  if (widget.moveToNext != null)
                                    widget.moveToNext!();
                                }))))
                : Container()
          ],
        ));
  }

  Widget footer(
      {required CTATheme ctaTheme,
      String? text,
      required void Function()? onPress}) {
    switch (ctaTheme) {
      case CTATheme.DARK:
        return ActionButton(
          onPress as Function,
          text ?? "NEXT",
          backgroundColor: Colors.black,
          textColor: Colors.white,
        );
      case CTATheme.LIGHT:
        return PrimaryButton(onPress, text ?? "NEXT");
    }
  }
}

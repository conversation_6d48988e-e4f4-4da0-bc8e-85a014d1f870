import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:flutter/material.dart';
import 'package:transform/blocs/order/models.dart';
import 'dart:math';

class NoteListWidgetView extends StatefulWidget {
  final NoteListWidgetData noteListWidgetData;

  const NoteListWidgetView(this.noteListWidgetData);

  @override
  _NoteListWidgetViewState createState() => _NoteListWidgetViewState();
}

class _NoteListWidgetViewState extends State<NoteListWidgetView> {
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 50),
      child: Column(children: [
        for (var noteItem in widget.noteListWidgetData.data)
          NoteListItemView(noteItem)
      ]),
    );
  }
}

class NoteListItemView extends StatefulWidget {
  final NoteListItem noteListItem;

  const NoteListItemView(this.noteListItem);

  @override
  _NoteListItemViewState createState() => _NoteListItemViewState();
}

class _NoteListItemViewState extends State<NoteListItemView>
    with TickerProviderStateMixin {
  bool _visible = false;
  int angle = 270;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        setState(() {
          _visible = !_visible;
          angle = (180 + angle) % 360;
        });
      },
      child: Container(
        decoration: BoxDecoration(color: Colors.white, boxShadow: [
          BoxShadow(
            color: Colors.grey,
            blurRadius: 1.0,
          )
        ]),
        padding: EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(widget.noteListItem.title!, style: AuroraTheme.of(context).textStyle(TypescaleValues.P1)),
                Transform.rotate(
                    angle: angle * pi / 180,
                    child: Icon(
                      Icons.arrow_back_ios_rounded,
                      color: Colors.black,
                      size: 16.0,
                    ))
              ],
            ),
            _visible
                ? Padding(
                    padding: EdgeInsets.only(top: 20),
                    child: Text(widget.noteListItem.info),
                  )
                : Container()
          ],
        ),
      ),
    );
  }
}

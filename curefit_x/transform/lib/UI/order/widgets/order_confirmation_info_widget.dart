import 'package:common/ui/organisms/address_accordion.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/util/color.dart';
import 'package:flutter/material.dart';
import 'package:transform/blocs/order/models.dart';

class OrderConfirmationInfoWidgetView extends StatelessWidget {
  final OrderConfirmationInfoData orderConfirmationInfoData;
  const OrderConfirmationInfoWidgetView(this.orderConfirmationInfoData);
  @override
  Widget build(BuildContext context) {
    AuroraThemeData themeData = AuroraTheme.of(context);
    return Container(
      child: Column(
        children: [
          SizedBox(height: Spacings.x2),
          if (orderConfirmationInfoData.title != null)
            Text(orderConfirmationInfoData.title!,
                style: AuroraTheme.of(context).textStyle(TypescaleValues.P4)),
          if (orderConfirmationInfoData.subtitle != null)
            Text(orderConfirmationInfoData.subtitle ?? '',
                style: AuroraTheme.of(context).textStyle(TypescaleValues.H9)),
          if (orderConfirmationInfoData.textInfoItems != null)
            ...(orderConfirmationInfoData.textInfoItems?.map((e) {
                  return Container(
                    margin: EdgeInsets.only(bottom: 8),
                    child: Text(
                      e.tag != null ? '${e.title!} # ${e.tag!}' : e.title!,
                      style: themeData.textStyle(
                          styleValue(
                              e.textStyleType ?? TextInfoItemStyleType.REGULAR),
                          color: e.tag != null
                              ? (e.color != null
                                  ? HexColor.fromHex(e.color!)
                                  : Color.fromARGB(255, 247, 199, 68))
                              : e.color != null ? HexColor.fromHex(e.color!) : null),
                    ),
                  );
                }).toList() ??
                []),
          if ((orderConfirmationInfoData.addressAccordionItemList?.length ??
                  0) >
              0)
            AddressAccordion(
                address: orderConfirmationInfoData.address!,
                itemList: orderConfirmationInfoData.addressAccordionItemList!),
          if (orderConfirmationInfoData.footer != null)
            Padding(
              padding: const EdgeInsets.only(top: 13),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Container(
                    width: scale(context, 20.0),
                    height: scale(context, 20.0),
                    child: Image.asset(
                      "assets/stopwatch.png",
                    ),
                  ),
                  SizedBox(
                    width: 10,
                  ),
                  Text(
                    (orderConfirmationInfoData.footer!).title!,
                    style: themeData.textStyle(TypescaleValues.P5,
                        color: Color.fromRGBO(255, 255, 255, 0.6)),
                  )
                ],
              ),
            )
        ],
      ),
    );
  }
}

TypescaleValues styleValue(
  TextInfoItemStyleType styleType,
) {
  switch (styleType) {
    case TextInfoItemStyleType.header1Text:
      return TypescaleValues.H1;
    case TextInfoItemStyleType.MEDIUM:
      return TypescaleValues.H4;
    case TextInfoItemStyleType.paragraph4Text:
      return TypescaleValues.P4;
    case TextInfoItemStyleType.paragraph2Text:
      return TypescaleValues.P2;
    default:
      return TypescaleValues.P4;
  }
}

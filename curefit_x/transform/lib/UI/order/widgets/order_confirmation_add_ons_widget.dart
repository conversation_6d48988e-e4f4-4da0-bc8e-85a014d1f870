import 'package:common/ui/molecules/bottom_tray.dart';
import 'package:common/ui/reminder_bottom_sheet/widgets/reminder_bottom_sheet.dart';
import 'package:flutter/material.dart';
import 'package:transform/blocs/order/models.dart';
import 'package:common/ui/atoms/action_card.dart';
import 'package:common/ui/theme/aurora_theme.dart';

class OrderConfirmationAddOnsView extends StatelessWidget {
  const OrderConfirmationAddOnsView(this.orderConfirmationAddOnsData);

  final OrderConfirmationAddOnsData orderConfirmationAddOnsData;
  @override
  Widget build(BuildContext context) {
    final themeData = AuroraTheme.of(context);
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 40),
      child: Wrap(
        runSpacing: 20,
        children: orderConfirmationAddOnsData.actionCardList
            .map((data) => ActionCard(
                  actionCardData: data,
                  onPress: orderConfirmationAddOnsData
                              .reminderSlotsScreenArguments !=
                          null
                      ? () => showBottomTray(
                          context: context,
                          child: ReminderBottomSheet(
                              reminderSlotsScreenArguments:
                                  orderConfirmationAddOnsData
                                      .reminderSlotsScreenArguments!))
                      : () {},
                ))
            .toList(),
      ),
    );
  }
}

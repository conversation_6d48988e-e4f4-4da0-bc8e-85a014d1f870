import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/util/theme.dart';
import 'package:flutter/material.dart';
import 'package:transform/blocs/order/models.dart';
import 'package:common/ui/animated_check.dart';

class CareTCConfirmInfoWidgetView extends StatefulWidget {
  final CareTCConfirmInfoWidgetData widgetData;

  const CareTCConfirmInfoWidgetView(this.widgetData);

  @override
  _CareTCConfirmInfoWidgetViewState createState() =>
      _CareTCConfirmInfoWidgetViewState();
}

class _CareTCConfirmInfoWidgetViewState
    extends State<CareTCConfirmInfoWidgetView> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController =
        AnimationController(vsync: this, duration: Duration(seconds: 2));
    _animation = new Tween<double>(begin: 0, end: 1).animate(
        new CurvedAnimation(
            parent: _animationController, curve: Curves.easeInOutCirc));
    _animationController.forward();
  }

  @override
  void dispose() {
    // TODO: implement dispose
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(children: [
        BlurView(
          borderRadius: 100,
          child: Container(
            height: scale(context, 80),
              width: scale(context, 80),
              child: Icon(
            const IconData(0xf634, fontFamily: 'MaterialIcons'),
            color: Color.fromRGBO(15, 228, 152, 1),
            size: scale(context, 45),
          )),
        ),
        SizedBox(
          height: 40,
        ),
        widget.widgetData.textInfoItems != null
            ? Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: widget.widgetData.textInfoItems!
                    .map((e) => Padding(
                        padding: EdgeInsets.only(bottom: 10, left: 70, right: 70),
                        child: Text(
                          e.title ?? e.title!,
                          style: e.textStyleType == null
                              ? styleValue(e.textStyleType!)
                              : AuroraTheme.of(context)
                                  .textStyle(TypescaleValues.H1),
                        )))
                    .toList())
            : Container(),
        if(widget.widgetData.coachImageUrl != null)
        Padding(
          padding: const EdgeInsets.all(20.0),
          child: Container(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                ClipRRect(
                  borderRadius:
                  BorderRadius.circular(50.0),
                  child: CFNetworkImage(
                    fit: BoxFit.cover,
                    imageUrl: getImageUrl(context,
                        imagePath: widget.widgetData.coachImageUrl ?? ""),
                    height: 70,
                    width: 70,
                  ),
                ),
                SizedBox(width: scale(context, 15)),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text( widget.widgetData.coachName!,
                      style: AuroraTheme.of(context)
                          .textStyle(TypescaleValues.H4),
                    ),
                    SizedBox(height: scale(context, 5)),
                    Text( widget.widgetData.coachDescription!,
                      style: AuroraTheme.of(context)
                          .textStyle(TypescaleValues.P8),
                    )
                  ],
                )
              ],
            ),
          ),
        ),
      ]),
    );
  }

  TextStyle styleValue(TextInfoItemStyleType styleType) {
    switch (styleType) {
      case TextInfoItemStyleType.BOLD:
        return AuroraTheme.of(context).textStyle(TypescaleValues.H2);
      case TextInfoItemStyleType.MEDIUM:
        return AuroraTheme.of(context).textStyle(TypescaleValues.H3);
    }
    return AuroraTheme.of(context).textStyle(TypescaleValues.H3);
  }
}

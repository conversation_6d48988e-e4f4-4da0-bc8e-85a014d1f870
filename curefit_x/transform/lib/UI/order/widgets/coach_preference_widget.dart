import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/navigation/navigation_bloc.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:common/ui/components/single_select.dart';
import 'package:common/ui/components/multi_select_question.dart';
import 'package:transform/blocs/order/models.dart';
import 'package:transform/blocs/trainer/coach_detail_bloc.dart';
import 'package:transform/blocs/trainer/events.dart';
import 'package:enum_to_string/enum_to_string.dart';

class CoachPreferenceWidgetView extends StatefulWidget {
  final CoachPreferenceWidgetData widgetData;

  const CoachPreferenceWidgetView(this.widgetData);

  @override
  State<CoachPreferenceWidgetView> createState() =>
      _CoachPreferenceWidgetViewState();
}

class _CoachPreferenceWidgetViewState extends State<CoachPreferenceWidgetView> {
  Map _selectedOptionMap = {};

  int _selectedQuestionIndex = 0;

  _CoachPreferenceWidgetViewState();

  @override
  Widget build(BuildContext context) {
    return Container(
        height: MediaQuery.of(context).size.height / 1.5,
        child: Padding(
            padding: EdgeInsets.symmetric(vertical: 20),
            child: AnimatedSwitcher(
                duration: Duration(milliseconds: 250),
                child: renderQuestion(context,
                    widget.widgetData.questions[_selectedQuestionIndex]))));
  }

  renderQuestion(BuildContext context, CoachQuestion question) {
    AuroraThemeData themeData = AuroraTheme.of(context);
    return Column(
        key: Key(question.title?.replaceAll(" ", "") ?? ""),
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          BlurView(
              borderRadius: 0,
              child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Padding(
                        padding:
                            EdgeInsets.symmetric(vertical: 20, horizontal: 20),
                        child: Text(
                          question.title ?? "",
                          textAlign: TextAlign.left,
                          style: AuroraTheme.of(context)
                              .textStyle(TypescaleValues.H1),
                        )),
                    if (question.subtitle != null)
                      Padding(
                          padding:
                              EdgeInsets.symmetric(vertical: 0, horizontal: 20),
                          child: Text(
                            question.subtitle ?? "",
                            textAlign: TextAlign.left,
                            style: AuroraTheme.of(context)
                                .textStyle(TypescaleValues.P8),
                          )),
                    question.type == "MULTIPLE_SELECT"
                        ? Padding(
                            padding: EdgeInsets.only(
                                left: 20, right: 20, bottom: 20),
                            child: MultiSelectQuestion(
                              options: question.options
                                  .map<SingleSelectOption>(
                                      (e) => SingleSelectOption(
                                            id: e.id,
                                            title: e.title,
                                            subTitle: e.subtitle,
                                          ))
                                  .toList(),
                              onValuesSelected: (List<String> value) {
                                _selectedOptionMap[question.id] = value;
                              },
                            ))
                        : Padding(
                            padding: EdgeInsets.only(
                                left: 20, right: 20, bottom: 20),
                            child: SingleSelectQuestion(
                              defaultSelectedOption:
                                  widget.widgetData.defaultOptionId,
                              textColor: Colors.white,
                              showSubtitles: true,
                              subtitleStyle: AuroraTheme.of(context).textStyle(
                                  TypescaleValues.P5,
                                  color: Colors.white60),
                              options: question.options
                                  .map<SingleSelectOption>(
                                      (e) => SingleSelectOption(
                                            id: e.id,
                                            title: e.title,
                                            subTitle: e.subtitle,
                                          ))
                                  .toList(),
                              onValueSelected: (value) {
                                _selectedOptionMap[question.id] = value.id;
                              },
                            ))
                  ])),
          Column(children: [
            if (widget.widgetData.questions.length > 1)
              Text(
                '${_selectedQuestionIndex + 1}/${widget.widgetData.questions.length}',
                style: themeData.textStyle(TypescaleValues.P8),
              ),
            SizedBox(
              height: 10,
            ),
            Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: 20,
                ),
                child: Align(
                    alignment: Alignment.bottomCenter,
                    child: PrimaryButton(() {
                      if (!isLastQuestion) {
                        setState(() {
                          _selectedQuestionIndex += 1;
                        });
                        return;
                      }
                      final coachDetailBloc =
                          BlocProvider.of<CoachDetailBloc>(context);
                      coachDetailBloc.add(SubmitCoachPreference(
                          bookingId: widget.widgetData.bookingId,
                          selectedOptionMap: _selectedOptionMap,
                          subCategoryCode: widget.widgetData.subCategoryCode));
                      String pageId =
                          BlocProvider.of<NavigationBloc>(context).currentRoute;
                      RepositoryProvider.of<AnalyticsRepository>(context)
                          .logPageClickEvent(
                              actionText: EnumToString.convertToString(
                                  widget.widgetData.widgetType),
                              pageId: pageId);
                    }, isLastQuestion ? "SUBMIT" : "NEXT",
                        horizontalPadding: 20))),
          ])
        ]);
  }

  get isLastQuestion {
    return (_selectedQuestionIndex == widget.widgetData.questions.length - 1);
  }
}

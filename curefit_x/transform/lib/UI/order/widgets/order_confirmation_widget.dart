import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/atoms/action_button.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/util/color.dart';
import 'package:common/util/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:transform/blocs/order/models.dart';
import 'package:common/ui/animated_check.dart';

class TransformConfirmationWidgetView extends StatefulWidget {
  final TransformConfirmationWidgetData? widgetData;

  const TransformConfirmationWidgetView({this.widgetData}) : super();

  @override
  _TransformConfirmationWidgetViewState createState() =>
      _TransformConfirmationWidgetViewState();
}

class _TransformConfirmationWidgetViewState
    extends State<TransformConfirmationWidgetView>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();

    _animationController =
        AnimationController(vsync: this, duration: Duration(seconds: 2));
    _animation = new Tween<double>(begin: 0, end: 1).animate(
        new CurvedAnimation(
            parent: _animationController, curve: Curves.easeInOutCirc));
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Widget buildCompanyLogo(context) {
    if ((widget.widgetData?.companyLogo != null &&
            widget.widgetData?.companyLogo != "") ||
        (widget.widgetData?.companyName != null &&
            widget.widgetData?.companyName != "")) {
      return Column(
        children: [
          SizedBox(height: Spacings.x2),
          Text(
            "sponsored by",
            style: AuroraTheme.of(context)
                .textStyle(TypescaleValues.P10, color: Colors.white60),
          ),
          SizedBox(height: Spacings.x1),
          if (widget.widgetData?.companyLogo != null &&
              widget.widgetData?.companyLogo != "")
            Container(
              height: scaleHeight(context, 20),
              width: scale(context, 100),
              child: CFNetworkImage(
                fit: BoxFit.contain,
                imageUrl: getImageUrl(
                  context,
                  imagePath: widget.widgetData?.companyLogo ?? "",
                ),
                errorWidget: (context, url, error) =>
                    Icon(Icons.error, color: Colors.white),
              ),
            )
          else if (widget.widgetData?.companyName != null &&
              widget.widgetData?.companyName != "")
            Text(
              widget.widgetData?.companyName ?? "",
              style: AuroraTheme.of(context).textStyle(TypescaleValues.P3),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            )
        ],
      );
    }
    return Container();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.all(Radius.circular(40)),
          child: BlurView(
            child: Container(
                margin: EdgeInsets.all(15),
                height: 50,
                width: 50,
                child: AnimatedCheck(
                  progress: _animation,
                  strokeWidth: 4,
                  size: 70,
                  color: Color.fromRGBO(15, 228, 152, 1),
                )),
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(
              left: Spacings.x4, right: Spacings.x4, top: Spacings.x8),
          child: Text(
            widget.widgetData?.title ?? "",
            textAlign: TextAlign.center,
            style: AuroraTheme.of(context).textStyle(TypescaleValues.H2),
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(
              left: Spacings.x4, right: Spacings.x4, top: Spacings.x2),
          child: Text(
            widget.widgetData!.subTitle!,
            textAlign: TextAlign.center,
            style: AuroraTheme.of(context).textStyle(TypescaleValues.P1,
                color: Colors.white.withOpacity(0.7)),
          ),
        ),
        buildCompanyLogo(context),
        SizedBox(height: 30),
        if (widget.widgetData!.coachInfo != null)
          Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Padding(
                padding: EdgeInsets.only(left: 30, bottom: 20),
                child: Text(
                  "What's Next",
                  style: AuroraTheme.of(context).textStyle(TypescaleValues.P1,
                      color: HexColor.fromHex("#222222")),
                )),
            Padding(
                padding: EdgeInsets.symmetric(horizontal: 20),
                child: AnimationConfiguration.synchronized(
                    child: SlideAnimation(
                        horizontalOffset: MediaQuery.of(context).size.width / 2,
                        delay: Duration(seconds: 3),
                        child: Card(
                            child: Column(children: [
                          Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Padding(
                                    padding: EdgeInsets.only(top: 10, left: 20),
                                    child: Text(
                                      String.fromCharCode(0x2022),
                                      style: TextStyle(
                                          fontSize: 30,
                                          color: HexColor.fromHex("#4da6a3")),
                                    )),
                                Padding(
                                    padding: EdgeInsets.symmetric(
                                        horizontal: 30, vertical: 20),
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          widget.widgetData!.coachInfo!.title!,
                                          style: AuroraTheme.of(context)
                                              .textStyle(TypescaleValues.P1,
                                                  color: HexColor.fromHex(
                                                      "#5c5c78")),
                                        ),
                                        SizedBox(height: 15),
                                        Text(
                                          widget.widgetData!.coachInfo!.name!,
                                          style: AuroraTheme.of(context)
                                              .textStyle(TypescaleValues.P5,
                                                  color: HexColor.fromHex(
                                                      "#5c5c78")),
                                        ),
                                        SizedBox(
                                          height: 5,
                                        ),
                                        Text(
                                          widget.widgetData!.coachInfo!
                                              .experience!,
                                          style: AuroraTheme.of(context)
                                              .textStyle(TypescaleValues.P7,
                                                  color: HexColor.fromHex(
                                                      "#afafbc")),
                                        )
                                      ],
                                    )),
                                Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      SizedBox(
                                        height: 15,
                                      ),
                                      ClipRRect(
                                          borderRadius:
                                              BorderRadius.circular(38.0),
                                          child: CFNetworkImage(
                                            width: 75,
                                            height: 75,
                                            fit: BoxFit.cover,
                                            imageUrl: getImageUrl(context,
                                                imagePath: widget.widgetData!
                                                    .coachInfo!.imageUrl!),
                                            errorWidget:
                                                (context, url, error) =>
                                                    Icon(Icons.error),
                                          ))
                                    ]),
                              ]),
                          if (widget.widgetData!.coachInfo!.action != null)
                            Column(
                                crossAxisAlignment: CrossAxisAlignment.stretch,
                                children: [
                                  Padding(
                                      padding: EdgeInsets.only(
                                          bottom: 20,
                                          top: 10,
                                          left: 20,
                                          right: 20),
                                      child: ActionButton(
                                          () {},
                                          widget.widgetData!.coachInfo!.action!
                                              .title!))
                                ])
                        ])))))
          ]),
      ],
    );
  }
}

import 'package:common/action/action_handler.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/ui/alert_view.dart';
import 'package:common/ui/atoms/floating_button.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/widget_factory.dart';
import 'package:flutter/material.dart' hide Action;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/UI/trainer/screens/coach_detail_screen.dart';
import 'package:transform/blocs/trainer/coach_detail_bloc.dart';
import 'package:transform/blocs/trainer/state.dart';
import 'package:animations/animations.dart';

import '../../../blocs/clp/clp_bloc.dart';
import '../../../blocs/clp/events.dart';
import '../../../blocs/weight_loss_tab/events.dart';
import '../../../blocs/weight_loss_tab/weight_loss_tab_bloc.dart';

enum PageState { CONFIRMED, ASSIGNING, ASSIGNED }

class OrderConfirmationScreenArguments {
  List? widgets;
  Action? pageAction;

  OrderConfirmationScreenArguments(Map<String, dynamic> payload) {
    this.widgets = payload["widgets"];
    this.pageAction =
        payload["pageAction"] != null ? Action.fromJson(payload["pageAction"]) : null;
  }
}

class OrderConfirmationScreen extends StatefulWidget {
  @override
  _OrderConfirmationScreenState createState() =>
      _OrderConfirmationScreenState();
}

class _OrderConfirmationScreenState extends State<OrderConfirmationScreen> {
  PageState pageState = PageState.CONFIRMED;

  @override
  Widget build(BuildContext context) {
    Action? pageAction = null;
    final ScreenArguments? args =
        ModalRoute.of(context)!.settings.arguments as ScreenArguments?;
    if (args != null) {
      OrderConfirmationScreenArguments arguments =
          OrderConfirmationScreenArguments(args.params);
      pageAction = arguments.pageAction;
    }
    return WillPopScope(
        onWillPop: () async {
          if (Navigator.canPop(context)) {
            Navigator.pop(context);
          }
          return false;
        },
        child: Scaffold(
            backgroundColor: Colors.black,
            appBar: AppBar(
              leading: Container(),
              flexibleSpace: Align(
                alignment: Alignment.topRight,
                child: Padding(
                  padding: EdgeInsets.fromLTRB(
                      0,
                      AuroraTheme.of(context).embeddedSafeArea.top +
                          MediaQuery.of(context).padding.top,
                      5,
                      0),
                  child: CloseButton(
                    color: Colors.white,
                    onPressed: () {
                      if (Navigator.canPop(context)) {
                        Navigator.pop(context);

                        // Navigator.of(context)
                        //     .popUntil(ModalRoute.withName('weight_loss_tab'));
                        final weightLossTab = BlocProvider.of<WeightLossTabBloc>(context);
                        weightLossTab.add(LoadWeightLossTabEvent(showLoader: true));
                        final coachCLPBloc = BlocProvider.of<CoachCLPBloc>(context);
                        coachCLPBloc.add(LoadCoachCLPEvent(showLoader: true));
                      }
                    },
                  ),
                ),
              ),
            ),
            extendBodyBehindAppBar: true,
            body: LayoutBuilder(builder: (context, constraints) {
              if (constraints.maxWidth > 0) {
                return Stack(children: [
                  Aurora(size: constraints.biggest, context: context),
                  Positioned.fill(
                      child: BlocListener<CoachDetailBloc, TrainerState>(
                          listener: (context, state) {
                            PageState newPageState;
                            switch (state.runtimeType) {
                              case CoachDetailLoading:
                                newPageState = PageState.ASSIGNING;
                                break;
                              case CoachDetailLoaded:
                                newPageState = PageState.ASSIGNED;
                                break;
                              default:
                                newPageState = PageState.CONFIRMED;
                            }
                            setState(() {
                              this.pageState = newPageState;
                            });
                          },
                          child: PageTransitionSwitcher(
                            reverse: false,
                            duration: Duration(milliseconds: 700),
                            transitionBuilder: (
                              Widget child,
                              Animation<double> animation,
                              Animation<double> secondaryAnimation,
                            ) {
                              return SharedAxisTransition(
                                child: child,
                                fillColor: Colors.transparent,
                                animation: animation,
                                secondaryAnimation: secondaryAnimation,
                                transitionType:
                                    SharedAxisTransitionType.horizontal,
                              );
                            },
                            child: currentPage(),
                          ))),
                  if (pageAction != null)
                    Positioned(
                      left: 0,
                      right: 0,
                      bottom: 0,
                      child: FloatingButton(
                        buttonText: pageAction.title ?? "DONE",
                        onPress: () {
                          ActionBloc actionBloc =
                              BlocProvider.of<ActionBloc>(context);
                          PerformActionEvent event =
                              PerformActionEvent(pageAction!);
                          actionBloc.add(event);
                        },
                        titleText: pageAction.subtitle ?? "",
                      ),
                    ),
                ]);
              }
              return Container();
            })));
  }

  currentPage() {
    switch (this.pageState) {
      case PageState.ASSIGNING:
        return coachAssigning();
      case PageState.ASSIGNED:
        return coachAssigned();
      default:
        return confirmationPage();
    }
  }

  Widget coachAssigned() {
    return BlocListener<CoachDetailBloc, TrainerState>(
        listener: (context, state) {
      if (state is TrainerNotLoaded) {
        showErrorAlert(
            context: context,
            title: state.message,
            onClose: () {
              Navigator.popUntil(context, (route) => false);
            });
      }
    }, child: BlocBuilder<CoachDetailBloc, TrainerState>(
      builder: (context, state) {
        if (state is CoachDetailLoaded) {
          return CoachDetailContainer(
              coachDetailPageData: state.coachDetailPage);
        }
        return Center(
            child: Container(
          color: Colors.transparent,
          child: Text("COACH ASSIGNED"),
        ));
      },
    ));
  }

  Widget coachAssigning() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        PageLoadingIndicator(
          color: Colors.white,
        ),
        SizedBox(
          height: 20,
        ),
        Text(
          "Assigning the best coach for you...",
          style: AuroraTheme.of(context).textStyle(TypescaleValues.H1),
        )
      ],
    );
  }

  Widget confirmationPage() {
    final ScreenArguments? args =
        ModalRoute.of(context)!.settings.arguments as ScreenArguments?;
    if (args != null) {
      OrderConfirmationScreenArguments arguments =
          OrderConfirmationScreenArguments(args.params);
      List widgets = arguments.widgets!;
      WidgetFactory widgetFactory =
          RepositoryProvider.of<WidgetFactory>(context);
      final widgetViews = widgetFactory.createWidgets(widgets);
      return ListView(children: widgetViews);
    }
    return Container();
  }
}

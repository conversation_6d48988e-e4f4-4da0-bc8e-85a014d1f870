import 'package:common/action/action_handler.dart';
import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/blocs/navigation/navigation_bloc.dart';
import 'package:common/constants/widget_constants.dart';
import 'package:common/model/checkout/models.dart' as CommonCheckoutModels;
import 'package:common/ui/alert_view.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/components/checkbox.dart' as CFCheckbox;
import 'package:common/ui/components/checkout_cta.dart';
import 'package:common/ui/flutter_scale_tap.dart';
import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/organisms/titlebar/classic_title_bar.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:common/ui/widget_factory.dart';
import 'package:common/util/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:transform/UI/checkout/widgets/payment_details_widget.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/blocs/checkout_v2/checkout_v2_events.dart';
import 'package:common/blocs/checkout_v2/checkout_v2_bloc.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:common/blocs/checkout_v2/checkout_v2_state.dart';

class HeroMeta {
  final String? title;
  final String? subTitle;
  final String? hexColor;

  HeroMeta({this.title, this.subTitle, this.hexColor});
}

class CheckoutScreenArguments {
  String? productId;
  String? coachId;
  String? subCategoryCode;
  HeroMeta? heroMeta;

  CheckoutScreenArguments(Map<String, dynamic> payload) {
    this.productId = payload["productId"];
    this.coachId = payload["coachId"];
    this.subCategoryCode =
        payload["subCategoryCode"] != null ? payload["subCategoryCode"] : "";
    if (payload['heroMeta'] != null) {
      this.heroMeta = HeroMeta(
          title: payload['heroMeta']['title'],
          subTitle: payload['heroMeta']['subTitle'],
          hexColor: payload['heroMeta']['hexColor']);
    }
  }
}

class CheckoutScreen extends StatefulWidget {
  @override
  _CheckoutScreenState createState() => _CheckoutScreenState();
}

class _CheckoutScreenState extends State<CheckoutScreen> {
  bool loading = true;
  bool consentProvided = true;
  Future<void>? _initialiseHeroBackground;
  bool clickable = true;

  @override
  void initState() {
    super.initState();
    _initialiseHeroBackground = initialiseHeroBackground();
    Future.delayed(Duration.zero, () {
      CheckoutScreenArguments? arguments = getScreenArguments(context);
      String? productId = arguments?.productId;
      if (productId != null) {
        final checkoutBloc = BlocProvider.of<CheckoutV2Bloc>(context);
        checkoutBloc.add(LoadPackCheckoutEvent(productId, arguments?.coachId,
            subCategoryCode: arguments?.subCategoryCode));
      }
    });
  }

  CheckoutScreenArguments? getScreenArguments(BuildContext context) {
    final ScreenArguments? args =
        ModalRoute.of(context)!.settings.arguments as ScreenArguments?;
    if (args != null) {
      return CheckoutScreenArguments(args.params);
    }
    return null;
  }

  Future<void> initialiseHeroBackground() async {
    await Future.delayed(Duration(milliseconds: 500));
    setState(() {
      loading = false;
    });
  }

  Widget buildCheckoutLoadedState(CheckoutLoaded state) {
    CheckoutScreenArguments? checkoutScreenArguments =
        getScreenArguments(context);
    String? url = state.checkoutPage.action.url;
    String? title = state.checkoutPage.action.title;
    bool freePayment = state.checkoutPage.freePayment;
    WidgetFactory widgetFactory = RepositoryProvider.of<WidgetFactory>(context);
    double listBottomPadding =
        state.checkoutPage.action.meta?['consentInfo'] != null ? 200 : 100;
    final widgets = widgetFactory.createWidgets(state.checkoutPage.widgets,
        screenParams: {
          "hexColor": checkoutScreenArguments?.heroMeta?.hexColor
        });
    return Stack(children: [
      Positioned.fill(
          child: AnimationLimiter(
              child: ListView(
        padding: EdgeInsets.only(bottom: listBottomPadding, top: Spacings.x20),
        children:
            applyStaggeredAnimation(widgets, context, excludeIndexes: [1]),
      ))),
      CheckoutCTA(
          enabled:
              url != null && url.length > 0 && consentProvided ? true : false,
          header: consentHeader(state),
          onPressed: () {
            if (clickable) {
              if (freePayment) { // To disable multiple API calls on multiple click
                clickable = false;
              }
              ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
              PerformActionEvent event =
                  PerformActionEvent(state.checkoutPage.action);
              actionBloc.add(event);
              String pageId =
                  BlocProvider.of<NavigationBloc>(context).currentRoute;
              final ScreenArguments? args = ModalRoute.of(context)!
                  .settings
                  .arguments as ScreenArguments?;
              RepositoryProvider.of<AnalyticsRepository>(context)
                  .logPageClickEvent(
                      actionTarget: state.checkoutPage.action.url,
                      actionText: state.checkoutPage.action.title,
                      pageId: pageId,
                      extraInfo: args?.params != null ? args!.params : {});
            }
          },
          title: title),
    ]);
  }

  Widget? consentHeader(CheckoutLoaded state) {
    if (state.checkoutPage.action.meta?['consentInfo'] != null) {
      String message =
          state.checkoutPage.action.meta?['consentInfo']['message'] ?? "";
      return Opacity(
          opacity: consentProvided ? 1 : 0.5,
          child: Container(
            child: Padding(
              padding: EdgeInsets.all(20),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  SizedBox(
                      width: MediaQuery.of(context).size.width / 1.5,
                      child: Text(
                        message,
                        style: AuroraTheme.of(context)
                            .textStyle(TypescaleValues.P3),
                      )),
                  ScaleTap(
                      onTap: () {
                        setState(() {
                          consentProvided = !consentProvided;
                          RepositoryProvider.of<AnalyticsRepository>(context)
                              .logButtonClickEvent(extraInfo: {
                            "page_id": "tf_checkout",
                            "whatsapp_legal_checkbox_consent": consentProvided,
                          });
                        });
                      },
                      child: CFCheckbox.Checkbox(
                        selected: consentProvided,
                      ))
                ],
              ),
            ),
          ));
    }
    return null;
  }

  Widget buildWidgets() {
    CheckoutScreenArguments? checkoutScreenArguments =
        getScreenArguments(context);
    return BlocListener<CheckoutV2Bloc, CheckoutV2State>(listener:
        (context, state) {
      if (state is CheckoutNotLoaded) {
        showErrorAlert(
            context: context,
            title: state.error,
            onClose: () {
              Navigator.pop(context);
            });
      }
    }, child:
        BlocBuilder<CheckoutV2Bloc, CheckoutV2State>(builder: (context, state) {
      return FutureBuilder(
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.done &&
              state is CheckoutLoaded) return buildCheckoutLoadedState(state);
          return checkoutScreenArguments?.heroMeta != null
              ? Stack(children: [
                  Positioned.fill(
                      child: SafeArea(
                          child: ListView(
                    padding: EdgeInsets.only(bottom: 100),
                    children: [
                      createHeroWidget(checkoutScreenArguments!.heroMeta!)
                    ],
                  ))),
                  if (state is CheckoutLoading && !loading)
                    FancyLoadingIndicator()
                ])
              : Container();
        },
        future: _initialiseHeroBackground,
      );
    }));
  }

  createHeroWidget(HeroMeta heroMeta) {
    PaymentDetailsWidgetData widgetData = PaymentDetailsWidgetData(
        WidgetTypes.PAYMENT_DETAILS_WIDGET,
        hexColor: heroMeta.hexColor,
        footer: heroMeta.subTitle?.toUpperCase() ?? "",
        packDuration: PackDuration(heroMeta.title ?? "", "transform"),
        priceDetails: [],
        finalPrice: CommonCheckoutModels.FinalPrice(0, 0, ""));
    return PaymentDetailWidgetView(widgetData);
  }

  @override
  Widget build(BuildContext context) {
    CheckoutScreenArguments? checkoutScreenArguments =
        getScreenArguments(context);
    return Scaffold(
        extendBodyBehindAppBar: true,
        appBar: ClassicTitleBar(
          context: context,
          hasBackground: false,
        ),
        body: LayoutBuilder(
          builder: (context, constraints) {
            if (constraints.maxWidth > 0) {
              return Stack(children: [
                Aurora(
                  size: MediaQuery.of(context).size,
                  context: context,
                ),
                Hero(
                    tag:
                        "${checkoutScreenArguments?.heroMeta?.title ?? ""}_blur",
                    child: Stack(children: [
                      Container(
                          width: constraints.maxWidth,
                          height: constraints.maxHeight,
                          child: BlurView(
                            borderRadius: 0,
                            opacity: 0.05,
                          )),
                      AnimatedOpacity(
                          opacity: loading ? 1 : 0,
                          duration: Duration(milliseconds: 250),
                          child: FancyLoadingIndicator())
                    ])),
                buildWidgets(),
              ]);
            }
            return Container();
          },
        ));
  }
}

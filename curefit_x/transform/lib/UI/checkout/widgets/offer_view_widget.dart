import 'package:common/font/cf_icons.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:common/util/color.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:common/action/action_handler.dart' as ActionHandler;
import 'package:common/ui/atoms/tertiary_button.dart';
import 'package:common/constants/action_constants.dart';
import 'package:common/font/mapping.dart';
import 'package:common/ui/atoms/secondary_button.dart';
import 'package:common/ui/theme/Colors.dart';
import 'package:common/util/action_util.dart';


class OfferWidget implements IWidgetData {
  @override
  WidgetInfo? widgetInfo;

  OfferWidget(this.widgetType, this.title, this.titleColor, this.titleFont, this.titleStyle, this.offers,
      {this.actionSupported = false});

  factory OfferWidget.fromJson(widget, WidgetTypes widgetType) {
    return OfferWidget(
      widgetType,
      widget['data']['title'],
      widget['data']['titleColor'],
      widget['data']['titleFont'],
      widget['titleStyle'],
      widget['data']['offers']
          .map<Offer>(
            (e) => Offer(e['description'],
                action: e['action'] != null
                    ? ActionHandler.Action.fromJson(e['action'])
                    : null,
                prefixImageurl: e['prefixImageurl'],
                prefixImageSize: e['prefixImageSize'],
                maxLines: e['maxLines'],
              fontSize: e['fontSize'] ?? "P4",
              hexColor: e['hexColor'] ?? "#AAAAAA"
            ),
      )
          .toList(),
      actionSupported: widget['actionSupported'] ?? false,
    );
  }

  @override
  WidgetTypes widgetType;

  String title;
  String? titleFont;
  String? titleColor;
  String? offerSpacing;
  String? titleStyle;
  bool actionSupported;
  List<Offer> offers;
}

class Offer {
  Offer(this.description, {this.action, this.prefixImageurl, this.prefixImageSize, this.maxLines, this.fontSize, this.hexColor});

  String description;
  String? prefixImageurl;
  String? prefixImageSize;
  ActionHandler.Action? action;
  int? maxLines;
  String? fontSize;
  String? hexColor;
}

class OfferWidgetView extends StatelessWidget {
  OfferWidgetView(this.offerWidget);

  final OfferWidget offerWidget;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(Spacings.x4),
      alignment: Alignment.centerLeft,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(offerWidget.title,
              style: offerWidget.titleFont != null && offerWidget.titleColor != null ?
              AuroraTheme.of(context).textStyle(
                  EnumToString.fromString(TypescaleValues.values, offerWidget.titleFont!,) ?? TypescaleValues.P4,
                  color: HexColor.fromHex(offerWidget.titleColor ?? "#FFFFFF")
              ) : getTextStyle(offerWidget.titleStyle, context)),
          for (var offer in offerWidget.offers)
            offerWidget.actionSupported
                ? InkWell(
                    onTap: () {
                      if (offer.action != null) {
                        clickActionWithAnalytics(offer.action!, context, null, {});
                      }
                    },
                    child: Padding(
                      padding: EdgeInsets.only(top: Spacings.x5),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Flexible(
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                BlurView(
                                  borderRadius: 5,
                                  child: Container(
                                    height: 35,
                                    width: 35,
                                    child: offer.prefixImageurl != null
                                        ? CFNetworkImage(
                                            fit: BoxFit.cover,
                                            height: 18,
                                            width: 18,
                                            imageUrl: getImageUrl(
                                              context,
                                              imagePath: offer.prefixImageurl!,
                                              width: MediaQuery.of(context).size.width.toInt(),
                                            ),
                                            errorWidget:
                                                (context, url, error) => Icon(
                                              CFIcons.offer_round,
                                              color: Colors.white,
                                              size: 18,
                                            ),
                                          )
                                        : Icon(
                                            CFIcons.offer_round,
                                            color: Colors.white,
                                            size: 18,
                                          ),
                                  ),
                                ),
                                SizedBox(width: Spacings.x3),
                                Flexible(
                                  child: Text(
                                    offer.description,
                                    style: AuroraTheme.of(context).textStyle(
                                        EnumToString.fromString(
                                          TypescaleValues.values, offer.fontSize!,
                                        ) ?? TypescaleValues.P4,
                                        color: HexColor.fromHex(offer.hexColor ?? "#AAAAAA")
                                    ),
                                    maxLines: offer.maxLines ?? 2,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Padding(
                            padding: const EdgeInsets.only(left: Spacings.x2),
                            child: offer.action != null ? getActionWidget(offer.action!, context) : Container()
                          ),
                        ],
                      ),
                    ),
                  )
                : Padding(
                    padding: EdgeInsets.only(top: offerWidget.offerSpacing != null ? double.parse(offerWidget.offerSpacing!) : 16),
                    child: Row(
                      children: [
                        offer.prefixImageurl != null
                            ? CFNetworkImage(
                                fit: BoxFit.cover,
                                height: offer.prefixImageSize != null ? double.parse(offer.prefixImageSize!) : 35,
                                width: offer.prefixImageSize != null ? double.parse(offer.prefixImageSize!) : 35,
                                imageUrl: getImageUrl(context,
                                    imagePath: offer.prefixImageurl!,
                                    width: MediaQuery.of(context).size.width.toInt(),
                                ),
                                errorWidget: (context, url, error) => Icon(
                                  CFIcons.offer_round,
                                  color: Colors.white,
                                  size: 18,
                                ),
                              )
                            : Icon(
                                Icons.local_offer_rounded,
                                color: Colors.white,
                                size: 20,
                              ),
                        SizedBox(width: offerWidget.offerSpacing != null ? double.parse(offerWidget.offerSpacing!) : 16),
                        Flexible(
                            child: Text(
                          offer.description,
                          style: AuroraTheme.of(context).textStyle(
                              EnumToString.fromString(
                                TypescaleValues.values, offer.fontSize!,
                              ) ?? TypescaleValues.P8,
                              color: HexColor.fromHex(offer.hexColor ?? "#FFFFFF")
                          ),
                          maxLines: offer.maxLines ?? 2,
                        )
                        ),
                      ],
                    ),
                  )
        ],
      ),
    );
  }

  Widget getActionWidget(ActionHandler.Action action, BuildContext context){
    return action.variant == null ? Icon(
        Icons.chevron_right_rounded,
        color: Colors.white,
        size: 25) : createActionButton(action, context);
  }

  Widget createActionButton(ActionHandler.Action action, BuildContext context) {
    ActionVariant? variant = EnumToString.fromString(ActionVariant.values, action.variant!);

    if(variant == ActionVariant.tertiary || variant == ActionVariant.tertiarySmall) {
      return TertiaryButton(
            () {
          clickActionWithAnalytics(action, context, null, {});
        },
        action.title ?? "",
        expanded: false,
        enabled: !action.disabled,
        textColor: action.textColor != null
            ? HexColor.fromHex(action.textColor ??  "#ffffff")
            : ColorPalette.statusNegative,
        buttonType: TertiaryButtonType.SMALL,
        alignItems: MainAxisAlignment.center,
        iconData: action.icon != null ? icon(action.icon!) : null,
        showUnderLine: action.showUnderLine,
      );
    } else if (variant == ActionVariant.secondary || variant == ActionVariant.secondarySmall) {
      return SecondaryButton(
            () {
              clickActionWithAnalytics(action, context, null, {});
        },
        action.title ?? "",
        expanded: false,
        enabled: !action.disabled,
        height: variant == ActionVariant.secondarySmall ? 40 : 50,
        iconData: action.icon != null ? icon(action.icon!) : null,
        buttonType: variant == ActionVariant.secondarySmall ? SecondaryButtonType.SMALL : SecondaryButtonType.BIG,
      );
    }
    return Container();
  }

  TextStyle getTextStyle(String? style, BuildContext context) {
    switch (style) {
      case "TEXT_16_700":
        return AuroraTheme.of(context).textStyle(TypescaleValues.H4);
      default:
        return AuroraTheme.of(context).textStyle(TypescaleValues.H2);
    }
  }
}

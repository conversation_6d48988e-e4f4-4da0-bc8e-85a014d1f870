import 'package:common/ui/widget_builder.dart';
import 'package:flutter/material.dart';
import 'package:common/action/action_handler.dart' as actionHandler;
import 'package:common/ui/header/card_header.dart';
import 'package:common/ui/ui_toolkit/ui_utils.dart';
import 'package:common/data/cf_text_data.dart';
import 'package:common/ui/atoms/tertiary_button.dart';
import 'package:common/ui/atoms/twin_button.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/util/action_util.dart';
import 'package:flutter/services.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/toast.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/font/cf_icons.dart';
import 'package:common/ui/theme/Colors.dart';


class ShareActionWidgetV2 extends StatelessWidget {
  final ShareActionWidgetDataV2 widgetData;

  const ShareActionWidgetV2({required this.widgetData, Key? key})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: Spacings.x3),
      child: Column(
        children: [
          if (widgetData.header != null)
            Padding(
              padding: EdgeInsets.only(bottom: Spacings.x3),
              child: WidgetHeader(cardHeaderData: widgetData.header!),
            ),
          if (widgetData.title != null)
            UiUtils.getCFTextWidget(context, widgetData.title),
          if (widgetData.referralLink != null)
            Padding(
              padding: EdgeInsets.symmetric(vertical: Spacings.x2),
              child: BlurView(
                borderRadius: Spacings.x1,
                blurType: BlurType.HIGH,
                child: Container(
                  padding: EdgeInsets.symmetric(vertical: Spacings.x2, horizontal: Spacings.x2),
                  child: Row(
                    children: [
                      Flexible(
                        child: Container(
                          width: MediaQuery.of(context).size.width,
                          padding: EdgeInsets.only(left: Spacings.x2),
                          child: UiUtils.getCFTextWidget(context, widgetData.referralLink),
                        ),
                      ),
                      SizedBox(width: Spacings.x1),
                      Container(
                        width: scale(context, 80),
                        child: TertiaryButton(
                              () {
                            String copyText = widgetData.referralLink?.text ?? "";
                            Clipboard.setData(ClipboardData(text: copyText))
                                .then((_) {
                              Toast.show("REFERRAL LINK COPIED", context,
                                  duration: Toast.lengthShort,
                                  gravity: Toast.top,
                                  textStyle: AuroraTheme.of(context)
                                      .textStyle(TypescaleValues.P3, color: Colors.black),
                                  backgroundColor: Colors.white,
                                  icon: const Icon(
                                    CFIcons.tick_circle,
                                    color: ColorPalette.statusPositive,
                                    size: 20,
                                  ));
                            });
                          },
                          "COPY",
                          iconData: widgetData.showCopyIcon == true ? Icons.copy_rounded : null,
                          buttonType: TertiaryButtonType.SMALL,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          if (widgetData.actionList != null)...[
            SizedBox(height: Spacings.x2),
            TwinButton(
              data: widgetData.actionList,
              onPress: (actionHandler.Action action) {
                clickActionWithAnalytics(action, context, widgetData.widgetInfo, {});
              },
              horizontal: false,
            )
          ]
        ],
      ),
    );
  }
}

class ShareActionWidgetDataV2 implements IWidgetData {

  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;

  final WidgetHeaderData? header;
  final CFTextData? title;
  final CFTextData? referralLink;
  final bool? showCopyIcon;
  final List<actionHandler.Action>? actionList;

  ShareActionWidgetDataV2(
      this.widgetType, {
        this.widgetInfo,
        this.title,
        this.referralLink,
        this.showCopyIcon,
        this.header,
        this.actionList,
      });

  factory ShareActionWidgetDataV2.fromJson(WidgetTypes widgetType, dynamic payload, WidgetInfo? widgetInfo) {
    return ShareActionWidgetDataV2(
      widgetType,
      widgetInfo: widgetInfo,
      header: payload['header'] != null
          ? WidgetHeaderData.fromJson(payload['header'])
          : null,
      title: payload['title'] != null ? CFTextData.fromJson(payload['title']) : null,
      referralLink: payload['referralLink'] != null ? CFTextData.fromJson(payload['referralLink']) : null,
      showCopyIcon: payload['showCopyIcon'] ?? false,
      actionList: payload['actionList'] != null
          ? payload['actionList']
          .map<actionHandler.Action>((action) => actionHandler.Action.fromJson(action))
          .toList()
          : null,
    );
  }
}
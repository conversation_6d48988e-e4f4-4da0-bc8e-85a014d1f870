import 'dart:math';

import 'package:common/action/action_handler.dart' as ActionHandler;
import 'package:common/model/checkout/models.dart';
import 'package:common/ui/theme/Colors.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:common/util/action_util.dart';
import 'package:common/util/color.dart';
import 'package:common/analytics/analytics_repository.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shimmer/shimmer.dart';
import 'package:common/data/cf_text_data.dart';
import 'package:common/ui/ui_toolkit/ui_utils.dart';
import 'package:common/font/mapping.dart';


class PriceDetail {
  PriceDetail(
    this.title,
    this.value,
    this.isDiscount,
    this.showBottomDivider,
    this.action, {
    this.symbol,
    this.prefixIcon,
  });

  String title;
  String value;
  String? symbol;
  bool isDiscount;
  bool showBottomDivider;
  ActionHandler.Action? action;
  String? prefixIcon;
}

class PackDuration {
  PackDuration(
    this.value,
    this.type,
  );

  String type;
  String value;
}

class PackName {
  PackName(
    this.title,
    this.subtitle1,
    this.subtitle2,
  );

  CFTextData? title;
  CFTextData? subtitle1;
  CFTextData? subtitle2;
}

class PaymentDetailsWidgetData implements IWidgetData {
  @override
  WidgetInfo? widgetInfo;

  PaymentDetailsWidgetData(
    this.widgetType, {
    required this.packDuration,
    this.packName,
    required this.priceDetails,
    required this.finalPrice,
    this.packHeader,
    this.hexColor,
    this.footer,
    this.expiryDate,
    this.topPadding,
    this.wrapInGradient,
    this.isCollapseOpen = true,
    this.showCollapse = false,
  });

  @override
  WidgetTypes widgetType;

  PackDuration packDuration;
  PackName? packName;
  List<PriceDetail> priceDetails;
  String? footer;
  String? packHeader;
  FinalPrice finalPrice;
  String? hexColor;
  String? expiryDate;
  double? topPadding;
  bool? wrapInGradient;
  bool showCollapse;
  bool isCollapseOpen;

  factory PaymentDetailsWidgetData.fromJson(payload, WidgetTypes widgetType) {
    int? topPadding = payload['topPadding'];
    return PaymentDetailsWidgetData(
      widgetType,
      footer: payload["footer"],
      hexColor: payload["hexColor"],
      packHeader: payload['packHeader'],
      packDuration: PackDuration(
          payload['duration'] != null ? payload['duration'] : "",
          payload['suffix'] != null ? payload['suffix'] : ""),
      packName: payload['packName'] != null ? PackName(
          payload['packName']['title'] != null ? CFTextData.fromJson(payload['packName']['title']): null,
          payload['packName']['subtitle1'] != null ? CFTextData.fromJson(payload['packName']['subtitle1']): null,
          payload['packName']['subtitle2'] != null ? CFTextData.fromJson(payload['packName']['subtitle2']): null
      ) : null,
      priceDetails: payload['priceDetails']
          .map<PriceDetail>((price) => PriceDetail(
              price['title'],
              price['value'],
              price['isDiscount'] ?? false,
              price['showBottomDivider'] ?? false,
              price['action'] != null
                  ? ActionHandler.Action.fromJson(price['action'])
                  : null,
              symbol: price['symbol'],
        prefixIcon: price['prefixIcon'],
      ))
          .toList(),
      finalPrice: FinalPrice(payload['finalPrice']['listingPrice'],
          payload['finalPrice']['mrp'], payload['finalPrice']['currency']),
      expiryDate: payload['expiryDate'],
      topPadding: topPadding != null ? topPadding.toDouble() : 0,
      wrapInGradient: payload['wrapInGradient'],
      showCollapse: payload['showCollapse'] ?? false,
      isCollapseOpen: payload['isCollapseOpen'] ?? true,
    );
  }
}

class PaymentDetailWidgetView extends StatefulWidget {
  PaymentDetailWidgetView(this.paymentDetailWidgetData);

  final PaymentDetailsWidgetData paymentDetailWidgetData;

  @override
  State<PaymentDetailWidgetView> createState() =>
      _PaymentDetailWidgetViewState();
}

class _PaymentDetailWidgetViewState extends State<PaymentDetailWidgetView> {
  bool isCollapsedOpen = false;

  Widget getPriceRowWidget() {
    int totalPriceWidgetLength =
        widget.paymentDetailWidgetData.priceDetails.length;

    if (!widget.paymentDetailWidgetData.showCollapse) {
      List<Widget> priceWidgets = [];
      for (int i = 0; i < totalPriceWidgetLength; i++)
        priceWidgets.add(PriceRow(
          priceDetail: widget.paymentDetailWidgetData.priceDetails[i],
          isTotal: i == totalPriceWidgetLength - 1,
          showCollapse: false,
          isCollapsed: false,
        ));
      return Column(
        children: applyStaggeredAnimation(
          priceWidgets,
          context,
        ),
      );
    }
    List<Widget> priceWidgets = [];
    for (int i = 0; i < totalPriceWidgetLength - 1; i++)
      priceWidgets.add(
        PriceRow(
          priceDetail: widget.paymentDetailWidgetData.priceDetails[i],
          isTotal: false,
          showCollapse: false,
          isCollapsed: false,
          withExtraPadding: true,
        ),
      );
    return Column(
      children: [
        AnimatedSize(
          duration: Duration(milliseconds: 250),
          child: (isCollapsedOpen)
              ? Column(
                  children: applyStaggeredAnimation(
                    priceWidgets,
                    context,
                  ),
                )
              : Container(),
        ),
        ...applyStaggeredAnimation([
          PriceRow(
            priceDetail: widget.paymentDetailWidgetData.priceDetails.last,
            isTotal: true,
            showCollapse: widget.paymentDetailWidgetData.showCollapse,
            isCollapsed: !isCollapsedOpen,
            onClick: (bool isCollapsed) {
              setState(() {
                isCollapsedOpen = !isCollapsed;
              });
            },
            withExtraPadding: true,
          ),
        ], context),
      ],
    );
  }

  Widget getPaymentWidget(BuildContext context) {
    Color titleColor = widget.paymentDetailWidgetData.hexColor != null
        ? HexColor.fromHex(widget.paymentDetailWidgetData.hexColor!)
        : Colors.white;
    String? expiryDate = widget.paymentDetailWidgetData.expiryDate;
    return Padding(
      padding: EdgeInsets.only(
          top: widget.paymentDetailWidgetData.topPadding != null
              ? widget.paymentDetailWidgetData.topPadding!
              : 0),
      child: Hero(
          tag: '${widget.paymentDetailWidgetData.packDuration.value}_title',
          child: Container(
            padding: EdgeInsets.symmetric(vertical: 30, horizontal: 20),
            child: Column(
              children: [
                widget.paymentDetailWidgetData.packDuration.value.length > 0
                    ? Column(
                        children: [
                          Material(
                            color: Colors.transparent,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Container(
                                  constraints: BoxConstraints(
                                    maxWidth: Spacings.x20,
                                  ),
                                  child: Column(
                                    children: [
                                      Shimmer.fromColors(
                                        loop: 1,
                                        period: Duration(milliseconds: 2500),
                                        baseColor: titleColor,
                                        highlightColor:
                                            titleColor.withOpacity(0.1),
                                        child: Text(
                                          widget.paymentDetailWidgetData
                                              .packDuration.value,
                                          style:
                                              AuroraTheme.of(context).textStyle(
                                            TypescaleValues.H5,
                                            color: widget
                                                        .paymentDetailWidgetData
                                                        .hexColor !=
                                                    null
                                                ? HexColor.fromHex(widget
                                                    .paymentDetailWidgetData
                                                    .hexColor!)
                                                : Colors.white,
                                          ),
                                        ),
                                      ),
                                      Text(
                                        widget.paymentDetailWidgetData.footer ??
                                            "",
                                        style:
                                            AuroraTheme.of(context).textStyle(
                                          TypescaleValues.P8,
                                          color: widget.paymentDetailWidgetData
                                                      .hexColor !=
                                                  null
                                              ? HexColor.fromHex(
                                                  widget.paymentDetailWidgetData
                                                      .hexColor!,
                                                )
                                              : Colors.white,
                                        ),
                                      )
                                    ],
                                  ),
                                ),
                                Container(
                                  constraints: BoxConstraints(
                                    maxWidth:
                                        MediaQuery.of(context).size.width -
                                            Spacings.x20,
                                  ),
                                  child: Padding(
                                    padding: const EdgeInsets.only(left: 10.0),
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        if(widget.paymentDetailWidgetData.packName != null)...[
                                          UiUtils.getCFTextWidget(context, widget.paymentDetailWidgetData.packName?.title),
                                          Row(
                                            mainAxisAlignment: MainAxisAlignment.start,
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              UiUtils.getCFTextWidget(context, widget.paymentDetailWidgetData.packName?.subtitle1),
                                              UiUtils.getCFTextWidget(context, widget.paymentDetailWidgetData.packName?.subtitle2),
                                            ],
                                          )
                                        ]
                                        else ...[
                                          Text(
                                            widget.paymentDetailWidgetData
                                                .packDuration.type,
                                            style: AuroraTheme.of(context)
                                                .textStyle(TypescaleValues.H2),
                                          ),
                                          if (expiryDate != null)
                                            Text(
                                              'Use by: $expiryDate',
                                              style: AuroraTheme.of(context)
                                                  .textStyle(
                                                TypescaleValues.P5,
                                                color: ColorPalette.errorRed,
                                              ),
                                            ),
                                        ]
                                      ],
                                    ),
                                  ),
                                )
                              ],
                            ),
                          ),
                          SizedBox(height: 40),
                        ],
                      )
                    : Container(),
                getPriceRowWidget(),
              ],
            ),
          )),
    );
  }

  @override
  void initState() {
    try {
      RepositoryProvider.of<AnalyticsRepository>(context)
          .logCustomWidgetViewEvent(extraInfo: {
        "widgetType": "payment_details_widget",
        "showCollapse": widget.paymentDetailWidgetData.showCollapse,
        "productId": widget.paymentDetailWidgetData.packDuration.type,
      });
    } catch(e) {}
    setState(() {
      isCollapsedOpen = widget.paymentDetailWidgetData.isCollapseOpen;
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return widget.paymentDetailWidgetData.wrapInGradient == true
        ? wrapInGradientBg(getPaymentWidget(context))
        : getPaymentWidget(context);
  }
}

class PriceRow extends StatefulWidget {
  final PriceDetail priceDetail;
  final bool isTotal;
  final bool showCollapse;
  final bool isCollapsed;
  final bool withExtraPadding;
  final Function? onClick;

  PriceRow({
    required this.priceDetail,
    required this.isTotal,
    this.showCollapse = false,
    this.isCollapsed = false,
    this.onClick = null,
    this.withExtraPadding = false,
  });

  @override
  State<PriceRow> createState() => _PriceRowState();
}

class _PriceRowState extends State<PriceRow> {
  int _angle = 270;
  bool isCollapsed = false;

  _clickAction(BuildContext context) {
    if (widget.priceDetail.action != null) {
      clickActionWithAnalytics(widget.priceDetail.action!, context, null, {});
    }
  }

  @override
  void initState() {
    setState(() {
      isCollapsed = widget.isCollapsed;
      _angle = (widget.isCollapsed) ? 90 : 270;
    });
    if (widget.isTotal) {
      RepositoryProvider.of<AnalyticsRepository>(context)
          .logButtonClickEvent(extraInfo: {
        "widget": "PAYMENT_DETAILS_WIDGET",
        "showCollapse": widget.showCollapse,
        "isCollapsed": isCollapsed
      });
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        _clickAction(context);
        if (this.widget.isTotal && widget.showCollapse) {
          setState(() {
            isCollapsed = !isCollapsed;
            if (isCollapsed)
              _angle = 90;
            else
              _angle = 270;
          });
        }
        if (widget.onClick != null) {
          widget.onClick!(isCollapsed);
        }
        if (widget.isTotal) {
          RepositoryProvider.of<AnalyticsRepository>(context)
              .logButtonClickEvent(extraInfo: {
            "widget": "PAYMENT_DETAILS_WIDGET",
            "showCollapse": widget.showCollapse,
            "isCollapsed": isCollapsed
          });
        }
      },
      child: AnimatedSize(
        duration: Duration(milliseconds: 250),
        child: Padding(
          padding: EdgeInsets.only(bottom: 10),
          child: Column(
            children: [
              if (this.widget.isTotal &&
                  (!widget.showCollapse ||
                      (widget.showCollapse && !isCollapsed)))
                Padding(
                  padding: EdgeInsets.symmetric(vertical: Spacings.x3),
                  child: Container(
                    height: 1,
                    color: Colors.white.withOpacity(0.3),
                  ),
                ),
              Padding(
                padding: EdgeInsets.only(
                    left: (this.widget.isTotal || !this.widget.withExtraPadding)
                        ? 0
                        : Spacings.x2),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    if (widget.priceDetail.action == null)
                      Text(
                        widget.priceDetail.title,
                        style: !this.widget.isTotal
                            ? AuroraTheme.of(context).textStyle(
                                TypescaleValues.P5,
                                color: Colors.white.withOpacity(0.7))
                            : AuroraTheme.of(context)
                                .textStyle(TypescaleValues.H2),
                      ),
                    if (widget.priceDetail.action != null)
                      Row(
                        children: [
                          Text(
                            widget.priceDetail.title,
                            style: AuroraTheme.of(context)
                                .textStyle(TypescaleValues.P5,
                                    color: Colors.white.withOpacity(0.7))
                                .copyWith(
                              shadows: [
                                Shadow(
                                  color: Colors.white.withOpacity(0.7),
                                  offset: Offset(0, -3),
                                )
                              ],
                              color: Colors.transparent,
                              decoration: TextDecoration.underline,
                              decorationStyle: TextDecorationStyle.dashed,
                              decorationColor: Colors.white.withOpacity(0.6),
                              decorationThickness: 1,
                            ),
                          ),
                          if (widget.priceDetail.prefixIcon != null)
                          GestureDetector(
                            child: Icon(
                              icon(widget.priceDetail.prefixIcon!),
                              color: ColorPalette.white,
                              size: 20,
                            ),
                          ),
                        ],
                      ),
                    Row(
                      children: [
                        Text(
                          (widget.priceDetail.isDiscount ? "-" : "") +
                              widget.priceDetail.value,
                          style: !this.widget.isTotal
                              ? AuroraTheme.of(context).textStyle(
                                  TypescaleValues.P5,
                                  color: Colors.white.withOpacity(0.7))
                              : AuroraTheme.of(context).textStyle(
                                  TypescaleValues.H2,
                                ),
                        ),
                        SizedBox(
                          width: (this.widget.withExtraPadding)
                              ? (this.widget.isTotal)
                                  ? Spacings.x1
                                  : Spacings.x4
                              : 0,
                        ),
                        if (this.widget.isTotal && widget.showCollapse)
                          Transform.rotate(
                            angle: _angle / 180 * pi,
                            child: Icon(
                              Icons.arrow_forward_ios,
                              color: ColorPalette.white60,
                              size: 20.0,
                            ),
                          ),
                      ],
                    )
                  ],
                ),
              ),
              if (this.widget.priceDetail.showBottomDivider)
                Padding(
                  padding: EdgeInsets.only(top: Spacings.x2),
                  child: Container(
                    height: 0.5,
                    color: Colors.white.withOpacity(0.2),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}

Widget wrapInGradientBg(Widget child) {
  return Container(
    decoration: const BoxDecoration(
      gradient: LinearGradient(
        colors: [
          Colors.transparent,
          Colors.white10,
        ],
        begin: Alignment.topCenter,
        end: Alignment.bottomCenter,
      ),
    ),
    child: child,
  );
}

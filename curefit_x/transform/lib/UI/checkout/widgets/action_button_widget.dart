import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:flutter/material.dart';

class ActionButtonData {
  ActionButtonData(this.title, this.meta);

  String? title;
  Map<String, String> meta;
}

class ActionButtonView extends StatelessWidget {
  ActionButtonView(this.actionButton);

  final ActionButtonData actionButton;

  @override
  Widget build(BuildContext context) {
    return ConstrainedBox(
        constraints: const BoxConstraints(minWidth: double.infinity),
        child: Container(
            padding: EdgeInsets.all(20),
            child: ElevatedButton(
              child: Text(actionButton.title!,
                  style: AuroraTheme.of(context).textStyle(TypescaleValues.P3)),
              onPressed: () {
                print("Move to payment screen");
              },
              style: ElevatedButton.styleFrom(
                foregroundColor: Colors.white,
                backgroundColor: Color.fromRGBO(34, 34, 34, 1),
                padding: EdgeInsets.all(15),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10.0),
                ),
              ),
            )));
  }
}

import 'dart:math';

import 'package:common/action/action_handler.dart' as action_handler;
import 'package:common/analytics/analytics_repository.dart';
import 'package:common/constants/action_constants.dart';
import 'package:common/data/cf_gradient_data.dart';
import 'package:common/data/cf_text_data.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/atoms/tertiary_button.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/gradient_text.dart';
import 'package:common/ui/theme/Colors.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit/ui_utils.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:common/util/action_util.dart';
import 'package:common/util/color.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/pack_purchase/models.dart';

class ProductListItem {
  ProductListItem(
      {this.title,
      this.titleFont,
      this.titleColor,
      this.titleGradientColor,
      this.subtitle,
      this.subTitleColor,
      this.subTitleFont,
      this.subTitleGradientColor,
      this.icon,
      this.number,
      this.iconSize,
      this.padding,
      this.topSpacing,
      this.action});

  String? title;
  String? titleFont;
  String? titleColor;
  CFGradientColorData? titleGradientColor;
  String? subtitle;
  String? subTitleFont;
  String? subTitleColor;
  CFGradientColorData? subTitleGradientColor;
  String? icon;
  String? number;
  double? iconSize;
  double? padding;
  double? topSpacing;
  action_handler.Action? action;
}

class ProductListWidgetData implements IWidgetData {
  ProductListWidgetData(
    this.widgetType,
    this.header,
    this.headerGradientColor,
    this.headerRightAction,
    this.footerAction,
    this.iconBackgroundOpacity,
    this.items, {
    this.hideSeparatorLines = false,
    this.showSquareIcons = false,
    this.showItemSeparatorLine = false,
    this.isCollapsable = true,
    this.widgetInfo,
    this.hasDividerBelow = false,
    this.hasDividerTop = false,
    this.textData,
  });

  @override
  WidgetInfo? widgetInfo;

  factory ProductListWidgetData.fromJson(
      widget, WidgetTypes widgetType, WidgetInfo? widgetInfo) {
    return ProductListWidgetData(
      widgetType,
      WidgetHeader(title: widget['header'] != null ? widget['header']['title'] : null),
      widget['headerGradientColor'] != null
          ? CFGradientColorData.fromJson(widget['headerGradientColor'])
          : null,
      widget['headerRightAction'] != null
          ? action_handler.Action.fromJson(widget['headerRightAction'])
          : null,
      widget['footerAction'] != null
          ? action_handler.Action.fromJson(widget['footerAction'])
          : null,
      widget['iconBackgroundOpacity'] != null
          ? double.tryParse(widget['iconBackgroundOpacity'].toString())
          : 0.1,
      widget['items']
          .map<ProductListItem>((e) => ProductListItem(
              subtitle: e['subTitle'],
              subTitleFont: e['subTitleFont'],
              subTitleColor: e['subTitleColor'],
              subTitleGradientColor: e['subTitleGradientColor'] != null
                  ? CFGradientColorData.fromJson(e['subTitleGradientColor'])
                  : null,
              icon: e['icon'],
              number: e['number'],
              iconSize: e['iconSize'] != null
                  ? double.tryParse(e['iconSize'].toString())
                  : null,
              padding:
                  e['padding'] != null ? e['padding'].toDouble() : Spacings.x2,
              topSpacing: e['topSpacing'] != null
                  ? double.parse(e['topSpacing'].toString())
                  : Spacings.x5,
              title: e['title'],
              titleColor: e['titleColor'],
              titleFont: e['titleFont'],
              titleGradientColor: e['titleGradientColor'] != null
                  ? CFGradientColorData.fromJson(e['titleGradientColor'])
                  : null,
              action: e['seeMoreAction'] != null
                  ? action_handler.Action.fromJson(e['seeMoreAction'])
                  : null))
          .toList(),
      hideSeparatorLines:
          widget['hideSeparatorLines'] ?? widget['hideSepratorLines'] ?? true,
      showSquareIcons: widget['showSquareIcons'] ?? false,
      showItemSeparatorLine: widget['showItemSeparatorLine'] ?? false,
      isCollapsable: widget['collapsable'] ?? true,
      widgetInfo: widgetInfo,
      hasDividerBelow: widget['hasDividerBelow'] ?? false,
      hasDividerTop: widget['hasDividerTop'] ?? false,
      textData: widget['header']?['textData'] != null
          ? CFTextData.fromJson(widget['header']['textData'])
          : null,
    );
  }

  @override
  WidgetTypes widgetType;

  WidgetHeader header;
  CFTextData? textData;
  CFGradientColorData? headerGradientColor;
  action_handler.Action? headerRightAction;
  action_handler.Action? footerAction;
  double? iconBackgroundOpacity;
  List<ProductListItem> items;
  bool hideSeparatorLines;
  bool showSquareIcons;
  bool showItemSeparatorLine;
  bool isCollapsable;
  bool? hasDividerBelow;
  bool? hasDividerTop;
}

class ProductListWidgetView extends StatefulWidget {
  final ProductListWidgetData productListWidgetData;

  ProductListWidgetView(this.productListWidgetData);

  @override
  _ProductListWidgetViewState createState() => _ProductListWidgetViewState();
}

class _ProductListWidgetViewState extends State<ProductListWidgetView>
    with TickerProviderStateMixin {
  bool _visible = true;
  int _angle = 270;

  @override
  void initState() {
    try {
      if (widget.productListWidgetData.widgetInfo?.widgetMetric
              .isWidgetImpressionRequired ??
          false) {
        RepositoryProvider.of<AnalyticsRepository>(context)
            .logCustomWidgetViewEvent(extraInfo: {
          "widgetType": "PRODUCT_LIST_WIDGET",
          "widgetName":
              widget.productListWidgetData.widgetInfo?.widgetMetric.widgetName,
          "title": widget.productListWidgetData.header.title
        });
      }
    } catch (e) {}
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (widget.productListWidgetData.hasDividerTop == true)
          Divider(
            thickness: 1,
            color: ColorPalette.white20,
          ),
        Container(
          padding: EdgeInsets.only(
            top: Spacings.x6,
            left: Spacings.x4,
            right: Spacings.x4,
            bottom: Spacings.x6,
          ),
          alignment: Alignment.centerLeft,
          decoration: widget.productListWidgetData.hideSeparatorLines != true
              ? BoxDecoration(
                  border: Border.symmetric(
                      horizontal: BorderSide(
                          color: ColorPalette.white.withOpacity(0.3),
                          width: 0.5)),
                )
              : null,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if ((widget.productListWidgetData.header.title != null &&
                      widget.productListWidgetData.header.title!.isNotEmpty) ||
                  widget.productListWidgetData.textData != null)
                GestureDetector(
                  onTap: () {
                    setState(() {
                      if (widget.productListWidgetData.isCollapsable == true) {
                        _visible = !_visible;
                        _angle = (_angle + 180) % 360;
                      }
                    });
                  },
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      if ((widget.productListWidgetData.header.title != null &&
                          widget.productListWidgetData.header.title!
                              .isNotEmpty)) ...[
                        if (widget.productListWidgetData.headerGradientColor !=
                            null)
                          GradientText(
                            widget.productListWidgetData.header.title ?? "",
                            UiUtils.getGradientData(
                              widget.productListWidgetData.headerGradientColor,
                            ),
                            style: AuroraTheme.of(context)
                                .textStyle(TypescaleValues.H2),
                          )
                        else
                          Text(
                            widget.productListWidgetData.header.title!,
                            style: AuroraTheme.of(context)
                                .textStyle(TypescaleValues.H2),
                          ),
                      ],
                      UiUtils.getCFTextWidget(
                          context, widget.productListWidgetData.textData),
                      if (widget.productListWidgetData.headerRightAction !=
                          null) ...[
                        TertiaryButton(
                          () {
                            clickActionWithAnalytics(
                                widget.productListWidgetData.headerRightAction!,
                                context,
                                widget.productListWidgetData.widgetInfo, {});
                          },
                          widget.productListWidgetData.headerRightAction!
                                  .title ??
                              "",
                          buttonType:
                              widget.productListWidgetData.headerRightAction! ==
                                      ActionVariant.tertiarySmall
                                  ? TertiaryButtonType.SMALL
                                  : TertiaryButtonType.BIG,
                          textColor: widget.productListWidgetData
                                      .headerRightAction!.meta?['textColor'] !=
                                  null
                              ? HexColor.fromHex((widget.productListWidgetData
                                      .headerRightAction!.meta?['textColor'])
                                  .toString())
                              : Colors.white,
                          expanded: false,
                        ),
                      ],
                      if (widget.productListWidgetData.isCollapsable == true)
                        Transform.rotate(
                            angle: _angle / 180 * pi,
                            child: Icon(
                              Icons.arrow_forward_ios,
                              color: ColorPalette.white,
                              size: 20.0,
                            ))
                    ],
                  ),
                ),
              AnimatedSize(
                duration: Duration(milliseconds: 250),
                child: _visible
                    ? ProductListItems(widget.productListWidgetData.items,
                        widget.productListWidgetData.showItemSeparatorLine,
                        widget.productListWidgetData.iconBackgroundOpacity,
                        showSquareIcons:
                            widget.productListWidgetData.showSquareIcons,
                    )
                    : Container(),
              ),
              if (widget.productListWidgetData.footerAction != null) ...[
                SizedBox(height: 50),
                Align(
                  alignment: Alignment.center,
                  child: TertiaryButton(
                        () {
                      clickActionWithAnalytics(
                          widget.productListWidgetData.footerAction!,
                          context,
                          widget.productListWidgetData.widgetInfo, {});
                    },
                    widget.productListWidgetData.footerAction!
                        .title ??
                        "",
                    buttonType:
                    widget.productListWidgetData.footerAction! ==
                        ActionVariant.tertiarySmall
                        ? TertiaryButtonType.SMALL
                        : TertiaryButtonType.BIG,
                    textColor: widget.productListWidgetData
                        .footerAction!.meta?['textColor'] !=
                        null
                        ? HexColor.fromHex((widget.productListWidgetData
                        .footerAction!.meta?['textColor'])
                        .toString())
                        : Colors.white,
                    expanded: false,
                  ),
                ),
              ],
            ],
          ),
        ),
        if (widget.productListWidgetData.hasDividerBelow == true)
          Divider(
            thickness: 1,
            color: ColorPalette.white20,
          )
      ],
    );
  }
}

class ProductListItems extends StatelessWidget {
  final List<ProductListItem> items;
  final bool showSquareIcons;
  final bool? showItemSeparatorLine;
  final double? iconBackgroundOpacity;

  ProductListItems(this.items, this.showItemSeparatorLine,
      this.iconBackgroundOpacity,
      {this.showSquareIcons = false});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        for (var item in items)
          GestureDetector(
            onTap: () {
              if (item.action != null) {
                clickActionWithAnalytics(item.action!, context, null, {});
              }
            },
            child: Padding(
              padding: EdgeInsets.only(top: item.topSpacing ?? Spacings.x5),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      if (item.icon != null) ...[
                        Container(
                            width: item.iconSize != null
                                ? item.iconSize
                                : (item.title != null)
                                    ? 60
                                    : 35,
                            height: item.iconSize != null
                                ? item.iconSize
                                : (item.title != null)
                                    ? 60
                                    : 35,
                            padding:
                                EdgeInsets.all(item.padding ?? Spacings.x2),
                            decoration: BoxDecoration(
                                color: ColorPalette.white.withOpacity(iconBackgroundOpacity != null ? iconBackgroundOpacity! : 0.1),
                                borderRadius: BorderRadius.all(
                                    Radius.circular(showSquareIcons ? 5 : 50))),
                            child: CFNetworkImage(
                              fit: BoxFit.cover,
                              height:
                                  item.iconSize != null ? item.iconSize : 18,
                              width: item.iconSize != null ? item.iconSize : 18,
                              imageUrl: getImageUrl(context,
                                  imagePath: item.icon!,
                                  width: MediaQuery.of(context)
                                      .size
                                      .width
                                      .toInt()),
                              errorWidget: (context, url, error) =>
                                  Icon(Icons.error),
                            )),
                        SizedBox(
                            width: showSquareIcons ? Spacings.x3 : Spacings.x5),
                      ],
                      if (item.number != null) ...[
                        Text(
                            '${ int.parse(item.number ?? '0') > 9 ? '' : '0'}${item.number}',
                            style: AuroraTheme.of(context).textStyle(TypescaleValues.H1,
                                color: Colors.white.withOpacity(.2))),
                        SizedBox(width: showSquareIcons ? Spacings.x3 : Spacings.x5),
                      ],
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            if (item.title != null) ...[
                              if (item.titleGradientColor != null)
                                GradientText(
                                  item.title ?? "",
                                  UiUtils.getGradientData(
                                      item.titleGradientColor),
                                  style: AuroraTheme.of(context)
                                      .textStyle(EnumToString.fromString(
                                              TypescaleValues.values,
                                              item.titleFont ?? "H4") ??
                                          TypescaleValues.H4)
                                      .copyWith(height: 0),
                                )
                              else
                                Text(
                                  item.title ?? "",
                                  style: AuroraTheme.of(context).textStyle(
                                    EnumToString.fromString(
                                            TypescaleValues.values,
                                            item.titleFont ?? "H4") ??
                                        TypescaleValues.H4,
                                    color: item.titleColor != null
                                        ? HexColor.fromHex(item.titleColor!)
                                        : ColorPalette.white,
                                  ),
                                ),
                            ],
                            if (item.subtitle != null && item.title != null)
                              SizedBox(height: Spacings.x1),
                            if (item.subtitle != null) ...[
                              if (item.subTitleGradientColor != null)
                                GradientText(
                                  item.subtitle ?? "",
                                  UiUtils.getGradientData(
                                      item.subTitleGradientColor),
                                  style: AuroraTheme.of(context)
                                      .textStyle(EnumToString.fromString(
                                              TypescaleValues.values,
                                              item.subTitleFont ?? "P5") ??
                                          TypescaleValues.P5)
                                      .copyWith(height: 0),
                                  maxLines:2,
                                    textAlignment:TextAlign.start
                                )
                              else
                                Text(
                                  item.subtitle ?? "",
                                  textAlign: TextAlign.start,
                                  maxLines: 4,
                                  style: AuroraTheme.of(context).textStyle(
                                    EnumToString.fromString(
                                            TypescaleValues.values,
                                            item.subTitleFont ?? "P5") ??
                                        TypescaleValues.P5,
                                    color: item.subTitleColor != null
                                        ? HexColor.fromHex(item.subTitleColor!)
                                        : showSquareIcons || item.icon == null
                                            ? ColorPalette.white60
                                            : ColorPalette.white,
                                  ),
                                ),
                            ],
                          ],
                        ),
                      ),
                      if (item.action != null) ...[
                        SizedBox(width: Spacings.x2),
                        Icon(
                          Icons.arrow_forward_ios,
                          color: ColorPalette.white,
                          size: 20.0,
                        )
                      ]
                    ],
                  ),
                  if (showItemSeparatorLine != null &&
                      showItemSeparatorLine! &&
                      item != items.last)
                    Container(
                        margin: EdgeInsets.only(
                            top: item.topSpacing ?? Spacings.x5),
                        decoration: BoxDecoration(
                          border: Border.symmetric(
                              horizontal: BorderSide(
                                  color: ColorPalette.white.withOpacity(0.3),
                                  width: 0.5)),
                        )),
                ],
              ),
            ),
          )
      ],
    );
  }
}

import 'package:common/action/action_handler.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/font/cf_icons.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/ui/widget_factory.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/guides/events.dart';
import 'package:transform/blocs/guides/guides_bloc.dart';
import 'package:transform/blocs/guides/models.dart';
import 'package:transform/blocs/guides/state.dart';

class GuidesScreenArguments {
  String? subCategoryCode;

  GuidesScreenArguments(Map<String, dynamic> payload) {
    this.subCategoryCode =
        payload["subCategoryCode"] != null ? payload["subCategoryCode"] : "";
  }
}

class GuidesScreen extends StatefulWidget {
  const GuidesScreen({Key? key}) : super(key: key);

  @override
  State<GuidesScreen> createState() => _GuidesScreenState();
}

class _GuidesScreenState extends State<GuidesScreen> {
  late GuidesScreenData guidesScreenData;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final ScreenArguments? args =
          ModalRoute.of(context)!.settings.arguments as ScreenArguments?;
      String subCategoryCode = "";
      if (args != null) {
        GuidesScreenArguments arguments = GuidesScreenArguments(args.params);
        subCategoryCode = arguments.subCategoryCode ?? "";
      }
      final guidesBloc = BlocProvider.of<GuidesBloc>(context);
      guidesBloc.add(GuidesDataLoadEvent(subCategoryCode: subCategoryCode));
    });
  }

  CanvasTheme draftTheme() {
    TimeOfDay now = TimeOfDay.now();
    return now.hour > 4 && now.hour < 19 ? CanvasTheme.TEAL : CanvasTheme.NIGHT;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(
            50.0 + AuroraTheme.of(context).embeddedSafeArea.top),
        child: Padding(
          padding: EdgeInsets.only(
              top: AuroraTheme.of(context).embeddedSafeArea.top),
          child: AppBar(
            backgroundColor: Colors.transparent,
            iconTheme: IconThemeData(color: Colors.white),
            titleSpacing: 0,
            centerTitle: false,
            title: Text("Habit Guides",
                style: AuroraTheme.of(context).textStyle(TypescaleValues.H1)),
            leading: IconButton(
              icon: const Icon(
                CFIcons.chevron_left,
                color: Colors.white,
                size: 20,
                semanticLabel: "chevron_left",
              ),
              onPressed: () {
                if (Navigator.canPop(context)) {
                  Navigator.pop(context);
                } else if (!Navigator.canPop(context)) {
                  ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
                  actionBloc.add(CloseApplicationEvent(shouldReset: true));
                }
              },
            ),
          ),
        ),
      ),
      body: LayoutBuilder(
        builder: (context, constraints) {
          AuroraThemeData themeData = AuroraTheme.of(context);
          CanvasTheme canvasTheme = draftTheme();
          themeData.canvasTheme = canvasTheme;
          if (constraints.maxWidth > 0) {
            return Container(
              child: Stack(
                children: [
                  Aurora(
                    size: constraints.biggest,
                    context: context,
                  ),
                  BlocBuilder<GuidesBloc, GuidesDataState>(
                    builder: (context, state) {
                      if (state is GuidesDataLoadingState) {
                        return FancyLoadingIndicator();
                      } else if (state is GuidesDataLoadedState) {
                        if (state.guidesScreenData != null) {
                          guidesScreenData = state.guidesScreenData!;
                        }
                        WidgetFactory widgetFactory =
                            RepositoryProvider.of<WidgetFactory>(context);
                        if (guidesScreenData.themeType != null) {
                          AuroraTheme.of(context).canvasTheme =
                              guidesScreenData.themeType!;
                        }
                        return SingleChildScrollView(
                          child: Padding(
                            padding: EdgeInsets.only(
                                top: Spacings.x22,
                                bottom: Spacings.x6,
                                left: Spacings.x4,
                                right: Spacings.x4),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                if (guidesScreenData.title != null)
                                  Padding(
                                    padding: EdgeInsets.only(
                                        top: Spacings.x4,
                                        left: Spacings.x1,
                                        right: Spacings.x1),
                                    child: Text(
                                      guidesScreenData.title ?? "",
                                      style: AuroraTheme.of(context)
                                          .textStyle(TypescaleValues.P2),
                                      textAlign: TextAlign.left,
                                    ),
                                  ),
                                ...applyStaggeredAnimation(
                                    widgetFactory.createWidgets(
                                        guidesScreenData.widgets!),
                                    context),
                              ],
                            ),
                          ),
                        );
                      }
                      return Container();
                    },
                  ),
                ],
              ),
            );
          }
          return Container();
        },
      ),
    );
  }
}

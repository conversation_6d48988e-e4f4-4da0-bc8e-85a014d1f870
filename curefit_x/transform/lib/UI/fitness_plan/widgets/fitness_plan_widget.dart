import 'package:common/font/mapping.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/atoms/secondary_button.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/user/user_repository.dart';
import 'package:common/util/action_util.dart';
import 'package:common/util/color.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/fitness_plan/fitness_plan_models/workout_detail.dart';

import '../../../blocs/fitness_plan/fitness_plan_bloc_v2.dart';

class FitnessPlanWidget extends StatelessWidget {
  WorkoutDetail workoutDetails;
  bool isCurrentDay;
  String subCategoryCode;
  String currentDayNumber;
  bool isRedoAllowed;

  FitnessPlanWidget({required this.workoutDetails, required this.isCurrentDay, required this.currentDayNumber, required this.subCategoryCode, required this.isRedoAllowed});

  @override
  Widget build(BuildContext context) {
    FitnessPlanBlocV2 bloc = BlocProvider.of<FitnessPlanBlocV2>(context);
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (workoutDetails.workoutName != null)
          Padding(
            padding: const EdgeInsets.only(top: 35, bottom: 15),
            child: Text(
              workoutDetails.workoutName!,
              style: AuroraTheme.of(context)
                  .textStyle(TypescaleValues.H9, color: Colors.white),
            ),
          ),
        BlurView(
          borderRadius: 10,
          child: Container(
            width: scale(context, 335),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Stack(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(10),
                          topRight: Radius.circular(10)),
                      child: CFNetworkImage(
                        imageUrl: getImageUrl(context,
                            imagePath: workoutDetails.workoutImageUrl),
                        width: scale(context, 335),
                        height: scale(context, 175),
                        fit: BoxFit.cover,
                      ),
                    ),
                    if (workoutDetails.fitnessPlanWorkoutStatus == "COMPLETED")
                      Positioned(
                          top: 5.5,
                          left: 5.0,
                          child: Container(
                            decoration: BoxDecoration(
                                color: HexColor.fromHex("#0FE498"),
                                borderRadius: BorderRadius.circular(10)),
                            width: scale(context, 90),
                            height: scale(context, 15),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  const IconData(0xe156,
                                      fontFamily: 'MaterialIcons'),
                                  size: 16,
                                  color: Colors.black,
                                ),
                                Text(
                                  workoutDetails.fitnessPlanWorkoutStatus!,
                                  style: AuroraTheme.of(context).textStyle(
                                      TypescaleValues.TAGTEXT,
                                      color: Colors.black),
                                ),
                              ],
                            ),
                          )),
                    if (workoutDetails.fitnessPlanWorkoutStatus == "MISSED")
                      Positioned(
                          top: 5.5,
                          left: 5.0,
                          child: Container(
                            decoration: BoxDecoration(
                                color: HexColor.fromHex("#F7C744"),
                                borderRadius: BorderRadius.circular(10)),
                            width: scale(context, 72),
                            height: scale(context, 16),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  const IconData(0xe237,
                                      fontFamily: 'MaterialIcons'),
                                  size: 16,
                                  // color: HexColor.fromHex("#F7C744"),
                                ),
                                Text(
                                  workoutDetails.fitnessPlanWorkoutStatus!,
                                  style: AuroraTheme.of(context).textStyle(
                                      TypescaleValues.TAGTEXT,
                                      color: Colors.black),
                                ),
                              ],
                            ),
                          )),
                  ],
                ),
                if (workoutDetails.workoutSubTitle != null)
                  Padding(
                    padding: const EdgeInsets.only(top: 20.0),
                    child: Text(
                      workoutDetails.workoutSubTitle!,
                      style: AuroraTheme.of(context)
                          .textStyle(TypescaleValues.P1, color: Colors.white),
                    ),
                  ),
                if (workoutDetails.workoutTitle != null)
                  Padding(
                    padding: const EdgeInsets.only(top: 10.0),
                    child: Text(
                      workoutDetails.workoutTitle!,
                      style: AuroraTheme.of(context)
                          .textStyle(TypescaleValues.H6, color: Colors.white),
                    ),
                  ),
                if (workoutDetails.workoutDuration != null)
                  Padding(
                    padding: const EdgeInsets.only(top: 10.0, bottom: 20),
                    child: Text(
                      workoutDetails.workoutDuration!,
                      style: AuroraTheme.of(context)
                          .textStyle(TypescaleValues.P1, color: Colors.white),
                    ),
                  ),
                if ((isCurrentDay && workoutDetails.action?.title != null) || (workoutDetails.workoutType == "LIVE"))
                  Padding(
                    padding: const EdgeInsets.only(bottom: 15),
                    child: SecondaryButton(() {
                      clickActionWithAnalytics(
                          workoutDetails.action!, context, null, {});
                    }, workoutDetails.action!.title!, expanded: false,
                      iconData: const IconData(0xf00a0, fontFamily: 'MaterialIcons'),
                    ),
                  ),
                if ((!isCurrentDay && isRedoAllowed && (workoutDetails.fitnessPlanWorkoutStatus == "MISSED" || workoutDetails.fitnessPlanWorkoutStatus == "COMPLETED")))
                  Padding(
                    padding: const EdgeInsets.only(bottom: 15),
                    child: SecondaryButton(() {
                      bloc.add(SwapWorkoutFitnessPlanEventV2(subCategoryCode: subCategoryCode,
                          payload: {
                            "userId" : RepositoryProvider.of<UserRepository>(context)
                                .getUserInfo()!.userId!,
                            "fromDayNumber" : workoutDetails.dayNumber!,
                            "toDayNumber" : currentDayNumber!,
                          }
                      ));
                    }, "Redo", expanded: false,
                      iconData: const IconData(0xf00a0, fontFamily: 'MaterialIcons'),
                    ),
                  )
              ],
            ),
          ),
        ),
      ],
    );
  }
}

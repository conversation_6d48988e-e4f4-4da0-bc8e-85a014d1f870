import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/atoms/floating_twin_button.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/util/action_util.dart';
import 'package:flutter/material.dart';
import 'package:common/action/action_handler.dart' as ActionHandler;
import 'package:transform/blocs/fitness_plan/fitness_plan_models/empty_message.dart';

class PlanInProgressWidget extends StatelessWidget {
  final EmptyMessage emptyMessage;
  final List<ActionHandler.Action>? pageActions;
  const PlanInProgressWidget(
      {Key? key, required this.emptyMessage, this.pageActions})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Expanded(
          child: Padding(
            padding: const EdgeInsets.only(left: 20.0, top: 150, right: 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(height: Spacings.x12),
                if (emptyMessage.imageUrl != null)
                  CFNetworkImage(
                    imageUrl: getImageUrl(context,
                        imagePath: emptyMessage.imageUrl!, width: 100),
                    width: scale(
                      context,
                      56,
                    ),
                  ),
                SizedBox(height: Spacings.x5),
                if (emptyMessage.description != null)
                  Text(
                    emptyMessage.description!,
                    textAlign: TextAlign.center,
                    style:
                        AuroraTheme.of(context).textStyle(TypescaleValues.H2),
                  ),
                SizedBox(height: Spacings.x1),
                if (emptyMessage.header != null)
                  Text(
                    emptyMessage.header!.toUpperCase(),
                    style: AuroraTheme.of(context).textStyle(
                        TypescaleValues.TAGTEXT,
                        color: Colors.white54),
                    textAlign: TextAlign.center,
                  ),
              ],
            ),
          ),
        ),
        if (pageActions != null && pageActions!.isNotEmpty)
          FloatingTwinButton(
            isHorizontalLayout: false,
            onPress: (action) {
              clickActionWithAnalytics(action, context, null, {});
            },
            data: pageActions,
          )
      ],
    );
  }
}

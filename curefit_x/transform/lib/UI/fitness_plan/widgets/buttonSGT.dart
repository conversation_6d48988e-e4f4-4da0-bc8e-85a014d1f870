import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/util/color.dart';
import 'package:common/util/theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:common/action/action_handler.dart' as ActionHandler;

enum ButtonType { BIG, SMALL }

class ButtonSgt extends StatelessWidget {
  final void Function()? onPressed;
  final String title;
  final double horizontalPadding;
  final double textVerticalPadding;
  final double cornerRadius;
  final ButtonType buttonType;
  final IconData? iconData;
  final double? verticalPadding;
  final bool enabled;
  final ActionHandler.Action? action;
  final double? iconSize;

  ButtonSgt(this.onPressed, this.title,
      {this.horizontalPadding = 20,
        this.enabled = true,
        this.action,
        this.iconData,
        this.cornerRadius = 5,
        this.buttonType = ButtonType.BIG,
        this.textVerticalPadding = 18,
        this.iconSize,
        this.verticalPadding});

  @override
  Widget build(BuildContext context) {
    Widget buttonWidget = enabled
        ? Material(color: Colors.transparent, child: button(context))
        : button(context);
    return Container(
        clipBehavior: Clip.hardEdge,
        decoration: BoxDecoration(
            borderRadius: BorderRadius.all(Radius.circular(cornerRadius))),
        child: Container(
            decoration:
            BoxDecoration(color: enabled ? Colors.white : Colors.white10),
            child: Row(
              mainAxisSize: this.buttonType == ButtonType.BIG
                  ? MainAxisSize.max
                  : MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                this.buttonType == ButtonType.BIG
                    ? Expanded(child: buttonWidget)
                    : buttonWidget
              ],
            )));
  }

  button(BuildContext context) {
    final verticalPadding = this.verticalPadding != null
        ? this.verticalPadding!
        : this.buttonType == ButtonType.BIG
        ? Spacings.x3
        : Spacings.x2;
    return InkWell(
        onTap: this.enabled
            ? () {
          if (this.onPressed != null) {
            HapticFeedback.mediumImpact();
            this.onPressed!();
          }
        }
            : null,
        child: Container(
            padding: EdgeInsets.symmetric(
                vertical: verticalPadding, horizontal: horizontalPadding),
            child: enabled
                ? Row(mainAxisAlignment: MainAxisAlignment.center, children: [
              if (iconData != null)
                ShaderMask(
                  shaderCallback: (bounds) => RadialGradient(
                    center: Alignment.center,
                    colors: [
                      HexColor.fromHex("#FF834A"),
                      HexColor.fromHex("#FF4C6A")
                    ],
                    tileMode: TileMode.mirror,
                  ).createShader(bounds),
                  child: Icon(
                    iconData,
                    size: iconSize ?? 20,
                    color: Colors.white,
                  ),
                ),
              if (iconData != null)
                SizedBox(
                  width: Spacings.x1,
                ),
              GradientText(
                  this.title.toUpperCase(),
                  LinearGradient(stops: [
                    0,
                    0.7,
                  ], colors: <Color>[
                    HexColor.fromHex("#FF834A"),
                    HexColor.fromHex("#FF4C6A")
                  ])),
            ])
                : Text(
              this.title.toUpperCase(),
              textAlign: TextAlign.center,
              style: CFTextStyles.bold14(
                  color: Colors.white.withOpacity(0.2)),
            )));
  }
}

class GradientText extends StatelessWidget {
  GradientText(this.text, this.gradient);

  final String text;
  final Gradient gradient;

  @override
  Widget build(BuildContext context) {
    return ShaderMask(
      shaderCallback: (bounds) => gradient.createShader(
        Rect.fromLTRB(0, 0, bounds.width, bounds.height),
      ),
      child: Text(
        text,
        textAlign: TextAlign.center,
        style: AuroraTheme.of(context).textStyle(TypescaleValues.P6,
          // The color must be set to white for this to work
          color: Colors.white,
        ),
      ),
    );
  }
}

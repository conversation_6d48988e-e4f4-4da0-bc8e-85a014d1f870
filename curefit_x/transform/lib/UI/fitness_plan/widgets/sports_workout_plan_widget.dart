import 'dart:ffi';

import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/atoms/secondary_button.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/user/user_repository.dart';
import 'package:common/util/color.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/fitness_plan/fitness_plan_models/workout_detail.dart';

import '../../../blocs/fitness_plan/fitness_plan_bloc_v2.dart';

class SportsWorkoutPlanWidget extends StatelessWidget {
  WorkoutDetail workoutDetails;
  bool isCurrentDay;
  String subCategoryCode;
  String currentDayNumber;
  bool isRedoAllowed;

  SportsWorkoutPlanWidget(
      {required this.workoutDetails, required this.isCurrentDay, required this.currentDayNumber, required this.subCategoryCode, required this.isRedoAllowed});

  @override
  Widget build(BuildContext context) {
    FitnessPlanBlocV2 bloc = BlocProvider.of<FitnessPlanBlocV2>(context);

    final List<String> options = workoutDetails
        .action?.meta!['fitnessPlanSportsModalView']['options']
        .map<String>((dynamic obj) => obj.toString())
        .toList();
    final ValueNotifier<int> selectedOption = ValueNotifier<int>(-1);

    void _showModal(BuildContext context) {
      showModalBottomSheet<dynamic>(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(15.0),
        ),
        backgroundColor: Colors.black,
        enableDrag: true,
        context: context,
        isScrollControlled: true,
        builder: (
          BuildContext context,
        ) {
          return Container(
            color: Colors.black,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Padding(
                  padding: const EdgeInsets.only(top: 0.0, left: 165),
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(10.0),
                      color: Colors.grey,
                    ),
                    height: scale(context, 2.02),
                    width: scale(context,
                        54), // Adjust the height of the notch as needed
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 20.0, top: 20.0),
                  child: Text(
                    workoutDetails.action?.meta!['fitnessPlanSportsModalView']
                        ['question'],
                    style: AuroraTheme.of(context)
                        .textStyle(TypescaleValues.H9, color: Colors.white),
                  ),
                ),
                Column(
                  children: options.asMap().entries.map((entry) {
                    int index = entry.key;
                    String option = entry.value;
                    return ListTile(
                      title: Text(
                        option,
                        style: AuroraTheme.of(context)
                            .textStyle(TypescaleValues.P2, color: Colors.white),
                      ),
                      leading: CustomRadioButton(
                        index: index,
                        selectedOption: selectedOption,
                      ),
                    );
                  }).toList(),
                ),
                Padding(
                  padding: const EdgeInsets.all(20.0),
                  child: PrimaryButton(
                    () {
                      // bloc.add(SubmitFitnessPlanEventV2(fitnessPlanId:
                      // "653f7a5283e024452b011aef",
                      //     payload: {
                      //       "dayNumber": "3",
                      //       "fitnessPlanWorkoutStatus": "COMPLETED"
                      //     }
                      // ));
                      Navigator.of(context).pop();
                    },
                    workoutDetails.action?.meta!['fitnessPlanSportsModalView']
                        ['cta'],
                  ),
                )
              ],
            ),
          );
        },
      );
    }

    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (workoutDetails.workoutName != null)
          Padding(
            padding: const EdgeInsets.only(top: 35, bottom: 15),
            child: Text(
              workoutDetails.workoutName!,
              style: AuroraTheme.of(context)
                  .textStyle(TypescaleValues.H9, color: Colors.white),
            ),
          ),
        BlurView(
          borderRadius: 10,
          child: Container(
            width: scale(context, 335),
            child: Column(
              children: [
                ClipRRect(
                  borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(10),
                      topRight: Radius.circular(10)),
                  child: Stack(
                    children: [
                      CFNetworkImage(
                        imageUrl: getImageUrl(context,
                            imagePath: workoutDetails.workoutImageUrl),
                        width: scale(context, 335),
                        height: scale(context, 175),
                        fit: BoxFit.cover,
                      ),
                      if (workoutDetails.fitnessPlanWorkoutStatus ==
                          "COMPLETED")
                        Positioned(
                            top: 5.5,
                            left: 5.0,
                            child: Container(
                              decoration: BoxDecoration(
                                  color: HexColor.fromHex("#0FE498"),
                                  borderRadius: BorderRadius.circular(10)),
                              width: scale(context, 90),
                              height: scale(context, 15),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    const IconData(0xe156,
                                        fontFamily: 'MaterialIcons'),
                                    size: 16,
                                    color: Colors.black,
                                  ),
                                  Text(
                                    "  " +
                                        workoutDetails
                                            .fitnessPlanWorkoutStatus!,
                                    style: AuroraTheme.of(context).textStyle(
                                        TypescaleValues.TAGTEXT,
                                        color: Colors.black),
                                  ),
                                ],
                              ),
                            )),
                      if (workoutDetails.fitnessPlanWorkoutStatus == "MISSED")
                        Positioned(
                            top: 5.5,
                            left: 5.0,
                            child: Container(
                              decoration: BoxDecoration(
                                  color: HexColor.fromHex("#F7C744"),
                                  borderRadius: BorderRadius.circular(10)),
                              width: scale(context, 72),
                              height: scale(context, 16),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    const IconData(0xe237,
                                        fontFamily: 'MaterialIcons'),
                                    size: 16,
                                    // color: HexColor.fromHex("#F7C744"),
                                  ),
                                  Text(
                                    "  " +
                                        workoutDetails
                                            .fitnessPlanWorkoutStatus!,
                                    style: AuroraTheme.of(context).textStyle(
                                        TypescaleValues.TAGTEXT,
                                        color: Colors.black),
                                  ),
                                ],
                              ),
                            )),
                    ],
                  ),
                ),
                if (workoutDetails.workoutTitle != null)
                  Padding(
                    padding: const EdgeInsets.only(top: 20, bottom: 10),
                    child: Text(
                      workoutDetails.workoutTitle!,
                      style: AuroraTheme.of(context)
                          .textStyle(TypescaleValues.H6, color: Colors.white),
                    ),
                  ),
                if (workoutDetails.workoutSubTitle != null)
                  Padding(
                    padding:
                        const EdgeInsets.only(left: 20, right: 20, bottom: 20),
                    child: Text(
                      workoutDetails.workoutSubTitle!,
                      style: AuroraTheme.of(context).textStyle(
                          TypescaleValues.P5,
                          color: Colors.white.withOpacity(0.6)),
                      textAlign: TextAlign.center,
                    ),
                  ),
                if (isCurrentDay && workoutDetails.action?.title != null)
                  Padding(
                    padding: const EdgeInsets.only(bottom: 20),
                    child: SecondaryButton(() {
                      _showModal(context);
                    }, workoutDetails.action!.title!, expanded: false),
                  ),
                if ((!isCurrentDay && isRedoAllowed && (workoutDetails.fitnessPlanWorkoutStatus == "MISSED" || workoutDetails.fitnessPlanWorkoutStatus == "COMPLETED")))
                  Padding(
                    padding: const EdgeInsets.only(bottom: 15),
                    child: SecondaryButton(() {
                      bloc.add(SwapWorkoutFitnessPlanEventV2(subCategoryCode: subCategoryCode,
                          payload: {
                            "userId" : RepositoryProvider.of<UserRepository>(context)
                              .getUserInfo()!.userId!,
                            "fromDayNumber" : workoutDetails.dayNumber!,
                            "toDayNumber" : currentDayNumber!,
                          }
                      ));
                    }, "Redo", expanded: false,
                      iconData: const IconData(0xf00a0, fontFamily: 'MaterialIcons'),
                    ),
                  ),
                // if(isFutureDay)
                SizedBox(
                  height: Spacings.x2,
                )
              ],
            ),
          ),
        ),
      ],
    );
  }
}

class CustomRadioButton extends StatelessWidget {
  final int index;
  final ValueNotifier<int> selectedOption;

  CustomRadioButton({
    required this.index,
    required this.selectedOption,
  });

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<int>(
      valueListenable: selectedOption,
      builder: (context, value, child) {
        return Radio<int>(
          activeColor: Colors.grey,
          value: index,
          fillColor: MaterialStateColor.resolveWith((states) => Colors.white),
          groupValue: value,
          onChanged: (int? newValue) {
            selectedOption.value = newValue!;
          },
        );
      },
    );
  }
}

import 'package:common/action/action_handler.dart';
import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/blocs/navigation/navigation_bloc.dart';
import 'package:common/blocs/navigation/state.dart';
import 'package:common/font/cf_icons.dart';
import 'package:common/model/error_prop_model.dart';
import 'package:common/ui/atoms/date_label.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/page/basic_page_container.dart';
import 'package:common/ui/screens/error_screen.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:transform/UI/fitness_plan/widgets/plan_in_progress_widget.dart';
import 'package:transform/blocs/fitness_plan/fitness_plan_bloc.dart';
import 'package:transform/blocs/fitness_plan/fitness_plan_models/fitness_plan.dart';
import 'package:transform/constants/constants.dart';
import '../widgets/drop_down_chevron.dart';
import 'package:navigation_history_observer/navigation_history_observer.dart';

class FitnessPlanScreenArguments {
  String? subCategoryCode;

  FitnessPlanScreenArguments(Map<String, dynamic> payload) {
    this.subCategoryCode =
        payload["subCategoryCode"] != null ? payload["subCategoryCode"] : "";
  }
}

class FitnessPlanScreen extends StatefulWidget {
  const FitnessPlanScreen({Key? key}) : super(key: key);

  @override
  State<FitnessPlanScreen> createState() => _FitnessPlanScreenState();
}

class _FitnessPlanScreenState extends State<FitnessPlanScreen>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  TabController? _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(vsync: this, length: 0);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final ScreenArguments? args =
          ModalRoute.of(context)!.settings.arguments as ScreenArguments?;
      String subCategoryCode = "";
      if (args != null) {
        FitnessPlanScreenArguments arguments =
            FitnessPlanScreenArguments(args.params);
        subCategoryCode = arguments.subCategoryCode ?? "";
      }
      FitnessPlanBloc bloc = BlocProvider.of<FitnessPlanBloc>(context);
      bloc.add(LoadFitnessPlanEvent(subCategoryCode: subCategoryCode));
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  refresh({required BuildContext context, bool showLoader = false}) {
    final fitnessBloc = BlocProvider.of<FitnessPlanBloc>(context);
    fitnessBloc.add(LoadFitnessPlanEvent());
  }

  onBackPress() {
    if (Navigator.canPop(context)) {
      Navigator.pop(context);
    } else if (!Navigator.canPop(context)) {
      ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
      actionBloc.add(CloseApplicationEvent(shouldReset: true));
    }
  }

  logPageView() {
    AnalyticsRepository analyticsRepository =
        RepositoryProvider.of<AnalyticsRepository>(context);
    analyticsRepository.logPageViewEvent(
        pageId: EnumToString.convertToString(RouteNames.tf_fitnessplan),
        eventInfo: {});
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    switch (state) {
      case AppLifecycleState.resumed:
        logPageView();
        refresh(context: context);
        break;
      case AppLifecycleState.inactive:
        break;
      case AppLifecycleState.paused:
        break;
      case AppLifecycleState.detached:
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    AuroraThemeData themeData = AuroraTheme.of(context);
    return MultiBlocListener(
      listeners: [
        BlocListener<NavigationBloc, NavigationState>(
            listener: (context, state) {
          if (state is NavigationStackUpdated &&
              state.action == NavigationStackAction.pop &&
              state.route?.settings.name ==
                  '/${EnumToString.convertToString(RouteNames.tf_fitnessplan)}') {
            refresh(context: context, showLoader: false);
          }
        }),
        BlocListener<FitnessPlanBloc, FitnessPlanState>(
          listener: (context, state) {
            if (state is FitnessPlanLoaded) {
              _tabController = TabController(
                  length: state.plan.planList!.length,
                  vsync: this,
                  initialIndex: state.plan.selectedTabIndex);
            }
          },
        )
      ],
      child: BlocBuilder<FitnessPlanBloc, FitnessPlanState>(
        builder: (context, state) {
          return Stack(
            children: [
              Aurora(
                size: MediaQuery.of(context).size,
                context: context,
              ),
              Scaffold(
                  backgroundColor: Colors.transparent,
                  appBar: PreferredSize(
                    preferredSize: Size.fromHeight(
                        110.0 + AuroraTheme.of(context).embeddedSafeArea.top),
                    child: Padding(
                      padding: EdgeInsets.only(
                          top: AuroraTheme.of(context).embeddedSafeArea.top),
                      child: AppBar(
                        centerTitle: false,
                        leadingWidth: 40,
                        title: Text(
                          "Fitness",
                          style: themeData.textStyle(TypescaleValues.H1),
                        ),
                        leading: IconButton(
                          icon: const Icon(CFIcons.chevron_left,
                              color: Colors.white, size: 16, semanticLabel: "chevron_left",),
                          onPressed: onBackPress,
                        ),
                        backgroundColor: Colors.transparent,
                        bottom: (() {
                          if (state is FitnessPlanLoaded) {
                            FitnessPlan plan = state.plan;
                            return PreferredSize(
                              preferredSize: Size.fromHeight(100),
                              child: TabBar(
                                tabAlignment: TabAlignment.start,
                                dividerHeight: 0,
                                indicatorWeight: 14,
                                controller: _tabController,
                                splashBorderRadius: BorderRadius.all(
                                    Radius.circular(Spacings.x2)),
                                padding: EdgeInsets.only(
                                    left: Spacings.x2, top: Spacings.x1),
                                labelPadding: EdgeInsets.symmetric(
                                    horizontal: Spacings.x3),
                                tabs: plan.planList!
                                    .map((planItem) => DateLabel(
                                          date: planItem.date,
                                          stateChangeEnabled: false,
                                          onTapEnabled: false,
                                          padding: 3,
                                        ))
                                    .toList(),
                                isScrollable: true,
                                indicator: DropDownChevronDecoration(
                                    containerColor:
                                        Colors.white.withOpacity(0.2)),
                              ),
                            );
                          }
                        })(),
                      ),
                    ),
                  ),
                  body: Builder(builder: ((context) {
                    if (state is FitnessPlanLoaded) {
                      FitnessPlan plan = state.plan;
                      if (plan.planList != null && plan.planList!.isNotEmpty) {
                        return TabBarView(
                          controller: _tabController,
                          children: plan.planList!.map((planItem) {
                            if (planItem.widgets != null &&
                                planItem.widgets!.isNotEmpty) {
                              return Padding(
                                padding:
                                    const EdgeInsets.only(top: Spacings.x6),
                                child: BasicPageContainer(
                                  widgetData: planItem.widgets!,
                                  showTitleBar: false,
                                  auroraEnabled: false,
                                ),
                              );
                            } else if (planItem.emptyMessage != null) {
                              return PlanInProgressWidget(
                                emptyMessage: planItem.emptyMessage!,
                                pageActions: planItem.pageActions,
                              );
                            }
                            return const SizedBox();
                          }).toList(),
                        );
                      } else {
                        return ErrorScreen(errorInfo: UnknownError());
                      }
                    } else if (state is FitnessPlanLoading) {
                      return Center(
                        child: FancyLoadingIndicator(color: Colors.white),
                      );
                    } else if (state is FitnessPlanFailed) {
                      return ErrorScreen(
                          errorInfo: state.errorInfo ?? UnknownError());
                    }
                    return SizedBox();
                  }))),
            ],
          );
        },
      ),
    );
  }
}

import 'package:common/action/action_handler.dart';
import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/blocs/navigation/navigation_bloc.dart';
import 'package:common/blocs/navigation/state.dart';
import 'package:common/font/cf_icons.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/model/error_prop_model.dart';
import 'package:common/ui/atoms/date_label_v2.dart';
import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/atoms/secondary_button.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/screens/error_screen.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/user/user_repository.dart';
import 'package:common/util/color.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/UI/fitness_plan/screens/sgt_slot_screen.dart';
import 'package:transform/UI/fitness_plan/widgets/buttonSGT.dart';
import 'package:transform/UI/fitness_plan/widgets/sports_workout_plan_widget.dart';
import 'package:transform/blocs/fitness_plan/fitness_plan_models/sgt_detail.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../blocs/fitness_plan/fitness_plan_bloc_v2.dart';
import '../../../blocs/fitness_plan/fitness_plan_models/fitness_plan.dart';
import '../widgets/drop_down_chevron.dart';
import '../widgets/fitness_plan_widget.dart';

import 'package:common/ui/theme/spacing.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:transform/constants/constants.dart';
import 'package:navigation_history_observer/navigation_history_observer.dart';

class FitnessPlanScreenV2Arguments {
  String? subCategoryCode;

  FitnessPlanScreenV2Arguments(Map<String, dynamic> payload) {
    this.subCategoryCode =
        payload["subCategoryCode"] != null ? payload["subCategoryCode"] : "";
  }
}

class FitnessPlanScreenV2 extends StatefulWidget {
  const FitnessPlanScreenV2({Key? key}) : super(key: key);

  @override
  State<FitnessPlanScreenV2> createState() => _FitnessPlanScreenV2State();
}

class _FitnessPlanScreenV2State extends State<FitnessPlanScreenV2>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  TabController? _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(vsync: this, length: 0);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final ScreenArguments? args =
          ModalRoute.of(context)!.settings.arguments as ScreenArguments?;
      String subCategoryCode = "";
      if (args != null) {
        FitnessPlanScreenV2Arguments arguments =
            FitnessPlanScreenV2Arguments(args.params);
        subCategoryCode = arguments.subCategoryCode ?? "";
      }
      FitnessPlanBlocV2 bloc = BlocProvider.of<FitnessPlanBlocV2>(context);
      bloc.add(LoadFitnessPlanEventV2(subCategoryCode: subCategoryCode));
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  refresh({required BuildContext context, bool showLoader = false}) {
    final fitnessBlocV2 = BlocProvider.of<FitnessPlanBlocV2>(context);
    fitnessBlocV2.add(LoadFitnessPlanEventV2());
  }

  onBackPress() {
    if (Navigator.canPop(context)) {
      Navigator.pop(context);
    } else if (!Navigator.canPop(context)) {
      ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
      actionBloc.add(CloseApplicationEvent(shouldReset: true));
    }
  }

  logPageView() {
    AnalyticsRepository analyticsRepository =
        RepositoryProvider.of<AnalyticsRepository>(context);
    analyticsRepository.logPageViewEvent(
        pageId: EnumToString.convertToString(RouteNames.tf_fitnessplanv2),
        eventInfo: {});
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    switch (state) {
      case AppLifecycleState.resumed:
        logPageView();
        refresh(context: context);
        break;
      case AppLifecycleState.inactive:
        break;
      case AppLifecycleState.paused:
        break;
      case AppLifecycleState.detached:
        break;
    }
  }

  Widget getWidgetsSeparator(BuildContext context, String separatorText) {
    return Padding(
      padding: EdgeInsets.only(
          top: scale(context, Spacings.x4),
          bottom: scale(context, Spacings.x4),
          left: scale(context, Spacings.x4),
          right: scale(context, Spacings.x4)),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Expanded(
            child: Container(
              margin: EdgeInsets.only(right: Spacings.x1),
              height: 1,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                  colors: <Color>[Colors.white12, Colors.white60],
                ),
              ),
            ),
          ),
          ConstrainedBox(
            constraints: BoxConstraints(maxWidth: scale(context, 300)),
            child: Text(
              separatorText,
              maxLines: 1,
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
              style: AuroraTheme.of(context)
                  .textStyle(TypescaleValues.P3, color: Colors.white),
            ),
          ),
          Expanded(
            child: Container(
              margin: EdgeInsets.only(left: Spacings.x1),
              height: 1,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.centerLeft,
                  end: Alignment.centerRight,
                  colors: <Color>[Colors.white60, Colors.white12],
                  tileMode: TileMode.mirror,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget getSingleCard(BuildContext context, SgtDetail sgtDetail,
      List<SgtDetail> sgtDetailList, String date) {
    void _launchZoomLink(String zoomLink) async {
      if (await canLaunchUrl(Uri.parse(zoomLink))) {
        await launchUrl(Uri.parse(zoomLink));
      } else {
        throw 'Could not launch $zoomLink';
      }
    }

    String dot = " • ";

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          width: scale(context, 335),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Padding(
                padding: const EdgeInsets.only(bottom: 15),
                child: Text(
                  "Small Group Training",
                  style: AuroraTheme.of(context)
                      .textStyle(TypescaleValues.H1, color: Colors.white),
                ),
              ),
              Spacer(),
              Padding(
                padding: const EdgeInsets.only(bottom: 15),
                child: Container(
                  child: Align(
                    alignment: Alignment.center,
                    child: SecondaryButton(
                      () {
                        refresh(context: context);
                      },
                      "REFRESH",
                      expanded: false,
                      buttonType: SecondaryButtonType.SMALL,
                      verticalPadding: scale(context, 8),
                    ),
                  ),
                ),
              )
            ],
          ),
        ),
        if (sgtDetailList.length > 1)
          BlurView(
            borderRadius: 15,
            child: Container(
              height: scale(context, 173),
              width: scale(context, 335),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                    flex: 3,
                    child: Padding(
                      padding: EdgeInsets.all(Spacings.x3),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          ClipRRect(
                              borderRadius:
                                  BorderRadius.circular(scale(context, 15.0)),
                              child: CFNetworkImage(
                                width: scale(context, 100),
                                height: scale(context, 100),
                                imageUrl: getImageUrl(context,
                                    imagePath: sgtDetail.workoutImageUrl),
                                fit: BoxFit.cover,
                              )),
                          SizedBox(width: Spacings.x3),
                          Container(
                            height: scale(context, 100),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                if (sgtDetail.workoutName != null)
                                  Text(
                                    sgtDetail.workoutName ?? "",
                                    maxLines: 2,
                                    style: AuroraTheme.of(context).textStyle(
                                        TypescaleValues.H4,
                                        color: Colors.white),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                SizedBox(height: Spacings.x1),
                                if (sgtDetail.coachName != null)
                                  Text(
                                    sgtDetail.coachName ?? "",
                                    maxLines: 2,
                                    style: AuroraTheme.of(context).textStyle(
                                        TypescaleValues.P5,
                                        color: Colors.white60),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                if (sgtDetail.slotStatus == "ACTIVE" &&
                                    sgtDetail.zoomLink != null)
                                  Expanded(
                                    // flex: 0,
                                    child: Align(
                                      alignment: Alignment.bottomCenter,
                                      child: ButtonSgt(
                                        () {
                                          AnalyticsRepository
                                              analyticsRepository =
                                              RepositoryProvider.of<
                                                  AnalyticsRepository>(context);
                                          analyticsRepository
                                              .logButtonClickEvent(extraInfo: {
                                            'eventName' : "sgt_join_now",
                                            'slotId' : sgtDetail.slotId,
                                            'coachId' : sgtDetail.coachId,
                                            if(RepositoryProvider.of<
                                                UserRepository>(context)
                                                .getUserInfo() != null)
                                              'userId' : RepositoryProvider.of<
                                                  UserRepository>(context)
                                                  .getUserInfo()?.userId,
                                            'eventClickTime' : "${DateTime.now().hour}:${DateTime.now().minute}:${DateTime.now().second}",
                                            'slotTime' : sgtDetail.slotTiming,
                                            'slotName' : sgtDetail.workoutName,
                                            'slotCoach' : sgtDetail.coachName,
                                          });
                                          _launchZoomLink(sgtDetail.zoomLink!);
                                        },
                                        sgtDetail.slotTiming! +
                                            dot +
                                            "JOIN NOW",
                                        buttonType: ButtonType.SMALL,
                                        horizontalPadding: scale(context, 5),
                                        verticalPadding: scale(context, 10),
                                      ),
                                    ),
                                  ),
                                if (sgtDetail.slotStatus == "UPCOMING" &&
                                    sgtDetail.zoomLink != null)
                                  Expanded(
                                    child: Align(
                                      alignment: Alignment.bottomCenter,
                                      child: SecondaryButton(
                                        () {
                                          AnalyticsRepository
                                        analyticsRepository =
                                        RepositoryProvider.of<
                                            AnalyticsRepository>(context);
                                        analyticsRepository
                                            .logButtonClickEvent(extraInfo: {
                                          'eventName' : "sgt_upcoming",
                                          'slotId' : sgtDetail.slotId,
                                          'coachId' : sgtDetail.coachId,
                                          if(RepositoryProvider.of<
                                              UserRepository>(context)
                                              .getUserInfo() != null)
                                            'userId' : RepositoryProvider.of<
                                                UserRepository>(context)
                                                .getUserInfo()?.userId,
                                          'eventClickTime' : "${DateTime.now().hour}:${DateTime.now().minute}:${DateTime.now().second}",
                                          'slotTime' : sgtDetail.slotTiming,
                                          'slotName' : sgtDetail.workoutName,
                                          'slotCoach' : sgtDetail.coachName,
                                        });
                                        },
                                        sgtDetail.slotTiming! +
                                            dot +
                                            sgtDetail.slotStatus!,
                                        expanded: false,
                                        buttonType: SecondaryButtonType.SMALL,
                                        verticalPadding: scale(context, 10),
                                      ),
                                    ),
                                  )
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  Container(
                    height: 0.5,
                    decoration:
                        BoxDecoration(color: Colors.white.withOpacity(0.2)),
                  ),
                  Expanded(
                    flex: 1,
                    child: InkWell(
                      onTap: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => SgtSlotScreen(
                                sgtDetail: sgtDetailList, currentDate: date),
                          ),
                        );
                      },
                      child: Center(
                        child: Text(
                          "+ " +
                              (sgtDetailList.length - 1).toString() +
                              " MORE",
                          style: AuroraTheme.of(context).textStyle(
                              TypescaleValues.P3,
                              color: Colors.white),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        if (sgtDetailList.length == 1)
          BlurView(
            borderRadius: 15,
            child: Container(
              height: scale(context, 127),
              width: scale(context, 335),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                    flex: 3,
                    child: Padding(
                      padding: EdgeInsets.all(Spacings.x3),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          ClipRRect(
                              borderRadius:
                                  BorderRadius.circular(scale(context, 15.0)),
                              child: CFNetworkImage(
                                width: scale(context, 100),
                                height: scale(context, 100),
                                imageUrl: getImageUrl(context,
                                    imagePath: sgtDetail.workoutImageUrl),
                                fit: BoxFit.cover,
                              )),
                          SizedBox(width: Spacings.x3),
                          Container(
                            height: scale(context, 100),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                if (sgtDetail.workoutName != null)
                                  Text(
                                    sgtDetail.workoutName ?? "",
                                    maxLines: 2,
                                    style: AuroraTheme.of(context).textStyle(
                                        TypescaleValues.H4,
                                        color: Colors.white),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                SizedBox(height: Spacings.x1),
                                if (sgtDetail.coachName != null)
                                  Text(
                                    sgtDetail.coachName ?? "",
                                    maxLines: 2,
                                    style: AuroraTheme.of(context).textStyle(
                                        TypescaleValues.P5,
                                        color: Colors.white60),
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                if (sgtDetail.slotStatus == "ACTIVE" &&
                                    sgtDetail.zoomLink != null)
                                  Expanded(
                                    // flex: 0,
                                    child: Align(
                                      alignment: Alignment.bottomCenter,
                                      child: ButtonSgt(
                                        () {
                                          AnalyticsRepository
                                          analyticsRepository =
                                          RepositoryProvider.of<
                                              AnalyticsRepository>(context);
                                          analyticsRepository
                                              .logButtonClickEvent(extraInfo: {
                                            'eventName' : "sgt_join_now",
                                            'slotId' : sgtDetail.slotId,
                                            'coachId' : sgtDetail.coachId,
                                            if(RepositoryProvider.of<
                                                UserRepository>(context)
                                                .getUserInfo() != null)
                                              'userId' : RepositoryProvider.of<
                                                  UserRepository>(context)
                                                  .getUserInfo()?.userId,
                                            'eventClickTime' : "${DateTime.now().hour}:${DateTime.now().minute}:${DateTime.now().second}",
                                            'slotTime' : sgtDetail.slotTiming,
                                            'slotName' : sgtDetail.workoutName,
                                            'slotCoach' : sgtDetail.coachName,
                                          });
                                          _launchZoomLink(sgtDetail.zoomLink!);
                                        },
                                        sgtDetail.slotTiming! +
                                            dot +
                                            "JOIN NOW",
                                        buttonType: ButtonType.SMALL,
                                        horizontalPadding: scale(context, 5),
                                        verticalPadding: scale(context, 10),
                                      ),
                                    ),
                                  ),
                                if (sgtDetail.slotStatus == "UPCOMING" &&
                                    sgtDetail.zoomLink != null)
                                  Expanded(
                                    child: Align(
                                      alignment: Alignment.bottomCenter,
                                      child: SecondaryButton(
                                        () {
                                          AnalyticsRepository
                                        analyticsRepository =
                                        RepositoryProvider.of<
                                            AnalyticsRepository>(context);
                                        analyticsRepository
                                            .logButtonClickEvent(extraInfo: {
                                          'eventName' : "sgt_upcoming",
                                          'slotId' : sgtDetail.slotId,
                                          'coachId' : sgtDetail.coachId,
                                          if(RepositoryProvider.of<
                                              UserRepository>(context)
                                              .getUserInfo() != null)
                                            'userId' : RepositoryProvider.of<
                                                UserRepository>(context)
                                                .getUserInfo()?.userId,
                                          'eventClickTime' : "${DateTime.now().hour}:${DateTime.now().minute}:${DateTime.now().second}",
                                          'slotTime' : sgtDetail.slotTiming,
                                          'slotName' : sgtDetail.workoutName,
                                          'slotCoach' : sgtDetail.coachName,
                                        });
                                        },
                                        sgtDetail.slotTiming! +
                                            dot +
                                            sgtDetail.slotStatus!,
                                        expanded: false,
                                        buttonType: SecondaryButtonType.SMALL,
                                        verticalPadding: scale(context, 10),
                                      ),
                                    ),
                                  )
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    AuroraThemeData themeData = AuroraTheme.of(context);
    return MultiBlocListener(
      listeners: [
        BlocListener<NavigationBloc, NavigationState>(
            listener: (context, state) {
          if (state is NavigationStackUpdated &&
              state.action == NavigationStackAction.pop &&
              state.route?.settings.name ==
                  '/${EnumToString.convertToString(RouteNames.tf_fitnessplanv2)}') {
            refresh(context: context, showLoader: false);
          }
        }),
        BlocListener<FitnessPlanBlocV2, FitnessPlanStateV2>(
          listener: (context, state) {
            if (state is FitnessPlanLoadedV2) {
              _tabController = TabController(
                  length: state.plan.planList!.length,
                  vsync: this,
                  initialIndex: state.plan.selectedTabIndex);
            }
          },
        )
      ],
      child: BlocBuilder<FitnessPlanBlocV2, FitnessPlanStateV2>(
        builder: (context, state) {
          return Stack(
            children: [
              Aurora(
                size: MediaQuery.of(context).size,
                context: context,
              ),
              Scaffold(
                  backgroundColor: Colors.transparent,
                  appBar: PreferredSize(
                    preferredSize: Size.fromHeight(
                        110.0 + AuroraTheme.of(context).embeddedSafeArea.top),
                    child: Padding(
                      padding: EdgeInsets.only(
                          top: AuroraTheme.of(context).embeddedSafeArea.top),
                      child: AppBar(
                        centerTitle: false,
                        leadingWidth: 40,
                        title: Text(
                          "Fitness Plan",
                          style: themeData.textStyle(TypescaleValues.H1),
                        ),
                        leading: IconButton(
                          icon: const Icon(CFIcons.chevron_left,
                              color: Colors.white, size: 16, semanticLabel: "chevron_left",),
                          onPressed: onBackPress,
                        ),
                        backgroundColor: Colors.transparent,
                        bottom: (() {
                          if (state is FitnessPlanLoadedV2) {
                            FitnessPlan plan = state.plan;
                            return PreferredSize(
                              preferredSize: Size.fromHeight(100),
                              child: TabBar(
                                tabAlignment: TabAlignment.start,
                                dividerHeight: 0,
                                indicatorWeight: 14,
                                controller: _tabController,
                                splashBorderRadius: BorderRadius.all(
                                    Radius.circular(Spacings.x2)),
                                padding: EdgeInsets.only(
                                    left: Spacings.x2, top: Spacings.x1),
                                labelPadding: EdgeInsets.symmetric(
                                    horizontal: Spacings.x3),
                                tabs: plan.planList!
                                    .map((planItem) => DateLabelV2(
                                          date: planItem.date,
                                          stateChangeEnabled: false,
                                          onTapEnabled: false,
                                          padding: 5,
                                        ))
                                    .toList(),
                                isScrollable: true,
                                indicator: DropDownChevronDecoration(
                                    containerColor:
                                        Colors.white.withOpacity(0.2)),
                              ),
                            );
                          }
                        })(),
                      ),
                    ),
                  ),
                  body: Builder(builder: ((context) {
                    if (state is FitnessPlanLoadedV2) {
                      FitnessPlan plan = state.plan;
                      FitnessPlanBlocV2 bloc =
                          BlocProvider.of<FitnessPlanBlocV2>(context);
                      if (plan.planList != null && plan.planList!.isNotEmpty) {
                        return TabBarView(
                          controller: _tabController,
                          children: plan.planList!.map((planItem) {
                            if (planItem.baseWidgets != null &&
                                planItem.baseWidgets!.isNotEmpty) {
                              if (planItem.type == "SPORT") {
                                return Padding(
                                  padding: const EdgeInsets.only(bottom: 0),
                                  child: SingleChildScrollView(
                                    child: Column(
                                      children: [
                                        SportsWorkoutPlanWidget(
                                            workoutDetails:
                                                planItem.baseWidgets!.first,
                                            isCurrentDay: plan.currentDate! ==
                                                planItem.date,
                                            currentDayNumber:
                                                plan.currentDayNumber!,
                                            subCategoryCode:
                                                plan.subCategoryCode!,
                                            isRedoAllowed:
                                                planItem.isRedoAllowed!),
                                        if (RepositoryProvider.of<
                                                        UserRepository>(context)
                                                    .getUserInfo() !=
                                                null &&
                                            plan.isSwapWithTodayActive! &&
                                            plan.currentDayNumber != null &&
                                            planItem.baseWidgets!.first
                                                    .dayNumber !=
                                                null &&
                                            DateTime.parse(plan.currentDate!)
                                                .isBefore(DateTime.parse(
                                                    planItem.date!)))
                                          Container(
                                            width: scale(context, 335),
                                            child: Padding(
                                              padding: const EdgeInsets.only(
                                                  top: 25, bottom: 25),
                                              child: PrimaryButton(
                                                () {
                                                  bloc.add(
                                                      SwapWorkoutFitnessPlanEventV2(
                                                          subCategoryCode: plan
                                                              .subCategoryCode,
                                                          payload: {
                                                        "userId": RepositoryProvider
                                                                .of<UserRepository>(
                                                                    context)
                                                            .getUserInfo()!
                                                            .userId!,
                                                        "fromDayNumber":
                                                            planItem
                                                                .baseWidgets!
                                                                .first
                                                                .dayNumber!,
                                                        "toDayNumber": plan
                                                            .currentDayNumber!,
                                                      }));
                                                },
                                                "SWAP WITH TODAY",
                                                iconData: const IconData(0xe627,
                                                    fontFamily:
                                                        'MaterialIcons'),
                                                iconSize: 20,
                                              ),
                                            ),
                                          ),
                                        if (planItem.sgtClasses != null &&
                                            planItem.sgtClasses!.isNotEmpty)
                                          getWidgetsSeparator(
                                              context, plan.separatorText!),
                                        if (planItem.sgtClasses != null &&
                                            planItem.sgtClasses!.isNotEmpty)
                                          getSingleCard(
                                            context,
                                            planItem.sgtClasses!.first,
                                            planItem.sgtClasses!,
                                            planItem.date!,
                                          ),
                                        SizedBox(height: 200)
                                      ],
                                    ),
                                  ),
                                );
                              } else {
                                return Padding(
                                  padding: const EdgeInsets.only(bottom: 0),
                                  child: SingleChildScrollView(
                                    child: Column(
                                      children: [
                                        FitnessPlanWidget(
                                          workoutDetails:
                                              planItem.baseWidgets!.first,
                                          isCurrentDay: plan.currentDate! ==
                                              planItem.date,
                                          currentDayNumber:
                                              plan.currentDayNumber!,
                                          subCategoryCode:
                                              plan.subCategoryCode!,
                                          isRedoAllowed:
                                              planItem.isRedoAllowed!,
                                        ),
                                        if (RepositoryProvider.of<
                                                        UserRepository>(context)
                                                    .getUserInfo() !=
                                                null &&
                                            plan.isSwapWithTodayActive! &&
                                            plan.currentDayNumber != null &&
                                            planItem.baseWidgets!.first
                                                    .dayNumber !=
                                                null &&
                                            DateTime.parse(plan.currentDate!)
                                                .isBefore(DateTime.parse(
                                                    planItem.date!)))
                                          Container(
                                            width: scale(context, 335),
                                            child: Padding(
                                              padding: const EdgeInsets.only(
                                                  top: 25, bottom: 25),
                                              child: PrimaryButton(
                                                () {
                                                  bloc.add(
                                                      SwapWorkoutFitnessPlanEventV2(
                                                          subCategoryCode: plan
                                                              .subCategoryCode,
                                                          payload: {
                                                        "userId": RepositoryProvider
                                                                .of<UserRepository>(
                                                                    context)
                                                            .getUserInfo()!
                                                            .userId!,
                                                        "fromDayNumber":
                                                            planItem
                                                                .baseWidgets!
                                                                .first
                                                                .dayNumber!,
                                                        "toDayNumber": plan
                                                            .currentDayNumber!,
                                                      }));
                                                },
                                                "SWAP WITH TODAY",
                                                iconData: const IconData(0xe627,
                                                    fontFamily:
                                                        'MaterialIcons'),
                                                iconSize: 20,
                                              ),
                                            ),
                                          ),
                                        if (planItem.sgtClasses != null &&
                                            planItem.sgtClasses!.isNotEmpty)
                                          getWidgetsSeparator(
                                              context, plan.separatorText!),
                                        if (planItem.sgtClasses != null &&
                                            planItem.sgtClasses!.isNotEmpty)
                                          getSingleCard(
                                            context,
                                            planItem.sgtClasses!.first,
                                            planItem.sgtClasses!,
                                            planItem.date!,
                                          ),
                                        SizedBox(height: 200)
                                      ],
                                    ),
                                  ),
                                );
                              }
                            } else if (planItem.emptyMessage != null) {
                              return SingleChildScrollView(
                                child: Padding(
                                  padding: const EdgeInsets.only(
                                      left: 20.0, top: 50, right: 20),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      SizedBox(height: Spacings.x12),
                                      if (planItem.emptyMessage!.imageUrl !=
                                          null)
                                        CFNetworkImage(
                                          imageUrl: getImageUrl(context,
                                              imagePath: planItem
                                                  .emptyMessage!.imageUrl!,
                                              width: 100),
                                          width: scale(
                                            context,
                                            56,
                                          ),
                                        ),
                                      SizedBox(height: Spacings.x5),
                                      if (planItem.emptyMessage!.description !=
                                          null)
                                        Text(
                                          planItem.emptyMessage!.description!,
                                          textAlign: TextAlign.center,
                                          style: AuroraTheme.of(context)
                                              .textStyle(TypescaleValues.H2),
                                        ),
                                      SizedBox(height: Spacings.x1),
                                      if (planItem.emptyMessage!.header != null)
                                        Text(
                                          planItem.emptyMessage!.header!
                                              .toUpperCase(),
                                          style: AuroraTheme.of(context)
                                              .textStyle(
                                                  TypescaleValues.TAGTEXT,
                                                  color: Colors.white54),
                                          textAlign: TextAlign.center,
                                        ),
                                      if (planItem.sgtClasses != null &&
                                          planItem.sgtClasses!.isNotEmpty)
                                        Padding(
                                          padding:
                                              const EdgeInsets.only(top: 80),
                                          child: getWidgetsSeparator(
                                              context, plan.separatorText!),
                                        ),
                                      if (planItem.sgtClasses != null &&
                                          planItem.sgtClasses!.isNotEmpty)
                                        getSingleCard(
                                            context,
                                            planItem.sgtClasses!.first,
                                            planItem.sgtClasses!,
                                            planItem.date!),
                                      SizedBox(height: 200)
                                    ],
                                  ),
                                ),
                              );
                            }
                            return const SizedBox();
                          }).toList(),
                        );
                      } else {
                        return ErrorScreen(errorInfo: UnknownError());
                      }
                    } else if (state is FitnessPlanLoadingV2) {
                      return Center(
                        child: FancyLoadingIndicator(color: Colors.white),
                      );
                    } else if (state is FitnessPlanFailedV2) {
                      return ErrorScreen(
                          errorInfo: state.errorInfo ?? UnknownError());
                    }
                    return SizedBox();
                  }))),
            ],
          );
        },
      ),
    );
  }
}

import 'package:common/font/cf_icons.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/atoms/secondary_button.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../blocs/fitness_plan/fitness_plan_models/sgt_detail.dart';
import '../../../blocs/fitness_plan/fitness_plan_models/workout_detail.dart';
import '../widgets/buttonSGT.dart';

class SgtSlotScreen extends StatelessWidget {
  final List<SgtDetail> sgtDetail;
  String currentDate;

  SgtSlotScreen({required this.sgtDetail, required this.currentDate});

  @override
  Widget build(BuildContext context) {
    DateTime dateTime = DateTime.parse(currentDate);
    String formattedDate = DateFormat('dd MMM').format(dateTime);
    AuroraThemeData themeData = AuroraTheme.of(context);
    if (sgtDetail != null)
      return Stack(
        children: [
          Aurora(
            size: MediaQuery.of(context).size,
            context: context,
          ),
          Scaffold(
            backgroundColor: Colors.transparent,
            appBar: PreferredSize(
              preferredSize: Size.fromHeight(
                  80.0 + AuroraTheme.of(context).embeddedSafeArea.top),
              child: Padding(
                padding: EdgeInsets.only(
                    top: AuroraTheme.of(context).embeddedSafeArea.top),
                child: AppBar(
                  centerTitle: false,
                  leadingWidth: 40,
                  title: Text(
                    "Small Group Training",
                    style: themeData.textStyle(TypescaleValues.H1),
                  ),
                  leading: IconButton(
                    icon: const Icon(CFIcons.chevron_left,
                        color: Colors.white, size: 16, semanticLabel: "chevron_left",),
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    padding: const EdgeInsets.only(left: Spacings.x4),
                  ),
                  backgroundColor: Colors.transparent,
                ),
              ),
            ),
            body: Padding(
              padding: const EdgeInsets.only(left: Spacings.x4),
              child: SingleChildScrollView(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "Slots for " + formattedDate,
                      style: themeData.textStyle(TypescaleValues.H1),
                    ),
                    SizedBox(height: Spacings.x2),
                    Column(
                      children: sgtDetail.asMap().entries.map((e) =>
                            getSingleCard(context, e.value),
                        ).toList()
                    ),
                    SizedBox(height: Spacings.x1),
                    Text(
                      "* You can join the sessions from here, 5 mins prior to the session",
                      style: themeData.textStyle(TypescaleValues.P9),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      );
    else
      return Stack(
        children: [
          Aurora(
            size: MediaQuery.of(context).size,
            context: context,
          ),
          Scaffold(
            backgroundColor: Colors.transparent,
            appBar: PreferredSize(
              preferredSize: Size.fromHeight(
                  80.0 + AuroraTheme.of(context).embeddedSafeArea.top),
              child: Padding(
                padding: EdgeInsets.only(
                    top: AuroraTheme.of(context).embeddedSafeArea.top),
                child: AppBar(
                  centerTitle: false,
                  leadingWidth: 40,
                  title: Text(
                    "Small Group Training",
                    style: themeData.textStyle(TypescaleValues.H1),
                  ),
                  leading: IconButton(
                    icon: const Icon(CFIcons.chevron_left,
                        color: Colors.white, size: 16, semanticLabel: "chevron_left",),
                    onPressed: () {
                      Navigator.pop(context);
                    },
                    padding: const EdgeInsets.only(left: Spacings.x4),
                  ),
                  backgroundColor: Colors.transparent,
                ),
              ),
            ),
            body: Column(
              children: [
                Expanded(
                  child: Padding(
                    padding:
                        const EdgeInsets.only(left: 20.0, top: 150, right: 20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        SizedBox(height: Spacings.x12),
                        Icon(Icons.remove_circle, size: 70, color: Colors.white24,),
                        SizedBox(height: Spacings.x2),
                        Text(
                          "No slots left for today",
                          textAlign: TextAlign.center,
                          style: AuroraTheme.of(context)
                              .textStyle(TypescaleValues.H2),
                        ),
                        SizedBox(height: Spacings.x1),
                        Text(
                          "Reach out to your fitness coach to get your Workout of the day updated",
                          style: AuroraTheme.of(context).textStyle(
                              TypescaleValues.P6,
                              color: Colors.white54),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      );
  }
}

Widget getSingleCard(BuildContext context, SgtDetail sgtDetail) {
  void _launchZoomLink(String zoomLink) async {
    if (await canLaunchUrl(Uri.parse(zoomLink))) {
      await launchUrl(Uri.parse(zoomLink));
    } else {
      throw 'Could not launch $zoomLink';
    }
  }

  String dot = " • ";

  return SingleChildScrollView(
    child: Container(
      height: scale(context, 135),
      width: scale(context, 335),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              ClipRRect(
                  borderRadius:
                  BorderRadius.circular(scale(context, 15.0)),
                  child: CFNetworkImage(
                    width: scale(context, 100),
                    height: scale(context, 100),
                    imageUrl: getImageUrl(context,
                        imagePath: sgtDetail.workoutImageUrl),
                    fit: BoxFit.cover,
                  )),
              SizedBox(width: Spacings.x3),
              Container(
                height: scale(context, 100),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (sgtDetail.workoutName != null)
                      Text(
                        sgtDetail.workoutName ?? "",
                        maxLines: 2,
                        style: AuroraTheme.of(context).textStyle(
                            TypescaleValues.H4,
                            color: Colors.white),
                        overflow: TextOverflow.ellipsis,
                      ),
                    SizedBox(height: Spacings.x1),
                    if (sgtDetail.coachName != null)
                      Text(
                        sgtDetail.coachName ?? "",
                        maxLines: 2,
                        style: AuroraTheme.of(context).textStyle(
                            TypescaleValues.P5,
                            color: Colors.white60),
                        overflow: TextOverflow.ellipsis,
                      ),
                    if (sgtDetail.slotStatus == "ACTIVE" &&
                        sgtDetail.zoomLink != null)
                      Expanded(
                        // flex: 0,
                        child: Align(
                          alignment: Alignment.bottomCenter,
                          child: ButtonSgt(
                                () {
                              _launchZoomLink(sgtDetail.zoomLink!);
                            },
                            sgtDetail.slotTiming! + dot + "JOIN NOW",
                            buttonType: ButtonType.SMALL,
                            horizontalPadding: scale(context, 5),
                            verticalPadding: scale(context, 10),
                          ),
                        ),
                      ),
                    if (sgtDetail.slotStatus == "UPCOMING" &&
                        sgtDetail.zoomLink != null)
                      Expanded(
                        child: Align(
                          alignment: Alignment.bottomCenter,
                          child: SecondaryButton(
                                () {},
                            sgtDetail.slotTiming!,
                            expanded: false,
                            buttonType: SecondaryButtonType.SMALL,
                            verticalPadding: scale(context, 10),
                          ),
                        ),
                      )
                  ],
                ),
              ),
            ],
          ),
          Padding(
            padding: const EdgeInsets.only(top: Spacings.x4),
            child: Container(
              height: 1,
              decoration: BoxDecoration(color: Colors.white.withOpacity(0.2)),
            ),
          ),
          SizedBox(height: Spacings.x3),
        ],
      ),
    ),
  );
}



import 'package:common/action/action_handler.dart';
import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/blocs/navigation/navigation_bloc.dart';
import 'package:common/blocs/navigation/state.dart';
import 'package:common/font/cf_icons.dart';
import 'package:common/model/error_prop_model.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/screens/error_screen.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:flutter/material.dart' hide Action;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:transform/UI/clp/widgets/review_cards_widget.dart';
import 'package:transform/blocs/google_review/events.dart';
import 'package:transform/blocs/google_review/google_review_bloc.dart';
import 'package:transform/blocs/google_review/models.dart';
import 'package:transform/blocs/google_review/state.dart';
import 'package:transform/constants/constants.dart';
import 'package:scroll_to_index/scroll_to_index.dart';
import 'package:navigation_history_observer/navigation_history_observer.dart';

import '../clp/widgets/review_cards_widget_v2.dart';

class GoogleReviewClpArguments {
  String? subCategoryCode;
  String? centerId;
  String? limit;
  String? offset;

  GoogleReviewClpArguments(Map<String, dynamic> payload) {
    this.subCategoryCode = payload["subCategoryCode"];
    this.centerId = payload["centerId"];
    this.limit = payload["limit"];
    this.offset = payload["offset"];
  }
}

class GoogleReviewClp extends StatefulWidget {
  @override
  _GoogleReviewClpState createState() => _GoogleReviewClpState();
}

class _GoogleReviewClpState extends State<GoogleReviewClp>
    with
        TickerProviderStateMixin,
        WidgetsBindingObserver,
        AutomaticKeepAliveClientMixin<GoogleReviewClp> {
  late final AutoScrollController? scrollController;

  GoogleReviewClpArguments? getGoogleReviewClpArguments() {
    final ScreenArguments? args =
        ModalRoute.of(context)!.settings.arguments as ScreenArguments?;
    if (args != null) {
      return GoogleReviewClpArguments(args.params);
    }
    return null;
  }

  @override
  void initState() {
    super.initState();
    scrollController = AutoScrollController();
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      GoogleReviewClpArguments? arguments = getGoogleReviewClpArguments();
      final googleReviewClpBloc = BlocProvider.of<GoogleReviewBloc>(context);
      googleReviewClpBloc.add(GoogleReviewScreenLoadEvent(
          subCategoryCode: arguments?.subCategoryCode,
          centerId: arguments?.centerId,
          offset: arguments?.offset,
          limit: arguments?.limit));
    });
  }

  @override
  void dispose() {
    scrollController?.dispose();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  refresh({required BuildContext context, bool showLoader = false}) {
    GoogleReviewClpArguments? arguments = getGoogleReviewClpArguments();
    final googleReviewClpBloc = BlocProvider.of<GoogleReviewBloc>(context);
    googleReviewClpBloc.add(GoogleReviewScreenLoadEvent(
        subCategoryCode: arguments?.subCategoryCode,
        centerId: arguments?.centerId,
        offset: arguments?.offset,
        limit: arguments?.limit));
  }

  onBackPress() {
    if (Navigator.canPop(context)) {
      Navigator.pop(context);
    } else if (!Navigator.canPop(context)) {
      ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
      actionBloc.add(CloseApplicationEvent(shouldReset: true));
    }
  }

  logPageView() {
    AnalyticsRepository analyticsRepository =
        RepositoryProvider.of<AnalyticsRepository>(context);
    analyticsRepository.logPageViewEvent(
        pageId: EnumToString.convertToString(RouteNames.lift), eventInfo: {});
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    switch (state) {
      case AppLifecycleState.resumed:
        logPageView();
        refresh(context: context, showLoader: false);
        break;
      case AppLifecycleState.inactive:
        break;
      case AppLifecycleState.paused:
        break;
      case AppLifecycleState.detached:
        break;
    }
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(
            50.0 + AuroraTheme.of(context).embeddedSafeArea.top),
        child: Padding(
          padding: EdgeInsets.only(
              top: AuroraTheme.of(context).embeddedSafeArea.top),
          child: AppBar(
            backgroundColor: Colors.transparent,
            iconTheme: IconThemeData(color: Colors.white),
            titleSpacing: 0,
            centerTitle: false,
            title: BlocBuilder<GoogleReviewBloc, GoogleReviewState>(
                builder: (context, state) {
              String title = "Google Review";
              if (state is GoogleReviewScreenLoaded) {
                title = state.screenData.title ?? "";
              }
              return Text(title,
                  style: AuroraTheme.of(context).textStyle(TypescaleValues.H1));
            }),
            leading: IconButton(
              icon: const Icon(
                CFIcons.chevron_left,
                color: Colors.white,
                size: 20,
                semanticLabel: "chevron_left",
              ),
              onPressed: () {
                if (Navigator.canPop(context)) {
                  Navigator.pop(context);
                } else if (!Navigator.canPop(context)) {
                  ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
                  actionBloc.add(CloseApplicationEvent(shouldReset: true));
                }
              },
            ),
          ),
        ),
      ),
      body: Stack(
        children: [
          Aurora(
            context: context,
            size: MediaQuery.of(context).size,
          ),
          Positioned.fill(
            child: BlocListener<NavigationBloc, NavigationState>(
              listener: (context, state) {
                if (state is NavigationStackUpdated &&
                    state.action == NavigationStackAction.pop &&
                    state.route?.settings.name ==
                        '/${EnumToString.convertToString(RouteNames.tf_google_review)}') {
                  refresh(context: context, showLoader: false);
                }
              },
              child: BlocBuilder<GoogleReviewBloc, GoogleReviewState>(
                builder: (context, state) {
                  GoogleReviewScreenData? screenData = null;
                  if (state is GoogleReviewScreenLoaded) {
                    screenData = state.screenData;
                  } else if (state is GoogleReviewScreenLoading &&
                      state.screenData != null) {
                    screenData = state.screenData!;
                  } else if (state is GoogleReviewScreenFailed) {
                    return Stack(
                      children: [
                        Container(
                          color: Colors.black,
                          child: ErrorScreen(
                              errorInfo: state.errorInfo ?? UnknownError()),
                        ),
                        Positioned(
                          right: 5,
                          top: 30,
                          child: IconButton(
                            onPressed: () {
                              if (Navigator.canPop(context)) {
                                Navigator.pop(context);
                              } else if (!Navigator.canPop(context)) {
                                ActionBloc actionBloc =
                                    BlocProvider.of<ActionBloc>(context);
                                actionBloc.add(
                                    CloseApplicationEvent(shouldReset: false));
                              }
                            },
                            icon: const Icon(
                              Icons.clear,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    );
                  }
                  if (screenData != null && screenData.reviewCards != null)
                    return Center(
                      child: ListView.builder(
                        controller: scrollController,
                        itemBuilder: (context, index) {
                          if (index == screenData!.reviewCards!.length - 1 &&
                              !screenData.isLimit) {
                            final googleReviewClpBloc =
                                BlocProvider.of<GoogleReviewBloc>(context);
                            googleReviewClpBloc.add(FetchGoogleReviewEvent());
                          }
                          if (index == screenData.reviewCards!.length &&
                              !screenData.isLimit) {
                            return const FancyLoadingIndicator();
                          }
                          return Center(
                            child: Container(
                              padding:
                                  const EdgeInsets.only(bottom: Spacings.x4),
                              child: ReviewCardV2(
                                cardData: screenData.reviewCards![index],
                              ),
                            ),
                          );
                        },
                        itemCount: screenData.reviewCards!.length +
                            (screenData.isLimit ? 0 : 1),
                      ),
                    );

                  return Container();
                },
              ),
            ),
          ),
          Positioned.fill(
            child: BlocBuilder<GoogleReviewBloc, GoogleReviewState>(
              builder: (context, state) {
                if (state is GoogleReviewScreenLoading) {
                  return Center(child: FancyLoadingIndicator());
                }
                return Container();
              },
            ),
          ),
        ],
      ),
    );
  }
}

import 'dart:io';

import 'package:collection/collection.dart';
import 'package:common/action/action_handler.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/font/cf_icons.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/page/basic_page_container.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/testimonials/events.dart';
import 'package:transform/blocs/testimonials/state.dart';
import 'package:transform/blocs/testimonials/testimonials_bloc.dart';
import 'package:common/ui/widgets/testimonial_card_widget.dart';

class ReviewPaginationScreenArguments {
  String? subCategoryCode;

  ReviewPaginationScreenArguments(Map<String, dynamic> payload) {
    this.subCategoryCode =
        payload["subCategoryCode"] != null ? payload["subCategoryCode"] : "";
  }
}

class ReviewPaginationScreen extends StatefulWidget {
  @override
  _ReviewPaginationScreenState createState() => _ReviewPaginationScreenState();
}

class _ReviewPaginationScreenState extends State<ReviewPaginationScreen> {
  final _scrollController = ScrollController();
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final ScreenArguments? args =
          ModalRoute.of(context)!.settings.arguments as ScreenArguments?;
      String subCategoryCode = "";
      if (args != null) {
        ReviewPaginationScreenArguments arguments =
            ReviewPaginationScreenArguments(args.params);
        subCategoryCode = arguments.subCategoryCode ?? "";
      }
      _scrollController.addListener(paginationTrigger);
      final testimonialsBloc = BlocProvider.of<TestimonialsBloc>(context);
      testimonialsBloc
          .add(TestimonialsLoadEvent(subCategoryCode: subCategoryCode));
    });
  }

  void paginationTrigger() {
    if(_scrollController.position.maxScrollExtent ==
        _scrollController.position.pixels) {
      if (!isLoading) {
        isLoading = !isLoading;
        // Perform event when user reach at the end of list (e.g. do Api call)
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<TestimonialsBloc, TestimonialsState>(
        builder: (context, state) {
      List<Widget> _widgets = [];
      if (state is TestimonialsLoadedState) {
        _widgets = state.testimonialsScreenData.testimonials
            .mapIndexed((index, testimonial) => Padding(
                padding: EdgeInsets.fromLTRB(20, index == 0 ? 100 : 10, 20, 10),
                child: TestimonialCard(testimonial)))
            .toList();
      } else if (state is TestimonialsFailedState) {
        _widgets = [
          Padding(
            padding:
                EdgeInsets.only(top: MediaQuery.of(context).size.width / 2),
            child: Center(
              child: Text(
                "Something Went Wrong",
                style: AuroraTheme.of(context).textStyle(TypescaleValues.H1),
              ),
            ),
          )
        ];
      }
      return Scaffold(
        extendBodyBehindAppBar: true,
        body: BasicPageContainer(
          shouldBuildWidget: false,
          itemBuilder: (BuildContext context, Widget currentWidget, int index) {
            return _widgets[index];
          },
          widgetData: _widgets,
          onBackPressed: () {
            if (Navigator.canPop(context)) {
              Navigator.pop(context);
            } else if (!Navigator.canPop(context)) {
              ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
              actionBloc.add(CloseApplicationEvent(shouldReset: false));
            }
          },
          title: state is TestimonialsLoadedState
              ? state.testimonialsScreenData.title
              : "",
          showLoader: state is TestimonialsLoadingState,
        ),
      );
    });
  }
}

import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/util/theme.dart';
import 'package:flutter/material.dart';
import 'dart:math' as math;

final Gradient grad = new LinearGradient(
  colors: <Color>[
    Color.fromRGBO(255, 89, 66, 1),
    Color.fromRGBO(247, 199, 68, 1),
    Color.fromRGBO(15, 228, 152, 1),
  ],
  stops: [
    0.0,
    0.5,
    1.0,
  ],
);

class CircularArc extends StatelessWidget {
  final String consistencyText;
  final double width;
  final double value;

  CircularArc(this.value, {this.width = 300, this.consistencyText = ""});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      alignment: Alignment.center,
      child: Stack(
        children: [
          CustomPaint(
            size: Size(width, width),
            painter: ProgressArc(math.pi, Color.fromRGBO(255, 255, 255, 0.26),
                false, width, 20),
          ),
          TweenAnimationBuilder(
            tween: Tween<double>(begin: 0.0, end: value * math.pi),
            duration: Duration(seconds: 1),
            builder: (BuildContext context, double _val, _) {
              return CustomPaint(
                size: Size(width, width),
                painter: ProgressArc(
                    _val, Colors.white, true, width, 10),
              );
            },
          ),
          Center(
            child: Container(
              width: width * 0.5,
              alignment: Alignment.center,
              child: Text(
                consistencyText,
                textAlign: TextAlign.center,
                style: AuroraTheme.of(context).textStyle(TypescaleValues.P8),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class ProgressArc extends CustomPainter {
  bool isBackground;
  double arc;
  Color progressColor;
  double arcSize;
  double strokeWidth;

  ProgressArc(this.arc, this.progressColor, this.isBackground, this.arcSize,
      this.strokeWidth);

  @override
  void paint(Canvas canvas, Size size) {
    final rect = Rect.fromLTRB(0, 0, arcSize, arcSize);
    final startAngle = -math.pi;
    final sweepAngle = arc;
    final useCenter = false;
    final paint = Paint()
      ..strokeCap = StrokeCap.round
      ..color = progressColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth;

    if (isBackground) {
      paint.shader = grad.createShader(rect);
    }

    canvas.drawArc(rect, startAngle, sweepAngle, useCenter, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    // TODO: implement shouldRepaint
    return true;
  }
}

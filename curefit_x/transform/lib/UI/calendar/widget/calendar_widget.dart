import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/util/theme.dart';
import 'package:flutter/physics.dart';
import 'package:intl/intl.dart';
import 'package:flutter/material.dart';
import 'package:transform/blocs/calendar/models.dart';

class CalendarWidget extends StatefulWidget {
  final ActionCalendarData actionCalendarData;

  CalendarWidget(this.actionCalendarData, {Key? key}) : super(key: key);

  @override
  _CalendarWidgetState createState() => _CalendarWidgetState();
}

const List<String> days = [
  'Monday',
  'Tuesday',
  'Wednesday',
  'Thursday',
  'Friday',
  'Saturday',
  'Sunday'
];

class _CalendarWidgetState extends State<CalendarWidget> {

  late var currentMonth;
  late var currentYear;
  int numberOfDaysInWeek =7;
  int lowerLimitYear = 1990;
  int lowerLimitMonth =1;

  @override
  void initState() {
    currentMonth = widget.actionCalendarData.latestMonth ?? DateTime.now().month;
    currentYear = widget.actionCalendarData.latestYear ?? DateTime.now().year;
    super.initState();
  }

  int _numberOfDaysBeforeFirst(String month, String year) {
    String? day = days.firstWhere((element) {
      return element ==
          DateFormat.EEEE()
              .format(DateFormat("dd/MM/yyyy").parse("1/$month/$year"));
    });
    if (day != null) {
      return days.indexOf(day);
    }
    return 0;
  }

  int _numberOfDaysInMonth(String month, String year) {
    return DateTime(int.parse(year), int.parse(month) + 1, 0).day;
  }

  int _num = 1;

  Widget _getCell(
      {String text = "", bool toIncrement = false, bool dayText = false}) {
    if (toIncrement) {
      _num++;
    }
    String key =
        text + "/" + currentMonth.toString() + "/" + currentYear.toString();
    return TableCell(
      verticalAlignment: TableCellVerticalAlignment.top,
      child: Container(
        height: 25,
        width: 25,
        margin: EdgeInsets.all(7),
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: getCellColor(key),
          shape: BoxShape.circle,
        ),
        child: Text(
          text,
          style: dayText
              ? AuroraTheme.of(context).textStyle(TypescaleValues.P5, color: getTextColor(key))
              : AuroraTheme.of(context).textStyle(TypescaleValues.P8, color: getTextColor(key)),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    _num = 1;
    var firstRowEmptyCells = _numberOfDaysBeforeFirst(
        currentMonth.toString(), currentYear.toString());
    var numberOfDays =
        _numberOfDaysInMonth(currentMonth.toString(), currentYear.toString());
    var numberOfRows = ((numberOfDays - (numberOfDaysInWeek - firstRowEmptyCells)) ~/ numberOfDaysInWeek);
    var lastRowCells = ((numberOfDays - (numberOfDaysInWeek - firstRowEmptyCells)) % numberOfDaysInWeek);
    return Container(
      alignment: Alignment.center,
      child: Column(
        children: [
          Container(
            alignment: Alignment.center,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  child: IconButton(
                    iconSize: 15,
                    color: ((currentMonth == (widget.actionCalendarData.startingMonth ??  lowerLimitMonth) )&&
                        (currentYear == (widget.actionCalendarData.startingYear ??  lowerLimitYear) ))
                        ? Colors.transparent
                        : Colors.white.withOpacity(0.8),
                    onPressed: () {
                      if ((currentMonth == (widget.actionCalendarData.startingMonth ??  lowerLimitMonth) )&&
                          (currentYear == (widget.actionCalendarData.startingYear ??  lowerLimitYear) )) {
                        return;
                      }
                      setState(() {
                        if (currentMonth == 1) {
                          currentMonth = 12;
                          currentYear -= 1;
                        } else {
                          currentMonth -= 1;
                        }
                      });
                    },
                    icon: Icon(
                      Icons.arrow_back_ios,
                    ),
                  ),
                ),
                Container(
                  child: Text(
                    DateFormat('MMMM').format(DateTime(0, currentMonth)) +
                        ", " +
                        currentYear.toString().substring(2),
                    style: AuroraTheme.of(context).textStyle(TypescaleValues.P3),
                  ),
                ),
                Container(
                  child: IconButton(
                    iconSize: 15,
                    color: ((currentMonth == (widget.actionCalendarData.latestMonth ??  DateTime.now().month) )&&
                        (currentYear == (widget.actionCalendarData.latestYear ??  DateTime.now().year) ))
                        ? Colors.transparent
                        : Colors.white.withOpacity(0.8),
                    onPressed: () {
                      if ((currentMonth == (widget.actionCalendarData.latestMonth ??  DateTime.now().month) )&&
                          (currentYear == (widget.actionCalendarData.latestYear ??  DateTime.now().year) )) {
                        return;
                      }
                      setState(() {
                        if (currentMonth == 12) {
                          currentMonth = 1;
                          currentYear += 1;
                        } else {
                          currentMonth += 1;
                        }
                      });
                    },
                    icon: Icon(
                      Icons.arrow_forward_ios,
                    ),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(
            height: 20,
          ),
          Table(
            children: <TableRow>[
              TableRow(
                children: days.map((day) {
                  return _getCell(text: day.substring(0, 1), dayText: true);
                }).toList(),
              ),
              TableRow(
                children: [
                  for (var i = 0; i < firstRowEmptyCells; i++) _getCell(),
                  for (var i = firstRowEmptyCells; i < 7; i++)
                    _getCell(text: _num.toString(), toIncrement: true),
                ],
              ),
              for (var i = 0; i < numberOfRows; i++)
                TableRow(
                  children: [
                    for (var i = 0; i < 7; i++)
                      _getCell(text: _num.toString(), toIncrement: true),
                  ],
                ),
              if (lastRowCells != 0)
                TableRow(
                  children: [
                    for (int i = 0; i < lastRowCells; i++)
                      _getCell(text: _num.toString(), toIncrement: true),
                    for (int i = lastRowCells; i < 7; i++) _getCell(),
                  ],
                ),
            ],
          ),
        ],
      ),
    );
  }

  Color getCellColor(String key) {
    if (widget.actionCalendarData.actionCalendarData.containsKey(key)) {
      if (widget.actionCalendarData.actionCalendarData[key] == "DONE") {
        return Color.fromRGBO(28, 227, 148, 1);
      } else if (widget.actionCalendarData.actionCalendarData[key] ==
          "NOT_DONE") {
        return Color.fromRGBO(255, 89, 66, 1);
      } else if (widget.actionCalendarData.actionCalendarData[key] ==
          "NO_RESPONSE") {
        return Color.fromRGBO(255, 255, 255, 0.2);
      }
    }
    return Colors.transparent;
  }

  Color getTextColor(String key) {
    if (widget.actionCalendarData.actionCalendarData.containsKey(key)) {
      if (widget.actionCalendarData.actionCalendarData[key] == "DONE") {
        return Color.fromRGBO(39, 116, 88, 1);
      } else if (widget.actionCalendarData.actionCalendarData[key] ==
          "NOT_DONE") {
        return Color.fromRGBO(105, 33, 23, 1);
      } else if (widget.actionCalendarData.actionCalendarData[key] ==
          "NO_RESPONSE") {
        return Colors.white;
      }
    }
    return Colors.white;
  }
}

import 'dart:io';

import 'package:common/action/action_handler.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/font/cf_icons.dart';
import 'package:common/ui/molecules/bottom_tray.dart';
import 'package:common/ui/toast.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:transform/UI/cult_habit_building/edit_date_time_page.dart';
import 'package:transform/blocs/cult_habit_building/models.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/widget_factory.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/UI/calendar/widget/circular_arc.dart';
import 'package:transform/blocs/calendar/action_calendar_bloc.dart';
import 'package:transform/blocs/calendar/events.dart';
import 'package:transform/blocs/calendar/models.dart';
import 'package:transform/blocs/calendar/state.dart';
import 'package:transform/UI/cult_habit_building/delete_button_widget.dart';

class ActionCalendarArguments {
  String? actionId;
  String? subCategoryCode;

  ActionCalendarArguments(Map<String, dynamic> payload) {
    this.actionId = payload["actionId"];
    this.subCategoryCode = payload["subCategoryCode"] != null ? payload["subCategoryCode"] : "";
  }
}

class CalendarScreen extends StatefulWidget {
  const CalendarScreen({Key? key}) : super(key: key);

  @override
  _CalendarScreenState createState() => _CalendarScreenState();
}

class _CalendarScreenState extends State<CalendarScreen> {
  late ActionCalendarScreenData actionCalendarScreenData;

  ActionCalendarArguments? actionCalendarArguments() {
    final ScreenArguments? args =
        ModalRoute.of(context)!.settings.arguments as ScreenArguments?;
    if (args != null) {
      return ActionCalendarArguments(args.params);
    }
    return null;
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ActionCalendarArguments? arguments = actionCalendarArguments();
      String? actionId = arguments?.actionId;
      String? subCategoryCode = arguments?.subCategoryCode;
      if (actionId != null) {
        final actionCalendarBloc = BlocProvider.of<ActionCalendarBloc>(context);
        actionCalendarBloc.add(ActionCalendarLoadEvent(
            actionId: actionId, subCategoryCode: subCategoryCode));
      }
    });
  }


  onDeleteButtonPressed(Pages page, BuildContext context) {
    Navigator.pop(context);
    HabitResponse habitResponse = new HabitResponse(isSelected: false, habitId: page.habitID, response: []);
    final ActionCalendarBloc habitBuildingBloc =
    BlocProvider.of<ActionCalendarBloc>(context);
    ActionCalendarArguments? arguments = actionCalendarArguments();
    String? actionId = arguments?.actionId;
    String? subCategoryCode = arguments?.subCategoryCode;
    habitBuildingBloc.add(DeleteHabitEvent(habitResponse: habitResponse,actionId: actionId,subCategoryCode: subCategoryCode));
  }

  Widget footer(Pages editPage,String? editAction,String? deleteAction,List<String>? deleteTexts) {
    List<Widget> buttons = [];
    if(editAction != null) {
      buttons.add(
        Padding(
          padding: EdgeInsets.only(bottom: scale(context, 20)),
          child: InkWell(
            onTap: (){
              final ActionCalendarBloc habitBuildingBloc =
              BlocProvider.of<ActionCalendarBloc>(context);
              Navigator.of(context).push(MaterialPageRoute(builder: (context) => HabitBuildEditPage(
                identifier: editPage.habitID,
                page:  editPage,
                responseForTimer: getInitialValue,
                onTimeChange: (){},
                onSave: (){},
                backgroundColorStart: editPage.backgroundColors[0],
                backgroundColorEnd:  editPage.backgroundColors[1],
                onNext: onNextButtonPress,
                onPrev: onPrevButtonPress,
              )));
            },
            child: Container(
              height: scale(context,50),
              width: scale(context,350),
              decoration: BoxDecoration(
                  borderRadius:
                  BorderRadius.all(
                      Radius.circular(5)),
                  color: Colors.white10
              ),
              child: Center(
                child: Text(
                  editAction.toUpperCase(),
                  textAlign: TextAlign.center,
                  style: AuroraTheme.of(context).textStyle(TypescaleValues.P3, color: Colors.white)
                ),
              ),
            ),
          ),
        ));
    }
    if(deleteAction != null) {
      ActionCalendarArguments? arguments = actionCalendarArguments();
      String? actionId = arguments?.actionId;
      buttons.add(
          InkWell(
            onTap: (){
              showBottomTray(
                  context: context,
                  child: DeleteButtonWidget(buttonPressed: () {onDeleteButtonPressed(editPage,context);},
                    codes: deleteTexts!,actionID: actionId,)
              );
            },
            child: Container(
              height: scale(context,50),
              width: scale(context,350),
              decoration: BoxDecoration(
                  borderRadius:
                  BorderRadius.all(
                      Radius.circular(5)),
                  color: Color.fromRGBO(255, 89, 66, 0.1)
              ),
              child: Center(
                child: Text(
                  deleteAction.toUpperCase(),
                  textAlign: TextAlign.center,
                  style: AuroraTheme.of(context).textStyle(TypescaleValues.P3,
                      color: Color.fromRGBO(255, 89, 66, 1))
                ),
              ),
            ),
          ));
    }
    if (buttons.isNotEmpty) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            height: scale(context, 20),
          ),
          SizedBox(
            height: scale(context, 20),
          ),
          ...buttons,
        ],
      );
    }
    return Container();
  }

  getInitialValue(String key,Pages page) {
    if(page.current != null && page.current!.isNotEmpty) {
      return page.current;
    }
    return page.initial;
  }

  onPrevButtonPress() {
    Navigator.pop(context);
    ActionCalendarArguments? arguments = actionCalendarArguments();
    String? actionId = arguments?.actionId;
    String? subCategoryCode = arguments?.subCategoryCode;
    if (actionId != null) {
      final actionCalendarBloc = BlocProvider.of<ActionCalendarBloc>(context);
      actionCalendarBloc.add(ActionCalendarLoadEvent(
          actionId: actionId, subCategoryCode: subCategoryCode));
    }
  }

  onNextButtonPress(HabitResponse habitResponse) {
    Navigator.pop(context);
    ActionCalendarArguments? arguments = actionCalendarArguments();
    String? actionId = arguments?.actionId;
    String? subCategoryCode = arguments?.subCategoryCode;
    habitResponse.isSelected = true;
    final ActionCalendarBloc habitBuildingBloc =
    BlocProvider.of<ActionCalendarBloc>(context);
    habitBuildingBloc.add(SaveEditTimerResponseEvent(habitResponse: habitResponse,actionId: actionId,subCategoryCode: subCategoryCode));
  }

  CanvasTheme draftTheme() {
    TimeOfDay now = TimeOfDay.now();
    return now.hour > 4 && now.hour < 19 ? CanvasTheme.TEAL : CanvasTheme.NIGHT;
  }

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: PreferredSize(
        preferredSize: Size.fromHeight(
            50.0 + AuroraTheme.of(context).embeddedSafeArea.top),
        child: Padding(
          padding: EdgeInsets.only(
              top: AuroraTheme.of(context).embeddedSafeArea.top),
          child: AppBar(
            backgroundColor: Colors.transparent,
            iconTheme: IconThemeData(color: Colors.white),
            leading: IconButton(
              icon: const Icon(
                CFIcons.chevron_left,
                color: Colors.white,
                size: 20,
                semanticLabel: "chevron_left",
              ),
              onPressed: () {
                if (Navigator.canPop(context)) {
                  Navigator.pop(context);
                } else if (!Navigator.canPop(context)) {
                  ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
                  actionBloc.add(CloseApplicationEvent(shouldReset: true));
                }
              },
            ),
          ),
        ),
      ),
      body: LayoutBuilder(builder: (context, constraints) {
        WidgetFactory widgetFactory =
            RepositoryProvider.of<WidgetFactory>(context);
        if (constraints.maxWidth > 0) {
          AuroraTheme.of(context).canvasTheme = draftTheme();
          return Container(
            color: Colors.black,
            child: Stack(
              children: [
                Aurora(
                  size: constraints.biggest,
                  context: context,
                ),
                BlocListener<ActionCalendarBloc,ActionCalendarState>(
                  listener: (context,state) {
                    if(state is ActionCalendarLoadedState) {
                      if(state.toastText != null && state.toastText!.length > 0) {
                        Toast.show(state.toastText!, context,
                            duration: Toast.lengthLong,
                            gravity: Toast.top);
                      }
                    }

                    if(state is SaveEditHabitResponseState) {
                      Navigator.pop(context);
                      print(state.savePage);
                    }
                  },
                  child: BlocBuilder<ActionCalendarBloc, ActionCalendarState>(
                    builder: (context, state) {
                      if (state is ActionCalendarLoadingState) {
                        return PageLoadingIndicator();
                      }
                      if(state is LoadHabitEditPageState) {
                        return HabitBuildEditPage(
                          identifier: state.page.habitID,
                          onSave: (){},
                          onNext: onNextButtonPress,
                          onPrev: onPrevButtonPress,
                          onTimeChange: (){},
                          page: state.page,
                          responseForTimer: getInitialValue,
                        );
                      }

                      if (state is ActionCalendarLoadedState) {
                        actionCalendarScreenData = state.actionCalendarScreenData;
                        return Positioned.fill(
                          child: SingleChildScrollView(
                            child: Container(
                              padding: EdgeInsets.only(
                                  left: 27, right: 27, top: 120, bottom: 20),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Container(
                                    child: Text(
                                      actionCalendarScreenData.subtitle ?? "",
                                      style: AuroraTheme.of(context)
                                          .textStyle(TypescaleValues.P5),
                                    ),
                                    padding: EdgeInsets.only(bottom: 10),
                                  ),
                                  Container(
                                    child: Text(
                                      actionCalendarScreenData.title ?? "",
                                      style: AuroraTheme.of(context)
                                          .textStyle(TypescaleValues.H9),
                                    ),
                                  ),
                                  SizedBox(
                                    height: 60,
                                  ),
                                  Center(
                                    child: Container(
                                      alignment: Alignment.center,
                                      width: width * 0.5,
                                      height: width * 0.35,
                                      child: CircularArc(
                                          actionCalendarScreenData.scoreValue ??
                                              0,
                                          consistencyText:
                                              actionCalendarScreenData
                                                      .consistencyText ??
                                                  "",
                                          width: width * 0.5),
                                    ),
                                  ),
                                  SizedBox(
                                    height: 10,
                                  ),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Padding(
                                        padding:
                                            EdgeInsets.only(left: width * 0.05),
                                        child: Column(
                                          children: [
                                            Container(
                                              width: 100,
                                              child: Text(
                                                actionCalendarScreenData.days ??
                                                    "",
                                                textAlign: TextAlign.center,
                                                style: AuroraTheme.of(context)
                                                    .textStyle(
                                                        TypescaleValues.H10),
                                              ),
                                            ),
                                            Container(
                                              width: 100,
                                              child: Text(
                                                actionCalendarScreenData
                                                        .daysDescription ??
                                                    "",
                                                textAlign: TextAlign.center,
                                                style: AuroraTheme.of(context)
                                                    .textStyle(
                                                        TypescaleValues.P5),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      Padding(
                                        padding:
                                            EdgeInsets.only(right: width * 0.05),
                                        child: Column(
                                          children: [
                                            Container(
                                              width: 100,
                                              child: Text(
                                                actionCalendarScreenData.streak ??
                                                    "",
                                                textAlign: TextAlign.center,
                                                style: AuroraTheme.of(context)
                                                    .textStyle(
                                                        TypescaleValues.H10),
                                              ),
                                            ),
                                            Container(
                                              width: 100,
                                              child: Text(
                                                actionCalendarScreenData
                                                        .streakDescription ??
                                                    "",
                                                textAlign: TextAlign.center,
                                                style: AuroraTheme.of(context)
                                                    .textStyle(
                                                        TypescaleValues.P5),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(
                                    height: 50,
                                  ),
                                  Center(
                                    child: widgetFactory.createWidget(
                                        actionCalendarScreenData.widget),
                                  ),
                                  (state.actionCalendarScreenData.editAction!=null && state.actionCalendarScreenData.deleteAction!=null) ?
                                  footer(state.actionCalendarScreenData.editPage!,
                                      state.actionCalendarScreenData.editAction,
                                      state.actionCalendarScreenData.deleteAction,
                                      state.actionCalendarScreenData.deleteTexts
                                  ) : Container(),
                                ],
                              ),
                            ),
                          ),
                        );
                      };
                      return Container();
                    },
                  ),
                ),
              ],
            ),
          );
        }
        return Container();
      }),
    );
  }
}

import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'dart:math' as math;
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import 'package:transform/blocs/onboarding/models.dart';

class SlidingCardsView extends StatefulWidget {
  final List<OnboardingCard>? cards;

  const SlidingCardsView({this.cards}) : super();
  @override
  _SlidingCardsViewState createState() => _SlidingCardsViewState();
}

class _SlidingCardsViewState extends State<SlidingCardsView> {
  PageController? pageController;
  double? pageOffset = 0;

  @override
  void initState() {
    super.initState();
    pageController = PageController(viewportFraction: 0.8);
    pageController!.addListener(() {
      setState(() => pageOffset = pageController!.page);
    });
  }

  @override
  void dispose() {
    pageController!.dispose();
    super.dispose();
  }

  List<SlidingCard> createSlidingCards() {
    List<SlidingCard> slidingCards = [];
    for (int index = 0; index < widget.cards!.length; index++) {
      OnboardingCard onboardingCard = widget.cards![index];
      slidingCards.add(SlidingCard(
          name: onboardingCard.title,
          description: onboardingCard.description,
          assetName: onboardingCard.imageUrl,
          offset: pageOffset! - index));
    }
    return slidingCards;
  }

  @override
  Widget build(BuildContext context) {
    return Column(children: [
      SizedBox(
        height: MediaQuery.of(context).size.height * 0.60,
        child: PageView(
          controller: pageController,
          children: createSlidingCards(),
        ),
      ),
      Padding(
          padding: EdgeInsets.only(top: 20),
          child: Container(
            child: SmoothPageIndicator(
              controller: pageController!,
              count: widget.cards!.length,
              effect: WormEffect(
                  dotWidth: 12, dotHeight: 12, dotColor: Colors.white),
            ),
          )),
    ]);
  }
}

class SlidingCard extends StatelessWidget {
  final String? name;
  final String? description;
  final String? assetName;
  final double offset;

  const SlidingCard({
    Key? key,
    required this.name,
    required this.description,
    required this.assetName,
    required this.offset,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    double gauss = math.exp(-(math.pow((offset.abs() - 0.5), 2) / 0.08));

    return Transform.translate(
      offset: Offset(-32 * gauss * offset.sign, 0),
      child: Container(
          child: Column(
        children: <Widget>[
          CFNetworkImage(
            imageUrl: getImageUrl(context, imagePath: assetName!),
            height: MediaQuery.of(context).size.height * 0.3,
            alignment: Alignment(-offset.abs(), 0),
            fit: BoxFit.cover,
          ),
          SizedBox(height: 8),
          Expanded(
            child: CardContent(
              name: name,
              description: description,
              offset: gauss,
            ),
          ),
        ],
      )),
    );
  }
}

class CardContent extends StatelessWidget {
  final String? name;
  final String? description;
  final double offset;

  const CardContent(
      {Key? key,
      required this.name,
      required this.description,
      required this.offset})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Transform.translate(
              offset: Offset(8 * offset, 0),
              child: Center(
                child: Text(name!,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                        fontSize: 26,
                        color: Colors.white,
                        fontWeight: FontWeight.bold)),
              )),
          SizedBox(height: 20),
          Transform.translate(
              offset: Offset(32 * offset, 0),
              child: Center(
                child: Text(
                  description!,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.normal),
                ),
              )),
          Spacer(),
          Row(
            children: <Widget>[
              Spacer(),
            ],
          )
        ],
      ),
    );
  }
}

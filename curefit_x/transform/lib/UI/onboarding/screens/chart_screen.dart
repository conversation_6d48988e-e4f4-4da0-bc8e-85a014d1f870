import 'package:common/constants/action_constants.dart';
import 'package:common/ui/atoms/action_button.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/util/color.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:transform/constants/constants.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/action/action_handler.dart' as ActionHandler;

class ChartScreen extends StatefulWidget {
  @override
  State<StatefulWidget> createState() => ChartScreenState();
}

class ChartScreenState extends State<ChartScreen> {
  late bool isShowingMainData;

  @override
  void initState() {
    super.initState();
    isShowingMainData = false;
    Future.delayed(Duration(milliseconds: 0), () {
      setState(() {
        isShowingMainData = true;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        elevation: 0,
      ),
      body: Container(
        color: Colors.transparent,
        child: Stack(
          children: <Widget>[
            Positioned(
                bottom: 50,
                left: 0,
                right: 0,
                child: SafeArea(
                    child: Column(
                  children: AnimationConfiguration.toStaggeredList(
                      duration: const Duration(milliseconds: 500),
                      childAnimationBuilder: (widget) {
                        if (widget is Padding) {
                          return SlideAnimation(
                            horizontalOffset:
                                MediaQuery.of(context).size.width / 2,
                            child: FadeInAnimation(child: widget),
                          );
                        }
                        if (widget is ActionButton) {
                          return FadeInAnimation(
                            child: widget,
                          );
                        }
                        return widget;
                      },
                      children: [
                        Padding(
                            padding: EdgeInsets.symmetric(
                                horizontal: 30, vertical: 30),
                            child: Text(
                              CHART_SCREEN_TITLE,
                              style:
                                  Theme.of(context).primaryTextTheme.headlineMedium,
                            )),
                        AspectRatio(
                          aspectRatio: 1.7,
                          child: Padding(
                            padding: const EdgeInsets.only(right: 50, left: 50),
                            child: LineChart(
                              isShowingMainData
                                  ? sampleData(linesBarData())
                                  : sampleData(initialBarData()),
                              duration:
                                  const Duration(milliseconds: 800),
                            ),
                          ),
                        ),
                        SizedBox(height: 30),
                        Row(children: [
                          Expanded(
                              child: Padding(
                                  padding: EdgeInsets.symmetric(
                                      vertical: 20, horizontal: 20),
                                  child: ActionButton(
                                    () {
                                      ActionHandler.Action navigationAction =
                                          ActionHandler.Action(
                                              type: ActionTypes.NAVIGATION,
                                              url:
                                                  "curefit://tf_pack_purchase");
                                      ActionBloc actionBloc =
                                          BlocProvider.of<ActionBloc>(context);
                                      PerformActionEvent event =
                                          PerformActionEvent(navigationAction);
                                      actionBloc.add(event);
                                    },
                                    "NEXT",
                                  )))
                        ])
                      ]),
                ))),
          ],
        ),
      ),
    );
  }

  LineChartData sampleData(List<LineChartBarData> lineChartData) {
    return LineChartData(
      extraLinesData: ExtraLinesData(verticalLines: [
        VerticalLine(
          x: 5,
          color: HexColor.fromHex("#ff8484"),
          strokeWidth: 2,
          dashArray: [5, 8],
        )
      ], horizontalLines: [
        HorizontalLine(
          label: HorizontalLineLabel(
            style: AuroraTheme.of(context).textStyle(TypescaleValues.P4),
            padding: EdgeInsets.only(top: 50),
            show: true,
            alignment: Alignment.bottomRight,
            labelResolver: (value) {
              return "cult.fit members";
            },
          ),
          y: 5,
          color: Colors.grey,
          strokeWidth: 2,
          dashArray: [5, 8],
        ),
      ]),
      lineTouchData: LineTouchData(
        enabled: false,
      ),
      gridData: FlGridData(
        show: false,
      ),
      titlesData: FlTitlesData(
        bottomTitles: AxisTitles(sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 22,
              getTitlesWidget: (value, meta) {
                return Container(
                  margin: EdgeInsets.only(top: 10),
                  child: Text(value.toInt() == 2
                      ? "Month 1" : value.toInt() == 12
                      ? "Month 3" : "",
                    style: AuroraTheme.of(context).textStyle(TypescaleValues.P4),
                  ),
                );
              },
          )),
        topTitles: AxisTitles(sideTitles: SideTitles(
          showTitles: true,
          reservedSize: 30,
          getTitlesWidget: (value, meta) {
            return Container(
              margin: EdgeInsets.only(top: 10),
              child: Text(value.toInt() == 2
                  ? "Weight" : value.toInt() == 12
                  ? "Restrictive Diet" : "",
                style: value == 12
                    ? AuroraTheme.of(context).textStyle(TypescaleValues.P1,
                    color: HexColor.fromHex("#ff8484"))
                    : AuroraTheme.of(context).textStyle(TypescaleValues.P1),
              ),
            );
          },
        )),
        leftTitles: AxisTitles(sideTitles: SideTitles(
          showTitles: false,
        )),
      ),
      borderData: FlBorderData(
          show: true,
          border: const Border(
            bottom: BorderSide(
              color: Colors.white,
              width: 1,
            ),
            left: BorderSide(
              color: Colors.white,
            ),
            right: BorderSide(
              color: Colors.transparent,
            ),
            top: BorderSide(
              color: Colors.transparent,
            ),
          )),
      minX: 0,
      maxX: 15,
      maxY: 8,
      minY: 0,
      lineBarsData: lineChartData,
    );
  }

  List<LineChartBarData> linesBarData() {
    return [
      LineChartBarData(
        spots: [
          FlSpot(0, 5),
          FlSpot(4, 5),
          FlSpot(8, 1.25),
          FlSpot(15, 1),
        ],
        isCurved: true,
        color: HexColor.fromHex("#88d13f"),
        barWidth: 3,
        isStrokeCapRound: true,
        dotData: FlDotData(
          show: true,
          checkToShowDot: (spot, barData) {
            if (barData.spots.indexOf(spot) == 0)
              return true;
            else
              return false;
          },
        ),
        belowBarData: BarAreaData(show: true, color:
          HexColor.fromHex("cad7cf"),
        ),
      ),
      LineChartBarData(
        spots: [
          FlSpot(0, 5),
          FlSpot(2, 5),
          FlSpot(5, 2.5),
          FlSpot(11, 7),
          FlSpot(15, 8),
        ],
        isCurved: true,
        color: HexColor.fromHex("#ff8484"),
        barWidth: 3,
        isStrokeCapRound: true,
        dotData: FlDotData(
          show: true,
          checkToShowDot: (spot, barData) {
            int index = barData.spots.indexOf(spot);
            if (index == barData.spots.length - 1 || index == 2)
              return true;
            else
              return false;
          },
        ),
        belowBarData:
            BarAreaData(cutOffY: 5, applyCutOffY: true, show: true, color:
          HexColor.fromHex("#cdb6c2"),
        ),
      )
    ];
  }

  List<LineChartBarData> initialBarData() {
    return [
      LineChartBarData(
        spots: [
          FlSpot(0, 3),
          FlSpot(0, 0),
        ],
        isCurved: true,
        color: HexColor.fromHex("#88d13f"),
        barWidth: 3,
        isStrokeCapRound: true,
        dotData: FlDotData(
          show: true,
          checkToShowDot: (spot, barData) {
            if (barData.spots.indexOf(spot) == 0)
              return true;
            else
              return false;
          },
        ),
        belowBarData: BarAreaData(show: true, color:
          HexColor.fromHex("cad7cf"),
        ),
      ),
      LineChartBarData(
        spots: [
          FlSpot(0, 0),
          FlSpot(2, 0),
          FlSpot(5, 0),
          FlSpot(11, 0),
          FlSpot(15, 0),
        ],
        isCurved: true,
        color: HexColor.fromHex("#ff8484"),
        barWidth: 3,
        isStrokeCapRound: true,
        dotData: FlDotData(
          show: true,
          checkToShowDot: (spot, barData) {
            int index = barData.spots.indexOf(spot);
            if (index == barData.spots.length - 1 || index == 2)
              return true;
            else
              return false;
          },
        ),
        belowBarData:
            BarAreaData(cutOffY: 5, applyCutOffY: true, show: true, color:
          HexColor.fromHex("#cdb6c2"),
        ),
      )
    ];
  }
}

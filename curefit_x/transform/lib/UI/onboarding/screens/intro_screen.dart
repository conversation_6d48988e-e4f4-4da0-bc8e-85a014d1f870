import 'package:common/action/action_handler.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:transform/UI/onboarding/screens/sliding_cards.dart';
import 'package:common/ui/atoms/action_button.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:transform/blocs/onboarding/events.dart';
import 'package:transform/blocs/onboarding/onboarding_bloc.dart';
import 'package:common/util/util.dart';
import 'package:transform/blocs/onboarding/state.dart';
import 'package:common/ui/loading_indicator.dart';

class IntroScreen extends StatefulWidget {
  final Function? onClose;

  const IntroScreen({Key? key, this.onClose}) : super(key: key);

  @override
  _IntroScreenState createState() => _IntroScreenState();
}

class _IntroScreenState extends State<IntroScreen>
    with SingleTickerProviderStateMixin {
  @override
  void initState() {
    super.initState();

    Future.delayed(Duration.zero, () {
      final ScreenArguments? args =
          ModalRoute.of(context)!.settings.arguments as ScreenArguments?;
      OnboardingBloc onboardingBloc = BlocProvider.of<OnboardingBloc>(context);
      bool showTrial = (args != null &&
              args.params != null &&
              args.params["showTrial"] != null)
          ? args.params["showTrial"].toString().parseBool()
          : false;
      onboardingBloc.add(LoadOnboardingEvent(showTrial));
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        extendBodyBehindAppBar: true,
        appBar: AppBar(
          leading: Container(),
          backgroundColor: Colors.transparent,
          iconTheme: IconThemeData(color: Colors.white),
          flexibleSpace: Align(
            alignment: Alignment.topRight,
            child: Padding(
              padding: EdgeInsets.fromLTRB(
                  0,
                  AuroraTheme.of(context).embeddedSafeArea.top +
                      MediaQuery.of(context).padding.top,
                  5,
                  0),
              child: CloseButton(
                color: Colors.white,
                onPressed: () {
                  if (Navigator.canPop(context)) {
                    Navigator.pop(context);
                  } else if (!Navigator.canPop(context)) {
                    ActionBloc actionBloc =
                    BlocProvider.of<ActionBloc>(context);
                    actionBloc
                        .add(CloseApplicationEvent(shouldReset: true));
                  }
                },
              ),
            ),
          ),
        ),
        backgroundColor: Colors.white,
        body: Stack(children: [
          Container(
              decoration: BoxDecoration(
                  gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment(
                0.8, 0.0), // 10% of the width, so there are ten blinds.
            colors: <Color>[Color(0xff4657dc), Color(0xff1e357c)],
          ))),
          BlocBuilder<OnboardingBloc, OnboardingState>(
              builder: (context, state) {
            if (state is OnboardingLoading) {
              return Center(
                  child: FancyLoadingIndicator(
                color: Colors.white,
              ));
            }
            if (state is OnboardingLoaded) {
              return AnimationLimiter(
                  child: Column(
                      children: applyStaggeredAnimation([
                SafeArea(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      SizedBox(height: 50),
                      SlidingCardsView(cards: state.page.cards),
                      Padding(padding: EdgeInsets.only(top: 40))
                    ],
                  ),
                ),
                if (state.page.actions!.isNotEmpty)
                  Align(
                      alignment: Alignment.bottomCenter,
                      child: ActionButton(() {
                        ActionBloc actionBloc =
                            BlocProvider.of<ActionBloc>(context);
                        PerformActionEvent event =
                            PerformActionEvent(state.page.actions!.first);
                        actionBloc.add(event);
                      }, state.page.actions!.first.title!)),
              ], context)));
            }
            return Container();
          }),
        ]));
  }
}

import 'package:common/action/action_handler.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/font/cf_icons.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/model/error_prop_model.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/page/basic_page_container.dart';
import 'package:common/ui/screens/error_screen.dart';
import 'package:common/ui/theme/Colors.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:flutter/material.dart' hide Action;
import 'package:common/ui/aurora.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:common/ui/loading_indicator.dart';

import '../../../blocs/meal_planner/events.dart';
import '../../../blocs/meal_planner/meal_planner_bloc.dart';
import '../../../blocs/meal_planner/model.dart';
import '../../../blocs/meal_planner/states.dart';

class MealPlannerScreenArguments {
  String? subCategoryCode;

  MealPlannerScreenArguments(Map<String, dynamic> payload) {
    this.subCategoryCode =
        payload["subCategoryCode"] != null ? payload["subCategoryCode"] : "";
  }
}

class MealPlannerScreen extends StatefulWidget {
  const MealPlannerScreen({Key? key}) : super(key: key);

  @override
  State<MealPlannerScreen> createState() => _MealPlannerScreenState();
}

class _MealPlannerScreenState extends State<MealPlannerScreen>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  late ScrollController _scrollController;
  late int idx = 0;
  final mealTypesWithMealNumber = new Map();
  List<Widget> widgetViews = [];
  TabController? _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(vsync: this, length: 0);
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      final ScreenArguments? args =
          ModalRoute.of(context)!.settings.arguments as ScreenArguments?;
      String subCategoryCode = "";
      if (args != null) {
        MealPlannerScreenArguments arguments =
            MealPlannerScreenArguments(args.params);
        subCategoryCode = arguments.subCategoryCode ?? "";
      }
      MealPlannerBloc mealUiBloc = BlocProvider.of<MealPlannerBloc>(context);
      mealUiBloc
          .add(LoadMealPlannerContentEvent(subCategoryCode: subCategoryCode));
    });
    _scrollController = ScrollController();
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  CanvasTheme draftTheme() {
    TimeOfDay now = TimeOfDay.now();
    return now.hour > 4 && now.hour < 19 ? CanvasTheme.TEAL : CanvasTheme.NIGHT;
  }

  onBackPress() {
    if (Navigator.canPop(context)) {
      Navigator.pop(context);
    } else if (!Navigator.canPop(context)) {
      ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
      actionBloc.add(CloseApplicationEvent(shouldReset: true));
    }
  }

  List<Widget> mealTypeLabels(MealContentScreen screenData) {
    List<Widget> mealTypeLabels = [];
    screenData.mealType.forEach((mealTypeItem) {
      mealTypeLabels.add(Container(
        height: 30,
        child: Center(
            child: Text(
          mealTypeItem,
        )),
      ));
    });
    return mealTypeLabels;
  }

  Widget getDos(Map<String, dynamic> mealPlanModalData) {
    List<Widget> list = [];
    List<String> dos = mealPlanModalData["doList"]
        .map<String>((dynamic obj) => obj.toString())
        .toList();
    for (var i = 0; i < dos.length; i++) {
      list.add(
        Padding(
          padding: EdgeInsets.only(bottom: 5, top: 5),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Padding(
                padding: EdgeInsets.symmetric(horizontal: scale(context, 8)),
                child: Icon(
                  const IconData(0xe156, fontFamily: 'MaterialIcons'),
                  color: Colors.white,
                ),
              ),
              Flexible(
                child: Text(
                  dos[i],
                  overflow: TextOverflow.visible,
                  style: AuroraTheme.of(context).textStyle(TypescaleValues.P5),
                ),
              ),
            ],
          ),
        ),
      );
    }
    return new Column(
      children: list,
    );
  }

  Widget getDonts(Map<String, dynamic> mealPlanModalData) {
    List<Widget> list = [];
    List<String> donts = mealPlanModalData["dontList"]
        .map<String>((dynamic obj) => obj.toString())
        .toList();
    for (var i = 0; i < donts.length; i++) {
      list.add(
        Padding(
          padding: EdgeInsets.only(bottom: 10, top: 10),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Padding(
                padding: EdgeInsets.symmetric(horizontal: scale(context, 8)),
                child: Icon(
                  Icons.close,
                  color: Colors.white,
                ),
              ),
              Flexible(
                child: Text(
                  donts[i],
                  overflow: TextOverflow.visible,
                  style: AuroraTheme.of(context).textStyle(TypescaleValues.P5),
                ),
              ),
            ],
          ),
        ),
      );
    }
    return new Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: list,
    );
  }

  @override
  Widget build(BuildContext context) {
    void _showModal(BuildContext context, MealContentScreen screenData) {
      showModalBottomSheet<dynamic>(
        backgroundColor: Colors.transparent,
        enableDrag: true,
        context: context,
        isScrollControlled: true,
        builder: (
          BuildContext context,
        ) {
          return BlurView(
            borderRadius: 15,
            child: Container(
              color: Colors.black,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(left: 12.0, top: 32.0),
                    child: Text(
                      "Do's",
                      style: AuroraTheme.of(context).textStyle(
                          TypescaleValues.H2,
                          color: ColorPalette.statusPositive),
                    ),
                  ),
                  getDos(screenData.action.meta?['mealPlanModalData']),
                  Padding(
                    padding: const EdgeInsets.only(left: 12.0, top: 12.0),
                    child: Text(
                      "Dont's",
                      style: AuroraTheme.of(context).textStyle(
                          TypescaleValues.H2,
                          color: ColorPalette.errorRed),
                    ),
                  ),
                  getDonts(screenData.action.meta?['mealPlanModalData']),
                  Padding(
                    padding: const EdgeInsets.only(left: 12.0, top: 40),
                    child: CFNetworkImage(
                      imageUrl: getImageUrl(context,
                          imagePath: screenData
                              .action.meta?['mealPlanModalData']['image'],
                          width: 100),
                    ),
                  ),
                  if (screenData.action.meta?['mealPlanModalData']
                          ['planTitle'] !=
                      null)
                    Padding(
                      padding: const EdgeInsets.only(
                          left: 12.0, bottom: 10.0, top: 40),
                      child: Text(
                        screenData.action.meta?['mealPlanModalData']
                            ['planTitle'],
                        style: AuroraTheme.of(context)
                            .textStyle(TypescaleValues.TAGTEXT),
                      ),
                    ),
                  if (screenData.action.meta?['mealPlanModalData']
                          ['planSubTitle'] !=
                      null)
                    Padding(
                      padding: const EdgeInsets.only(left: 12.0, bottom: 25.0),
                      child: Text(
                        screenData.action.meta?['mealPlanModalData']
                            ['planSubTitle'],
                        style: AuroraTheme.of(context).textStyle(
                            TypescaleValues.P8,
                            color: Colors.white.withOpacity(0.6)),
                      ),
                    ),
                ],
              ),
            ),
          );
        },
      );
    }

    return BlocListener<MealPlannerBloc, MealPlannerState>(
        listener: (context, state) {
      if (state is MealPlannerLoadedState) {
        _tabController = TabController(
            length: state.screenData.mealType.length,
            vsync: this,
            initialIndex: 0);
      }
    }, child: BlocBuilder<MealPlannerBloc, MealPlannerState>(
            builder: (context, state) {
      if (state is MealPlannerLoadingState) {
        return const FancyLoadingIndicator();
      } else if (state is MealPlannerLoadedState) {
        return Stack(
          children: [
            Aurora(
              size: MediaQuery.of(context).size,
              context: context,
              // canvasTheme: draftTheme(),
            ),
            Scaffold(
                backgroundColor: Colors.transparent,
                appBar: PreferredSize(
                  preferredSize: Size.fromHeight(
                      110.0 + AuroraTheme.of(context).embeddedSafeArea.top),
                  child: Padding(
                    padding: EdgeInsets.only(
                        top: 10 + AuroraTheme.of(context).embeddedSafeArea.top),
                    child: AppBar(
                      centerTitle: false,
                      leadingWidth: 40,
                      title: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            state.screenData.pageTitle,
                            style: AuroraTheme.of(context)
                                .textStyle(TypescaleValues.H9),
                          ),
                          if (state.screenData.pageSubTitle != null)
                            Padding(
                              padding: const EdgeInsets.only(top: 5),
                              child: Text(
                                state.screenData.pageSubTitle ?? "",
                                style: AuroraTheme.of(context)
                                    .textStyle(TypescaleValues.P8),
                              ),
                            ),
                        ],
                      ),
                      leading: GestureDetector(
                        onTap: () {
                          onBackPress();
                        },
                        child: Icon(CFIcons.chevron_left,
                            color: Colors.white, size: 25,
                          semanticLabel: "chevron_left",
                        ),
                      ),
                      backgroundColor: Colors.transparent,
                      actions: [
                        TextButton(
                          style: TextButton.styleFrom(
                            textStyle: const TextStyle(fontSize: 14),
                          ),
                          onPressed: () {
                            _showModal(context, state.screenData);
                          },
                          child: Align(
                            alignment: Alignment.topRight,
                            child: Text(
                              (state.screenData.action
                                      .meta?['mealPlanModalData']['title'])
                                  .toString()
                                  .toUpperCase(),
                              style: AuroraTheme.of(context)
                                  .textStyle(TypescaleValues.P6),
                            ),
                          ),
                        )
                      ],
                      bottom: (() {
                        return PreferredSize(
                            preferredSize: Size.fromHeight(kToolbarHeight),
                            child: Align(
                              alignment: Alignment.centerLeft,
                              child: Container(
                                decoration: BoxDecoration(
                                  border: Border(
                                    bottom: BorderSide(
                                      color: Colors.grey, // Separator color
                                      width: 0.5, // Separator thickness
                                    ),
                                  ),
                                ),
                                child: TabBar(
                                  tabAlignment: TabAlignment.start,
                                  dividerHeight: 0,
                                  controller: _tabController,
                                  splashBorderRadius:
                                      BorderRadius.all(Radius.circular(5)),
                                  padding: EdgeInsets.only(left: Spacings.x2),
                                  indicatorPadding: const EdgeInsets.all(0),
                                  indicatorSize: TabBarIndicatorSize.tab,
                                  tabs: mealTypeLabels(state.screenData),
                                  isScrollable: true,
                                  indicatorColor: Colors.white,
                                  labelColor: Colors.white,
                                  labelStyle: AuroraTheme.of(context)
                                      .textStyle(TypescaleValues.P3),
                                  unselectedLabelStyle: AuroraTheme.of(context)
                                      .textStyle(TypescaleValues.P5),
                                  unselectedLabelColor: Colors.white60,
                                  // indicator: BoxDecoration(
                                  //   border: Border(
                                  //     bottom: BorderSide(
                                  //       color: Colors.grey, // Separator color
                                  //       width: 1.0, // Separator thickness
                                  //     ),
                                  //   ),
                                  // ),
                                ),
                              ),
                            ));
                      })(),
                    ),
                  ),
                ),
                body: TabBarView(controller: _tabController, children: [
                  for (int index = 0;
                      index < state.screenData.widgets.length;
                      index++)
                    BasicPageContainer(
                      widgetData: [
                        ...state.screenData.widgets[index],
                      ],
                      showTitleBar: false,
                      auroraEnabled: false,
                      canvasTheme: draftTheme(),
                    ),
                ]))
          ],
        );
      } else if (state is MealPlannerErrorState) {
        return ErrorScreen(errorInfo: state.errorInfo ?? UnknownError());
      }
      return SizedBox();
    }));
  }
}

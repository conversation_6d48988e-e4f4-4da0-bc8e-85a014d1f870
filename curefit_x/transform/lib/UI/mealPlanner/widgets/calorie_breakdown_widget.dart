import 'package:common/util/theme.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/ui_toolkit.dart';
import '../../../blocs/meal_planner/model.dart';
import 'package:common/ui/theme/Colors.dart';


class CalorieBreakDownWidget extends StatefulWidget {
  CalorieBreakdownWidgetInfo widgetData;

  CalorieBreakDownWidget({required this.widgetData, Key? key}) : super(key: key);

  @override
  State<CalorieBreakDownWidget> createState() => _CalorieBreakDownWidgetState();
}

class _CalorieBreakDownWidgetState extends State<CalorieBreakDownWidget> {

  @override
  Widget build(BuildContext context) {

    AuroraThemeData themeData = AuroraTheme.of(context);
    Widget caloryWidget(String nutreint, String amount, Color color){
      return SizedBox(
        width: scale(context, 120),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                if(widget.widgetData.icon != null)
                Text(widget.widgetData.icon!,
                  style: TextStyle(fontSize: 20, color: color),
                ),
                Padding(padding: EdgeInsets.all(0),
                  child: Text(
                    nutreint,
                    style: themeData.textStyle(TypescaleValues.P8).copyWith(
                      color: ColorPalette.white60,
                    ),
                  ),
                )

              ],
            ),

            Text(amount,
                style: themeData.textStyle(TypescaleValues.H4)),
          ],
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.only(top: Spacings.x6),
      child: SizedBox(

        child: DecoratedBox(
          decoration: const BoxDecoration(
            color: Color.fromRGBO(182, 182, 182, 0.2),

          ),
          child:

          Padding(
            padding: const EdgeInsets.symmetric(horizontal: Spacings.x4),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,

              children: [
               if(widget.widgetData.titleHeader!=null)
                Padding(
                  padding: const EdgeInsets.only(
                      bottom: Spacings.x1, top: Spacings.x4),
                  child: Text(
                    widget.widgetData.titleHeader ?? "",
                    textAlign: TextAlign.left,
                    style: CFTextStyles.bold20(),
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          width: 150,
                          child: Padding(
                            padding: const EdgeInsets.only(
                                bottom: Spacings.x3, top: Spacings.x3),
                            child: Container(
                              width: MediaQuery.of(context).size.width/2,
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,

                                children: [
                                  Text(
                                    'Total Caloric INTAKE'.toUpperCase(),
                                    style: themeData
                                        .textStyle(TypescaleValues.P8)
                                        .copyWith(color: ColorPalette.white60),
                                  ),
                                  if(widget.widgetData.data!['topContainer']['Total Caloric INTAKE'] != null)
                                    Text(
                                      widget.widgetData.data!['topContainer']['Total Caloric INTAKE'],
                                      textAlign: TextAlign.left,
                                      style:  CFTextStyles.bold16(),
                                    )
                                ],
                              ),
                            ),
                          ),
                        ),
                        Container(
                          width: 150,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [

                              Padding(
                                  padding: const EdgeInsets.only(
                                      bottom: Spacings.x3, top: Spacings.x3),
                                  child: caloryWidget("Starchy Carbs".toUpperCase(), widget.widgetData.data!['bottomContainer']['Starchy Carbs'],Colors.blue)),
                              Padding( padding: const EdgeInsets.only(
                                  bottom: Spacings.x3, top: Spacings.x3),
                                  child: caloryWidget("Proteins".toUpperCase(), widget.widgetData.data!['bottomContainer']['Proteins'],Colors.yellow)),
                            ],
                          ),
                        )

                      ],
                    ),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          width: 150,
                          child: Padding(
                            padding:  EdgeInsets.only(
                                bottom: Spacings.x3, top: Spacings.x3),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [

                                if(widget.widgetData.data!['topContainer']['Goal'] != null)
                                  Text(
                                    'Goal'.toUpperCase(),
                                    textAlign: TextAlign.left,
                                    style: themeData
                                        .textStyle(TypescaleValues.P8)
                                        .copyWith(color: ColorPalette.white60),
                                  ),
                                Text(
                                  widget.widgetData.data!['topContainer']["Goal"],
                                  textAlign: TextAlign.left,
                                  style:  CFTextStyles.bold16(),
                                )
                              ],
                            ),
                          ),
                        ),


                        Container(
                          width: 150,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Padding( padding: const EdgeInsets.only(
                                  bottom: Spacings.x3, top: Spacings.x3 ),
                                  child: caloryWidget("Vegetables".toUpperCase(), widget.widgetData.data!['bottomContainer']['Vegetables'],Colors.green)),
                              Padding( padding: const EdgeInsets.only(
                                  bottom: Spacings.x3, top: Spacings.x3),
                                  child: caloryWidget("Fats".toUpperCase(), widget.widgetData.data!['bottomContainer']['Fats'],Colors.red))
                            ],
                          ),
                        )
                      ],
                    )
                  ],
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}

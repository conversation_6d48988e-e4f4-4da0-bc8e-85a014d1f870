import 'package:common/action/action_handler.dart';
import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/atoms/multi_select_chips.dart';
import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/atoms/secondary_button.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/molecules/multi_date_jumper.dart';
import 'package:common/ui/organisms/titlebar/frosty_app_bar.dart';
import 'package:common/ui/theme/Colors.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:flutter/material.dart' hide Action;
import 'package:common/ui/aurora.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:common/ui/widget_factory.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:intl/intl.dart';
import 'package:transform/UI/content_history/screen/content_history_screen.dart';

import '../../../blocs/clp/models.dart';
import '../../../blocs/meal_planner/events.dart';
import '../../../blocs/meal_planner/meal_planner_bloc.dart';
import '../../../blocs/meal_planner/states.dart';
import 'package:common/util/theme.dart';

import '../../../constants/constants.dart';
// import 'components/meal_plan_widget.dart';

class EmptyStateWidgetView extends StatefulWidget {
  final MealPlanEmptyStateWidgetData data;

  EmptyStateWidgetView(this.data);

  @override
  State<StatefulWidget> createState() => _EmptyStateWidgetViewState();
}

class _EmptyStateWidgetViewState extends State<EmptyStateWidgetView> {

  void PerformAction(BuildContext context, Action? action) {
    ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
    PerformActionEvent event = PerformActionEvent(action!);
    actionBloc.add(event);
  }


  @override
  Widget build(BuildContext context) {
    AuroraThemeData themeData = AuroraTheme.of(context);
    return Container(
      child: Container(
        child:
        Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Padding(
              padding: const EdgeInsets.only(top: 120),
              child: Center(
                child: Column(
                  children: [
                    Container(
                      width: 124,
                      height: 124,
                      child: CFNetworkImage(
                        imageUrl: getMediaUrl(
                            widget.data.imageUrl ?? ""),
                        errorWidget: (context, url, error) => Icon(Icons.error),
                      ),
                    ),
                    if(widget.data.title != null)
                    Padding(
                      padding: const EdgeInsets.only(top: 25),
                      child: Center(
                          child: Text(
                        widget.data.title ?? "",
                        textAlign: TextAlign.center,
                        style: themeData
                            .textStyle(TypescaleValues.P6)
                            .copyWith(color: ColorPalette.white60),
                      )),
                    ),
                    Padding(
                      padding: EdgeInsets.only(left: Spacings.x3, right: Spacings.x3, top: Spacings.x1),
                      child: Text(
                        widget.data.subtitle ?? "",
                        textAlign: TextAlign.center,
                        style: CFTextStyles.bold18()
                      ),
                    )
                  ],
                ),
              ),
            ),
        if(widget.data.primaryButtonAction != null && widget.data.secondaryButtonAction != null)
        SizedBox(
        child: DecoratedBox(
        decoration: const BoxDecoration(
        color: Color.fromRGBO(182, 182, 182, 0.2),

        ),
            //
              child: Padding(
                padding: EdgeInsets.only(top: Spacings.x2,left: Spacings.x3, right: Spacings.x3, bottom:Spacings.x2 ),
                child: Column(
                  children: [
                    if(widget.data.primaryButtonAction != null)
                    PrimaryButton(() {
                      PerformAction(context, widget.data.primaryButtonAction);
                    },
                      widget.data.primaryButtonAction!.title ?? "",

                    ),
                    if(widget.data.secondaryButtonAction != null)
                    Padding(
                      padding: const EdgeInsets.only(top: Spacings.x2 ),
                      child: SecondaryButton(
                            () {
                          PerformAction(context, widget.data.secondaryButtonAction);
                        },
                        widget.data.secondaryButtonAction!.title ?? "",

                        height: AuroraTheme.of(context).secondaryButtonHeight,
                      ),
                    )
                  ],
                ),
              )),
            )



          ],
        ),
      ),
    );
  }
}

import 'package:collection/collection.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/theme/Colors.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/util/color.dart';
import 'package:flutter/material.dart';
import 'package:transform/blocs/clp/models.dart';
import 'package:shared_preferences/shared_preferences.dart';

class MealDetailsWidgetViewV2 extends StatefulWidget {
  final MealDetailsWidgetDataV2 mealDetailsData;

  MealDetailsWidgetViewV2(this.mealDetailsData);

  @override
  State<StatefulWidget> createState() => _MealDetailsWidgetViewStateV2();
}

class _MealDetailsWidgetViewStateV2 extends State<MealDetailsWidgetViewV2> {
  @override
  void initState() {
    super.initState();
  }

  Future<SharedPreferences> initialiseSharedPreference() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    return prefs;
  }

  @override
  Widget build(BuildContext context) {
    Widget getMealView(MealPlanDataV2 mealsList, SharedPreferences? data) {
      String? dot = "   • ";
      return Container(
        width: MediaQuery.of(context).size.width,
        child: Align(
          alignment: Alignment.centerLeft,
          child: Column(children: [
            ...mealsList.mealDetails.map<Widget>((meal) {
              return Padding(
                padding: EdgeInsets.only(bottom: Spacings.x6),
                child: Align(
                  alignment: Alignment.centerLeft,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          if (meal.description != null)
                            Padding(
                              padding: const EdgeInsets.only(top: 5.0),
                              child: Text(
                                meal.description ?? "",
                                textAlign: TextAlign.left,
                                style: AuroraTheme.of(context)
                                    .textStyle(TypescaleValues.P8)
                                    .copyWith(color: ColorPalette.white60),
                              ),
                            ),
                          if (meal.unitImage != null)
                            Padding(
                              padding: const EdgeInsets.only(top: 5.0, left: 3.0),
                              child: CFNetworkImage(
                                imageUrl: getImageUrl(
                                  context,
                                  imagePath: meal.unitImage,
                                ),
                                color: Colors.white60,
                                fit: BoxFit.fill,
                              ),
                            ),
                        ],
                      ),
                      if (meal.title != null)
                        Padding(
                          padding:
                              const EdgeInsets.only(top: 5.0, bottom: 10.0),
                          child: Text(
                            meal.title ?? "",
                            textAlign: TextAlign.left,
                            style: AuroraTheme.of(context)
                                .textStyle(TypescaleValues.H4)
                                .copyWith(color: ColorPalette.white),
                          ),
                        ),
                      if (meal.foodGroup != null)
                        BlurView(
                          child: Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(10),
                              shape: BoxShape.rectangle
                            ),
                            child: Text(
                              dot + meal.foodGroup! + "    ",
                              textAlign: TextAlign.left,
                              style: AuroraTheme.of(context)
                                  .textStyle(TypescaleValues.P10)
                                  .copyWith(
                                      color: HexColor.fromHex(
                                          meal.foodGroupColor.toString())),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              );
            }).toList()
          ]),
        ),
      );
    }

    return FutureBuilder(
      future: initialiseSharedPreference(),
      builder:
          (BuildContext context, AsyncSnapshot<SharedPreferences> snapshot) {
        if (snapshot.hasData) {
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.only(
                    left: Spacings.x3, right: Spacings.x3, bottom: Spacings.x3),
                child: Column(
                  children: [
                    if(widget.mealDetailsData.mealPlanDataV2 != null)
                    ...filterListData(widget.mealDetailsData.mealPlanDataV2!,
                            snapshot.data)
                        .mapIndexed<Widget>((index, data) {
                      return Column(
                        children: [
                          Container(
                            child: Padding(
                              padding: EdgeInsets.only(
                                  top: Spacings.x6, bottom: Spacings.x3),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.only(
                                        right: Spacings.x3),
                                    child: Text(
                                      "OPTION " + (index + 1).toString(),
                                      style: AuroraTheme.of(context)
                                          .textStyle(TypescaleValues.P4)
                                          .copyWith(
                                              color: ColorPalette.white70),
                                    ),
                                  ),
                                  GestureDetector(
                                    onTap: () {
                                      saveLikePreference(data, snapshot.data);
                                    },
                                    child: Icon(
                                      checkLikePreference(data, snapshot.data)
                                          ? Icons.favorite
                                          : Icons.favorite_border,
                                      color: checkLikePreference(
                                              data, snapshot.data)
                                          ? Colors.pink
                                          : Colors.white,
                                      size: 24.0,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          BlurView(
                              child: Padding(
                            padding: EdgeInsets.only(
                                top: Spacings.x3,
                                left: Spacings.x3,
                                right: Spacings.x3),
                            child: getMealView(data, snapshot.data),
                          ))
                        ],
                      );
                    }).toList(),
                  ],
                ),
              )
              // )
            ],
          );
        } else {
          return const FancyLoadingIndicator();
        }
      },
    );
  }

  void saveLikePreference(MealPlanDataV2 data, SharedPreferences? prefs) {
    setState(() {
      if (prefs != null) {
        if (prefs.getBool("MealPreferenceLiked_" + data.id) ?? false) {
          prefs.remove("MealPreferenceLiked_" + data.id);
        } else {
          prefs.setBool("MealPreferenceLiked_" + data.id, true);
        }
      }
    });
  }

  bool checkLikePreference(MealPlanDataV2 data, SharedPreferences? prefs) {
    if (prefs != null) {
      return prefs.getBool("MealPreferenceLiked_" + data.id) ?? false;
    }
    return false;
  }

  List<MealPlanDataV2> filterListData(
      List<MealPlanDataV2> planList, SharedPreferences? prefs) {
    List<MealPlanDataV2> filteredList = [];
    planList.forEach((data) {
      if (checkLikePreference(data, prefs)) {
        filteredList.insert(0, data);
      } else {
        filteredList.add(data);
      }
    });
    return filteredList;
  }
}

import 'package:common/ui/atoms/multi_select_chips.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/theme/Colors.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:flutter/material.dart';

import '../../../blocs/clp/models.dart';

class MealDetailsWidgetView extends StatefulWidget {
  final MealDetailsWidgetData mealDetailsData;

  MealDetailsWidgetView(this.mealDetailsData);

  @override
  State<StatefulWidget> createState() => _MealDetailsWidgetViewState();
}

class _MealDetailsWidgetViewState extends State<MealDetailsWidgetView> {
  late Map<String, String> mealTypesWithMealNumber = new Map();
  final selectedMealData = [];
  final selectedIndexList = [];

  final filteredList = [];

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    if (widget.mealDetailsData.mealTypesWithMealNumberMap != null) {
      mealTypesWithMealNumber =
          widget.mealDetailsData.mealTypesWithMealNumberMap!;
    }
    for (var mealData in widget.mealDetailsData.data!['dayPlans']) {
      selectedMealData.add(mealData['mealDetails']);
    }
  }

  void setSelectedMealData() {}

  @override
  Widget build(BuildContext context) {
    List<Widget> getMultiSelectTabs =
        mealTypesWithMealNumber.entries.map<Padding>((e) {
      return Padding(
          padding: EdgeInsets.only(right: 10),
          child: InlineMultiSelectTab(
              buttonText: e.value,
              isSelected: selectedIndexList.contains(e.key),
              onSelected: (bool selected) {
                if (selected) {
                  this.setState(() {
                    for (var mealData in widget
                        .mealDetailsData.data!['dayPlans'][0]['mealDetails']) {
                      if (e.key == mealData['mealNumber']) {
                        this.setState(() {
                          filteredList.add(mealData);
                          selectedIndexList.add(e.key);
                        });
                      }
                    }
                    ;
                  });
                } else {
                  this.setState(() {
                    for (int i = 0; i < filteredList.length; i++)
                      if (filteredList[i]['mealNumber'] == e.key) {
                        filteredList.removeAt(i);
                        selectedIndexList.remove(e.key);
                      }
                  });
                }
              }));
    }).toList();

    String getMealType(int i) {
      String mealNumber = selectedMealData[0][i]['mealNumber'];
      String mealType = mealTypesWithMealNumber[mealNumber] ?? "";
      return mealType;
    }

    Widget getMealView(Map mealsList) {
      List<Widget> mealWidget = [];
      if (mealsList['meals'] != null)
        for (var meal in mealsList['meals']) {
          mealWidget.add(Padding(
            padding: EdgeInsets.only(bottom: Spacings.x6),
            child: Align(
              alignment: Alignment.centerLeft,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                // mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  if (meal['description'] != null)
                    Text(
                      meal['description'].toString(),
                      textAlign: TextAlign.left,
                      style: AuroraTheme.of(context)
                          .textStyle(TypescaleValues.P3)
                          .copyWith(color: ColorPalette.white60),
                    ),
                  if (meal['title'] != null)
                    Text(
                      meal['title'],
                      textAlign: TextAlign.left,
                      style: AuroraTheme.of(context)
                          .textStyle(TypescaleValues.P1)
                          .copyWith(color: ColorPalette.white),
                    )
                ],
              ),
            ),
          ));
        }
      // }
      return Container(
        width: MediaQuery.of(context).size.width,
        child: Align(
            alignment: Alignment.centerLeft,
            child: Column(children: mealWidget)),
      );
    }

    int getLength() {
      final len = filteredList.length != 0
          ? filteredList.length
          : selectedMealData[0].length;
      return len;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(
              left: Spacings.x3, right: Spacings.x3, bottom: Spacings.x3),
          child: Column(
            children: [
              SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Row(
                  children: getMultiSelectTabs,
                ),
              ),
              for (int i = 0; i < getLength(); i++)
                Column(
                  children: [
                    Container(
                      child: Padding(
                        padding: EdgeInsets.only(
                            top: Spacings.x6, bottom: Spacings.x3),
                        child: Row(
                          children: [
                            Padding(
                              padding:
                                  const EdgeInsets.only(right: Spacings.x3),
                              child: Text(
                                filteredList.length != 0
                                    ? (mealTypesWithMealNumber[
                                                selectedIndexList[i]
                                                    .toString()] ??
                                            "")
                                        .toUpperCase()
                                    : getMealType(i).toUpperCase(),
                                style: AuroraTheme.of(context)
                                    .textStyle(TypescaleValues.P4)
                                    .copyWith(color: ColorPalette.white70),
                              ),
                            ),
                            Expanded(
                              child: Container(
                                padding: EdgeInsets.only(
                                    left: Spacings.x2, right: Spacings.x3),
                                height: 1,
                                color: Colors.white70,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    BlurView(
                      child: Padding(
                          padding: EdgeInsets.only(
                              top: Spacings.x3,
                              left: Spacings.x3,
                              right: Spacings.x3),
                          child: getMealView(filteredList.length != 0
                              ? filteredList[0]
                              : selectedMealData[0][i])),
                    )
                  ],
                )
            ],
          ),
        )
        // )
      ],
    );
  }
}

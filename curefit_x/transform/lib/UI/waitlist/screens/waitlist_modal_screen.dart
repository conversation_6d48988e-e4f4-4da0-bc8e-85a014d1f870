import 'package:common/network/client.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/UI/waitlist/models/waitlist_data.dart';
import 'package:transform/UI/waitlist/widgets/registration_failed_widget.dart';
import 'package:transform/UI/waitlist/widgets/waitlist_registration_widget.dart';
import 'package:transform/UI/waitlist/widgets/registration_completed_widget.dart';
import 'package:transform/blocs/clp/clp_bloc.dart';
import 'package:transform/blocs/clp/events.dart';
import 'package:transform/blocs/waitlist/events.dart';
import 'package:transform/blocs/waitlist/state.dart';
import 'package:transform/blocs/waitlist/waitlist_bloc.dart';
import 'package:transform/network/waitlist_repository.dart';
import 'dart:async';

dynamic showWaitlistModalScreen(
    BuildContext context, WaitlistInfo waitlistInfo) {
  StreamController<bool> modalController = StreamController<bool>();
  return showModalBottomSheet<void>(
    shape: RoundedRectangleBorder(
      borderRadius: BorderRadius.only(
        topLeft: Radius.circular(20),
        topRight: Radius.circular(20),
      ),
    ),
    isScrollControlled: true,
    context: context,
    builder: (context) {
      return BlocProvider<WaitlistBloc>(
        create: (context) => WaitlistBloc(
            repository: WaitlistRepository(
                RepositoryProvider.of<NetworkClient>(context))),
        child: BlocListener<WaitlistBloc, WaitlistState>(
          listener: (context, state) {
            if (state is WaitlistSuccess) {
              final coachCLPBloc = BlocProvider.of<CoachCLPBloc>(context);
              coachCLPBloc.add(LoadCoachCLPEvent());
            }
          },
          child: AnimatedSwitcher(
            duration: Duration(milliseconds: 250),
            child: BlocBuilder<WaitlistBloc, WaitlistState>(
              builder: (context, state) {
                if (state is WaitlistSuccess &&
                    waitlistInfo.successInfo != null) {
                  return RegistrationCompleteWidget(
                      waitlistInfo.successInfo!, modalController);
                } else if (state is WaitlistLoading) {
                  return BlurView(
                      borderRadius: 20,
                      child: FractionallySizedBox(
                          heightFactor: 0.8, child: FancyLoadingIndicator()));
                } else if (state is WaitlistFailed) {
                  return RegistrationFailedWidget(modalController);
                }
                return WaitlistRegistrationWidget(
                  waitlistInfo,
                  () {
                    final waitlistBloc = BlocProvider.of<WaitlistBloc>(context);
                    waitlistBloc.add(WaitlistRegisterEvent());
                  },
                );
              },
            ),
          ),
        ),
      );
    },
  ).then((value) => {modalController.add(true)});
}

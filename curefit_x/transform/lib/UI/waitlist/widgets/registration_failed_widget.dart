import 'package:common/ui/blur_view.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:flutter/material.dart';
import 'package:async/async.dart';
import 'dart:async';

class RegistrationFailedWidget extends StatelessWidget {

  CancelableOperation<Null>? cancelableOperation;
  StreamController<bool> controller;

  RegistrationFailedWidget(this.controller);
  @override
  Widget build(BuildContext context) {
    var width = MediaQuery.of(context).size.width;
    var height = MediaQuery.of(context).size.height;

    controller.stream.listen((event) {
      cancelableOperation?.cancel();
    });
    cancelableOperation = CancelableOperation.fromFuture(
      Future.delayed(Duration(seconds: 5), () {
        Navigator.of(context).pop();
      }),
    );

    return BlurView(
      borderRadius: 20,
      child: FractionallySizedBox(
        heightFactor: 0.8,
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
            color: Color.fromRGBO(32, 77, 89, 0.8),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              Divider(
                thickness: 2.5,
                indent: width - width * 0.58,
                endIndent: width - width * 0.58,
                color: Colors.grey.withOpacity(0.3),
              ),
              Container(
                padding: EdgeInsets.only(bottom:height*0.4),
                child: Text(
                  'Waitlist Registration Failed',
                  style: AuroraTheme.of(context).textStyle(TypescaleValues.H1),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/util/theme.dart';
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:transform/UI/waitlist/models/waitlist_data.dart';

class WaitlistRegistrationWidget extends StatelessWidget {
  final Function registerUser;
  final WaitlistInfo widgetData;

  WaitlistRegistrationWidget(this.widgetData, this.registerUser);

  @override
  Widget build(BuildContext context) {
    var width = MediaQuery.of(context).size.width;
    var height = MediaQuery.of(context).size.height;
    return BlurView(
      borderRadius: 20,
      child: FractionallySizedBox(
        heightFactor: 0.8,
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
            color: Color.fromRGBO(32, 77, 89, 0.8),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              Divider(
                thickness: 2.5,
                indent: width - width * 0.58,
                endIndent: width - width * 0.58,
                color: Colors.grey.withOpacity(0.3),
              ),
              SizedBox(
                height: height * 0.005,
              ),
              Container(
                width: width * 0.25,
                height: width * 0.25,
                decoration: BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                ),
                child: Lottie.network(
                  getMediaUrl(widgetData.lottieUrl.toString()),
                ),
              ),
              SizedBox(
                height: height * 0.05,
              ),
              Container(
                width: width * 0.70,
                height: 100,
                child: Column(
                  children: [
                    Text(
                      widgetData.title ?? "",
                      textAlign: TextAlign.center,
                      style: AuroraTheme.of(context).textStyle(TypescaleValues.H3),
                    ),
                    Text(
                      widgetData.subtitle ?? "",
                      textAlign: TextAlign.center,
                      style: AuroraTheme.of(context).textStyle(TypescaleValues.H2),
                    ),
                  ],
                ),
              ),
              if (widgetData.descriptionList != null)
                Container(
                  padding: EdgeInsets.symmetric(vertical: width * 0.12),
                  width: width * 0.76,
                  height: height * 0.25,
                  alignment: Alignment.center,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children:
                        widgetData.descriptionList!.map((descriptionPoint) {
                      return Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          Text(
                            "• ",
                            style: AuroraTheme.of(context).textStyle(TypescaleValues.P4),
                          ),
                          Expanded(
                            child: Text(
                              descriptionPoint,
                              style: AuroraTheme.of(context).textStyle(TypescaleValues.P4),
                            ),
                          ),
                        ],
                      );
                    }).toList(),
                  ),
                ),
              Container(
                height: height * 0.135,
                color: Color.fromRGBO(255, 255, 255, 0.2),
                width: double.infinity,
                padding: EdgeInsets.symmetric(vertical: 10),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    Text(
                      'No additional charges',
                      style: AuroraTheme.of(context).textStyle(TypescaleValues.P8),
                      textAlign: TextAlign.center,
                    ),
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: width * 0.1),
                      width: width * 0.9,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(5),
                        color: Colors.white,
                      ),
                      child: PrimaryButton(() {
                        registerUser();
                      }, 'JOIN THE WAITLIST'),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

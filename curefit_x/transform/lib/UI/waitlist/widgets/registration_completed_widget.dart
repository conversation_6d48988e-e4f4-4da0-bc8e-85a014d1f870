import 'dart:async';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/util/theme.dart';
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:transform/UI/waitlist/models/waitlist_data.dart';
import 'package:async/async.dart';

class RegistrationCompleteWidget extends StatelessWidget {
  final WaitlistSuccessInfo widgetData;

  CancelableOperation<Null>? cancelableOperation;
  StreamController<bool> controller;

  RegistrationCompleteWidget(this.widgetData, this.controller);

  @override
  Widget build(BuildContext context) {
    var width = MediaQuery.of(context).size.width;
    var height = MediaQuery.of(context).size.height;

    controller.stream.listen((event) {
      cancelableOperation?.cancel();
    });
    cancelableOperation = CancelableOperation.fromFuture(
      Future.delayed(Duration(seconds: 5), () {
        Navigator.of(context).pop();
      }),
    );

    return BlurView(
      borderRadius: 20,
      child: FractionallySizedBox(
        heightFactor: 0.8,
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.only(
              topLeft: Radius.circular(20),
              topRight: Radius.circular(20),
            ),
            color: Color.fromRGBO(32, 77, 89, 0.8),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              Divider(
                thickness: 2.5,
                indent: width - width * 0.58,
                endIndent: width - width * 0.58,
                color: Colors.grey.withOpacity(0.3),
              ),
              Container(
                child: Text(
                  widgetData.title,
                  style: AuroraTheme.of(context).textStyle(TypescaleValues.H9),
                  textAlign: TextAlign.center,
                ),
              ),
              Container(
                //margin: EdgeInsets.all(100.0),
                width: width * 0.75,
                height: width * 0.75,
                decoration: BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                ),
                // Lottie insertion
                child: Lottie.network(
                  getMediaUrl(widgetData.lottieUrl.toString()),
                ),
              ),
              Container(
                padding: EdgeInsets.only(
                  left: width * 0.13,
                  right: width * 0.13,
                  bottom: height * 0.1,
                ),
                child: Text(
                  widgetData.description,
                  style: AuroraTheme.of(context).textStyle(TypescaleValues.P1,
                      color: Color.fromRGBO(230, 241, 207, 1)),
                  textAlign: TextAlign.center,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

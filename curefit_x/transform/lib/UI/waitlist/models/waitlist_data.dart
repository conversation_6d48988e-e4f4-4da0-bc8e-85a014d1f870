class WaitlistSuccessInfo {
  String title;
  String lottieUrl;
  String description;
  WaitlistSuccessInfo(this.title, this.lottieUrl, this.description);
}

class WaitlistInfo {
  String? title;
  String? subtitle;
  List<dynamic>? descriptionList;
  String? lottieUrl;
  WaitlistSuccessInfo? successInfo;
  WaitlistInfo(
      {this.descriptionList,
      required this.successInfo,
      this.title,
        this.subtitle,
      this.lottieUrl});

  factory WaitlistInfo.fromJson(dynamic payload) {
    WaitlistSuccessInfo successInfo = WaitlistSuccessInfo(
      payload['successInfo']['title'],
      payload['successInfo']['lottieUrl'],
      payload['successInfo']['description'],
    );
    return WaitlistInfo(
      descriptionList: payload['descriptionList'],
      successInfo: successInfo,
      title: payload['title'],
      subtitle: payload['subtitle'],
      lottieUrl: payload['lottieUrl'],
    );
  }
}

import 'package:collection/collection.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:flutter/material.dart';
import 'package:transform/blocs/trainer/models.dart';

class CoachDetailCardsWidget extends StatelessWidget {
  final CoachDetailCardsWidgetData widgetData;

  const CoachDetailCardsWidget({required this.widgetData, Key? key})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    AuroraThemeData themeData = AuroraTheme.of(context);
    return Container(
        padding: EdgeInsets.only(left: Spacings.x4, right: Spacings.x4, bottom: Spacings.x2),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              widgetData.title ?? "",
              style: themeData.textStyle(TypescaleValues.H7),
            ),
            SizedBox(
              height: Spacings.x8,
            ),
            ...widgetData.coachSections!
                .mapIndexed<Widget>(
                  (index, coachDetail) => CoachDetailCard(coachDetail,
                      isLastCard: index == widgetData.coachSections!.length - 1),
                )
                .toList(),
            // SizedBox(height: Spacings.x20,),
          ],
        ),
    );
  }
}

class CoachDetailCard extends StatelessWidget {
  final CoachSection coachSection;
  final bool isLastCard;

  const CoachDetailCard(this.coachSection, {this.isLastCard = true});

  @override
  Widget build(BuildContext context) {
    AuroraThemeData themeData = AuroraTheme.of(context);
    return Column(
      children: [
        Container(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ClipOval(
                child: CFNetworkImage(
                  imageUrl: getImageUrl(context, imagePath: coachSection.imageUrl),
                  height: scale(context, 100),
                  width: scale(context, 100),
                  fit: BoxFit.fill,
                  errorWidget: (context, url, error) => Icon(Icons.error),
                  placeholder: (BuildContext imageContext, String url) {
                    return Container(
                      height: scale(context, 100),
                      width: scale(context, 100),
                    );
                  },
                ),
              ),
              SizedBox(
                width: Spacings.x3,
              ),
              Expanded(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      coachSection.tagTitle ?? "",
                      style: themeData.textStyle(TypescaleValues.TAGTEXT,
                          color: Colors.white60),
                    ),
                    SizedBox(
                      height: Spacings.x1,
                    ),
                    Text(
                      coachSection.title ?? "",
                      style: themeData.textStyle(TypescaleValues.H2,
                          color: Colors.white),
                    ),
                    SizedBox(
                      height: Spacings.x1,
                    ),
                    Text(
                      coachSection.subtitle ?? "",
                      style: themeData.textStyle(TypescaleValues.P5,
                          color: Colors.white60),
                    ),
                    SizedBox(
                      height: Spacings.x1,
                    ),
                    Text(
                      coachSection.funFactText ?? "",
                      style: themeData.textStyle(TypescaleValues.P5,
                          color: Colors.white60),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        if (!isLastCard)
          SizedBox(
            height: Spacings.x8,
          ),
      ],
    );
  }
}

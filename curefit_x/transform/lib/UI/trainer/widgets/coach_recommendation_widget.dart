import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:common/ui/flutter_scale_tap.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:transform/blocs/trainer/models.dart';
import 'package:common/image/image_url_generator.dart';

class CoachRecommendationWidgetView extends StatefulWidget {
  CoachRecommendationWidgetView(this.coachRecommendationWidget);
  final CoachRecommendationWidgetData coachRecommendationWidget;

  @override
  _CoachRecommendationWidgetViewState createState() =>
      _CoachRecommendationWidgetViewState(coachRecommendationWidget);
}

class _CoachRecommendationWidgetViewState
    extends State<CoachRecommendationWidgetView> {
  CoachRecommendationWidgetData coachRecommendationWidget;

  _CoachRecommendationWidgetViewState(this.coachRecommendationWidget);

  @override
  Widget build(BuildContext context) {
    return Container(
        padding: EdgeInsets.only(left: 20, right: 10, top: 20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Recommended coaches for you',
              style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                  color: Color.fromRGBO(85, 86, 91, 1),
                  decoration: TextDecoration.none),
            ),
            SizedBox(height: 20),
            Container(
                height: 330,
                child: ListView.builder(
                  //     shrinkWrap: true,
                  scrollDirection: Axis.horizontal,
                  itemCount: coachRecommendationWidget.coaches!.length,
                  itemBuilder: (context, index) {
                    return AnimationConfiguration.staggeredList(
                        position: index,
                        child: SlideAnimation(
                            duration: const Duration(milliseconds: 250),
                            horizontalOffset:
                                MediaQuery.of(context).size.width / 2,
                            child: FadeInAnimation(
                              child: CoachCard(
                                  coach: coachRecommendationWidget
                                      .coaches![index]),
                            )));
                  },
                ))
          ],
        ));
  }
}

class CoachCard extends StatelessWidget {
  final Coach coach;

  const CoachCard({required this.coach});

  @override
  Widget build(BuildContext context) {
    return ScaleTap(
        onTap: () {
          ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
          PerformActionEvent event = PerformActionEvent(this.coach.action);
          actionBloc.add(event);
        },
        child: Padding(
            padding: EdgeInsets.only(left: 0, right: 10),
            child: Container(
                width: 220,
                decoration: new BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  gradient: new LinearGradient(
                      colors: [
                        const Color.fromRGBO(42, 51, 81, 1),
                        const Color.fromRGBO(30, 53, 124, 1),
                      ],
                      begin: const FractionalOffset(0.0, 0.0),
                      end: const FractionalOffset(0.0, 1.0),
                      stops: [0.0, 1.0],
                      tileMode: TileMode.clamp),
                ),
                child: Column(
                  children: <Widget>[
                    Hero(
                        tag: coach.heroId!,
                        child: CFNetworkImage(
                            fit: BoxFit.cover,
                            errorWidget: (context, url, error) =>
                                Icon(Icons.error),
                            imageUrl: getImageUrl(context,
                                imagePath: coach.imageUrl!),
                            width: 158,
                            height: 182)),
                    SizedBox(height: 8),
                    Expanded(
                      child: CardContent(
                          title: coach.title,
                          subtitle: coach.subtitle,
                          description: coach.description),
                    ),
                  ],
                ))));
  }
}

class CardContent extends StatelessWidget {
  final String? title;
  final String? subtitle;
  final String? description;

  const CardContent(
      {Key? key,
      required this.title,
      required this.subtitle,
      required this.description})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: <Widget>[
          Text(title!,
              textAlign: TextAlign.center,
              style: TextStyle(
                  fontSize: 16,
                  color: Colors.white,
                  fontWeight: FontWeight.bold)),
          SizedBox(height: 2),
          Text(
            subtitle!,
            textAlign: TextAlign.center,
            style: TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.normal),
          ),
          SizedBox(height: 20),
          Text(
            description!,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
            textAlign: TextAlign.center,
            style: TextStyle(
                color: Color.fromRGBO(200, 204, 212, 1),
                fontSize: 12,
                fontWeight: FontWeight.normal),
          )
        ],
      ),
    );
  }
}

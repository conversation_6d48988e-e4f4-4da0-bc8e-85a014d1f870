import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/trainer/models.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/flutter_scale_tap.dart';

class CoachListWidgetView extends StatelessWidget {
  CoachListWidgetView(this.coach);

  final CoachListingWidgetData coach;

  @override
  Widget build(BuildContext context) {
    return Container(child: <PERSON><PERSON><PERSON>(coach: coach));
  }
}

class CoachCard extends StatelessWidget {
  final CoachListingWidgetData coach;

  const CoachCard({required this.coach});

  @override
  Widget build(BuildContext context) {
    return ScaleTap(
        onTap: () {
          ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
          PerformActionEvent event = PerformActionEvent(this.coach.action);
          actionBloc.add(event);
        },
        child: Container(
            padding: EdgeInsets.only(top: 20, left: 20, right: 20),
            child: Container(
                padding: EdgeInsets.all(20),
                decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10.0),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.2),
                        spreadRadius: 2,
                        blurRadius: 2,
                        offset: Offset(0, 3), // changes position of shadow
                      ),
                    ]),
                child: Row(
                  children: <Widget>[
                    Hero(
                        tag: coach.heroId!,
                        child: ClipRRect(
                            borderRadius: BorderRadius.circular(38.0),
                            child: CFNetworkImage(
                              width: 75,
                              height: 75,
                              fit: BoxFit.cover,
                              imageUrl: getImageUrl(context,
                                  imagePath: coach.imageUrl!),
                              errorWidget: (context, url, error) =>
                                  Icon(Icons.error),
                            ))),
                    SizedBox(height: 8),
                    Expanded(
                      child: CardContent(
                          title: coach.title,
                          subtitle: coach.subtitle,
                          description: coach.description),
                    ),
                    Icon(Icons.arrow_forward_ios, size: 20)
                  ],
                ))));
  }
}

class CardContent extends StatelessWidget {
  final String? title;
  final String? subtitle;
  final String? description;

  const CardContent(
      {Key? key,
      required this.title,
      required this.subtitle,
      required this.description})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: 25, right: 25),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Text(title!,
              textAlign: TextAlign.center,
              style: TextStyle(
                  fontSize: 16,
                  color: Color.fromRGBO(92, 92, 120, 1),
                  fontWeight: FontWeight.normal)),
          SizedBox(height: 2),
          Text(
            subtitle!,
            textAlign: TextAlign.center,
            style: TextStyle(
                color: Color.fromRGBO(175, 175, 188, 1),
                fontSize: 13,
                fontWeight: FontWeight.normal),
          ),
          SizedBox(height: 2),
          Text(
            description!,
            textAlign: TextAlign.center,
            style: TextStyle(
                color: Color.fromRGBO(175, 175, 188, 1),
                fontSize: 13,
                fontWeight: FontWeight.normal),
          )
        ],
      ),
    );
  }
}

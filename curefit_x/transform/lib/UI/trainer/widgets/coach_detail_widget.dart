import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/util/color.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:common/ui//ui_toolkit.dart';
import 'package:transform/blocs/trainer/models.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:common/util/theme.dart';

class CoachDetailWidgetView extends StatelessWidget {
  final CoachDetailWidgetData coachDetailWidgetData;

  const CoachDetailWidgetView(this.coachDetailWidgetData);

  @override
  Widget build(BuildContext context) {
    return SafeArea(
        child: Column(children: [
      Column(
        children: [
          Text(
            "Meet",
            style: AuroraTheme.of(context).textStyle(TypescaleValues.H7),
          ),
          Padding(
              padding: EdgeInsets.only(right: 100),
              child: Text(
                "your",
                style: AuroraTheme.of(context).textStyle(TypescaleValues.H5),
              )),
          Text(
            "coach",
            style: AuroraTheme.of(context).textStyle(TypescaleValues.H5),
          ),
          SizedBox(
            height: 50,
          )
        ],
      ),
      if (coachDetailWidgetData.images != null &&
          coachDetailWidgetData.images!.isNotEmpty)
        ClipRRect(
            borderRadius: BorderRadius.circular(50.0),
            child: Hero(
                tag: coachDetailWidgetData.heroId != null
                    ? coachDetailWidgetData.heroId!
                    : "1",
                child: CFNetworkImage(
                  width: 100,
                  height: 100,
                  imageUrl: getImageUrl(context,
                      imagePath: coachDetailWidgetData.images!.first),
                  fit: BoxFit.cover,
                ))),
      Padding(
          padding: EdgeInsets.symmetric(horizontal: 20, vertical: 20),
          child: Container(
              child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                Container(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      if (coachDetailWidgetData.title != null)
                        Text(coachDetailWidgetData.title!,
                            textAlign: TextAlign.center,
                            style: AuroraTheme.of(context).textStyle(TypescaleValues.H1),),
                      SizedBox(
                        height: 10,
                      ),
                      if (coachDetailWidgetData.subtitle != null)
                        Text(coachDetailWidgetData.subtitle!,
                            textAlign: TextAlign.center,
                            style: AuroraTheme.of(context).textStyle(TypescaleValues.P4,
                                color: Colors.white.withOpacity(0.7))),
                      SizedBox(
                        height: 10,
                      ),
                      Padding(
                          padding: EdgeInsets.symmetric(horizontal: 20),
                          child: Text(coachDetailWidgetData.description ?? "",
                              textAlign: TextAlign.center,
                              style: AuroraTheme.of(context).textStyle(TypescaleValues.P7,
                                  color: Colors.white.withOpacity(0.7))))
                    ],
                  ),
                ),
                if (coachDetailWidgetData.coachSection != null)
                  Padding(
                      padding:
                          EdgeInsets.symmetric(horizontal: 20, vertical: 20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(coachDetailWidgetData.coachSection!.title!,
                              textAlign: TextAlign.center,
                              style: AuroraTheme.of(context).textStyle(TypescaleValues.P1,
                                  color: Colors.white.withOpacity(0.7))),
                          SizedBox(height: 20),
                          Text(
                            coachDetailWidgetData.coachSection!.about!,
                            maxLines: 20,
                            textAlign: TextAlign.center,
                            style: AuroraTheme.of(context).textStyle(TypescaleValues.P2,
                                color: Colors.white.withOpacity(0.7)),
                            overflow: TextOverflow.ellipsis,
                          )
                        ],
                      ))
              ])))
    ]));
  }
}

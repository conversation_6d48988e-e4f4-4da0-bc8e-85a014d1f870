import 'package:common/action/action_handler.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/atoms/floating_button.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/widget_factory.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/blocs/trainer/coach_detail_bloc.dart';
import 'package:transform/blocs/trainer/events.dart';
import 'package:transform/blocs/trainer/models.dart';
import 'package:transform/blocs/trainer/state.dart';
import 'package:common/action/action_handler.dart' as ActionHandler;

class CoachDetailScreenArguments {
  String? productId;
  String? coachId;
  String? heroId;
  String? subCategoryCode;

  CoachDetailScreenArguments(Map<String, dynamic>? payload) {
    this.productId = payload?["productId"];
    this.heroId = payload?["heroId"];
    this.coachId = payload?["coachId"];
    this.subCategoryCode =
        (payload != null && payload["subCategoryCode"] != null)
            ? payload["subCategoryCode"]
            : "";
  }
}

class CoachDetailScreen extends StatefulWidget {
  @override
  _CoachDetailScreenState createState() => _CoachDetailScreenState();
}

class _CoachDetailScreenState extends State<CoachDetailScreen> {
  ScreenArguments? args;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      args = ModalRoute.of(context)!.settings.arguments as ScreenArguments?;
      final coachDetailBloc = BlocProvider.of<CoachDetailBloc>(context);
      if (args != null && args?.params != null && args!.params.isNotEmpty) {
        CoachDetailScreenArguments arguments =
            CoachDetailScreenArguments(args?.params);
        if(arguments.subCategoryCode != null && arguments.subCategoryCode!.isNotEmpty){
          coachDetailBloc.add(LoadTransformCoachesDetailEvent(subCategoryCode: arguments.subCategoryCode));
        } else {
          coachDetailBloc.add(LoadCoachDetailEvent(arguments.coachId,
              heroId: arguments.heroId, productId: arguments.productId));
        }
      } else {
        coachDetailBloc.add(LoadTransformCoachesDetailEvent());
      }
    });
  }

  CanvasTheme draftTheme() {
    TimeOfDay now = TimeOfDay.now();
    return now.hour > 4 && now.hour < 19 ? CanvasTheme.TEAL : CanvasTheme.NIGHT;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      body: Stack(
        children: [
          Aurora(
            canvasTheme: draftTheme(),
            size: MediaQuery.of(context).size,
          ),
          BlocBuilder<CoachDetailBloc, TrainerState>(builder: (context, state) {
            if (state is CoachDetailLoaded) {
              return CoachDetailContainer(
                  coachDetailPageData: state.coachDetailPage);
            } else if (args != null &&
                args?.params["heroId"] != null &&
                args?.params["imageUrl"] != null) {
              return Hero(
                  tag: (args?.params["heroId"]).toString(),
                  child: CFNetworkImage(
                    width: MediaQuery.of(context).size.width,
                    height: MediaQuery.of(context).size.height / 3,
                    imageUrl: getImageUrl(context,
                        imagePath: args?.params["imageUrl"]),
                    fit: BoxFit.cover,
                  ));
            } else {
              return Center(child: CircularProgressIndicator());
            }
          }),
        ],
      ),
      appBar: AppBar(
        leading: Container(),
        flexibleSpace: Align(
          alignment: Alignment.topRight,
          child: Padding(
            padding: EdgeInsets.fromLTRB(
                0,
                AuroraTheme.of(context).embeddedSafeArea.top +
                    MediaQuery.of(context).padding.top,
                5,
                0),
            child: CloseButton(
              color: Colors.white,
              onPressed: () {
                if (Navigator.canPop(context)) {
                  Navigator.pop(context);
                } else {
                  ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
                  actionBloc.add(CloseApplicationEvent());
                }
              },
            ),
          ),
        ),
      ),
    );
  }
}

class CoachDetailContainer extends StatelessWidget {
  final CoachDetailPage coachDetailPageData;

  const CoachDetailContainer({Key? key, required this.coachDetailPageData})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    List widgets = coachDetailPageData.widgets!;
    WidgetFactory widgetFactory = RepositoryProvider.of<WidgetFactory>(context);
    final widgetViews = widgetFactory.createWidgets(widgets);

    return Stack(children: [
      Positioned.fill(
          child: Column(children: [
        Expanded(
            child: ListView(
          children: widgetViews,
        )),
        if (coachDetailPageData.actions != null)
          ...coachDetailPageData.actions!
              .map(
                (e) => FloatingButton(
                  onPress: () {
                    ActionBloc actionBloc =
                        BlocProvider.of<ActionBloc>(context);
                    ActionHandler.Action action =
                        ActionHandler.Action.fromAction(e, {}, canPop: true);
                    PerformActionEvent event = PerformActionEvent(action);
                    actionBloc.add(event);
                  },
                  buttonText: e.title ?? "",
                  titleText: e.subtitle ?? "",
                ),
              )
              .toList()
      ]))
    ]);
  }
}

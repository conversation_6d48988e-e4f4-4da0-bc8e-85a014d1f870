import 'package:common/action/action_handler.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/font/cf_icons.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:common/ui/widget_factory.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:transform/blocs/trainer/events.dart';
import 'package:transform/blocs/trainer/state.dart';
import 'package:transform/blocs/trainer/trainer_bloc.dart';

class CoachSelectionScreenArguments {
  String? productId;

  CoachSelectionScreenArguments(Map<String, dynamic> payload) {
    this.productId = payload["productId"];
  }
}

class CoachSelectionScreen extends StatefulWidget {
  @override
  _CoachSelectionScreenState createState() => _CoachSelectionScreenState();
}

class _CoachSelectionScreenState extends State<CoachSelectionScreen> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final ScreenArguments? args =
        ModalRoute.of(context)!.settings.arguments as ScreenArguments?;
    if (args != null) {
      final CoachSelectionScreenArguments screenArguments =
          CoachSelectionScreenArguments(args.params);
      if (screenArguments.productId != null) {
        final trainerBloc = BlocProvider.of<TrainerBloc>(context);
        trainerBloc.add(LoadTrainerEvent(screenArguments.productId));
      }
    }
    return Scaffold(
        appBar: PreferredSize(
          preferredSize: Size.fromHeight(
              50.0 + AuroraTheme.of(context).embeddedSafeArea.top),
          child: Padding(
            padding: EdgeInsets.only(
                top: AuroraTheme.of(context).embeddedSafeArea.top),
            child: AppBar(
              backgroundColor: Colors.transparent,
              iconTheme: IconThemeData(color: Colors.black),
              title: Text(
                "Select a coach",
                style: AuroraTheme.of(context).textStyle(
                  TypescaleValues.H2,
                  color: Color.fromRGBO(34, 34, 34, 1),
                ),
              ),
              titleSpacing: 0,
              centerTitle: false,
              leading: IconButton(
                icon: const Icon(
                  CFIcons.chevron_left,
                  color: Colors.white,
                  size: 20,
                  semanticLabel: "chevron_left",
                ),
                onPressed: () {
                  if (Navigator.canPop(context)) {
                    Navigator.pop(context);
                  } else if (!Navigator.canPop(context)) {
                    ActionBloc actionBloc =
                        BlocProvider.of<ActionBloc>(context);
                    actionBloc.add(CloseApplicationEvent(shouldReset: true));
                  }
                },
              ),
            ),
          ),
        ),
        extendBodyBehindAppBar: false,
        body: Stack(children: [
          BlocBuilder<TrainerBloc, TrainerState>(
            builder: (context, state) {
              if (state is TrainerLoaded) {
                List widgets = state.trainerPage.widgets!;
                WidgetFactory widgetFactory =
                    RepositoryProvider.of<WidgetFactory>(context);
                final widgetViews = widgetFactory.createWidgets(widgets);

                return Container(
                    child: ListView(
                        children:
                            applyStaggeredAnimation(widgetViews, context)));
              } else {
                return Center(child: CircularProgressIndicator());
              }
            },
          )
        ]));
  }
}

import 'package:collection/collection.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/header/card_header.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:flutter/material.dart';
import 'package:transform/blocs/pack_details/models.dart';

class PackDetailsWidget extends StatelessWidget {
  final PackDetailsWidgetData widgetData;

  const PackDetailsWidget({required this.widgetData});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: Spacings.x4),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (widgetData.widgetHeaderData != null)
            Padding(
              padding: const EdgeInsets.only(bottom: Spacings.x8),
              child: WidgetHeader(
                cardHeaderData: widgetData.widgetHeaderData!,
              ),
            ),
          if (widgetData.packDetailsCards != null)
            ...widgetData.packDetailsCards!
                .mapIndexed<Widget>((index, detailItem) {
              bool isLast = index == widgetData.packDetailsCards!.length - 1;
              return Padding(
                padding: EdgeInsets.only(bottom: isLast ? 0 : Spacings.x7),
                child: PackDetailCard(
                  packDetailItem: detailItem,
                ),
              );
            }).toList(),
        ],
      ),
    );
  }
}

class PackDetailCard extends StatelessWidget {
  final PackDetailItem packDetailItem;

  const PackDetailCard({required this.packDetailItem});

  @override
  Widget build(BuildContext context) {
    AuroraThemeData themeData = AuroraTheme.of(context);
    return BlurView(
      borderRadius: 20,
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          border: Border.all(color: Colors.white24, width: 1),
          borderRadius: BorderRadius.circular(
            20,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.only(
              top: Spacings.x1,
              left: Spacings.x4,
              right: Spacings.x4,
              bottom: Spacings.x4),
          child: Column(
            children: [
              if (packDetailItem.title != null)
                Transform.translate(
                  offset: const Offset(0.0, -(Spacings.x1 + 10)),
                  child: BlurView(
                    borderRadius: 20,
                    child: Container(
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.white24, width: 1),
                        borderRadius: BorderRadius.circular(
                          20,
                        ),
                      ),
                      padding: const EdgeInsets.symmetric(
                          horizontal: Spacings.x2, vertical: Spacings.x1),
                      child: Text(
                        packDetailItem.title ?? "",
                        style: themeData.textStyle(TypescaleValues.TAGTEXT),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ),
              if (packDetailItem.points != null &&
                  packDetailItem.points!.isNotEmpty)
                ...packDetailItem.points!
                    .map<Widget>(
                      (point) => Padding(
                        padding:
                            const EdgeInsets.symmetric(vertical: Spacings.x2),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Icon(
                              Icons.check_circle_outline_rounded,
                              color: Colors.white,
                              size: 18,
                            ),
                            SizedBox(
                              width: Spacings.x2,
                            ),
                            Expanded(
                              child: Text(
                                point,
                                style: themeData.textStyle(TypescaleValues.P5),
                              ),
                            ),
                          ],
                        ),
                      ),
                    )
                    .toList(),
            ],
          ),
        ),
      ),
    );
  }
}

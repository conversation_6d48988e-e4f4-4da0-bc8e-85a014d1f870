import 'package:common/action/action_handler.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/ui/alert_view.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/page/basic_page_container.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit/ui_toolkit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:transform/blocs/pack_details/events.dart';
import 'package:transform/blocs/pack_details/models.dart';
import 'package:transform/blocs/pack_details/pack_details_bloc.dart';
import 'package:transform/blocs/pack_details/state.dart';

class PackDetailsScreenArguments {
  String? subCategoryCode;

  PackDetailsScreenArguments(Map<String, dynamic> payload) {
    this.subCategoryCode =
    payload["subCategoryCode"] != null ? payload["subCategoryCode"] : "";
  }
}

class PackDetailsScreen extends StatefulWidget {
  const PackDetailsScreen({Key? key}) : super(key: key);

  @override
  State<PackDetailsScreen> createState() => _PackDetailsScreenState();
}

class _PackDetailsScreenState extends State<PackDetailsScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      final ScreenArguments? args =
      ModalRoute.of(context)!.settings.arguments as ScreenArguments?;
      String subCategoryCode = "";
      if (args != null) {
        PackDetailsScreenArguments arguments = PackDetailsScreenArguments(args.params);
        subCategoryCode = arguments.subCategoryCode ?? "";
      }
      final packDetailsBloc = BlocProvider.of<PackDetailsBloc>(context);
      packDetailsBloc.add(PackDetailsLoadEvent(subCategoryCode: subCategoryCode));
    });
  }

  CanvasTheme draftTheme() {
    TimeOfDay now = TimeOfDay.now();
    return now.hour > 4 && now.hour < 19 ? CanvasTheme.TEAL : CanvasTheme.NIGHT;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      body: BlocListener<PackDetailsBloc, PackDetailsState>(
        listener: (context, state) {
          if (state is PackDetailsFailedState) {
            showErrorAlert(
              context: context,
              title: "Something went wrong",
              onClose: () {
                Navigator.pop(context);
              },
            );
          }
        },
        child: BlocBuilder<PackDetailsBloc, PackDetailsState>(
          builder: (context, state) {
            PackDetailsScreenData? screenData;
            if (state is PackDetailsLoadedState) {
              screenData = state.packDetailsScreenData;
            }
            return BasicPageContainer(
              canvasTheme: screenData?.themeType ?? draftTheme(),
              onBackPressed: () {
                if (Navigator.canPop(context)) {
                  Navigator.pop(context);
                } else if (!Navigator.canPop(context)) {
                  ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
                  actionBloc.add(CloseApplicationEvent(shouldReset: false));
                }
              },
              title: screenData?.title ?? "",
              titleBarRightActions: screenData?.action != null
                  ? [
                      InkWell(
                        onTap: () {
                          ActionBloc actionBloc =
                              BlocProvider.of<ActionBloc>(context);
                          PerformActionEvent event =
                              PerformActionEvent(screenData!.action!);
                          actionBloc.add(event);
                        },
                        child: Text(
                          screenData?.action!.title ?? "",
                          style: AuroraTheme.of(context)
                              .textStyle(TypescaleValues.P6),
                        ),
                      ),
                    ]
                  : null,
              showLoader: state is PackDetailsLoadingState,
              itemBuilder: (context, widget, index) {
                return AnimationConfiguration.staggeredList(
                    position: index, child: applySlideFade(widget, context));
              },
              widgetData: screenData?.widgets ?? [],
            );
          },
        ),
      ),
    );
  }
}

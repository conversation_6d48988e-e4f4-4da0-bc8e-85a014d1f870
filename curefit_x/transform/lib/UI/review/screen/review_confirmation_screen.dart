import 'dart:async';
import 'package:common/action/action_handler.dart' as ActionHandler;
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:flutter/material.dart';
import 'package:async/async.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ReviewConfirmationScreen extends StatefulWidget {
  final StreamController<bool> controller;
  final ActionHandler.Action? endRouteAction;
  ReviewConfirmationScreen(this.controller, this.endRouteAction);

  @override
  _ReviewConfirmationScreenState createState() =>
      _ReviewConfirmationScreenState();
}

class _ReviewConfirmationScreenState extends State<ReviewConfirmationScreen> {
  @override
  void initState() {
    CancelableOperation<Null>? cancelableOperation;
    widget.controller.stream.listen((event) {
      cancelableOperation?.cancel();
    });
    cancelableOperation = CancelableOperation.fromFuture(
      Future.delayed(Duration(seconds: 3), () {
        if (widget.endRouteAction != null) {
          ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
          PerformActionEvent event = PerformActionEvent(ActionHandler.Action.fromAction(widget.endRouteAction!, {}, canPop: true));
          actionBloc.add(event);
        } else {
          Navigator.of(context).pop();
        }
      }),
    );
    super.initState();
  }

  void onCloseAction() {
    if (widget.endRouteAction != null) {
      ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
      PerformActionEvent event = PerformActionEvent(ActionHandler.Action.fromAction(widget.endRouteAction!, {}, canPop: true));
      actionBloc.add(event);
    } else {
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () async {
          onCloseAction();
          return false;
        },
        child: Scaffold(
            extendBodyBehindAppBar: true,
            appBar: AppBar(
              leading: Container(),
              backgroundColor: Colors.transparent,
              iconTheme: IconThemeData(color: Colors.white),
              flexibleSpace: Align(
                alignment: Alignment.topRight,
                child: Padding(
                  padding: EdgeInsets.fromLTRB(
                      0,
                      AuroraTheme.of(context).embeddedSafeArea.top +
                          MediaQuery.of(context).padding.top,
                      5,
                      0),
                  child: CloseButton(
                    color: Colors.white,
                    onPressed: onCloseAction,
                  ),
                ),
              ),
            ),
            body: LayoutBuilder(
              builder: (context, constraints) {
                if (constraints.maxWidth > 0) {
                  return Stack(
                    children: [
                      Aurora(size: constraints.biggest, context: context,),
                      Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Container(
                              height: 70,
                              width: 70,
                              decoration: BoxDecoration(
                                color: Color.fromRGBO(61, 113, 117, 1),
                                shape: BoxShape.circle,
                              ),
                              child: Icon(
                                Icons.check_outlined,
                                size: 30,
                                color: Color.fromRGBO(15, 228, 152, 1),
                              ),
                            ),
                            Container(
                              padding: EdgeInsets.symmetric(vertical: 20),
                              width: 200,
                              child: Text(
                                "Thanks for submitting your feedback!",
                                textAlign: TextAlign.center,
                                style: AuroraTheme.of(context)
                                    .textStyle(TypescaleValues.P4),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  );
                }
                return Container();
              },
            )),
        );
  }
}

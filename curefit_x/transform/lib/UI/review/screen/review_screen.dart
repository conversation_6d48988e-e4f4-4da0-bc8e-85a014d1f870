import 'dart:async';

import 'package:common/action/action_handler.dart' as ActionHandler;
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/atoms/chips.dart';
import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/UI/review/screen/review_confirmation_screen.dart';
import 'package:transform/blocs/review/events.dart';
import 'package:transform/blocs/review/models.dart';
import 'package:transform/blocs/review/review_bloc.dart';
import 'package:transform/blocs/review/state.dart';

class FeedbackArguments {
  String? feedbackId;
  String? subCategoryCode;

  FeedbackArguments(Map<String, dynamic> payload) {
    this.feedbackId = payload["feedbackId"];
    this.subCategoryCode =
    payload["subCategoryCode"] != null ? payload["subCategoryCode"] : "";
  }
}

class ReviewScreen extends StatefulWidget {
  const ReviewScreen({Key? key}) : super(key: key);

  @override
  _ReviewScreenState createState() => _ReviewScreenState();
}

class _ReviewScreenState extends State<ReviewScreen> {
  late ReviewScreenData reviewScreenData;
  int _rating = 0;
  String _ratingS = "NOT_RATED";
  bool _showInputText = false;
  late TextEditingController _controller;
  late StreamController<bool> _screenController;
  List<dynamic> _selectedTags = [];

  FeedbackArguments? getFeedbackArguments() {
    final ActionHandler.ScreenArguments? args =
        ModalRoute.of(context)!.settings.arguments as ActionHandler.ScreenArguments?;
    if (args != null) {
      return FeedbackArguments(args.params);
    }
    return null;
  }

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      FeedbackArguments? arguments = getFeedbackArguments();
      String? feedbackId = arguments?.feedbackId;
      String? subCategoryCode = arguments?.subCategoryCode;
      if (feedbackId != null) {
        final reviewBloc = BlocProvider.of<ReviewBloc>(context);
        reviewBloc.add(ReviewLoadEvent(feedbackId: feedbackId, subCategoryCode: subCategoryCode));
        _screenController = StreamController<bool>();
      }
    });
    _controller = TextEditingController();
    super.initState();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void onCloseAction() {
    if (reviewScreenData.endRouteAction != null) {
      ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
      PerformActionEvent event = PerformActionEvent(ActionHandler.Action.fromAction(reviewScreenData.endRouteAction!, {}, canPop: true));
      actionBloc.add(event);
    } else {
      Navigator.of(context).pop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: () async {
          onCloseAction();
          return false;
        },
        child: Scaffold(
          extendBodyBehindAppBar: true,
          appBar: AppBar(
            leading: Container(),
            backgroundColor: Colors.transparent,
            iconTheme: IconThemeData(color: Colors.white),
            flexibleSpace: Align(
              alignment: Alignment.topRight,
              child: Padding(
                padding: EdgeInsets.fromLTRB(
                    0,
                    AuroraTheme.of(context).embeddedSafeArea.top +
                        MediaQuery.of(context).padding.top,
                    5,
                    0),
                child: CloseButton(
                  color: Colors.white,
                  onPressed: () {
                    final reviewBloc = BlocProvider.of<ReviewBloc>(context);
                    FeedbackArguments? arguments = getFeedbackArguments();
                    String? feedbackId = arguments?.feedbackId;
                    reviewBloc.add(
                      ReviewRegisterEvent(
                        feedbackId: feedbackId,
                        rating: "DISMISSED",
                        review: "",
                        selectedTags: [],
                      ),
                    );
                    onCloseAction();
                  },
                ),
              ),
            ),
          ),
          body: LayoutBuilder(builder: (context, constraints) {
            if (constraints.maxWidth > 0) {
              return Container(
                color: Colors.black,
                child: Stack(
                  children: [
                    Aurora(
                      size: constraints.biggest,
                      context: context,
                    ),
                    BlocBuilder<ReviewBloc, ReviewState>(
                      builder: (context, state) {
                        if (state is ReviewLoading) {
                          return FancyLoadingIndicator();
                        }
                        if (state is ReviewFailed) {
                          return Center(
                            child: Container(
                              child: Text(
                                "Feedback Loading Failed",
                                style: AuroraTheme.of(context)
                                    .textStyle(TypescaleValues.H1),
                              ),
                            ),
                          );
                        }
                        if (state is ReviewLoaded) {
                          reviewScreenData = state.reviewScreenData;
                          return Positioned.fill(
                            child: Stack(
                              children: [
                                SingleChildScrollView(
                                  child: Container(
                                    padding: EdgeInsets.only(
                                        left: 27, right: 27, top: 100, bottom: 20),
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        reviewScreenData.title != null ? reviewHeader() : Container(),
                                        Container(
                                          padding: const EdgeInsets.only(
                                              bottom: 40, top: 50),
                                          child: Text(
                                            reviewScreenData.question ?? "",
                                            style: AuroraTheme.of(context)
                                                .textStyle(TypescaleValues.H1),
                                          ),
                                        ),
                                        reviewEmojis(),
                                        if (_rating != 0)
                                          Padding(
                                            padding: EdgeInsets.only(
                                                top: 30, bottom: 40),
                                            child: Column(
                                              crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                              mainAxisAlignment:
                                              MainAxisAlignment.center,
                                              children: [
                                                Text(
                                                  reviewScreenData
                                                      .emojiList![_rating - 1]
                                                      .title ??
                                                      "",
                                                  style: AuroraTheme.of(context)
                                                      .textStyle(
                                                      TypescaleValues.H4),
                                                ),
                                                SizedBox(
                                                  height: 10,
                                                ),
                                                Text(
                                                  reviewScreenData
                                                      .emojiList![_rating - 1]
                                                      .subtitle ??
                                                      "",
                                                  style: AuroraTheme.of(context)
                                                      .textStyle(
                                                      TypescaleValues.P5),
                                                ),
                                              ],
                                            ),
                                          ),
                                        if (_rating != 0)
                                          Container(
                                            padding:
                                            const EdgeInsets.only(bottom: 30),
                                            child: Text(
                                              reviewScreenData
                                                  .emojiList![_rating - 1]
                                                  .reviewQuestion ??
                                                  "",
                                              style: AuroraTheme.of(context)
                                                  .textStyle(TypescaleValues.H2),
                                            ),
                                          ),
                                        if (_rating != 0) reviewTags(),
                                        _rating != 0 && _showInputText
                                            ? Padding(
                                          padding: const EdgeInsets.only(
                                              top: 15, bottom: 80),
                                          child: TextField(
                                            controller: _controller,
                                            style: AuroraTheme.of(context)
                                                .textStyle(TypescaleValues.P5,
                                                color: Colors.white),
                                            decoration: InputDecoration(
                                              focusColor: Colors.white,
                                              filled: true,
                                              hintText: 'Tell us More...',
                                              hintStyle: AuroraTheme.of(
                                                  context)
                                                  .textStyle(
                                                  TypescaleValues.P5,
                                                  color: Colors.white
                                                      .withOpacity(0.6)),
                                              fillColor: Color.fromRGBO(
                                                  255, 255, 255, 0.1),
                                              border: OutlineInputBorder(
                                                borderRadius:
                                                BorderRadius.circular(5),
                                                borderSide: BorderSide.none,
                                              ),
                                              contentPadding:
                                              EdgeInsets.symmetric(
                                                  horizontal: 20,
                                                  vertical: 9),
                                            ),
                                          ),
                                        )
                                            : SizedBox(height: 80),
                                      ],
                                    ),
                                  ),
                                ),
                                if (_rating != 0) reviewSubmitButton(),
                              ],
                            ),
                          );
                        }
                        return Container();
                      },
                    ),
                  ],
                ),
              );
            }
            return Container();
          }),
        )
    );
  }

  String getReviewImages(String? url) {
    return "/image/transform/review/" + url! + ".png";
  }

  Widget reviewHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        if (reviewScreenData.imageUrl != null)
          Container(
            margin: EdgeInsets.only(right: 20),
            width: 50,
            height: 50,
            child: CFNetworkImage(
              imageUrl:
                  getMediaUrl(getReviewImages(reviewScreenData.imageUrl!)),
              errorWidget: (context, url, error) => Icon(Icons.error),
            ),
          ),
        Expanded(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                reviewScreenData.title ?? "",
                textAlign: TextAlign.start,
                style: AuroraTheme.of(context).textStyle(TypescaleValues.H1),
              ),
              SizedBox(
                height: 5,
              ),
              Text(
                reviewScreenData.subtitle ?? "",
                textAlign: TextAlign.start,
                style: AuroraTheme.of(context).textStyle(TypescaleValues.P8),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget reviewEmojis() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: List.generate(reviewScreenData.emojiList!.length, (int index) {
        return InkWell(
          child: Container(
              decoration: BoxDecoration(
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.5),
                    blurRadius: 10,
                    offset: Offset(0, 4),
                  ),
                ],
                shape: BoxShape.circle,
                color: _rating == index + 1 ? Colors.white : Colors.transparent,
                border: _rating == index + 1
                    ? Border.all(
                        color: Colors.white,
                        width: 4,
                      )
                    : Border.all(
                        color: Colors.transparent,
                        width: 4,
                      ),
              ),
              height: _rating == index + 1 ? 75 : 65,
              width: _rating == index + 1 ? 75 : 65,
              child: CFNetworkImage(
                imageUrl: getImageUrl(
                  context,
                  imagePath:
                      getReviewImages(reviewScreenData.emojiList![index].icon!),
                ),
                fit: BoxFit.cover,
              )),
          onTap: () {
            setState(() {
              _showInputText = false;
              if (_rating == index + 1) {
                _rating = 0;
                _ratingS = "NOT_RATED";
              } else {
                _controller.clear();
                _selectedTags.clear();
                _ratingS = reviewScreenData.emojiList![index].rating!;
                _rating = index + 1;
              }
            });
          },
        );
      }),
    );
  }

  Widget reviewTags() {
    return Wrap(
      spacing: 15, // gap between adjacent chips
      runSpacing: 15,
      children: [
        ...List.generate(
          reviewScreenData.emojiList![_rating - 1].tags!.length,
          (int index) {
            return Chips(
              text: reviewScreenData.emojiList![_rating - 1].tags![index],
              key: Key(reviewScreenData.emojiList![_rating - 1].tags![index] +
                  _rating.toString()),
              stateChangeEnabled: true,
              onSelected: (bool selected) {
                if (selected) {
                  _selectedTags.add({
                    "text":
                        reviewScreenData.emojiList![_rating - 1].tags![index]
                  });
                } else {
                  _selectedTags.remove({
                    "text":
                        reviewScreenData.emojiList![_rating - 1].tags![index]
                  });
                }
              },
            );
          },
        ),
        Chips(
          text: "Other",
          key: Key("OtherRandom" + _rating.toString()),
          stateChangeEnabled: true,
          onSelected: (bool selected) {
            if (selected) {
              _selectedTags.add({"text": "Other"});
              setState(() {
                _showInputText = true;
              });
            } else {
              _selectedTags.remove({"text": "Other"});
              setState(() {
                _showInputText = false;
              });
            }
          },
        ),
      ],
    );
  }

  Widget reviewSubmitButton() {
    return Container(
      alignment: Alignment.bottomCenter,
      padding: EdgeInsets.only(bottom: 30, left: 27, right: 27),
      child: PrimaryButton(() {
        FeedbackArguments? arguments = getFeedbackArguments();
        String? feedbackId = arguments?.feedbackId;
        String review = "";
        if (_controller.text.isNotEmpty) {
          review = _controller.text;
        }
        final reviewBloc = BlocProvider.of<ReviewBloc>(context);
        reviewBloc.add(ReviewRegisterEvent(
            feedbackId: feedbackId,
            rating: _ratingS,
            review: review,
            selectedTags: _selectedTags));
        _screenController = StreamController<bool>();
        Navigator.pushReplacement(
            context,
            MaterialPageRoute(
              builder: (BuildContext context) =>
                  ReviewConfirmationScreen(_screenController, reviewScreenData.endRouteAction),
            )).then((value) => _screenController.add(true));
      }, reviewScreenData.buttonTitle ?? "SUBMIT"),
    );
  }
}

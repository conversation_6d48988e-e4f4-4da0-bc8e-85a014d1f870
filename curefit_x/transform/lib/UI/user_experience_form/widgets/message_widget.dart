import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:flutter/material.dart' hide Page;
import 'package:transform/UI/user_experience_form/widgets/chat_box_container.dart';
import 'package:transform/blocs/user_experience_form/models.dart';

class MessageWidget extends StatelessWidget {
  final Page widgetData;

  const MessageWidget({required this.widgetData});

  @override
  Widget build(BuildContext context) {
    AuroraThemeData themeData = AuroraTheme.of(context);
    return Container(
      padding: EdgeInsets.symmetric(horizontal: Spacings.x4),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.symmetric(vertical: Spacings.x4),
            child: Column(
              children: [
                Row(
                  children: [
                    ClipRRect(
                        borderRadius: BorderRadius.all(Radius.circular(20)),
                        child: CFNetworkImage(
                          width: 42,
                          height: 42,
                          fit: BoxFit.cover,
                          imageUrl: getImageUrl(context,
                              imagePath: widgetData.coachProfileImage ?? ""),
                          errorWidget: (context, url, error) =>
                              Icon(Icons.error),
                        )),
                    SizedBox(width: Spacings.x2),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widgetData.coachTitle ?? "",
                          style: themeData.textStyle(TypescaleValues.P3,
                              color: Colors.white),
                        ),
                        Text(
                          widgetData.coachSubtitle ?? "",
                          style: themeData.textStyle(TypescaleValues.P8,
                              color: Colors.white60),
                        ),
                      ],
                    ),
                  ],
                ),
                Padding(
                  padding: const EdgeInsets.only(top: Spacings.x3),
                  child: CustomPaint(
                    painter: ChatBoxContainer(),
                    child: Container(
                      padding: EdgeInsets.all(Spacings.x3),
                      child: Text(
                        widgetData.description ?? "",
                        style: themeData.textStyle(TypescaleValues.P1),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

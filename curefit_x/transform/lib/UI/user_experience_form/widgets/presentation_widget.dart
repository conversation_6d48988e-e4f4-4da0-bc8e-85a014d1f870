import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:lottie/lottie.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:flutter/material.dart' hide Page;
import 'package:transform/blocs/user_experience_form/models.dart';

class PresentationWidget extends StatelessWidget {
  final Page widgetData;

  const PresentationWidget({required this.widgetData});

  @override
  Widget build(BuildContext context) {
    AuroraThemeData themeData = AuroraTheme.of(context);
    return Container(
      padding: EdgeInsets.symmetric(horizontal: Spacings.x4),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (widgetData.lottieUrl != null)
            Padding(
              padding: EdgeInsets.only(top: Spacings.x3),
              child: Lottie.network(
                getMediaUrl(widgetData.lottieUrl ?? ""),
                width: scale(context, 200),
                height: scale(context, 200),
              ),
            ),
          if (widgetData.title != null)
            Padding(
              padding: EdgeInsets.symmetric(horizontal: Spacings.x4),
              child: Text(
                widgetData.title ?? "",
                style:
                    themeData.textStyle(TypescaleValues.H2, color: Colors.white),
                textAlign: TextAlign.center,
              ),
            ),
          if (widgetData.subtitle != null)
            Padding(
              padding: EdgeInsets.symmetric(horizontal: Spacings.x2, vertical: Spacings.x2),
              child: Text(
                widgetData.subtitle ?? "",
                style:
                themeData.textStyle(TypescaleValues.P5, color: Colors.white60),
                textAlign: TextAlign.center,
              ),
            ),
        ],
      ),
    );
  }
}

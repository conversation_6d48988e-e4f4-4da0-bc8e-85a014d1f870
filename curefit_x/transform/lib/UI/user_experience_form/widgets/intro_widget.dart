import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:flutter/material.dart' hide Page;
import 'package:transform/blocs/user_experience_form/models.dart';

class IntroWidget extends StatelessWidget {
  final Page widgetData;

  const IntroWidget({required this.widgetData});

  @override
  Widget build(BuildContext context) {
    AuroraThemeData themeData = AuroraTheme.of(context);
    return Container(
      padding: EdgeInsets.symmetric(horizontal: Spacings.x4),
      child: Padding(
        padding: const EdgeInsets.only(bottom: Spacings.x20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            if (widgetData.imageUrl != null)
              BlurView(
                borderRadius: 45,
                child: Container(
                  decoration: BoxDecoration(shape: BoxShape.circle),
                  height: 90,
                  width: 90,
                  child: Padding(
                    padding: const EdgeInsets.all(Spacings.x4),
                    child: CFNetworkImage(
                      imageUrl: getImageUrl(context,
                          imagePath: widgetData.imageUrl ?? ""),
                      fit: BoxFit.fill,
                      errorWidget: (context, url, error) =>
                          const Icon(Icons.error),
                    ),
                  ),
                ),
              ),
            if (widgetData.description != null)
              Padding(
                padding: EdgeInsets.only(top: Spacings.x4),
                child: Text(
                  widgetData.description ?? "",
                  style: themeData.textStyle(TypescaleValues.H4,
                      color: Colors.white),
                ),
              ),
            if (widgetData.title != null)
              Padding(
                padding: EdgeInsets.only(top: Spacings.x3),
                child: Text(
                  widgetData.title ?? "",
                  style: themeData.textStyle(TypescaleValues.H8,
                      color: Colors.white),
                ),
              ),
            if (widgetData.subtitle != null)
              Padding(
                padding: EdgeInsets.only(top: Spacings.x3),
                child: Text(
                  widgetData.subtitle ?? "",
                  style: themeData.textStyle(TypescaleValues.H3,
                      color: Colors.white60),
                ),
              ),
          ],
        ),
      ),
    );
  }
}

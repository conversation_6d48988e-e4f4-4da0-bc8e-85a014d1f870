import 'package:collection/collection.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/atoms/radio_button.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:flutter/material.dart' hide Page;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/UI/user_experience_form/widgets/chat_box_container.dart';
import 'package:transform/blocs/user_experience_form/button_enable_bloc.dart';
import 'package:transform/blocs/user_experience_form/events.dart';
import 'package:transform/blocs/user_experience_form/models.dart';
import 'package:transform/blocs/user_experience_form/user_experience_form_bloc.dart';

class SingleSelectFeedbackWidget extends StatefulWidget {
  final Page widgetData;

  const SingleSelectFeedbackWidget({required this.widgetData});

  @override
  State<SingleSelectFeedbackWidget> createState() =>
      _SingleSelectFeedbackWidgetState();
}

class _SingleSelectFeedbackWidgetState
    extends State<SingleSelectFeedbackWidget> {
  int _selectedIndex = -1;

  @override
  Widget build(BuildContext context) {
    UserExperienceFormBloc userExperienceFormBloc =
        BlocProvider.of<UserExperienceFormBloc>(context);
    if (userExperienceFormBloc.responseMap
            .containsKey(widget.widgetData.pageId) &&
        widget.widgetData.items != null) {
      String mappedValue =
          userExperienceFormBloc.responseMap[widget.widgetData.pageId] ?? "";
      Item selectedItem = widget.widgetData.items!
          .firstWhere((element) => element.id == mappedValue);
      _selectedIndex = widget.widgetData.items!.indexOf(selectedItem);
    }
    AuroraThemeData themeData = AuroraTheme.of(context);
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: Spacings.x4),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (widget.widgetData.title != null)
                Text(
                  widget.widgetData.title ?? "",
                  style: themeData.textStyle(TypescaleValues.H1),
                ),
              if (widget.widgetData.subtitle != null)
                Padding(
                  padding: const EdgeInsets.only(top: Spacings.x1),
                  child: Text(
                    widget.widgetData.subtitle ?? "",
                    style: themeData.textStyle(TypescaleValues.P5,
                        color: Colors.white60),
                  ),
                ),
              SizedBox(
                height: Spacings.x4,
              ),
              if (widget.widgetData.items != null)
                Column(
                  children: widget.widgetData.items!
                      .mapIndexed<Widget>((index, item) {
                    return Padding(
                      padding: const EdgeInsets.only(top: Spacings.x4),
                      child: CFRadioButton(
                        onTap: () {
                          setState(() {
                            _selectedIndex = index;
                            BlocProvider.of<UserExperienceFormBloc>(context)
                                    .responseMap[
                                widget.widgetData.pageId ?? ""] = item.id ?? "";
                            BlocProvider.of<ButtonEnableBloc>(context)
                                .add(ButtonEnableTriggerEvent());
                          });
                        },
                        selected: _selectedIndex == index,
                        text: item.title ?? "",
                      ),
                    );
                  }).toList(),
                ),
            ],
          ),
          AnimatedSwitcher(
            switchInCurve: Curves.fastOutSlowIn,
            switchOutCurve: Curves.fastOutSlowIn,
            duration: Duration(milliseconds: 500),
            transitionBuilder: (Widget child, Animation<double> animation) {
              final inAnimation =
                  Tween<double>(begin: 0, end: 1).animate(animation);
              return ClipRect(
                  child: ScaleTransition(
                      scale: inAnimation,
                      alignment: Alignment.topLeft,
                      child: child));
            },
            child: _selectedIndex != -1
                ? Container(
                    key: ValueKey<int>(1),
                    padding: EdgeInsets.symmetric(vertical: Spacings.x4),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            ClipRRect(
                                borderRadius:
                                    BorderRadius.all(Radius.circular(20)),
                                child: CFNetworkImage(
                                  width: 42,
                                  height: 42,
                                  fit: BoxFit.cover,
                                  imageUrl: getImageUrl(context,
                                      imagePath:
                                          widget.widgetData.coachProfileImage ??
                                              ""),
                                  errorWidget: (context, url, error) =>
                                      Icon(Icons.error),
                                )),
                            SizedBox(width: Spacings.x2),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  widget.widgetData.coachTitle ?? "",
                                  style: themeData.textStyle(TypescaleValues.P3,
                                      color: Colors.white),
                                ),
                                Text(
                                  widget.widgetData.coachSubtitle ?? "",
                                  style: themeData.textStyle(TypescaleValues.P8,
                                      color: Colors.white60),
                                ),
                              ],
                            ),
                          ],
                        ),
                        Padding(
                          padding: const EdgeInsets.only(top: Spacings.x3),
                          child: CustomPaint(
                            painter: ChatBoxContainer(),
                            child: Container(
                              padding: EdgeInsets.all(Spacings.x3),
                              child: Text(
                                widget.widgetData.items![_selectedIndex]
                                        .message ??
                                    "",
                                style: themeData.textStyle(TypescaleValues.H3),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  )
                : Container(
                    key: ValueKey<int>(-1),
                  ),
          ),
        ],
      ),
    );
  }
}

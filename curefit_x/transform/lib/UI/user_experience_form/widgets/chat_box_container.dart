import 'package:flutter/material.dart';

class ChatBoxContainer extends CustomPainter {
  ChatBoxContainer({this.cornerRadius = 10});

  final double cornerRadius;

  @override
  void paint(Canvas canvas, Size size) {
    final Paint fistPaint = new Paint()..color = Colors.white.withOpacity(0.05);
    final Paint secondPaint = new Paint()
      ..color = Colors.white.withOpacity(0.1)
      ..strokeCap = StrokeCap.round
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;
    final height = size.height;
    final width = size.width;
    Path path = Path();
    path.moveTo(cornerRadius, height);
    path.lineTo(width - cornerRadius, height);
    path.cubicTo(width - cornerRadius, height, width, height, width,
        height - cornerRadius);
    path.lineTo(width, cornerRadius);
    path.cubicTo(width, cornerRadius, width, 0, width - cornerRadius, 0);
    path.lineTo(4 * cornerRadius, 0);
    path.cubicTo(4 * cornerRadius, 0, 0, 0, 0, -2 * cornerRadius);
    path.lineTo(0, height - cornerRadius);
    path.cubicTo(0, height - cornerRadius, 0, height, cornerRadius, height);
    path.close();
    canvas.drawPath(path, fistPaint);
    canvas.drawPath(path, secondPaint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

import 'package:common/action/action_handler.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/constants/action_constants.dart';
import 'package:common/font/cf_icons.dart';
import 'package:common/ui/alert_view.dart';
import 'package:common/ui/atoms/floating_button.dart';
import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/atoms/secondary_button.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:flutter/material.dart' hide Page;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:transform/UI/user_experience_form/widgets/intro_widget.dart';
import 'package:transform/UI/user_experience_form/widgets/message_widget.dart';
import 'package:transform/UI/user_experience_form/widgets/presentation_widget.dart';
import 'package:transform/UI/user_experience_form/widgets/single_select_feedback_widget.dart';
import 'package:transform/blocs/user_experience_form/button_enable_bloc.dart';
import 'package:transform/blocs/user_experience_form/events.dart';
import 'package:transform/blocs/user_experience_form/models.dart';
import 'package:transform/blocs/user_experience_form/states.dart';
import 'package:transform/blocs/user_experience_form/user_experience_form_bloc.dart';

class UserExperienceFormScreenArguments {
  String? subCategoryCode;

  UserExperienceFormScreenArguments(Map<String, dynamic> payload) {
    this.subCategoryCode =
    payload["subCategoryCode"] != null ? payload["subCategoryCode"] : "";
  }
}

class UserExperienceFormScreen extends StatefulWidget {
  const UserExperienceFormScreen({Key? key}) : super(key: key);

  @override
  State<UserExperienceFormScreen> createState() =>
      _UserExperienceFormScreenState();
}

class _UserExperienceFormScreenState extends State<UserExperienceFormScreen> {
  late PageController pageController;

  @override
  void initState() {
    super.initState();
    pageController = PageController(initialPage: 0);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final ScreenArguments? args =
      ModalRoute.of(context)!.settings.arguments as ScreenArguments?;
      String subCategoryCode = "";
      if (args != null) {
        UserExperienceFormScreenArguments arguments = UserExperienceFormScreenArguments(args.params);
        subCategoryCode = arguments.subCategoryCode ?? "";
      }
      final userPreferenceFormBloc =
          BlocProvider.of<UserExperienceFormBloc>(context);
      userPreferenceFormBloc.add(UserExperienceFormLoadEvent(subCategoryCode: subCategoryCode));
    });
  }

  @override
  void dispose() {
    pageController.dispose();
    super.dispose();
  }

  CanvasTheme draftTheme() {
    TimeOfDay now = TimeOfDay.now();
    return now.hour > 4 && now.hour < 19 ? CanvasTheme.TEAL : CanvasTheme.NIGHT;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      body: LayoutBuilder(
        builder: (context, constraints) {
          AuroraThemeData themeData = AuroraTheme.of(context);
          themeData.canvasTheme = draftTheme();
          if (constraints.maxWidth > 0) {
            return Container(
              child: Stack(
                children: [
                  Aurora(
                    size: constraints.biggest,
                    context: context,
                  ),
                  BlocListener<UserExperienceFormBloc, UserExperienceFormState>(
                    listener: (context, state) {
                      if (state is UserExperienceFormFailedState) {
                        showErrorAlert(
                            context: context,
                            title: "Something went wrong",
                            onClose: () {
                              Navigator.pop(context);
                            });
                      }
                    },
                    child: BlocBuilder<UserExperienceFormBloc,
                        UserExperienceFormState>(
                      builder: (context, state) {
                        if (state is UserExperienceFormLoadingState) {
                          return FancyLoadingIndicator();
                        } else if (state is UserExperienceFormLoadedState) {
                          final UserExperienceFormData userExperienceFormData =
                              state.userExperienceFormData;
                          return Positioned.fill(
                            child: PageView(
                              scrollDirection: Axis.horizontal,
                              physics: NeverScrollableScrollPhysics(),
                              padEnds: false,
                              controller: pageController,
                              children: <Widget>[
                                for (int idx = 0;
                                    idx < userExperienceFormData.pages!.length;
                                    idx++)
                                  AnimatedSize(
                                    duration: Duration(milliseconds: 250),
                                    child: Align(
                                      alignment: Alignment.center,
                                      child: SingleChildScrollView(
                                          scrollDirection: Axis.vertical,
                                          child: Container(
                                            height: MediaQuery.of(context)
                                                .size
                                                .height,
                                            child: Column(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.start,
                                              children: [
                                                SizedBox(height: Spacings.x24),
                                                Expanded(
                                                  child: getScreenWidget(
                                                      userExperienceFormData
                                                          .pages!
                                                          .elementAt(idx)),
                                                ),
                                                getBottomButtons(
                                                    userExperienceFormData
                                                        .pages!
                                                        .elementAt(idx),
                                                    userExperienceFormData
                                                            .pages!.last ==
                                                        userExperienceFormData
                                                            .pages!
                                                            .elementAt(idx)),
                                              ],
                                            ),
                                          )),
                                    ),
                                  ),
                              ],
                            ),
                          );
                        }
                        return Container();
                      },
                    ),
                  ),
                  BlocBuilder<ButtonEnableBloc, ButtonEnableState>(
                      builder: (context, state) {
                    int selectedPage =
                        BlocProvider.of<ButtonEnableBloc>(context).selectedPage;
                    return selectedPage == 0
                        ? Container()
                        : Positioned(
                            left: 0,
                            top: 40,
                            child: IconButton(
                              icon: const Icon(
                                CFIcons.chevron_left,
                                color: Colors.white,
                                size: 20,
                                semanticLabel: "chevron_left",
                              ),
                              onPressed: () {
                                BlocProvider.of<ButtonEnableBloc>(context)
                                    .selectedPage = selectedPage - 1;
                                BlocProvider.of<ButtonEnableBloc>(context)
                                    .add(ButtonEnableTriggerEvent());
                                pageController.previousPage(
                                    duration: Duration(milliseconds: 500),
                                    curve: Curves.easeInOutSine);
                              },
                            ),
                          );
                  }),
                ],
              ),
            );
          }
          return Container();
        },
      ),
    );
  }

  void actionTrigger(Page page) {
    setState(() {
      if (page.action?.type != ActionTypes.EMPTY_ACTION) {
        ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
        PerformActionEvent event = PerformActionEvent(page.action!);
        actionBloc.add(event);
      } else {
        ButtonEnableBloc buttonEnableBloc =
            BlocProvider.of<ButtonEnableBloc>(context);
        buttonEnableBloc.selectedPage = buttonEnableBloc.selectedPage + 1;
        buttonEnableBloc.add(ButtonEnableTriggerEvent());
        pageController.nextPage(
            duration: Duration(milliseconds: 500), curve: Curves.easeInOutSine);
      }
    });
  }

  Widget getBottomButtons(Page page, isLastPage) {
    bool isButtonEnabled = false;
    return BlocBuilder<ButtonEnableBloc, ButtonEnableState>(
      builder: (context, state) {
        if (state is ButtonEnableIdleState ||
            state is ButtonEnableLoadedState) {
          String? optionSelected =
              BlocProvider.of<UserExperienceFormBloc>(context)
                  .responseMap[page.pageId ?? ""];
          switch (page.pageType) {
            case PageType.SINGLE_SELECT_SCREEN:
              isButtonEnabled =
                  optionSelected != null && optionSelected.isNotEmpty;
              break;
            default:
              isButtonEnabled = true;
          }
        }
        return Row(
          children: [
            if (page.isPrimaryButton)
              Flexible(
                flex: 1,
                child: page.pageType == PageType.INTRO_SCREEN
                    ? FloatingButton(
                        onPress: () {
                          actionTrigger.call(page);
                        },
                        buttonText: page.action?.title ?? "",
                        titleText: page.action?.subtitle,
                  bottomPadding: Spacings.x4,
                      )
                    : Padding(
                        padding: const EdgeInsets.all(Spacings.x4),
                        child: PrimaryButton(
                          () {
                            actionTrigger.call(page);
                          },
                          page.action!.title ?? "NEXT",
                          enabled: isButtonEnabled,
                        ),
                      ),
              ),
            if (!page.isPrimaryButton && isButtonEnabled)
              Flexible(
                flex: 1,
                child: Padding(
                  padding: const EdgeInsets.all(Spacings.x4),
                  child: SecondaryButton(
                    () {
                      actionTrigger.call(page);
                    },
                    page.action!.title ?? "NEXT",
                    enabled: isButtonEnabled,
                  ),
                ),
              )
          ],
        );
      },
    );
  }

  Widget getScreenWidget(Page page) {
    switch (page.pageType) {
      case PageType.SINGLE_SELECT_SCREEN:
        return SingleSelectFeedbackWidget(widgetData: page);
      case PageType.INTRO_SCREEN:
        return Padding(
            padding: EdgeInsets.only(top: Spacings.x24),
            child: IntroWidget(widgetData: page));
      case PageType.PRESENTATION_SCREEN:
        return PresentationWidget(widgetData: page);
      case PageType.RESPONSE_SCREEN:
        return MessageWidget(widgetData: page);
    }
    return Container();
  }
}

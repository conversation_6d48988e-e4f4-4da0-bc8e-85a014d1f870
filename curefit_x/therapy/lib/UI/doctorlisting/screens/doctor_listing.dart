import 'package:common/action/action_handler.dart';
import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/scroll/scroll_bloc.dart';
import 'package:common/font/cf_icons.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/header/card_header.dart';
import 'package:common/ui/header/search_header.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/molecules/bottom_tray.dart';
import 'package:common/ui/organisms/search_bar.dart' as AuroraSearchBar;
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit/ui_toolkit.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:common/ui/widget_factory.dart';
import 'package:common/video/video_player_registry.dart';
import 'package:flutter/material.dart' hide Action;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import '../widgets/filter_modal.dart';
import 'package:therapy/blocs/doctorlisting/bloc/doctor_listing_bloc.dart';
import 'package:therapy/blocs/doctorlisting/models/models.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:collection/collection.dart';
import 'package:common/util/util.dart';

class DoctorListingScreen extends StatefulWidget {
  const DoctorListingScreen({Key? key}) : super(key: key);

  @override
  _DoctorListingScreenState createState() => _DoctorListingScreenState();
}

class _DoctorListingScreenState extends State<DoctorListingScreen> {
  late VideoPlayerRegistry _videoPlayerRegistry;
  DoctorListingScreenArguments? arguments;

  DoctorListingScreenArguments? getScreenArguments() {
    final ScreenArguments? args =
        ModalRoute.of(context)!.settings.arguments as ScreenArguments?;
    if (args != null) {
      return DoctorListingScreenArguments(args.params);
    }
    return null;
  }

  late TextEditingController _textEditingController;
  String myText = "";

  @override
  void dispose() {
    _videoPlayerRegistry.hardDispose();
    super.dispose();
  }

  @override
  void initState() {
    _textEditingController = TextEditingController();
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      _videoPlayerRegistry =
          RepositoryProvider.of<VideoPlayerRegistry>(context);
      arguments = getScreenArguments();
      if (arguments != null) {
        if (arguments!.productId != null) {
          final doctorListingBloc = BlocProvider.of<DoctorListingBloc>(context);
          doctorListingBloc.add(LoadDoctorListingEvent(
              appliedFilters: arguments!.preAppliedFilters ?? const {},
              productId: arguments!.productId!,
              parentBookingId: arguments!.parentBookingId,
              isDoctorSearch: arguments!.isDoctorSearch ?? "false"));
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      body: Stack(
        children: [
          Aurora(
            context: context,
            size: MediaQuery.of(context).size,
          ),
          Positioned.fill(
            child: buildWidgets(),
          ),
          BlocBuilder<DoctorListingBloc, DoctorListingState>(
            builder: (context, state) {
              if (state is DoctorListingLoading) {
                _textEditingController.text = myText;
                _textEditingController.selection = TextSelection.fromPosition(
                    TextPosition(offset: _textEditingController.text.length));
                return const PageLoadingIndicator();
              }
              return Container();
            },
          )
        ],
      ),
    );
  }

  Widget searchWidget(
      {required DoctorListingFilterData doctorListingFilterData,
      required DoctorListingData doctorListingData,
      DoctorListingSelectedFilterData? doctorListingSelectedFilterData}) {
    return Padding(
        padding: EdgeInsets.symmetric(
            vertical: AuroraTheme.of(context).spacing(Spacing.x1),
            horizontal: AuroraTheme.of(context).spacing(Spacing.x4)),
        child: AuroraSearchBar.SearchBar(
            textEditingController: _textEditingController,
            icon: CFIcons.filter_icon,
            onIconTapped: () {
              showFilterModal(
                  doctorListingFilterData, doctorListingSelectedFilterData);
            },
            onTextChanged: (e) {
              myText = e;
              final doctorListingBloc =
                  BlocProvider.of<DoctorListingBloc>(context);
              doctorListingBloc.add(FilterDoctorCardsEvent(searchText: e));
            },
            placeholderText: doctorListingData.placeHolder));
  }

  void showFilterModal(DoctorListingFilterData doctorListingFilterData,
      DoctorListingSelectedFilterData? doctorListingSelectedFilterData) {
    final doctorListingBloc = BlocProvider.of<DoctorListingBloc>(context);
    filterModal(context, doctorListingFilterData,
        doctorListingSelectedFilterData, doctorListingBloc, arguments, myText);
    RepositoryProvider.of<AnalyticsRepository>(context).logWidgetClick(
        widgetInfo: WidgetInfo(
            widgetMetric: WidgetMetric(
                widgetId: '', widgetName: 'Search listing Widget '),
            widgetType: WidgetTypes.SEARCH_LISTING_WIDGET));
  }

  Widget buildFilters(
      {required DoctorListingSelectedFilterData
          doctorListingSelectedFilterData}) {
    List<DoctorListingSelectedFilterDataItem> selectedFilterItem =
        doctorListingSelectedFilterData.selectedFilterDataItem ?? [];
    return doctorListingSelectedFilterData.selectedFilterDataItem!.isNotEmpty
        ? Padding(
            padding: const EdgeInsets.fromLTRB(20, 10, 0, 0),
            child: Container(
              alignment: Alignment.centerLeft,
              height: 40,
              child: ListView.separated(
                separatorBuilder: (context, index) {
                  return const SizedBox(width: 10);
                },
                shrinkWrap: true,
                scrollDirection: Axis.horizontal,
                itemCount: doctorListingSelectedFilterData
                    .selectedFilterDataItem!.length,
                itemBuilder: (context, index) {
                  return GestureDetector(
                    onTap: () {
                      if (arguments != null) {
                        if (arguments!.productId != null &&
                            arguments!.productSlug != null &&
                            arguments!.isDoctorSearch != null) {
                          selectedFilterItem = selectedFilterItem
                              .where((element) =>
                                  doctorListingSelectedFilterData
                                      .selectedFilterDataItem![index]
                                      .filterCode !=
                                  element.filterCode)
                              .toList();
                          Map<String, List<String>> localAppliedFilters = {};
                          for (int i = 0; i < selectedFilterItem.length; i++) {
                            if (localAppliedFilters[
                                    selectedFilterItem[i].filterType] !=
                                null) {
                              localAppliedFilters[
                                      selectedFilterItem[i].filterType]!
                                  .add(selectedFilterItem[i].filterCode);
                            } else {
                              localAppliedFilters[
                                  selectedFilterItem[i].filterType] = [
                                selectedFilterItem[i].filterCode
                              ];
                            }
                          }

                          final doctorListingBloc =
                              BlocProvider.of<DoctorListingBloc>(context);
                          doctorListingBloc.add(LoadDoctorListingEvent(
                              myText: myText,
                              appliedFilters: localAppliedFilters,
                              productId: arguments!.productId!,
                              parentBookingId: arguments!.parentBookingId,
                              isDoctorSearch: arguments!.isDoctorSearch!));
                        }
                      }
                    },
                    child: Container(
                      decoration: const BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.all(Radius.circular(20))),
                      child: Padding(
                        padding: const EdgeInsets.fromLTRB(15, 10, 10, 10),
                        child: Row(children: [
                          Text(
                            doctorListingSelectedFilterData
                                .selectedFilterDataItem![index].displayName,
                            style: AuroraTheme.of(context).textStyle(
                                TypescaleValues.P3,
                                color: Colors.black),
                          ),
                          const Padding(
                            padding: EdgeInsets.fromLTRB(5, 1, 0, 0),
                            child: Icon(
                              Icons.close,
                              color: Colors.black,
                              size: 15,
                            ),
                          )
                        ]),
                      ),
                    ),
                  );
                },
              ),
            ),
          )
        : Container();
  }

  Widget buildWidgets() {
    return BlocBuilder<DoctorListingBloc, DoctorListingState>(
        builder: (context, state) {
      if (state is DoctorListingLoaded) {
        DoctorListingData doctorListingData = state.doctorListingData;
        dynamic doctorSearchWidget = doctorListingData.widgets.firstWhereOrNull(
            (element) =>
                element['widgetType'] ==
                EnumToString.convertToString(
                    WidgetTypes.SEARCH_LISTING_WIDGET));
        List<dynamic> doctorResults = doctorSearchWidget['list'];
        WidgetFactory widgetFactory =
            RepositoryProvider.of<WidgetFactory>(context);
        DoctorListingFilterData? doctorListingFilterData =
            doctorListingData.doctorListingFilterData;

        DoctorListingSelectedFilterData doctorListingSelectedFilterData =
            doctorListingData.doctorListingSelectedFilterData!;

        List? filteredCards = state.filteredDoctorCards ?? [];
        if (myText.isEmpty) {
          doctorResults = [...doctorResults];
        } else if (filteredCards.isNotEmpty) {
          doctorResults = [...filteredCards];
        } else if (myText.isNotEmpty && filteredCards.isEmpty) {
          doctorResults = [];
        }

        return BlocProvider<ScrollBloc>(
            create: (scrollContext) => ScrollBloc(),
            child: BlocBuilder<ScrollBloc, ScrollState>(
                builder: (scrollContext, state) => NotificationListener(
                    onNotification: (notification) {
                      if (notification is ScrollEndNotification) {
                        BlocProvider.of<ScrollBloc>(scrollContext)
                            .add(ScrollEndedEvent());
                      }
                      return true;
                    },
                    child: CustomScrollView(
                      slivers: [
                        SliverPadding(
                            padding: const EdgeInsets.only(bottom: 20),
                            sliver: SliverPersistentHeader(
                              floating: true,
                              delegate: SearchHeader(
                                  BlurView(
                                      borderRadius: 0,
                                      child: Column(children: [
                                        SizedBox(
                                            height: AuroraTheme.of(context)
                                                .embeddedSafeArea
                                                .top),
                                        AppBar(
                                            leading: IconButton(
                                                icon: const Icon(
                                                    CFIcons.chevron_left,
                                                    color: Colors.white,
                                                  semanticLabel: "chevron_left",
                                                ),
                                                onPressed: () =>
                                                    Navigation.popOrExit(
                                                        context)),
                                            centerTitle: false,
                                            titleSpacing: 0.0,
                                            title: Text("Book Appointment",
                                                style: AuroraTheme.of(context)
                                                    .textStyle(
                                                        TypescaleValues.H1))),
                                        searchWidget(
                                            doctorListingFilterData:
                                                doctorListingFilterData!,
                                            doctorListingData:
                                                doctorListingData,
                                            doctorListingSelectedFilterData:
                                                doctorListingSelectedFilterData),
                                        buildFilters(
                                            doctorListingSelectedFilterData:
                                                doctorListingSelectedFilterData),
                                      ])),
                                  MediaQuery.of(context).padding.top,
                                  (doctorListingSelectedFilterData
                                              .selectedFilterDataItem!.isEmpty
                                          ? 120
                                          : 175) +
                                      MediaQuery.of(context).padding.top +
                                      AuroraTheme.of(context)
                                          .embeddedSafeArea
                                          .top),
                            )),
                        AnimationLimiter(
                            child: SliverList(
                                delegate: SliverChildBuilderDelegate(
                                    (context, index) {
                          Widget child;
                          dynamic widgetData = doctorResults[index];
                          if (widgetData["widgetType"] ==
                              "EDIT_THERAPY_PREFERNCE") {
                            child = GestureDetector(
                              onTap: () {
                                showFilterModal(doctorListingFilterData,
                                    doctorListingSelectedFilterData);
                              },
                              child: WidgetHeader(
                                  cardHeaderData:
                                      WidgetHeaderData.fromJson(widgetData)),
                            );
                          } else {
                            child = widgetFactory.createWidget({
                              ...widgetData,
                              'doctorCardFooterAction':
                                  doctorSearchWidget['doctorCardFooterAction'],
                              'widgetMetric': {
                                'widgetName': widgetData['isFeatured'] != null
                                    ? 'FeaturedTherapistCard'
                                    : 'NormalTherapistCard'
                              }
                            });
                          }
                          return AnimationConfiguration.staggeredList(
                              position: index,
                              child: applyHorizontalSlideFade(
                                  Padding(
                                      padding: const EdgeInsets.fromLTRB(
                                          20, 0, 10, 20),
                                      child: child),
                                  context));
                        }, childCount: doctorResults.length)))
                      ],
                    ))));
      }
      return Container();
    });
  }

  filterModal(
      BuildContext context,
      DoctorListingFilterData doctorListingFilterData,
      DoctorListingSelectedFilterData? doctorListingSelectedFilterData,
      DoctorListingBloc bloc,
      DoctorListingScreenArguments? arguments,
      String myText) {
    showBottomTray(
        context: context,
        child: TherapyFilterModal(
            bloc: bloc,
            myText: myText,
            doctorListingScreenArguments: arguments,
            doctorListingFilterData: doctorListingFilterData,
            doctorListingSelectedFilterData: doctorListingSelectedFilterData));
  }
}

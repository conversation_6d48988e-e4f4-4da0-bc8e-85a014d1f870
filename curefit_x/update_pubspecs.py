import os
import yaml
import argparse

# Array of submodule directories relative to the root
SUBMODULES = ["core"]

# Modules which are migrated
MIGRATED_MODULES = ["common"]

# Errors to display at the end of this program for visibility
visibleErrorsAtEnd = []

# Loading file data
def load_versions(file_path):
    if os.path.isfile(file_path):
      with open(file_path, 'r') as file:
          return yaml.safe_load(file)
    else:
      visibleErrorsAtEnd.append(f"No file found at: {file_path}")
      return None
# safe dumping file data
def dump_data(data, file_path):
  with open(file_path, 'w') as file:
      yaml.dump(data, file, default_flow_style=False)

# updating flutter package version
def update_flutter_package_version(module):
  file_path = f"../../flutter-packages/{module}/pubspec.yaml"

  if os.path.isfile(file_path) == False:
    visibleErrorsAtEnd.append(f"Skipping Module: {module} no file found at path: {file_path}")
    return

  data = load_versions(file_path)
  if data == None:
    return

  version = data.get('version', None)
  print(f"{module} Version available to sync: [{version}]")

  if version == None:
    visibleErrorsAtEnd(f"[Terminating]: No {module} Version found")
    return

  flutter_package_versions_path = "flutter_package_versions.yaml"

  package_data = load_versions(flutter_package_versions_path)
  if package_data == None:
      return

  if 'versions' in package_data and f'{module}' in package_data['versions'] and 'git' in package_data['versions'][f'{module}']:
      if package_data['versions'][f'{module}']['git']['ref'] == f"{module}-v" + version:
        print("No update needed in: flutter_package_versions.yaml")
        return
      package_data['versions'][f'{module}']['git']['ref'] = f"{module}-v" + version
  else:
      visibleErrorsAtEnd(f"[Terminating]: The key structure 'versions.{module}.git.ref' does not exist in the flutter_package_versions.yaml file.")
      return

  dump_data(package_data, flutter_package_versions_path)

  print(f"Updated flutter_package_versions.yaml with version: {version}")

def update_pubspec_file(pubspec_path, versions, mode, is_parent):

    data = load_versions(pubspec_path)
    if data == None:
          return

    updated = False
    if 'dependencies' in data:
        for module_name, version_info in versions['versions'].items():
            if module_name in data['dependencies']:
                if mode == 'local':
                    new_dep = {'path': version_info['local']['parent' if is_parent else 'submodules']}
                elif mode == 'git' or mode == 'sync':
                    new_dep = {'git': version_info['git']}
                else:
                    raise ValueError(f"Unknown mode: {mode}")

                print(f"Updating {module_name} in {pubspec_path}")
                data['dependencies'][module_name] = new_dep
                updated = True

    if updated:
        dump_data(data, pubspec_path)
        print(f"Updated {pubspec_path}")
    else:
        print(f"No updates needed in {pubspec_path}")

def main():
    parser = argparse.ArgumentParser(description="Update pubspec.yaml dependencies.")
    parser.add_argument('mode', choices=['local', 'git', 'sync'], help="Mode to update dependencies to (local or git).")
    parser.add_argument('--config', default='flutter_package_versions.yaml', help="Path to the versions configuration file.")
    args = parser.parse_args()

    if args.mode == 'sync':
      for module in MIGRATED_MODULES:
        update_flutter_package_version(f'{module}')

    versions = load_versions(args.config)

    # Update submodules
    for module in SUBMODULES:
        pubspec_file = os.path.join(module, 'pubspec.yaml')
        if os.path.isfile(pubspec_file):
            update_pubspec_file(pubspec_file, versions, args.mode, is_parent=False)
        else:
            visibleErrorsAtEnd(f"pubspec.yaml not found in {module}")

    # Update parent module (current directory)
    parent_pubspec_file = 'pubspec.yaml'
    if os.path.isfile(parent_pubspec_file):
        update_pubspec_file(parent_pubspec_file, versions, args.mode, is_parent=True)
    else:
        visibleErrorsAtEnd(f"pubspec.yaml not found in the current directory")

    os.system("sh install.sh")

    ## Printing errors in the end for visibility
    if len(visibleErrorsAtEnd) > 0:
      print("\n[Errors while Processing]:")
      for err in visibleErrorsAtEnd:
        print(err)
      print("\n")

if __name__ == "__main__":
    main()

import 'package:core_authentication/util/session_manager.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'dart:io';

Future<Map<String, String>> getDeviceInfo() async {
  DeviceInfoPlugin deviceInfoPlugin = DeviceInfoPlugin();
  final PackageInfo info = await PackageInfo.fromPlatform();
  String? deviceId;
  String? osName;
  String? brand;
  String? model;
  String? osVersion;
  String? encryptedDeviceId;

  if (Platform.isAndroid) {
    AndroidDeviceInfo? androidDeviceInfo = await deviceInfoPlugin.androidInfo;
    deviceId = SessionManager.instance.deviceId ?? androidDeviceInfo.id;
    osName = "Android";
    brand = androidDeviceInfo.brand;
    model = androidDeviceInfo.model;
    osVersion = androidDeviceInfo.version.baseOS;
    encryptedDeviceId =
        SessionManager.instance.encryptedDeviceId ?? androidDeviceInfo.id;
  } else {
    IosDeviceInfo? iosDeviceInfo = await deviceInfoPlugin.iosInfo;
    deviceId =
        SessionManager.instance.deviceId ?? iosDeviceInfo.identifierForVendor;
    osName = "iOS";
    brand = "Apple";
    model = iosDeviceInfo.model;
    osVersion = iosDeviceInfo.systemVersion;
    encryptedDeviceId = SessionManager.instance.encryptedDeviceId ??
        iosDeviceInfo.identifierForVendor;
  }

  return {
    "appId": info.packageName,
    "bundleId": info.packageName,
    "deviceId": deviceId ?? "",
    "osName": osName,
    "brand": brand ?? "",
    "model": model ?? "",
    "osVersion": osVersion ?? "",
    "encryptedDeviceId": encryptedDeviceId ?? ""
  };
}

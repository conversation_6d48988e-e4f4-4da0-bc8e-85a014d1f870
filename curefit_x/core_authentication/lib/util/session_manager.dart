import 'dart:convert';
import 'package:common/network/client.dart';
import 'package:core_authentication/util/route_name.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../app_config/bloc/app_config_bloc.dart';
import '../authentication/data/model/user_model.dart';

class SessionManager {
  static final SessionManager instance = SessionManager._internal();

  SessionManager._internal();

  factory SessionManager() {
    return instance;
  }

  void Function(Map<String, String> payload)? bridgeSetToken;
  void Function(Map<String, String> payload)? bridgeSetDevConfig;
  void Function(Map<String, dynamic> payload)? bridgeSetUserInfo;
  NetworkClient? networkClient;
  SharedPreferences? prefs;
  String? deviceId;
  String? encryptedDeviceId;
  bool isTest = false;
  GlobalKey<NavigatorState>? navigatorKey;

  String? get token => prefs?.getString("ACCESS_TOKEN");

  String? get sToken => prefs?.getString("SESSION_TOKEN");

  User? get userInfo => prefs?.getString("USER_INFO") != null
      ? User.fromJson(json.decode(prefs!.getString("USER_INFO")!))
      : null;

  Future<void> init({
    required NetworkClient newNetworkClient,
    required void Function(Map<String, String> payload) newBridgeSetToken,
    required void Function(Map<String, String> payload) newBridgeSetDevConfig,
    required void Function(Map<String, dynamic> payload) newBridgeSetUserInfo,
    GlobalKey<NavigatorState>? newNavigatorKey,
    bool? isTest,
  }) async {
    networkClient = newNetworkClient;
    bridgeSetToken = newBridgeSetToken;
    bridgeSetDevConfig = newBridgeSetDevConfig;
    bridgeSetUserInfo = newBridgeSetUserInfo;
    navigatorKey = newNavigatorKey;
    this.isTest = isTest ?? false;
    prefs = await SharedPreferences.getInstance();
    String? currentToken = token;
    String? currentSessionToken = sToken;
    Future.delayed(const Duration(seconds: 2), () {
      if (currentToken != null && currentSessionToken != null) {
        networkClient?.setUserToken(currentToken);
      } else if (currentToken != null && currentSessionToken != null) {
        bridgeSetToken!({
          "at": currentToken,
          "st": currentSessionToken,
        });
      }
    });
  }

  void setDeviceId(
      {required String deviceId, required String encryptedDeviceId}) {
    this.deviceId = deviceId;
    this.encryptedDeviceId = encryptedDeviceId;
  }

  Future<bool?> checkAuthState({required BuildContext context}) async {
    final authToken = token;
    final sessionToken = sToken;
    if (authToken == null || sessionToken == null) {
      Navigator.of(context).pushNamed(Routes.loginPage);
    } else {
      BlocProvider.of<AppConfigBloc>(context)
          .add(AfterLoginConfigEvent(context));
      if (bridgeSetToken != null) {
        bridgeSetToken!({
          "at": authToken,
          "st": sessionToken,
          "initNotification": "true",
        });
        return true;
      }
    }
  }

  Future<void> setUserSession({
    User? user,
    String? authToken,
    String? sessionToken,
  }) async {
    if (authToken != null && sessionToken != null) {
      networkClient?.setUserToken(authToken);
      if (bridgeSetToken != null) {
        bridgeSetToken!({
          "at": authToken,
          "st": sessionToken,
        });
      }
      prefs = await SharedPreferences.getInstance();
      await prefs?.setString("ACCESS_TOKEN", authToken);
      await prefs?.setString("SESSION_TOKEN", sessionToken);
    }
    if (user != null) {
      setUserInfo(user: user);
    }
  }

  Future<void> setUserInfo({required User user}) async {
    await prefs?.setString("USER_INFO", jsonEncode(user.toJson()));
    if (bridgeSetUserInfo != null) {
      bridgeSetUserInfo!({"user": user.toJson()});
    }
  }

  Future<void> clearSession({bool clearRnSession = false}) async {
    networkClient?.setUserToken(null);
    await prefs?.remove("ACCESS_TOKEN");
    await prefs?.remove("SESSION_TOKEN");
    await prefs?.remove("USER_INFO");
    if (clearRnSession && bridgeSetToken != null) {
      bridgeSetToken!({
        "clearSession": "true",
      });
    }
  }

  Future<void> sendRNAppConfig({required Map<String, String> payload}) async {
    if (bridgeSetToken != null) {
      bridgeSetToken!(payload);
    }
  }
}

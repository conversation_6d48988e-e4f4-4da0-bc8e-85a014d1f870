import 'dart:io';

import 'package:advertising_id/advertising_id.dart';
import 'package:app_tracking_transparency/app_tracking_transparency.dart';
import 'package:logger/logger.dart';

class AdditionalHeadersGenerator {
  static Future<Map<String, String>> generate() async {
    final Map<String, String> additionalHeaders = {};
    try {
      if (Platform.isIOS) {
        if (await AppTrackingTransparency.trackingAuthorizationStatus ==
            TrackingStatus.authorized) {
          final uuid = await AppTrackingTransparency.getAdvertisingIdentifier();
          additionalHeaders["advertiserId"] = uuid;
        }
      } else {
        final uuid = await AdvertisingId.id();
        if (uuid != null) {
          additionalHeaders["advertiserId"] = uuid;
        }
      }
      return additionalHeaders;
    } catch (e) {
      Logger().e('Error generating additional headers: $e');
      return additionalHeaders;
    }
  }
}

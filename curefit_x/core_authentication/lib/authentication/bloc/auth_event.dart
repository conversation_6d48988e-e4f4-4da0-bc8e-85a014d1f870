part of 'auth_bloc.dart';

@immutable
abstract class AuthEvent {
  const AuthEvent() : super();
}

class ResetAuthEvent extends AuthEvent {
  const ResetAuthEvent();
}

class GoogleSignInEvent extends AuthEvent {
  const GoogleSignInEvent();
}

class FbSignInEvent extends AuthEvent {
  const FbSignInEvent();
}

class AppleSignInEvent extends AuthEvent {
  const AppleSignInEvent();
}

class PhoneNumberValidationEvent extends AuthEvent {
  final String phoneNumber;
  final bool isContinueButtonPressed;
  const PhoneNumberValidationEvent({
    required this.phoneNumber,
    this.isContinueButtonPressed = false,
  });
}

class GeneratePhoneOtpEvent extends AuthEvent {
  final String phoneNumber;
  final bool isCall;
  final bool isResend;
  const GeneratePhoneOtpEvent(
      {required this.phoneNumber, this.isCall = false, this.isResend = false});
}

class OtpValidationEvent extends AuthEvent {
  final String otp;
  const OtpValidationEvent({required this.otp});
}

class VerifyPhoneOtpEvent extends AuthEvent {
  final String otp;
  const VerifyPhoneOtpEvent({required this.otp});
}

class EmailValidationEvent extends AuthEvent {
  final String emailId;
  const EmailValidationEvent({required this.emailId});
}

class GenerateEmailOtpEvent extends AuthEvent {
  final String email;
  final bool isResend;
  const GenerateEmailOtpEvent({required this.email, this.isResend = false});
}

class VerifyEmailOtpEvent extends AuthEvent {
  final String otp;
  const VerifyEmailOtpEvent({required this.otp});
}

class LogOutEvent extends AuthEvent {
  final bool clearRnSession;
  const LogOutEvent({this.clearRnSession = false});
}

class SkipLoginEvent extends AuthEvent {
  final bool navigateToHome;
  const SkipLoginEvent({this.navigateToHome = true});
}

class NameValidationEvent extends AuthEvent {
  final String name;
  const NameValidationEvent({required this.name});
}

class SetNameEvent extends AuthEvent {
  final String name;
  const SetNameEvent({required this.name});
}

class GenerateSetEmailOtpEvent extends AuthEvent {
  final String email;
  final bool isResend;
  const GenerateSetEmailOtpEvent({required this.email, this.isResend = false});
}

class VerifySetEmailOtpEvent extends AuthEvent {
  final String otp;

  const VerifySetEmailOtpEvent({required this.otp});
}

class AuthenticatedUserEvent extends AuthEvent {}

import 'dart:io';

import 'package:common/user/user_repository.dart';
import 'package:common/blocs/userStatusV2/userStatus_v2_bloc.dart';
import 'package:common/blocs/userStatusV2/userStatus_v2_events.dart';
import 'package:core_authentication/authentication/data/repository/auth_repository.dart';
import 'package:core_authentication/util/enums/login_type_enum.dart';
import 'package:core_authentication/util/extensions/string_extension.dart';
import 'package:core_authentication/util/route_name.dart';
import 'package:core_authentication/util/session_manager.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:hydrated_bloc/hydrated_bloc.dart';
import 'package:logger/logger.dart';
import 'package:common/util/app_config.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';

import '../../util/device_info.dart';
import '../data/model/generate_otp_model.dart';
import '../data/model/user_model.dart';

part 'auth_event.dart';

part 'auth_state.dart';

class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final AuthRepository loginRepository;
  Map<String, String>? deviceInfo;
  String? phoneNumber;
  String? email;
  LoginType? loginType;
  bool navigateBackToTabView = true;

  Future<void> fetchDeviceInfo() async {
    deviceInfo = await getDeviceInfo();
  }

  void setNavigateBackToTabView(bool val) {
    navigateBackToTabView = val;
  }

  void handleAuthenticatedUserNavigation({
    required BuildContext context,
    User? user,
  }) {
    if (user != null) {
      final userBgDetailsBloc = BlocProvider.of<UserBgDetailsBloc>(context);
      userBgDetailsBloc.add(FetchData());
    }
    if (user == null) {
      if (SessionManager.instance.isTest) {
        Navigator.of(context)
            .pushNamedAndRemoveUntil(Routes.exampleHomePage, (_) => false);
        return;
      }
      Navigator.popUntil(context, ModalRoute.withName(Routes.loginPage));
      Navigator.of(context).pop();
      return;
    }
    if (!SessionManager.instance.isTest) {
      final userRepository = RepositoryProvider.of<UserRepository>(context);
      userRepository.saveUserInfo({"user": user.toJson()});
    }

    if (user.firstName == null &&
        (loginType == LoginType.EMAIL || loginType == LoginType.PHONE)) {
      Navigator.of(context).popAndPushNamed(Routes.enterNamePage);
    } else {
      if (SessionManager.instance.isTest) {
        Navigator.of(context)
            .pushNamedAndRemoveUntil(Routes.exampleHomePage, (_) => false);
        return;
      }

      if ((loginType == LoginType.SKIP ||
              loginType == LoginType.GOOGLE ||
              loginType == LoginType.FACEBOOK ||
              loginType == LoginType.APPLE) &&
          !navigateBackToTabView) {
        Navigator.of(context).pop();
        return;
      }

      Navigator.popUntil(context, ModalRoute.withName(Routes.loginPage));
      Navigator.of(context).pop();
    }
  }

  AuthBloc({required this.loginRepository}) : super(AuthInitialState()) {
    on<AuthenticatedUserEvent>((event, emit) async {
      emit(AuthenticatedUserState());
    });
    on<GoogleSignInEvent>((event, emit) async {
      try {
        loginType = LoginType.GOOGLE;
        await fetchDeviceInfo();
        GoogleSignIn googleSignIn = GoogleSignIn(
          clientId: Platform.isAndroid
              ? "************-qd1nd35n1qh81bon4qkodabif1t0g0gn.apps.googleusercontent.com"
              : "************-d3dmr7p4uhcnk62tmr3cqsidkdoeok53.apps.googleusercontent.com",
          scopes: <String>[
            'email',
          ],
        );
        bool resp = await googleSignIn.isSignedIn();
        if (resp) {
          await googleSignIn.signOut();
        }
        final GoogleSignInAccount? gUser = await googleSignIn.signIn();
        final GoogleSignInAuthentication gAuth = await gUser!.authentication;

        Logger().i('---GOOGLE AUTH---');
        Logger().i('G AUTH ID: ${gAuth.idToken}');
        Logger().i('G AUTH ACCESS TOKEN: ${gAuth.accessToken}');
        dynamic response = await loginRepository.verifyGoogleLogIn(
            gAuth.idToken, gAuth.accessToken, deviceInfo);

        if (response["type"] == "error") {
          emit(OtpVerificationFailedState("Oops! Something went wrong"));
        } else {
          final userModel = UserModel.fromJson(response);
          await SessionManager().setUserSession(
            user: userModel.user,
            authToken: userModel.session!.at!,
            sessionToken: userModel.session!.st!,
          );
          emit(GoogleAuthenticationSuccessfulState(user: userModel.user!));
        }
      } catch (e) {
        Logger().e(e.toString());
      }
    });

    on<FbSignInEvent>((event, emit) async {
      try {
        loginType = LoginType.FACEBOOK;
        await fetchDeviceInfo();
        final LoginResult loginResult = await FacebookAuth.instance.login();
        if (loginResult.status == LoginStatus.success) {
          final userId = loginResult.accessToken?.userId;
          final accessToken = loginResult.accessToken?.token;
          final expiresIn = loginResult.accessToken?.dataAccessExpirationTime;

          Logger().i('FB TOKEN: $accessToken');
          Logger().i('FB USER ID: $userId');

          final response = await loginRepository.verifyFbLogIn(userId,
              accessToken, expiresIn?.microsecondsSinceEpoch, deviceInfo);

          if (response["type"] == "error") {
            emit(OtpVerificationFailedState("Oops! Something went wrong"));
          } else {
            final userModel = UserModel.fromJson(response);
            await SessionManager().setUserSession(
              user: userModel.user,
              authToken: userModel.session!.at!,
              sessionToken: userModel.session!.st!,
            );
            emit(FacebookAuthenticationSuccessfulState(user: userModel.user!));
          }
        }
      } catch (e) {
        Logger().e(e.toString());
      }
    });

    on<AppleSignInEvent>((event, emit) async {
      try {
        loginType = LoginType.APPLE;
        await fetchDeviceInfo();
        final credential = await SignInWithApple.getAppleIDCredential(
          scopes: [
            AppleIDAuthorizationScopes.email,
            AppleIDAuthorizationScopes.fullName,
          ],
        );
        final response = await loginRepository.verifyAppleLogIn(
          credential.authorizationCode,
          credential.identityToken,
          deviceInfo,
        );
        if (response["type"] == "error") {
          emit(OtpVerificationFailedState("Oops! Something went wrong"));
        } else {
          final userModel = UserModel.fromJson(response);
          await SessionManager().setUserSession(
            user: userModel.user,
            authToken: userModel.session!.at!,
            sessionToken: userModel.session!.st!,
          );
          emit(AppleAuthenticationSuccessfulState(user: userModel.user!));
        }
      } catch (e) {
        Logger().e(e.toString());
      }
    });

    on<PhoneNumberValidationEvent>((event, emit) async {
      if (event.phoneNumber.length == 10) {
        emit(PhoneNumberValidationSuccessfulState());
      } else if (event.phoneNumber.length > 10) {
        emit(PhoneNumberVerificationFailedState(
            "Please enter a valid phone number"));
      } else if (event.phoneNumber.length != 10) {
        emit(PhoneNumberVerificationFailedState(
          event.isContinueButtonPressed
              ? "Please enter a valid phone number"
              : null,
        ));
      }
    });
    on<GeneratePhoneOtpEvent>((event, emit) async {
      await fetchDeviceInfo();
      phoneNumber = event.phoneNumber;
      try {
        loginType = LoginType.PHONE;
        final response = await loginRepository.generatePhoneOtp(
            phoneNumber: phoneNumber!,
            deviceInfo: deviceInfo!,
            isCall: event.isCall);
        final generateOtpModel = GenerateOtpModel.fromJson(response);
        if (generateOtpModel.success) {
          if (!event.isResend) {
            emit(PhoneOtpGeneratedSuccessfullyState(event.phoneNumber));
          }
        } else {
          emit(OtpGenerationFailedState("Error generating OTP"));
        }
      } catch (e) {
        Logger().e(e.toString());
        emit(OtpGenerationFailedState("Error generating OTP"));
      }
    });

    on<OtpValidationEvent>((event, emit) async {
      await fetchDeviceInfo();
      if (event.otp.length == 6) {
        emit(OtpValidationSuccessfulState());
      } else {
        emit(OtpValidationFailedState());
      }
    });

    on<VerifyPhoneOtpEvent>((event, emit) async {
      await fetchDeviceInfo();
      try {
        if (phoneNumber == null) {
          return;
        }
        final response = await loginRepository.verifyPhoneOtp(
          otp: event.otp,
          phoneNumber: phoneNumber!,
          deviceInfo: deviceInfo!,
        );
        if (response["type"] == "error") {
          emit(OtpVerificationFailedState(
              "Oops! Looks like you've entered the wrong OTP."));
        } else {
          final userModel = UserModel.fromJson(response);
          await SessionManager().setUserSession(
            user: userModel.user,
            authToken: userModel.session!.at!,
            sessionToken: userModel.session!.st!,
          );
          emit(
            OtpVerificationSuccessfulState(
              user: userModel.user!,
            ),
          );
        }
      } catch (e) {
        Logger().e(e.toString());
        emit(OtpVerificationFailedState(
            "Oops! Looks like you've entered the wrong OTP."));
      }
    });

    on<EmailValidationEvent>((event, emit) async {
      if (event.emailId.isEmail()) {
        emit(EmailValidationSuccessfulState());
      } else {
        emit(AuthInitialState());
      }
    });

    on<GenerateEmailOtpEvent>((event, emit) async {
      emit(EmailOtpGenerationInProgressState());
      await fetchDeviceInfo();
      email = event.email;
      try {
        loginType = LoginType.EMAIL;
        final response = await loginRepository.generateEmailOtp(email: email!);
        if (response) {
          if (!event.isResend) {
            emit(EmailOtpGeneratedSuccessfullyState(email!));
          }
        } else {
          emit(OtpGenerationFailedState(
              "Something went wrong! Please check the email and try again."));
        }
      } catch (e) {
        Logger().e(e.toString());
        emit(OtpGenerationFailedState("Error generating OTP"));
      }
    });

    on<VerifyEmailOtpEvent>((event, emit) async {
      await fetchDeviceInfo();
      try {
        if (email == null) {
          return;
        }
        final response = await loginRepository.verifyEmailOtp(
          otp: event.otp,
          email: email!,
          deviceInfo: deviceInfo!,
        );
        if (response["type"] == "error") {
          emit(OtpVerificationFailedState(
              "Oops! Looks like you've entered the wrong OTP."));
        } else {
          final userModel = UserModel.fromJson(response);
          SessionManager().setUserSession(
              user: userModel.user,
              authToken: userModel.session!.at!,
              sessionToken: userModel.session!.st!);
          emit(
            OtpVerificationSuccessfulState(
              user: userModel.user!,
            ),
          );
        }
      } catch (e) {
        Logger().e(e.toString());
        emit(OtpVerificationFailedState(
            "Oops! Looks like you've entered the wrong OTP."));
      }
    });

    on<SkipLoginEvent>((event, emit) async {
      await fetchDeviceInfo();
      try {
        AppConfig().setAskForLogin = false;
        loginType = LoginType.SKIP;
        if (event.navigateToHome) {
          emit(SkipLoginInitiatedState());
        }
        final response = await loginRepository.skipLogin(
          deviceInfo: deviceInfo!,
        );
        if (response["type"] == "error") {
          emit(SkipLoginFailedState());
        } else {
          final userModel = UserModel.fromJson(response);
          SessionManager().setUserSession(
            user: userModel.user,
            authToken: userModel.session!.at!,
            sessionToken: userModel.session!.st!,
          );
          if (event.navigateToHome) {
            emit(SkipLoginSuccessfulState(user: userModel.user!));
          }
        }
      } catch (e) {
        Logger().e(e.toString());
      }
    });

    on<NameValidationEvent>((event, emit) async {
      final String name = event.name;
      final regex = RegExp(r'^[a-zA-Z]+([ .][a-zA-Z]+)*$');
      if (name.isNotEmpty &&
          name.length <= 29 &&
          name.length >= 3 &&
          regex.hasMatch(name)) {
        emit(NameValidationSuccessfulState());
      } else {
        emit(AuthInitialState());
      }
    });

    on<SetNameEvent>((event, emit) async {
      await fetchDeviceInfo();
      try {
        final response = await loginRepository.setName(
          name: event.name,
        );
        if (response["type"] == "error") {
          //TODO: Handle error
        } else {
          final userModel = UserModel.fromJson(response);
          SessionManager().setUserSession(
            user: userModel.user,
            authToken: userModel.session!.at!,
            sessionToken: userModel.session!.st!,
          );
          emit(NameSetSuccessfulState(
              loginType: loginType ?? LoginType.SKIP, user: userModel.user!));
        }
      } catch (e) {
        Logger().e(e.toString());
      }
    });

    on<GenerateSetEmailOtpEvent>((event, emit) async {
      emit(EmailOtpGenerationInProgressState());
      await fetchDeviceInfo();
      email = event.email;
      try {
        final response =
            await loginRepository.generateSetEmailOtp(email: email!);
        if (response["success"]) {
          if (!event.isResend) {
            emit(EmailOtpGeneratedSuccessfullyState(email!));
          }
        } else {
          emit(OtpGenerationFailedState("Error generating OTP"));
        }
      } catch (e) {
        Logger().e(e.toString());
        emit(OtpGenerationFailedState("Error generating OTP"));
      }
    });

    on<VerifySetEmailOtpEvent>((event, emit) async {
      await fetchDeviceInfo();
      try {
        if (email == null) {
          return;
        }
        final response = await loginRepository.verifySetEmailOtp(
          otp: event.otp,
          email: email!,
        );
        if (response["type"] == "error") {
          emit(OtpVerificationFailedState(
              "Oops! Looks like you've entered the wrong OTP."));
        } else {
          emit(
            SetEmailOtpVerificationSuccessfulState(),
          );
        }
      } catch (e) {
        Logger().e(e.toString());
        emit(OtpVerificationFailedState(
            "Oops! Looks like you've entered the wrong OTP."));
      }
    });

    on<LogOutEvent>((event, emit) async {
      await fetchDeviceInfo();
      try {
        await HydratedBloc.storage.clear();
        SessionManager.instance
            .clearSession(clearRnSession: event.clearRnSession);
        emit(LogOutState());
      } catch (e) {
        Logger().e(e.toString());
      }
    });

    on<ResetAuthEvent>((event, emit) async {
      emit(AuthInitialState());
    });
  }
}

part of 'auth_bloc.dart';

@immutable
abstract class AuthState {}

class AuthInitialState extends AuthState {
  AuthInitialState();
}

//PHONE AUTH
class PhoneNumberValidationSuccessfulState extends AuthState {
  PhoneNumberValidationSuccessfulState();
}

class PhoneOtpGenerationInProgressState extends AuthState {
  PhoneOtpGenerationInProgressState();
}

class PhoneNumberVerificationFailedState extends AuthState {
  final String? errorMessage;
  PhoneNumberVerificationFailedState(this.errorMessage);
}

class PhoneOtpGeneratedSuccessfullyState extends AuthState {
  final String phoneNumber;
  PhoneOtpGeneratedSuccessfullyState(this.phoneNumber);
}

class OtpGenerationFailedState extends AuthState {
  final String error;
  OtpGenerationFailedState(this.error);
}

class OtpValidationSuccessfulState extends AuthState {
  OtpValidationSuccessfulState();
}

class OtpValidationFailedState extends AuthState {
  OtpValidationFailedState();
}

class OtpVerificationSuccessfulState extends AuthState {
  final User user;
  OtpVerificationSuccessfulState({required this.user});
}

class SetEmailOtpVerificationSuccessfulState extends AuthState {
  SetEmailOtpVerificationSuccessfulState();
}

class OtpVerificationFailedState extends AuthState {
  final String error;
  OtpVerificationFailedState(this.error);
}

//EMAIL AUTH
class EmailValidationSuccessfulState extends AuthState {
  EmailValidationSuccessfulState();
}

class EmailOtpGenerationInProgressState extends AuthState {
  EmailOtpGenerationInProgressState();
}

class EmailOtpGeneratedSuccessfullyState extends AuthState {
  final String email;
  EmailOtpGeneratedSuccessfullyState(this.email);
}

class SocialAuthSuccessfulState extends AuthState {
  final User user;
  SocialAuthSuccessfulState({required this.user});
}

//GOOGLE AUTH
class GoogleAuthenticationSuccessfulState extends SocialAuthSuccessfulState {
  GoogleAuthenticationSuccessfulState({required super.user});
}

//FACEBOOK AUTH
class FacebookAuthenticationSuccessfulState extends SocialAuthSuccessfulState {
  FacebookAuthenticationSuccessfulState({required super.user});
}

//APPLE AUTH
class AppleAuthenticationSuccessfulState extends SocialAuthSuccessfulState {
  AppleAuthenticationSuccessfulState({required super.user});
}

class SkipLoginInitiatedState extends AuthState {
  SkipLoginInitiatedState();
}

//SKIP LOGIN
class SkipLoginSuccessfulState extends AuthState {
  final User user;
  SkipLoginSuccessfulState({required this.user});
}

class SkipLoginFailedState extends AuthState {
  SkipLoginFailedState();
}

class LogOutState extends AuthState {
  LogOutState();
}

//NAME
class NameValidationSuccessfulState extends AuthState {
  NameValidationSuccessfulState();
}

class NameSetSuccessfulState extends AuthState {
  final LoginType loginType;
  final User user;
  NameSetSuccessfulState({
    required this.loginType,
    required this.user,
  });
}

class AuthenticatedUserState extends AuthState {}

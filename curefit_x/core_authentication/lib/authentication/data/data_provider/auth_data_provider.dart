import '../model/onboarding_carousel_model.dart';

class AuthDataProvider {
  static List<OnboardingCarouselModel> getOnboardingCarouselList() {
    return [
      OnboardingCarouselModel(
        duration: 5000,
        image:
            "https://cdn-media.cure.fit/video/onboardingStories/onboarding1.jpg",
        videoUrl:
            "https://cdn-media.cure.fit/video/onboardingStories/CF+-+Oct+2021+-+Onboarding+Video+-+Offline.mp4",
      ),
      OnboardingCarouselModel(
        duration: 5000,
        image:
            "https://cdn-media.cure.fit/video/onboardingStories/onboarding2.jpg",
        videoUrl:
            "https://cdn-media.cure.fit/video/onboardingStories/CF+-+Oct+2021+-+Onboarding+Video+-+Online.mp4",
      ),
      OnboardingCarouselModel(
        duration: 5000,
        image:
            "https://cdn-media.cure.fit/video/onboardingStories/onboarding3.jpg",
        videoUrl:
            "https://cdn-media.cure.fit/video/onboardingStories/Wellness+Onboard.mp4",
      ),
    ];
  }
}

class UserModel {
  User? user;
  Session? session;
  String? cityName;
  String? cityId;
  List<String>? appTabs;
  List<Vertical>? verticals;
  bool? isMoreToBeHighlighted;
  bool? isPhoneNumberUpdateRequired;
  bool? showFlutterHomeClp;

  UserModel({
    this.user,
    this.session,
    this.cityName,
    this.cityId,
    this.appTabs,
    this.verticals,
    this.isMoreToBeHighlighted,
    this.isPhoneNumberUpdateRequired,
    this.showFlutterHomeClp,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) => UserModel(
        user: json["user"] == null ? null : User.fromJson(json["user"]),
        session:
            json["session"] == null ? null : Session.fromJson(json["session"]),
        cityName: json["cityName"],
        cityId: json["cityId"],
        appTabs: json["appTabs"] == null
            ? []
            : List<String>.from(json["appTabs"]!.map((x) => x)),
        verticals: json["verticals"] == null
            ? []
            : List<Vertical>.from(
                json["verticals"]!.map((x) => Vertical.fromJson(x))),
        isMoreToBeHighlighted: json["isMoreToBeHighlighted"],
        isPhoneNumberUpdateRequired: json["isPhoneNumberUpdateRequired"],
        showFlutterHomeClp: json["showFlutterHomeCLP"],
      );

  Map<String, dynamic> toJson() => {
        "user": user?.toJson(),
        "session": session?.toJson(),
        "cityName": cityName,
        "cityId": cityId,
        "appTabs":
            appTabs == null ? [] : List<dynamic>.from(appTabs!.map((x) => x)),
        "verticals": verticals == null
            ? []
            : List<dynamic>.from(verticals!.map((x) => x.toJson())),
        "isMoreToBeHighlighted": isMoreToBeHighlighted,
        "isPhoneNumberUpdateRequired": isPhoneNumberUpdateRequired,
        "showFlutterHomeCLP": showFlutterHomeClp,
      };
}

class Session {
  String? at;
  String? st;
  Gate? gate;
  String? tlaSemusnoc;

  Session({
    this.at,
    this.st,
    this.gate,
    this.tlaSemusnoc,
  });

  factory Session.fromJson(Map<String, dynamic> json) => Session(
        at: json["at"],
        st: json["st"],
        gate: json["gate"] == null ? null : Gate.fromJson(json["gate"]),
        tlaSemusnoc: json["tlaSemusnoc"],
      );

  Map<String, dynamic> toJson() => {
        "at": at,
        "st": st,
        "gate": gate?.toJson(),
        "tlaSemusnoc": tlaSemusnoc,
      };
}

class Gate {
  String? type;

  Gate({
    this.type,
  });

  factory Gate.fromJson(Map<String, dynamic> json) => Gate(
        type: json["type"],
      );

  Map<String, dynamic> toJson() => {
        "type": type,
      };
}

class User {
  final bool? showStoreWebviewForApp;
  final bool? showEquipmentTabWebview;
  final String? profileImageUrl;
  final String? firstName;
  final String? lastName;
  final bool? isNotLoggedIn;
  final String? phoneNumber;
  final String? country;
  final List<String>? appTabs; // Assuming appTabs is a list of strings
  final bool? isInternalUser;
  final bool? isFlutterAllCentersPageEnabled;
  final bool? isPostBookingPageEnabled;
  final bool? enableCardHeroAnimation;
  final bool? enableNoShowAutomatedException;
  final bool? apiCacheEnabled;
  final Map<String, dynamic>? apiCacheConfig; // Assuming it's a map
  final String? genderId;
  final String? birthday;
  final String? userId;
  final bool? parqNeeded;
  final bool? createProfilePending;
  final bool? activeMembershipPresent;
  final bool? hideDiscoverTab;
  final bool isActivityStreakEnabled;
  final bool isActivityStreakEnabledV2;
  final bool resetActivityStreakAssets;
  final String? activityStreakAssetsUrl;
  final bool? isTransformCombinedTabEnabled;
  final bool? isFirstTimeTransformVisit;
  final bool isV2universalGymCheckin;

  User({
    this.showStoreWebviewForApp,
    this.showEquipmentTabWebview,
    this.profileImageUrl,
    this.firstName,
    this.lastName,
    this.isNotLoggedIn,
    this.phoneNumber,
    this.country,
    this.appTabs,
    this.isInternalUser,
    this.isFlutterAllCentersPageEnabled,
    this.isPostBookingPageEnabled,
    this.enableCardHeroAnimation,
    this.enableNoShowAutomatedException,
    this.apiCacheEnabled,
    this.apiCacheConfig,
    this.genderId,
    this.birthday,
    this.userId,
    this.parqNeeded,
    this.createProfilePending,
    this.activeMembershipPresent,
    this.hideDiscoverTab,
    this.isActivityStreakEnabled = false,
    this.isActivityStreakEnabledV2 = false,
    this.resetActivityStreakAssets = false,
    this.activityStreakAssetsUrl,
    this.isTransformCombinedTabEnabled,
    this.isFirstTimeTransformVisit,
    this.isV2universalGymCheckin = false,
  });

  factory User.fromJson(
    Map<String, dynamic> json,
  ) {
    return User(
      profileImageUrl: json['profilePictureUrl'],
      firstName: json['firstName'],
      lastName: json['lastName'],
      isNotLoggedIn: json['isNotLoggedIn'],
      phoneNumber: json['phoneNumber'],
      country: json['country'],
      appTabs: json['appTabs'] != null
          ? List<String>.from(json['appTabs'])
          : null, // Adjust based on your data structure
      isInternalUser: json['isInternalUser'],
      isFlutterAllCentersPageEnabled: json['isFlutterAllCentersPageEnabled'],
      isPostBookingPageEnabled: json['isPostBookingPageEnabled'],
      enableCardHeroAnimation: json['enableCardHeroAnimation'],
      enableNoShowAutomatedException: json['enableNoShowAutomatedException'],
      apiCacheEnabled: json['apiCacheEnabled'],
      apiCacheConfig: json['apiCacheConfig'],
      genderId: json['gender'],
      birthday: json['birthday'],
      userId: json['userId'],
      parqNeeded: json['parqNeeded'],
      createProfilePending: json['createProfilePending'],
      activeMembershipPresent: json['activeMembershipPresent'],
      hideDiscoverTab: json['hideDiscoverTab'],
      isActivityStreakEnabled: json['isActivityStreakEnabled'] ?? false,
      isActivityStreakEnabledV2: json['isActivityStreakEnabledV2'] ?? false,
      resetActivityStreakAssets: json['resetActivityStreakAssets'] ?? false,
      activityStreakAssetsUrl: json['activityStreakAssetsUrl'],
      isTransformCombinedTabEnabled: json['isTransformCombinedTabEnabled'],
      isFirstTimeTransformVisit: json['isFirstTimeTransformVisit'],
      isV2universalGymCheckin: json['isV2universalGymCheckin'] ?? false,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      "enableStoreWebviewForApp": showStoreWebviewForApp,
      "enableEquipmentTabWebviewForApp": showEquipmentTabWebview,
      "profilePictureUrl": profileImageUrl,
      "firstName": firstName,
      "lastName": lastName,
      "isNotLoggedIn": isNotLoggedIn,
      "phoneNumber": phoneNumber,
      "country": country,
      "appTabs": appTabs,
      "isInternalUser": isInternalUser,
      "isFlutterAllCentersPageEnabled": isFlutterAllCentersPageEnabled,
      "isPostBookingPageEnabled": isPostBookingPageEnabled,
      "enableCardHeroAnimation": enableCardHeroAnimation,
      "enableNoShowAutomatedException": enableNoShowAutomatedException,
      "apiCacheEnabled": apiCacheEnabled,
      "apiCacheConfig": apiCacheConfig,
      "gender": genderId,
      "birthday": birthday,
      "userId": userId,
      "parqNeeded": parqNeeded,
      "createProfilePending": createProfilePending,
      "activeMembershipPresent": activeMembershipPresent,
      "hideDiscoverTab": hideDiscoverTab,
      "isActivityStreakEnabled": isActivityStreakEnabled,
      "isActivityStreakEnabledV2": isActivityStreakEnabledV2,
      "resetActivityStreakAssets": resetActivityStreakAssets,
      "activityStreakAssetsUrl": activityStreakAssetsUrl,
      "isTransformCombinedTabEnabled": isTransformCombinedTabEnabled,
      "isFirstTimeTransformVisit": isFirstTimeTransformVisit,
      "isV2universalGymCheckin": isV2universalGymCheckin,
    };
  }
}

// class User {
//   String? userId;
//   String? email;
//   dynamic countryCallingCode;
//   String? firstName;
//   dynamic workEmail;
//   bool? isInternalUser;
//   int? dotVersion;
//   bool? isNotLoggedIn;
//   bool? isNewUser;
//   int? profileCompletion;
//   bool? isFbLinked;
//   bool? isGoogleLinked;
//   bool? isPhoneChurned;
//
//   User({
//     this.userId,
//     this.email,
//     this.countryCallingCode,
//     this.firstName,
//     this.workEmail,
//     this.isInternalUser,
//     this.dotVersion,
//     this.isNotLoggedIn,
//     this.isNewUser,
//     this.profileCompletion,
//     this.isFbLinked,
//     this.isGoogleLinked,
//     this.isPhoneChurned,
//   });
//
//   factory User.fromJson(Map<String, dynamic> json) => User(
//         userId: json["userId"],
//         email: json["email"],
//         countryCallingCode: json["countryCallingCode"],
//         firstName: json["firstName"],
//         workEmail: json["workEmail"],
//         isInternalUser: json["isInternalUser"],
//         dotVersion: json["dotVersion"],
//         isNotLoggedIn: json["isNotLoggedIn"],
//         isNewUser: json["isNewUser"],
//         profileCompletion: json["profileCompletion"],
//         isFbLinked: json["isFBLinked"],
//         isGoogleLinked: json["isGoogleLinked"],
//         isPhoneChurned: json["isPhoneChurned"],
//       );
//
//   Map<String, dynamic> toJson() => {
//         "userId": userId,
//         "email": email,
//         "countryCallingCode": countryCallingCode,
//         "firstName": firstName,
//         "workEmail": workEmail,
//         "isInternalUser": isInternalUser,
//         "dotVersion": dotVersion,
//         "isNotLoggedIn": isNotLoggedIn,
//         "isNewUser": isNewUser,
//         "profileCompletion": profileCompletion,
//         "isFBLinked": isFbLinked,
//         "isGoogleLinked": isGoogleLinked,
//         "isPhoneChurned": isPhoneChurned,
//       };
// }

class Vertical {
  String? verticalType;
  String? displayText;
  String? pageId;
  bool? isEditable;
  bool? isHighlighted;

  Vertical({
    this.verticalType,
    this.displayText,
    this.pageId,
    this.isEditable,
    this.isHighlighted,
  });

  factory Vertical.fromJson(Map<String, dynamic> json) => Vertical(
        verticalType: json["verticalType"],
        displayText: json["displayText"],
        pageId: json["pageId"],
        isEditable: json["isEditable"],
        isHighlighted: json["isHighlighted"],
      );

  Map<String, dynamic> toJson() => {
        "verticalType": verticalType,
        "displayText": displayText,
        "pageId": pageId,
        "isEditable": isEditable,
        "isHighlighted": isHighlighted,
      };
}

import 'package:enum_to_string/enum_to_string.dart';
import 'package:common/analytics/analytics_repository.dart';

enum LoginEventTypes {
  page_view,
  login_button_click,
  app_login,
  otp_initiated,
  otp_confirmed,
  user_input_success
}

typedef void SendEventFunction({
  required String eventName,
  required Map<String, dynamic> eventInfo,
  required InspectorTab inspectorTab,
});

class AuthAnalyticsRepository {
  final SendEventFunction sendEvent;

  AuthAnalyticsRepository({required this.sendEvent});

  void logLoginPageViewEvent(
      {required String pageId,
      required Map<String, dynamic> eventInfo,
      String? pageFrom,
      String? pageName,
      String? pageType}) {
    Map<String, dynamic> extraParams = {};
    if (pageFrom != null) {
      extraParams['pageFrom'] = pageFrom;
    }
    String route = pageId.replaceAll("/", "");
    sendEvent(
      eventName: EnumToString.convertToString(LoginEventTypes.page_view),
      eventInfo: {
        "pageType": pageType,
        "pageName": pageName ?? route,
        "dateTime": DateTime.now().millisecondsSinceEpoch.toString(),
        "pageId": route,
        "eventType": "page_view",
        ...extraParams,
        ...eventInfo,
      },
      inspectorTab: InspectorTab.ANALYTICS,
    );
  }

  void logLoginButtonClickEvent({
    required Map<String, dynamic> extraInfo,
  }) {
    sendEvent(
      eventName:
          EnumToString.convertToString(LoginEventTypes.login_button_click),
      eventInfo: {
        "dateTime": DateTime.now().millisecondsSinceEpoch.toString(),
        "eventType": "login_button_click",
        ...extraInfo,
      },
      inspectorTab: InspectorTab.ANALYTICS,
    );
  }

  void logAppLoginEvent({
    required Map<String, dynamic> extraInfo,
  }) {
    sendEvent(
      eventName: EnumToString.convertToString(LoginEventTypes.app_login),
      eventInfo: {
        "dateTime": DateTime.now().millisecondsSinceEpoch.toString(),
        "eventType": "app_login",
        ...extraInfo,
      },
      inspectorTab: InspectorTab.ANALYTICS,
    );
  }

  void logOTPConfirmedEvent({
    required String otpSource,
  }) {
    sendEvent(
      eventName: EnumToString.convertToString(LoginEventTypes.otp_confirmed),
      eventInfo: {
        "dateTime": DateTime.now().millisecondsSinceEpoch.toString(),
        "eventType": "otp_confirmed",
        "otpSource": otpSource,
      },
      inspectorTab: InspectorTab.ANALYTICS,
    );
  }

  void logOTPInitiatedEvent({
    required String medium,
    String? phone,
    String? countryCallingCode,
    String? email,
  }) {
    sendEvent(
      eventName: EnumToString.convertToString(LoginEventTypes.otp_confirmed),
      eventInfo: {
        "dateTime": DateTime.now().millisecondsSinceEpoch.toString(),
        "eventType": "otp_initiated",
        if (phone != null) "phone": phone,
        "medium": medium,
        if (countryCallingCode != null)
          "countryCallingCode": countryCallingCode,
        if (email != null) "email": email
      },
      inspectorTab: InspectorTab.ANALYTICS,
    );
  }

  void logUserInputSuccessEvent({
    required String input,
  }) {
    sendEvent(
      eventName:
          EnumToString.convertToString(LoginEventTypes.user_input_success),
      eventInfo: {
        "input": input,
      },
      inspectorTab: InspectorTab.ANALYTICS,
    );
  }
}

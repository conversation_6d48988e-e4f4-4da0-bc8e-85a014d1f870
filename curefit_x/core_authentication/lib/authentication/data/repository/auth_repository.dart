import 'package:common/network/client.dart';

import '../../../util/additional_headers_generator.dart';

class AuthRepository {
  final NetworkClient networkClient;

  AuthRepository({required this.networkClient});

  Future<dynamic> generatePhoneOtp({
    required String phoneNumber,
    required Map<String, String> deviceInfo,
    required bool isCall,
  }) async {
    Map<String, dynamic> body = {
      "phone": phoneNumber,
      "medium": isCall ? "call" : "sms",
      "countryCallingCode": "+91",
      "deviceInfo": deviceInfo
    };
    final additionalHeaders = await AdditionalHeadersGenerator.generate();
    final response = await networkClient.post(
      "/auth/loginPhoneSendOtp",
      body,
      null,
      additionalHeaders,
    );
    return response;
  }

  Future<dynamic> verifyPhoneOtp(
      {required String otp,
      required String phoneNumber,
      required Map<String, String> deviceInfo}) async {
    Map<String, dynamic> body = {
      "phone": phoneNumber,
      "otp": otp,
      "countryCallingCode": "+91",
      "captchaResponse": null,
      "deviceInfo": deviceInfo
    };
    try {
      final additionalHeaders = await AdditionalHeadersGenerator.generate();

      final response = await networkClient.post(
        "/auth/loginPhoneVerifyOtp",
        body,
        null,
        additionalHeaders,
      );
      return response;
    } catch (error) {
      return {"type": "error"};
    }
  }

  Future<dynamic> verifyGoogleLogIn(String? idToken, String? accessToken,
      Map<String, String>? deviceInfo) async {
    Map<String, dynamic> body = {
      "idToken": idToken,
      "accessToken": accessToken,
      "deviceInfo": deviceInfo
    };
    final additionalHeaders = await AdditionalHeadersGenerator.generate();

    final response = await networkClient.post(
      "/auth/googleLogIn",
      body,
      null,
      additionalHeaders,
    );
    return response;
  }

  Future<dynamic> verifyFbLogIn(String? userId, String? accessToken,
      int? expiresIn, Map<String, String>? deviceInfo) async {
    Map<String, dynamic> body = {
      "userId": userId,
      "accessToken": accessToken,
      "expiresIn": expiresIn,
      "deviceInfo": deviceInfo,
    };
    final additionalHeaders = await AdditionalHeadersGenerator.generate();

    final response = await networkClient.post(
      "/auth/fbLogin",
      body,
      null,
      additionalHeaders,
    );
    return response;
  }

  Future<dynamic> verifyAppleLogIn(String? authorizationCode,
      String? identityToken, Map<String, String>? deviceInfo) async {
    Map<String, dynamic> body = {
      "authorizationCode": authorizationCode,
      "idToken": identityToken,
      "deviceInfo": deviceInfo,
    };
    final additionalHeaders = await AdditionalHeadersGenerator.generate();

    final response = await networkClient.post(
      "/auth/appleLogin",
      body,
      null,
      additionalHeaders,
    );
    return response;
  }

  Future<dynamic> generateEmailOtp({
    required String email,
  }) async {
    Map<String, dynamic> body = {
      "email": email,
    };
    final additionalHeaders = await AdditionalHeadersGenerator.generate();

    final response = await networkClient.post(
      '/auth/email/otprequest',
      body,
      null,
      additionalHeaders,
    );
    return response;
  }

  Future<dynamic> verifyEmailOtp(
      {required String email,
      required String otp,
      required Map<String, dynamic> deviceInfo}) async {
    Map<String, dynamic> body = {
      "email": email,
      "otp": otp,
      "deviceInfo": deviceInfo
    };
    final additionalHeaders = await AdditionalHeadersGenerator.generate();

    final response = await networkClient.post(
      '/auth/email/otplogin',
      body,
      null,
      additionalHeaders,
    );
    return response;
  }

  Future<dynamic> skipLogin({required Map<String, String>? deviceInfo}) async {
    try {
      final additionalHeaders = await AdditionalHeadersGenerator.generate();

      final response = await networkClient.post(
        "/auth/deviceLogin",
        {
          "deviceInfo": deviceInfo,
        },
        null,
        additionalHeaders,
      );
      return response;
    } catch (error) {
      return {"type": "error"};
    }
  }

  Future<dynamic> setName({required String name}) async {
    try {
      final additionalHeaders = await AdditionalHeadersGenerator.generate();

      final response = await networkClient.post(
        "/user/setName",
        {
          "name": name,
        },
        null,
        additionalHeaders,
      );
      return response;
    } catch (error) {
      return {"type": "error"};
    }
  }

  Future<dynamic> generateSetEmailOtp({
    required String email,
  }) async {
    Map<String, dynamic> body = {
      "email": email,
      "emailType": "PRIMARY",
    };
    final additionalHeaders = await AdditionalHeadersGenerator.generate();

    final response = await networkClient.post(
      "/user/setEmailSendOtp",
      body,
      null,
      additionalHeaders,
    );
    return response;
  }

  Future<dynamic> verifySetEmailOtp({
    required String email,
    required String otp,
  }) async {
    Map<String, dynamic> body = {
      "email": email,
      "emailType": "PRIMARY",
      "otp": otp,
    };
    final additionalHeaders = await AdditionalHeadersGenerator.generate();

    final response = await networkClient.post(
      '/user/verifyEmailThroughOtp',
      body,
      null,
      additionalHeaders,
    );
    return response;
  }
}

import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../util/route_name.dart';
import '../../bloc/auth_bloc.dart';

class SetEmailView extends StatefulWidget {
  const SetEmailView({super.key});

  @override
  State<SetEmailView> createState() => _SetEmailViewState();
}

class _SetEmailViewState extends State<SetEmailView> {
  final TextEditingController _emailTextEditingController =
      TextEditingController();

  @override
  Widget build(BuildContext context) {
    final authBloc = BlocProvider.of<AuthBloc>(context);

    return BlocConsumer<AuthBloc, AuthState>(
      listener: (context, state) {
        if (state is EmailOtpGeneratedSuccessfullyState) {
          _emailTextEditingController.clear();
          Navigator.of(context).pushNamed(Routes.setEmailOtpPage);
        }
      },
      builder: (context, state) {
        return Scaffold(
          extendBodyBehindAppBar: true,
          appBar: AppBar(
            toolbarHeight: Spacings.x20,
            titleSpacing: 0,
            elevation: 0,
            backgroundColor: Colors.transparent,
            leading: Semantics(
              label: "BACK_BTN",
              child: IconButton(
                icon: const Icon(
                  Icons.arrow_back_ios_new_rounded,
                  color: Colors.white,
                ),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
            ),
            actions: [
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Semantics(
                    label: "SKIP_SET_EMAIL_BTN",
                    child: TextButton(
                      style: TextButton.styleFrom(
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(5),
                        ),
                        backgroundColor: Colors.white.withOpacity(0.2),
                      ),
                      child: const Text(
                        'Skip',
                        style: TextStyle(color: Colors.white),
                      ),
                      onPressed: () {
                        authBloc.handleAuthenticatedUserNavigation(
                            context: context);
                      },
                    ),
                  ),
                  const SizedBox(
                    width: Spacings.x4,
                  )
                ],
              ),
            ],
          ),
          body: Stack(
            children: [
              Aurora(size: MediaQuery.of(context).size),
              Padding(
                padding: const EdgeInsets.all(Spacings.x4),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(
                      height: Spacings.x24,
                    ),
                    Text(
                      "One last step,\nWhat's your email?",
                      textAlign: TextAlign.left,
                      style: AuroraTheme.of(context).textStyle(
                        TypescaleValues.H10,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(
                      height: Spacings.x5,
                    ),
                    if (state is OtpGenerationFailedState)
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Center(
                          child: Text(
                            textAlign: TextAlign.center,
                            state.error,
                            style: const TextStyle(color: Colors.red),
                          ),
                        ),
                      ),
                    Semantics(
                      label: "GENERATE_SET_EMAIL_OTP_TF",
                      child: TextField(
                        style: AuroraTheme.of(context)
                            .textStyle(TypescaleValues.P1),
                        keyboardType: TextInputType.emailAddress,
                        autofocus: true,
                        decoration: const InputDecoration(
                          contentPadding:
                              EdgeInsets.symmetric(horizontal: Spacings.x4),
                          fillColor: Color.fromARGB(26, 255, 255, 255),
                          filled: true,
                          hintText: 'Enter your email',
                          prefixIconColor: Colors.white,
                          hintStyle: TextStyle(
                            color: Colors.grey,
                          ),
                          border: OutlineInputBorder(
                            borderRadius:
                                BorderRadius.all(Radius.circular(8.0)),
                            borderSide: BorderSide(color: Colors.transparent),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius:
                                BorderRadius.all(Radius.circular(8.0)),
                            borderSide: BorderSide(color: Colors.transparent),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius:
                                BorderRadius.all(Radius.circular(8.0)),
                            borderSide: BorderSide(color: Colors.transparent),
                          ),
                        ),
                        controller: _emailTextEditingController,
                        onChanged: (value) {
                          authBloc.add(EmailValidationEvent(
                              emailId: _emailTextEditingController.text));
                        },
                        enabled: true,
                      ),
                    ),
                  ],
                ),
              ),
              Positioned(
                left: 0,
                right: 0,
                bottom: scale(context, 20),
                child: Padding(
                  padding: const EdgeInsets.only(
                      left: Spacings.x4,
                      right: Spacings.x4,
                      bottom: Spacings.x1),
                  child: BlocBuilder<AuthBloc, AuthState>(
                    builder: (context, state) {
                      return PrimaryButton(
                        semanticLabel: "GENERATE_SET_EMAIL_OTP_BTN",
                        enabled: state is EmailValidationSuccessfulState,
                        () {
                          authBloc.add(
                            GenerateSetEmailOtpEvent(
                              email: _emailTextEditingController.text,
                            ),
                          );
                        },
                        "GET STARTED",
                      );
                    },
                  ),
                ),
              )
            ],
          ),
        );
      },
    );
  }
}

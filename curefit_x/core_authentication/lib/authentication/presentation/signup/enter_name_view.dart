import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:core_authentication/util/enums/login_type_enum.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:common/analytics/analytics_repository.dart';

import '../../../util/route_name.dart';
import '../../bloc/auth_bloc.dart';
import '../../data/repository/auth_analytics_repository.dart';

class EnterNameView extends StatefulWidget {
  const EnterNameView({super.key});

  @override
  State<EnterNameView> createState() => _EnterNameViewState();
}

class _EnterNameViewState extends State<EnterNameView> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      RepositoryProvider.of<AnalyticsRepository>(context).logPageViewEvent(
        pageType: 'updatename',
        pageName: 'Update Name Page',
        pageId: 'updatename',
        eventInfo: {},
      );
    });
  }

  final TextEditingController _nameTextEditingController =
      TextEditingController();

  @override
  Widget build(BuildContext context) {
    final authBloc = BlocProvider.of<AuthBloc>(context);

    return BlocConsumer<AuthBloc, AuthState>(
      listener: (context, state) {
        if (state is NameSetSuccessfulState) {
          RepositoryProvider.of<AuthAnalyticsRepository>(context)
              .logUserInputSuccessEvent(
            input: "name",
          );
          if (state.loginType == LoginType.PHONE) {
            Navigator.of(context).pushNamed(Routes.setEmailPage).then((_) {
              authBloc.add(
                  NameValidationEvent(name: _nameTextEditingController.text));
            });
          } else {
            authBloc.handleAuthenticatedUserNavigation(
              context: context,
              user: state.user,
            );
          }
        }
      },
      builder: (context, state) {
        return PopScope(
          canPop: false,
          child: Scaffold(
            extendBodyBehindAppBar: true,
            resizeToAvoidBottomInset: true,
            body: Stack(
              children: [
                Aurora(size: MediaQuery.of(context).size),
                Padding(
                  padding: const EdgeInsets.all(Spacings.x4),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(
                        height: Spacings.x23,
                      ),
                      Text(
                        "Before we start, what do we call you?",
                        style: AuroraTheme.of(context).textStyle(
                          TypescaleValues.H10,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(
                        height: Spacings.x5,
                      ),
                      Semantics(
                        label: "NAME_TF",
                        child: TextField(
                          style: AuroraTheme.of(context)
                              .textStyle(TypescaleValues.P1),
                          keyboardType: TextInputType.name,
                          autofocus: true,
                          decoration: const InputDecoration(
                            contentPadding:
                                EdgeInsets.symmetric(horizontal: Spacings.x4),
                            fillColor: Color.fromARGB(26, 255, 255, 255),
                            filled: true,
                            hintText: 'Name',
                            prefixIconColor: Colors.white,
                            hintStyle: TextStyle(
                              color: Colors.grey,
                            ),
                            border: OutlineInputBorder(
                              borderRadius:
                                  BorderRadius.all(Radius.circular(8.0)),
                              borderSide: BorderSide(color: Colors.transparent),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius:
                                  BorderRadius.all(Radius.circular(8.0)),
                              borderSide: BorderSide(color: Colors.transparent),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius:
                                  BorderRadius.all(Radius.circular(8.0)),
                              borderSide: BorderSide(color: Colors.transparent),
                            ),
                          ),
                          controller: _nameTextEditingController,
                          onChanged: (value) {
                            authBloc.add(
                              NameValidationEvent(
                                  name: _nameTextEditingController.text),
                            );
                          },
                          enabled: true,
                        ),
                      ),
                    ],
                  ),
                ),
                Positioned(
                  left: 0,
                  right: 0,
                  bottom: scale(context, 20),
                  child: Padding(
                    padding: const EdgeInsets.only(
                        left: Spacings.x4,
                        right: Spacings.x4,
                        bottom: Spacings.x1),
                    child: BlocBuilder<AuthBloc, AuthState>(
                      builder: (context, state) {
                        return PrimaryButton(
                          semanticLabel: "SAVE_NAME_BTN",
                          enabled: state is NameValidationSuccessfulState,
                          () {
                            authBloc.add(SetNameEvent(
                                name: _nameTextEditingController.text));
                          },
                          "CONTINUE",
                        );
                      },
                    ),
                  ),
                )
              ],
            ),
          ),
        );
      },
    );
  }
}

import 'package:common/ui/aurora.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:core_authentication/app_config/bloc/app_config_bloc.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class ExampleHomeView extends StatefulWidget {
  const ExampleHomeView({super.key});

  @override
  State<ExampleHomeView> createState() => _ExampleHomeViewState();
}

class _ExampleHomeViewState extends State<ExampleHomeView> {
  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      BlocProvider.of<AppConfigBloc>(context)
          .add(AfterLoginConfigEvent(context));
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Aurora(size: MediaQuery.of(context).size),
          Center(
            child: Text(
              "Logged In Successfully",
              style: AuroraTheme.of(context).textStyle(
                TypescaleValues.H2,
                color: Colors.white60,
              ),
            ),
          )
        ],
      ),
    );
  }
}

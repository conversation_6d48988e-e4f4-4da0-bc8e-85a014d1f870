import 'dart:async';
import 'dart:ui';

import 'package:common/constants/action_constants.dart';
import 'package:common/ui/atoms/input_fields/otp_field.dart';
import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/atoms/twin_button.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/organisms/titlebar/classic_title_bar.dart';
import 'package:common/ui/theme/Colors.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:flutter/material.dart';
import 'package:common/action/action_handler.dart' as action_handler;
import 'package:enum_to_string/enum_to_string.dart';

class OTPScreen extends StatefulWidget {
  final void Function(String otp)? onVerificationConfirmed;
  final String title;
  final String? alertDescription;
  final bool showConfirmationAlert;
  final String? errorMessage;
  final void Function(Function()? onCompletion)? onVerificationStarted;
  final void Function(Function()? onCompletion)? onResendVerificationStarted;

  const OTPScreen(
      {Key? key,
      required this.title,
      this.alertDescription,
      this.errorMessage,
      this.showConfirmationAlert = true,
      this.onResendVerificationStarted,
      required this.onVerificationConfirmed,
      this.onVerificationStarted})
      : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return _OTPScreenState();
  }
}

class _OTPScreenState extends State<OTPScreen> {
  Timer? _resendTimer;
  late TextEditingController textEditingController;

  int duration = 0;

  void startTimer() {
    if (_resendTimer != null && _resendTimer!.isActive) {
      _resendTimer?.cancel();
    }
    duration = 30;
    _resendTimer = Timer.periodic(const Duration(seconds: 1), _timerCallback);
  }

  void _timerCallback(Timer timer) {
    if (mounted) {
      if (duration <= 0) {
        timer.cancel();
        return;
      }
      setState(() {
        duration -= 1;
      });
    }
  }

  @override
  void dispose() {
    super.dispose();
    if (_resendTimer != null && _resendTimer!.isActive) _resendTimer?.cancel();
  }

  @override
  void initState() {
    super.initState();
    textEditingController = TextEditingController();
    if (widget.showConfirmationAlert) {
      WidgetsBinding.instance.addPostFrameCallback(
        (timeStamp) {
          showDialog(
            barrierDismissible: false,
            context: context,
            builder: (context) {
              return BackdropFilter(
                filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
                child: AlertDialog(
                  title: Text(
                    widget.title,
                    style:
                        AuroraTheme.of(context).textStyle(TypescaleValues.P1),
                  ),
                  content: Text(
                    widget.alertDescription ?? "",
                    style:
                        AuroraTheme.of(context).textStyle(TypescaleValues.P1),
                  ),
                  backgroundColor: Colors.black,
                  actions: [
                    TwinButton(
                        horizontal: true,
                        data: [
                          action_handler.Action(
                              title: "CANCEL",
                              variant: EnumToString.convertToString(
                                  ActionVariant.secondarySmall),
                              type: ActionTypes.EMPTY_ACTION),
                          action_handler.Action(
                              title: "CONFIRM",
                              variant: EnumToString.convertToString(
                                  ActionVariant.primarySmall),
                              type: ActionTypes.EMPTY_ACTION)
                        ],
                        onPress: (action) {
                          Navigator.pop(context);
                          if (action.title == "CONFIRM") {
                            if (widget.onVerificationStarted != null) {
                              widget.onVerificationStarted!(onCompletion);
                            }
                          } else {
                            Navigator.pop(context);
                          }
                        })
                  ],
                ),
              );
            },
          );
        },
      );
    } else {
      if (widget.onVerificationStarted != null) {
        widget.onVerificationStarted!(onCompletion);
      }
    }
  }

  onCompletion() {
    startTimer();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      backgroundColor: Colors.transparent,
      body: Stack(
        children: [
          Aurora(size: MediaQuery.of(context).size),
          Center(
              child: Padding(
                  padding: const EdgeInsets.all(Spacings.x4),
                  child: Column(mainAxisSize: MainAxisSize.min, children: [
                    OTPField(
                      textEditingController: textEditingController,
                      onChanged: (value) {},
                      onCompleted: (value) {},
                      placeholderText: "Enter OTP Here",
                      hasError: false,
                    ),
                    const SizedBox(
                      height: Spacings.x4,
                    ),
                    PrimaryButton(() {
                      if (widget.onVerificationConfirmed != null) {
                        widget.onVerificationConfirmed!(
                            textEditingController.value.text);
                      }
                    }, "VERIFY"),
                    const SizedBox(
                      height: Spacings.x4,
                    ),
                    Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Text(
                          "Didnt receive the OTP?",
                          style: AuroraTheme.of(context)
                              .textStyle(TypescaleValues.P1),
                        ),
                        const SizedBox(
                          height: Spacings.x2,
                        ),
                        GestureDetector(
                          onTap: () {
                            if (widget.onResendVerificationStarted != null) {
                              widget.onResendVerificationStarted!(onCompletion);
                            }
                          },
                          child: duration > 0
                              ? Text(
                                  ' Resend OTP in $duration seconds',
                                  style: AuroraTheme.of(context).textStyle(
                                      TypescaleValues.P1,
                                      color: ColorPalette.cureFitPink),
                                )
                              : Text(
                                  "RESEND",
                                  style: AuroraTheme.of(context)
                                      .textStyle(TypescaleValues.P1),
                                ),
                        )
                      ],
                    )
                  ]))),
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            child: ClassicTitleBar(
              context: context,
              titleText: "Verification",
              //height: widget.widgetData.isEmpty ? 0 : null,
            ),
          ),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';

import '../../../util/constants.dart';

class SocialIconButton extends StatelessWidget {
  final Function()? onPressed;
  final String icon;
  final String semanticLabel;
  const SocialIconButton({
    super.key,
    required this.onPressed,
    required this.icon,
    required this.semanticLabel,
  });

  @override
  Widget build(BuildContext context) {
    return Semantics(
      label: semanticLabel,
      child: Container(
        width: 50,
        height: 50,
        decoration: BoxDecoration(
            color: Colors.white.withOpacity(0.1),
            borderRadius: BorderRadius.circular(5)),
        child: IconButton(
          onPressed: onPressed,
          icon: Image.asset(
            icon,
            package: Constants.packageName,
            width: 25,
          ),
        ),
      ),
    );
  }
}

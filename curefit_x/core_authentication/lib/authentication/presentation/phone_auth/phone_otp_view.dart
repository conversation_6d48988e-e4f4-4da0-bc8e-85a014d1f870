import 'dart:async';

import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/constants/action_constants.dart';
import 'package:common/ui/atoms/input_fields/otp_field.dart';
import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/toast.dart';
import 'package:common/user/user_repository.dart';
import 'package:common/util/action_util.dart';
import 'package:core_authentication/util/extensions/int_time_extension.dart';
import 'package:core_authentication/util/image_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:common/action/action_handler.dart' as ActionHandler;
import 'package:logger/logger.dart';
import 'package:sms_autofill/sms_autofill.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../util/constants.dart';
import '../../bloc/auth_bloc.dart';
import '../../data/repository/auth_analytics_repository.dart';

class PhoneOtpView extends StatefulWidget {
  const PhoneOtpView({
    super.key,
  });

  @override
  State<PhoneOtpView> createState() => _PhoneOtpViewState();
}

class _PhoneOtpViewState extends State<PhoneOtpView> with CodeAutoFill {
  Timer? resendTimer;
  final TextEditingController _otpTextEditingController =
      TextEditingController();

  int duration = 0;

  @override
  void codeUpdated() {
    if (code != null) {
      _otpTextEditingController.text = code!;
    }
  }

  void startTimer() {
    if (resendTimer != null && resendTimer!.isActive) {
      resendTimer?.cancel();
    }
    setState(() {
      duration = 30;
    });
    resendTimer = Timer.periodic(const Duration(seconds: 1), timerCallback);
  }

  void timerCallback(Timer timer) {
    if (mounted) {
      if (duration <= 0) {
        timer.cancel();
        return;
      }
      setState(() {
        duration -= 1;
      });
    }
  }

  onCompletion() {
    startTimer();
  }

  @override
  void dispose() {
    super.dispose();
    cancel();
    if (resendTimer != null && resendTimer!.isActive) resendTimer?.cancel();
  }

  @override
  void initState() {
    super.initState();
    listenForCode();
    startTimer();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      RepositoryProvider.of<AuthAnalyticsRepository>(context)
          .logLoginPageViewEvent(
        pageType: 'otpverifyview',
        pageName: 'OTP Verify Page',
        pageId: 'otpverifyview',
        eventInfo: {},
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    AuthBloc authBloc = BlocProvider.of<AuthBloc>(context);

    return Scaffold(
      body: BlocConsumer<AuthBloc, AuthState>(
        listener: (context, state) {
          if (state is OtpVerificationSuccessfulState) {
            authBloc.handleAuthenticatedUserNavigation(
              context: context,
              user: state.user,
            );
          }
        },
        builder: (context, state) {
          return Scaffold(
            extendBodyBehindAppBar: true,
            extendBody: true,
            resizeToAvoidBottomInset: true,
            bottomNavigationBar: Padding(
              padding: const EdgeInsets.symmetric(
                  horizontal: Spacings.x4, vertical: Spacings.x6),
              child: BlocBuilder<AuthBloc, AuthState>(
                builder: (context, state) {
                  return PrimaryButton(
                    semanticLabel: "VERIFY_PHONE_OTP_BTN",
                    enabled: state is OtpValidationSuccessfulState,
                    () {
                      RepositoryProvider.of<AuthAnalyticsRepository>(context)
                          .logOTPConfirmedEvent(
                        otpSource: "PHONE_LOGIN_OTP",
                      );
                      authBloc.add(VerifyPhoneOtpEvent(
                          otp: _otpTextEditingController.text));
                    },
                    "CONTINUE",
                  );
                },
              ),
            ),
            appBar: AppBar(
              toolbarHeight: Spacings.x20,
              titleSpacing: 0,
              backgroundColor: Colors.transparent,
              elevation: 0,
              leading: Semantics(
                label: "BACK_BTN",
                child: IconButton(
                  icon: const Icon(
                    Icons.arrow_back_ios_new_rounded,
                    color: Colors.white,
                  ),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                ),
              ),
            ),
            body: Stack(
              children: [
                Aurora(size: MediaQuery.of(context).size),
                Padding(
                  padding: const EdgeInsets.all(Spacings.x4),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      RichText(
                        textAlign: TextAlign.center,
                        text: TextSpan(
                          style: AuroraTheme.of(context).textStyle(
                            TypescaleValues.H2,
                            color: Colors.white60,
                          ),
                          children: [
                            TextSpan(
                              text: "Please enter the code we just sent to ",
                              style: AuroraTheme.of(context).textStyle(
                                TypescaleValues.H2,
                                color: Colors.white70,
                              ),
                            ),
                            TextSpan(
                              text:
                                  "\n(+91) ${context.read<AuthBloc>().phoneNumber}",
                              style: AuroraTheme.of(context).textStyle(
                                TypescaleValues.H2,
                                color: Colors.white,
                              ),
                            ),
                            TextSpan(
                              text: " to proceed",
                              style: AuroraTheme.of(context).textStyle(
                                TypescaleValues.H2,
                                color: Colors.white70,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(
                        height: Spacings.x6,
                      ),
                      Column(
                        children: [
                          if (state is OtpVerificationFailedState)
                            Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Center(
                                child: Text(
                                  state.error,
                                  style: const TextStyle(color: Colors.red),
                                ),
                              ),
                            ),
                          Semantics(
                            label: "PHONE_OTP_TF",
                            child: OTPField(
                              autoFocus: true,
                              textEditingController: _otpTextEditingController,
                              onChanged: (value) {
                                authBloc.add(OtpValidationEvent(
                                    otp: _otpTextEditingController.text));
                              },
                              onCompleted: (value) {
                                RepositoryProvider.of<AuthAnalyticsRepository>(
                                        context)
                                    .logOTPConfirmedEvent(
                                  otpSource: "PHONE_LOGIN_OTP",
                                );
                                authBloc.add(VerifyPhoneOtpEvent(
                                    otp: _otpTextEditingController.text));
                              },
                              placeholderText: "Enter OTP Here",
                              hasError: state is OtpVerificationFailedState,
                            ),
                          ),
                        ],
                      ),
                      Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          duration == 0
                              ? Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                      "Resend OTP via:",
                                      style: AuroraTheme.of(context).textStyle(
                                        TypescaleValues.P5,
                                        color: Colors.white,
                                      ),
                                    ),
                                    const SizedBox(
                                      width: 10,
                                    ),
                                    Semantics(
                                      label: "RESEND_SMS_OTP_BTN",
                                      child: TextButton(
                                        onPressed: () {
                                          RepositoryProvider.of<
                                                      AuthAnalyticsRepository>(
                                                  context)
                                              .logOTPInitiatedEvent(
                                            phone: authBloc.phoneNumber!,
                                            medium: "sms",
                                            countryCallingCode: "+91",
                                          );
                                          startTimer();
                                          Toast.show(
                                            "You will receive OTP on SMS shortly",
                                            context,
                                            duration: 2,
                                          );
                                          authBloc.add(
                                            GeneratePhoneOtpEvent(
                                              phoneNumber:
                                                  authBloc.phoneNumber!,
                                              isResend: true,
                                            ),
                                          );
                                        },
                                        child: Row(
                                          children: [
                                            Image.asset(
                                              IconConstants.smsIcon,
                                              package: Constants.packageName,
                                              width: 15,
                                            ),
                                            const SizedBox(
                                              width: 5,
                                            ),
                                            Text(
                                              "SMS",
                                              style: AuroraTheme.of(context)
                                                  .textStyle(
                                                TypescaleValues.P3,
                                                color: Colors.white,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                    Semantics(
                                      label: "RESEND_CALL_OTP_BTN",
                                      child: TextButton(
                                        onPressed: () {
                                          RepositoryProvider.of<
                                                      AuthAnalyticsRepository>(
                                                  context)
                                              .logOTPInitiatedEvent(
                                            phone: authBloc.phoneNumber!,
                                            medium: "call",
                                            countryCallingCode: "+91",
                                          );
                                          startTimer();
                                          Toast.show(
                                            "You will receive OTP on phone call shortly",
                                            context,
                                            duration: 2,
                                          );
                                          authBloc.add(
                                            GeneratePhoneOtpEvent(
                                              phoneNumber:
                                                  authBloc.phoneNumber!,
                                              isCall: true,
                                              isResend: true,
                                            ),
                                          );
                                        },
                                        child: Row(
                                          children: [
                                            Image.asset(
                                              IconConstants.callIcon,
                                              package: Constants.packageName,
                                              width: 15,
                                            ),
                                            const SizedBox(
                                              width: 5,
                                            ),
                                            Text(
                                              "CALL",
                                              style: AuroraTheme.of(context)
                                                  .textStyle(
                                                TypescaleValues.P3,
                                                color: Colors.white,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ],
                                )
                              : Column(
                                  children: [
                                    const SizedBox(
                                      height: Spacings.x4,
                                    ),
                                    RichText(
                                      text: TextSpan(
                                        style:
                                            AuroraTheme.of(context).textStyle(
                                          TypescaleValues.P5,
                                          color: Colors.white,
                                        ),
                                        children: [
                                          const TextSpan(
                                            text: "Didn't receive OTP?  ",
                                          ),
                                          TextSpan(
                                            text:
                                                "Resend OTP in (${duration.toFormattedCountdownSeconds()})",
                                            style: AuroraTheme.of(context)
                                                .textStyle(
                                              TypescaleValues.P3,
                                              color: Colors.white,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                          const SizedBox(
                            height: Spacings.x6,
                          ),
                          TextButton(
                            onPressed: () async {
                              if (await canLaunchUrl(
                                  Uri.parse(Constants.troubleLoggingInURL))) {
                                await launchUrl(
                                  Uri.parse(Constants.troubleLoggingInURL),
                                  mode: LaunchMode.externalApplication,
                                );
                              } else {
                                throw 'Could not launch ${Constants.troubleLoggingInURL}';
                              }
                            },
                            child: Text(
                              "TROUBLE LOGGING IN?",
                              style: AuroraTheme.of(context).textStyle(
                                TypescaleValues.P6,
                                color: Colors.white70,
                              ),
                            ),
                          ),
                        ],
                      )
                    ],
                  ),
                )
              ],
            ),
          );
        },
      ),
    );
  }
}

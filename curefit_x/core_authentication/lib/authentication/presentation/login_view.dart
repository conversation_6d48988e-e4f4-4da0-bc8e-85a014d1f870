import 'dart:io';
import 'dart:ui';

import 'package:carousel_slider/carousel_slider.dart';
import 'package:common/action/action_handler.dart';
import 'package:common/constants/action_constants.dart';
import 'package:common/ui/atoms/secondary_button.dart';
import 'package:common/ui/progress_indicator_carousel.dart';
import 'package:common/ui/screens/web_view.dart';
import 'package:common/user/user_repository.dart';
import 'package:common/util/action_util.dart';
import 'package:core_authentication/app_config/bloc/app_config_bloc.dart';
import 'package:core_authentication/authentication/data/data_provider/auth_data_provider.dart';
import 'package:core_authentication/authentication/presentation/widgets/social_icon_button.dart';
import 'package:core_authentication/util/route_name.dart';
import 'package:core_authentication/util/session_manager.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:sms_autofill/sms_autofill.dart';
import 'package:video_player_cf/video_player.dart';
import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:core_authentication/util/constants.dart';
import 'package:core_authentication/util/image_constants.dart';
import 'package:flutter/material.dart';
import 'package:common/ui/aurora.dart';
import 'package:flutter/services.dart';
import 'package:common/action/action_handler.dart' as ActionHandler;

import '../bloc/auth_bloc.dart';
import '../data/model/login_view_data_model.dart';
import '../data/repository/auth_analytics_repository.dart';

class LoginView extends StatefulWidget {
  const LoginView({super.key});

  @override
  State<LoginView> createState() => _LoginViewState();
}

class _LoginViewState extends State<LoginView> {
  final _phoneNumberController = TextEditingController();
  late VideoPlayerController _videoPlayerController;
  final _focusNode = FocusNode();

  @override
  void initState() {
    _videoPlayerController = VideoPlayerController(
      videoPlayerOptions: VideoPlayerOptions(mixWithOthers: true),
    );
    _videoPlayerController.setNetworkDataSource(
      AuthDataProvider.getOnboardingCarouselList()[0].videoUrl,
      useCache: true,
    );
    _videoPlayerController.play();
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      BlocProvider.of<AuthBloc>(context).add(const ResetAuthEvent());
      //Skip login call when the page is viewed so the second time user logs in, he see's the home page
      BlocProvider.of<AuthBloc>(context)
          .add(const SkipLoginEvent(navigateToHome: false));

      //Set args to check if we need to go to tab view or just pop back
      final ScreenArguments? args =
          ModalRoute.of(context)!.settings.arguments as ScreenArguments?;
      if (args != null) {
        LoginViewDataModel? loginViewDataModel =
            LoginViewDataModel.fromJson(args.params);
        BlocProvider.of<AuthBloc>(context)
            .setNavigateBackToTabView(loginViewDataModel.navigateBackToTabView);
      } else {
        BlocProvider.of<AuthBloc>(context).setNavigateBackToTabView(true);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    final authBloc = BlocProvider.of<AuthBloc>(context);
    AuroraThemeData themeData = AuroraTheme.of(context);
    return KeyboardVisibilityBuilder(builder: (context, isKeyboardVisible) {
      return BlocListener<AuthBloc, AuthState>(
        listener: (context, state) {
          if (state is PhoneOtpGeneratedSuccessfullyState) {
            SmsAutoFill().listenForCode;
            Navigator.of(context).pushNamed(Routes.phoneOtpPage).then(
              (_) {
                authBloc.add(
                  PhoneNumberValidationEvent(
                      phoneNumber: _phoneNumberController.text),
                );
              },
            );
          } else if (state is SocialAuthSuccessfulState) {
            authBloc.handleAuthenticatedUserNavigation(
              context: context,
              user: state.user,
            );
          } else if (state is SkipLoginSuccessfulState) {
            authBloc.handleAuthenticatedUserNavigation(
                context: context, user: state.user);
          }
        },
        child: PopScope(
          canPop: false,
          onPopInvoked: (_) {
            if (SessionManager.instance.navigatorKey != null) {
              BlocProvider.of<AppConfigBloc>(
                      SessionManager.instance.navigatorKey!.currentContext!)
                  .add(AfterLoginConfigEvent(
                      SessionManager.instance.navigatorKey!.currentContext!));
            }
          },
          child: Scaffold(
            resizeToAvoidBottomInset: false,
            body: Stack(
              children: <Widget>[
                Stack(
                  children: [
                    Aurora(
                      size: MediaQuery.of(context).size,
                      canvasTheme: CanvasTheme.BLACK,
                    ),
                    Container(
                      color: Colors.black87.withOpacity(0.2),
                      padding: const EdgeInsets.only(bottom: 15),
                      child: Container(
                        color: Colors.black.withOpacity(0.5),
                        padding: const EdgeInsets.only(bottom: 20),
                        child: ProgesssIndicatorCarousel(
                          autoPlay: true,
                          showProgressBar: true,
                          aspectRatio: "375:550",
                          onSnapToItem: (page, reason) {
                            _videoPlayerController.setNetworkDataSource(
                              AuthDataProvider.getOnboardingCarouselList()[page]
                                  .videoUrl,
                              useCache: true,
                            );
                            _videoPlayerController.play();
                          },
                          carouselOptions: CarouselOptions(
                            padEnds: false,
                            pageSnapping: true,
                            viewportFraction: 1,
                            height: MediaQuery.of(context).size.height * 0.6,
                            enableInfiniteScroll: false,
                            autoPlayInterval:
                                const Duration(milliseconds: 5000),
                            autoPlay: true,
                          ),
                          items: AuthDataProvider.getOnboardingCarouselList()
                              .map((carouselItem) {
                            return Builder(
                              builder: (BuildContext context) {
                                return SizedBox(
                                  width: MediaQuery.of(context).size.width,
                                  child: Opacity(
                                    opacity: 0.5,
                                    child:
                                        _videoPlayerController.value.isBuffering
                                            ? Image.network(carouselItem.image)
                                            : VideoPlayer(
                                                _videoPlayerController,
                                              ),
                                  ),
                                );
                              },
                            );
                          }).toList(),
                        ),
                      ),
                    ),
                    Positioned(
                      left: 0,
                      right: 0,
                      top: MediaQuery.of(context).size.height * 0.58,
                      child: ClipRRect(
                        // borderRadius: BorderRadius.circular(
                        //     10.0), // Ancestor widget's clip with rounded corners
                        child: BackdropFilter(
                          filter: ImageFilter.blur(sigmaX: 100, sigmaY: 20),
                          child: const SizedBox(
                            height: 600,
                          ),
                        ),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: Spacings.x7,
                        vertical: Spacings.x12,
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Semantics(
                            label: "CULT_APP_MODE_BTN",
                            child: GestureDetector(
                              onLongPress: (SessionManager().isTest ||
                                      RepositoryProvider.of<UserRepository>(
                                                  context)
                                              .devMode ==
                                          DevModes.STAGE ||
                                      RepositoryProvider.of<UserRepository>(
                                                  context)
                                              .devMode ==
                                          DevModes.ALPHA)
                                  ? () {
                                      BlocProvider.of<AppConfigBloc>(context)
                                          .add(
                                        ShowAppConfigBottomSheet(context),
                                      );
                                    }
                                  : null,
                              child: Image.asset(
                                ImageConstants.cultIcon,
                                package: Constants.packageName,
                                width: 30,
                              ),
                            ),
                          ),
                          const Spacer(),
                          BlocBuilder<AuthBloc, AuthState>(
                            builder: (context, state) {
                              return SizedBox(
                                width: Spacings.x16,
                                child: Semantics(
                                  label: "SKIP_LOGIN_BTN",
                                  child: SecondaryButton(
                                    (state is! SkipLoginInitiatedState)
                                        ? () {
                                            authBloc
                                                .add(const SkipLoginEvent());
                                          }
                                        : null,
                                    "Skip",
                                  ),
                                ),
                              );
                            },
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                Positioned(
                  left: 0,
                  right: 0,
                  bottom: scale(
                      context,
                      isKeyboardVisible
                          ? MediaQuery.of(context).viewInsets.bottom - 20
                          : 0),
                  child: Container(
                    color:
                        isKeyboardVisible ? Colors.black : Colors.transparent,
                    child: Padding(
                      padding: const EdgeInsets.only(
                        top: Spacings.x4,
                        left: Spacings.x4,
                        right: Spacings.x4,
                        bottom: Spacings.x1,
                      ),
                      child: Column(
                        children: [
                          BlocBuilder<AuthBloc, AuthState>(
                            builder: (context, state) {
                              if (state is PhoneNumberVerificationFailedState &&
                                  state.errorMessage != null) {
                                return Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: Center(
                                    child: Text(
                                      state.errorMessage!,
                                      style: const TextStyle(color: Colors.red),
                                    ),
                                  ),
                                );
                              }
                              return Container();
                            },
                          ),
                          Padding(
                            padding: const EdgeInsets.only(bottom: Spacings.x2),
                            child: TextField(
                              style: AuroraTheme.of(context)
                                  .textStyle(TypescaleValues.P1),
                              keyboardType: TextInputType.number,
                              inputFormatters: [
                                FilteringTextInputFormatter.allow(
                                  RegExp(r'[0-9]'),
                                ),
                              ],
                              focusNode: _focusNode,
                              decoration: const InputDecoration(
                                contentPadding: EdgeInsets.all(0),
                                fillColor: Color.fromARGB(26, 255, 255, 255),
                                filled: true,
                                prefixIcon: Padding(
                                  padding: EdgeInsets.only(
                                      top: Spacings.x3, left: Spacings.x3),
                                  child: Text(
                                    '+91   ',
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.w900,
                                      fontSize: 16.0,
                                    ),
                                  ),
                                ),
                                hintText: 'Enter your mobile number',
                                prefixIconColor: Colors.white,
                                hintStyle: TextStyle(
                                  color: Colors.grey,
                                ),
                                border: OutlineInputBorder(
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(8.0)),
                                  borderSide:
                                      BorderSide(color: Colors.transparent),
                                ),
                                focusedBorder: OutlineInputBorder(
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(8.0)),
                                  borderSide:
                                      BorderSide(color: Colors.transparent),
                                ),
                                enabledBorder: OutlineInputBorder(
                                  borderRadius:
                                      BorderRadius.all(Radius.circular(8.0)),
                                  borderSide:
                                      BorderSide(color: Colors.transparent),
                                ),
                              ),
                              controller: _phoneNumberController,
                              onTap: () {
                                authBloc.add(
                                  PhoneNumberValidationEvent(
                                      phoneNumber: _phoneNumberController.text),
                                );
                              },
                              onChanged: (value) {
                                authBloc.add(
                                  PhoneNumberValidationEvent(
                                      phoneNumber: value),
                                );
                              },
                              enabled: true,
                            ),
                          ),
                          // CONTINUE BUTTON
                          BlocBuilder<AuthBloc, AuthState>(
                            builder: (context, state) {
                              if (isKeyboardVisible ||
                                  state
                                      is PhoneNumberValidationSuccessfulState) {
                                return Padding(
                                  padding: const EdgeInsets.only(
                                      bottom: Spacings.x3),
                                  child: PrimaryButton(
                                    enabled: (state
                                        is PhoneNumberValidationSuccessfulState),
                                    semanticLabel: "GENERATE_PHONE_OTP_BTN",
                                    () {
                                      if (state
                                          is PhoneNumberValidationSuccessfulState) {
                                        FocusScope.of(context).unfocus();
                                        RepositoryProvider.of<
                                                    AuthAnalyticsRepository>(
                                                context)
                                            .logOTPInitiatedEvent(
                                          phone: _phoneNumberController.text,
                                          medium: "sms",
                                          countryCallingCode: "+91",
                                        );
                                        authBloc.add(
                                          GeneratePhoneOtpEvent(
                                              phoneNumber:
                                                  _phoneNumberController.text),
                                        );
                                      } else {
                                        authBloc.add(
                                          PhoneNumberValidationEvent(
                                            phoneNumber:
                                                _phoneNumberController.text,
                                            isContinueButtonPressed: true,
                                          ),
                                        );
                                        _focusNode.requestFocus();
                                      }
                                    },
                                    "Continue",
                                    verticalPadding: 12.0,
                                  ),
                                );
                              }
                              return Container();
                            },
                          ),
                          if (!isKeyboardVisible)
                            Column(
                              children: [
                                Padding(
                                  padding: const EdgeInsets.symmetric(
                                      vertical: Spacings.x3),
                                  child: Text(
                                    "OR",
                                    textAlign: TextAlign.center,
                                    style:
                                        themeData.textStyle(TypescaleValues.P3),
                                  ),
                                ),
                                if (Platform.isIOS)
                                  Row(
                                    children: [
                                      Expanded(
                                        child: TextButton(
                                          onPressed: () {
                                            RepositoryProvider.of<
                                                        AuthAnalyticsRepository>(
                                                    context)
                                                .logLoginButtonClickEvent(
                                              extraInfo: {
                                                "source": "full_page",
                                                "type": "APPLE",
                                              },
                                            );
                                            authBloc
                                                .add(const AppleSignInEvent());
                                          },
                                          style: TextButton.styleFrom(
                                            backgroundColor: Colors.white,
                                            padding: const EdgeInsets.symmetric(
                                                vertical: Spacings.x2,
                                                horizontal: Spacings.x4),
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(8.0),
                                            ),
                                          ),
                                          child: Row(
                                            mainAxisSize: MainAxisSize.min,
                                            children: [
                                              Image.asset(
                                                ImageConstants.appleIcon,
                                                package: Constants.packageName,
                                                width: 20,
                                                color: Colors.black,
                                              ),
                                              const SizedBox(
                                                width: Spacings.x3,
                                              ),
                                              Text(
                                                'Sign in with Apple',
                                                style: AuroraTheme.of(context)
                                                    .textStyle(
                                                  TypescaleValues.P1,
                                                  color: Colors.black,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                if (Platform.isIOS)
                                  const SizedBox(
                                    height: Spacings.x3,
                                  ),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    SocialIconButton(
                                      onPressed: () {
                                        RepositoryProvider.of<
                                                    AuthAnalyticsRepository>(
                                                context)
                                            .logLoginButtonClickEvent(
                                          extraInfo: {
                                            "source": "full_page",
                                            "type": "GOOGLE",
                                          },
                                        );
                                        authBloc.add(
                                          const GoogleSignInEvent(),
                                        );
                                      },
                                      semanticLabel: "GOOGLE_AUTH_BTN",
                                      icon: ImageConstants.googleIcon,
                                    ),
                                    SocialIconButton(
                                      onPressed: () {
                                        RepositoryProvider.of<
                                                    AuthAnalyticsRepository>(
                                                context)
                                            .logLoginButtonClickEvent(
                                          extraInfo: {
                                            "source": "full_page",
                                            "type": "FACEBOOK",
                                          },
                                        );
                                        authBloc.add(const FbSignInEvent());
                                      },
                                      semanticLabel: "FB_AUTH_BTN",
                                      icon: ImageConstants.fbIcon,
                                    ),
                                    // if (Platform.isIOS)
                                    //   SocialIconButton(
                                    //     onPressed: () {
                                    //       RepositoryProvider.of<
                                    //               AuthAnalyticsRepository>(context)
                                    //           .logLoginButtonClickEvent(
                                    //         extraInfo: {
                                    //           "source": "full_page",
                                    //           "type": "APPLE",
                                    //         },
                                    //       );
                                    //       authBloc.add(const AppleSignInEvent());
                                    //     },
                                    //     semanticLabel: "APPLE_AUTH_BTN",
                                    //     icon: ImageConstants.appleIcon,
                                    //   ),
                                    SocialIconButton(
                                      onPressed: () {
                                        RepositoryProvider.of<
                                                    AuthAnalyticsRepository>(
                                                context)
                                            .logLoginButtonClickEvent(
                                          extraInfo: {
                                            "source": "full_page",
                                            "type": "email",
                                          },
                                        );
                                        authBloc.add(const EmailValidationEvent(
                                            emailId: ""));
                                        Navigator.of(context)
                                            .pushNamed(Routes.emailPage);
                                      },
                                      semanticLabel: "EMAIL_AUTH_BTN",
                                      icon: ImageConstants.emailIcon,
                                    ),
                                  ],
                                ),
                              ],
                            ),

                          Padding(
                            padding: const EdgeInsets.symmetric(
                              vertical: Spacings.x5,
                            ),
                            child: SizedBox(
                              width: MediaQuery.of(context).size.width * 0.9,
                              child: RichText(
                                textAlign: TextAlign.center,
                                text: TextSpan(
                                  style: themeData
                                      .textStyle(TypescaleValues.P5)
                                      .copyWith(
                                        fontSize: 9,
                                      ),
                                  children: <TextSpan>[
                                    const TextSpan(
                                      text: 'By continuing you agree to the ',
                                    ),
                                    TextSpan(
                                      recognizer: TapGestureRecognizer()
                                        ..onTap = () {
                                          clickActionWithAnalytics(
                                            ActionHandler.Action(
                                              type: ActionTypes.NAVIGATION,
                                              url:
                                                  'curefit://webview?uri=${Constants.termsOfServicesURL}&title=Terms and Conditions',
                                              title: "Terms and Conditions",
                                            ),
                                            context,
                                            null,
                                            {},
                                          );
                                        },
                                      text: 'Terms of service ',
                                      style: const TextStyle(
                                          fontWeight: FontWeight.bold),
                                    ),
                                    const TextSpan(
                                      text: 'and ',
                                    ),
                                    TextSpan(
                                      recognizer: TapGestureRecognizer()
                                        ..onTap = () {
                                          clickActionWithAnalytics(
                                            ActionHandler.Action(
                                              type: ActionTypes.NAVIGATION,
                                              url:
                                                  'curefit://webview?uri=${Constants.privacyPolicyURL}&title=Privacy Policy',
                                              title: "Privacy Policy",
                                            ),
                                            context,
                                            null,
                                            {},
                                          );
                                        },
                                      text: 'Privacy Policy',
                                      style: const TextStyle(
                                          fontWeight: FontWeight.bold),
                                    )
                                  ],
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(
                            height: Spacings.x2,
                          )
                        ],
                      ),
                    ),
                  ),
                )
              ],
            ),
          ),
        ),
      );
    });
  }
}

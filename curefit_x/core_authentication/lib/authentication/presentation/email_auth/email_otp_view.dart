import 'dart:async';
import 'package:common/ui/atoms/input_fields/otp_field.dart';
import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/toast.dart';
import 'package:core_authentication/authentication/data/repository/auth_analytics_repository.dart';
import 'package:core_authentication/util/extensions/int_time_extension.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../util/constants.dart';
import '../../../util/image_constants.dart';
import '../../bloc/auth_bloc.dart';

class EmailOtpView extends StatefulWidget {
  const EmailOtpView({
    super.key,
  });

  @override
  State<EmailOtpView> createState() => _EmailOtpViewState();
}

class _EmailOtpViewState extends State<EmailOtpView> {
  Timer? resendTimer;
  final TextEditingController _otpTextEditingController =
      TextEditingController();

  int duration = 0;

  void startTimer() {
    if (resendTimer != null && resendTimer!.isActive) {
      resendTimer?.cancel();
    }
    setState(() {
      duration = 30;
    });
    resendTimer = Timer.periodic(const Duration(seconds: 1), timerCallback);
  }

  void timerCallback(Timer timer) {
    if (mounted) {
      if (duration <= 0) {
        timer.cancel();
        return;
      }
      setState(() {
        duration -= 1;
      });
    }
  }

  @override
  void dispose() {
    super.dispose();
    if (resendTimer != null && resendTimer!.isActive) resendTimer?.cancel();
  }

  @override
  void initState() {
    super.initState();
    startTimer();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      RepositoryProvider.of<AuthAnalyticsRepository>(context)
          .logLoginPageViewEvent(
        pageType: 'otpverifyview',
        pageName: 'OTP Verify Page',
        pageId: 'otpverifyview',
        eventInfo: {},
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    AuthBloc authBloc = BlocProvider.of<AuthBloc>(context);

    return Scaffold(
      body: BlocListener<AuthBloc, AuthState>(
        listener: (context, state) {
          if (state is OtpVerificationSuccessfulState) {
            authBloc.handleAuthenticatedUserNavigation(
              context: context,
              user: state.user,
            );
          }
        },
        child: BlocBuilder<AuthBloc, AuthState>(
          builder: (context, state) {
            return Scaffold(
              extendBodyBehindAppBar: true,
              extendBody: true,
              bottomNavigationBar: Padding(
                padding: const EdgeInsets.symmetric(
                    horizontal: Spacings.x4, vertical: Spacings.x6),
                child: BlocBuilder<AuthBloc, AuthState>(
                  builder: (context, state) {
                    return PrimaryButton(
                      semanticLabel: "VERIFY_EMAIL_OTP_BTN",
                      enabled: state is OtpValidationSuccessfulState,
                      () {
                        RepositoryProvider.of<AuthAnalyticsRepository>(context)
                            .logOTPConfirmedEvent(
                          otpSource: "EMAIL_LOGIN_OTP",
                        );
                        authBloc.add(VerifyEmailOtpEvent(
                            otp: _otpTextEditingController.text));
                      },
                      "CONTINUE",
                    );
                  },
                ),
              ),
              appBar: AppBar(
                toolbarHeight: Spacings.x20,
                titleSpacing: 0,
                elevation: 0,
                backgroundColor: Colors.transparent,
                leading: Semantics(
                  label: "BACK_BTN",
                  child: IconButton(
                    icon: const Icon(
                      Icons.arrow_back_ios_new_rounded,
                      color: Colors.white,
                    ),
                    onPressed: () {
                      Navigator.of(context).pop();
                    },
                  ),
                ),
              ),
              body: Stack(
                children: [
                  Aurora(size: MediaQuery.of(context).size),
                  Padding(
                    padding: const EdgeInsets.all(Spacings.x4),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        RichText(
                          text: TextSpan(
                            style: AuroraTheme.of(context).textStyle(
                              TypescaleValues.H2,
                              color: Colors.white60,
                            ),
                            children: [
                              TextSpan(
                                text: "Please enter the code we just sent to ",
                                style: AuroraTheme.of(context).textStyle(
                                  TypescaleValues.H2,
                                  color: Colors.white70,
                                ),
                              ),
                              TextSpan(
                                text: authBloc.email,
                                style: AuroraTheme.of(context).textStyle(
                                  TypescaleValues.H2,
                                  color: Colors.white,
                                ),
                              ),
                              TextSpan(
                                text: " to proceed",
                                style: AuroraTheme.of(context).textStyle(
                                  TypescaleValues.H2,
                                  color: Colors.white70,
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(
                          height: Spacings.x6,
                        ),
                        BlocBuilder<AuthBloc, AuthState>(
                          builder: (context, state) {
                            return Column(
                              children: [
                                if (state is OtpVerificationFailedState)
                                  Padding(
                                    padding: const EdgeInsets.all(8.0),
                                    child: Center(
                                      child: Text(
                                        state.error,
                                        style:
                                            const TextStyle(color: Colors.red),
                                      ),
                                    ),
                                  ),
                                Semantics(
                                  label: "EMAIL_OTP_TF",
                                  child: OTPField(
                                    textEditingController:
                                        _otpTextEditingController,
                                    onChanged: (value) {
                                      authBloc.add(OtpValidationEvent(
                                          otp: _otpTextEditingController.text));
                                    },
                                    onCompleted: (value) {
                                      RepositoryProvider.of<
                                              AuthAnalyticsRepository>(context)
                                          .logOTPConfirmedEvent(
                                        otpSource: "EMAIL_LOGIN_OTP",
                                      );
                                      authBloc.add(VerifyEmailOtpEvent(
                                          otp: _otpTextEditingController.text));
                                    },
                                    placeholderText: "Enter OTP Here",
                                    hasError:
                                        state is OtpVerificationFailedState,
                                    autoFocus: true,
                                  ),
                                ),
                              ],
                            );
                          },
                        ),
                        Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            duration == 0
                                ? Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Text(
                                        "Resend OTP via:",
                                        style:
                                            AuroraTheme.of(context).textStyle(
                                          TypescaleValues.P5,
                                          color: Colors.white,
                                        ),
                                      ),
                                      const SizedBox(
                                        width: 10,
                                      ),
                                      Semantics(
                                        label: "RESEND_EMAIL_OTP_BTN",
                                        child: TextButton(
                                          onPressed: () {
                                            RepositoryProvider.of<
                                                        AuthAnalyticsRepository>(
                                                    context)
                                                .logOTPInitiatedEvent(
                                              email: authBloc.email!,
                                              medium: "email",
                                            );
                                            startTimer();
                                            Toast.show(
                                              "You will receive OTP on EMAIL shortly",
                                              context,
                                              duration: 2,
                                            );
                                            authBloc.add(
                                              GenerateEmailOtpEvent(
                                                email: authBloc.email!,
                                                isResend: true,
                                              ),
                                            );
                                          },
                                          child: Row(
                                            children: [
                                              Image.asset(
                                                IconConstants.smsIcon,
                                                package: Constants.packageName,
                                                width: 15,
                                              ),
                                              const SizedBox(
                                                width: 5,
                                              ),
                                              Text(
                                                "EMAIL",
                                                style: AuroraTheme.of(context)
                                                    .textStyle(
                                                  TypescaleValues.P3,
                                                  color: Colors.white,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ],
                                  )

                                // TextButton(
                                //         onPressed: () {
                                //           startTimer();
                                //           authBloc.add(
                                //             GenerateEmailOtpEvent(
                                //               email: authBloc.email!,
                                //               isResend: true,
                                //             ),
                                //           );
                                //         },
                                //         child: Text(
                                //           "Resend OTP",
                                //           style: AuroraTheme.of(context).textStyle(
                                //             TypescaleValues.P3,
                                //             color: Colors.white,
                                //           ),
                                //         ),
                                //       )
                                : Column(
                                    children: [
                                      const SizedBox(
                                        height: Spacings.x4,
                                      ),
                                      RichText(
                                        text: TextSpan(
                                          style:
                                              AuroraTheme.of(context).textStyle(
                                            TypescaleValues.P5,
                                            color: Colors.white,
                                          ),
                                          children: [
                                            const TextSpan(
                                              text: "Didn't receive OTP?  ",
                                            ),
                                            TextSpan(
                                              text:
                                                  "Resend OTP in (${duration.toFormattedCountdownSeconds()})",
                                              style: AuroraTheme.of(context)
                                                  .textStyle(
                                                TypescaleValues.P3,
                                                color: Colors.white,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                            const SizedBox(
                              height: Spacings.x6,
                            ),
                            if (duration == 0)
                              TextButton(
                                onPressed: () async {
                                  if (await canLaunchUrl(Uri.parse(
                                      Constants.troubleLoggingInURL))) {
                                    await launchUrl(
                                      Uri.parse(Constants.troubleLoggingInURL),
                                      mode: LaunchMode.externalApplication,
                                    );
                                  } else {
                                    throw 'Could not launch ${Constants.troubleLoggingInURL}';
                                  }
                                },
                                child: Text(
                                  "TROUBLE LOGGING IN?",
                                  style: AuroraTheme.of(context).textStyle(
                                    TypescaleValues.P6,
                                    color: Colors.white70,
                                  ),
                                ),
                              ),
                          ],
                        )
                      ],
                    ),
                  )
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}

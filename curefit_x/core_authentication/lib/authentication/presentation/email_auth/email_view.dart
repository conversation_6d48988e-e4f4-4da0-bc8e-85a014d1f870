import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:core_authentication/util/route_name.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../bloc/auth_bloc.dart';
import '../../data/repository/auth_analytics_repository.dart';

class EmailView extends StatefulWidget {
  const EmailView({super.key});

  @override
  State<EmailView> createState() => _EmailViewState();
}

class _EmailViewState extends State<EmailView> {
  final TextEditingController _emailTextEditingController =
      TextEditingController();

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      RepositoryProvider.of<AuthAnalyticsRepository>(context)
          .logLoginPageViewEvent(
        pageType: 'emaillogin',
        pageName: 'E-mail OTP',
        pageId: 'emaillogin',
        eventInfo: {},
      );
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final authBloc = BlocProvider.of<AuthBloc>(context);

    return BlocConsumer<AuthBloc, AuthState>(
      listener: (context, state) {
        if (state is EmailOtpGeneratedSuccessfullyState) {
          Navigator.of(context).pushNamed(Routes.emailOtpPage).then(
            (_) {
              authBloc.add(
                EmailValidationEvent(emailId: _emailTextEditingController.text),
              );
            },
          );
        }
      },
      builder: (context, state) {
        return Theme(
          data: ThemeData(
            bottomSheetTheme: BottomSheetThemeData(
              backgroundColor: Colors.black.withOpacity(0),
            ),
          ),
          child: Scaffold(
            extendBodyBehindAppBar: true,
            appBar: AppBar(
              toolbarHeight: Spacings.x20,
              titleSpacing: 0,
              elevation: 0,
              backgroundColor: Colors.transparent,
              centerTitle: false,
              title: Text(
                "Email Sign in",
                style: AuroraTheme.of(context).textStyle(
                  TypescaleValues.H3,
                  color: Colors.white,
                ),
              ),
              leading: Semantics(
                label: "BACK_BTN",
                child: IconButton(
                  icon: const Icon(
                    Icons.arrow_back_ios_new_rounded,
                    color: Colors.white,
                  ),
                  onPressed: () {
                    Navigator.of(context).pop();
                  },
                ),
              ),
            ),
            // bottomSheet: Padding(
            //   padding: const EdgeInsets.only(
            //       left: Spacings.x4, right: Spacings.x4, bottom: Spacings.x4),
            //   child: BlocBuilder<AuthBloc, AuthState>(
            //     builder: (context, state) {
            //       return PrimaryButton(
            //         enabled: state is EmailValidationSuccessfulState,
            //         () {
            //           authBloc.add(
            //             GenerateEmailOtpEvent(
            //               email: _emailTextEditingController.text,
            //             ),
            //           );
            //         },
            //         "CONTINUE",
            //       );
            //     },
            //   ),
            // ),
            body: Stack(
              children: [
                Aurora(size: MediaQuery.of(context).size),
                Padding(
                  padding: const EdgeInsets.all(Spacings.x4),
                  child: Column(
                    children: [
                      const SizedBox(
                        height: Spacings.x23,
                      ),
                      if (state is OtpGenerationFailedState)
                        Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Center(
                            child: Text(
                              textAlign: TextAlign.center,
                              state.error,
                              style: const TextStyle(color: Colors.red),
                            ),
                          ),
                        ),
                      Semantics(
                        label: "EMAIL_TF",
                        child: TextField(
                          style: AuroraTheme.of(context)
                              .textStyle(TypescaleValues.P1),
                          keyboardType: TextInputType.emailAddress,
                          autofocus: true,
                          decoration: const InputDecoration(
                            contentPadding:
                                EdgeInsets.symmetric(horizontal: Spacings.x4),
                            fillColor: Color.fromARGB(26, 255, 255, 255),
                            filled: true,
                            hintText: 'Enter your email',
                            prefixIconColor: Colors.white,
                            hintStyle: TextStyle(
                              color: Colors.grey,
                            ),
                            border: OutlineInputBorder(
                              borderRadius:
                                  BorderRadius.all(Radius.circular(8.0)),
                              borderSide: BorderSide(color: Colors.transparent),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius:
                                  BorderRadius.all(Radius.circular(8.0)),
                              borderSide: BorderSide(color: Colors.transparent),
                            ),
                            enabledBorder: OutlineInputBorder(
                              borderRadius:
                                  BorderRadius.all(Radius.circular(8.0)),
                              borderSide: BorderSide(color: Colors.transparent),
                            ),
                          ),
                          controller: _emailTextEditingController,
                          onChanged: (value) {
                            authBloc.add(EmailValidationEvent(
                                emailId: _emailTextEditingController.text));
                          },
                          enabled: true,
                        ),
                      ),
                    ],
                  ),
                ),
                Positioned(
                  left: 0,
                  right: 0,
                  bottom: scale(context, 20),
                  child: Padding(
                    padding: const EdgeInsets.only(
                        left: Spacings.x4,
                        right: Spacings.x4,
                        bottom: Spacings.x1),
                    child: BlocBuilder<AuthBloc, AuthState>(
                      builder: (context, state) {
                        return PrimaryButton(
                          semanticLabel: "GENERATE_EMAIL_OTP_BTN",
                          enabled: state is EmailValidationSuccessfulState,
                          () {
                            RepositoryProvider.of<AuthAnalyticsRepository>(
                                    context)
                                .logAppLoginEvent(
                              extraInfo: {
                                "mode": "email",
                                "type": "personal",
                              },
                            );
                            authBloc.add(
                              GenerateEmailOtpEvent(
                                email: _emailTextEditingController.text,
                              ),
                            );
                          },
                          "CONTINUE",
                        );
                      },
                    ),
                  ),
                )
              ],
            ),
          ),
        );
      },
    );
  }
}

import 'dart:io';

import 'package:app_tracking_transparency/app_tracking_transparency.dart';
import 'package:bloc/bloc.dart';
import 'package:common/analytics/analytics_repository.dart';
import 'package:common/network/location_repository.dart';
import 'package:core_authentication/app_config/presentation/app_config_bottom_sheet.dart';
import 'package:core_authentication/app_config/presentation/widgets/app_mode_tile.dart';
import 'package:core_authentication/app_config/utils/app_mode_enum.dart';
import 'package:core_authentication/util/session_manager.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:logger/logger.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:common/constants/action_constants.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/action/action_handler.dart' as action_handler;

part 'app_config_event.dart';
part 'app_config_state.dart';

class AppConfigBloc extends Bloc<AppConfigEvent, AppConfigState> {
  Future<void> handleAppPermissions({required BuildContext context}) async {
    LocationRepository locationRepository =
        RepositoryProvider.of<LocationRepository>(context);
    try {
      //NOTIFICATION PERMISSION
      await Permission.notification.request().then((status) {
        if (status.isGranted) {
          RepositoryProvider.of<AnalyticsRepository>(context)
              .logNotificationPermissionEvent(
                  extraInfo: {"status": EnumToString.convertToString(status)},
                  action: "Notification permission granted");
          SessionManager.instance.sendRNAppConfig(payload: {
            "initNotification": "true",
          });
        } else if (status.isDenied) {
          RepositoryProvider.of<AnalyticsRepository>(context)
              .logNotificationPermissionEvent(
                  extraInfo: {"status": EnumToString.convertToString(status)},
                  action: "Notification permission denied");
        } else if (status.isPermanentlyDenied) {
          RepositoryProvider.of<AnalyticsRepository>(context)
              .logNotificationPermissionEvent(
                  extraInfo: {"status": EnumToString.convertToString(status)},
                  action: "Notification permission permanently denied");
        }
        //LOCATION PERMISSION
        locationRepository
            .getUserLocationAndCallUserStatus(
          requestPermission: true,
          withAppInitializeMethods: true,
          withLocationOnBoardingFlow: true,
          context: context,
        )
            .then((_) {
          if (Platform.isIOS) {
            //APP TRACKING PERMISSION
            Future.delayed(const Duration(seconds: 1), () {
              AppTrackingTransparency.requestTrackingAuthorization();
            });
          }
        });
      });
    } on Exception catch (e) {
      FirebaseCrashlytics.instance
          .recordFlutterError(FlutterErrorDetails(exception: e));
    }
  }

  Future<void> handleJuspayInitialisation(
      {required BuildContext context}) async {
    try {
      Future.delayed(const Duration(seconds: 2), () {
        BlocProvider.of<ActionBloc>(context).add(
          PerformActionEvent(
            action_handler.Action(type: ActionTypes.INITIATE_JUSPAY),
          ),
        );
      });
    } catch (e) {
      print("JUSPAY INIT FAILED${e.toString()}");
    }
  }

  AppConfigBloc() : super(AppConfigInitial()) {
    on<ShowAppConfigBottomSheet>((event, emit) {
      AppConfigBottomSheet.show(event.context);
    });
    on<SaveAppConfig>((event, emit) {
      SessionManager.instance.bridgeSetDevConfig!({"mode": event.mode.mode()});
      SessionManager.instance.networkClient?.setBaseUrl(event.mode.baseUrl());
      emit(DevModeChangedState(mode: event.mode));
    });
    on<AfterLoginConfigEvent>((event, emit) {
      handleAppPermissions(context: event.context);
      handleJuspayInitialisation(context: event.context);
    });
  }
}

part of 'app_config_bloc.dart';

@immutable
abstract class AppConfigEvent {
  const AppConfigEvent() : super();
}

class ShowAppConfigBottomSheet extends AppConfigEvent {
  final BuildContext context;
  const ShowAppConfigBottomSheet(this.context);
}

class SaveAppConfig extends AppConfigEvent {
  final BuildContext context;

  final AppMode mode;
  const SaveAppConfig(this.context, {required this.mode});
}

class AfterLoginConfigEvent extends AppConfigEvent {
  final BuildContext context;
  const AfterLoginConfigEvent(this.context);
}

enum AppMode { PRODUCTION, ALPHA, STAGE }

extension AppModeToString on AppMode {
  String title() {
    switch (this) {
      case AppMode.PRODUCTION:
        return "Production Server";
      case AppMode.ALPHA:
        return "Alpha Server";
      case AppMode.STAGE:
        return "Stage Server";
      default:
        return "Production Server";
    }
  }

  String mode() {
    switch (this) {
      case AppMode.PRODUCTION:
        return "PROD";
      case AppMode.ALPHA:
        return "ALPHA";
      case AppMode.STAGE:
        return "STAGE";
      default:
        return "PROD";
    }
  }

  String baseUrl() {
    switch (this) {
      case AppMode.PRODUCTION:
        return "https://www.cult.fit/api";
      case AppMode.ALPHA:
        return "https://alpha.cult.fit/api";
      case AppMode.STAGE:
        return "https://stage.cult.fit/api";
      default:
        return "https://www.cult.fit/api";
    }
  }
}

extension StringToAppMode on String? {
  AppMode getAppModeFromName() {
    switch (this) {
      case "PROD":
        return AppMode.PRODUCTION;
      case "ALPHA":
        return AppMode.ALPHA;
      case "STAGE":
        return AppMode.STAGE;
      default:
        return AppMode.PRODUCTION;
    }
  }

  AppMode getAppModeFromBaseUrl() {
    switch (this) {
      case "https://www.cult.fit/api":
        return AppMode.PRODUCTION;
      case "https://alpha.cult.fit/api":
        return AppMode.ALPHA;
      case "https://stage.cult.fit/api":
        return AppMode.STAGE;
      default:
        return AppMode.PRODUCTION;
    }
  }
}

import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/user/user_repository.dart';
import 'package:core_authentication/app_config/bloc/app_config_bloc.dart';
import 'package:core_authentication/app_config/presentation/widgets/app_mode_tile.dart';
import 'package:core_authentication/app_config/utils/app_mode_enum.dart';
import 'package:core_authentication/util/session_manager.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AppConfigBottomSheet {
  static show(BuildContext context) {
    showModalBottomSheet(
      backgroundColor: Colors.transparent,
      context: context,
      builder: (context) {
        return BlocListener<AppConfigBloc, AppConfigState>(
          listener: (context, state) {
            if (state is DevModeChangedState) {
              RepositoryProvider.of<UserRepository>(context).saveDevMode(
                {
                  "devMode": {
                    "mode": state.mode.mode(),
                  }
                },
              );
              Navigator.of(context).pop();
            }
          },
          child: Container(
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(15.0),
                topRight: Radius.circular(15.0),
              ),
            ),
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "App Mode",
                        style: AuroraTheme.of(context).textStyle(
                          TypescaleValues.H4,
                          color: Colors.black,
                        ),
                      ),
                      TextButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                        child: Text(
                          "Close",
                          style: AuroraTheme.of(context).textStyle(
                            TypescaleValues.P3,
                            color: Colors.black26,
                          ),
                        ),
                      )
                    ],
                  ),
                  const AppModeTile(
                    appModel: AppMode.PRODUCTION,
                  ),
                  const AppModeTile(
                    appModel: AppMode.ALPHA,
                  ),
                  const AppModeTile(
                    appModel: AppMode.STAGE,
                  ),
                  const SizedBox(
                    height: 20,
                  )
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

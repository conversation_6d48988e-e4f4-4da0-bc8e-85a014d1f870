import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:core_authentication/app_config/bloc/app_config_bloc.dart';
import 'package:core_authentication/app_config/utils/app_mode_enum.dart';
import 'package:core_authentication/util/session_manager.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class AppModeTile extends StatelessWidget {
  final AppMode appModel;
  const AppModeTile({
    super.key,
    required this.appModel,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      contentPadding: EdgeInsets.zero,
      minLeadingWidth: 0,
      title: Text(
        appModel.title(),
        style: AuroraTheme.of(context).textStyle(
          TypescaleValues.P4,
          color: Colors.black,
        ),
      ),
      trailing: Theme(
        data: ThemeData(useMaterial3: true),
        child: Semantics(
          label: appModel.name,
          child: Switch(
            inactiveThumbColor: const Color(0xffDA2C6C),
            activeColor: const Color(0xffDA2C6C),
            value: SessionManager()
                    .networkClient
                    ?.baseUrl
                    .getAppModeFromBaseUrl() ==
                appModel,
            onChanged: (bool value) {
              BlocProvider.of<AppConfigBloc>(context)
                  .add(SaveAppConfig(context, mode: appModel));
            },
          ),
        ),
      ),
    );
  }
}

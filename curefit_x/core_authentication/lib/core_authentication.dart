import 'package:core_authentication/authentication/presentation/signup/enter_name_view.dart';
import 'package:core_authentication/authentication/presentation/signup/set_email_otp_view.dart';
import 'package:core_authentication/authentication/presentation/signup/set_email_view.dart';
import 'package:core_authentication/util/route_name.dart';
import 'package:flutter/material.dart';

import 'authentication/presentation/email_auth/email_otp_view.dart';
import 'authentication/presentation/email_auth/email_view.dart';
import 'authentication/presentation/example_home_view.dart';
import 'authentication/presentation/login_view.dart';
import 'authentication/presentation/phone_auth/phone_otp_view.dart';

class CoreAuthentication {
  Map<String, WidgetBuilder> getRoutes({required Function onClose}) {
    return {
      Routes.loginPage: (_) => const LoginView(),
      Routes.phoneOtpPage: (_) => const PhoneOtpView(),
      Routes.emailPage: (_) => const EmailView(),
      Routes.emailOtpPage: (_) => const EmailOtpView(),
      Routes.exampleHomePage: (_) => const ExampleHomeView(),
      Routes.enterNamePage: (_) => const EnterNameView(),
      Routes.setEmailPage: (_) => const SetEmailView(),
      Routes.setEmailOtpPage: (_) => const SetEmailOtpView(),
    };
  }
}

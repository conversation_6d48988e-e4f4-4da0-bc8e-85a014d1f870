import 'package:bloc/bloc.dart';
import 'package:meta/meta.dart';

import '../data/repository/user_status_repository.dart';

part 'user_status_event.dart';
part 'user_status_state.dart';

class UserStatusBloc extends Bloc<UserStatusEvent, UserStatusState> {
  final UserStatusRepository userStatusRepository;
  UserStatusBloc({required this.userStatusRepository})
      : super(UserStatusInitial()) {
    on<UserStatusEvent>((event, emit) async {
      final response = await userStatusRepository.getUserStatus();
      emit(UserStatusUpdatedState());
    });
  }
}

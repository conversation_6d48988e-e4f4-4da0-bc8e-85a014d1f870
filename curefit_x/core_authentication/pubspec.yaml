name: core_authentication
description: "A new Flutter project."
version: 0.0.1
homepage:


environment:
  sdk: ">=2.17.0 <3.0.0"
  flutter: ">=2.0.0 <3.0.0"

dependencies:
  flutter:
    sdk: flutter
  core:
    path: ../core
  flutter_bloc: ^8.0.1
  google_sign_in: ^6.2.1
  flutter_facebook_auth: ^6.0.4
  sign_in_with_apple: ^6.1.3
  shared_preferences: ^2.0.7
  logger: ^2.4.0
  flutter_keyboard_visibility: 5.2.0
  url_launcher: 6.1.3
  sms_autofill: ^2.4.0
  enum_to_string: ^2.0.1
  app_tracking_transparency: ^2.0.6
  advertising_id: ^2.7.1
dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  assets:
   - assets/images/
  #
  # For details regarding assets in packages, see
  # https://flutter.dev/assets-and-images/#from-packages
  #
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # To add custom fonts to your plugin package, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts in packages, see
  # https://flutter.dev/custom-fonts/#from-packages

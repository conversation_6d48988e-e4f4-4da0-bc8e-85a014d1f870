import 'package:core_authentication/authentication/presentation/login_view.dart';
import 'package:flutter/material.dart';
import 'package:core_authentication/core_authentication.dart';

class RouteGenerator {
  Map<String, WidgetBuilder> appRoutes = {};
  final CoreAuthentication coreAuthentication = CoreAuthentication();
  Route<dynamic> generateRoute(RouteSettings settings) {
    final args = settings.arguments;
    appRoutes.addAll(coreAuthentication.getRoutes(onClose: () {}));

    if (appRoutes.containsKey(settings.name)) {
      return MaterialPageRoute(builder: appRoutes[settings.name]!);
    }
    return MaterialPageRoute(
      builder: (context) => const LoginView(),
    );
  }
}

import 'package:common/analytics/analytics_repository.dart';
import 'package:common/network/location_repository.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:core_authentication/app_config/bloc/app_config_bloc.dart';
import 'package:core_authentication/authentication/bloc/auth_bloc.dart';
import 'package:core_authentication/authentication/data/repository/auth_analytics_repository.dart';
import 'package:core_authentication/authentication/data/repository/auth_repository.dart';
import 'package:core_authentication/util/session_manager.dart';
import 'package:core_authentication_example/utils/route/route_generator.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:common/network/client.dart' show NetworkClient;

void main() {
  runApp(const MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  final RouteGenerator _routeGenerator = RouteGenerator();
  final AuroraThemeData _auroraThemeData = AuroraThemeData();

  @override
  void initState() {
    super.initState();
    SessionManager().init(
      newNetworkClient: NetworkClient("https://www.cult.fit/api"),
      newBridgeSetToken: (Map<String, String> payload) {},
      newBridgeSetDevConfig: (Map<String, String> payload) {},
      newBridgeSetUserInfo: (Map<String, dynamic> payload) {},
      isTest: true,
    );
  }

  @override
  Widget build(BuildContext context) {
    return AuroraTheme.fromAuroraThemeData(
      data: _auroraThemeData,
      child: KeyboardDismissOnTap(
        child: MultiRepositoryProvider(
          providers: [
            RepositoryProvider(
              create: (context) => AuthRepository(
                  networkClient: SessionManager().networkClient!),
            ),
            RepositoryProvider(
              create: (context) => AnalyticsRepository(
                  sendEventTrigger: (String eventName,
                      Map<String, dynamic> eventInfo,
                      InspectorTab inspectorTab) {}),
            ),
            RepositoryProvider(
              create: (context) =>
                  LocationRepository(SessionManager().networkClient!),
            ),
            RepositoryProvider(
              create: (context) => AuthAnalyticsRepository(
                sendEvent: RepositoryProvider.of<AnalyticsRepository>(context)
                    .sendEvent,
              ),
            )
          ],
          child: MultiBlocProvider(
            providers: [
              BlocProvider(
                create: (context) => AuthBloc(
                    loginRepository:
                        RepositoryProvider.of<AuthRepository>(context)),
              ),
              BlocProvider(
                create: (context) => AppConfigBloc(),
              ),
            ],
            child: MaterialApp(
              theme: ThemeData(
                useMaterial3: false,
              ),
              onGenerateRoute: _routeGenerator.generateRoute,
            ),
          ),
        ),
      ),
    );
  }
}

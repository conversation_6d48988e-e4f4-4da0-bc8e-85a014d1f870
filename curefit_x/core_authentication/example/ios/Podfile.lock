PODS:
  - advance_pdf_viewer_fork (1.1.7):
    - Flutter
  - AppAuth (1.7.5):
    - AppAuth/Core (= 1.7.5)
    - AppAuth/ExternalUserAgent (= 1.7.5)
  - AppAuth/Core (1.7.5)
  - AppAuth/ExternalUserAgent (1.7.5):
    - AppAuth/Core
  - audioplayers_darwin (0.0.1):
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
    - ReachabilitySwift
  - device_calendar (0.0.1):
    - Flutter
  - device_info_plus (0.0.1):
    - Flutter
  - Firebase/CoreOnly (10.25.0):
    - FirebaseCore (= 10.25.0)
  - Firebase/Crashlytics (10.25.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 10.25.0)
  - Firebase/Messaging (10.25.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 10.25.0)
  - firebase_core (2.32.0):
    - Firebase/CoreOnly (= 10.25.0)
    - Flutter
  - firebase_crashlytics (3.5.7):
    - Firebase/Crashlytics (= 10.25.0)
    - firebase_core
    - Flutter
  - firebase_messaging (14.9.4):
    - Firebase/Messaging (= 10.25.0)
    - firebase_core
    - Flutter
  - FirebaseCore (10.25.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.12)
    - GoogleUtilities/Logger (~> 7.12)
  - FirebaseCoreExtension (10.29.0):
    - FirebaseCore (~> 10.0)
  - FirebaseCoreInternal (10.29.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseCrashlytics (10.25.0):
    - FirebaseCore (~> 10.5)
    - FirebaseInstallations (~> 10.0)
    - FirebaseRemoteConfigInterop (~> 10.23)
    - FirebaseSessions (~> 10.5)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/Environment (~> 7.8)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (~> 2.1)
  - FirebaseInstallations (10.29.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (10.25.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.3)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Reachability (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseRemoteConfigInterop (10.29.0)
  - FirebaseSessions (10.29.0):
    - FirebaseCore (~> 10.5)
    - FirebaseCoreExtension (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/Environment (~> 7.13)
    - GoogleUtilities/UserDefaults (~> 7.13)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesSwift (~> 2.1)
  - Flutter (1.0.0)
  - flutter_blue_plus (0.0.1):
    - Flutter
  - flutter_exif_rotation (0.3.0):
    - Flutter
  - flutter_js (0.1.0):
    - Flutter
  - flutter_keyboard_visibility (0.0.1):
    - Flutter
  - flutter_native_timezone (0.0.1):
    - Flutter
  - flutter_platform_alert (0.0.1):
    - Flutter
  - geolocator_apple (1.2.0):
    - Flutter
  - google_sign_in_ios (0.0.1):
    - AppAuth (>= 1.7.4)
    - Flutter
    - FlutterMacOS
    - GoogleSignIn (~> 7.1)
    - GTMSessionFetcher (>= 3.4.0)
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleSignIn (7.1.0):
    - AppAuth (< 2.0, >= 1.7.3)
    - GTMAppAuth (< 5.0, >= 4.1.1)
    - GTMSessionFetcher/Core (~> 3.3)
  - GoogleUtilities/AppDelegateSwizzler (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.3):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/Reachability (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMAppAuth (4.1.1):
    - AppAuth/Core (~> 1.7)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3)
  - GTMSessionFetcher (3.5.0):
    - GTMSessionFetcher/Full (= 3.5.0)
  - GTMSessionFetcher/Core (3.5.0)
  - GTMSessionFetcher/Full (3.5.0):
    - GTMSessionFetcher/Core
  - health (1.0.4):
    - Flutter
  - image_cropper (0.0.4):
    - Flutter
    - TOCropViewController (~> 2.7.4)
  - image_picker_ios (0.0.1):
    - Flutter
  - in_app_review (0.2.0):
    - Flutter
  - nanopb (2.30910.0):
    - nanopb/decode (= 2.30910.0)
    - nanopb/encode (= 2.30910.0)
  - nanopb/decode (2.30910.0)
  - nanopb/encode (2.30910.0)
  - nhttp (0.0.1):
    - Flutter
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.1.1):
    - Flutter
  - PromisesObjC (2.4.0)
  - PromisesSwift (2.4.0):
    - PromisesObjC (= 2.4.0)
  - ReachabilitySwift (5.2.3)
  - screen_protector (1.2.1):
    - Flutter
    - ScreenProtectorKit (~> 1.3.1)
  - ScreenProtectorKit (1.3.1)
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sign_in_with_apple (0.0.1):
    - Flutter
  - sqflite (0.0.3):
    - Flutter
    - FlutterMacOS
  - store_redirect (0.0.1):
    - Flutter
  - SwiftyJSON (5.0.2)
  - TOCropViewController (2.7.4)
  - twilio_conversations (0.0.1):
    - Flutter
    - SwiftyJSON (~> 5.0)
    - TwilioConversationsClient (= 2.1.0)
  - TwilioConversationsClient (2.1.0)
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_player_cf (0.0.1):
    - Flutter
  - wakelock (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter
  - workmanager (0.0.1):
    - Flutter

DEPENDENCIES:
  - advance_pdf_viewer_fork (from `.symlinks/plugins/advance_pdf_viewer_fork/ios`)
  - audioplayers_darwin (from `.symlinks/plugins/audioplayers_darwin/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - device_calendar (from `.symlinks/plugins/device_calendar/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_crashlytics (from `.symlinks/plugins/firebase_crashlytics/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - Flutter (from `Flutter`)
  - flutter_blue_plus (from `.symlinks/plugins/flutter_blue_plus/ios`)
  - flutter_exif_rotation (from `.symlinks/plugins/flutter_exif_rotation/ios`)
  - flutter_facebook_auth (from `.symlinks/plugins/flutter_facebook_auth/ios`)
  - flutter_js (from `.symlinks/plugins/flutter_js/ios`)
  - flutter_keyboard_visibility (from `.symlinks/plugins/flutter_keyboard_visibility/ios`)
  - flutter_native_timezone (from `.symlinks/plugins/flutter_native_timezone/ios`)
  - flutter_platform_alert (from `.symlinks/plugins/flutter_platform_alert/ios`)
  - geolocator_apple (from `.symlinks/plugins/geolocator_apple/ios`)
  - google_sign_in_ios (from `.symlinks/plugins/google_sign_in_ios/darwin`)
  - health (from `.symlinks/plugins/health/ios`)
  - image_cropper (from `.symlinks/plugins/image_cropper/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - in_app_review (from `.symlinks/plugins/in_app_review/ios`)
  - nhttp (from `.symlinks/plugins/nhttp/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - screen_protector (from `.symlinks/plugins/screen_protector/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sign_in_with_apple (from `.symlinks/plugins/sign_in_with_apple/ios`)
  - sqflite (from `.symlinks/plugins/sqflite/darwin`)
  - store_redirect (from `.symlinks/plugins/store_redirect/ios`)
  - twilio_conversations (from `.symlinks/plugins/twilio_conversations/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_player_cf (from `.symlinks/plugins/video_player_cf/ios`)
  - wakelock (from `.symlinks/plugins/wakelock/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/ios`)
  - workmanager (from `.symlinks/plugins/workmanager/ios`)

SPEC REPOS:
  trunk:
    - AppAuth
    - Firebase
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseInstallations
    - FirebaseMessaging
    - FirebaseRemoteConfigInterop
    - FirebaseSessions
    - GoogleDataTransport
    - GoogleSignIn
    - GoogleUtilities
    - GTMAppAuth
    - GTMSessionFetcher
    - nanopb
    - PromisesObjC
    - PromisesSwift
    - ReachabilitySwift
    - ScreenProtectorKit
    - SwiftyJSON
    - TOCropViewController
    - TwilioConversationsClient

EXTERNAL SOURCES:
  advance_pdf_viewer_fork:
    :path: ".symlinks/plugins/advance_pdf_viewer_fork/ios"
  audioplayers_darwin:
    :path: ".symlinks/plugins/audioplayers_darwin/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  device_calendar:
    :path: ".symlinks/plugins/device_calendar/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_crashlytics:
    :path: ".symlinks/plugins/firebase_crashlytics/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  Flutter:
    :path: Flutter
  flutter_blue_plus:
    :path: ".symlinks/plugins/flutter_blue_plus/ios"
  flutter_exif_rotation:
    :path: ".symlinks/plugins/flutter_exif_rotation/ios"
  flutter_facebook_auth:
    :path: ".symlinks/plugins/flutter_facebook_auth/ios"
  flutter_js:
    :path: ".symlinks/plugins/flutter_js/ios"
  flutter_keyboard_visibility:
    :path: ".symlinks/plugins/flutter_keyboard_visibility/ios"
  flutter_native_timezone:
    :path: ".symlinks/plugins/flutter_native_timezone/ios"
  flutter_platform_alert:
    :path: ".symlinks/plugins/flutter_platform_alert/ios"
  geolocator_apple:
    :path: ".symlinks/plugins/geolocator_apple/ios"
  google_sign_in_ios:
    :path: ".symlinks/plugins/google_sign_in_ios/darwin"
  health:
    :path: ".symlinks/plugins/health/ios"
  image_cropper:
    :path: ".symlinks/plugins/image_cropper/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  in_app_review:
    :path: ".symlinks/plugins/in_app_review/ios"
  nhttp:
    :path: ".symlinks/plugins/nhttp/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  screen_protector:
    :path: ".symlinks/plugins/screen_protector/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sign_in_with_apple:
    :path: ".symlinks/plugins/sign_in_with_apple/ios"
  sqflite:
    :path: ".symlinks/plugins/sqflite/darwin"
  store_redirect:
    :path: ".symlinks/plugins/store_redirect/ios"
  twilio_conversations:
    :path: ".symlinks/plugins/twilio_conversations/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_player_cf:
    :path: ".symlinks/plugins/video_player_cf/ios"
  wakelock:
    :path: ".symlinks/plugins/wakelock/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/ios"
  workmanager:
    :path: ".symlinks/plugins/workmanager/ios"

SPEC CHECKSUMS:
  advance_pdf_viewer_fork: 7364d9c1ea71ec070d35b78d4341eadbabe84549
  AppAuth: 501c04eda8a8d11f179dbe8637b7a91bb7e5d2fa
  audioplayers_darwin: 877d9a4d06331c5c374595e46e16453ac7eafa40
  connectivity_plus: 413a8857dd5d9f1c399a39130850d02fe0feaf7e
  device_calendar: 9cb33f88a02e19652ec7b8b122ca778f751b1f7b
  device_info_plus: e5c5da33f982a436e103237c0c85f9031142abed
  Firebase: 0312a2352584f782ea56f66d91606891d4607f06
  firebase_core: a626d00494efa398e7c54f25f1454a64c8abf197
  firebase_crashlytics: 17e856fabec68d993662abaf2f6fe2413f0abece
  firebase_messaging: 06391e8f35dc65a00c56580266285263d2861f10
  FirebaseCore: 7ec4d0484817f12c3373955bc87762d96842d483
  FirebaseCoreExtension: 705ca5b14bf71d2564a0ddc677df1fc86ffa600f
  FirebaseCoreInternal: df84dd300b561c27d5571684f389bf60b0a5c934
  FirebaseCrashlytics: 4b96efb0ce73b38b2a85e8b8bd1bd8f63f09d015
  FirebaseInstallations: 913cf60d0400ebd5d6b63a28b290372ab44590dd
  FirebaseMessaging: 88950ba9485052891ebe26f6c43a52bb62248952
  FirebaseRemoteConfigInterop: 6efda51fb5e2f15b16585197e26eaa09574e8a4d
  FirebaseSessions: dbd14adac65ce996228652c1fc3a3f576bdf3ecc
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_blue_plus: 4837da7d00cf5d441fdd6635b3a57f936778ea96
  flutter_exif_rotation: de6b54e8bd54a687f28d6397d1a890391de41111
  flutter_facebook_auth: c8700ab1770f3d8e5e7456220e4f3bbcdb831454
  flutter_js: 95929d4e146e8ceb1c8e1889d8c2065c5d840076
  flutter_keyboard_visibility: 0339d06371254c3eb25eeb90ba8d17dca8f9c069
  flutter_native_timezone: 5f05b2de06c9776b4cc70e1839f03de178394d22
  flutter_platform_alert: 6a241c90f3cc350d0286deeefee9f52bee957019
  geolocator_apple: 6cbaf322953988e009e5ecb481f07efece75c450
  google_sign_in_ios: 07375bfbf2620bc93a602c0e27160d6afc6ead38
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleSignIn: d4281ab6cf21542b1cfaff85c191f230b399d2db
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  GTMAppAuth: f69bd07d68cd3b766125f7e072c45d7340dea0de
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  health: 5a380c0f6c4f619535845992993964293962e99e
  image_cropper: 37d40f62177c101ff4c164906d259ea2c3aa70cf
  image_picker_ios: c560581cceedb403a6ff17f2f816d7fea1421fc1
  in_app_review: 318597b3a06c22bb46dc454d56828c85f444f99d
  nanopb: 438bc412db1928dac798aa6fd75726007be04262
  nhttp: a8c355395d712c8ef676d746ed30fbb2c7e49685
  package_info_plus: 58f0028419748fad15bf008b270aaa8e54380b1c
  path_provider_foundation: 2b6b4c569c0fb62ec74538f866245ac84301af46
  permission_handler_apple: e76247795d700c14ea09e3a2d8855d41ee80a2e6
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  PromisesSwift: 9d77319bbe72ebf6d872900551f7eeba9bce2851
  ReachabilitySwift: 7f151ff156cea1481a8411701195ac6a984f4979
  screen_protector: 6f92086bd2f2f4b54f54913289b9d1310610140b
  ScreenProtectorKit: 83a6281b02c7a5902ee6eac4f5045f674e902ae4
  share_plus: 056a1e8ac890df3e33cb503afffaf1e9b4fbae68
  shared_preferences_foundation: fcdcbc04712aee1108ac7fda236f363274528f78
  sign_in_with_apple: f3bf75217ea4c2c8b91823f225d70230119b8440
  sqflite: 673a0e54cc04b7d6dba8d24fb8095b31c3a99eec
  store_redirect: 2977747cf81689a39bd62c248c2deacb7a0d131e
  SwiftyJSON: f5b1bf1cd8dd53cd25887ac0eabcfd92301c6a5a
  TOCropViewController: 80b8985ad794298fb69d3341de183f33d1853654
  twilio_conversations: 43c1d4d472197e0199d1bcf7ccd98782117b6f35
  TwilioConversationsClient: a951ceee63005b2b5050d27b8b23408453c5c169
  url_launcher_ios: 5334b05cef931de560670eeae103fd3e431ac3fe
  video_player_cf: 6c661a26de0038a04a16995b9df6bce954313390
  wakelock: d0fc7c864128eac40eba1617cb5264d9c940b46f
  webview_flutter_wkwebview: 2a23822e9039b7b1bc52e5add778e5d89ad488d1
  workmanager: 0afdcf5628bbde6924c21af7836fed07b42e30e6

PODFILE CHECKSUM: a57f30d18f102dd3ce366b1d62a55ecbef2158e5

COCOAPODS: 1.15.2

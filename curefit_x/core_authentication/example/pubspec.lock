# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  _flutterfire_internals:
    dependency: transitive
    description:
      name: _flutterfire_internals
      sha256: "37a42d06068e2fe3deddb2da079a8c4d105f241225ba27b7122b37e9865fd8f7"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.35"
  advance_pdf_viewer_fork:
    dependency: transitive
    description:
      name: advance_pdf_viewer_fork
      sha256: b011f30fd73e909a54719335dc20824a33e88a52f728c926ca6b9dcfb9100e45
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  advertising_id:
    dependency: transitive
    description:
      name: advertising_id
      sha256: ab06ee85203ab500be85b7f45de2a75a629d8d9c453dba779276fbc4e97ad8d3
      url: "https://pub.dev"
    source: hosted
    version: "2.7.1"
  animated_flip_counter:
    dependency: transitive
    description:
      name: animated_flip_counter
      sha256: "73f852d84c461c3e4c1ddf320bee334dde8dba89441922ab11a8013be0b2fad1"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.4"
  animated_size_and_fade:
    dependency: transitive
    description:
      name: animated_size_and_fade
      sha256: "9af8745c47d80283d5c959116f6105872520321e827cc0a79dba675b06f02bf0"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  animations:
    dependency: transitive
    description:
      name: animations
      sha256: d3d6dcfb218225bbe68e87ccf6378bbb2e32a94900722c5f81611dad089911cb
      url: "https://pub.dev"
    source: hosted
    version: "2.0.11"
  app_badge_plus:
    dependency: transitive
    description:
      name: app_badge_plus
      sha256: cda59a8194352e308c824af6099bd338917b17d065a3bce6818013f032099611
      url: "https://pub.dev"
    source: hosted
    version: "1.1.6"
  app_settings:
    dependency: transitive
    description:
      name: app_settings
      sha256: "476df1d85cec143c3d27dd1c7451629a59c0c5ccf70a0adcbfa92a0a2d928705"
      url: "https://pub.dev"
    source: hosted
    version: "5.2.0"
  app_tracking_transparency:
    dependency: transitive
    description:
      name: app_tracking_transparency
      sha256: "64d9745931e565790abdea91b518ac8dc3cebe6d0d0aaf7119343271b983259a"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.6"
  archive:
    dependency: transitive
    description:
      name: archive
      sha256: cb6a278ef2dbb298455e1a713bda08524a175630ec643a242c399c932a0a1f7d
      url: "https://pub.dev"
    source: hosted
    version: "3.6.1"
  args:
    dependency: transitive
    description:
      name: args
      sha256: bf9f5caeea8d8fe6721a9c358dd8a5c1947b27f1cfaa18b39c301273594919e6
      url: "https://pub.dev"
    source: hosted
    version: "2.6.0"
  async:
    dependency: transitive
    description:
      name: async
      sha256: "947bfcf187f74dbc5e146c9eb9c0f10c9f8b30743e341481c1e2ed3ecc18c20c"
      url: "https://pub.dev"
    source: hosted
    version: "2.11.0"
  audioplayers:
    dependency: transitive
    description:
      name: audioplayers
      sha256: c05c6147124cd63e725e861335a8b4d57300b80e6e92cea7c145c739223bbaef
      url: "https://pub.dev"
    source: hosted
    version: "5.2.1"
  audioplayers_android:
    dependency: transitive
    description:
      name: audioplayers_android
      sha256: b00e1a0e11365d88576320ec2d8c192bc21f1afb6c0e5995d1c57ae63156acb5
      url: "https://pub.dev"
    source: hosted
    version: "4.0.3"
  audioplayers_darwin:
    dependency: transitive
    description:
      name: audioplayers_darwin
      sha256: "3034e99a6df8d101da0f5082dcca0a2a99db62ab1d4ddb3277bed3f6f81afe08"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.2"
  audioplayers_linux:
    dependency: transitive
    description:
      name: audioplayers_linux
      sha256: "60787e73fefc4d2e0b9c02c69885402177e818e4e27ef087074cf27c02246c9e"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  audioplayers_platform_interface:
    dependency: transitive
    description:
      name: audioplayers_platform_interface
      sha256: "365c547f1bb9e77d94dd1687903a668d8f7ac3409e48e6e6a3668a1ac2982adb"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.0"
  audioplayers_web:
    dependency: transitive
    description:
      name: audioplayers_web
      sha256: "22cd0173e54d92bd9b2c80b1204eb1eb159ece87475ab58c9788a70ec43c2a62"
      url: "https://pub.dev"
    source: hosted
    version: "4.1.0"
  audioplayers_windows:
    dependency: transitive
    description:
      name: audioplayers_windows
      sha256: "9536812c9103563644ada2ef45ae523806b0745f7a78e89d1b5fb1951de90e1a"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  back_button_interceptor:
    dependency: transitive
    description:
      name: back_button_interceptor
      sha256: b85977faabf4aeb95164b3b8bf81784bed4c54ea1aef90a036ab6927ecf80c5a
      url: "https://pub.dev"
    source: hosted
    version: "8.0.4"
  bloc:
    dependency: transitive
    description:
      name: bloc
      sha256: "106842ad6569f0b60297619e9e0b1885c2fb9bf84812935490e6c5275777804e"
      url: "https://pub.dev"
    source: hosted
    version: "8.1.4"
  blur_bottom_bar:
    dependency: transitive
    description:
      name: blur_bottom_bar
      sha256: c675cc13a7df9374cfd7b305dfef1d4b089c6ec5aaa31fd4a1f77ae86f363afc
      url: "https://pub.dev"
    source: hosted
    version: "1.0.3"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      sha256: "6cfb5af12253eaf2b368f07bacc5a80d1301a071c73360d746b7f2e32d762c66"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  built_collection:
    dependency: transitive
    description:
      name: built_collection
      sha256: "376e3dd27b51ea877c28d525560790aee2e6fbb5f20e2f85d5081027d94e2100"
      url: "https://pub.dev"
    source: hosted
    version: "5.1.1"
  cached_network_image:
    dependency: transitive
    description:
      name: cached_network_image
      sha256: "28ea9690a8207179c319965c13cd8df184d5ee721ae2ce60f398ced1219cea1f"
      url: "https://pub.dev"
    source: hosted
    version: "3.3.1"
  cached_network_image_platform_interface:
    dependency: transitive
    description:
      name: cached_network_image_platform_interface
      sha256: "9e90e78ae72caa874a323d78fa6301b3fb8fa7ea76a8f96dc5b5bf79f283bf2f"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0"
  cached_network_image_web:
    dependency: transitive
    description:
      name: cached_network_image_web
      sha256: "205d6a9f1862de34b93184f22b9d2d94586b2f05c581d546695e3d8f6a805cd7"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  carousel_slider:
    dependency: transitive
    description:
      path: "."
      ref: HEAD
      resolved-ref: "2c3cf5503782fd4b60033667eb27695919318ce5"
      url: "https://github.com/curefit/flutter_carousel_slider.git"
    source: git
    version: "4.0.0"
  carp_serializable:
    dependency: transitive
    description:
      name: carp_serializable
      sha256: f039f8ea22e9437aef13fe7e9743c3761c76d401288dcb702eadd273c3e4dcef
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  characters:
    dependency: transitive
    description:
      name: characters
      sha256: f71061c654a3380576a52b451dd5532377954cf9dbd272a78fc8479606670803
      url: "https://pub.dev"
    source: hosted
    version: "1.4.0"
  clock:
    dependency: transitive
    description:
      name: clock
      sha256: fddb70d9b5277016c77a80201021d40a2247104d9f4aa7bab7157b7e3f05b84b
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  collection:
    dependency: transitive
    description:
      name: collection
      sha256: "2f5709ae4d3d59dd8f7cd309b4e023046b57d8a6c82130785d2b0e5868084e76"
      url: "https://pub.dev"
    source: hosted
    version: "1.19.1"
  common:
    dependency: transitive
    description:
      path: common
      ref: "common-v0.0.1951-firebase"
      resolved-ref: "36c32e2c5f11b8c249f04d5069973f9888b7aa93"
      url: "**************:curefit/flutter-packages.git"
    source: git
    version: "0.0.1951"
  connectivity_plus:
    dependency: transitive
    description:
      name: connectivity_plus
      sha256: "3f8fe4e504c2d33696dac671a54909743bc6a902a9bb0902306f7a2aed7e528e"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.9"
  connectivity_plus_linux:
    dependency: transitive
    description:
      name: connectivity_plus_linux
      sha256: "3caf859d001f10407b8e48134c761483e4495ae38094ffcca97193f6c271f5e2"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.1"
  connectivity_plus_macos:
    dependency: transitive
    description:
      name: connectivity_plus_macos
      sha256: "488d2de1e47e1224ad486e501b20b088686ba1f4ee9c4420ecbc3b9824f0b920"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.6"
  connectivity_plus_platform_interface:
    dependency: transitive
    description:
      name: connectivity_plus_platform_interface
      sha256: cf1d1c28f4416f8c654d7dc3cd638ec586076255d407cef3ddbdaf178272a71a
      url: "https://pub.dev"
    source: hosted
    version: "1.2.4"
  connectivity_plus_web:
    dependency: transitive
    description:
      name: connectivity_plus_web
      sha256: "81332be1b4baf8898fed17bb4fdef27abb7c6fd990bf98c54fd978478adf2f1a"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.5"
  connectivity_plus_windows:
    dependency: transitive
    description:
      name: connectivity_plus_windows
      sha256: "535b0404b4d5605c4dd8453d67e5d6d2ea0dd36e3b477f50f31af51b0aeab9dd"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.2"
  core:
    dependency: transitive
    description:
      path: "../../core"
      relative: true
    source: path
    version: "0.0.1"
  core_authentication:
    dependency: "direct main"
    description:
      path: ".."
      relative: true
    source: path
    version: "0.0.1"
  cross_file:
    dependency: transitive
    description:
      name: cross_file
      sha256: "7caf6a750a0c04effbb52a676dce9a4a592e10ad35c34d6d2d0e4811160d5670"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.4+2"
  crypto:
    dependency: transitive
    description:
      name: crypto
      sha256: "1e445881f28f22d6140f181e07737b22f1e099a5e1ff94b0af2f9e4a463f4855"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.6"
  csslib:
    dependency: transitive
    description:
      name: csslib
      sha256: "09bad715f418841f976c77db72d5398dc1253c21fb9c0c7f0b0b985860b2d58e"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  cupertino_icons:
    dependency: "direct main"
    description:
      name: cupertino_icons
      sha256: ba631d1c7f7bef6b729a622b7b752645a2d076dba9976925b8f25725a30e1ee6
      url: "https://pub.dev"
    source: hosted
    version: "1.0.8"
  dash_chat_2:
    dependency: transitive
    description:
      path: "packages/Dash-Chat-2"
      ref: e3fd64db35c2fe56200ed06e33c66e07d33d66c4
      resolved-ref: e3fd64db35c2fe56200ed06e33c66e07d33d66c4
      url: "**************:curefit/flutter-plugins.git"
    source: git
    version: "0.0.6"
  dbus:
    dependency: transitive
    description:
      name: dbus
      sha256: "365c771ac3b0e58845f39ec6deebc76e3276aa9922b0cc60840712094d9047ac"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.10"
  device_apps:
    dependency: transitive
    description:
      name: device_apps
      sha256: e84dc74d55749993fd671148cc0bd53096e1be0c268fc364285511b1d8a4c19b
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  device_calendar:
    dependency: transitive
    description:
      path: "."
      ref: "76a731a0815bb83c4e07d945584b3c47506e8684"
      resolved-ref: "76a731a0815bb83c4e07d945584b3c47506e8684"
      url: "**************:curefit/device_calendar.git"
    source: git
    version: "4.3.1"
  device_info_plus:
    dependency: transitive
    description:
      name: device_info_plus
      sha256: a7fd703482b391a87d60b6061d04dfdeab07826b96f9abd8f5ed98068acc0074
      url: "https://pub.dev"
    source: hosted
    version: "10.1.2"
  device_info_plus_platform_interface:
    dependency: transitive
    description:
      name: device_info_plus_platform_interface
      sha256: "282d3cf731045a2feb66abfe61bbc40870ae50a3ed10a4d3d217556c35c8c2ba"
      url: "https://pub.dev"
    source: hosted
    version: "7.0.1"
  dotted_border:
    dependency: transitive
    description:
      name: dotted_border
      sha256: "108837e11848ca776c53b30bc870086f84b62ed6e01c503ed976e8f8c7df9c04"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  easy_debounce:
    dependency: transitive
    description:
      name: easy_debounce
      sha256: f082609cfb8f37defb9e37fc28bc978c6712dedf08d4c5a26f820fa10165a236
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
  enum_to_string:
    dependency: transitive
    description:
      name: enum_to_string
      sha256: bd9e83a33b754cb43a75b36a9af2a0b92a757bfd9847d2621ca0b1bed45f8e7a
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  equatable:
    dependency: transitive
    description:
      name: equatable
      sha256: c2b87cb7756efdf69892005af546c56c0b5037f54d2a88269b4f347a505e3ca2
      url: "https://pub.dev"
    source: hosted
    version: "2.0.5"
  expand_tap_area:
    dependency: transitive
    description:
      name: expand_tap_area
      sha256: b3e178b5036a3eaa7950e2d02a01c536f34e5c1eb953dc0b3c15f06b1c9f75ac
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      sha256: "5368f224a74523e8d2e7399ea1638b37aecfca824a3cc4dfdf77bf1fa905ac44"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.3"
  ffi:
    dependency: transitive
    description:
      name: ffi
      sha256: "16ed7b077ef01ad6170a3d0c57caa4a112a38d7a2ed5602e0aca9ca6f3d98da6"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.3"
  file:
    dependency: transitive
    description:
      name: file
      sha256: "1b92bec4fc2a72f59a8e15af5f52cd441e4a7860b49499d69dfa817af20e925d"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.4"
  firebase_analytics:
    dependency: transitive
    description:
      name: firebase_analytics
      sha256: dbf1e7ab22cfb1f4a4adb103b46a26276b4edc593d4a78ef6fb942bafc92e035
      url: "https://pub.dev"
    source: hosted
    version: "10.10.7"
  firebase_analytics_platform_interface:
    dependency: transitive
    description:
      name: firebase_analytics_platform_interface
      sha256: "3729b74f8cf1d974a27ba70332ecb55ff5ff560edc8164a6469f4a055b429c37"
      url: "https://pub.dev"
    source: hosted
    version: "3.10.8"
  firebase_analytics_web:
    dependency: transitive
    description:
      name: firebase_analytics_web
      sha256: "019cd7eee74254d33fbd2e29229367ce33063516bf6b3258a341d89e3b0f1655"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.7+7"
  firebase_core:
    dependency: transitive
    description:
      name: firebase_core
      sha256: "26de145bb9688a90962faec6f838247377b0b0d32cc0abecd9a4e43525fc856c"
      url: "https://pub.dev"
    source: hosted
    version: "2.32.0"
  firebase_core_platform_interface:
    dependency: transitive
    description:
      name: firebase_core_platform_interface
      sha256: e30da58198a6d4b49d5bce4e852f985c32cb10db329ebef9473db2b9f09ce810
      url: "https://pub.dev"
    source: hosted
    version: "5.3.0"
  firebase_core_web:
    dependency: transitive
    description:
      name: firebase_core_web
      sha256: f967a7138f5d2ffb1ce15950e2a382924239eaa521150a8f144af34e68b3b3e5
      url: "https://pub.dev"
    source: hosted
    version: "2.18.1"
  firebase_crashlytics:
    dependency: transitive
    description:
      name: firebase_crashlytics
      sha256: "9897c01efaa950d2f6da8317d12452749a74dc45f33b46390a14cfe28067f271"
      url: "https://pub.dev"
    source: hosted
    version: "3.5.7"
  firebase_crashlytics_platform_interface:
    dependency: transitive
    description:
      name: firebase_crashlytics_platform_interface
      sha256: "16a71e08fbf6e00382816e1b13397898c29a54fa0ad969c2c2a3b82a704877f0"
      url: "https://pub.dev"
    source: hosted
    version: "3.6.35"
  firebase_messaging:
    dependency: transitive
    description:
      name: firebase_messaging
      sha256: "980259425fa5e2afc03e533f33723335731d21a56fd255611083bceebf4373a8"
      url: "https://pub.dev"
    source: hosted
    version: "14.7.10"
  firebase_messaging_platform_interface:
    dependency: transitive
    description:
      name: firebase_messaging_platform_interface
      sha256: "87c4a922cb6f811cfb7a889bdbb3622702443c52a0271636cbc90d813ceac147"
      url: "https://pub.dev"
    source: hosted
    version: "4.5.37"
  firebase_messaging_web:
    dependency: transitive
    description:
      name: firebase_messaging_web
      sha256: "90dc7ed885e90a24bb0e56d661d4d2b5f84429697fd2cbb9e5890a0ca370e6f4"
      url: "https://pub.dev"
    source: hosted
    version: "3.5.18"
  fixnum:
    dependency: transitive
    description:
      name: fixnum
      sha256: b6dc7065e46c974bc7c5f143080a6764ec7a4be6da1285ececdc37be96de53be
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  fl_chart:
    dependency: transitive
    description:
      name: fl_chart
      sha256: "00b74ae680df6b1135bdbea00a7d1fc072a9180b7c3f3702e4b19a9943f5ed7d"
      url: "https://pub.dev"
    source: hosted
    version: "0.66.2"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_bloc:
    dependency: "direct main"
    description:
      name: flutter_bloc
      sha256: b594505eac31a0518bdcb4b5b79573b8d9117b193cc80cc12e17d639b10aa27a
      url: "https://pub.dev"
    source: hosted
    version: "8.1.6"
  flutter_blue_plus:
    dependency: transitive
    description:
      name: flutter_blue_plus
      sha256: "6003c12eb254e948c8eb1356685663a72604c6878c23005c598e50b99896d732"
      url: "https://pub.dev"
    source: hosted
    version: "1.27.6"
  flutter_cache_manager:
    dependency: transitive
    description:
      name: flutter_cache_manager
      sha256: "8207f27539deb83732fdda03e259349046a39a4c767269285f449ade355d54ba"
      url: "https://pub.dev"
    source: hosted
    version: "3.3.1"
  flutter_contacts:
    dependency: transitive
    description:
      name: flutter_contacts
      sha256: "388d32cd33f16640ee169570128c933b45f3259bddbfae7a100bb49e5ffea9ae"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.9+2"
  flutter_exif_rotation:
    dependency: transitive
    description:
      name: flutter_exif_rotation
      sha256: f581601523bbb7fbec3a0498fe930bf6c48f627d4548d859b2772230edfbe658
      url: "https://pub.dev"
    source: hosted
    version: "0.5.2"
  flutter_facebook_auth:
    dependency: transitive
    description:
      name: flutter_facebook_auth
      sha256: fd1a6749dafbd5923585038671b63abdcedd4fe5923eb42fc154247dc5622519
      url: "https://pub.dev"
    source: hosted
    version: "6.0.4"
  flutter_facebook_auth_platform_interface:
    dependency: transitive
    description:
      name: flutter_facebook_auth_platform_interface
      sha256: "86630c4dbba1c20fba26ea9e59ad0d48f5ff59e7373cacd36f916160186f9ce9"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  flutter_facebook_auth_web:
    dependency: transitive
    description:
      name: flutter_facebook_auth_web
      sha256: "22dca8091409309ad85b9f430fbd8f57b686276979da5195e7e97587352567ce"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  flutter_js:
    dependency: transitive
    description:
      name: flutter_js
      sha256: d19cde4bad2f7c301ff5f69d7b3452ff91f2ca89d153569dd03e544579d8610d
      url: "https://pub.dev"
    source: hosted
    version: "0.8.1"
  flutter_keyboard_visibility:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility
      sha256: "5c5c2bf049e0f8b61c6bb6c317e3bf6142d238ab6711841f829e7432c568cc00"
      url: "https://pub.dev"
    source: hosted
    version: "5.2.0"
  flutter_keyboard_visibility_platform_interface:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility_platform_interface
      sha256: e43a89845873f7be10cb3884345ceb9aebf00a659f479d1c8f4293fcb37022a4
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  flutter_keyboard_visibility_web:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility_web
      sha256: d3771a2e752880c79203f8d80658401d0c998e4183edca05a149f5098ce6e3d1
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  flutter_lints:
    dependency: "direct dev"
    description:
      name: flutter_lints
      sha256: "9e8c3858111da373efc5aa341de011d9bd23e2c5c5e0c62bccf32438e192d7b1"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.2"
  flutter_markdown:
    dependency: transitive
    description:
      name: flutter_markdown
      sha256: "04c4722cc36ec5af38acc38ece70d22d3c2123c61305d555750a091517bbe504"
      url: "https://pub.dev"
    source: hosted
    version: "0.6.23"
  flutter_native_timezone:
    dependency: transitive
    description:
      path: "."
      ref: a9669f7e84fb704dc85dc7c88f771f36d52bf318
      resolved-ref: a9669f7e84fb704dc85dc7c88f771f36d52bf318
      url: "**************:curefit/flutter_native_timezone.git"
    source: git
    version: "2.0.1"
  flutter_parsed_text:
    dependency: transitive
    description:
      name: flutter_parsed_text
      sha256: "529cf5793b7acdf16ee0f97b158d0d4ba0bf06e7121ef180abe1a5b59e32c1e2"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  flutter_platform_alert:
    dependency: transitive
    description:
      name: flutter_platform_alert
      sha256: "29a27c81660468bfb7746bc78205f79e07f18ca785e0aeaf70eac28d7a011edb"
      url: "https://pub.dev"
    source: hosted
    version: "0.6.1"
  flutter_plugin_android_lifecycle:
    dependency: transitive
    description:
      name: flutter_plugin_android_lifecycle
      sha256: "9ee02950848f61c4129af3d6ec84a1cfc0e47931abc746b03e7a3bc3e8ff6eda"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.22"
  flutter_spinkit:
    dependency: transitive
    description:
      name: flutter_spinkit
      sha256: d2696eed13732831414595b98863260e33e8882fc069ee80ec35d4ac9ddb0472
      url: "https://pub.dev"
    source: hosted
    version: "5.2.1"
  flutter_staggered_animations:
    dependency: transitive
    description:
      name: flutter_staggered_animations
      sha256: "81d3c816c9bb0dca9e8a5d5454610e21ffb068aedb2bde49d2f8d04f75538351"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  flutter_svg:
    dependency: transitive
    description:
      name: flutter_svg
      sha256: "6ff9fa12892ae074092de2fa6a9938fb21dbabfdaa2ff57dc697ff912fc8d4b2"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.6"
  flutter_test:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_tindercard:
    dependency: transitive
    description:
      path: "."
      ref: d070b02cef050abc1055dfd25873975acb234b4c
      resolved-ref: d070b02cef050abc1055dfd25873975acb234b4c
      url: "**************:curefit/flutter_tindercard.git"
    source: git
    version: "0.2.0"
  flutter_typeahead:
    dependency: transitive
    description:
      name: flutter_typeahead
      sha256: "612a9c08554d49de02106dd06a666702e02347fe31cff47d41cef2a6a88dd01f"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.7"
  flutter_web_plugins:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_widget_from_html_core:
    dependency: transitive
    description:
      name: flutter_widget_from_html_core
      sha256: cc1d9be3d187ce668ee02091cd5442dfb050cdaf98e0ab9a4d12ad008f966979
      url: "https://pub.dev"
    source: hosted
    version: "0.14.12"
  focus_detector_v2:
    dependency: transitive
    description:
      name: focus_detector_v2
      sha256: d4abc4c755ba894238ab92f42f6eee7ade78aa285199e112f45926c7053f90c6
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0+1"
  gap:
    dependency: transitive
    description:
      name: gap
      sha256: f19387d4e32f849394758b91377f9153a1b41d79513ef7668c088c77dbc6955d
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  geolocator:
    dependency: transitive
    description:
      name: geolocator
      sha256: d2ec66329cab29cb297d51d96c067d457ca519dca8589665fa0b82ebacb7dbe4
      url: "https://pub.dev"
    source: hosted
    version: "13.0.2"
  geolocator_android:
    dependency: transitive
    description:
      name: geolocator_android
      sha256: "7aefc530db47d90d0580b552df3242440a10fe60814496a979aa67aa98b1fd47"
      url: "https://pub.dev"
    source: hosted
    version: "4.6.1"
  geolocator_apple:
    dependency: transitive
    description:
      name: geolocator_apple
      sha256: "6154ea2682563f69fc0125762ed7e91e7ed85d0b9776595653be33918e064807"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.8+1"
  geolocator_platform_interface:
    dependency: transitive
    description:
      name: geolocator_platform_interface
      sha256: "386ce3d9cce47838355000070b1d0b13efb5bc430f8ecda7e9238c8409ace012"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.4"
  geolocator_web:
    dependency: transitive
    description:
      name: geolocator_web
      sha256: "2ed69328e05cd94e7eb48bb0535f5fc0c0c44d1c4fa1e9737267484d05c29b5e"
      url: "https://pub.dev"
    source: hosted
    version: "4.1.1"
  geolocator_windows:
    dependency: transitive
    description:
      name: geolocator_windows
      sha256: "53da08937d07c24b0d9952eb57a3b474e29aae2abf9dd717f7e1230995f13f0e"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.3"
  google_identity_services_web:
    dependency: transitive
    description:
      name: google_identity_services_web
      sha256: "5be191523702ba8d7a01ca97c17fca096822ccf246b0a9f11923a6ded06199b6"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.1+4"
  google_sign_in:
    dependency: transitive
    description:
      name: google_sign_in
      sha256: "0b8787cb9c1a68ad398e8010e8c8766bfa33556d2ab97c439fb4137756d7308f"
      url: "https://pub.dev"
    source: hosted
    version: "6.2.1"
  google_sign_in_android:
    dependency: transitive
    description:
      name: google_sign_in_android
      sha256: "1ad54110a151d3ebbfe9be5a0c2b715aeabddb079e54efd84e56c49605b5474a"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.31"
  google_sign_in_ios:
    dependency: transitive
    description:
      name: google_sign_in_ios
      sha256: "83f015169102df1ab2905cf8abd8934e28f87db9ace7a5fa676998842fed228a"
      url: "https://pub.dev"
    source: hosted
    version: "5.7.8"
  google_sign_in_platform_interface:
    dependency: transitive
    description:
      name: google_sign_in_platform_interface
      sha256: "1f6e5787d7a120cc0359ddf315c92309069171306242e181c09472d1b00a2971"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.5"
  google_sign_in_web:
    dependency: transitive
    description:
      name: google_sign_in_web
      sha256: "042805a21127a85b0dc46bba98a37926f17d2439720e8a459d27045d8ef68055"
      url: "https://pub.dev"
    source: hosted
    version: "0.12.4+2"
  health:
    dependency: transitive
    description:
      path: "packages/health"
      ref: "336ea7b9e2c93726b9a84a5ffe010130c69e24d5"
      resolved-ref: "336ea7b9e2c93726b9a84a5ffe010130c69e24d5"
      url: "**************:curefit/Health.git"
    source: git
    version: "11.1.1"
  hive:
    dependency: transitive
    description:
      name: hive
      sha256: "8dcf6db979d7933da8217edcec84e9df1bdb4e4edc7fc77dbd5aa74356d6d941"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.3"
  html:
    dependency: transitive
    description:
      name: html
      sha256: "1fc58edeaec4307368c60d59b7e15b9d658b57d7f3125098b6294153c75337ec"
      url: "https://pub.dev"
    source: hosted
    version: "0.15.5"
  http:
    dependency: transitive
    description:
      name: http
      sha256: b9c29a161230ee03d3ccf545097fccd9b87a5264228c5d348202e0f0c28f9010
      url: "https://pub.dev"
    source: hosted
    version: "1.2.2"
  http_parser:
    dependency: transitive
    description:
      name: http_parser
      sha256: "2aa08ce0341cc9b354a498388e30986515406668dbcc4f7c950c3e715496693b"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.2"
  hydrated_bloc:
    dependency: transitive
    description:
      name: hydrated_bloc
      sha256: af35b357739fe41728df10bec03aad422cdc725a1e702e03af9d2a41ea05160c
      url: "https://pub.dev"
    source: hosted
    version: "9.1.5"
  image_cropper:
    dependency: transitive
    description:
      name: image_cropper
      sha256: fe37d9a129411486e0d93089b61bd326d05b89e78ad4981de54b560725bf5bd5
      url: "https://pub.dev"
    source: hosted
    version: "8.0.2"
  image_cropper_for_web:
    dependency: transitive
    description:
      name: image_cropper_for_web
      sha256: "34256c8fb7fcb233251787c876bb37271744459b593a948a2db73caa323034d0"
      url: "https://pub.dev"
    source: hosted
    version: "6.0.2"
  image_cropper_platform_interface:
    dependency: transitive
    description:
      name: image_cropper_platform_interface
      sha256: e8e9d2ca36360387aee39295ce49029362ae4df3071f23e8e71f2b81e40b7531
      url: "https://pub.dev"
    source: hosted
    version: "7.0.0"
  image_picker:
    dependency: transitive
    description:
      name: image_picker
      sha256: f3712cd190227fb92e0960cb0ce22928ba042c7183b16864ade83b259adf8ea6
      url: "https://pub.dev"
    source: hosted
    version: "0.8.5+3"
  image_picker_android:
    dependency: transitive
    description:
      name: image_picker_android
      sha256: "8c5abf0dcc24fe6e8e0b4a5c0b51a5cf30cefdf6407a3213dae61edc75a70f56"
      url: "https://pub.dev"
    source: hosted
    version: "0.8.12+12"
  image_picker_for_web:
    dependency: transitive
    description:
      name: image_picker_for_web
      sha256: "869fe8a64771b7afbc99fc433a5f7be2fea4d1cb3d7c11a48b6b579eb9c797f0"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  image_picker_ios:
    dependency: transitive
    description:
      name: image_picker_ios
      sha256: "4f0568120c6fcc0aaa04511cb9f9f4d29fc3d0139884b1d06be88dcec7641d6b"
      url: "https://pub.dev"
    source: hosted
    version: "0.8.12+1"
  image_picker_platform_interface:
    dependency: transitive
    description:
      name: image_picker_platform_interface
      sha256: "9ec26d410ff46f483c5519c29c02ef0e02e13a543f882b152d4bfd2f06802f80"
      url: "https://pub.dev"
    source: hosted
    version: "2.10.0"
  in_app_review:
    dependency: transitive
    description:
      name: in_app_review
      sha256: "99869244d09adc76af16bf8fd731dd13cef58ecafd5917847589c49f378cbb30"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.9"
  in_app_review_platform_interface:
    dependency: transitive
    description:
      name: in_app_review_platform_interface
      sha256: fed2c755f2125caa9ae10495a3c163aa7fab5af3585a9c62ef4a6920c5b45f10
      url: "https://pub.dev"
    source: hosted
    version: "2.0.5"
  infinite_listview:
    dependency: transitive
    description:
      name: infinite_listview
      sha256: f6062c1720eb59be553dfa6b89813d3e8dd2f054538445aaa5edaddfa5195ce6
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  intl:
    dependency: transitive
    description:
      name: intl
      sha256: "3bc132a9dbce73a7e4a21a17d06e1878839ffbf975568bc875c60537824b0c4d"
      url: "https://pub.dev"
    source: hosted
    version: "0.18.1"
  js:
    dependency: transitive
    description:
      name: js
      sha256: f2c445dce49627136094980615a031419f7f3eb393237e4ecd97ac15dea343f3
      url: "https://pub.dev"
    source: hosted
    version: "0.6.7"
  json_annotation:
    dependency: transitive
    description:
      name: json_annotation
      sha256: "1ce844379ca14835a50d2f019a3099f419082cfdd231cd86a142af94dd5c6bb1"
      url: "https://pub.dev"
    source: hosted
    version: "4.9.0"
  leak_tracker:
    dependency: transitive
    description:
      name: leak_tracker
      sha256: "8dcda04c3fc16c14f48a7bb586d4be1f0d1572731b6d81d51772ef47c02081e0"
      url: "https://pub.dev"
    source: hosted
    version: "11.0.1"
  leak_tracker_flutter_testing:
    dependency: transitive
    description:
      name: leak_tracker_flutter_testing
      sha256: "1dbc140bb5a23c75ea9c4811222756104fbcd1a27173f0c34ca01e16bea473c1"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.10"
  leak_tracker_testing:
    dependency: transitive
    description:
      name: leak_tracker_testing
      sha256: "8d5a2d49f4a66b49744b23b018848400d23e54caf9463f4eb20df3eb8acb2eb1"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.2"
  lints:
    dependency: transitive
    description:
      name: lints
      sha256: cbf8d4b858bb0134ef3ef87841abdf8d63bfc255c266b7bf6b39daa1085c4290
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  loader_overlay:
    dependency: transitive
    description:
      name: loader_overlay
      sha256: f3dd16e26cf705f64dd6e8ad36b1b6c15c1f58cb6ecf1f6edeb259f296eb4774
      url: "https://pub.dev"
    source: hosted
    version: "4.0.4+1"
  logger:
    dependency: transitive
    description:
      name: logger
      sha256: "697d067c60c20999686a0add96cf6aba723b3aa1f83ecf806a8097231529ec32"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.0"
  logging:
    dependency: transitive
    description:
      name: logging
      sha256: c8245ada5f1717ed44271ed1c26b8ce85ca3228fd2ffdb75468ab01979309d61
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0"
  lottie:
    dependency: transitive
    description:
      path: "."
      ref: "v3.0.0-alpha.3"
      resolved-ref: c7066ca444ddcb34705362be0c50b02924629b0d
      url: "**************:xvrh/lottie-flutter.git"
    source: git
    version: "3.0.0-alpha.3"
  markdown:
    dependency: transitive
    description:
      name: markdown
      sha256: ef2a1298144e3f985cc736b22e0ccdaf188b5b3970648f2d9dc13efd1d9df051
      url: "https://pub.dev"
    source: hosted
    version: "7.2.2"
  matcher:
    dependency: transitive
    description:
      name: matcher
      sha256: dc58c723c3c24bf8d3e2d3ad3f2f9d7bd9cf43ec6feaa64181775e60190153f2
      url: "https://pub.dev"
    source: hosted
    version: "0.12.17"
  material_color_utilities:
    dependency: transitive
    description:
      name: material_color_utilities
      sha256: f7142bb1154231d7ea5f96bc7bde4bda2a0945d2806bb11670e30b850d56bdec
      url: "https://pub.dev"
    source: hosted
    version: "0.11.1"
  math_expressions:
    dependency: transitive
    description:
      name: math_expressions
      sha256: e32d803d758ace61cc6c4bdfed1226ff60a6a23646b35685670d28b5616139f8
      url: "https://pub.dev"
    source: hosted
    version: "2.6.0"
  meta:
    dependency: transitive
    description:
      name: meta
      sha256: e3641ec5d63ebf0d9b41bd43201a66e3fc79a65db5f61fc181f04cd27aab950c
      url: "https://pub.dev"
    source: hosted
    version: "1.16.0"
  mime:
    dependency: transitive
    description:
      name: mime
      sha256: "801fd0b26f14a4a58ccb09d5892c3fbdeff209594300a542492cf13fba9d247a"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.6"
  mime_type:
    dependency: transitive
    description:
      name: mime_type
      sha256: d652b613e84dac1af28030a9fba82c0999be05b98163f9e18a0849c6e63838bb
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  modal_bottom_sheet:
    dependency: transitive
    description:
      name: modal_bottom_sheet
      sha256: eac66ef8cb0461bf069a38c5eb0fa728cee525a531a8304bd3f7b2185407c67e
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  navigation_history_observer:
    dependency: transitive
    description:
      name: navigation_history_observer
      sha256: "5e0b94ebda685ff763c4c04f832bd21d4441a00b7d9dd9f99a171d98eceebac4"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  nested:
    dependency: transitive
    description:
      name: nested
      sha256: "03bac4c528c64c95c722ec99280375a6f2fc708eec17c7b3f07253b626cd2a20"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  nhttp:
    dependency: transitive
    description:
      path: "."
      ref: "6bbaaa9ad8c53e28cd185f02dfb53b793e7c423c"
      resolved-ref: "6bbaaa9ad8c53e28cd185f02dfb53b793e7c423c"
      url: "**************:curefit/flutter-native-http.git"
    source: git
    version: "1.0.2"
  nm:
    dependency: transitive
    description:
      name: nm
      sha256: "2c9aae4127bdc8993206464fcc063611e0e36e72018696cd9631023a31b24254"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.0"
  no_screenshot:
    dependency: transitive
    description:
      name: no_screenshot
      sha256: ec3d86d7ee89a09c3a3939c1003012536ba4b3fcb4f8cbd23d87ada595c99258
      url: "https://pub.dev"
    source: hosted
    version: "0.3.1"
  numberpicker:
    dependency: transitive
    description:
      name: numberpicker
      sha256: "4c129154944b0f6b133e693f8749c3f8bfb67c4d07ef9dcab48b595c22d1f156"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  octo_image:
    dependency: transitive
    description:
      name: octo_image
      sha256: "34faa6639a78c7e3cbe79be6f9f96535867e879748ade7d17c9b1ae7536293bd"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  outline_gradient_button:
    dependency: transitive
    description:
      name: outline_gradient_button
      sha256: "006673015f090c314d7a14b41216ed984b21e8b55d0c8eb502d93e0bbd6b65ba"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1+1"
  package_info_plus:
    dependency: transitive
    description:
      path: "packages/package_info_plus/package_info_plus"
      ref: crashlytics-fix
      resolved-ref: a6bf23e4e564a2b4ad8a45d4e4a676feb2984ad1
      url: "**************:curefit/plus_plugins.git"
    source: git
    version: "5.0.1"
  package_info_plus_platform_interface:
    dependency: transitive
    description:
      name: package_info_plus_platform_interface
      sha256: "9bc8ba46813a4cc42c66ab781470711781940780fd8beddd0c3da62506d3a6c6"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  path:
    dependency: transitive
    description:
      name: path
      sha256: "75cca69d1490965be98c73ceaea117e8a04dd21217b37b292c9ddbec0d955bc5"
      url: "https://pub.dev"
    source: hosted
    version: "1.9.1"
  path_drawing:
    dependency: transitive
    description:
      name: path_drawing
      sha256: bbb1934c0cbb03091af082a6389ca2080345291ef07a5fa6d6e078ba8682f977
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  path_parsing:
    dependency: transitive
    description:
      name: path_parsing
      sha256: "883402936929eac138ee0a45da5b0f2c80f89913e6dc3bf77eb65b84b409c6ca"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  path_provider:
    dependency: transitive
    description:
      name: path_provider
      sha256: "50c5dd5b6e1aaf6fb3a78b33f6aa3afca52bf903a8a5298f53101fdaee55bbcd"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.5"
  path_provider_android:
    dependency: transitive
    description:
      name: path_provider_android
      sha256: "6f01f8e37ec30b07bc424b4deabac37cacb1bc7e2e515ad74486039918a37eb7"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.10"
  path_provider_foundation:
    dependency: transitive
    description:
      name: path_provider_foundation
      sha256: f234384a3fdd67f989b4d54a5d73ca2a6c422fa55ae694381ae0f4375cd1ea16
      url: "https://pub.dev"
    source: hosted
    version: "2.4.0"
  path_provider_linux:
    dependency: transitive
    description:
      name: path_provider_linux
      sha256: f7a1fe3a634fe7734c8d3f2766ad746ae2a2884abe22e241a8b301bf5cac3279
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  path_provider_platform_interface:
    dependency: transitive
    description:
      name: path_provider_platform_interface
      sha256: "88f5779f72ba699763fa3a3b06aa4bf6de76c8e5de842cf6f29e2e06476c2334"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  path_provider_windows:
    dependency: transitive
    description:
      name: path_provider_windows
      sha256: bd6f00dbd873bfb70d0761682da2b3a2c2fccc2b9e84c495821639601d81afe7
      url: "https://pub.dev"
    source: hosted
    version: "2.3.0"
  pedantic:
    dependency: transitive
    description:
      name: pedantic
      sha256: "67fc27ed9639506c856c840ccce7594d0bdcd91bc8d53d6e52359449a1d50602"
      url: "https://pub.dev"
    source: hosted
    version: "1.11.1"
  permission_handler:
    dependency: transitive
    description:
      name: permission_handler
      sha256: bc56bfe9d3f44c3c612d8d393bd9b174eb796d706759f9b495ac254e4294baa5
      url: "https://pub.dev"
    source: hosted
    version: "10.4.5"
  permission_handler_android:
    dependency: transitive
    description:
      name: permission_handler_android
      sha256: "59c6322171c29df93a22d150ad95f3aa19ed86542eaec409ab2691b8f35f9a47"
      url: "https://pub.dev"
    source: hosted
    version: "10.3.6"
  permission_handler_apple:
    dependency: transitive
    description:
      name: permission_handler_apple
      sha256: "99e220bce3f8877c78e4ace901082fb29fa1b4ebde529ad0932d8d664b34f3f5"
      url: "https://pub.dev"
    source: hosted
    version: "9.1.4"
  permission_handler_platform_interface:
    dependency: transitive
    description:
      name: permission_handler_platform_interface
      sha256: "6760eb5ef34589224771010805bea6054ad28453906936f843a8cc4d3a55c4a4"
      url: "https://pub.dev"
    source: hosted
    version: "3.12.0"
  permission_handler_windows:
    dependency: transitive
    description:
      name: permission_handler_windows
      sha256: cc074aace208760f1eee6aa4fae766b45d947df85bc831cde77009cdb4720098
      url: "https://pub.dev"
    source: hosted
    version: "0.1.3"
  petitparser:
    dependency: transitive
    description:
      name: petitparser
      sha256: c15605cd28af66339f8eb6fbe0e541bfe2d1b72d5825efc6598f3e0a31b9ad27
      url: "https://pub.dev"
    source: hosted
    version: "6.0.2"
  phone_number:
    dependency: transitive
    description:
      name: phone_number
      sha256: c66d3e2f2c69ce95f10bb923957049b1e5b8c6efea02d7366353fa3b80d4338a
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  photo_view:
    dependency: transitive
    description:
      name: photo_view
      sha256: "8036802a00bae2a78fc197af8a158e3e2f7b500561ed23b4c458107685e645bb"
      url: "https://pub.dev"
    source: hosted
    version: "0.14.0"
  pin_code_fields:
    dependency: transitive
    description:
      name: pin_code_fields
      sha256: "4c0db7fbc889e622e7c71ea54b9ee624bb70c7365b532abea0271b17ea75b729"
      url: "https://pub.dev"
    source: hosted
    version: "8.0.1"
  pin_input_text_field:
    dependency: transitive
    description:
      name: pin_input_text_field
      sha256: f45683032283d30b670ec343781660655e3e1953438b281a0bc6e2d358486236
      url: "https://pub.dev"
    source: hosted
    version: "4.5.2"
  platform:
    dependency: transitive
    description:
      name: platform
      sha256: "9b71283fc13df574056616011fb138fd3b793ea47cc509c189a6c3fa5f8a1a65"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.5"
  plugin_platform_interface:
    dependency: transitive
    description:
      name: plugin_platform_interface
      sha256: "4820fbfdb9478b1ebae27888254d445073732dae3d6ea81f0b7e06d5dedc3f02"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.8"
  popover:
    dependency: transitive
    description:
      name: popover
      sha256: "6a0928ccdcf12d46b407372b644a0d94400b316d0ee072a19dcef03c2bb88c3f"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.9"
  provider:
    dependency: transitive
    description:
      name: provider
      sha256: c8a055ee5ce3fd98d6fc872478b03823ffdb448699c6ebdbbc71d59b596fd48c
      url: "https://pub.dev"
    source: hosted
    version: "6.1.2"
  qr:
    dependency: transitive
    description:
      name: qr
      sha256: "5c4208b4dc0d55c3184d10d83ee0ded6212dc2b5e2ba17c5a0c0aab279128d21"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  qr_flutter:
    dependency: transitive
    description:
      name: qr_flutter
      sha256: c5c121c54cb6dd837b9b9d57eb7bc7ec6df4aee741032060c8833a678c80b87e
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0"
  queue:
    dependency: transitive
    description:
      name: queue
      sha256: "9a41ecadc15db79010108c06eae229a45c56b18db699760f34e8c9ac9b831ff9"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0+2"
  readmore:
    dependency: transitive
    description:
      name: readmore
      sha256: "99c2483202f7c7e98c50834d72be2b119aefecf4497ca1960ae9d5f418eb1481"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  rrule:
    dependency: transitive
    description:
      name: rrule
      sha256: "74a11d8b62bfe491340ccc2f0d19a0369402a92952180a090bb8ac865d024904"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.16"
  rxdart:
    dependency: transitive
    description:
      name: rxdart
      sha256: "0c7c0cedd93788d996e33041ffecda924cc54389199cde4e6a34b440f50044cb"
      url: "https://pub.dev"
    source: hosted
    version: "0.27.7"
  screen_protector:
    dependency: transitive
    description:
      name: screen_protector
      sha256: "305fd157f6f0b210afe216e790022bfe469c3b1d1f2e0d3dcc5906cc9c49c3e9"
      url: "https://pub.dev"
    source: hosted
    version: "1.4.2+1"
  screenshot:
    dependency: transitive
    description:
      name: screenshot
      sha256: "63817697a7835e6ce82add4228e15d233b74d42975c143ad8cfe07009fab866b"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  scroll_to_index:
    dependency: transitive
    description:
      name: scroll_to_index
      sha256: b707546e7500d9f070d63e5acf74fd437ec7eeeb68d3412ef7b0afada0b4f176
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  share_plus:
    dependency: transitive
    description:
      name: share_plus
      sha256: "3ef39599b00059db0990ca2e30fca0a29d8b37aae924d60063f8e0184cf20900"
      url: "https://pub.dev"
    source: hosted
    version: "7.2.2"
  share_plus_platform_interface:
    dependency: transitive
    description:
      name: share_plus_platform_interface
      sha256: "251eb156a8b5fa9ce033747d73535bf53911071f8d3b6f4f0b578505ce0d4496"
      url: "https://pub.dev"
    source: hosted
    version: "3.4.0"
  shared_preferences:
    dependency: transitive
    description:
      name: shared_preferences
      sha256: "746e5369a43170c25816cc472ee016d3a66bc13fcf430c0bc41ad7b4b2922051"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  shared_preferences_android:
    dependency: transitive
    description:
      name: shared_preferences_android
      sha256: "480ba4345773f56acda9abf5f50bd966f581dac5d514e5fc4a18c62976bbba7e"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  shared_preferences_foundation:
    dependency: transitive
    description:
      name: shared_preferences_foundation
      sha256: c4b35f6cb8f63c147312c054ce7c2254c8066745125264f0c88739c417fc9d9f
      url: "https://pub.dev"
    source: hosted
    version: "2.5.2"
  shared_preferences_linux:
    dependency: transitive
    description:
      name: shared_preferences_linux
      sha256: "580abfd40f415611503cae30adf626e6656dfb2f0cee8f465ece7b6defb40f2f"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  shared_preferences_platform_interface:
    dependency: transitive
    description:
      name: shared_preferences_platform_interface
      sha256: "57cbf196c486bc2cf1f02b85784932c6094376284b3ad5779d1b1c6c6a816b80"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  shared_preferences_web:
    dependency: transitive
    description:
      name: shared_preferences_web
      sha256: d2ca4132d3946fec2184261726b355836a82c33d7d5b67af32692aff18a4684e
      url: "https://pub.dev"
    source: hosted
    version: "2.4.2"
  shared_preferences_windows:
    dependency: transitive
    description:
      name: shared_preferences_windows
      sha256: "94ef0f72b2d71bc3e700e025db3710911bd51a71cefb65cc609dd0d9a982e3c1"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  shimmer:
    dependency: "direct overridden"
    description:
      name: shimmer
      sha256: "1f1009b5845a1f88f1c5630212279540486f97409e9fc3f63883e71070d107bf"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  sign_in_with_apple:
    dependency: transitive
    description:
      name: sign_in_with_apple
      sha256: "602f1374c9c4c33889c969b53ebf7cc8417c22cc7e25ea771581330173bc6603"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.3"
  sign_in_with_apple_platform_interface:
    dependency: transitive
    description:
      name: sign_in_with_apple_platform_interface
      sha256: c2ef2ce6273fce0c61acd7e9ff5be7181e33d7aa2b66508b39418b786cca2119
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  sign_in_with_apple_web:
    dependency: transitive
    description:
      name: sign_in_with_apple_web
      sha256: c009e9beeb6c376e86aaa154fcc8b4e075d4bad90c56286b9668a51cdb6129ea
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  sliding_up_panel:
    dependency: transitive
    description:
      name: sliding_up_panel
      sha256: "578e90956a6212d1e406373250b2436a0f3afece29aee3c24c8360094d6cf968"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0+1"
  sms_autofill:
    dependency: transitive
    description:
      name: sms_autofill
      sha256: c65836abe9c1f62ce411bb78d5546a09ece4297558070b1bd871db1db283aaf9
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  source_span:
    dependency: transitive
    description:
      name: source_span
      sha256: "53e943d4206a5e30df338fd4c6e7a077e02254531b138a15aec3bd143c1a8b3c"
      url: "https://pub.dev"
    source: hosted
    version: "1.10.0"
  sprintf:
    dependency: transitive
    description:
      name: sprintf
      sha256: "1fc9ffe69d4df602376b52949af107d8f5703b77cda567c4d7d86a0693120f23"
      url: "https://pub.dev"
    source: hosted
    version: "7.0.0"
  sqflite:
    dependency: transitive
    description:
      name: sqflite
      sha256: a43e5a27235518c03ca238e7b4732cf35eabe863a369ceba6cbefa537a66f16d
      url: "https://pub.dev"
    source: hosted
    version: "2.3.3+1"
  sqflite_common:
    dependency: transitive
    description:
      name: sqflite_common
      sha256: "3da423ce7baf868be70e2c0976c28a1bb2f73644268b7ffa7d2e08eab71f16a4"
      url: "https://pub.dev"
    source: hosted
    version: "2.5.4"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      sha256: "8b27215b45d22309b5cddda1aa2b19bdfec9df0e765f2de506401c071d38d1b1"
      url: "https://pub.dev"
    source: hosted
    version: "1.12.1"
  store_redirect:
    dependency: transitive
    description:
      name: store_redirect
      sha256: fa2f2b914ff5abef8083c0453bd4c9ee6eda1f0c87c13dfc33dde0ab23944055
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      sha256: "969e04c80b8bcdf826f8f16579c7b14d780458bd97f56d107d3950fdbeef059d"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      sha256: "556692adab6cfa87322a115640c11f13cb77b3f076ddcc5d6ae3c20242bedcde"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  sync_http:
    dependency: transitive
    description:
      name: sync_http
      sha256: "7f0cd72eca000d2e026bcd6f990b81d0ca06022ef4e32fb257b30d3d1014a961"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.1"
  syncfusion_flutter_core:
    dependency: transitive
    description:
      name: syncfusion_flutter_core
      sha256: "6e67726b85812afc7105725a23620b876ab7f6b04b8410e211330ffb8c2cdbe8"
      url: "https://pub.dev"
    source: hosted
    version: "26.2.14"
  syncfusion_flutter_gauges:
    dependency: transitive
    description:
      name: syncfusion_flutter_gauges
      sha256: "1962f55f6424484f6701b5bfc00733fa84f320d4228f3a9c059cc077e5efb02c"
      url: "https://pub.dev"
    source: hosted
    version: "26.2.14"
  synchronized:
    dependency: transitive
    description:
      name: synchronized
      sha256: "539ef412b170d65ecdafd780f924e5be3f60032a1128df156adad6c5b373d558"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0+1"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      sha256: a29248a84fbb7c79282b40b8c72a1209db169a2e0542bce341da992fe1bc7e84
      url: "https://pub.dev"
    source: hosted
    version: "1.2.1"
  test_api:
    dependency: transitive
    description:
      name: test_api
      sha256: "522f00f556e73044315fa4585ec3270f1808a4b186c936e612cab0b565ff1e00"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.6"
  time:
    dependency: transitive
    description:
      name: time
      sha256: ad8e018a6c9db36cb917a031853a1aae49467a93e0d464683e029537d848c221
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  timezone:
    dependency: transitive
    description:
      name: timezone
      sha256: "2236ec079a174ce07434e89fcd3fcda430025eb7692244139a9cf54fdcf1fc7d"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.4"
  top_modal_sheet:
    dependency: transitive
    description:
      name: top_modal_sheet
      sha256: "404249c2cec151a5872197601bce2913ff88bb3b714ddae0223756308e7ccf08"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  twilio_conversations:
    dependency: transitive
    description:
      path: "."
      ref: "717b4dc2273b821dbdb241a745e8e6146c6ea7e3"
      resolved-ref: "717b4dc2273b821dbdb241a745e8e6146c6ea7e3"
      url: "**************:curefit/twilio-flutter-conversations.git"
    source: git
    version: "0.1.4"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      sha256: facc8d6582f16042dd49f2463ff1bd6e2c9ef9f3d5da3d9b087e244a7b564b3c
      url: "https://pub.dev"
    source: hosted
    version: "1.3.2"
  url_launcher:
    dependency: transitive
    description:
      name: url_launcher
      sha256: f118f30f4301453d155c236a17d9ba53ec02a342591290aff91ae0fda4dc5428
      url: "https://pub.dev"
    source: hosted
    version: "6.1.3"
  url_launcher_android:
    dependency: transitive
    description:
      name: url_launcher_android
      sha256: f0c73347dfcfa5b3db8bc06e1502668265d39c08f310c29bff4e28eea9699f79
      url: "https://pub.dev"
    source: hosted
    version: "6.3.9"
  url_launcher_ios:
    dependency: transitive
    description:
      name: url_launcher_ios
      sha256: e43b677296fadce447e987a2f519dcf5f6d1e527dc35d01ffab4fff5b8a7063e
      url: "https://pub.dev"
    source: hosted
    version: "6.3.1"
  url_launcher_linux:
    dependency: transitive
    description:
      name: url_launcher_linux
      sha256: e2b9622b4007f97f504cd64c0128309dfb978ae66adbe944125ed9e1750f06af
      url: "https://pub.dev"
    source: hosted
    version: "3.2.0"
  url_launcher_macos:
    dependency: transitive
    description:
      name: url_launcher_macos
      sha256: "769549c999acdb42b8bcfa7c43d72bf79a382ca7441ab18a808e101149daf672"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.1"
  url_launcher_platform_interface:
    dependency: transitive
    description:
      name: url_launcher_platform_interface
      sha256: "552f8a1e663569be95a8190206a38187b531910283c3e982193e4f2733f01029"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  url_launcher_web:
    dependency: transitive
    description:
      name: url_launcher_web
      sha256: "772638d3b34c779ede05ba3d38af34657a05ac55b06279ea6edd409e323dca8e"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.3"
  url_launcher_windows:
    dependency: transitive
    description:
      name: url_launcher_windows
      sha256: "49c10f879746271804767cb45551ec5592cdab00ee105c06dddde1a98f73b185"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.2"
  uuid:
    dependency: transitive
    description:
      name: uuid
      sha256: a5be9ef6618a7ac1e964353ef476418026db906c4facdedaa299b7a2e71690ff
      url: "https://pub.dev"
    source: hosted
    version: "4.5.1"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      sha256: d530bd74fea330e6e364cda7a85019c434070188383e1cd8d9777ee586914c5b
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  video_player_cf:
    dependency: transitive
    description:
      path: "packages/video_player/video_player"
      ref: "9656e3ceae07a707b21f9997aa2c72261aa24c26"
      resolved-ref: "9656e3ceae07a707b21f9997aa2c72261aa24c26"
      url: "**************:curefit/flutter-plugins.git"
    source: git
    version: "2.2.10"
  video_player_platform_interface_cf:
    dependency: transitive
    description:
      path: "packages/video_player/video_player_platform_interface"
      ref: e1830f5cb4fb3aee289d88f5c48d04fe6fac94b7
      resolved-ref: e1830f5cb4fb3aee289d88f5c48d04fe6fac94b7
      url: "**************:curefit/flutter-plugins.git"
    source: git
    version: "5.0.0"
  video_player_web_cf:
    dependency: transitive
    description:
      path: "packages/video_player/video_player_web"
      ref: f51bbd641cd36040e6000fcec269dd27fb152168
      resolved-ref: f51bbd641cd36040e6000fcec269dd27fb152168
      url: "**************:curefit/flutter-plugins.git"
    source: git
    version: "2.0.5"
  visibility_detector:
    dependency: transitive
    description:
      name: visibility_detector
      sha256: dd5cc11e13494f432d15939c3aa8ae76844c42b723398643ce9addb88a5ed420
      url: "https://pub.dev"
    source: hosted
    version: "0.4.0+2"
  vm_service:
    dependency: transitive
    description:
      name: vm_service
      sha256: "3923c89304b715fb1eb6423f017651664a03bf5f4b29983627c4da791f74a4ec"
      url: "https://pub.dev"
    source: hosted
    version: "14.2.1"
  wakelock:
    dependency: transitive
    description:
      path: wakelock
      ref: "423db35ebc536e2f61ffef063567232aef24f9da"
      resolved-ref: "423db35ebc536e2f61ffef063567232aef24f9da"
      url: "**************:curefit/wakelock.git"
    source: git
    version: "0.6.2"
  wakelock_platform_interface:
    dependency: transitive
    description:
      name: wakelock_platform_interface
      sha256: "1f4aeb81fb592b863da83d2d0f7b8196067451e4df91046c26b54a403f9de621"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.0"
  web:
    dependency: transitive
    description:
      name: web
      sha256: cd3543bd5798f6ad290ea73d210f423502e71900302dde696f8bff84bf89a1cb
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  webview_flutter:
    dependency: transitive
    description:
      name: webview_flutter
      sha256: "6869c8786d179f929144b4a1f86e09ac0eddfe475984951ea6c634774c16b522"
      url: "https://pub.dev"
    source: hosted
    version: "4.8.0"
  webview_flutter_android:
    dependency: transitive
    description:
      name: webview_flutter_android
      sha256: ed021f27ae621bc97a6019fb601ab16331a3db4bf8afa305e9f6689bdb3edced
      url: "https://pub.dev"
    source: hosted
    version: "3.16.8"
  webview_flutter_platform_interface:
    dependency: transitive
    description:
      name: webview_flutter_platform_interface
      sha256: d937581d6e558908d7ae3dc1989c4f87b786891ab47bb9df7de548a151779d8d
      url: "https://pub.dev"
    source: hosted
    version: "2.10.0"
  webview_flutter_wkwebview:
    dependency: transitive
    description:
      name: webview_flutter_wkwebview
      sha256: "9c62cc46fa4f2d41e10ab81014c1de470a6c6f26051a2de32111b2ee55287feb"
      url: "https://pub.dev"
    source: hosted
    version: "3.14.0"
  win32:
    dependency: transitive
    description:
      name: win32
      sha256: "68d1e89a91ed61ad9c370f9f8b6effed9ae5e0ede22a270bdfa6daf79fc2290a"
      url: "https://pub.dev"
    source: hosted
    version: "5.5.4"
  win32_registry:
    dependency: transitive
    description:
      name: win32_registry
      sha256: "21ec76dfc731550fd3e2ce7a33a9ea90b828fdf19a5c3bcf556fa992cfa99852"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.5"
  xdg_directories:
    dependency: transitive
    description:
      name: xdg_directories
      sha256: faea9dee56b520b55a566385b84f2e8de55e7496104adada9962e0bd11bcff1d
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  xml:
    dependency: transitive
    description:
      name: xml
      sha256: b015a8ad1c488f66851d762d3090a21c600e479dc75e68328c52774040cf9226
      url: "https://pub.dev"
    source: hosted
    version: "6.5.0"
sdks:
  dart: ">=3.8.0-0 <4.0.0"
  flutter: ">=3.22.0"

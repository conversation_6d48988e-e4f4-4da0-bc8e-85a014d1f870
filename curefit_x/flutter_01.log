Flutter crash report.
Please report a bug at https://github.com/flutter/flutter/issues.

## command

flutter --no-color attach --machine --device-id=26305E7C-6EB0-4EF4-BE77-98D9DF9778A0 lib/main-dev-iso.dart

## exception

SocketException: SocketException: Send failed (OS Error: No route to host, errno = 65), address = 0.0.0.0, port = 5353

```
#0      _NativeSocket.send (dart:io-patch/socket_patch.dart:1269:34)
#1      _RawDatagramSocket.send (dart:io-patch/socket_patch.dart:2584:15)
#2      MDnsClient.lookup (package:multicast_dns/multicast_dns.dart:225:14)
#3      MDnsVmServiceDiscovery._pollingVmService (package:flutter_tools/src/mdns_discovery.dart:232:66)
<asynchronous suspension>
#4      MDnsVmServiceDiscovery.firstMatchingVmService (package:flutter_tools/src/mdns_discovery.dart:188:56)
<asynchronous suspension>
#5      MDnsVmServiceDiscovery.getVMServiceUriForAttach (package:flutter_tools/src/mdns_discovery.dart:397:50)
<asynchronous suspension>
#6      AttachCommand._attachToDevice (package:flutter_tools/src/commands/attach.dart:371:22)
<asynchronous suspension>
#7      AttachCommand.runCommand (package:flutter_tools/src/commands/attach.dart:267:5)
<asynchronous suspension>
#8      FlutterCommand.run.<anonymous closure> (package:flutter_tools/src/runner/flutter_command.dart:1394:27)
<asynchronous suspension>
#9      AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:153:19)
<asynchronous suspension>
#10     CommandRunner.runCommand (package:args/command_runner.dart:212:13)
<asynchronous suspension>
#11     FlutterCommandRunner.runCommand.<anonymous closure> (package:flutter_tools/src/runner/flutter_command_runner.dart:372:9)
<asynchronous suspension>
#12     AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:153:19)
<asynchronous suspension>
#13     FlutterCommandRunner.runCommand (package:flutter_tools/src/runner/flutter_command_runner.dart:308:5)
<asynchronous suspension>
#14     run.<anonymous closure>.<anonymous closure> (package:flutter_tools/runner.dart:130:9)
<asynchronous suspension>
#15     AppContext.run.<anonymous closure> (package:flutter_tools/src/base/context.dart:153:19)
<asynchronous suspension>
#16     main (package:flutter_tools/executable.dart:93:3)
<asynchronous suspension>
```

## flutter doctor

```
[✓] Flutter (Channel stable, 3.22.3, on macOS 14.4 23E214 darwin-arm64, locale en-IN)
    • Flutter version 3.22.3 on channel stable at /Users/<USER>/Development/flutter
    • Upstream repository https://github.com/flutter/flutter.git
    • Framework revision b0850beeb2 (3 months ago), 2024-07-16 21:43:41 -0700
    • Engine revision 235db911ba
    • Dart version 3.4.4
    • DevTools version 2.34.3

[!] Android toolchain - develop for Android devices (Android SDK version 35.0.0)
    • Android SDK at /Users/<USER>/Library/Android/sdk
    ✗ cmdline-tools component is missing
      Run `path/to/sdkmanager --install "cmdline-tools;latest"`
      See https://developer.android.com/studio/command-line for more details.
    ✗ Android license status unknown.
      Run `flutter doctor --android-licenses` to accept the SDK licenses.
      See https://flutter.dev/docs/get-started/install/macos#android-setup for more details.

[✓] Xcode - develop for iOS and macOS (Xcode 15.4)
    • Xcode at /Applications/Xcode.app/Contents/Developer
    • Build 15F31d
    • CocoaPods version 1.15.2

[✓] Chrome - develop for the web
    • Chrome at /Applications/Google Chrome.app/Contents/MacOS/Google Chrome

[✓] Android Studio (version 2024.2)
    • Android Studio at /Applications/Android Studio.app/Contents
    • Flutter plugin can be installed from:
      🔨 https://plugins.jetbrains.com/plugin/9212-flutter
    • Dart plugin can be installed from:
      🔨 https://plugins.jetbrains.com/plugin/6351-dart
    • Java version OpenJDK Runtime Environment (build 21.0.3+-79915917-b509.11)

[✓] VS Code (version 1.93.1)
    • VS Code at /Applications/Visual Studio Code.app/Contents
    • Flutter extension version 3.98.0

[✓] Connected device (4 available)
    • iPhone 15 Pro (mobile)          • 26305E7C-6EB0-4EF4-BE77-98D9DF9778A0 • ios            • com.apple.CoreSimulator.SimRuntime.iOS-17-5 (simulator)
    • macOS (desktop)                 • macos                                • darwin-arm64   • macOS 14.4 23E214 darwin-arm64
    • Mac Designed for iPad (desktop) • mac-designed-for-ipad                • darwin         • macOS 14.4 23E214 darwin-arm64
    • Chrome (web)                    • chrome                               • web-javascript • Google Chrome 129.0.6668.90

[✓] Network resources
    • All expected network resources are available.

! Doctor found issues in 1 category.
```

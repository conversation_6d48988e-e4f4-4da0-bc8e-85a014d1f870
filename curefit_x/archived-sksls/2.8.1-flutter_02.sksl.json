{"platform": "ios", "name": "<PERSON><PERSON><PERSON>’s iPhone", "engineRevision": "40a99c595137e4b2f5b2efa8ff343ea23c1e16b8", "data": {"E5QQAAAAMAAGEADCACRAAAAAMAACFQDBABRAAIXCAIAAAAAAHEAAAAAAAIAAAAAQGIAAAACQAAAAAAIAAAAAAAAAAAAAAAAAAAAAAAGDAEAAAAAAAAAA": "CAAAAExTS1O8DAAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0NCByYWRpaV9zZWxlY3RvcjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gZmxvYXQ0IGNvcm5lcl9hbmRfcmFkaXVzX291dHNldHM7CmxheW91dChsb2NhdGlvbiA9IDIpIGluIGZsb2F0NCBhYV9ibG9hdF9hbmRfY292ZXJhZ2U7CmxheW91dChsb2NhdGlvbiA9IDMpIGluIGZsb2F0NCBza2V3OwpsYXlvdXQobG9jYXRpb24gPSA0KSBpbiBmbG9hdDIgdHJhbnNsYXRlOwpsYXlvdXQobG9jYXRpb24gPSA1KSBpbiBmbG9hdDQgcmFkaWlfeDsKbGF5b3V0KGxvY2F0aW9uID0gNikgaW4gZmxvYXQ0IHJhZGlpX3k7CmxheW91dChsb2NhdGlvbiA9IDcpIGluIGhhbGY0IGNvbG9yOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IG91dCBoYWxmNCB2Y29sb3JfUzA7CmxheW91dChsb2NhdGlvbiA9IDEpIG91dCBmbG9hdDIgdmFyY2Nvb3JkX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gUHJpbWl0aXZlIFByb2Nlc3NvciBGaWxsUlJlY3RPcDo6UHJvY2Vzc29yCgl2Y29sb3JfUzAgPSBjb2xvcjsKCWZsb2F0IGFhX2Jsb2F0X211bHRpcGxpZXIgPSAxOwoJZmxvYXQyIGNvcm5lciA9IGNvcm5lcl9hbmRfcmFkaXVzX291dHNldHMueHk7CglmbG9hdDIgcmFkaXVzX291dHNldCA9IGNvcm5lcl9hbmRfcmFkaXVzX291dHNldHMuenc7CglmbG9hdDIgYWFfYmxvYXRfZGlyZWN0aW9uID0gYWFfYmxvYXRfYW5kX2NvdmVyYWdlLnh5OwoJZmxvYXQgaXNfbGluZWFyX2NvdmVyYWdlID0gYWFfYmxvYXRfYW5kX2NvdmVyYWdlLnc7CglmbG9hdDIgcGl4ZWxsZW5ndGggPSBpbnZlcnNlc3FydChmbG9hdDIoZG90KHNrZXcueHosIHNrZXcueHopLCBkb3Qoc2tldy55dywgc2tldy55dykpKTsKCWZsb2F0NCBub3JtYWxpemVkX2F4aXNfZGlycyA9IHNrZXcgKiBwaXhlbGxlbmd0aC54eXh5OwoJZmxvYXQyIGF4aXN3aWR0aHMgPSAoYWJzKG5vcm1hbGl6ZWRfYXhpc19kaXJzLnh5KSArIGFicyhub3JtYWxpemVkX2F4aXNfZGlycy56dykpOwoJZmxvYXQyIGFhX2Jsb2F0cmFkaXVzID0gYXhpc3dpZHRocyAqIHBpeGVsbGVuZ3RoICogLjU7CglmbG9hdDQgcmFkaWlfYW5kX25laWdoYm9ycyA9IHJhZGlpX3NlbGVjdG9yKiBmbG9hdDR4NChyYWRpaV94LCByYWRpaV95LCByYWRpaV94Lnl4d3osIHJhZGlpX3kud3p5eCk7CglmbG9hdDIgcmFkaWkgPSByYWRpaV9hbmRfbmVpZ2hib3JzLnh5OwoJZmxvYXQyIG5laWdoYm9yX3JhZGlpID0gcmFkaWlfYW5kX25laWdoYm9ycy56dzsKCWZsb2F0IGNvdmVyYWdlX211bHRpcGxpZXIgPSAxOwoJaWYgKGFueShncmVhdGVyVGhhbihhYV9ibG9hdHJhZGl1cywgZmxvYXQyKDEpKSkpIAoJewoJCWNvcm5lciA9IG1heChhYnMoY29ybmVyKSwgYWFfYmxvYXRyYWRpdXMpICogc2lnbihjb3JuZXIpOwoJCWNvdmVyYWdlX211bHRpcGxpZXIgPSAxIC8gKG1heChhYV9ibG9hdHJhZGl1cy54LCAxKSAqIG1heChhYV9ibG9hdHJhZGl1cy55LCAxKSk7CgkJcmFkaWkgPSBmbG9hdDIoMCk7Cgl9CglmbG9hdCBjb3ZlcmFnZSA9IGFhX2Jsb2F0X2FuZF9jb3ZlcmFnZS56OwoJaWYgKGFueShsZXNzVGhhbihyYWRpaSwgYWFfYmxvYXRyYWRpdXMgKiAxLjUpKSkgCgl7CgkJcmFkaWkgPSBmbG9hdDIoMCk7CgkJYWFfYmxvYXRfZGlyZWN0aW9uID0gc2lnbihjb3JuZXIpOwoJCWlmIChjb3ZlcmFnZSA+IC41KSAKCQl7CgkJCWFhX2Jsb2F0X2RpcmVjdGlvbiA9IC1hYV9ibG9hdF9kaXJlY3Rpb247CgkJfQoJCWlzX2xpbmVhcl9jb3ZlcmFnZSA9IDE7Cgl9CgllbHNlIAoJewoJCXJhZGlpID0gY2xhbXAocmFkaWksIHBpeGVsbGVuZ3RoICogMS41LCAyIC0gcGl4ZWxsZW5ndGggKiAxLjUpOwoJCW5laWdoYm9yX3JhZGlpID0gY2xhbXAobmVpZ2hib3JfcmFkaWksIHBpeGVsbGVuZ3RoICogMS41LCAyIC0gcGl4ZWxsZW5ndGggKiAxLjUpOwoJCWZsb2F0MiBzcGFjaW5nID0gMiAtIHJhZGlpIC0gbmVpZ2hib3JfcmFkaWk7CgkJZmxvYXQyIGV4dHJhX3BhZCA9IG1heChwaXhlbGxlbmd0aCAqIC4wNjI1IC0gc3BhY2luZywgZmxvYXQyKDApKTsKCQlyYWRpaSAtPSBleHRyYV9wYWQgKiAuNTsKCX0KCWZsb2F0MiBhYV9vdXRzZXQgPSBhYV9ibG9hdF9kaXJlY3Rpb24gKiBhYV9ibG9hdHJhZGl1cyAqIGFhX2Jsb2F0X211bHRpcGxpZXI7CglmbG9hdDIgdmVydGV4cG9zID0gY29ybmVyICsgcmFkaXVzX291dHNldCAqIHJhZGlpICsgYWFfb3V0c2V0OwoJaWYgKGNvdmVyYWdlID4gLjUpIAoJewoJCWlmIChhYV9ibG9hdF9kaXJlY3Rpb24ueCAhPSAwICYmIHZlcnRleHBvcy54ICogY29ybmVyLnggPCAwKSAKCQl7CgkJCWZsb2F0IGJhY2tzZXQgPSBhYnModmVydGV4cG9zLngpOwoJCQl2ZXJ0ZXhwb3MueCA9IDA7CgkJCXZlcnRleHBvcy55ICs9IGJhY2tzZXQgKiBzaWduKGNvcm5lci55KSAqIHBpeGVsbGVuZ3RoLnkvcGl4ZWxsZW5ndGgueDsKCQkJY292ZXJhZ2UgPSAoY292ZXJhZ2UgLSAuNSkgKiBhYnMoY29ybmVyLngpIC8gKGFicyhjb3JuZXIueCkgKyBiYWNrc2V0KSArIC41OwoJCX0KCQlpZiAoYWFfYmxvYXRfZGlyZWN0aW9uLnkgIT0gMCAmJiB2ZXJ0ZXhwb3MueSAqIGNvcm5lci55IDwgMCkgCgkJewoJCQlmbG9hdCBiYWNrc2V0ID0gYWJzKHZlcnRleHBvcy55KTsKCQkJdmVydGV4cG9zLnkgPSAwOwoJCQl2ZXJ0ZXhwb3MueCArPSBiYWNrc2V0ICogc2lnbihjb3JuZXIueCkgKiBwaXhlbGxlbmd0aC54L3BpeGVsbGVuZ3RoLnk7CgkJCWNvdmVyYWdlID0gKGNvdmVyYWdlIC0gLjUpICogYWJzKGNvcm5lci55KSAvIChhYnMoY29ybmVyLnkpICsgYmFja3NldCkgKyAuNTsKCQl9Cgl9CglmbG9hdDJ4MiBza2V3bWF0cml4ID0gZmxvYXQyeDIoc2tldy54eSwgc2tldy56dyk7CglmbG9hdDIgZGV2Y29vcmQgPSB2ZXJ0ZXhwb3MgKiBza2V3bWF0cml4ICsgdHJhbnNsYXRlOwoJaWYgKDAgIT0gaXNfbGluZWFyX2NvdmVyYWdlKSAKCXsKCQl2YXJjY29vcmRfUzAueHkgPSBmbG9hdDIoMCwgY292ZXJhZ2UgKiBjb3ZlcmFnZV9tdWx0aXBsaWVyKTsKCX0KCWVsc2UgCgl7CgkJZmxvYXQyIGFyY2Nvb3JkID0gMSAtIGFicyhyYWRpdXNfb3V0c2V0KSArIGFhX291dHNldC9yYWRpaSAqIGNvcm5lcjsKCQl2YXJjY29vcmRfUzAueHkgPSBmbG9hdDIoYXJjY29vcmQueCsxLCBhcmNjb29yZC55KTsKCX0KCXNrX1Bvc2l0aW9uID0gZGV2Y29vcmQueHkwMTsKfQoAAAAA/AIAAGxheW91dCAoYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gZmxvYXQyIHZhcmNjb29yZF9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMCwgaW5kZXggPSAwKSBvdXQgaGFsZjQgc2tfRnJhZ0NvbG9yOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgRmlsbFJSZWN0T3A6OlByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzA7CglvdXRwdXRDb2xvcl9TMCA9IHZjb2xvcl9TMDsKCWZsb2F0IHhfcGx1c18xPXZhcmNjb29yZF9TMC54LCB5PXZhcmNjb29yZF9TMC55OwoJaGFsZiBjb3ZlcmFnZTsKCWlmICgwID09IHhfcGx1c18xKSAKCXsKCQljb3ZlcmFnZSA9IGhhbGYoeSk7Cgl9CgllbHNlIAoJewoJCWZsb2F0IGZuID0geF9wbHVzXzEgKiAoeF9wbHVzXzEgLSAyKTsKCQlmbiA9IGZtYSh5LHksIGZuKTsKCQlmbG9hdCBmbndpZHRoID0gZndpZHRoKGZuKTsKCQljb3ZlcmFnZSA9IC41IC0gaGFsZihmbi9mbndpZHRoKTsKCQljb3ZlcmFnZSA9IGNsYW1wKGNvdmVyYWdlLCAwLCAxKTsKCX0KCWhhbGY0IG91dHB1dENvdmVyYWdlX1MwID0gaGFsZjQoY292ZXJhZ2UpOwoJewoJCS8vIFhmZXIgUHJvY2Vzc29yOiBQb3J0ZXIgRHVmZgoJCXNrX0ZyYWdDb2xvciA9IG91dHB1dENvbG9yX1MwICogb3V0cHV0Q292ZXJhZ2VfUzA7Cgl9Cn0KAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAoAAAAAEAAAACAAAAAwAAAB8AAAAAAAAAAQAAAB8AAAAQAAAAAQAAAB8AAAAgAAAAAQAAADAAAAAFAAAAHwAAAAAAAAACAAAAHQAAABAAAAACAAAAHwAAABgAAAACAAAAHwAAACgAAAACAAAACQAAADgAAAACAAAAPAAAAFAAAAABAAAAAQAAAAUAAAAAAAAAAQAAAAUAAAAAAAAAAQAAAAAAAAA=", "HRJAAAAAAAMAAAAAAQ4PZ72HLQCDR7H7777QGAAAAACAAAAAACCAYABAA4AAAACAAAAAAACCAYAAAUAAAAAACAAAAAAAAAAAAAAAAABAAAAABQYBAABAAAAAAA": "CAAAAExTS1PFAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDIpIGluIGZsb2F0MiBsb2NhbENvb3JkOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IG91dCBoYWxmNCB2Y29sb3JfUzA7CmxheW91dChsb2NhdGlvbiA9IDEpIG91dCBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmNvbG9yX1MwID0gY29sb3I7Cgl2bG9jYWxDb29yZF9TMCA9IGxvY2FsQ29vcmQ7Cglza19Qb3NpdGlvbiA9IHBvc2l0aW9uLnh5MDE7Cn0KAAAAAAAAAJkCAABsYXlvdXQoYmluZGluZz0wKSB1bmlmb3JtIHNhbXBsZXIyRCB1VGV4dHVyZVNhbXBsZXJfMF9TMDsKbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGZsYXQgaW4gaGFsZjQgdmNvbG9yX1MwOwpsYXlvdXQobG9jYXRpb24gPSAxKSBpbiBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CmxheW91dChsb2NhdGlvbiA9IDAsIGluZGV4ID0gMCkgb3V0IGhhbGY0IHNrX0ZyYWdDb2xvcjsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzA7CglvdXRwdXRDb2xvcl9TMCA9IHZjb2xvcl9TMDsKCWZsb2F0MiB0ZXhDb29yZDsKCXRleENvb3JkID0gdmxvY2FsQ29vcmRfUzA7CglvdXRwdXRDb2xvcl9TMCA9ICgoc2FtcGxlKHVUZXh0dXJlU2FtcGxlcl8wX1MwLCB0ZXhDb29yZCkgKiBvdXRwdXRDb2xvcl9TMCkpOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAAABAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGAAAAABAAAAAAAAAAMAAAAdAAAAAAAAAAEAAAAJAAAACAAAAAEAAAAdAAAADAAAAAEAAAAUAAAAAAAAAFAAAAABAAAAAQAAAAUAAAAAAAAAAQAAAAUAAAAAAAAAAQAAAAAAAAA=", "CEAAAAAADAAAAAAYQAMLQGEABAAAAAAAEIBAAQAOAAAABAAAAAAABBAMABIAAAAAAEAAAAAAAAAAAAAAAAAAAAAAADBQCAACAAAAAAA": "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", "HQIAAAAAAAGAAAAAAIOP577774BRZ7X7777QCAAAAAAAAAAAFIAA2AAAAAAAAAQAAAACUAQACAAAAABQIMACCAYAAAAAAAAAAAADSAAAAAAAEAAAAAIDEAAAABIAAAAAAEAAAAAAAAAAAAAAAAACAAAAAABQAAAAAAAAAAA": "CAAAAExTS1O/AQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKCWxheW91dChvZmZzZXQ9MTYpIGZsb2F0M3gzIHVtYXRyaXhfUzFfYzA7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGZsb2F0MiBsb2NhbENvb3JkOwpsYXlvdXQobG9jYXRpb24gPSAwKSBvdXQgZmxvYXQyIHZUcmFuc2Zvcm1lZENvb3Jkc18zX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gUHJpbWl0aXZlIFByb2Nlc3NvciBRdWFkUGVyRWRnZUFBR2VvbWV0cnlQcm9jZXNzb3IKCXNrX1Bvc2l0aW9uID0gcG9zaXRpb24ueHkwMTsKCXsKCQl2VHJhbnNmb3JtZWRDb29yZHNfM19TMCA9IGZsb2F0M3gyKHVtYXRyaXhfUzFfYzApICogbG9jYWxDb29yZC54eTE7Cgl9Cn0KAAAAAACOAwAAbGF5b3V0KGJpbmRpbmc9MCkgdW5pZm9ybSBzYW1wbGVyMkQgdVRleHR1cmVTYW1wbGVyXzBfUzE7CmxheW91dCAoYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7CglsYXlvdXQob2Zmc2V0PTE2KSBmbG9hdDN4MyB1bWF0cml4X1MxX2MwOwp9CjsKbGF5b3V0KGxvY2F0aW9uID0gMCkgaW4gZmxvYXQyIHZUcmFuc2Zvcm1lZENvb3Jkc18zX1MwOwpsYXlvdXQobG9jYXRpb24gPSAwLCBpbmRleCA9IDApIG91dCBoYWxmNCBza19GcmFnQ29sb3I7CmhhbGY0IFRleHR1cmVFZmZlY3RfUzFfYzBfYzAoaGFsZjQgX2lucHV0KSAKewoJcmV0dXJuIHNhbXBsZSh1VGV4dHVyZVNhbXBsZXJfMF9TMSwgdlRyYW5zZm9ybWVkQ29vcmRzXzNfUzApOwp9CmhhbGY0IE1hdHJpeEVmZmVjdF9TMV9jMChoYWxmNCBfaW5wdXQpIAp7CglyZXR1cm4gVGV4dHVyZUVmZmVjdF9TMV9jMF9jMChfaW5wdXQpOwp9CmhhbGY0IEJsZW5kX1MxKGhhbGY0IF9zcmMsIGhhbGY0IF9kc3QpIAp7CgkvLyBCbGVuZCBtb2RlOiBNb2R1bGF0ZQoJcmV0dXJuIGJsZW5kX21vZHVsYXRlKE1hdHJpeEVmZmVjdF9TMV9jMChfc3JjKSwgX3NyYyk7Cn0Kdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzAgPSBoYWxmNCgxKTsKCWNvbnN0IGhhbGY0IG91dHB1dENvdmVyYWdlX1MwID0gaGFsZjQoMSk7CgloYWxmNCBvdXRwdXRfUzE7CglvdXRwdXRfUzEgPSBCbGVuZF9TMShvdXRwdXRDb2xvcl9TMCwgaGFsZjQoMSkpOwoJewoJCS8vIFhmZXIgUHJvY2Vzc29yOiBQb3J0ZXIgRHVmZgoJCXNrX0ZyYWdDb2xvciA9IG91dHB1dF9TMSAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPAAAAAEAAAAAAAAAAgAAAB0AAAAAAAAAAQAAAB0AAAAIAAAAAQAAABAAAAAAAAAAUAAAAAAAAAABAAAAAAAAAA==", "CEAAAAAADAAAAAAYQAMLQGEABAAAAAAAEIBAAQAOAAAABAAAAAAABBAMABDAAAAAAEAAAAAAAAAAAAAAAAAAAAAAADBQCAACAAAAAAA": "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", "HQQAAAAAAAGAAAAAAIOP57ZDF37P7777777QCAAAAAAAAAAAAAAQAAAAAAQQGAAAKAAAAAABAAAAB7IAAAAACAAAAAAAAAAAAIAAAAQAAAAAA": "CAAAAExTS1NMAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDApIGZsYXQgb3V0IGhhbGY0IHZjb2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFByaW1pdGl2ZSBQcm9jZXNzb3IgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgl2Y29sb3JfUzAgPSBjb2xvcjsKCXNrX1Bvc2l0aW9uID0gcG9zaXRpb24ueHkwMTsKfQoAAAAAgAEAAGxheW91dCAoYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMCwgaW5kZXggPSAwKSBvdXQgaGFsZjQgc2tfRnJhZ0NvbG9yOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmNvbG9yX1MwOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogRGlzYWJsZSBDb2xvcgoJfQp9CgAAAAABAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwAAAABAAAAAAAAAAIAAAAdAAAAAAAAAAEAAAAJAAAACAAAAAEAAAAMAAAAAAAAAFAAAAAAAAAAAAAAAP0AAAA=", "HRIAAAAAAAMAAAAAAQ4PZ72HLQCDR7H7777QGAAAAAAAAAAAKQAAUAAAABCC3BNJEEAAAACIUBDGCQAAAAAAIAAA7QAQAAH4AEAAB7ABAAAPYAIACAAAAAEICFIUWYLKBAAAAAAAAAAAAMJCQENIKAIBAAABAAAA6ADQAAHQA4AAB4AHAAAPABYAEAAAAACAIUAAAAQAAAAGMCBAMQAAAAAAAAAAAIAHAAAAAQAAAAAAAQQGAAAFAAAAAAAQAAAA7UAAAAAAAAAAAAAAAAAMGAIAAIAAAAAA": "CAAAAExTS1NUAgAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKCWxheW91dChvZmZzZXQ9MTYpIGhhbGY0IHVjb2xvcl9TMV9jMDsKCWxheW91dChvZmZzZXQ9MzIpIGZsb2F0M3gzIHVtYXRyaXhfUzFfYzFfYzA7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGhhbGY0IGNvbG9yOwpsYXlvdXQobG9jYXRpb24gPSAyKSBpbiBmbG9hdDIgbG9jYWxDb29yZDsKbGF5b3V0KGxvY2F0aW9uID0gMCkgZmxhdCBvdXQgaGFsZjQgdmNvbG9yX1MwOwpsYXlvdXQobG9jYXRpb24gPSAxKSBvdXQgZmxvYXQyIHZUcmFuc2Zvcm1lZENvb3Jkc181X1MwOwp2b2lkIG1haW4oKSAKewoJLy8gUHJpbWl0aXZlIFByb2Nlc3NvciBRdWFkUGVyRWRnZUFBR2VvbWV0cnlQcm9jZXNzb3IKCXZjb2xvcl9TMCA9IGNvbG9yOwoJc2tfUG9zaXRpb24gPSBwb3NpdGlvbi54eTAxOwoJewoJCXZUcmFuc2Zvcm1lZENvb3Jkc181X1MwID0gZmxvYXQzeDIodW1hdHJpeF9TMV9jMV9jMCkgKiBsb2NhbENvb3JkLnh5MTsKCX0KfQoAAAAA/QQAAGxheW91dChiaW5kaW5nPTApIHVuaWZvcm0gc2FtcGxlcjJEIHVUZXh0dXJlU2FtcGxlcl8wX1MxOwpsYXlvdXQgKGJpbmRpbmc9MCkgdW5pZm9ybSB1bmlmb3JtQnVmZmVyCnsKCWxheW91dChvZmZzZXQ9MCkgZmxvYXQ0IHNrX1JUQWRqdXN0OwoJbGF5b3V0KG9mZnNldD0xNikgaGFsZjQgdWNvbG9yX1MxX2MwOwoJbGF5b3V0KG9mZnNldD0zMikgZmxvYXQzeDMgdW1hdHJpeF9TMV9jMV9jMDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGZsYXQgaW4gaGFsZjQgdmNvbG9yX1MwOwpsYXlvdXQobG9jYXRpb24gPSAxKSBpbiBmbG9hdDIgdlRyYW5zZm9ybWVkQ29vcmRzXzVfUzA7CmxheW91dChsb2NhdGlvbiA9IDAsIGluZGV4ID0gMCkgb3V0IGhhbGY0IHNrX0ZyYWdDb2xvcjsKaGFsZjQgY29sb3JfZnBfUzFfYzAoaGFsZjQgX2lucHV0KSAKewoJaGFsZjQgX3RtcF8wX2luQ29sb3IgPSBfaW5wdXQ7CglyZXR1cm4gaGFsZjQodWNvbG9yX1MxX2MwKTsKfQpoYWxmNCBUZXh0dXJlRWZmZWN0X1MxX2MxX2MwX2MwKGhhbGY0IF9pbnB1dCkgCnsKCXJldHVybiBzYW1wbGUodVRleHR1cmVTYW1wbGVyXzBfUzEsIHZUcmFuc2Zvcm1lZENvb3Jkc181X1MwKTsKfQpoYWxmNCBNYXRyaXhFZmZlY3RfUzFfYzFfYzAoaGFsZjQgX2lucHV0KSAKewoJcmV0dXJuIFRleHR1cmVFZmZlY3RfUzFfYzFfYzBfYzAoX2lucHV0KTsKfQpoYWxmNCBPdmVycmlkZUlucHV0X1MxX2MxKGhhbGY0IF9pbnB1dCkgCnsKCWhhbGY0IF90bXBfMV9pbkNvbG9yID0gX2lucHV0OwoJcmV0dXJuIGhhbGY0KE1hdHJpeEVmZmVjdF9TMV9jMV9jMChoYWxmNCgxLjAsMS4wLDEuMCwxLjApKSk7Cn0KaGFsZjQgQmxlbmRfUzEoaGFsZjQgX3NyYywgaGFsZjQgX2RzdCkgCnsKCS8vIEJsZW5kIG1vZGU6IFNyY0luCglyZXR1cm4gYmxlbmRfc3JjX2luKGNvbG9yX2ZwX1MxX2MwKF9zcmMpLCBPdmVycmlkZUlucHV0X1MxX2MxKF9zcmMpKTsKfQp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmNvbG9yX1MwOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCWhhbGY0IG91dHB1dF9TMTsKCW91dHB1dF9TMSA9IEJsZW5kX1MxKG91dHB1dENvbG9yX1MwLCBoYWxmNCgxKSk7Cgl7CgkJLy8gWGZlciBQcm9jZXNzb3I6IFBvcnRlciBEdWZmCgkJc2tfRnJhZ0NvbG9yID0gb3V0cHV0X1MxICogb3V0cHV0Q292ZXJhZ2VfUzA7Cgl9Cn0KAAAAAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYAAAAAEAAAAAAAAAAwAAAB0AAAAAAAAAAQAAAAkAAAAIAAAAAQAAAB0AAAAMAAAAAQAAABQAAAAAAAAAUAAAAAEAAAABAAAABQAAAAAAAAABAAAABQAAAAAAAAABAAAA/QAAAA==", "HQIAAAAAAAGAAAAAAIOP577774BRZ7X7777QCAAAAAAAAAAAVIBAYAAAAAAQCAIAAAACRRAAAAABAEAQAAAABIECAEAQCAAAAAZQIEBSAAIBAAAAAAAJAAYAAAACAAAAAAACCA2QAAAAAAIAAAAAAAAAAAAAAAAAAAAAAAADAAAAAAAAAAAA": "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", "HQJAAAAAAAGAAAAAAIOP577774BRZ7X7777QCAAAAABAAAAAABBAMAEQAMAAAABAAAAAAABBAMAAAUAAAAAACAAAAD6QAAAAAEAAAAAQAAAABQYBAABAAAAAAA": "CAAAAExTS1NdAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gZmxvYXQyIGxvY2FsQ29vcmQ7CmxheW91dChsb2NhdGlvbiA9IDApIG91dCBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmxvY2FsQ29vcmRfUzAgPSBsb2NhbENvb3JkOwoJc2tfUG9zaXRpb24gPSBwb3NpdGlvbi54eTAxOwp9CgAAAAAAAABTAgAAbGF5b3V0KGJpbmRpbmc9MCkgdW5pZm9ybSBzYW1wbGVyMkQgdVRleHR1cmVTYW1wbGVyXzBfUzA7CmxheW91dCAoYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CmxheW91dChsb2NhdGlvbiA9IDAsIGluZGV4ID0gMCkgb3V0IGhhbGY0IHNrX0ZyYWdDb2xvcjsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzAgPSBoYWxmNCgxKTsKCWZsb2F0MiB0ZXhDb29yZDsKCXRleENvb3JkID0gdmxvY2FsQ29vcmRfUzA7CglvdXRwdXRDb2xvcl9TMCA9ICgoc2FtcGxlKHVUZXh0dXJlU2FtcGxlcl8wX1MwLCB0ZXhDb29yZCkgKiBoYWxmNCgxKSkpOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABUAAAAAQAAAAAAAAACAAAAHQAAAAAAAAABAAAAHQAAAAgAAAABAAAAEAAAAAAAAABQAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAD9AAAA", "HQJAAAAAAAGAAAAAAIOP577774BRZ7X7777QCAAAAABAAAAAABBAMAEQAMAAAABAAAAAAABBAMAAARQAAAAACAAAAD6QAAAAAAAAAAAAAAAABQYBAABAAAAAAA": "CAAAAExTS1NdAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gZmxvYXQyIGxvY2FsQ29vcmQ7CmxheW91dChsb2NhdGlvbiA9IDApIG91dCBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmxvY2FsQ29vcmRfUzAgPSBsb2NhbENvb3JkOwoJc2tfUG9zaXRpb24gPSBwb3NpdGlvbi54eTAxOwp9CgAAAAAAAABTAgAAbGF5b3V0KGJpbmRpbmc9MCkgdW5pZm9ybSBzYW1wbGVyMkQgdVRleHR1cmVTYW1wbGVyXzBfUzA7CmxheW91dCAoYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CmxheW91dChsb2NhdGlvbiA9IDAsIGluZGV4ID0gMCkgb3V0IGhhbGY0IHNrX0ZyYWdDb2xvcjsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzAgPSBoYWxmNCgxKTsKCWZsb2F0MiB0ZXhDb29yZDsKCXRleENvb3JkID0gdmxvY2FsQ29vcmRfUzA7CglvdXRwdXRDb2xvcl9TMCA9ICgoc2FtcGxlKHVUZXh0dXJlU2FtcGxlcl8wX1MwLCB0ZXhDb29yZCkgKiBoYWxmNCgxKSkpOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABUAAAAAQAAAAAAAAACAAAAHQAAAAAAAAABAAAAHQAAAAgAAAABAAAAEAAAAAAAAABGAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAD9AAAA", "HQJAAAAAAAGAAAAAAIOP577774BRZ7X7777QCAAAAABAAAAAABBAMADCAB4QAAAAAEAAAAAAHEAAAAAAAIAAAAAQGIAAAAAAKAAAAAABAAAAB7IAAAAAAAAAAAAAAAAAYMAQAAQAAAAAA": "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", "CEAAAAAADAAAAAAYQAMLQGEABAAAAAAAEIBAAQAOAAAABAAAAAAABBAMABIAAAAAAEAAAAAAAAAAAAAAAAACAAAAADBQCAACAAAAAAA": "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", "E5YQAAAAMAAGEADCACRAAAAAMAACFQDBABRAAIXCAIAAAAAAHEAAAAAAAIAAAAAQGIAAAACGAAAAAAIAAAAAAAAAAAAAAAAAAAAAAAGDAEAAAAAAAAAA": "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", "HRIAAAAAAAMAAAAAAQ4PZ72HLQCDR7H7777QGAAAAAAAAAAAGQDCIUFDGAQAAAAAAIAAB7QAAAAP4AAAAD7AAAAA72MJ54XDUIAAAABQAAAAAYAAAAAEA3BFYANSIAAAABQBD6P3AEAAAAACAAAAAYWE6SKB6FYFAAAIAAIAAAAAGAAAABRCWAO6EAAQAAAARPEN6DYAAAACAAAAAAIIHMMVABXZAAAAAAAAAAAAKAIVQRH6PYAAAAEAAAAABAAYQJC6J3YHAAAAAAAAAAAMQAIAAAABAAAAACAJAAKQAAAAAAIAAAAAAAAAAAAAAAAAEAAAAAARAAAAEAAAAAAA": "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", "CEAAAAAADAAAAAAYQAMLQGEABAAAAAAAEIBAASAM4APBNJCRAAAAADAAAAAAAAAAACIAGAAAAAQAAAAAAAQQGAAAABIAAAAAAEAAAAAAAAAAAAAAAAACAAAAADBQCAACAAAAAAA": "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", "HRJAAAAAAAMAAAAAAQ4PZ72HLQCDR7H7777QGAAAAACAAAAAACCAYABAA4AAAACAAAAAAACCAYAAAUAAAAAACAAAAAAAAAAAAAAAAAAAAAAABQYBAABAAAAAAA": "CAAAAExTS1PFAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDIpIGluIGZsb2F0MiBsb2NhbENvb3JkOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IG91dCBoYWxmNCB2Y29sb3JfUzA7CmxheW91dChsb2NhdGlvbiA9IDEpIG91dCBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmNvbG9yX1MwID0gY29sb3I7Cgl2bG9jYWxDb29yZF9TMCA9IGxvY2FsQ29vcmQ7Cglza19Qb3NpdGlvbiA9IHBvc2l0aW9uLnh5MDE7Cn0KAAAAAAAAAJkCAABsYXlvdXQoYmluZGluZz0wKSB1bmlmb3JtIHNhbXBsZXIyRCB1VGV4dHVyZVNhbXBsZXJfMF9TMDsKbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGZsYXQgaW4gaGFsZjQgdmNvbG9yX1MwOwpsYXlvdXQobG9jYXRpb24gPSAxKSBpbiBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CmxheW91dChsb2NhdGlvbiA9IDAsIGluZGV4ID0gMCkgb3V0IGhhbGY0IHNrX0ZyYWdDb2xvcjsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzA7CglvdXRwdXRDb2xvcl9TMCA9IHZjb2xvcl9TMDsKCWZsb2F0MiB0ZXhDb29yZDsKCXRleENvb3JkID0gdmxvY2FsQ29vcmRfUzA7CglvdXRwdXRDb2xvcl9TMCA9ICgoc2FtcGxlKHVUZXh0dXJlU2FtcGxlcl8wX1MwLCB0ZXhDb29yZCkgKiBvdXRwdXRDb2xvcl9TMCkpOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAAABAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGAAAAABAAAAAAAAAAMAAAAdAAAAAAAAAAEAAAAJAAAACAAAAAEAAAAdAAAADAAAAAEAAAAUAAAAAAAAAFAAAAABAAAAAQAAAAUAAAAAAAAAAQAAAAUAAAAAAAAAAQAAAAAAAAA=", "HQQACAAAAAGAAAAAAIOP57ZDF37P7777777QCAAAAAAAAAAASABQAAAAEAAAAAAAEEBQAACQAAAAAAIAAAAAAAAAAAAAAAAAEAAAAAGDAEAAAAAAAAAA": "CAAAAExTS1NHAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDApIG91dCBoYWxmNCB2Y29sb3JfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmNvbG9yX1MwID0gY29sb3I7Cglza19Qb3NpdGlvbiA9IHBvc2l0aW9uLnh5MDE7Cn0KAAAAAACuAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMCwgaW5kZXggPSAwKSBvdXQgaGFsZjQgc2tfRnJhZ0NvbG9yOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmNvbG9yX1MwOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAVAAAAAEAAAAAAAAAAgAAAB0AAAAAAAAAAQAAAAkAAAAIAAAAAQAAAAwAAAAAAAAAUAAAAAEAAAABAAAABQAAAAAAAAABAAAABQAAAAAAAAABAAAAAAAAAA==", "CEAAAAAADAAAAAAYQAMLQGEABAAAAAAAEIBAAQAOAAAABAAAAAAABBAMABDAAAAAAEAAAAAAAAAAAAAAAAACAAAAADBQCAACAAAAAAA": "CAAAAExTS1MCBAAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKCWxheW91dChvZmZzZXQ9MTYpIGZsb2F0MiB1YXRsYXNfYWRqdXN0X1MwOwp9CjsKbGF5b3V0KGxvY2F0aW9uID0gMCkgaW4gZmxvYXQ0IGZpbGxCb3VuZHM7CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGhhbGY0IGNvbG9yOwpsYXlvdXQobG9jYXRpb24gPSAyKSBpbiBmbG9hdDQgbG9jYXRpb25zOwpsYXlvdXQobG9jYXRpb24gPSAwKSBvdXQgZmxvYXQyIHZhdGxhc0Nvb3JkX1MwOwpsYXlvdXQobG9jYXRpb24gPSAxKSBmbGF0IG91dCBoYWxmNCB2Y29sb3JfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIERyYXdBdGxhc1BhdGhTaGFkZXIKCWZsb2F0MiB1bml0Q29vcmQgPSBmbG9hdDIoc2tfVmVydGV4SUQgJiAxLCBza19WZXJ0ZXhJRCA+PiAxKTsKCWZsb2F0MiBkZXZDb29yZCA9IG1peChmaWxsQm91bmRzLnh5LCBmaWxsQm91bmRzLnp3LCB1bml0Q29vcmQpOwoJLy8gQSBuZWdhdGl2ZSB4IGNvb3JkaW5hdGUgaW4gdGhlIGF0bGFzIGluZGljYXRlcyB0aGF0IHRoZSBwYXRoIGlzIHRyYW5zcG9zZWQuCgkvLyBXZSBhbHNvIGFkZGVkIDEgc2luY2Ugd2UgY2FuJ3QgbmVnYXRlIHplcm8uCglmbG9hdDIgYXRsYXNUb3BMZWZ0ID0gZmxvYXQyKGFicyhsb2NhdGlvbnMueCkgLSAxLCBsb2NhdGlvbnMueSk7CglmbG9hdDIgZGV2VG9wTGVmdCA9IGxvY2F0aW9ucy56dzsKCWJvb2wgdHJhbnNwb3NlZCA9IGxvY2F0aW9ucy54IDwgMDsKCWZsb2F0MiBhdGxhc0Nvb3JkID0gZGV2Q29vcmQgLSBkZXZUb3BMZWZ0OwoJaWYgKHRyYW5zcG9zZWQpIAoJewoJCWF0bGFzQ29vcmQgPSBhdGxhc0Nvb3JkLnl4OwoJfQoJYXRsYXNDb29yZCArPSBhdGxhc1RvcExlZnQ7Cgl2YXRsYXNDb29yZF9TMCA9IGF0bGFzQ29vcmQgKiB1YXRsYXNfYWRqdXN0X1MwOwoJdmNvbG9yX1MwID0gY29sb3I7Cglza19Qb3NpdGlvbiA9IGRldkNvb3JkLnh5MDE7Cn0KAAAAAAAApwIAAGxheW91dChiaW5kaW5nPTApIHVuaWZvcm0gc2FtcGxlcjJEIHVUZXh0dXJlU2FtcGxlcl8wX1MwOwpsYXlvdXQgKGJpbmRpbmc9MCkgdW5pZm9ybSB1bmlmb3JtQnVmZmVyCnsKCWxheW91dChvZmZzZXQ9MCkgZmxvYXQ0IHNrX1JUQWRqdXN0OwoJbGF5b3V0KG9mZnNldD0xNikgZmxvYXQyIHVhdGxhc19hZGp1c3RfUzA7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgdmF0bGFzQ29vcmRfUzA7CmxheW91dChsb2NhdGlvbiA9IDEpIGZsYXQgaW4gaGFsZjQgdmNvbG9yX1MwOwpsYXlvdXQobG9jYXRpb24gPSAwLCBpbmRleCA9IDApIG91dCBoYWxmNCBza19GcmFnQ29sb3I7CnZvaWQgbWFpbigpIAp7CgkvLyBTdGFnZSAwLCBEcmF3QXRsYXNQYXRoU2hhZGVyCgloYWxmNCBvdXRwdXRDb3ZlcmFnZV9TMCA9IGhhbGY0KDEpOwoJaGFsZiBhdGxhc0NvdmVyYWdlID0gc2FtcGxlKHVUZXh0dXJlU2FtcGxlcl8wX1MwLCB2YXRsYXNDb29yZF9TMCkuMDAwci5hOwoJb3V0cHV0Q292ZXJhZ2VfUzAgKj0gYXRsYXNDb3ZlcmFnZTsKCWhhbGY0IG91dHB1dENvbG9yX1MwOwoJb3V0cHV0Q29sb3JfUzAgPSB2Y29sb3JfUzA7Cgl7CgkJLy8gWGZlciBQcm9jZXNzb3I6IFBvcnRlciBEdWZmCgkJc2tfRnJhZ0NvbG9yID0gb3V0cHV0Q29sb3JfUzAgKiBvdXRwdXRDb3ZlcmFnZV9TMDsKCX0KfQoAAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYAAAAAAAAAABAAAAAAAAAAMAAAAfAAAAAAAAAAEAAAAfAAAAEAAAAAEAAAAfAAAAIAAAAAEAAAAwAAAARgAAAAEAAAABAAAABQAAAAAAAAABAAAABQAAAAAAAAABAAAAAAAAAA==", "HRIAAAAAAAMAAAAAAQ4PZ72HLQCDR7H7777QGAAAAAAAAAAALAABUAAAABCC3BNJEEAAAACIUBDGCQAAAAADAAAAACIEBDOCQAAAAADAPLFI7CYCAAAMAAAAACAACAAAACVX2RMDIABAAAEAIXSO6BYAAAABAAAAACEBCUKLMFVAQAAAAAAAAAAAGERICGUFAEAQAAGAAAAAAQACGUFAGAQAACAOSKJ7FYFAAAAAAMAAAAAGAAAABLHWCUGQECIAAAABNEN7D4AAAABAAAAAAQCABQAAAABEKCRTAIAAAAAJRHXS4ORAAAAAGAAAAADAAAAABQDKL7ISBEAAAAAGAEPZ7MAQAAAAAQAAAAAAMJCAENIKAMBAAAEA5EUT6LQKAAAAAAYAAAAAMAAAACWPMFINAIEQAAAAC2I36HYAAAACAAAAAAQEMTCP7FYVCAAAAAMAAAAAGAAAAADAWWXWQECIAAAABMEI7T6QAAAAAABAAAAAGG4NUVZUBASAAAAAAAAAAAAVQFC6J3YHAAAAACAAAAAIQIKYIT7H4AAAAAAAAAAAAAKQAAAIAAAABGBBAAAAAAAAAAAAAAEADQAAAAAAAEAAAAAIDEAAAACQAAAAAAIAAAAAAAAAAAAAAAAAEAAAAAGDAEAAEAAAAAAA": "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", "HQIAAAAAAAGAAAAAAIOP577774BRZ7X7777QCAAAAAAAAAAAVIBAYAAAAAAACAIAAAACRRAAAAAAAEAQAAAABIECAAAQCAAAAAZQIEBSAAABAAAAAAAJAAYAAAACAAAAAAACCA2GAAAAAAIAAAAAAAAAAAAAAAAAAAAAAAADAAAAEAAAAAAA": "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", "AXAQGAAAQAAIPCELAGEP777777777737AAAAAAAAAAAIBRUAARVBIBQEAAAEAAAAYAPQAAGAD4AABQA7AAAMAH6TKN7FYFAAAAAAEAAAAAGAAAAARDUYG4YHAEAAAAAWSG7R6AAAAAQAAAAAEBDEYT7ZOFIQAAAABAAAAABQAAAAAIFGB7HB2BAAAAAFQRH6PYAAAAAAAEAABAAYRTUYG4YHAEAAAAAAAAAEARLACH47WAIAAAAAEAAAABRAQFURX4PQAAAAAAAAAABAA4AAAACAAAAAAACCAYAAAACQAAAAAAIAAAAP2AAAAAAAAAAAAAAAAAGDAEAAAAAAAAAA": "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", "E5YQAAAAMAAGEADCACRAAAAAMAACFQDBABRAAIXCAIAAAAAAHEAAAAAAAIAAAAAQGIAAAACQAAAAAAIAAAAAAAAAAAAAAAAAAAAAAAGDAEAAAAAAAAAA": "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", "HQQAAAAAAAGAAAAAAIOP57ZDF37P7777777QCAAAAAAAAAAASABQAAAAEAAAAAAAEEBQAACQAAAAAAIAAAAP2AAAAAAQAAAACAAAAAGDAEAAEAAAAAAA": "CAAAAExTS1NMAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDApIGZsYXQgb3V0IGhhbGY0IHZjb2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFByaW1pdGl2ZSBQcm9jZXNzb3IgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgl2Y29sb3JfUzAgPSBjb2xvcjsKCXNrX1Bvc2l0aW9uID0gcG9zaXRpb24ueHkwMTsKfQoAAAAAswEAAGxheW91dCAoYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMCwgaW5kZXggPSAwKSBvdXQgaGFsZjQgc2tfRnJhZ0NvbG9yOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmNvbG9yX1MwOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABUAAAAAQAAAAAAAAACAAAAHQAAAAAAAAABAAAACQAAAAgAAAABAAAADAAAAAAAAABQAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAD9AAAA", "HQQACAAAAAGAAAAAAIOP57ZDF37P7777777QCAAAAAAAAAAASABQAAAAEAAAAAAAEEBQAACQAAAAAAIAAAAP2AAAAAAAAAAAAAAAAAGDAEAAAAAAAAAA": "CAAAAExTS1NHAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDApIG91dCBoYWxmNCB2Y29sb3JfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmNvbG9yX1MwID0gY29sb3I7Cglza19Qb3NpdGlvbiA9IHBvc2l0aW9uLnh5MDE7Cn0KAAAAAACuAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMCwgaW5kZXggPSAwKSBvdXQgaGFsZjQgc2tfRnJhZ0NvbG9yOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmNvbG9yX1MwOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAVAAAAAEAAAAAAAAAAgAAAB0AAAAAAAAAAQAAAAkAAAAIAAAAAQAAAAwAAAAAAAAAUAAAAAEAAAABAAAABQAAAAAAAAABAAAABQAAAAAAAAABAAAA/QAAAA==", "HRIAAAAAAAMAAAAAAQ4PZ72HLQCDR7H7777QGAAAAAAAAAAAGQDCIUFDGAQAAAAAAIAAB7QAAAAP4AAAAD7AAAAA72MJ54XDUIAAAAAQAAAAAYAAAAAIBYTZF4EBCAAAABQBD6P3AEAAAAACAAAAAYWE6SKB6FYFAAAIAAAAAAAAGAAAAAKM662BRAAAAAAARPEN6DYAAAACAAAAAAIIHCXHXUQEIAAAAAAAAAAAKAIVQRH6PYAAAAEAAAAABAAYQJC6J3YHAAAAAAAAAAAMQAIAAAABAAAAACAJAAKQAAAAAAIAAAAAAAAAAAAAAAAAEAAAAAEBAAAAEAAAAAAA": "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", "HRJAAAAAAAMAAAAAAQ4PZ72HLQCDR7H7777QGAAAAACAAAAAACCAYABEA2CJQ4YGFEAAAAAGAAAAAAAAAAAMQAIAAAABAAAAACAJAAKGAAAAAAIAAAAP2AAAAAAAAAAAAAAAAAGDAEAAEAAAAAAA": "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", "HQJAAAAAAAGAAAAAAIOP577774BRZ7X7777QCAAAAABAAAAAABBAMAEQAMAAAABAAAAAAABBAMAAAUAAAAAACAAAAD6QAAAAAEAAAABQAAAABQYBAABAAAAAAA": "CAAAAExTS1NdAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gZmxvYXQyIGxvY2FsQ29vcmQ7CmxheW91dChsb2NhdGlvbiA9IDApIG91dCBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmxvY2FsQ29vcmRfUzAgPSBsb2NhbENvb3JkOwoJc2tfUG9zaXRpb24gPSBwb3NpdGlvbi54eTAxOwp9CgAAAAAAAABTAgAAbGF5b3V0KGJpbmRpbmc9MCkgdW5pZm9ybSBzYW1wbGVyMkQgdVRleHR1cmVTYW1wbGVyXzBfUzA7CmxheW91dCAoYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CmxheW91dChsb2NhdGlvbiA9IDAsIGluZGV4ID0gMCkgb3V0IGhhbGY0IHNrX0ZyYWdDb2xvcjsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzAgPSBoYWxmNCgxKTsKCWZsb2F0MiB0ZXhDb29yZDsKCXRleENvb3JkID0gdmxvY2FsQ29vcmRfUzA7CglvdXRwdXRDb2xvcl9TMCA9ICgoc2FtcGxlKHVUZXh0dXJlU2FtcGxlcl8wX1MwLCB0ZXhDb29yZCkgKiBoYWxmNCgxKSkpOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABUAAAAAQAAAAAAAAACAAAAHQAAAAAAAAABAAAAHQAAAAgAAAABAAAAEAAAAAAAAABQAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAD9AAAA", "HQIAAAAAAAGAAAAAAIOP577774BRZ7X7777QCAAAAAAAAAAAVIBACAIBAAAAAMYECAZAAEAQAAAAAAEQAMAAAABAAAAAAABBAMAAAACQAAAAAAIAAAAAAAAAAAAAAAAAAAAAAAADAAAAEAAAAAAA": "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", "HRJAAAAAAAMAAAAAAQ4PZ72HLQCDR7H7777QGAAAAACAAAAAACCAYABAA4AAAACAAAAAAACCAYAAAUAAAAAACAAAAD6QAAAAAAAAAAAAAAAABQYBAABAAAAAAA": "CAAAAExTS1PFAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDIpIGluIGZsb2F0MiBsb2NhbENvb3JkOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IG91dCBoYWxmNCB2Y29sb3JfUzA7CmxheW91dChsb2NhdGlvbiA9IDEpIG91dCBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmNvbG9yX1MwID0gY29sb3I7Cgl2bG9jYWxDb29yZF9TMCA9IGxvY2FsQ29vcmQ7Cglza19Qb3NpdGlvbiA9IHBvc2l0aW9uLnh5MDE7Cn0KAAAAAAAAAJkCAABsYXlvdXQoYmluZGluZz0wKSB1bmlmb3JtIHNhbXBsZXIyRCB1VGV4dHVyZVNhbXBsZXJfMF9TMDsKbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGZsYXQgaW4gaGFsZjQgdmNvbG9yX1MwOwpsYXlvdXQobG9jYXRpb24gPSAxKSBpbiBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CmxheW91dChsb2NhdGlvbiA9IDAsIGluZGV4ID0gMCkgb3V0IGhhbGY0IHNrX0ZyYWdDb2xvcjsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzA7CglvdXRwdXRDb2xvcl9TMCA9IHZjb2xvcl9TMDsKCWZsb2F0MiB0ZXhDb29yZDsKCXRleENvb3JkID0gdmxvY2FsQ29vcmRfUzA7CglvdXRwdXRDb2xvcl9TMCA9ICgoc2FtcGxlKHVUZXh0dXJlU2FtcGxlcl8wX1MwLCB0ZXhDb29yZCkgKiBvdXRwdXRDb2xvcl9TMCkpOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAAABAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGAAAAABAAAAAAAAAAMAAAAdAAAAAAAAAAEAAAAJAAAACAAAAAEAAAAdAAAADAAAAAEAAAAUAAAAAAAAAFAAAAABAAAAAQAAAAUAAAAAAAAAAQAAAAUAAAAAAAAAAQAAAP0AAAA=", "HQIAAAAAAAGAAAAAAIOP577774BRZ7X7777QCAAAAAAAAAAAVIBAYAAAAAAAAAIAAAACRRAAAAAAAAAQAAAABIECAAAACAAAAAZQIEBSAAAAAAAAAAAJAAYAAAACAAAAAAACCA2QAAAAAAIAAAAAAAAAAAAAAAAAAAAAAAADAAAAEAAAAAAA": "CAAAAExTS1N0AgAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKCWxheW91dChvZmZzZXQ9MTYpIGZsb2F0M3gzIHVtYXRyaXhfUzFfYzBfYzA7CglsYXlvdXQob2Zmc2V0PTY0KSBoYWxmMiB1XzBfSW5jcmVtZW50X1MxX2MwOwoJbGF5b3V0KG9mZnNldD03MikgaGFsZjQgdV8xX0tlcm5lbF9TMV9jMFs0XTsKCWxheW91dChvZmZzZXQ9MTA0KSBoYWxmNCB1XzJfT2Zmc2V0c19TMV9jMFs0XTsKCWxheW91dChvZmZzZXQ9MTQ0KSBmbG9hdDN4MyB1bWF0cml4X1MxOwp9CjsKbGF5b3V0KGxvY2F0aW9uID0gMCkgaW4gZmxvYXQyIHBvc2l0aW9uOwpsYXlvdXQobG9jYXRpb24gPSAxKSBpbiBmbG9hdDIgbG9jYWxDb29yZDsKbGF5b3V0KGxvY2F0aW9uID0gMCkgb3V0IGZsb2F0MiB2VHJhbnNmb3JtZWRDb29yZHNfMl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFByaW1pdGl2ZSBQcm9jZXNzb3IgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCglza19Qb3NpdGlvbiA9IHBvc2l0aW9uLnh5MDE7Cgl7CgkJdlRyYW5zZm9ybWVkQ29vcmRzXzJfUzAgPSBmbG9hdDN4Mih1bWF0cml4X1MxKSAqIGxvY2FsQ29vcmQueHkxOwoJfQp9CgAAAADHBQAAbGF5b3V0KGJpbmRpbmc9MCkgdW5pZm9ybSBzYW1wbGVyMkQgdVRleHR1cmVTYW1wbGVyXzBfUzE7CmxheW91dCAoYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7CglsYXlvdXQob2Zmc2V0PTE2KSBmbG9hdDN4MyB1bWF0cml4X1MxX2MwX2MwOwoJbGF5b3V0KG9mZnNldD02NCkgaGFsZjIgdV8wX0luY3JlbWVudF9TMV9jMDsKCWxheW91dChvZmZzZXQ9NzIpIGhhbGY0IHVfMV9LZXJuZWxfUzFfYzBbNF07CglsYXlvdXQob2Zmc2V0PTEwNCkgaGFsZjQgdV8yX09mZnNldHNfUzFfYzBbNF07CglsYXlvdXQob2Zmc2V0PTE0NCkgZmxvYXQzeDMgdW1hdHJpeF9TMTsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiB2VHJhbnNmb3JtZWRDb29yZHNfMl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMCwgaW5kZXggPSAwKSBvdXQgaGFsZjQgc2tfRnJhZ0NvbG9yOwpoYWxmNCBUZXh0dXJlRWZmZWN0X1MxX2MwX2MwX2MwKGhhbGY0IF9pbnB1dCwgZmxvYXQyIF9jb29yZHMpIAp7CglyZXR1cm4gc2FtcGxlKHVUZXh0dXJlU2FtcGxlcl8wX1MxLCBfY29vcmRzKTsKfQpoYWxmNCBNYXRyaXhFZmZlY3RfUzFfYzBfYzAoaGFsZjQgX2lucHV0LCBmbG9hdDIgX2Nvb3JkcykgCnsKCXJldHVybiBUZXh0dXJlRWZmZWN0X1MxX2MwX2MwX2MwKF9pbnB1dCwgZmxvYXQzeDIodW1hdHJpeF9TMV9jMF9jMCkgKiBfY29vcmRzLnh5MSk7Cn0KaGFsZjQgR2F1c3NpYW5Db252b2x1dGlvbl9TMV9jMChoYWxmNCBfaW5wdXQpIAp7CgloYWxmNCBfM19jb2xvciA9IGhhbGY0KDAuMCk7CglmbG9hdDIgXzRfY29vcmQgPSB2VHJhbnNmb3JtZWRDb29yZHNfMl9TMDsKCWZvciAoaW50IF81X2kgPSAwOyAoXzVfaSA8IDEzKTsgXzVfaSsrKSAoXzNfY29sb3IgKz0gKE1hdHJpeEVmZmVjdF9TMV9jMF9jMChfaW5wdXQsIChfNF9jb29yZCArIGZsb2F0MigodV8yX09mZnNldHNfUzFfYzBbKF81X2kgLyA0KV1bKF81X2kgJiAzKV0gKiB1XzBfSW5jcmVtZW50X1MxX2MwKSkpKSAqIHVfMV9LZXJuZWxfUzFfYzBbKF81X2kgLyA0KV1bKF81X2kgJiAzKV0pKTsKCXJldHVybiBfM19jb2xvcjsKfQpoYWxmNCBNYXRyaXhFZmZlY3RfUzEoaGFsZjQgX2lucHV0KSAKewoJcmV0dXJuIEdhdXNzaWFuQ29udm9sdXRpb25fUzFfYzAoX2lucHV0KTsKfQp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMCA9IGhhbGY0KDEpOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCWhhbGY0IG91dHB1dF9TMTsKCW91dHB1dF9TMSA9IE1hdHJpeEVmZmVjdF9TMShvdXRwdXRDb2xvcl9TMCk7Cgl7CgkJLy8gWGZlciBQcm9jZXNzb3I6IFBvcnRlciBEdWZmCgkJc2tfRnJhZ0NvbG9yID0gb3V0cHV0X1MxICogb3V0cHV0Q292ZXJhZ2VfUzA7Cgl9Cn0KAAAAAAABAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwAAAABAAAAAAAAAAIAAAAdAAAAAAAAAAEAAAAdAAAACAAAAAEAAAAQAAAAAAAAAFAAAAAAAAAAAQAAAAAAAAA=", "HRIAAAAAAAMAAAAAAQ4PZ72HLQCDR7H7777QGAAAAAAAAAAAKQAAUAAAABCC3BNJEEAAAACIUBDGCQAAAAAAIAAA7QAQAAH4AEAAB7ABAAAPYAIACAAAAAEICFIUWYLKBAAAAAAAAAAAAMJCQENIKAIBAAABAAAA6ADQAAHQA4AAB4AHAAAPABYAEAAAAACAIUAAAAQAAAAGMCBAMQAAAAAAAAAAAIAHAAAAAQAAAAAAAQQGAAAFAAAAAAAQAAAAAAAAAAAAAAAAAAAAAAAMGAIAAIAAAAAA": "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", "HQIAAAAAAAGAAAAAAIOP577774BRZ7X7777QCAAAAAAAAAAAVIBAYAAAAAAQAAIAAAACRRAAAAABAAAQAAAABIECAEAACAAAAAZQIEBSAAIAAAAAAAAJAAYAAAACAAAAAAACCA2QAAAAAAIAAAAAAAAAAAAAAAAAAAAAAAADAAAAAAAAAAAA": "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", "HQQAAAAAAAGAAAAAAIOP57ZDF37P7777777QCAAAAAAAAAAASABQAAAAEAAAAAAAEEBQAACQAAAAAAIAAAAAAAAAAAAAAAAAEAAAAAGDAEAAEAAAAAAA": "CAAAAExTS1NMAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDApIGZsYXQgb3V0IGhhbGY0IHZjb2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFByaW1pdGl2ZSBQcm9jZXNzb3IgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgl2Y29sb3JfUzAgPSBjb2xvcjsKCXNrX1Bvc2l0aW9uID0gcG9zaXRpb24ueHkwMTsKfQoAAAAAswEAAGxheW91dCAoYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMCwgaW5kZXggPSAwKSBvdXQgaGFsZjQgc2tfRnJhZ0NvbG9yOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmNvbG9yX1MwOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABUAAAAAQAAAAAAAAACAAAAHQAAAAAAAAABAAAACQAAAAgAAAABAAAADAAAAAAAAABQAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAAAAAAA", "HRJAAAAAAAMAAAAAAQ4PZ72HLQCDR7H7777QGAAAAACAAAAAACCAYAGEADZAAAAAAIAAAAAAOIAAAAAAAQAAAABAMQAAAAAAKAAAAAABAAAAB7IAAAAAAAAAAAAAAAAAYMAQAAQAAAAAA": "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", "HQQAAAAAAAGAAAAAAIOP57ZDF37P7777777QCAAAAAAAAAAACIBUFTBZQMKAAAAAAMAAAAAAAAAABZAAAAAAACAAAAAEBSAAKAAAAAABAAAAB7IAAAAAAAAAAAAAAAAAYMAQAAQAAAAAA": "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", "HQJAAAAAAAGAAAAAAIOP577774BRZ7X7777QCAAAAABAAAAAABBAMAASANBMYOMDCQAAAAADAAAAAAAAAAAOIAAAAAAAQAAAABAMQACGAAAAAAIAAAAAAAAAAAAAAAAAAAAAAAGDAEAAEAAAAAAA": "CAAAAExTS1ODAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKCWxheW91dChvZmZzZXQ9MTYpIGZsb2F0NCB1Y2lyY2xlX1MxOwp9CjsKbGF5b3V0KGxvY2F0aW9uID0gMCkgaW4gZmxvYXQyIHBvc2l0aW9uOwpsYXlvdXQobG9jYXRpb24gPSAxKSBpbiBmbG9hdDIgbG9jYWxDb29yZDsKbGF5b3V0KGxvY2F0aW9uID0gMCkgb3V0IGZsb2F0MiB2bG9jYWxDb29yZF9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFByaW1pdGl2ZSBQcm9jZXNzb3IgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgl2bG9jYWxDb29yZF9TMCA9IGxvY2FsQ29vcmQ7Cglza19Qb3NpdGlvbiA9IHBvc2l0aW9uLnh5MDE7Cn0KAAAAAAAIBQAAY29uc3QgaW50IGtGaWxsQUFfUzEgPSAxOwpjb25zdCBpbnQga0ludmVyc2VGaWxsQldfUzEgPSAyOwpjb25zdCBpbnQga0ludmVyc2VGaWxsQUFfUzEgPSAzOwpsYXlvdXQoYmluZGluZz0wKSB1bmlmb3JtIHNhbXBsZXIyRCB1VGV4dHVyZVNhbXBsZXJfMF9TMDsKbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKCWxheW91dChvZmZzZXQ9MTYpIGZsb2F0NCB1Y2lyY2xlX1MxOwp9CjsKbGF5b3V0KGxvY2F0aW9uID0gMCkgaW4gZmxvYXQyIHZsb2NhbENvb3JkX1MwOwpsYXlvdXQobG9jYXRpb24gPSAwLCBpbmRleCA9IDApIG91dCBoYWxmNCBza19GcmFnQ29sb3I7CmhhbGY0IENpcmNsZV9TMShoYWxmNCBfaW5wdXQpIAp7CgloYWxmNCBfdG1wXzBfaW5Db2xvciA9IF9pbnB1dDsKCWhhbGYgZDsKCWlmIChpbnQoMSkgPT0ga0ludmVyc2VGaWxsQldfUzEgfHwgaW50KDEpID09IGtJbnZlcnNlRmlsbEFBX1MxKSAKCXsKCQlkID0gaGFsZigobGVuZ3RoKCh1Y2lyY2xlX1MxLnh5IC0gc2tfRnJhZ0Nvb3JkLnh5KSAqIHVjaXJjbGVfUzEudykgLSAxLjApICogdWNpcmNsZV9TMS56KTsKCX0KCWVsc2UgCgl7CgkJZCA9IGhhbGYoKDEuMCAtIGxlbmd0aCgodWNpcmNsZV9TMS54eSAtIHNrX0ZyYWdDb29yZC54eSkgKiB1Y2lyY2xlX1MxLncpKSAqIHVjaXJjbGVfUzEueik7Cgl9CglpZiAoaW50KDEpID09IGtGaWxsQUFfUzEgfHwgaW50KDEpID09IGtJbnZlcnNlRmlsbEFBX1MxKSAKCXsKCQlyZXR1cm4gaGFsZjQoX2lucHV0ICogc2F0dXJhdGUoZCkpOwoJfQoJZWxzZSAKCXsKCQlyZXR1cm4gaGFsZjQoZCA+IDAuNSA/IF9pbnB1dCA6IGhhbGY0KDAuMCkpOwoJfQp9CnZvaWQgbWFpbigpIAp7CgkvLyBTdGFnZSAwLCBRdWFkUGVyRWRnZUFBR2VvbWV0cnlQcm9jZXNzb3IKCWhhbGY0IG91dHB1dENvbG9yX1MwID0gaGFsZjQoMSk7CglmbG9hdDIgdGV4Q29vcmQ7Cgl0ZXhDb29yZCA9IHZsb2NhbENvb3JkX1MwOwoJb3V0cHV0Q29sb3JfUzAgPSAoKHNhbXBsZSh1VGV4dHVyZVNhbXBsZXJfMF9TMCwgdGV4Q29vcmQpICogaGFsZjQoMSkpKTsKCWNvbnN0IGhhbGY0IG91dHB1dENvdmVyYWdlX1MwID0gaGFsZjQoMSk7CgloYWxmNCBvdXRwdXRfUzE7CglvdXRwdXRfUzEgPSBDaXJjbGVfUzEob3V0cHV0Q292ZXJhZ2VfUzApOwoJewoJCS8vIFhmZXIgUHJvY2Vzc29yOiBQb3J0ZXIgRHVmZgoJCXNrX0ZyYWdDb2xvciA9IG91dHB1dENvbG9yX1MwICogb3V0cHV0X1MxOwoJfQp9CgEAAAABAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFQAAAABAAAAAAAAAAIAAAAdAAAAAAAAAAEAAAAdAAAACAAAAAEAAAAQAAAAAAAAAEYAAAABAAAAAQAAAAUAAAAAAAAAAQAAAAUAAAAAAAAAAQAAAAAAAAA=", "E5QQAAAAMAAGEADCACRAAAAAMAACFQDBABRAAIXCAIAAAAAAHEAAAAAAAIAAAAAQGIAAAACQAAAAAAIAAAAP2AAAAAAAAAAAAAAAAAGDAEAAAAAAAAAA": "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", "HRIACAAAAAMAAAAAAQ4AANCELQCDR7H7777QGAAAAAAAAAAAGQDCIUFDGAQAAAAAAIAAB7QAAAAP4AAAAD7AAAAA72MJ54XDUIAAAAAQAAAAAYAAAAAIBYTZF4EBCAAAABQBD6P3AEAAAAACAAAAAYWE6SKB6FYFAAAIAAAAAAAAGAAAAAKM662BRAAAAAAARPEN6DYAAAACAAAAAAIIHCXHXUQEIAAAAAAAAAAAKAIVQRH6PYAAAAEAAAAABAAYQJC6J3YHAAAAAAAAAAAMQAIAAAABAAAAACAJAAKGAAAAAAIAAAAAAAAAAAAAAAAAAAAAAAGDAEAAAAAAAAAA": "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", "HQJAAAAAAAGAAAAAAIOP577774BRZ7X7777QCAAAAABAAAAAABBAMAASANBMYOMDCQAAAAABAAAAACBR44GFEAAAAAGAAAAACAAAAAAQENBMYOMDCQAAAAADAAAAAAAAAAAOIAAAAAAAQAAAABAMQAAAABDAAAAAAEAAAAH5AAAAAAAAAAAAAAAAADBQCAACAAAAAAA": "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", "HQIAAAAAAAGAAAAAAIOP577774BRZ7X7777QCAAAAAAAAAAAVIBAYAAAAAAQCAIAAAACRRAAAAABAEAQAAAABIECAEAQCAAAAAZQIEBSAAIBAAAAAAAJAAYAAAACAAAAAAACCA2QAAAAAAIAAAAAAAAAAAAAAAAAAAAAAAADAAAAEAAAAAAA": "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", "HQIAAAAAAAGAAAAAAIOP577774BRZ7X7777QCAAAAAAAAAAAVIBAYAAAAAAACAIAAAACRRAAAAAAAEAQAAAABIECAAAQCAAAAAZQIEBSAAABAAAAAAAJAAYAAAACAAAAAAACCA2QAAAAAAIAAAAAAAAAAAAAAAAAAAAAAAADAAAAEAAAAAAA": "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", "AXAQGAAAQAAIPCELAGEP777777777737AAAAAAAAAAAIBRUAARVBIBQEAAAEAAAAYAPQAAGAD4AABQA7AAAMAH6TKN7FYFAAAAAAEAAAAAGAAAAARDUYG4YHAEAAAAAWSG7R6AAAAAQAAAAAEBDEYT7ZOFIQAAAABAAAAABQAAAAAIFGB7HB2BAAAAAFQRH6PYAAAAAAAEAABAAYRTUYG4YHAEAAAAAAAAAEARLACH47WAIAAAAAEAAAABRAQFURX4PQAAAAAAAAAABAA4AAAACAAAAAAACCAYAAAACGAAAAAAIAAAAP2AAAAAAAAAAAAAAAAAGDAEAAAAAAAAAA": "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", "AWQAGAAAQAAIPCELAGEP777777777737AAAAAAAAAAAABZAAAAAAACAAAAAEBSAAKAAAAAABAAAAB7IAAAAAAAAAAAQAAAAAYMAQAAAAAAAAA": "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", "HRIAAAAAAAMAAAAAAQ4PZ72HLQCDR7H7777QGAAAAAAAAAAAIQCQAAACAAAAAZQIAAAAAAAAAAAAAABAA4AAAACAAAAAAACCAYAAAACQAAAAAAIAAAAAAAAAAAAAAAAAEAAAAAGDAEAAEAAAAAAA": "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", "HRJAAAAAAAMAAAAAAQ4PZ72HLQCDR7H7777QGAAAAACAAAAAACCAYABEA2CJQ4YGFEAAAAAGAAAAAAAAAAAMQAIAAAABAAAAACAJAAKQAAAAAAIAAAAP2AAAAAAAAAAAEAAAAAGDAEAAEAAAAAAA": "CAAAAExTS1PrAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKCWxheW91dChvZmZzZXQ9MTYpIGZsb2F0NCB1Y2lyY2xlX1MxOwp9CjsKbGF5b3V0KGxvY2F0aW9uID0gMCkgaW4gZmxvYXQyIHBvc2l0aW9uOwpsYXlvdXQobG9jYXRpb24gPSAxKSBpbiBoYWxmNCBjb2xvcjsKbGF5b3V0KGxvY2F0aW9uID0gMikgaW4gZmxvYXQyIGxvY2FsQ29vcmQ7CmxheW91dChsb2NhdGlvbiA9IDApIGZsYXQgb3V0IGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMSkgb3V0IGZsb2F0MiB2bG9jYWxDb29yZF9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFByaW1pdGl2ZSBQcm9jZXNzb3IgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgl2Y29sb3JfUzAgPSBjb2xvcjsKCXZsb2NhbENvb3JkX1MwID0gbG9jYWxDb29yZDsKCXNrX1Bvc2l0aW9uID0gcG9zaXRpb24ueHkwMTsKfQoAAAAAAE4FAABjb25zdCBpbnQga0ZpbGxBQV9TMSA9IDE7CmNvbnN0IGludCBrSW52ZXJzZUZpbGxCV19TMSA9IDI7CmNvbnN0IGludCBrSW52ZXJzZUZpbGxBQV9TMSA9IDM7CmxheW91dChiaW5kaW5nPTApIHVuaWZvcm0gc2FtcGxlcjJEIHVUZXh0dXJlU2FtcGxlcl8wX1MwOwpsYXlvdXQgKGJpbmRpbmc9MCkgdW5pZm9ybSB1bmlmb3JtQnVmZmVyCnsKCWxheW91dChvZmZzZXQ9MCkgZmxvYXQ0IHNrX1JUQWRqdXN0OwoJbGF5b3V0KG9mZnNldD0xNikgZmxvYXQ0IHVjaXJjbGVfUzE7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gZmxvYXQyIHZsb2NhbENvb3JkX1MwOwpsYXlvdXQobG9jYXRpb24gPSAwLCBpbmRleCA9IDApIG91dCBoYWxmNCBza19GcmFnQ29sb3I7CmhhbGY0IENpcmNsZV9TMShoYWxmNCBfaW5wdXQpIAp7CgloYWxmNCBfdG1wXzBfaW5Db2xvciA9IF9pbnB1dDsKCWhhbGYgZDsKCWlmIChpbnQoMSkgPT0ga0ludmVyc2VGaWxsQldfUzEgfHwgaW50KDEpID09IGtJbnZlcnNlRmlsbEFBX1MxKSAKCXsKCQlkID0gaGFsZigobGVuZ3RoKCh1Y2lyY2xlX1MxLnh5IC0gc2tfRnJhZ0Nvb3JkLnh5KSAqIHVjaXJjbGVfUzEudykgLSAxLjApICogdWNpcmNsZV9TMS56KTsKCX0KCWVsc2UgCgl7CgkJZCA9IGhhbGYoKDEuMCAtIGxlbmd0aCgodWNpcmNsZV9TMS54eSAtIHNrX0ZyYWdDb29yZC54eSkgKiB1Y2lyY2xlX1MxLncpKSAqIHVjaXJjbGVfUzEueik7Cgl9CglpZiAoaW50KDEpID09IGtGaWxsQUFfUzEgfHwgaW50KDEpID09IGtJbnZlcnNlRmlsbEFBX1MxKSAKCXsKCQlyZXR1cm4gaGFsZjQoX2lucHV0ICogc2F0dXJhdGUoZCkpOwoJfQoJZWxzZSAKCXsKCQlyZXR1cm4gaGFsZjQoZCA+IDAuNSA/IF9pbnB1dCA6IGhhbGY0KDAuMCkpOwoJfQp9CnZvaWQgbWFpbigpIAp7CgkvLyBTdGFnZSAwLCBRdWFkUGVyRWRnZUFBR2VvbWV0cnlQcm9jZXNzb3IKCWhhbGY0IG91dHB1dENvbG9yX1MwOwoJb3V0cHV0Q29sb3JfUzAgPSB2Y29sb3JfUzA7CglmbG9hdDIgdGV4Q29vcmQ7Cgl0ZXhDb29yZCA9IHZsb2NhbENvb3JkX1MwOwoJb3V0cHV0Q29sb3JfUzAgPSAoKHNhbXBsZSh1VGV4dHVyZVNhbXBsZXJfMF9TMCwgdGV4Q29vcmQpICogb3V0cHV0Q29sb3JfUzApKTsKCWNvbnN0IGhhbGY0IG91dHB1dENvdmVyYWdlX1MwID0gaGFsZjQoMSk7CgloYWxmNCBvdXRwdXRfUzE7CglvdXRwdXRfUzEgPSBDaXJjbGVfUzEob3V0cHV0Q292ZXJhZ2VfUzApOwoJewoJCS8vIFhmZXIgUHJvY2Vzc29yOiBQb3J0ZXIgRHVmZgoJCXNrX0ZyYWdDb2xvciA9IG91dHB1dENvbG9yX1MwICogb3V0cHV0X1MxOwoJfQp9CgAAAQAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYAAAAAEAAAAAAAAAAwAAAB0AAAAAAAAAAQAAAAkAAAAIAAAAAQAAAB0AAAAMAAAAAQAAABQAAAAAAAAAUAAAAAEAAAABAAAABQAAAAAAAAABAAAABQAAAAAAAAABAAAA/QAAAA==", "I4AAAAAAAEAAAAABBYBQAAAAAMIAGEABBYAAAAAAQAAAAAAAQCIACAAAABDAAAAAAEAAAAH5AAAAAAIAAAABAAAAAABAAAAAAAAAAAA": "CAAAAExTS1PRFgAAY29uc3QgZmxvYXQgUFJFQ0lTSU9OID0gNC4wMDAwMDA7CmNvbnN0IGZsb2F0IE1BWF9GSVhFRF9SRVNPTFZFX0xFVkVMID0gNS4wMDAwMDA7CmNvbnN0IGZsb2F0IE1BWF9GSVhFRF9TRUdNRU5UUyA9IDMyLjAwMDAwMDsKbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKCWxheW91dChvZmZzZXQ9MTYpIGZsb2F0NCB1YWZmaW5lTWF0cml4X1MwOwoJbGF5b3V0KG9mZnNldD0zMikgZmxvYXQyIHV0cmFuc2xhdGVfUzA7CglsYXlvdXQob2Zmc2V0PTQwKSBoYWxmNCB1Y29sb3JfUzA7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcmVzb2x2ZUxldmVsX2FuZF9pZHg7CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGZsb2F0NCBwMDE7CmxheW91dChsb2NhdGlvbiA9IDIpIGluIGZsb2F0NCBwMjM7CmxheW91dChsb2NhdGlvbiA9IDMpIGluIGZsb2F0MiBmYW5Qb2ludDsKLy8gUmV0dXJucyB0aGUgbGVuZ3RoIHNxdWFyZWQgb2YgdGhlIGxhcmdlc3QgZm9yd2FyZCBkaWZmZXJlbmNlIGZyb20gV2FuZydzIGN1YmljIGZvcm11bGEuCmZsb2F0IHdhbmdzX2Zvcm11bGFfbWF4X2ZkaWZmX3BvdzIoZmxvYXQyIHAwLCBmbG9hdDIgcDEsIGZsb2F0MiBwMiwgZmxvYXQyIHAzLCAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZsb2F0MngyIG1hdHJpeCkgCnsKCWZsb2F0MiBkMCA9IG1hdHJpeCAqIChmbWEoZmxvYXQyKC0yKSwgcDEsIHAyKSArIHAwKTsKCWZsb2F0MiBkMSA9IG1hdHJpeCAqIChmbWEoZmxvYXQyKC0yKSwgcDIsIHAzKSArIHAxKTsKCXJldHVybiBtYXgoZG90KGQwLGQwKSwgZG90KGQxLGQxKSk7Cn0KZmxvYXQgd2FuZ3NfZm9ybXVsYV9jdWJpYyhmbG9hdCBfcHJlY2lzaW9uXywgZmxvYXQyIHAwLCBmbG9hdDIgcDEsIGZsb2F0MiBwMiwgZmxvYXQyIHAzLCAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZsb2F0MngyIG1hdHJpeCkgCnsKCWZsb2F0IG0gPSB3YW5nc19mb3JtdWxhX21heF9mZGlmZl9wb3cyKHAwLCBwMSwgcDIsIHAzLCBtYXRyaXgpOwoJcmV0dXJuIG1heChjZWlsKHNxcnQoMC43NTAwMDAgKiBfcHJlY2lzaW9uXyAqIHNxcnQobSkpKSwgMS4wKTsKfQpmbG9hdCB3YW5nc19mb3JtdWxhX2N1YmljX2xvZzIoZmxvYXQgX3ByZWNpc2lvbl8sIGZsb2F0MiBwMCwgZmxvYXQyIHAxLCBmbG9hdDIgcDIsIGZsb2F0MiBwMywgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGZsb2F0MngyIG1hdHJpeCkgCnsKCWZsb2F0IG0gPSB3YW5nc19mb3JtdWxhX21heF9mZGlmZl9wb3cyKHAwLCBwMSwgcDIsIHAzLCBtYXRyaXgpOwoJcmV0dXJuIGNlaWwobG9nMihtYXgoMC41NjI1MDAgKiBfcHJlY2lzaW9uXyAqIF9wcmVjaXNpb25fICogbSwgMS4wKSkgKiAuMjUpOwp9CmZsb2F0IHdhbmdzX2Zvcm11bGFfY29uaWNfcG93MihmbG9hdCBfcHJlY2lzaW9uXywgZmxvYXQyIHAwLCBmbG9hdDIgcDEsIGZsb2F0MiBwMiwgZmxvYXQgdykgCnsKCS8vIFRyYW5zbGF0ZSB0aGUgYm91bmRpbmcgYm94IGNlbnRlciB0byB0aGUgb3JpZ2luLgoJZmxvYXQyIEMgPSAobWluKG1pbihwMCwgcDEpLCBwMikgKyBtYXgobWF4KHAwLCBwMSksIHAyKSkgKiAwLjU7CglwMCAtPSBDOwoJcDEgLT0gQzsKCXAyIC09IEM7CgkvLyBDb21wdXRlIG1heCBsZW5ndGguCglmbG9hdCBtID0gc3FydChtYXgobWF4KGRvdChwMCxwMCksIGRvdChwMSxwMSkpLCBkb3QocDIscDIpKSk7CgkvLyBDb21wdXRlIGZvcndhcmQgZGlmZmVyZW5jZXMuCglmbG9hdDIgZHAgPSBmbWEoZmxvYXQyKC0yLjAgKiB3KSwgcDEsIHAwKSArIHAyOwoJZmxvYXQgZHcgPSBhYnMoZm1hKC0yLjAsIHcsIDIuMCkpOwoJLy8gQ29tcHV0ZSBudW1lcmF0b3IgYW5kIGRlbm9taW5hdG9yIGZvciBwYXJhbWV0cmljIHN0ZXAgc2l6ZSBvZiBsaW5lYXJpemF0aW9uLiBIZXJlLCB0aGUKCS8vIGVwc2lsb24gcmVmZXJlbmNlZCBmcm9tIHRoZSBjaXRlZCBwYXBlciBpcyAxL3ByZWNpc2lvbi4KCWZsb2F0IHJwX21pbnVzXzEgPSBtYXgoMC4wLCBmbWEobSwgX3ByZWNpc2lvbl8sIC0xLjApKTsKCWZsb2F0IG51bWVyID0gbGVuZ3RoKGRwKSAqIF9wcmVjaXNpb25fICsgcnBfbWludXNfMSAqIGR3OwoJZmxvYXQgZGVub20gPSA0ICogbWluKHcsIDEuMCk7CglyZXR1cm4gbnVtZXIvZGVub207Cn0KZmxvYXQgd2FuZ3NfZm9ybXVsYV9jb25pYyhmbG9hdCBfcHJlY2lzaW9uXywgZmxvYXQyIHAwLCBmbG9hdDIgcDEsIGZsb2F0MiBwMiwgZmxvYXQgdykgCnsKCWZsb2F0IG4yID0gd2FuZ3NfZm9ybXVsYV9jb25pY19wb3cyKF9wcmVjaXNpb25fLCBwMCwgcDEsIHAyLCB3KTsKCXJldHVybiBtYXgoY2VpbChzcXJ0KG4yKSksIDEuMCk7Cn0KZmxvYXQgd2FuZ3NfZm9ybXVsYV9jb25pY19sb2cyKGZsb2F0IF9wcmVjaXNpb25fLCBmbG9hdDIgcDAsIGZsb2F0MiBwMSwgZmxvYXQyIHAyLCBmbG9hdCB3KSAKewoJZmxvYXQgbjIgPSB3YW5nc19mb3JtdWxhX2NvbmljX3BvdzIoX3ByZWNpc2lvbl8sIHAwLCBwMSwgcDIsIHcpOwoJcmV0dXJuIGNlaWwobG9nMihtYXgobjIsIDEuMCkpICogLjUpOwp9CmJvb2wgaXNfY29uaWNfY3VydmUoKSAKewoJcmV0dXJuIGlzaW5mKHAyMy53KTsKfQpib29sIGlzX3RyaWFuZ3VsYXJfY29uaWNfY3VydmUoKSAKewoJcmV0dXJuIGlzaW5mKHAyMy56KTsKfQpmbG9hdCBsZGV4cF9wb3J0YWJsZShmbG9hdCB4LCBmbG9hdCBwKSAKewoJcmV0dXJuIHggKiBleHAyKHApOwp9CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIHRlc3NlbGxhdGVfTWlkZGxlT3V0U2hhZGVyCglmbG9hdDJ4MiBBRkZJTkVfTUFUUklYID0gZmxvYXQyeDIodWFmZmluZU1hdHJpeF9TMCk7CglmbG9hdDIgVFJBTlNMQVRFID0gdXRyYW5zbGF0ZV9TMDsKCWZsb2F0IHJlc29sdmVMZXZlbCA9IHJlc29sdmVMZXZlbF9hbmRfaWR4Lng7CglmbG9hdCBpZHhJblJlc29sdmVMZXZlbCA9IHJlc29sdmVMZXZlbF9hbmRfaWR4Lnk7CglmbG9hdDIgbG9jYWxjb29yZDsKCS8vIEEgbmVnYXRpdmUgcmVzb2x2ZSBsZXZlbCBtZWFucyB0aGlzIGlzIHRoZSBmYW4gcG9pbnQuCglpZiAocmVzb2x2ZUxldmVsIDwgMCkgCgl7CgkJbG9jYWxjb29yZCA9IGZhblBvaW50OwoJfQoJZWxzZSAgICAgICAgICAgIGlmIChpc190cmlhbmd1bGFyX2NvbmljX2N1cnZlKCkpIAoJewoJCS8vIFRoaXMgcGF0Y2ggaXMgYW4gZXhhY3QgdHJpYW5nbGUuCgkJbG9jYWxjb29yZCA9IChyZXNvbHZlTGV2ZWwgIT0gMCkgICAgICA/IHAwMS56dyAgICAgICAgICAgICAgICAgICAgICAgICAgIDogKGlkeEluUmVzb2x2ZUxldmVsICE9IDApID8gcDIzLnh5ICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBwMDEueHk7Cgl9CgllbHNlIAoJewoJCWZsb2F0MiBwMD1wMDEueHksIHAxPXAwMS56dywgcDI9cDIzLnh5LCBwMz1wMjMuenc7CgkJZmxvYXQgdyA9IC0xOwoJCS8vIHcgPCAwIHRlbGxzIHVzIHRvIHRyZWF0IHRoZSBpbnN0YW5jZSBhcyBhbiBpbnRlZ3JhbCBjdWJpYy4KCQlmbG9hdCBtYXhSZXNvbHZlTGV2ZWw7CgkJaWYgKGlzX2NvbmljX2N1cnZlKCkpIAoJCXsKCQkJLy8gQ29uaWNzIGFyZSAzIHBvaW50cywgd2l0aCB0aGUgd2VpZ2h0IGluIHAzLgoJCQl3ID0gcDMueDsKCQkJbWF4UmVzb2x2ZUxldmVsID0gd2FuZ3NfZm9ybXVsYV9jb25pY19sb2cyKFBSRUNJU0lPTiwgQUZGSU5FX01BVFJJWCAqIHAwLCAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgQUZGSU5FX01BVFJJWCAqIHAxLCAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgQUZGSU5FX01BVFJJWCAqIHAyLCB3KTsKCQkJcDEgKj0gdzsKCQkJLy8gVW5wcm9qZWN0IHAxLgoJCQlwMyA9IHAyOwoJCQkvLyBEdXBsaWNhdGUgdGhlIGVuZHBvaW50IGZvciBzaGFyZWQgY29kZSB0aGF0IGFsc28gcnVucyBvbiBjdWJpY3MuCgkJfQoJCWVsc2UgCgkJewoJCQkvLyBUaGUgcGF0Y2ggaXMgYW4gaW50ZWdyYWwgY3ViaWMuCgkJCW1heFJlc29sdmVMZXZlbCA9IHdhbmdzX2Zvcm11bGFfY3ViaWNfbG9nMihQUkVDSVNJT04sIHAwLCBwMSwgcDIsIHAzLCAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIEFGRklORV9NQVRSSVgpOwoJCX0KCQlpZiAocmVzb2x2ZUxldmVsID4gbWF4UmVzb2x2ZUxldmVsKSAKCQl7CgkJCS8vIFRoaXMgdmVydGV4IGlzIGF0IGEgaGlnaGVyIHJlc29sdmUgbGV2ZWwgdGhhbiB3ZSBuZWVkLiBEZW1vdGUgdG8gYSBsb3dlcgoJCQkvLyByZXNvbHZlTGV2ZWwsIHdoaWNoIHdpbGwgcHJvZHVjZSBhIGRlZ2VuZXJhdGUgdHJpYW5nbGUuCgkJCWlkeEluUmVzb2x2ZUxldmVsID0gZmxvb3IobGRleHBfcG9ydGFibGUoaWR4SW5SZXNvbHZlTGV2ZWwsICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIG1heFJlc29sdmVMZXZlbCAtIHJlc29sdmVMZXZlbCkpOwoJCQlyZXNvbHZlTGV2ZWwgPSBtYXhSZXNvbHZlTGV2ZWw7CgkJfQoJCS8vIFByb21vdGUgb3VyIGxvY2F0aW9uIHRvIGEgZGlzY3JldGUgcG9zaXRpb24gaW4gdGhlIG1heGltdW0gZml4ZWQgcmVzb2x2ZSBsZXZlbC4KCQkvLyBUaGlzIGlzIGV4dHJhIHBhcmFub2lhIHRvIGVuc3VyZSB3ZSBnZXQgdGhlIGV4YWN0IHNhbWUgZnAzMiBjb29yZGluYXRlcyBmb3IKCQkvLyBjb2xvY2F0ZWQgcG9pbnRzIGZyb20gZGlmZmVyZW50IHJlc29sdmUgbGV2ZWxzIChlLmcuLCB0aGUgdmVydGljZXMgVD0zLzQgYW5kCgkJLy8gVD02Lzggc2hvdWxkIGJlIGV4YWN0bHkgY29sb2NhdGVkKS4KCQlmbG9hdCBmaXhlZFZlcnRleElEID0gZmxvb3IoLjUgKyBsZGV4cF9wb3J0YWJsZSggICAgICAgICAgICAgICAgICAgICAgICBpZHhJblJlc29sdmVMZXZlbCwgTUFYX0ZJWEVEX1JFU09MVkVfTEVWRUwgLSByZXNvbHZlTGV2ZWwpKTsKCQlpZiAoMCA8IGZpeGVkVmVydGV4SUQgJiYgZml4ZWRWZXJ0ZXhJRCA8IE1BWF9GSVhFRF9TRUdNRU5UUykgCgkJewoJCQlmbG9hdCBUID0gZml4ZWRWZXJ0ZXhJRCAqICgxIC8gTUFYX0ZJWEVEX1NFR01FTlRTKTsKCQkJLy8gRXZhbHVhdGUgYXQgVC4gVXNlIERlIENhc3RlbGphdSdzIGZvciBpdHMgYWNjdXJhY3kgYW5kIHN0YWJpbGl0eS4KCQkJZmxvYXQyIGFiID0gbWl4KHAwLCBwMSwgVCk7CgkJCWZsb2F0MiBiYyA9IG1peChwMSwgcDIsIFQpOwoJCQlmbG9hdDIgY2QgPSBtaXgocDIsIHAzLCBUKTsKCQkJZmxvYXQyIGFiYyA9IG1peChhYiwgYmMsIFQpOwoJCQlmbG9hdDIgYmNkID0gbWl4KGJjLCBjZCwgVCk7CgkJCWZsb2F0MiBhYmNkID0gbWl4KGFiYywgYmNkLCBUKTsKCQkJLy8gRXZhbHVhdGUgdGhlIGNvbmljIHdlaWdodCBhdCBULgoJCQlmbG9hdCB1ID0gbWl4KDEuMCwgdywgVCk7CgkJCWZsb2F0IHYgPSB3ICsgMSAtIHU7CgkJCS8vID09IG1peCh3LCAxLCBUKQoJCQlmbG9hdCB1diA9IG1peCh1LCB2LCBUKTsKCQkJbG9jYWxjb29yZCA9ICh3IDwgMCkgPyAvKmN1YmljKi8gYWJjZCA6IC8qY29uaWMqLyBhYmMvdXY7CgkJfQoJCWVsc2UgCgkJewoJCQlsb2NhbGNvb3JkID0gKGZpeGVkVmVydGV4SUQgPT0gMCkgPyBwMC54eSA6IHAzLnh5OwoJCX0KCX0KCWZsb2F0MiB2ZXJ0ZXhwb3MgPSBBRkZJTkVfTUFUUklYICogbG9jYWxjb29yZCArIFRSQU5TTEFURTsKCXNrX1Bvc2l0aW9uID0gdmVydGV4cG9zLnh5MDE7Cn0KAAAAAAAAALYBAABsYXlvdXQgKGJpbmRpbmc9MCkgdW5pZm9ybSB1bmlmb3JtQnVmZmVyCnsKCWxheW91dChvZmZzZXQ9MCkgZmxvYXQ0IHNrX1JUQWRqdXN0OwoJbGF5b3V0KG9mZnNldD0xNikgZmxvYXQ0IHVhZmZpbmVNYXRyaXhfUzA7CglsYXlvdXQob2Zmc2V0PTMyKSBmbG9hdDIgdXRyYW5zbGF0ZV9TMDsKCWxheW91dChvZmZzZXQ9NDApIGhhbGY0IHVjb2xvcl9TMDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDAsIGluZGV4ID0gMCkgb3V0IGhhbGY0IHNrX0ZyYWdDb2xvcjsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIHRlc3NlbGxhdGVfTWlkZGxlT3V0U2hhZGVyCgloYWxmNCBvdXRwdXRDb2xvcl9TMCA9IHVjb2xvcl9TMDsKCWNvbnN0IGhhbGY0IG91dHB1dENvdmVyYWdlX1MwID0gaGFsZjQoMSk7Cgl7CgkJLy8gWGZlciBQcm9jZXNzb3I6IERpc2FibGUgQ29sb3IKCX0KfQoAAAAAAAABAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFgAAAABAAAAAgAAAAEAAAAdAAAAAAAAAAEAAAAIAAAAAwAAAB8AAAAAAAAAAgAAAB8AAAAQAAAAAgAAAB0AAAAgAAAAAgAAACgAAABGAAAAAAAAAAAAAAD9AAAA", "HQIAAAAAAAGAAAAAAIOP577774BRZ7X7777QCAAAAAAAAAAAVIBAAAIBAAAAAMYECAZAAAAQAAAAAAEQAMAAAABAAAAAAABBAMAAAACQAAAAAAIAAAAAAAAAAAAAAAAAAAAAAAADAAAAEAAAAAAA": "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", "HRIAAAAAAAMAAAAAAQ4PZ72HLQCDR7H7777QGAAAAAAAAAAAHADCIUFDGAQAAAAAAIAAB7QAAAAP4AAAAD7AAAAA72MJ54XDUIAAAAAQAAAAAYAAAAAIBYTZF4EBCAAAABQBD6P3AEAAAAACAAAAAYWE6SKB6FYFAAAIAAAAAAAAGAAAAAKM662BRAAAAAAARPEN6DYAAAACAAAAAAIIHCXHXUQEIAAAAAAAAAAAKAIVQRH6PYAAAAEAAAAABAAYQJC6J3YHAAAAAAAAAAAFAAIAQAAAAAEADEBAAAAAAAAAAAAAADEACAAAAAIAAAAAQCIACACGAAAAAAIAAAAAAAAAAAAAAAAAAAAAAAGDAEAAEAAAAAAA": "CAAAAExTS1PFAwAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKCWxheW91dChvZmZzZXQ9MTYpIGZsb2F0NCB1c2NhbGVfUzFfYzBfYzBbMl07CglsYXlvdXQob2Zmc2V0PTQ4KSBmbG9hdDQgdWJpYXNfUzFfYzBfYzBbMl07CglsYXlvdXQob2Zmc2V0PTgwKSBoYWxmIHV0aHJlc2hvbGRfUzFfYzBfYzA7CglsYXlvdXQob2Zmc2V0PTk2KSBmbG9hdDN4MyB1bWF0cml4X1MxX2MwX2MxOwoJbGF5b3V0KG9mZnNldD0xNDQpIGhhbGY0IHVsZWZ0Qm9yZGVyQ29sb3JfUzFfYzA7CglsYXlvdXQob2Zmc2V0PTE1MikgaGFsZjQgdXJpZ2h0Qm9yZGVyQ29sb3JfUzFfYzA7CglsYXlvdXQob2Zmc2V0PTE2MCkgZmxvYXQzeDMgdW1hdHJpeF9TMjsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDIpIGluIGZsb2F0MiBsb2NhbENvb3JkOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IG91dCBoYWxmNCB2Y29sb3JfUzA7CmxheW91dChsb2NhdGlvbiA9IDEpIG91dCBmbG9hdDIgdlRyYW5zZm9ybWVkQ29vcmRzXzVfUzA7CmxheW91dChsb2NhdGlvbiA9IDIpIG91dCBmbG9hdDIgdlRyYW5zZm9ybWVkQ29vcmRzXzdfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmNvbG9yX1MwID0gY29sb3I7Cglza19Qb3NpdGlvbiA9IHBvc2l0aW9uLnh5MDE7Cgl7CgkJdlRyYW5zZm9ybWVkQ29vcmRzXzVfUzAgPSBmbG9hdDN4Mih1bWF0cml4X1MxX2MwX2MxKSAqIGxvY2FsQ29vcmQueHkxOwoJfQoJewoJCXZUcmFuc2Zvcm1lZENvb3Jkc183X1MwID0gZmxvYXQzeDIodW1hdHJpeF9TMikgKiBsb2NhbENvb3JkLnh5MTsKCX0KfQoAAAAAAAAAGgoAAGxheW91dChiaW5kaW5nPTApIHVuaWZvcm0gc2FtcGxlcjJEIHVUZXh0dXJlU2FtcGxlcl8wX1MyOwpsYXlvdXQgKGJpbmRpbmc9MCkgdW5pZm9ybSB1bmlmb3JtQnVmZmVyCnsKCWxheW91dChvZmZzZXQ9MCkgZmxvYXQ0IHNrX1JUQWRqdXN0OwoJbGF5b3V0KG9mZnNldD0xNikgZmxvYXQ0IHVzY2FsZV9TMV9jMF9jMFsyXTsKCWxheW91dChvZmZzZXQ9NDgpIGZsb2F0NCB1Ymlhc19TMV9jMF9jMFsyXTsKCWxheW91dChvZmZzZXQ9ODApIGhhbGYgdXRocmVzaG9sZF9TMV9jMF9jMDsKCWxheW91dChvZmZzZXQ9OTYpIGZsb2F0M3gzIHVtYXRyaXhfUzFfYzBfYzE7CglsYXlvdXQob2Zmc2V0PTE0NCkgaGFsZjQgdWxlZnRCb3JkZXJDb2xvcl9TMV9jMDsKCWxheW91dChvZmZzZXQ9MTUyKSBoYWxmNCB1cmlnaHRCb3JkZXJDb2xvcl9TMV9jMDsKCWxheW91dChvZmZzZXQ9MTYwKSBmbG9hdDN4MyB1bWF0cml4X1MyOwp9CjsKbGF5b3V0KGxvY2F0aW9uID0gMCkgZmxhdCBpbiBoYWxmNCB2Y29sb3JfUzA7CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGZsb2F0MiB2VHJhbnNmb3JtZWRDb29yZHNfNV9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMikgaW4gZmxvYXQyIHZUcmFuc2Zvcm1lZENvb3Jkc183X1MwOwpsYXlvdXQobG9jYXRpb24gPSAwLCBpbmRleCA9IDApIG91dCBoYWxmNCBza19GcmFnQ29sb3I7CmhhbGY0IER1YWxJbnRlcnZhbENvbG9yaXplcl9TMV9jMF9jMChoYWxmNCBfaW5wdXQsIGZsb2F0MiBfY29vcmRzKSAKewoJaGFsZjQgX3RtcF8wX2luQ29sb3IgPSBfaW5wdXQ7CglmbG9hdDIgX3RtcF8xX2Nvb3JkcyA9IF9jb29yZHM7CgloYWxmIHQgPSBoYWxmKF90bXBfMV9jb29yZHMueCk7CglmbG9hdDQgczsKCWZsb2F0NCBiOwoJaWYgKHQgPCB1dGhyZXNob2xkX1MxX2MwX2MwKSAKCXsKCQlzID0gdXNjYWxlX1MxX2MwX2MwWzBdOwoJCWIgPSB1Ymlhc19TMV9jMF9jMFswXTsKCX0KCWVsc2UgCgl7CgkJcyA9IHVzY2FsZV9TMV9jMF9jMFsxXTsKCQliID0gdWJpYXNfUzFfYzBfYzBbMV07Cgl9CglyZXR1cm4gaGFsZjQoaGFsZjQoZmxvYXQodCkgKiBzICsgYikpOwp9CmhhbGY0IExpbmVhckxheW91dF9TMV9jMF9jMV9jMChoYWxmNCBfaW5wdXQpIAp7CgloYWxmNCBfdG1wXzJfaW5Db2xvciA9IF9pbnB1dDsKCWZsb2F0MiBfdG1wXzNfY29vcmRzID0gdlRyYW5zZm9ybWVkQ29vcmRzXzVfUzA7CglyZXR1cm4gaGFsZjQoaGFsZjQoaGFsZihfdG1wXzNfY29vcmRzLngpICsgOS45OTk5OTk3NDczNzg3NTE2ZS0wNiwgMS4wLCAwLjAsIDAuMCkpOwp9CmhhbGY0IE1hdHJpeEVmZmVjdF9TMV9jMF9jMShoYWxmNCBfaW5wdXQpIAp7CglyZXR1cm4gTGluZWFyTGF5b3V0X1MxX2MwX2MxX2MwKF9pbnB1dCk7Cn0KaGFsZjQgQ2xhbXBlZEdyYWRpZW50X1MxX2MwKGhhbGY0IF9pbnB1dCkgCnsKCWhhbGY0IF90bXBfNF9pbkNvbG9yID0gX2lucHV0OwoJaGFsZjQgdCA9IE1hdHJpeEVmZmVjdF9TMV9jMF9jMShfdG1wXzRfaW5Db2xvcik7CgloYWxmNCBvdXRDb2xvcjsKCWlmICghYm9vbChpbnQoMSkpICYmIHQueSA8IDAuMCkgCgl7CgkJb3V0Q29sb3IgPSBoYWxmNCgwLjApOwoJfQoJZWxzZSBpZiAodC54IDwgMC4wKSAKCXsKCQlvdXRDb2xvciA9IHVsZWZ0Qm9yZGVyQ29sb3JfUzFfYzA7Cgl9CgllbHNlIGlmICh0LnggPiAxLjApIAoJewoJCW91dENvbG9yID0gdXJpZ2h0Qm9yZGVyQ29sb3JfUzFfYzA7Cgl9CgllbHNlIAoJewoJCW91dENvbG9yID0gRHVhbEludGVydmFsQ29sb3JpemVyX1MxX2MwX2MwKF90bXBfNF9pbkNvbG9yLCBmbG9hdDIoaGFsZjIodC54LCAwLjApKSk7Cgl9CglpZiAoYm9vbChpbnQoMCkpKSAKCXsKCQlvdXRDb2xvci54eXogKj0gb3V0Q29sb3IudzsKCX0KCXJldHVybiBoYWxmNChvdXRDb2xvcik7Cn0KaGFsZjQgT3ZlcnJpZGVJbnB1dF9TMShoYWxmNCBfaW5wdXQpIAp7CgloYWxmNCBfdG1wXzVfaW5Db2xvciA9IF9pbnB1dDsKCXJldHVybiBoYWxmNChDbGFtcGVkR3JhZGllbnRfUzFfYzAoaGFsZjQoMS4wLDEuMCwxLjAsMS4wKSkpOwp9CmhhbGY0IFRleHR1cmVFZmZlY3RfUzJfYzAoaGFsZjQgX2lucHV0KSAKewoJcmV0dXJuIHNhbXBsZSh1VGV4dHVyZVNhbXBsZXJfMF9TMiwgdlRyYW5zZm9ybWVkQ29vcmRzXzdfUzApLnJycnI7Cn0KaGFsZjQgTWF0cml4RWZmZWN0X1MyKGhhbGY0IF9pbnB1dCkgCnsKCXJldHVybiBUZXh0dXJlRWZmZWN0X1MyX2MwKF9pbnB1dCk7Cn0Kdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzA7CglvdXRwdXRDb2xvcl9TMCA9IHZjb2xvcl9TMDsKCWNvbnN0IGhhbGY0IG91dHB1dENvdmVyYWdlX1MwID0gaGFsZjQoMSk7CgloYWxmNCBvdXRwdXRfUzE7CglvdXRwdXRfUzEgPSBPdmVycmlkZUlucHV0X1MxKG91dHB1dENvbG9yX1MwKTsKCWhhbGY0IG91dHB1dF9TMjsKCW91dHB1dF9TMiA9IE1hdHJpeEVmZmVjdF9TMihvdXRwdXRDb3ZlcmFnZV9TMCk7Cgl7CgkJLy8gWGZlciBQcm9jZXNzb3I6IFBvcnRlciBEdWZmCgkJc2tfRnJhZ0NvbG9yID0gb3V0cHV0X1MxICogb3V0cHV0X1MyOwoJfQp9CgAAAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYAAAAAEAAAAAAAAAAwAAAB0AAAAAAAAAAQAAAAkAAAAIAAAAAQAAAB0AAAAMAAAAAQAAABQAAAAAAAAARgAAAAEAAAABAAAABQAAAAAAAAABAAAABQAAAAAAAAABAAAAAAAAAA==", "JAAQAAAAAEHAAAAAAAAAAAAAQAAAAAAAQCIACAAKAAAAABAAAAAP2AAAAAAQAAAAAAAAAAACAAAAAAAAAAAA": "CAAAAExTS1MXAgAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKCWxheW91dChvZmZzZXQ9MTYpIGZsb2F0NCB1YWZmaW5lTWF0cml4X1MwOwoJbGF5b3V0KG9mZnNldD0zMikgZmxvYXQyIHV0cmFuc2xhdGVfUzA7CglsYXlvdXQob2Zmc2V0PTQwKSBoYWxmNCB1Y29sb3JfUzA7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgaW5wdXRQb2ludDsKdm9pZCBtYWluKCkgCnsKCS8vIFByaW1pdGl2ZSBQcm9jZXNzb3IgdGVzc2VsbGF0ZV9TaW1wbGVUcmlhbmdsZVNoYWRlcgoJZmxvYXQyeDIgQUZGSU5FX01BVFJJWCA9IGZsb2F0MngyKHVhZmZpbmVNYXRyaXhfUzApOwoJZmxvYXQyIFRSQU5TTEFURSA9IHV0cmFuc2xhdGVfUzA7CglmbG9hdDIgbG9jYWxjb29yZCA9IGlucHV0UG9pbnQ7CglmbG9hdDIgdmVydGV4cG9zID0gQUZGSU5FX01BVFJJWCAqIGxvY2FsY29vcmQgKyBUUkFOU0xBVEU7Cglza19Qb3NpdGlvbiA9IHZlcnRleHBvcy54eTAxOwp9CgAAAAAAuwEAAGxheW91dCAoYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7CglsYXlvdXQob2Zmc2V0PTE2KSBmbG9hdDQgdWFmZmluZU1hdHJpeF9TMDsKCWxheW91dChvZmZzZXQ9MzIpIGZsb2F0MiB1dHJhbnNsYXRlX1MwOwoJbGF5b3V0KG9mZnNldD00MCkgaGFsZjQgdWNvbG9yX1MwOwp9CjsKbGF5b3V0KGxvY2F0aW9uID0gMCwgaW5kZXggPSAwKSBvdXQgaGFsZjQgc2tfRnJhZ0NvbG9yOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgdGVzc2VsbGF0ZV9TaW1wbGVUcmlhbmdsZVNoYWRlcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzAgPSB1Y29sb3JfUzA7Cgljb25zdCBoYWxmNCBvdXRwdXRDb3ZlcmFnZV9TMCA9IGhhbGY0KDEpOwoJewoJCS8vIFhmZXIgUHJvY2Vzc29yOiBEaXNhYmxlIENvbG9yCgl9Cn0KAAAAAAABAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADAAAAABAAAAAAAAAAEAAAAdAAAAAAAAAAEAAAAIAAAAAAAAAAoAAAAAAAAAAAAAAP0AAAA=", "HQIAAAAAAAGAAAAAAIOP577774BRZ7X7777QCAAAAAAAAAAAVIBACAABAAAAAMYECAZAAEAAAAAAAAEQAMAAAABAAAAAAABBAMAAAACQAAAAAAIAAAAAAAAAAAAAAAAAAAAAAAADAAAAEAAAAAAA": "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", "HQIAAAAAAAGAAAAAAIOP577774BRZ7X7777QCAAAAAAAAAAAVIBAAAABAAAAAMYECAZAAAAAAAAAAAEQAMAAAABAAAAAAABBAMAAAACQAAAAAAIAAAAAAAAAAAAAAAAAAAAAAAADAAAAEAAAAAAA": "CAAAAExTS1O5AQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKCWxheW91dChvZmZzZXQ9MTYpIGZsb2F0M3gzIHVtYXRyaXhfUzE7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGZsb2F0MiBsb2NhbENvb3JkOwpsYXlvdXQobG9jYXRpb24gPSAwKSBvdXQgZmxvYXQyIHZUcmFuc2Zvcm1lZENvb3Jkc18yX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gUHJpbWl0aXZlIFByb2Nlc3NvciBRdWFkUGVyRWRnZUFBR2VvbWV0cnlQcm9jZXNzb3IKCXNrX1Bvc2l0aW9uID0gcG9zaXRpb24ueHkwMTsKCXsKCQl2VHJhbnNmb3JtZWRDb29yZHNfMl9TMCA9IGZsb2F0M3gyKHVtYXRyaXhfUzEpICogbG9jYWxDb29yZC54eTE7Cgl9Cn0KAAAAAAAAAAIDAABsYXlvdXQoYmluZGluZz0wKSB1bmlmb3JtIHNhbXBsZXIyRCB1VGV4dHVyZVNhbXBsZXJfMF9TMTsKbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKCWxheW91dChvZmZzZXQ9MTYpIGZsb2F0M3gzIHVtYXRyaXhfUzE7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgdlRyYW5zZm9ybWVkQ29vcmRzXzJfUzA7CmxheW91dChsb2NhdGlvbiA9IDAsIGluZGV4ID0gMCkgb3V0IGhhbGY0IHNrX0ZyYWdDb2xvcjsKaGFsZjQgVGV4dHVyZUVmZmVjdF9TMV9jMChoYWxmNCBfaW5wdXQpIAp7CglyZXR1cm4gc2FtcGxlKHVUZXh0dXJlU2FtcGxlcl8wX1MxLCB2VHJhbnNmb3JtZWRDb29yZHNfMl9TMCk7Cn0KaGFsZjQgTWF0cml4RWZmZWN0X1MxKGhhbGY0IF9pbnB1dCkgCnsKCXJldHVybiBUZXh0dXJlRWZmZWN0X1MxX2MwKF9pbnB1dCk7Cn0Kdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzAgPSBoYWxmNCgxKTsKCWNvbnN0IGhhbGY0IG91dHB1dENvdmVyYWdlX1MwID0gaGFsZjQoMSk7CgloYWxmNCBvdXRwdXRfUzE7CglvdXRwdXRfUzEgPSBNYXRyaXhFZmZlY3RfUzEob3V0cHV0Q29sb3JfUzApOwoJewoJCS8vIFhmZXIgUHJvY2Vzc29yOiBQb3J0ZXIgRHVmZgoJCXNrX0ZyYWdDb2xvciA9IG91dHB1dF9TMSAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPAAAAAEAAAAAAAAAAgAAAB0AAAAAAAAAAQAAAB0AAAAIAAAAAQAAABAAAAAAAAAAUAAAAAAAAAABAAAAAAAAAA==", "HQQAAAAAAAGAAAAAAIOP57ZDF37P7777777QCAAAAAAAAAAASABQAAAAEAAAAAAAEEBQAACGAAAAAAIAAAAAAAAAAAAAAAAAEAAAAAGDAEAAEAAAAAAA": "CAAAAExTS1NMAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDApIGZsYXQgb3V0IGhhbGY0IHZjb2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFByaW1pdGl2ZSBQcm9jZXNzb3IgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgl2Y29sb3JfUzAgPSBjb2xvcjsKCXNrX1Bvc2l0aW9uID0gcG9zaXRpb24ueHkwMTsKfQoAAAAAswEAAGxheW91dCAoYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMCwgaW5kZXggPSAwKSBvdXQgaGFsZjQgc2tfRnJhZ0NvbG9yOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmNvbG9yX1MwOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABUAAAAAQAAAAAAAAACAAAAHQAAAAAAAAABAAAACQAAAAgAAAABAAAADAAAAAAAAABGAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAAAAAAA", "HQJAAAAAAAGAAAAAAIOP577774BRZ7X7777QCAAAAABAAAAAABBAMAEQAMAAAABAAAAAAABBAMAAARQAAAAACAAAAD6QAAAAAEAAAAAQAAAABQYBAABAAAAAAA": "CAAAAExTS1NdAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gZmxvYXQyIGxvY2FsQ29vcmQ7CmxheW91dChsb2NhdGlvbiA9IDApIG91dCBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmxvY2FsQ29vcmRfUzAgPSBsb2NhbENvb3JkOwoJc2tfUG9zaXRpb24gPSBwb3NpdGlvbi54eTAxOwp9CgAAAAAAAABTAgAAbGF5b3V0KGJpbmRpbmc9MCkgdW5pZm9ybSBzYW1wbGVyMkQgdVRleHR1cmVTYW1wbGVyXzBfUzA7CmxheW91dCAoYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CmxheW91dChsb2NhdGlvbiA9IDAsIGluZGV4ID0gMCkgb3V0IGhhbGY0IHNrX0ZyYWdDb2xvcjsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzAgPSBoYWxmNCgxKTsKCWZsb2F0MiB0ZXhDb29yZDsKCXRleENvb3JkID0gdmxvY2FsQ29vcmRfUzA7CglvdXRwdXRDb2xvcl9TMCA9ICgoc2FtcGxlKHVUZXh0dXJlU2FtcGxlcl8wX1MwLCB0ZXhDb29yZCkgKiBoYWxmNCgxKSkpOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABUAAAAAQAAAAAAAAACAAAAHQAAAAAAAAABAAAAHQAAAAgAAAABAAAAEAAAAAAAAABGAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAD9AAAA", "HQQAAAAAAAGAAAAAAIOP57ZDF37P7777777QCAAAAAAAAAAACIBUFTBZQMKAAAAAAMAAAAAAAAAABZAAAAAAACAAAAAEBSAAIYAAAAABAAAAAAAAAAAAAAAAAAAAAAAAYMAQAAQAAAAAA": "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", "HRJAAAAAAAMAAAAAAQ4PZ72HLQCDR7H7777QGAAAAACAAAAAACCAYABAA4AAAACAAAAAAACCAYAAAUAAAAAACAAAAD6QAAAAAEAAAAAQAAAABQYBAABAAAAAAA": "CAAAAExTS1PFAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDIpIGluIGZsb2F0MiBsb2NhbENvb3JkOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IG91dCBoYWxmNCB2Y29sb3JfUzA7CmxheW91dChsb2NhdGlvbiA9IDEpIG91dCBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmNvbG9yX1MwID0gY29sb3I7Cgl2bG9jYWxDb29yZF9TMCA9IGxvY2FsQ29vcmQ7Cglza19Qb3NpdGlvbiA9IHBvc2l0aW9uLnh5MDE7Cn0KAAAAAAAAAJkCAABsYXlvdXQoYmluZGluZz0wKSB1bmlmb3JtIHNhbXBsZXIyRCB1VGV4dHVyZVNhbXBsZXJfMF9TMDsKbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGZsYXQgaW4gaGFsZjQgdmNvbG9yX1MwOwpsYXlvdXQobG9jYXRpb24gPSAxKSBpbiBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CmxheW91dChsb2NhdGlvbiA9IDAsIGluZGV4ID0gMCkgb3V0IGhhbGY0IHNrX0ZyYWdDb2xvcjsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzA7CglvdXRwdXRDb2xvcl9TMCA9IHZjb2xvcl9TMDsKCWZsb2F0MiB0ZXhDb29yZDsKCXRleENvb3JkID0gdmxvY2FsQ29vcmRfUzA7CglvdXRwdXRDb2xvcl9TMCA9ICgoc2FtcGxlKHVUZXh0dXJlU2FtcGxlcl8wX1MwLCB0ZXhDb29yZCkgKiBvdXRwdXRDb2xvcl9TMCkpOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAAABAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGAAAAABAAAAAAAAAAMAAAAdAAAAAAAAAAEAAAAJAAAACAAAAAEAAAAdAAAADAAAAAEAAAAUAAAAAAAAAFAAAAABAAAAAQAAAAUAAAAAAAAAAQAAAAUAAAAAAAAAAQAAAP0AAAA=", "C4QAAAAAMAAAAABAYAQ6FASCAEAAAABAAAAAAAAAAAAAAOIAAAAAAAQAAAABAMQAKAAAAAABAAAAAAAAAAAAAAAAAAQAAAAAYMAQAAAAAAAAA": "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", "AWQAGAAAQAAIPCELAGEP777777777737AAAAAAAAAAAABZAAAAAAACAAAAAEBSAAIYAAAAABAAAAAAAAAAAAAAAAAAAAAAAAYMAQAAAAAAAAA": "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", "I4AAAAAAAEAAAAABBYBQAAAAAMIAGEABBYAAAAAAQAAAAAAAQCIACAAAAAFAAAAAAQAAAAH5AAAAAAIAAAAAAAAAAABAAAAAAAAAAAA": "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", "HQQACAAAAAGAAAAAAIOP57ZDF37P7777777QCAAAAAAAAAAASABQAAAAEAAAAAAAEEBQAACGAAAAAAIAAAAAAAAAAAAAAAAAAAAAAAGDAEAAAAAAAAAA": "CAAAAExTS1NHAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDApIG91dCBoYWxmNCB2Y29sb3JfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmNvbG9yX1MwID0gY29sb3I7Cglza19Qb3NpdGlvbiA9IHBvc2l0aW9uLnh5MDE7Cn0KAAAAAACuAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMCwgaW5kZXggPSAwKSBvdXQgaGFsZjQgc2tfRnJhZ0NvbG9yOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmNvbG9yX1MwOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAVAAAAAEAAAAAAAAAAgAAAB0AAAAAAAAAAQAAAAkAAAAIAAAAAQAAAAwAAAAAAAAARgAAAAEAAAABAAAABQAAAAAAAAABAAAABQAAAAAAAAABAAAAAAAAAA==", "CEAAAAAADAAAAAAYQAMLQGEABAAAAAAAEIBAASAM4APBNJCRAAAAADAAAAAAAAAAACIAGAAAAAQAAAAAAAQQGAAAABDAAAAAAEAAAAAAAAAAAAAAAAAAAAAAADBQCAACAAAAAAA": "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", "HQJAAAAAAAGAAAAAAIOP577774BRZ7X7777QCAAAAABAAAAAABBAMADCAB4QAAAAAEAAAAAAHEAAAAAAAIAAAAAQGIAAAAAAIYAAAAABAAAAAAAAAAAAAAAAAAAAAAAAYMAQAAQAAAAAA": "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", "HRIAAAAAAAMAAAAAAQ4PZ72HLQCDR7H7777QGAAAAAAAAAAAGQDCIUFDGAQAAAAAAIAAB7QAAAAP4AAAAD7AAAAA72MJ54XDUIAAAAAQAAAAAYAAAAAIBYTZF4EBCAAAABQBD6P3AEAAAAACAAAAAYWE6SKB6FYFAAAIAAAAAAAAGAAAAAKM662BRAAAAAAARPEN6DYAAAACAAAAAAIIHCXHXUQEIAAAAAAAAAAAKAIVQRH6PYAAAAEAAAAABAAYQJC6J3YHAAAAAAAAAAAMQAIAAAABAAAAACAJAAKGAAAAAAIAAAAAAAAAAAAAAAAAEAAAAAEBAAAAEAAAAAAA": "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", "C4QAAAAAMAAAAABAYAQ6FASCAEAAAABAAAAAAAAAAAAAAOIAAAAAAAQAAAABAMQAIYAAAAABAAAAAAAAAAAAAAAAAAQAAAAAYMAQAAAAAAAAA": "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", "HQJAAAAAAAGAAAAAAIOP577774BRZ7X7777QCAAAAABAAAAAABBAMAEQAMAAAABAAAAAAABBAMAAAUAAAAAACAAAAD6QAAAAAAAAAAAAAAAABQYBAABAAAAAAA": "CAAAAExTS1NdAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gZmxvYXQyIGxvY2FsQ29vcmQ7CmxheW91dChsb2NhdGlvbiA9IDApIG91dCBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmxvY2FsQ29vcmRfUzAgPSBsb2NhbENvb3JkOwoJc2tfUG9zaXRpb24gPSBwb3NpdGlvbi54eTAxOwp9CgAAAAAAAABTAgAAbGF5b3V0KGJpbmRpbmc9MCkgdW5pZm9ybSBzYW1wbGVyMkQgdVRleHR1cmVTYW1wbGVyXzBfUzA7CmxheW91dCAoYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CmxheW91dChsb2NhdGlvbiA9IDAsIGluZGV4ID0gMCkgb3V0IGhhbGY0IHNrX0ZyYWdDb2xvcjsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzAgPSBoYWxmNCgxKTsKCWZsb2F0MiB0ZXhDb29yZDsKCXRleENvb3JkID0gdmxvY2FsQ29vcmRfUzA7CglvdXRwdXRDb2xvcl9TMCA9ICgoc2FtcGxlKHVUZXh0dXJlU2FtcGxlcl8wX1MwLCB0ZXhDb29yZCkgKiBoYWxmNCgxKSkpOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABUAAAAAQAAAAAAAAACAAAAHQAAAAAAAAABAAAAHQAAAAgAAAABAAAAEAAAAAAAAABQAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAD9AAAA", "HRJAAAAAAAMAAAAAAQ4PZ72HLQCDR7H7777QGAAAAACAAAAAACCAYABAA4AAAACAAAAAAACCAYAAARQAAAAACAAAAAAAAAAAAAAAAAAAAAAABQYBAABAAAAAAA": "CAAAAExTS1PFAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDIpIGluIGZsb2F0MiBsb2NhbENvb3JkOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IG91dCBoYWxmNCB2Y29sb3JfUzA7CmxheW91dChsb2NhdGlvbiA9IDEpIG91dCBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmNvbG9yX1MwID0gY29sb3I7Cgl2bG9jYWxDb29yZF9TMCA9IGxvY2FsQ29vcmQ7Cglza19Qb3NpdGlvbiA9IHBvc2l0aW9uLnh5MDE7Cn0KAAAAAAAAAJkCAABsYXlvdXQoYmluZGluZz0wKSB1bmlmb3JtIHNhbXBsZXIyRCB1VGV4dHVyZVNhbXBsZXJfMF9TMDsKbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGZsYXQgaW4gaGFsZjQgdmNvbG9yX1MwOwpsYXlvdXQobG9jYXRpb24gPSAxKSBpbiBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CmxheW91dChsb2NhdGlvbiA9IDAsIGluZGV4ID0gMCkgb3V0IGhhbGY0IHNrX0ZyYWdDb2xvcjsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzA7CglvdXRwdXRDb2xvcl9TMCA9IHZjb2xvcl9TMDsKCWZsb2F0MiB0ZXhDb29yZDsKCXRleENvb3JkID0gdmxvY2FsQ29vcmRfUzA7CglvdXRwdXRDb2xvcl9TMCA9ICgoc2FtcGxlKHVUZXh0dXJlU2FtcGxlcl8wX1MwLCB0ZXhDb29yZCkgKiBvdXRwdXRDb2xvcl9TMCkpOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAAABAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGAAAAABAAAAAAAAAAMAAAAdAAAAAAAAAAEAAAAJAAAACAAAAAEAAAAdAAAADAAAAAEAAAAUAAAAAAAAAEYAAAABAAAAAQAAAAUAAAAAAAAAAQAAAAUAAAAAAAAAAQAAAAAAAAA=", "CEAAAAAADAAAAAAYQAMLQGEABAAAAAAAEIBAAQAOAAAABAAAAAAABBAMABIAAAAAAEAAAAH5AAAAAAAAAAAAAAAAADBQCAACAAAAAAA": "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", "HQQAAAAAAAGAAAAAAIOP57ZDF37P7777777QCAAAAAAAAAAAAAAQAAAAAAQQGAAAIYAAAAABAAAAB7IAAAAACAAAAAAAAAAAAIAAAAQAAAAAA": "CAAAAExTS1NMAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDApIGZsYXQgb3V0IGhhbGY0IHZjb2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFByaW1pdGl2ZSBQcm9jZXNzb3IgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgl2Y29sb3JfUzAgPSBjb2xvcjsKCXNrX1Bvc2l0aW9uID0gcG9zaXRpb24ueHkwMTsKfQoAAAAAgAEAAGxheW91dCAoYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMCwgaW5kZXggPSAwKSBvdXQgaGFsZjQgc2tfRnJhZ0NvbG9yOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmNvbG9yX1MwOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogRGlzYWJsZSBDb2xvcgoJfQp9CgAAAAABAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwAAAABAAAAAAAAAAIAAAAdAAAAAAAAAAEAAAAJAAAACAAAAAEAAAAMAAAAAAAAAEYAAAAAAAAAAAAAAP0AAAA=", "HQJAAAAAAAGAAAAAAIOP577774BRZ7X7777QCAAAAABAAAAAABBAMAASANBMYOMDCQAAAAABAAAAACBR44GFEAAAAAGAAAAACAAAAAAQENBMYOMDCQAAAAADAAAAAAAAAAAOIAAAAAAAQAAAABAMQAAAABIAAAAAAEAAAAH5AAAAAAAAAAAAAAAAADBQCAACAAAAAAA": "CAAAAExTS1OsAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKCWxheW91dChvZmZzZXQ9MTYpIGZsb2F0NCB1Y2lyY2xlX1MxX2MwOwoJbGF5b3V0KG9mZnNldD0zMikgZmxvYXQ0IHVjaXJjbGVfUzE7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGZsb2F0MiBsb2NhbENvb3JkOwpsYXlvdXQobG9jYXRpb24gPSAwKSBvdXQgZmxvYXQyIHZsb2NhbENvb3JkX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gUHJpbWl0aXZlIFByb2Nlc3NvciBRdWFkUGVyRWRnZUFBR2VvbWV0cnlQcm9jZXNzb3IKCXZsb2NhbENvb3JkX1MwID0gbG9jYWxDb29yZDsKCXNrX1Bvc2l0aW9uID0gcG9zaXRpb24ueHkwMTsKfQoAAAAA1QcAAGNvbnN0IGludCBrRmlsbEFBX1MxX2MwID0gMTsKY29uc3QgaW50IGtJbnZlcnNlRmlsbEJXX1MxX2MwID0gMjsKY29uc3QgaW50IGtJbnZlcnNlRmlsbEFBX1MxX2MwID0gMzsKY29uc3QgaW50IGtGaWxsQUFfUzEgPSAxOwpjb25zdCBpbnQga0ludmVyc2VGaWxsQldfUzEgPSAyOwpjb25zdCBpbnQga0ludmVyc2VGaWxsQUFfUzEgPSAzOwpsYXlvdXQoYmluZGluZz0wKSB1bmlmb3JtIHNhbXBsZXIyRCB1VGV4dHVyZVNhbXBsZXJfMF9TMDsKbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKCWxheW91dChvZmZzZXQ9MTYpIGZsb2F0NCB1Y2lyY2xlX1MxX2MwOwoJbGF5b3V0KG9mZnNldD0zMikgZmxvYXQ0IHVjaXJjbGVfUzE7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CmxheW91dChsb2NhdGlvbiA9IDAsIGluZGV4ID0gMCkgb3V0IGhhbGY0IHNrX0ZyYWdDb2xvcjsKaGFsZjQgQ2lyY2xlX1MxX2MwKGhhbGY0IF9pbnB1dCkgCnsKCWhhbGY0IF90bXBfMF9pbkNvbG9yID0gX2lucHV0OwoJaGFsZiBkOwoJaWYgKGludCgxKSA9PSBrSW52ZXJzZUZpbGxCV19TMV9jMCB8fCBpbnQoMSkgPT0ga0ludmVyc2VGaWxsQUFfUzFfYzApIAoJewoJCWQgPSBoYWxmKChsZW5ndGgoKHVjaXJjbGVfUzFfYzAueHkgLSBza19GcmFnQ29vcmQueHkpICogdWNpcmNsZV9TMV9jMC53KSAtIDEuMCkgKiB1Y2lyY2xlX1MxX2MwLnopOwoJfQoJZWxzZSAKCXsKCQlkID0gaGFsZigoMS4wIC0gbGVuZ3RoKCh1Y2lyY2xlX1MxX2MwLnh5IC0gc2tfRnJhZ0Nvb3JkLnh5KSAqIHVjaXJjbGVfUzFfYzAudykpICogdWNpcmNsZV9TMV9jMC56KTsKCX0KCWlmIChpbnQoMSkgPT0ga0ZpbGxBQV9TMV9jMCB8fCBpbnQoMSkgPT0ga0ludmVyc2VGaWxsQUFfUzFfYzApIAoJewoJCXJldHVybiBoYWxmNChfaW5wdXQgKiBzYXR1cmF0ZShkKSk7Cgl9CgllbHNlIAoJewoJCXJldHVybiBoYWxmNChkID4gMC41ID8gX2lucHV0IDogaGFsZjQoMC4wKSk7Cgl9Cn0KaGFsZjQgQ2lyY2xlX1MxKGhhbGY0IF9pbnB1dCkgCnsKCV9pbnB1dCA9IENpcmNsZV9TMV9jMChfaW5wdXQpOwoJaGFsZjQgX3RtcF8xX2luQ29sb3IgPSBfaW5wdXQ7CgloYWxmIGQ7CglpZiAoaW50KDApID09IGtJbnZlcnNlRmlsbEJXX1MxIHx8IGludCgwKSA9PSBrSW52ZXJzZUZpbGxBQV9TMSkgCgl7CgkJZCA9IGhhbGYoKGxlbmd0aCgodWNpcmNsZV9TMS54eSAtIHNrX0ZyYWdDb29yZC54eSkgKiB1Y2lyY2xlX1MxLncpIC0gMS4wKSAqIHVjaXJjbGVfUzEueik7Cgl9CgllbHNlIAoJewoJCWQgPSBoYWxmKCgxLjAgLSBsZW5ndGgoKHVjaXJjbGVfUzEueHkgLSBza19GcmFnQ29vcmQueHkpICogdWNpcmNsZV9TMS53KSkgKiB1Y2lyY2xlX1MxLnopOwoJfQoJaWYgKGludCgwKSA9PSBrRmlsbEFBX1MxIHx8IGludCgwKSA9PSBrSW52ZXJzZUZpbGxBQV9TMSkgCgl7CgkJcmV0dXJuIGhhbGY0KF9pbnB1dCAqIHNhdHVyYXRlKGQpKTsKCX0KCWVsc2UgCgl7CgkJcmV0dXJuIGhhbGY0KGQgPiAwLjUgPyBfaW5wdXQgOiBoYWxmNCgwLjApKTsKCX0KfQp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMCA9IGhhbGY0KDEpOwoJZmxvYXQyIHRleENvb3JkOwoJdGV4Q29vcmQgPSB2bG9jYWxDb29yZF9TMDsKCW91dHB1dENvbG9yX1MwID0gKChzYW1wbGUodVRleHR1cmVTYW1wbGVyXzBfUzAsIHRleENvb3JkKSAqIGhhbGY0KDEpKSk7Cgljb25zdCBoYWxmNCBvdXRwdXRDb3ZlcmFnZV9TMCA9IGhhbGY0KDEpOwoJaGFsZjQgb3V0cHV0X1MxOwoJb3V0cHV0X1MxID0gQ2lyY2xlX1MxKG91dHB1dENvdmVyYWdlX1MwKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dF9TMTsKCX0KfQoAAAABAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABUAAAAAQAAAAAAAAACAAAAHQAAAAAAAAABAAAAHQAAAAgAAAABAAAAEAAAAAAAAABQAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAD9AAAA", "AWQAGAAAQAAIPCELAGEP777777777737AAAAAAAAAAAABZAAAAAAACAAAAAEBSAAKAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAYMAQAAAAAAAAA": "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", "HQJAAAAAAAGAAAAAAIOP577774BRZ7X7777QCAAAAABAAAAAABBAMAEQAMAAAABAAAAAAABBAMAAARQAAAAACAAAAAAAAAAAAAAAAAAAAAAABQYBAABAAAAAAA": "CAAAAExTS1NdAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gZmxvYXQyIGxvY2FsQ29vcmQ7CmxheW91dChsb2NhdGlvbiA9IDApIG91dCBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmxvY2FsQ29vcmRfUzAgPSBsb2NhbENvb3JkOwoJc2tfUG9zaXRpb24gPSBwb3NpdGlvbi54eTAxOwp9CgAAAAAAAABTAgAAbGF5b3V0KGJpbmRpbmc9MCkgdW5pZm9ybSBzYW1wbGVyMkQgdVRleHR1cmVTYW1wbGVyXzBfUzA7CmxheW91dCAoYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CmxheW91dChsb2NhdGlvbiA9IDAsIGluZGV4ID0gMCkgb3V0IGhhbGY0IHNrX0ZyYWdDb2xvcjsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzAgPSBoYWxmNCgxKTsKCWZsb2F0MiB0ZXhDb29yZDsKCXRleENvb3JkID0gdmxvY2FsQ29vcmRfUzA7CglvdXRwdXRDb2xvcl9TMCA9ICgoc2FtcGxlKHVUZXh0dXJlU2FtcGxlcl8wX1MwLCB0ZXhDb29yZCkgKiBoYWxmNCgxKSkpOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABUAAAAAQAAAAAAAAACAAAAHQAAAAAAAAABAAAAHQAAAAgAAAABAAAAEAAAAAAAAABGAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAAAAAAA", "E5QQAAAAMAAGEADCACRAAAAAMAACFQDBABRAAIXCAIAAAAAAHEAAAAAAAIAAAAAQGIAAAACQAAAAAAIAAAAAAAAAAAAAAAAAEAAAAAGDAEAAAAAAAAAA": "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", "HRIAAAAAAAMAAAAAAQ4PZ72HLQCDR7H7777QGAAAAAAAAAAAGQDCIUFDGAQAAAAAAIAAB7QAAAAP4AAAAD7AAAAA72MJ54XDUIAAAABQAAAAAYAAAAAEATA7TQ5QQAAAABHEWBVCAAAAAAABAAAAAMLCPLFI7CYCAAAMAAAAACAACAAAAAYX24HOEAAAAABYFUMYQAQAAAAAQAAAADCGATA7TQ5QQAAAAAAAAAAAFLRLIZBABIAAAAAQAAAAAECDJZFQNIQAAAAAAAAAAAADSAAAAAAAEAAAAAIDEACGAAAAAAIAAAAAAAAAAAAAAAAAAAAAAAGDAEAAEAAAAAAA": "CAAAAExTS1PiAgAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKCWxheW91dChvZmZzZXQ9MTYpIGhhbGY0IHVzdGFydF9TMV9jMF9jMDsKCWxheW91dChvZmZzZXQ9MjQpIGhhbGY0IHVlbmRfUzFfYzBfYzA7CglsYXlvdXQob2Zmc2V0PTMyKSBmbG9hdDN4MyB1bWF0cml4X1MxX2MwX2MxOwoJbGF5b3V0KG9mZnNldD04MCkgaGFsZjQgdWxlZnRCb3JkZXJDb2xvcl9TMV9jMDsKCWxheW91dChvZmZzZXQ9ODgpIGhhbGY0IHVyaWdodEJvcmRlckNvbG9yX1MxX2MwOwp9CjsKbGF5b3V0KGxvY2F0aW9uID0gMCkgaW4gZmxvYXQyIHBvc2l0aW9uOwpsYXlvdXQobG9jYXRpb24gPSAxKSBpbiBoYWxmNCBjb2xvcjsKbGF5b3V0KGxvY2F0aW9uID0gMikgaW4gZmxvYXQyIGxvY2FsQ29vcmQ7CmxheW91dChsb2NhdGlvbiA9IDApIGZsYXQgb3V0IGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMSkgb3V0IGZsb2F0MiB2VHJhbnNmb3JtZWRDb29yZHNfNV9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFByaW1pdGl2ZSBQcm9jZXNzb3IgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgl2Y29sb3JfUzAgPSBjb2xvcjsKCXNrX1Bvc2l0aW9uID0gcG9zaXRpb24ueHkwMTsKCXsKCQl2VHJhbnNmb3JtZWRDb29yZHNfNV9TMCA9IGZsb2F0M3gyKHVtYXRyaXhfUzFfYzBfYzEpICogbG9jYWxDb29yZC54eTE7Cgl9Cn0KAAAAAAAAkgcAAGxheW91dCAoYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7CglsYXlvdXQob2Zmc2V0PTE2KSBoYWxmNCB1c3RhcnRfUzFfYzBfYzA7CglsYXlvdXQob2Zmc2V0PTI0KSBoYWxmNCB1ZW5kX1MxX2MwX2MwOwoJbGF5b3V0KG9mZnNldD0zMikgZmxvYXQzeDMgdW1hdHJpeF9TMV9jMF9jMTsKCWxheW91dChvZmZzZXQ9ODApIGhhbGY0IHVsZWZ0Qm9yZGVyQ29sb3JfUzFfYzA7CglsYXlvdXQob2Zmc2V0PTg4KSBoYWxmNCB1cmlnaHRCb3JkZXJDb2xvcl9TMV9jMDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGZsYXQgaW4gaGFsZjQgdmNvbG9yX1MwOwpsYXlvdXQobG9jYXRpb24gPSAxKSBpbiBmbG9hdDIgdlRyYW5zZm9ybWVkQ29vcmRzXzVfUzA7CmxheW91dChsb2NhdGlvbiA9IDAsIGluZGV4ID0gMCkgb3V0IGhhbGY0IHNrX0ZyYWdDb2xvcjsKaGFsZjQgU2luZ2xlSW50ZXJ2YWxDb2xvcml6ZXJfUzFfYzBfYzAoaGFsZjQgX2lucHV0LCBmbG9hdDIgX2Nvb3JkcykgCnsKCWhhbGY0IF90bXBfMF9pbkNvbG9yID0gX2lucHV0OwoJZmxvYXQyIF90bXBfMV9jb29yZHMgPSBfY29vcmRzOwoJcmV0dXJuIGhhbGY0KG1peCh1c3RhcnRfUzFfYzBfYzAsIHVlbmRfUzFfYzBfYzAsIGhhbGYoX3RtcF8xX2Nvb3Jkcy54KSkpOwp9CmhhbGY0IFJhZGlhbExheW91dF9TMV9jMF9jMV9jMChoYWxmNCBfaW5wdXQpIAp7CgloYWxmNCBfdG1wXzJfaW5Db2xvciA9IF9pbnB1dDsKCWZsb2F0MiBfdG1wXzNfY29vcmRzID0gdlRyYW5zZm9ybWVkQ29vcmRzXzVfUzA7CglyZXR1cm4gaGFsZjQoaGFsZjQoaGFsZihsZW5ndGgoX3RtcF8zX2Nvb3JkcykpLCAxLjAsIDAuMCwgMC4wKSk7Cn0KaGFsZjQgTWF0cml4RWZmZWN0X1MxX2MwX2MxKGhhbGY0IF9pbnB1dCkgCnsKCXJldHVybiBSYWRpYWxMYXlvdXRfUzFfYzBfYzFfYzAoX2lucHV0KTsKfQpoYWxmNCBDbGFtcGVkR3JhZGllbnRfUzFfYzAoaGFsZjQgX2lucHV0KSAKewoJaGFsZjQgX3RtcF80X2luQ29sb3IgPSBfaW5wdXQ7CgloYWxmNCB0ID0gTWF0cml4RWZmZWN0X1MxX2MwX2MxKF90bXBfNF9pbkNvbG9yKTsKCWhhbGY0IG91dENvbG9yOwoJaWYgKCFib29sKGludCgxKSkgJiYgdC55IDwgMC4wKSAKCXsKCQlvdXRDb2xvciA9IGhhbGY0KDAuMCk7Cgl9CgllbHNlIGlmICh0LnggPCAwLjApIAoJewoJCW91dENvbG9yID0gdWxlZnRCb3JkZXJDb2xvcl9TMV9jMDsKCX0KCWVsc2UgaWYgKHQueCA+IDEuMCkgCgl7CgkJb3V0Q29sb3IgPSB1cmlnaHRCb3JkZXJDb2xvcl9TMV9jMDsKCX0KCWVsc2UgCgl7CgkJb3V0Q29sb3IgPSBTaW5nbGVJbnRlcnZhbENvbG9yaXplcl9TMV9jMF9jMChfdG1wXzRfaW5Db2xvciwgZmxvYXQyKGhhbGYyKHQueCwgMC4wKSkpOwoJfQoJaWYgKGJvb2woaW50KDEpKSkgCgl7CgkJb3V0Q29sb3IueHl6ICo9IG91dENvbG9yLnc7Cgl9CglyZXR1cm4gaGFsZjQob3V0Q29sb3IpOwp9CmhhbGY0IE92ZXJyaWRlSW5wdXRfUzEoaGFsZjQgX2lucHV0KSAKewoJaGFsZjQgX3RtcF81X2luQ29sb3IgPSBfaW5wdXQ7CglyZXR1cm4gaGFsZjQoQ2xhbXBlZEdyYWRpZW50X1MxX2MwKGhhbGY0KDEuMCwxLjAsMS4wLDEuMCkpKTsKfQp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmNvbG9yX1MwOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCWhhbGY0IG91dHB1dF9TMTsKCW91dHB1dF9TMSA9IE92ZXJyaWRlSW5wdXRfUzEob3V0cHV0Q29sb3JfUzApOwoJewoJCS8vIFhmZXIgUHJvY2Vzc29yOiBQb3J0ZXIgRHVmZgoJCXNrX0ZyYWdDb2xvciA9IG91dHB1dF9TMSAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYAAAAAEAAAAAAAAAAwAAAB0AAAAAAAAAAQAAAAkAAAAIAAAAAQAAAB0AAAAMAAAAAQAAABQAAAAAAAAARgAAAAEAAAABAAAABQAAAAAAAAABAAAABQAAAAAAAAABAAAAAAAAAA==", "HQJAAAAAAAGAAAAAAIOP577774BRZ7X7777QCAAAAABAAAAAABBAMAASANBMYOMDCQAAAAADAAAAAAAAAAAOIAAAAAAAQAAAABAMQACGAAAAAAIAAAAP2AAAAAAAAAAAAAAAAAGDAEAAEAAAAAAA": "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", "C4QAAAAAMAAAAABAYAQ6FASCAEAAAABAAAAAAAAAAAAAAOIAAAAAAAQAAAABAMQAKAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAYMAQAAAAAAAAA": "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", "HRIACAAAAAMAAAAAAQ4AANCELQCDR7H7777QGAAAAAAAAAAAGQDCIUFDGAQAAAAAAIAAB7QAAAAP4AAAAD7AAAAA72MJ54XDUIAAAAAQAAAAAYAAAAAIBYTZF4EBCAAAABQBD6P3AEAAAAACAAAAAYWE6SKB6FYFAAAIAAAAAAAAGAAAAAKM662BRAAAAAAARPEN6DYAAAACAAAAAAIIHCXHXUQEIAAAAAAAAAAAKAIVQRH6PYAAAAEAAAAABAAYQJC6J3YHAAAAAAAAAAAMQAIAAAABAAAAACAJAAKQAAAAAAIAAAAAAAAAAAAAAAAAEAAAAAGDAEAAAAAAAAAA": "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", "HQIAAAAAAAGAAAAAAIOP577774BRZ7X7777QCAAAAAAAAAAAVIBAYAAAAAAQAAIAAAACRRAAAAABAAAQAAAABIECAEAACAAAAAZQIEBSAAIAAAAAAAAJAAYAAAACAAAAAAACCA2GAAAAAAIAAAAAAAAAAAAAAAAAAAAAAAADAAAAEAAAAAAA": "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", "AXAQGAAAQAAIPCELAGEP777777777737AAAAAAAAAAAIBRUAARVBIBQEAAAEAAAAYAPQAAGAD4AABQA7AAAMAH6TKN7FYFAAAAAAEAAAAAGAAAAARDUYG4YHAEAAAAAWSG7R6AAAAAQAAAAAEBDEYT7ZOFIQAAAABAAAAABQAAAAAIFGB7HB2BAAAAAFQRH6PYAAAAAAAEAABAAYRTUYG4YHAEAAAAAAAAAEARLACH47WAIAAAAAEAAAABRAQFURX4PQAAAAAAAAAABAA4AAAACAAAAAAACCAYAAAACGAAAAAAIAAAAAAAAAAAAAAAAAAAAAAAGDAEAAAAAAAAAA": "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", "HRIAAAAAAAMAAAAAAQ4PZ72HLQCDR7H7777QGAAAAAAAAAAAGQDCIUFDGAQAAAAAAIAAB7QAAAAP4AAAAD7AAAAA72MJ54XDUIAAAABQAAAAAYAAAAAEA3BFYANSIAAAABQBD6P3AEAAAAACAAAAAYWE6SKB6FYFAAAIAAIAAAAAGAAAABRCWAO6EAAQAAAARPEN6DYAAAACAAAAAAIIHMMVABXZAAAAAAAAAAAAKAIVQRH6PYAAAAEAAAAABAAYQJC6J3YHAAAAAAAAAAAMQAIAAAABAAAAACAJAAKQAAAAAAIAAAAAAAAAAAAAAAAAEAAAAAEBAEAAEAAAAAAA": "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", "HQIAAAAAAAGAAAAAAIOP577774BRZ7X7777QCAAAAAAAAAAAVIBAAAIBAAAAAMYECAZAAAAQAAAAAAEQAMAAAABAAAAAAABBAMAAAACGAAAAAAIAAAAAAAAAAAAAAAAAAAAAAAADAAAAEAAAAAAA": "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", "C4QAAAAAMAAAAABAYAQ6FASCAEAAAABAAAAAAAAAAAAAAOIAAAAAAAQAAAABAMQAIYAAAAABAAAAB7IAAAAAAAAAAAAAAAAAYMAQAAAAAAAAA": "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", "I4AAAAAAAEAAAAABBYBQAAAAAMIAGEABBYAAAAAAQAAAAAAAQCIACAAAABIAAAAAAEAAAAH5AAAAAAIAAAADAAAAAABAAAAAAAAAAAA": "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", "HQIAAAAAAAGAAAAAAIOP577774BRZ7X7777QCAAAAAAAAAAAFIAA2AAAAAAAAAQAAAACUAQACAAAAABQIMACCAYAAAAAAAAAAAADSAAAAAAAEAAAAAIDEAAAABDAAAAAAEAAAAAAAAAAAAAAAAACAAAAAABQAAAAAAAAAAA": "CAAAAExTS1O/AQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKCWxheW91dChvZmZzZXQ9MTYpIGZsb2F0M3gzIHVtYXRyaXhfUzFfYzA7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGZsb2F0MiBsb2NhbENvb3JkOwpsYXlvdXQobG9jYXRpb24gPSAwKSBvdXQgZmxvYXQyIHZUcmFuc2Zvcm1lZENvb3Jkc18zX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gUHJpbWl0aXZlIFByb2Nlc3NvciBRdWFkUGVyRWRnZUFBR2VvbWV0cnlQcm9jZXNzb3IKCXNrX1Bvc2l0aW9uID0gcG9zaXRpb24ueHkwMTsKCXsKCQl2VHJhbnNmb3JtZWRDb29yZHNfM19TMCA9IGZsb2F0M3gyKHVtYXRyaXhfUzFfYzApICogbG9jYWxDb29yZC54eTE7Cgl9Cn0KAAAAAACOAwAAbGF5b3V0KGJpbmRpbmc9MCkgdW5pZm9ybSBzYW1wbGVyMkQgdVRleHR1cmVTYW1wbGVyXzBfUzE7CmxheW91dCAoYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7CglsYXlvdXQob2Zmc2V0PTE2KSBmbG9hdDN4MyB1bWF0cml4X1MxX2MwOwp9CjsKbGF5b3V0KGxvY2F0aW9uID0gMCkgaW4gZmxvYXQyIHZUcmFuc2Zvcm1lZENvb3Jkc18zX1MwOwpsYXlvdXQobG9jYXRpb24gPSAwLCBpbmRleCA9IDApIG91dCBoYWxmNCBza19GcmFnQ29sb3I7CmhhbGY0IFRleHR1cmVFZmZlY3RfUzFfYzBfYzAoaGFsZjQgX2lucHV0KSAKewoJcmV0dXJuIHNhbXBsZSh1VGV4dHVyZVNhbXBsZXJfMF9TMSwgdlRyYW5zZm9ybWVkQ29vcmRzXzNfUzApOwp9CmhhbGY0IE1hdHJpeEVmZmVjdF9TMV9jMChoYWxmNCBfaW5wdXQpIAp7CglyZXR1cm4gVGV4dHVyZUVmZmVjdF9TMV9jMF9jMChfaW5wdXQpOwp9CmhhbGY0IEJsZW5kX1MxKGhhbGY0IF9zcmMsIGhhbGY0IF9kc3QpIAp7CgkvLyBCbGVuZCBtb2RlOiBNb2R1bGF0ZQoJcmV0dXJuIGJsZW5kX21vZHVsYXRlKE1hdHJpeEVmZmVjdF9TMV9jMChfc3JjKSwgX3NyYyk7Cn0Kdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzAgPSBoYWxmNCgxKTsKCWNvbnN0IGhhbGY0IG91dHB1dENvdmVyYWdlX1MwID0gaGFsZjQoMSk7CgloYWxmNCBvdXRwdXRfUzE7CglvdXRwdXRfUzEgPSBCbGVuZF9TMShvdXRwdXRDb2xvcl9TMCwgaGFsZjQoMSkpOwoJewoJCS8vIFhmZXIgUHJvY2Vzc29yOiBQb3J0ZXIgRHVmZgoJCXNrX0ZyYWdDb2xvciA9IG91dHB1dF9TMSAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPAAAAAEAAAAAAAAAAgAAAB0AAAAAAAAAAQAAAB0AAAAIAAAAAQAAABAAAAAAAAAARgAAAAAAAAABAAAAAAAAAA==", "HQJAAAAAAAGAAAAAAIOP577774BRZ7X7777QCAAAAABAAAAAABBAMAEQAMAAAABAAAAAAABBAMAAAUAAAAAACAAAAAAAAAAAAAAAAAAAAAAABQYBAABAAAAAAA": "CAAAAExTS1NdAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gZmxvYXQyIGxvY2FsQ29vcmQ7CmxheW91dChsb2NhdGlvbiA9IDApIG91dCBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmxvY2FsQ29vcmRfUzAgPSBsb2NhbENvb3JkOwoJc2tfUG9zaXRpb24gPSBwb3NpdGlvbi54eTAxOwp9CgAAAAAAAABTAgAAbGF5b3V0KGJpbmRpbmc9MCkgdW5pZm9ybSBzYW1wbGVyMkQgdVRleHR1cmVTYW1wbGVyXzBfUzA7CmxheW91dCAoYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CmxheW91dChsb2NhdGlvbiA9IDAsIGluZGV4ID0gMCkgb3V0IGhhbGY0IHNrX0ZyYWdDb2xvcjsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzAgPSBoYWxmNCgxKTsKCWZsb2F0MiB0ZXhDb29yZDsKCXRleENvb3JkID0gdmxvY2FsQ29vcmRfUzA7CglvdXRwdXRDb2xvcl9TMCA9ICgoc2FtcGxlKHVUZXh0dXJlU2FtcGxlcl8wX1MwLCB0ZXhDb29yZCkgKiBoYWxmNCgxKSkpOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABUAAAAAQAAAAAAAAACAAAAHQAAAAAAAAABAAAAHQAAAAgAAAABAAAAEAAAAAAAAABQAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAAAAAAA", "HQQAAAAAAAGAAAAAAIOP57ZDF37P7777777QCAAAAAAAAAAAAAAQAAAAAAQQGAAAKAAAAAABAAAAB7IAAAAACAAAAAAAAAAAAIAAAAAAAAAAA": "CAAAAExTS1NMAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDApIGZsYXQgb3V0IGhhbGY0IHZjb2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFByaW1pdGl2ZSBQcm9jZXNzb3IgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgl2Y29sb3JfUzAgPSBjb2xvcjsKCXNrX1Bvc2l0aW9uID0gcG9zaXRpb24ueHkwMTsKfQoAAAAAgAEAAGxheW91dCAoYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMCwgaW5kZXggPSAwKSBvdXQgaGFsZjQgc2tfRnJhZ0NvbG9yOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmNvbG9yX1MwOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogRGlzYWJsZSBDb2xvcgoJfQp9CgAAAAABAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwAAAABAAAAAAAAAAIAAAAdAAAAAAAAAAEAAAAJAAAACAAAAAEAAAAMAAAAAAAAAFAAAAAAAAAAAAAAAP0AAAA=", "HQIAAAAAAAGAAAAAAIOP577774BRZ7X7777QCAAAAAAAAAAAVIBACAIBAAAAAMYECAZAAEAQAAAAAAEQAMAAAABAAAAAAABBAMAAAACQAAAAAAIAAAAAAAAAAAAAAAAAEAAAAAADAAAAEAAAAAAA": "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", "HQQAAAAAAAGAAAAAAIOP57ZDF37P7777777QCAAAAAAAAAAASABQAAAAEAAAAABQIQCAAAAKAAAAABAAAAAP2AAAAAAQAAAAAAAAAAADAAAAEAAAAAAA": "CAAAAExTS1NMAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDApIGZsYXQgb3V0IGhhbGY0IHZjb2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFByaW1pdGl2ZSBQcm9jZXNzb3IgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgl2Y29sb3JfUzAgPSBjb2xvcjsKCXNrX1Bvc2l0aW9uID0gcG9zaXRpb24ueHkwMTsKfQoAAAAA1wEAAGxheW91dCAoYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMCwgaW5kZXggPSAwKSBvdXQgaGFsZjQgc2tfRnJhZ0NvbG9yOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmNvbG9yX1MwOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJCXNrX0ZyYWdDb2xvciA9IHNrX0ZyYWdDb2xvci5hMDAwOwoJfQp9CgAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8AAAAAQAAAAAAAAACAAAAHQAAAAAAAAABAAAACQAAAAgAAAABAAAADAAAAAAAAAAKAAAAAAAAAAEAAAD9AAAA", "HQIAAAAAAAGAAAAAAIOP577774BRZ7X7777QCAAAAAAAAAAAVIBACAIBAAAAAMYECAZAAEAQAAAAAAEQAMAAAABAAAAAAABBAMAAAACGAAAAAAIAAAAAAAAAAAAAAAAAAAAAAAADAAAAEAAAAAAA": "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", "C4QAAAAAMAAAAABAYAQ6FASCAEAAAABAAAAAAAAAAAAAAOIAAAAAAAQAAAABAMQAIYAAAAABAAAAAAAAAAAAAAAAAAAAAAAAYMAQAAAAAAAAA": "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", "HQQACAAAAAGAAAAAAIOP57ZDF37P7777777QCAAAAAAAAAAASABQAAAAEAAAAAAAEEBQAACQAAAAAAIAAAAP2AAAAAAQAAAACAAAAAGDAEAAAAAAAAAA": "CAAAAExTS1NHAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDApIG91dCBoYWxmNCB2Y29sb3JfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmNvbG9yX1MwID0gY29sb3I7Cglza19Qb3NpdGlvbiA9IHBvc2l0aW9uLnh5MDE7Cn0KAAAAAACuAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMCwgaW5kZXggPSAwKSBvdXQgaGFsZjQgc2tfRnJhZ0NvbG9yOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmNvbG9yX1MwOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAVAAAAAEAAAAAAAAAAgAAAB0AAAAAAAAAAQAAAAkAAAAIAAAAAQAAAAwAAAAAAAAAUAAAAAEAAAABAAAABQAAAAAAAAABAAAABQAAAAAAAAABAAAA/QAAAA==", "AWQAGAAAQAAIPCELAGEP777777777737AAAAAAAAAAAABZAAAAAAACAAAAAEBSAAKAAAAAABAAAAAAAAAAAAAAAAAAQAAAAAYMAQAAAAAAAAA": "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", "HRIAAAAAAAMAAAAAAQ4PZ72HLQCDR7H7777QGAAAAAAAAAAAGQDCIUFDGAQAAAAAAIAAB7QAAAAP4AAAAD7AAAAA72MJ54XDUIAAAABQAAAAAYAAAAAEA3BFYANSIAAAABQBD6P3AEAAAAACAAAAAYWE6SKB6FYFAAAIAAIAAAAAGAAAABRCWAO6EAAQAAAARPEN6DYAAAACAAAAAAIIHMMVABXZAAAAAAAAAAAAKAIVQRH6PYAAAAEAAAAABAAYQJC6J3YHAAAAAAAAAAAMQAIAAAABAAAAACAJAAKGAAAAAAIAAAAAAAAAAAAAAAAAEAAAAAARAAAAEAAAAAAA": "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", "I4AAAAAAAEAAAAABBYBQAAAAAMIAGEABBYAAAAAAQAAAAAAAQCIACAAAABIAAAAAAEAAAAH5AAAAAAIAAAABAAAAAABAAAAAAAAAAAA": "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", "I4AQAAAAAEAAAAABBYBAAAAAAMIAGEAAAAAABAAAAAAABAEQAEAAUAAAAACAAAAA7UAAAAABAAAAAAAAAAAAEAAAAAAAAAAA": "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", "HQQAAAAAAAGAAAAAAIOP57ZDF37P7777777QCAAAAAAAAAAASABQAAAAEAAAAAAAEEBQAACQAAAAAAIAAAAP2AAAAAAAAAAAAAAAAAGDAEAAEAAAAAAA": "CAAAAExTS1NMAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDApIGZsYXQgb3V0IGhhbGY0IHZjb2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFByaW1pdGl2ZSBQcm9jZXNzb3IgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgl2Y29sb3JfUzAgPSBjb2xvcjsKCXNrX1Bvc2l0aW9uID0gcG9zaXRpb24ueHkwMTsKfQoAAAAAswEAAGxheW91dCAoYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMCwgaW5kZXggPSAwKSBvdXQgaGFsZjQgc2tfRnJhZ0NvbG9yOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmNvbG9yX1MwOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABUAAAAAQAAAAAAAAACAAAAHQAAAAAAAAABAAAACQAAAAgAAAABAAAADAAAAAAAAABQAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAD9AAAA", "HRIAAAAAAAMAAAAAAQ4PZ72HLQCDR7H7777QGAAAAAAAAAAALAABUAAAABCC3BNJEEAAAACIUBDGCQAAAAADAAAAACIEBDOCQAAAAADAPLFI7CYCAAAMAAAAACAACAAAACVX2RMDIABAAAEAIXSO6BYAAAABAAAAACEBCUKLMFVAQAAAAAAAAAAAGERICGUFAEAQAAGAAAAAAQACGUFAGAQAACAOSKJ7FYFAAAAAAMAAAAAGAAAABLHWCUGQECIAAAABNEN7D4AAAABAAAAAAQCABQAAAABEKCRTAIAAAAAJRHXS4ORAAAAAGAAAAADAAAAABQDKL7ISBEAAAAAGAEPZ7MAQAAAAAQAAAAAAMJCAENIKAMBAAAEA5EUT6LQKAAAAAAYAAAAAMAAAACWPMFINAIEQAAAAC2I36HYAAAACAAAAAAQEMTCP7FYVCAAAAAMAAAAAGAAAAADAWWXWQECIAAAABMEI7T6QAAAAAABAAAAAGG4NUVZUBASAAAAAAAAAAAAVQFC6J3YHAAAAACAAAAAIQIKYIT7H4AAAAAAAAAAAAAKQAAAIAAAABGBBAAAAAAAAAAAAAAEADQAAAAAAAEAAAAAIDEAAAACGAAAAAAIAAAAAAAAAAAAAAAAAAAAAAAGDAEAAEAAAAAAA": "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", "HRJAAAAAAAMAAAAAAQ4PZ72HLQCDR7H7777QGAAAAACAAAAAACCAYABEA2CJQ4YGFEAAAAAGAAAAAAAAAAAMQAIAAAABAAAAACAJAAKGAAAAAAIAAAAP2AAAAAAAAAAAEAAAAAGDAEAAEAAAAAAA": "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", "HQJAAAAAAAGAAAAAAIOP577774BRZ7X7777QCAAAAABAAAAAABBAMAASANBMYOMDCQAAAAADAAAAAAAAAAAOIAAAAAAAQAAAABAMQACQAAAAAAIAAAAP2AAAAAAAAAAAAAAAAAGDAEAAEAAAAAAA": "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", "HQIAAAAAAAGAAAAAAIOP577774BRZ7X7777QCAAAAAAAAAAAVIBACAABAAAAAMYECAZAAEAAAAAAAAEQAMAAAABAAAAAAABBAMAAAACQAAAAAAIAAAAAAAAAAAAAAAAAEAAAAAADAAAAEAAAAAAA": "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", "C4QAAAAAMAAAAABAYAQ6FASCAEAAAABAAAAAAAAAAAAAAOIAAAAAAAQAAAABAMQAKAAAAAABAAAAB7IAAAAAAAAAAAAAAAAAYMAQAAAAAAAAA": "CAAAAExTS1PYAgAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKCWxheW91dChvZmZzZXQ9MTYpIGZsb2F0MiB1QXRsYXNTaXplSW52X1MwOwp9CjsKbGF5b3V0KGxvY2F0aW9uID0gMCkgaW4gZmxvYXQyIGluUG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGhhbGY0IGluQ29sb3I7CmxheW91dChsb2NhdGlvbiA9IDIpIGluIHVzaG9ydDIgaW5UZXh0dXJlQ29vcmRzOwpsYXlvdXQobG9jYXRpb24gPSAwKSBvdXQgZmxvYXQyIHZUZXh0dXJlQ29vcmRzX1MwOwpsYXlvdXQobG9jYXRpb24gPSAxKSBmbGF0IG91dCBmbG9hdCB2VGV4SW5kZXhfUzA7CmxheW91dChsb2NhdGlvbiA9IDIpIG91dCBoYWxmNCB2aW5Db2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFByaW1pdGl2ZSBQcm9jZXNzb3IgQml0bWFwVGV4dAoJaW50IHRleElkeCA9IDA7CglmbG9hdDIgdW5vcm1UZXhDb29yZHMgPSBmbG9hdDIoaW5UZXh0dXJlQ29vcmRzLngsIGluVGV4dHVyZUNvb3Jkcy55KTsKCXZUZXh0dXJlQ29vcmRzX1MwID0gdW5vcm1UZXhDb29yZHMgKiB1QXRsYXNTaXplSW52X1MwOwoJdlRleEluZGV4X1MwID0gZmxvYXQodGV4SWR4KTsKCXZpbkNvbG9yX1MwID0gaW5Db2xvcjsKCWZsb2F0MiBfdG1wXzFfaW5Qb3NpdGlvbiA9IGluUG9zaXRpb247Cglza19Qb3NpdGlvbiA9IGluUG9zaXRpb24ueHkwMTsKfQoAAAAAuwIAAGxheW91dChiaW5kaW5nPTApIHVuaWZvcm0gc2FtcGxlcjJEIHVUZXh0dXJlU2FtcGxlcl8wX1MwOwpsYXlvdXQgKGJpbmRpbmc9MCkgdW5pZm9ybSB1bmlmb3JtQnVmZmVyCnsKCWxheW91dChvZmZzZXQ9MCkgZmxvYXQ0IHNrX1JUQWRqdXN0OwoJbGF5b3V0KG9mZnNldD0xNikgZmxvYXQyIHVBdGxhc1NpemVJbnZfUzA7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgdlRleHR1cmVDb29yZHNfUzA7CmxheW91dChsb2NhdGlvbiA9IDEpIGZsYXQgaW4gZmxvYXQgdlRleEluZGV4X1MwOwpsYXlvdXQobG9jYXRpb24gPSAyKSBpbiBoYWxmNCB2aW5Db2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMCwgaW5kZXggPSAwKSBvdXQgaGFsZjQgc2tfRnJhZ0NvbG9yOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgQml0bWFwVGV4dAoJaGFsZjQgb3V0cHV0Q29sb3JfUzA7CglvdXRwdXRDb2xvcl9TMCA9IHZpbkNvbG9yX1MwOwoJaGFsZjQgdGV4Q29sb3I7Cgl7CgkJdGV4Q29sb3IgPSBzYW1wbGUodVRleHR1cmVTYW1wbGVyXzBfUzAsIHZUZXh0dXJlQ29vcmRzX1MwKS5ycnJyOwoJfQoJaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSB0ZXhDb2xvcjsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABgAAAAAQAAAAAAAAADAAAAHQAAAAAAAAABAAAACQAAAAgAAAABAAAADQAAAAwAAAABAAAAEAAAAAAAAABQAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAD9AAAA", "HQQAAAAAAAGAAAAAAIOP57ZDF37P7777777QCAAAAAAAAAAASABQAAAAEAAAAAAAEEBQAACQAAAAAAIAAAAAAAAAAAAAAAAAAAAAAAGDAEAAEAAAAAAA": "CAAAAExTS1NMAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDApIGZsYXQgb3V0IGhhbGY0IHZjb2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFByaW1pdGl2ZSBQcm9jZXNzb3IgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgl2Y29sb3JfUzAgPSBjb2xvcjsKCXNrX1Bvc2l0aW9uID0gcG9zaXRpb24ueHkwMTsKfQoAAAAAswEAAGxheW91dCAoYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMCwgaW5kZXggPSAwKSBvdXQgaGFsZjQgc2tfRnJhZ0NvbG9yOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmNvbG9yX1MwOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABUAAAAAQAAAAAAAAACAAAAHQAAAAAAAAABAAAACQAAAAgAAAABAAAADAAAAAAAAABQAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAAAAAAA", "HRIAAAAAAAMAAAAAAQ4PZ72HLQCDR7H7777QGAAAAAAAAAAAHADCIUFDGAQAAAAAAIAAB7QAAAAP4AAAAD7AAAAA72MJ54XDUIAAAAAQAAAAAYAAAAAIBYTZF4EBCAAAABQBD6P3AEAAAAACAAAAAYWE6SKB6FYFAAAIAAAAAAAAGAAAAAKM662BRAAAAAAARPEN6DYAAAACAAAAAAIIHCXHXUQEIAAAAAAAAAAAKAIVQRH6PYAAAAEAAAAABAAYQJC6J3YHAAAAAAAAAAAFAAIAQAAAAAEADEBAAAAAAAAAAAAAADEACAAAAAIAAAAAQCIACACQAAAAAAIAAAAAAAAAAAAAAAAAEAAAAAGDAEAAEAAAAAAA": "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", "HQQACAAAAAGAAAAAAIOP57ZDF37P7777777QCAAAAAAAAAAASABQAAAAEAAAAAAAEEBQAACQAAAAAAIAAAAAAAAAAAAAAAAAAAAAAAGDAEAAAAAAAAAA": "CAAAAExTS1NHAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDApIG91dCBoYWxmNCB2Y29sb3JfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmNvbG9yX1MwID0gY29sb3I7Cglza19Qb3NpdGlvbiA9IHBvc2l0aW9uLnh5MDE7Cn0KAAAAAACuAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMCwgaW5kZXggPSAwKSBvdXQgaGFsZjQgc2tfRnJhZ0NvbG9yOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmNvbG9yX1MwOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAVAAAAAEAAAAAAAAAAgAAAB0AAAAAAAAAAQAAAAkAAAAIAAAAAQAAAAwAAAAAAAAAUAAAAAEAAAABAAAABQAAAAAAAAABAAAABQAAAAAAAAABAAAAAAAAAA==", "AWQAGAAAQAAIPCELAGEP777777777737AAAAAAAAAAAABZAAAAAAACAAAAAEBSAAIYAAAAABAAAAB7IAAAAAAAAAAAAAAAAAYMAQAAAAAAAAA": "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", "HQIAAAAAAAGAAAAAAIOP577774BRZ7X7777QCAAAAAAAAAAAVIBAYAAAAAAQCAIAAAACRRAAAAABAEAQAAAABIECAEAQCAAAAAZQIEBSAAIBAAAAAAAJAAYAAAACAAAAAAACCA2GAAAAAAIAAAAAAAAAAAAAAAAAAAAAAAADAAAAEAAAAAAA": "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", "HQIAAAAAAAGAAAAAAIOP577774BRZ7X7777QCAAAAAAAAAAAVIBACAIBAAAAAMYECAZAAEAQAAAAAAEQAMAAAABAAAAAAABBAMAAAACGAAAAAAIAAAAAAAAAAAAAAAAAEAAAAAADAAAAEAAAAAAA": "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", "HQIAAAAAAAGAAAAAAIOP577774BRZ7X7777QCAAAAAAAAAAAVIBAAAABAAAAAMYECAZAAAAAAAAAAAEQAMAAAABAAAAAAABBAMAAAACGAAAAAAIAAAAAAAAAAAAAAAAAAAAAAAADAAAAEAAAAAAA": "CAAAAExTS1O5AQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKCWxheW91dChvZmZzZXQ9MTYpIGZsb2F0M3gzIHVtYXRyaXhfUzE7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGZsb2F0MiBsb2NhbENvb3JkOwpsYXlvdXQobG9jYXRpb24gPSAwKSBvdXQgZmxvYXQyIHZUcmFuc2Zvcm1lZENvb3Jkc18yX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gUHJpbWl0aXZlIFByb2Nlc3NvciBRdWFkUGVyRWRnZUFBR2VvbWV0cnlQcm9jZXNzb3IKCXNrX1Bvc2l0aW9uID0gcG9zaXRpb24ueHkwMTsKCXsKCQl2VHJhbnNmb3JtZWRDb29yZHNfMl9TMCA9IGZsb2F0M3gyKHVtYXRyaXhfUzEpICogbG9jYWxDb29yZC54eTE7Cgl9Cn0KAAAAAAAAAAIDAABsYXlvdXQoYmluZGluZz0wKSB1bmlmb3JtIHNhbXBsZXIyRCB1VGV4dHVyZVNhbXBsZXJfMF9TMTsKbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKCWxheW91dChvZmZzZXQ9MTYpIGZsb2F0M3gzIHVtYXRyaXhfUzE7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgdlRyYW5zZm9ybWVkQ29vcmRzXzJfUzA7CmxheW91dChsb2NhdGlvbiA9IDAsIGluZGV4ID0gMCkgb3V0IGhhbGY0IHNrX0ZyYWdDb2xvcjsKaGFsZjQgVGV4dHVyZUVmZmVjdF9TMV9jMChoYWxmNCBfaW5wdXQpIAp7CglyZXR1cm4gc2FtcGxlKHVUZXh0dXJlU2FtcGxlcl8wX1MxLCB2VHJhbnNmb3JtZWRDb29yZHNfMl9TMCk7Cn0KaGFsZjQgTWF0cml4RWZmZWN0X1MxKGhhbGY0IF9pbnB1dCkgCnsKCXJldHVybiBUZXh0dXJlRWZmZWN0X1MxX2MwKF9pbnB1dCk7Cn0Kdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzAgPSBoYWxmNCgxKTsKCWNvbnN0IGhhbGY0IG91dHB1dENvdmVyYWdlX1MwID0gaGFsZjQoMSk7CgloYWxmNCBvdXRwdXRfUzE7CglvdXRwdXRfUzEgPSBNYXRyaXhFZmZlY3RfUzEob3V0cHV0Q29sb3JfUzApOwoJewoJCS8vIFhmZXIgUHJvY2Vzc29yOiBQb3J0ZXIgRHVmZgoJCXNrX0ZyYWdDb2xvciA9IG91dHB1dF9TMSAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPAAAAAEAAAAAAAAAAgAAAB0AAAAAAAAAAQAAAB0AAAAIAAAAAQAAABAAAAAAAAAARgAAAAAAAAABAAAAAAAAAA==", "HQIAAAAAAAGAAAAAAIOP577774BRZ7X7777QCAAAAAAAAAAAVIBAYAAAAAAQAAIAAAACRRAAAAABAAAQAAAABIECAEAACAAAAAZQIEBSAAIAAAAAAAAJAAYAAAACAAAAAAACCA2QAAAAAAIAAAAAAAAAAAAAAAAAAAAAAAADAAAAEAAAAAAA": "CAAAAExTS1OiAgAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKCWxheW91dChvZmZzZXQ9MTYpIGZsb2F0NCB1Y2xhbXBfUzFfYzBfYzBfYzA7CglsYXlvdXQob2Zmc2V0PTMyKSBmbG9hdDN4MyB1bWF0cml4X1MxX2MwX2MwOwoJbGF5b3V0KG9mZnNldD04MCkgaGFsZjIgdV8wX0luY3JlbWVudF9TMV9jMDsKCWxheW91dChvZmZzZXQ9ODgpIGhhbGY0IHVfMV9LZXJuZWxfUzFfYzBbNF07CglsYXlvdXQob2Zmc2V0PTEyMCkgaGFsZjQgdV8yX09mZnNldHNfUzFfYzBbNF07CglsYXlvdXQob2Zmc2V0PTE2MCkgZmxvYXQzeDMgdW1hdHJpeF9TMTsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gZmxvYXQyIGxvY2FsQ29vcmQ7CmxheW91dChsb2NhdGlvbiA9IDApIG91dCBmbG9hdDIgdlRyYW5zZm9ybWVkQ29vcmRzXzJfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJc2tfUG9zaXRpb24gPSBwb3NpdGlvbi54eTAxOwoJewoJCXZUcmFuc2Zvcm1lZENvb3Jkc18yX1MwID0gZmxvYXQzeDIodW1hdHJpeF9TMSkgKiBsb2NhbENvb3JkLnh5MTsKCX0KfQoAAAAAAAARBwAAbGF5b3V0KGJpbmRpbmc9MCkgdW5pZm9ybSBzYW1wbGVyMkQgdVRleHR1cmVTYW1wbGVyXzBfUzE7CmxheW91dCAoYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7CglsYXlvdXQob2Zmc2V0PTE2KSBmbG9hdDQgdWNsYW1wX1MxX2MwX2MwX2MwOwoJbGF5b3V0KG9mZnNldD0zMikgZmxvYXQzeDMgdW1hdHJpeF9TMV9jMF9jMDsKCWxheW91dChvZmZzZXQ9ODApIGhhbGYyIHVfMF9JbmNyZW1lbnRfUzFfYzA7CglsYXlvdXQob2Zmc2V0PTg4KSBoYWxmNCB1XzFfS2VybmVsX1MxX2MwWzRdOwoJbGF5b3V0KG9mZnNldD0xMjApIGhhbGY0IHVfMl9PZmZzZXRzX1MxX2MwWzRdOwoJbGF5b3V0KG9mZnNldD0xNjApIGZsb2F0M3gzIHVtYXRyaXhfUzE7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgdlRyYW5zZm9ybWVkQ29vcmRzXzJfUzA7CmxheW91dChsb2NhdGlvbiA9IDAsIGluZGV4ID0gMCkgb3V0IGhhbGY0IHNrX0ZyYWdDb2xvcjsKaGFsZjQgVGV4dHVyZUVmZmVjdF9TMV9jMF9jMF9jMChoYWxmNCBfaW5wdXQsIGZsb2F0MiBfY29vcmRzKSAKewoJZmxvYXQyIGluQ29vcmQgPSBfY29vcmRzOwoJZmxvYXQyIHN1YnNldENvb3JkOwoJc3Vic2V0Q29vcmQueCA9IGluQ29vcmQueDsKCXN1YnNldENvb3JkLnkgPSBpbkNvb3JkLnk7CglmbG9hdDIgY2xhbXBlZENvb3JkOwoJY2xhbXBlZENvb3JkLnggPSBjbGFtcChzdWJzZXRDb29yZC54LCB1Y2xhbXBfUzFfYzBfYzBfYzAueCwgdWNsYW1wX1MxX2MwX2MwX2MwLnopOwoJY2xhbXBlZENvb3JkLnkgPSBzdWJzZXRDb29yZC55OwoJaGFsZjQgdGV4dHVyZUNvbG9yID0gc2FtcGxlKHVUZXh0dXJlU2FtcGxlcl8wX1MxLCBjbGFtcGVkQ29vcmQpOwoJcmV0dXJuIHRleHR1cmVDb2xvcjsKfQpoYWxmNCBNYXRyaXhFZmZlY3RfUzFfYzBfYzAoaGFsZjQgX2lucHV0LCBmbG9hdDIgX2Nvb3JkcykgCnsKCXJldHVybiBUZXh0dXJlRWZmZWN0X1MxX2MwX2MwX2MwKF9pbnB1dCwgZmxvYXQzeDIodW1hdHJpeF9TMV9jMF9jMCkgKiBfY29vcmRzLnh5MSk7Cn0KaGFsZjQgR2F1c3NpYW5Db252b2x1dGlvbl9TMV9jMChoYWxmNCBfaW5wdXQpIAp7CgloYWxmNCBfM19jb2xvciA9IGhhbGY0KDAuMCk7CglmbG9hdDIgXzRfY29vcmQgPSB2VHJhbnNmb3JtZWRDb29yZHNfMl9TMDsKCWZvciAoaW50IF81X2kgPSAwOyAoXzVfaSA8IDEzKTsgXzVfaSsrKSAoXzNfY29sb3IgKz0gKE1hdHJpeEVmZmVjdF9TMV9jMF9jMChfaW5wdXQsIChfNF9jb29yZCArIGZsb2F0MigodV8yX09mZnNldHNfUzFfYzBbKF81X2kgLyA0KV1bKF81X2kgJiAzKV0gKiB1XzBfSW5jcmVtZW50X1MxX2MwKSkpKSAqIHVfMV9LZXJuZWxfUzFfYzBbKF81X2kgLyA0KV1bKF81X2kgJiAzKV0pKTsKCXJldHVybiBfM19jb2xvcjsKfQpoYWxmNCBNYXRyaXhFZmZlY3RfUzEoaGFsZjQgX2lucHV0KSAKewoJcmV0dXJuIEdhdXNzaWFuQ29udm9sdXRpb25fUzFfYzAoX2lucHV0KTsKfQp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMCA9IGhhbGY0KDEpOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCWhhbGY0IG91dHB1dF9TMTsKCW91dHB1dF9TMSA9IE1hdHJpeEVmZmVjdF9TMShvdXRwdXRDb2xvcl9TMCk7Cgl7CgkJLy8gWGZlciBQcm9jZXNzb3I6IFBvcnRlciBEdWZmCgkJc2tfRnJhZ0NvbG9yID0gb3V0cHV0X1MxICogb3V0cHV0Q292ZXJhZ2VfUzA7Cgl9Cn0KAAAAAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPAAAAAEAAAAAAAAAAgAAAB0AAAAAAAAAAQAAAB0AAAAIAAAAAQAAABAAAAAAAAAAUAAAAAAAAAABAAAAAAAAAA==", "HQJAAAAAAAGAAAAAAIOP577774BRZ7X7777QCAAAAABAAAAAABBAMAEQAMAAAABAAAAAAABBAMAAAUAAAAAACAAAAAAAAAAAAAAAAABAAAAABQYBAABAAAAAAA": "CAAAAExTS1NdAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gZmxvYXQyIGxvY2FsQ29vcmQ7CmxheW91dChsb2NhdGlvbiA9IDApIG91dCBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmxvY2FsQ29vcmRfUzAgPSBsb2NhbENvb3JkOwoJc2tfUG9zaXRpb24gPSBwb3NpdGlvbi54eTAxOwp9CgAAAAAAAABTAgAAbGF5b3V0KGJpbmRpbmc9MCkgdW5pZm9ybSBzYW1wbGVyMkQgdVRleHR1cmVTYW1wbGVyXzBfUzA7CmxheW91dCAoYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CmxheW91dChsb2NhdGlvbiA9IDAsIGluZGV4ID0gMCkgb3V0IGhhbGY0IHNrX0ZyYWdDb2xvcjsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzAgPSBoYWxmNCgxKTsKCWZsb2F0MiB0ZXhDb29yZDsKCXRleENvb3JkID0gdmxvY2FsQ29vcmRfUzA7CglvdXRwdXRDb2xvcl9TMCA9ICgoc2FtcGxlKHVUZXh0dXJlU2FtcGxlcl8wX1MwLCB0ZXhDb29yZCkgKiBoYWxmNCgxKSkpOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABUAAAAAQAAAAAAAAACAAAAHQAAAAAAAAABAAAAHQAAAAgAAAABAAAAEAAAAAAAAABQAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAAAAAAA", "AWQAGAAAQAAIPCELAGEP777777777737AAAAAAAAAAAABZAAAAAAACAAAAAEBSAAKAAAAAABAAAAB7IAAAAAAAAAAAAAAAAAYMAQAAAAAAAAA": "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", "CEAAAAAADAAAAAAYQAMLQGEABAAAAAAAEIBAAQAOAAAABAAAAAAABBAMABDAAAAAAEAAAAH5AAAAAAAAAAAAAAAAADBQCAACAAAAAAA": "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", "HQQAAAAAAAGAAAAAAIOP57ZDF37P7777777QCAAAAAAAAAAASABQAAAAEAAAAAAAEEBQAACGAAAAAAIAAAAAAAAAAAAAAAAAAAAAAAGDAEAAEAAAAAAA": "CAAAAExTS1NMAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDApIGZsYXQgb3V0IGhhbGY0IHZjb2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFByaW1pdGl2ZSBQcm9jZXNzb3IgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgl2Y29sb3JfUzAgPSBjb2xvcjsKCXNrX1Bvc2l0aW9uID0gcG9zaXRpb24ueHkwMTsKfQoAAAAAswEAAGxheW91dCAoYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMCwgaW5kZXggPSAwKSBvdXQgaGFsZjQgc2tfRnJhZ0NvbG9yOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmNvbG9yX1MwOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABUAAAAAQAAAAAAAAACAAAAHQAAAAAAAAABAAAACQAAAAgAAAABAAAADAAAAAAAAABGAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAAAAAAA", "HQQAAAAAAAGAAAAAAIOP57ZDF37P7777777QCAAAAAAAAAAASABQAAAAEAAAAAAAEEBQAACGAAAAAAIAAAAAAAAAAAAAAAAAAAAAAAGDAEAAAAAAAAAA": "CAAAAExTS1NMAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDApIGZsYXQgb3V0IGhhbGY0IHZjb2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFByaW1pdGl2ZSBQcm9jZXNzb3IgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgl2Y29sb3JfUzAgPSBjb2xvcjsKCXNrX1Bvc2l0aW9uID0gcG9zaXRpb24ueHkwMTsKfQoAAAAAswEAAGxheW91dCAoYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMCwgaW5kZXggPSAwKSBvdXQgaGFsZjQgc2tfRnJhZ0NvbG9yOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmNvbG9yX1MwOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABUAAAAAQAAAAAAAAACAAAAHQAAAAAAAAABAAAACQAAAAgAAAABAAAADAAAAAAAAABGAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAAAAAAA", "HQQACAAAAAGAAAAAAIOP57ZDF37P7777777QCAAAAAAAAAAASABQAAAAEAAAAAAAEEBQAACGAAAAAAIAAAAP2AAAAAAAAAAAAAAAAAGDAEAAAAAAAAAA": "CAAAAExTS1NHAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDApIG91dCBoYWxmNCB2Y29sb3JfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmNvbG9yX1MwID0gY29sb3I7Cglza19Qb3NpdGlvbiA9IHBvc2l0aW9uLnh5MDE7Cn0KAAAAAACuAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMCwgaW5kZXggPSAwKSBvdXQgaGFsZjQgc2tfRnJhZ0NvbG9yOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmNvbG9yX1MwOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAVAAAAAEAAAAAAAAAAgAAAB0AAAAAAAAAAQAAAAkAAAAIAAAAAQAAAAwAAAAAAAAARgAAAAEAAAABAAAABQAAAAAAAAABAAAABQAAAAAAAAABAAAA/QAAAA==", "HRJAAAAAAAMAAAAAAQ4PZ72HLQCDR7H7777QGAAAAACAAAAAACCAYABEA2CJQ4YGFEAAAAAGAAAAAAAAAAAMQAIAAAABAAAAACAJAAKQAAAAAAIAAAAP2AAAAAAAAAAAAAAAAAGDAEAAEAAAAAAA": "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", "HQIAAAAAAAGAAAAAAIOP577774BRZ7X7777QCAAAAAAAAAAAVIBAYAAAAAAQCAIAAAACRRAAAAABAEAQAAAABIECAEAQCAAAAAZQIEBSAAIBAAAAAAAJAAYAAAACAAAAAAACCA2GAAAAAAIAAAAAAAAAAAAAAAAAAAAAAAADAAAAAAAAAAAA": "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", "HQJAAAAAAAGAAAAAAIOP577774BRZ7X7777QCAAAAABAAAAAABBAMAEQAMAAAABAAAAAAABBAMAAARQAAAAACAAAAAAAAAAAAAAAAABAAAAABQYBAABAAAAAAA": "CAAAAExTS1NdAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gZmxvYXQyIGxvY2FsQ29vcmQ7CmxheW91dChsb2NhdGlvbiA9IDApIG91dCBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmxvY2FsQ29vcmRfUzAgPSBsb2NhbENvb3JkOwoJc2tfUG9zaXRpb24gPSBwb3NpdGlvbi54eTAxOwp9CgAAAAAAAABTAgAAbGF5b3V0KGJpbmRpbmc9MCkgdW5pZm9ybSBzYW1wbGVyMkQgdVRleHR1cmVTYW1wbGVyXzBfUzA7CmxheW91dCAoYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CmxheW91dChsb2NhdGlvbiA9IDAsIGluZGV4ID0gMCkgb3V0IGhhbGY0IHNrX0ZyYWdDb2xvcjsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzAgPSBoYWxmNCgxKTsKCWZsb2F0MiB0ZXhDb29yZDsKCXRleENvb3JkID0gdmxvY2FsQ29vcmRfUzA7CglvdXRwdXRDb2xvcl9TMCA9ICgoc2FtcGxlKHVUZXh0dXJlU2FtcGxlcl8wX1MwLCB0ZXhDb29yZCkgKiBoYWxmNCgxKSkpOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABUAAAAAQAAAAAAAAACAAAAHQAAAAAAAAABAAAAHQAAAAgAAAABAAAAEAAAAAAAAABGAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAAAAAAA", "HRIAAAAAAAMAAAAAAQ4PZ72HLQCDR7H7777QGAAAAAAAAAAAIQCQAAACAAAAAZQIAAAAAAAAAAAAAABAA4AAAACAAAAAAACCAYAAAACGAAAAAAIAAAAAAAAAAAAAAAAAAAAAAAGDAEAAEAAAAAAA": "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", "HQQACAAAAAGAAAAAAIOP57ZDF37P7777777QCAAAAAAAAAAASABQAAAAEAAAAAAAEEBQAACGAAAAAAIAAAAAAAAAAAAAAAAAEAAAAAGDAEAAAAAAAAAA": "CAAAAExTS1NHAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDApIG91dCBoYWxmNCB2Y29sb3JfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmNvbG9yX1MwID0gY29sb3I7Cglza19Qb3NpdGlvbiA9IHBvc2l0aW9uLnh5MDE7Cn0KAAAAAACuAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMCwgaW5kZXggPSAwKSBvdXQgaGFsZjQgc2tfRnJhZ0NvbG9yOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmNvbG9yX1MwOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAVAAAAAEAAAAAAAAAAgAAAB0AAAAAAAAAAQAAAAkAAAAIAAAAAQAAAAwAAAAAAAAARgAAAAEAAAABAAAABQAAAAAAAAABAAAABQAAAAAAAAABAAAAAAAAAA=="}}
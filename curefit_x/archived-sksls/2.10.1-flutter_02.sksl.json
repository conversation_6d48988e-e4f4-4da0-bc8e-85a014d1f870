{"platform": "ios", "name": "<PERSON><PERSON><PERSON>’s iPhone", "engineRevision": "ab46186b246f5a36bd1f3f295d14a43abb1e2f38", "data": {"HUQAAAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAAKAAYAAAACAAAAAAACCAYAABIAAAAAAEAAAAH5AAAAAAIAAAABAAAAADBQCAACAAAAAAA": "CAAAAExTS1NMAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDApIGZsYXQgb3V0IGhhbGY0IHZjb2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFByaW1pdGl2ZSBQcm9jZXNzb3IgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgl2Y29sb3JfUzAgPSBjb2xvcjsKCXNrX1Bvc2l0aW9uID0gcG9zaXRpb24ueHkwMTsKfQoAAAAAswEAAGxheW91dCAoYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMCwgaW5kZXggPSAwKSBvdXQgaGFsZjQgc2tfRnJhZ0NvbG9yOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmNvbG9yX1MwOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABUAAAAAQAAAAAAAAACAAAAHQAAAAAAAAABAAAACQAAAAgAAAABAAAADAAAAAAAAABQAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAD9AAAA", "JAAQAAAABAAACAABBYAAAKAAAMAAGEAAAABRAEAAAEHCAAAAAAAABCAAAAAABAEQAEAAAACQAAAAAAIAAAAP2AAAAAAQAAAAGAAAAAACAAAAAAAAAAAA": "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", "HTQAAGAABBYAAAEIXBAAAGEAMAAAAAAAAAAAAAAAQAHAAAAAQAAAAAAAQQGAAAAAKAAAAAABAAAAAAAAAAAAAAAAAAQAAAAAYMAQAAAAAAAAA": "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", "HVJAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAABAAAAAABBAMABAAOAAAABAAAAAAABBAMAAAIYAAAAABAAAAAAAAAAAAAAAAAAAAAAAAYMAQAAQAAAAAA": "CAAAAExTS1PFAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDIpIGluIGZsb2F0MiBsb2NhbENvb3JkOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IG91dCBoYWxmNCB2Y29sb3JfUzA7CmxheW91dChsb2NhdGlvbiA9IDEpIG91dCBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmNvbG9yX1MwID0gY29sb3I7Cgl2bG9jYWxDb29yZF9TMCA9IGxvY2FsQ29vcmQ7Cglza19Qb3NpdGlvbiA9IHBvc2l0aW9uLnh5MDE7Cn0KAAAAAAAAAJkCAABsYXlvdXQoYmluZGluZz0wKSB1bmlmb3JtIHNhbXBsZXIyRCB1VGV4dHVyZVNhbXBsZXJfMF9TMDsKbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGZsYXQgaW4gaGFsZjQgdmNvbG9yX1MwOwpsYXlvdXQobG9jYXRpb24gPSAxKSBpbiBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CmxheW91dChsb2NhdGlvbiA9IDAsIGluZGV4ID0gMCkgb3V0IGhhbGY0IHNrX0ZyYWdDb2xvcjsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzA7CglvdXRwdXRDb2xvcl9TMCA9IHZjb2xvcl9TMDsKCWZsb2F0MiB0ZXhDb29yZDsKCXRleENvb3JkID0gdmxvY2FsQ29vcmRfUzA7CglvdXRwdXRDb2xvcl9TMCA9ICgoc2FtcGxlKHVUZXh0dXJlU2FtcGxlcl8wX1MwLCB0ZXhDb29yZCkgKiBvdXRwdXRDb2xvcl9TMCkpOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAAABAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGAAAAABAAAAAAAAAAMAAAAdAAAAAAAAAAEAAAAJAAAACAAAAAEAAAAdAAAADAAAAAEAAAAUAAAAAAAAAEYAAAABAAAAAQAAAAUAAAAAAAAAAQAAAAUAAAAAAAAAAQAAAAAAAAA=", "HUQAAAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAAKAAYAAAACAAAAAAACCAYAABIAAAAAAEAAAAAAAAAAAAAAAAACAAAAADBQCAACAAAAAAA": "CAAAAExTS1NMAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDApIGZsYXQgb3V0IGhhbGY0IHZjb2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFByaW1pdGl2ZSBQcm9jZXNzb3IgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgl2Y29sb3JfUzAgPSBjb2xvcjsKCXNrX1Bvc2l0aW9uID0gcG9zaXRpb24ueHkwMTsKfQoAAAAAswEAAGxheW91dCAoYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMCwgaW5kZXggPSAwKSBvdXQgaGFsZjQgc2tfRnJhZ0NvbG9yOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmNvbG9yX1MwOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABUAAAAAQAAAAAAAAACAAAAHQAAAAAAAAABAAAACQAAAAgAAAABAAAADAAAAAAAAABQAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAAAAAAA", "HVIAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAAAAAAAAFQBTWV34ISAIAAAAAAAACAAAAAVQEAAQAAAAAQCDAAQQGAAAAAAAAAAAA4IAPAAACAAAAAAAEABYAAAAEAAAAAAAEEBQAIYAAAAABAAAAAAAAAAAAAAAAAAAAAAAAYMAQAAQAAAAAA": "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", "HUQACAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAAKAAYAAAACAAAAAAACCAYAABIAAAAAAEAAAAH5AAAAAAAAAAAAAAAAADBQCAAAAAAAAAA": "CAAAAExTS1NHAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDApIG91dCBoYWxmNCB2Y29sb3JfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmNvbG9yX1MwID0gY29sb3I7Cglza19Qb3NpdGlvbiA9IHBvc2l0aW9uLnh5MDE7Cn0KAAAAAACuAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMCwgaW5kZXggPSAwKSBvdXQgaGFsZjQgc2tfRnJhZ0NvbG9yOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmNvbG9yX1MwOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAVAAAAAEAAAAAAAAAAgAAAB0AAAAAAAAAAQAAAAkAAAAIAAAAAQAAAAwAAAAAAAAAUAAAAAEAAAABAAAABQAAAAAAAAABAAAABQAAAAAAAAABAAAA/QAAAA==", "HUQAAAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAAHEADZAAAAAAIAAAAAAOQAAAAAAAQAAAABAMQAAAAAAUAAAAAACAAAAAAAAAAAAAAAAAAAAAAABQYBAABAAAAAAA": "CAAAAExTS1OiAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKCWxheW91dChvZmZzZXQ9MTYpIGZsb2F0NCB1aW5uZXJSZWN0X1MxOwoJbGF5b3V0KG9mZnNldD0zMikgaGFsZjIgdXJhZGl1c1BsdXNIYWxmX1MxOwp9CjsKbGF5b3V0KGxvY2F0aW9uID0gMCkgaW4gZmxvYXQyIHBvc2l0aW9uOwpsYXlvdXQobG9jYXRpb24gPSAxKSBpbiBoYWxmNCBjb2xvcjsKbGF5b3V0KGxvY2F0aW9uID0gMCkgZmxhdCBvdXQgaGFsZjQgdmNvbG9yX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gUHJpbWl0aXZlIFByb2Nlc3NvciBRdWFkUGVyRWRnZUFBR2VvbWV0cnlQcm9jZXNzb3IKCXZjb2xvcl9TMCA9IGNvbG9yOwoJc2tfUG9zaXRpb24gPSBwb3NpdGlvbi54eTAxOwp9CgAAAAAAAFgDAABsYXlvdXQgKGJpbmRpbmc9MCkgdW5pZm9ybSB1bmlmb3JtQnVmZmVyCnsKCWxheW91dChvZmZzZXQ9MCkgZmxvYXQ0IHNrX1JUQWRqdXN0OwoJbGF5b3V0KG9mZnNldD0xNikgZmxvYXQ0IHVpbm5lclJlY3RfUzE7CglsYXlvdXQob2Zmc2V0PTMyKSBoYWxmMiB1cmFkaXVzUGx1c0hhbGZfUzE7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMCwgaW5kZXggPSAwKSBvdXQgaGFsZjQgc2tfRnJhZ0NvbG9yOwpoYWxmNCBDaXJjdWxhclJSZWN0X1MxKGhhbGY0IF9pbnB1dCkgCnsKCWZsb2F0MiBkeHkwID0gdWlubmVyUmVjdF9TMS5MVCAtIHNrX0ZyYWdDb29yZC54eTsKCWZsb2F0MiBkeHkxID0gc2tfRnJhZ0Nvb3JkLnh5IC0gdWlubmVyUmVjdF9TMS5SQjsKCWZsb2F0MiBkeHkgPSBtYXgobWF4KGR4eTAsIGR4eTEpLCAwLjApOwoJaGFsZiBhbHBoYSA9IGhhbGYoc2F0dXJhdGUodXJhZGl1c1BsdXNIYWxmX1MxLnggLSBsZW5ndGgoZHh5KSkpOwoJcmV0dXJuIF9pbnB1dCAqIGFscGhhOwp9CnZvaWQgbWFpbigpIAp7CgkvLyBTdGFnZSAwLCBRdWFkUGVyRWRnZUFBR2VvbWV0cnlQcm9jZXNzb3IKCWhhbGY0IG91dHB1dENvbG9yX1MwOwoJb3V0cHV0Q29sb3JfUzAgPSB2Y29sb3JfUzA7Cgljb25zdCBoYWxmNCBvdXRwdXRDb3ZlcmFnZV9TMCA9IGhhbGY0KDEpOwoJaGFsZjQgb3V0cHV0X1MxOwoJb3V0cHV0X1MxID0gQ2lyY3VsYXJSUmVjdF9TMShvdXRwdXRDb3ZlcmFnZV9TMCk7Cgl7CgkJLy8gWGZlciBQcm9jZXNzb3I6IFBvcnRlciBEdWZmCgkJc2tfRnJhZ0NvbG9yID0gb3V0cHV0Q29sb3JfUzAgKiBvdXRwdXRfUzE7Cgl9Cn0KAQAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAVAAAAAEAAAAAAAAAAgAAAB0AAAAAAAAAAQAAAAkAAAAIAAAAAQAAAAwAAAAAAAAAUAAAAAEAAAABAAAABQAAAAAAAAABAAAABQAAAAAAAAABAAAAAAAAAA==", "HVJAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAABAAAAAABBAMABAAOAAAABAAAAAAABBAMAAAKAAAAAABAAAAB7IAAAAACAAAAAIAAAAAYMAQAAQAAAAAA": "CAAAAExTS1PFAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDIpIGluIGZsb2F0MiBsb2NhbENvb3JkOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IG91dCBoYWxmNCB2Y29sb3JfUzA7CmxheW91dChsb2NhdGlvbiA9IDEpIG91dCBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmNvbG9yX1MwID0gY29sb3I7Cgl2bG9jYWxDb29yZF9TMCA9IGxvY2FsQ29vcmQ7Cglza19Qb3NpdGlvbiA9IHBvc2l0aW9uLnh5MDE7Cn0KAAAAAAAAAJkCAABsYXlvdXQoYmluZGluZz0wKSB1bmlmb3JtIHNhbXBsZXIyRCB1VGV4dHVyZVNhbXBsZXJfMF9TMDsKbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGZsYXQgaW4gaGFsZjQgdmNvbG9yX1MwOwpsYXlvdXQobG9jYXRpb24gPSAxKSBpbiBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CmxheW91dChsb2NhdGlvbiA9IDAsIGluZGV4ID0gMCkgb3V0IGhhbGY0IHNrX0ZyYWdDb2xvcjsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzA7CglvdXRwdXRDb2xvcl9TMCA9IHZjb2xvcl9TMDsKCWZsb2F0MiB0ZXhDb29yZDsKCXRleENvb3JkID0gdmxvY2FsQ29vcmRfUzA7CglvdXRwdXRDb2xvcl9TMCA9ICgoc2FtcGxlKHVUZXh0dXJlU2FtcGxlcl8wX1MwLCB0ZXhDb29yZCkgKiBvdXRwdXRDb2xvcl9TMCkpOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAAABAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGAAAAABAAAAAAAAAAMAAAAdAAAAAAAAAAEAAAAJAAAACAAAAAEAAAAdAAAADAAAAAEAAAAUAAAAAAAAAFAAAAABAAAAAQAAAAUAAAAAAAAAAQAAAAUAAAAAAAAAAQAAAP0AAAA=", "HUIAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAAAAAALUAQMAAAAAAABAEAAAABJYQAAAAAACAIAAAAAWCBAAAIBAAAAANAECAZAAAAQAAAAAAFAAMAAAABAAAAAAABBANDAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAABQAAACAAAAAAA": "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", "HVJAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAABAAAAAABBAMADSAB4QAAAAAEAAAAAAHIAAAAAAAIAAAAAQGIAAAAAAFAAAAAAAQAAAA7UAAAAAAAAAAAAAAAAAMGAIAAIAAAAAA": "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", "DAQAAAAAAABGAABAYAAQAIHCAIAYAQUBAEAAAAAAEAAAAAAAAAAAAAB2AAAAAAACAAAAAEBSAAAAAUAAAAAACAAAAAAAAAAAAAAAAAAAAAAABQYBAAAAAAAAAA": "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", "FAAQMYAAMAAAEADAAABAEYAAAICIAB5AABQAAAQAEDAACATAAABAGYAAAICSBYQCA4AAAAAAAA5AAAAAAABAAAAACAZAAAAAKAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAYMAQAAAAAAAAA": "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", "HUIAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAAAAAALUAQMAAAAAAAAAEAAAABJYQAAAAAAAAIAAAAAWCBAAAABAAAAANAECAZAAAAAAAAAAAFAAMAAAABAAAAAAABBANIAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAABQAAACAAAAAAA": "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", "FAAQMYAAMAAAEADAAABAEYAAAICIAB5AABQAAAQAEDAACATAAABAGYAAAICSBYQCA4AAAAAAEABWAAAAAAAAAIAAAAAPAIAAAAAQAAAAFMDAAEAAAAAEAQ2AIQAAAAAAAAAAAAAAHIAAAAAAAIAAAAAQGIAAAAAAIYAAAAABAAAAAAAAAAAAAAAAAAAAAAAAYMAQAAAAAAAAA": "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", "HVIACAAAABQAAGAAAQ4AAAAAGQQAARC4GAAAIOCAAD6P7777777777YDAAAAAAAAAAAFIBTWV34ISAIAAAAEYT7ZOFIQAAAABAAAAABQAAAAAQHRXQLYICAAAAALBCH47UAAAAAAAEAAAABSMJ5MVD4LAIAAAQAAAAAIAAIAAAAIVZ55EBCAAAAAQBC6J3YHAAAAAEAAAAAJAQOF6NPBAIQAAAAAAAAAACWAQLBCP47QAAAAIAAAAAEABTASF4XXAMAAAAAAAAAAB2AAAAAAACAAAAAEBSAAKAAAAAABAAAAAAAAAAAAAAAAAAQAAAAAYMAQAAAAAAAAA": "CAAAAExTS1OpAwAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKCWxheW91dChvZmZzZXQ9MTYpIGZsb2F0NCB1c2NhbGVfUzFfYzBfYzBbMl07CglsYXlvdXQob2Zmc2V0PTQ4KSBmbG9hdDQgdWJpYXNfUzFfYzBfYzBbMl07CglsYXlvdXQob2Zmc2V0PTgwKSBoYWxmIHV0aHJlc2hvbGRfUzFfYzBfYzA7CglsYXlvdXQob2Zmc2V0PTk2KSBmbG9hdDN4MyB1bWF0cml4X1MxX2MwX2MxOwoJbGF5b3V0KG9mZnNldD0xNDQpIGhhbGY0IHVsZWZ0Qm9yZGVyQ29sb3JfUzFfYzA7CglsYXlvdXQob2Zmc2V0PTE1MikgaGFsZjQgdXJpZ2h0Qm9yZGVyQ29sb3JfUzFfYzA7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGZsb2F0IGNvdmVyYWdlOwpsYXlvdXQobG9jYXRpb24gPSAyKSBpbiBoYWxmNCBjb2xvcjsKbGF5b3V0KGxvY2F0aW9uID0gMykgaW4gZmxvYXQyIGxvY2FsQ29vcmQ7CmxheW91dChsb2NhdGlvbiA9IDApIGZsYXQgb3V0IGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMSkgb3V0IGZsb2F0IHZjb3ZlcmFnZV9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMikgb3V0IGZsb2F0MiB2VHJhbnNmb3JtZWRDb29yZHNfNV9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFByaW1pdGl2ZSBQcm9jZXNzb3IgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCglmbG9hdDIgcG9zaXRpb24gPSBwb3NpdGlvbi54eTsKCXZjb2xvcl9TMCA9IGNvbG9yOwoJdmNvdmVyYWdlX1MwID0gY292ZXJhZ2U7Cglza19Qb3NpdGlvbiA9IHBvc2l0aW9uLnh5MDE7Cgl7CgkJdlRyYW5zZm9ybWVkQ29vcmRzXzVfUzAgPSBmbG9hdDN4Mih1bWF0cml4X1MxX2MwX2MxKSAqIGxvY2FsQ29vcmQueHkxOwoJfQp9CgAAAAAAAADsCAAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKCWxheW91dChvZmZzZXQ9MTYpIGZsb2F0NCB1c2NhbGVfUzFfYzBfYzBbMl07CglsYXlvdXQob2Zmc2V0PTQ4KSBmbG9hdDQgdWJpYXNfUzFfYzBfYzBbMl07CglsYXlvdXQob2Zmc2V0PTgwKSBoYWxmIHV0aHJlc2hvbGRfUzFfYzBfYzA7CglsYXlvdXQob2Zmc2V0PTk2KSBmbG9hdDN4MyB1bWF0cml4X1MxX2MwX2MxOwoJbGF5b3V0KG9mZnNldD0xNDQpIGhhbGY0IHVsZWZ0Qm9yZGVyQ29sb3JfUzFfYzA7CglsYXlvdXQob2Zmc2V0PTE1MikgaGFsZjQgdXJpZ2h0Qm9yZGVyQ29sb3JfUzFfYzA7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gZmxvYXQgdmNvdmVyYWdlX1MwOwpsYXlvdXQobG9jYXRpb24gPSAyKSBpbiBmbG9hdDIgdlRyYW5zZm9ybWVkQ29vcmRzXzVfUzA7CmxheW91dChsb2NhdGlvbiA9IDAsIGluZGV4ID0gMCkgb3V0IGhhbGY0IHNrX0ZyYWdDb2xvcjsKaGFsZjQgRHVhbEludGVydmFsQ29sb3JpemVyX1MxX2MwX2MwKGhhbGY0IF9pbnB1dCwgZmxvYXQyIF9jb29yZHMpIAp7CgloYWxmNCBfdG1wXzBfaW5Db2xvciA9IF9pbnB1dDsKCWZsb2F0MiBfdG1wXzFfY29vcmRzID0gX2Nvb3JkczsKCWhhbGYgdCA9IGhhbGYoX3RtcF8xX2Nvb3Jkcy54KTsKCWZsb2F0NCBzOwoJZmxvYXQ0IGI7CglpZiAodCA8IHV0aHJlc2hvbGRfUzFfYzBfYzApIAoJewoJCXMgPSB1c2NhbGVfUzFfYzBfYzBbMF07CgkJYiA9IHViaWFzX1MxX2MwX2MwWzBdOwoJfQoJZWxzZSAKCXsKCQlzID0gdXNjYWxlX1MxX2MwX2MwWzFdOwoJCWIgPSB1Ymlhc19TMV9jMF9jMFsxXTsKCX0KCXJldHVybiBoYWxmNChoYWxmNChmbG9hdCh0KSAqIHMgKyBiKSk7Cn0KaGFsZjQgTGluZWFyTGF5b3V0X1MxX2MwX2MxX2MwKGhhbGY0IF9pbnB1dCkgCnsKCWhhbGY0IF90bXBfMl9pbkNvbG9yID0gX2lucHV0OwoJZmxvYXQyIF90bXBfM19jb29yZHMgPSB2VHJhbnNmb3JtZWRDb29yZHNfNV9TMDsKCXJldHVybiBoYWxmNChoYWxmNChoYWxmKF90bXBfM19jb29yZHMueCkgKyA5Ljk5OTk5OTc0NzM3ODc1MTZlLTA2LCAxLjAsIDAuMCwgMC4wKSk7Cn0KaGFsZjQgTWF0cml4RWZmZWN0X1MxX2MwX2MxKGhhbGY0IF9pbnB1dCkgCnsKCXJldHVybiBMaW5lYXJMYXlvdXRfUzFfYzBfYzFfYzAoX2lucHV0KTsKfQpoYWxmNCBDbGFtcGVkR3JhZGllbnRfUzFfYzAoaGFsZjQgX2lucHV0KSAKewoJaGFsZjQgX3RtcF80X2luQ29sb3IgPSBfaW5wdXQ7CgloYWxmNCB0ID0gTWF0cml4RWZmZWN0X1MxX2MwX2MxKF90bXBfNF9pbkNvbG9yKTsKCWhhbGY0IG91dENvbG9yOwoJaWYgKCFib29sKGludCgxKSkgJiYgdC55IDwgMC4wKSAKCXsKCQlvdXRDb2xvciA9IGhhbGY0KDAuMCk7Cgl9CgllbHNlIGlmICh0LnggPCAwLjApIAoJewoJCW91dENvbG9yID0gdWxlZnRCb3JkZXJDb2xvcl9TMV9jMDsKCX0KCWVsc2UgaWYgKHQueCA+IDEuMCkgCgl7CgkJb3V0Q29sb3IgPSB1cmlnaHRCb3JkZXJDb2xvcl9TMV9jMDsKCX0KCWVsc2UgCgl7CgkJb3V0Q29sb3IgPSBEdWFsSW50ZXJ2YWxDb2xvcml6ZXJfUzFfYzBfYzAoX3RtcF80X2luQ29sb3IsIGZsb2F0MihoYWxmMih0LngsIDAuMCkpKTsKCX0KCWlmIChib29sKGludCgwKSkpIAoJewoJCW91dENvbG9yLnh5eiAqPSBvdXRDb2xvci53OwoJfQoJcmV0dXJuIGhhbGY0KG91dENvbG9yKTsKfQpoYWxmNCBEaXNhYmxlQ292ZXJhZ2VBc0FscGhhX1MxKGhhbGY0IF9pbnB1dCkgCnsKCV9pbnB1dCA9IENsYW1wZWRHcmFkaWVudF9TMV9jMChfaW5wdXQpOwoJaGFsZjQgX3RtcF81X2luQ29sb3IgPSBfaW5wdXQ7CglyZXR1cm4gaGFsZjQoX2lucHV0KTsKfQp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmNvbG9yX1MwOwoJZmxvYXQgY292ZXJhZ2UgPSB2Y292ZXJhZ2VfUzA7CgloYWxmNCBvdXRwdXRDb3ZlcmFnZV9TMCA9IGhhbGY0KGhhbGYoY292ZXJhZ2UpKTsKCWhhbGY0IG91dHB1dF9TMTsKCW91dHB1dF9TMSA9IERpc2FibGVDb3ZlcmFnZUFzQWxwaGFfUzEob3V0cHV0Q29sb3JfUzApOwoJewoJCS8vIFhmZXIgUHJvY2Vzc29yOiBQb3J0ZXIgRHVmZgoJCXNrX0ZyYWdDb2xvciA9IG91dHB1dF9TMSAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAABAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGwAAAABAAAAAAAAAAQAAAAdAAAAAAAAAAEAAAAcAAAACAAAAAEAAAAJAAAADAAAAAEAAAAdAAAAEAAAAAEAAAAYAAAAAAAAAFAAAAABAAAAAQAAAAUAAAAAAAAAAQAAAAUAAAAAAAAAAQAAAAAAAAA=", "DAQAAAAAAABGAABAYAAQAIHCAIAYAQUBAEAAAAAAEAAAAAAAAAAAAAB2AAAAAAACAAAAAEBSAAAAAUAAAAAACAAAAD6QAAAAAAAAAAAAAAAABQYBAAAAAAAAAA": "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", "HUJAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAQAAAAAAQQGAAZAABQAAAAAAAACAAAAADYCAAIAAAAAWBRAAAABAAAAANAEIQCAAAAAAAAAAAAAUABQAAAAEAAAAAAAEEBQAAAAKAAAAAABAAAAAAAAAAAAAAAAAAQAAAAAYMAQAAQAAAAAA": "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", "FAAQMYAAMAAAEADAAABAEYAAAICIAB5AABQAAAQAEDAACATAAABAGYAAAICSBYQCA4AAAAAAAA5AAAAAAABAAAAACAZAAAAAKAAAAAABAAAAB7IAAAAAAAAAAAAAAAAAYMAQAAAAAAAAA": "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", "HUQAAAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAAKAAYAAAACAAAAAAACCAYAABIAAAAAAEAAAAAAAAAAAAAAAAAAAAAAADBQCAAAAAAAAAA": "CAAAAExTS1NMAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDApIGZsYXQgb3V0IGhhbGY0IHZjb2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFByaW1pdGl2ZSBQcm9jZXNzb3IgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgl2Y29sb3JfUzAgPSBjb2xvcjsKCXNrX1Bvc2l0aW9uID0gcG9zaXRpb24ueHkwMTsKfQoAAAAAswEAAGxheW91dCAoYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMCwgaW5kZXggPSAwKSBvdXQgaGFsZjQgc2tfRnJhZ0NvbG9yOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmNvbG9yX1MwOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABUAAAAAQAAAAAAAAACAAAAHQAAAAAAAAABAAAACQAAAAgAAAABAAAADAAAAAAAAABQAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAAAAAAA", "HUQAAAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAABAAIAAAAAAIIDAAAEMAAAAAAQAAAA7UAAAAABAAAAAAAAAAAAEAAAAIAAAAAA": "CAAAAExTS1NMAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDApIGZsYXQgb3V0IGhhbGY0IHZjb2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFByaW1pdGl2ZSBQcm9jZXNzb3IgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgl2Y29sb3JfUzAgPSBjb2xvcjsKCXNrX1Bvc2l0aW9uID0gcG9zaXRpb24ueHkwMTsKfQoAAAAAgAEAAGxheW91dCAoYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMCwgaW5kZXggPSAwKSBvdXQgaGFsZjQgc2tfRnJhZ0NvbG9yOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmNvbG9yX1MwOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogRGlzYWJsZSBDb2xvcgoJfQp9CgAAAAABAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwAAAABAAAAAAAAAAIAAAAdAAAAAAAAAAEAAAAJAAAACAAAAAEAAAAMAAAAAAAAAEYAAAAAAAAAAAAAAP0AAAA=", "FAAQMYAAMAAAEADAAABAEYAAAICIAB5AABQAAAQAEDAACATAAABAGYAAAICSBYQCA4AAAAAAEABWAAAAAAAAAIAAAAAPAIAAAAAQAAAAFMDAAEAAAAAEAQ2AIQAAAAAAAAAAAAAAHIAAAAAAAIAAAAAQGIAAAAAAKAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAYMAQAAAAAAAAA": "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", "HTQAAGAABBYAAAEIXBAAAGEAMAAAAAAAAAAAAAAAQAHAAAAAQAAAAAAAQQGAAAAAKAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAYMAQAAAAAAAAA": "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", "HUIAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAAAAAALUAQBAAAQAAAAGQCBAMQACAAAAAAAACQAGAAAAAQAAAAAAAQQGAAAABIAAAAAAEAAAAAAAAAAAAAAAAACAAAAAABQAAACAAAAAAA": "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", "CIAAAAAAQAARQAAYQAAAAGFYQAABRAAAAEEAAAAAAARAEAEABYAAAAEAAAAAAAEEBQAAAACQAAAAAAIAAAAAAAAAAAAAAAAAEAAAAAGDAEAAEAAAAAAA": "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", "HUIAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAAAAAALUAQBAAAQAAAAGQCBAMQACAAAAAAAACQAGAAAAAQAAAAAAAQQGAAAABIAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAABQAAACAAAAAAA": "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", "FAAQMYAAMAAAEADAAABAEYAAAICIAB5AABQAAAQAEDAACATAAABAGYAAAICSBYQCA4AAAAAAEABWAAAAAAAAAIAAAAAPAIAAAAAQAAAAFMDAAEAAAAAEAQ2AIQAAAAAAAAAAAAAAHIAAAAAAAIAAAAAQGIAAAAAAKAAAAAABAAAAB7IAAAAAAAAAAAAAAAAAYMAQAAAAAAAAA": "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", "HVIAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAAAAAAAAFQBUW3IF4MAAAAAAEYT7ZOFIQAAAADAAAAABQAAAAAYFVV5UBASAAAAALBCH47UAAAAAAAEAAAABSMJ5MVD4LAIAABQAAAAAIAAIAAAAKW7KFQNAAEAAAQBC6J3YHAAAAAEAAAAAJBQOVX2RECIABAAAAAAAAACWAQLBCP47QAAAAIAAAAAEABTASF4XXAMAAAAAAAAAABLAAABAAAAAAAAGQCAAAAAAAAAAAAAAOQAAAAAAAQAAAABAMQAAAKAAAAAABAAAAAAAAAAAAAAAAAAQAAAAAYMAQAAQAAAAAA": "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", "HUJAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAQAAAAAAQQGAAZAABQAAAAAAAACAAAAADYCAAIAAAAAWBRAAAABAAAAANAEIQCAAAAAAAAAAAAAUABQAAAAEAAAAAAAEEBQAAAAKAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAYMAQAAQAAAAAA": "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", "CIAAAAAAQAARQAAYQAAAAGFYQAABRAAAAEEAAAAAAARAEAEABYAAAAEAAAAAAAEEBQAAAACQAAAAAAIAAAAP2AAAAAAAAAAAAAAAAAGDAEAAEAAAAAAA": "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", "HUQACAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAAKAAYAAAACAAAAAAACCAYAABDAAAAAAEAAAAH5AAAAAAAAAAAAAAAAADBQCAAAAAAAAAA": "CAAAAExTS1NHAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDApIG91dCBoYWxmNCB2Y29sb3JfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmNvbG9yX1MwID0gY29sb3I7Cglza19Qb3NpdGlvbiA9IHBvc2l0aW9uLnh5MDE7Cn0KAAAAAACuAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMCwgaW5kZXggPSAwKSBvdXQgaGFsZjQgc2tfRnJhZ0NvbG9yOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmNvbG9yX1MwOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAVAAAAAEAAAAAAAAAAgAAAB0AAAAAAAAAAQAAAAkAAAAIAAAAAQAAAAwAAAAAAAAARgAAAAEAAAABAAAABQAAAAAAAAABAAAABQAAAAAAAAABAAAA/QAAAA==", "AYQA5AADQAAAOAEARAFQJAABBADIB7777777777777777777777767YAAAAAAAAAAAAOQAAAAAAAQAAAABAMQAAAABIAAAAAAEAAAAAAAAAAAAAAAAACAAAAADBQCAAAAAAAAAA": "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", "FAAQMYAAMAAAEADAAABAEYAAAICIAB5AABQAAAQAEDAACATAAABAGYAAAICSBYQCA4AAAAAAAA5AAAAAAABAAAAACAZAAAAAIYAAAAABAAAAAAAAAAAAAAAAAAAAAAAAYMAQAAAAAAAAA": "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", "HUJAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAQAAAAAAQQGACQAGAAAAAQAAAAAAAQQGAAAKAAAAAABAAAAB7IAAAAACAAAAAIAAAAAYMAQAAQAAAAAA": "CAAAAExTS1NdAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gZmxvYXQyIGxvY2FsQ29vcmQ7CmxheW91dChsb2NhdGlvbiA9IDApIG91dCBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmxvY2FsQ29vcmRfUzAgPSBsb2NhbENvb3JkOwoJc2tfUG9zaXRpb24gPSBwb3NpdGlvbi54eTAxOwp9CgAAAAAAAABTAgAAbGF5b3V0KGJpbmRpbmc9MCkgdW5pZm9ybSBzYW1wbGVyMkQgdVRleHR1cmVTYW1wbGVyXzBfUzA7CmxheW91dCAoYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CmxheW91dChsb2NhdGlvbiA9IDAsIGluZGV4ID0gMCkgb3V0IGhhbGY0IHNrX0ZyYWdDb2xvcjsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzAgPSBoYWxmNCgxKTsKCWZsb2F0MiB0ZXhDb29yZDsKCXRleENvb3JkID0gdmxvY2FsQ29vcmRfUzA7CglvdXRwdXRDb2xvcl9TMCA9ICgoc2FtcGxlKHVUZXh0dXJlU2FtcGxlcl8wX1MwLCB0ZXhDb29yZCkgKiBoYWxmNCgxKSkpOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABUAAAAAQAAAAAAAAACAAAAHQAAAAAAAAABAAAAHQAAAAgAAAABAAAAEAAAAAAAAABQAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAD9AAAA", "GEMAAAYAAEHAAAARC4EAAAQWBQAAAAAAAAAQAAAAIBCAAAGQAEAAAAAQAAAABAEQAEAAAACQAAAAAAIAAAAP2AAAAAAAAAAAEAAAAAGDAEAAAAAAAAAA": "CAAAAExTS1P2AQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBpblBvc2l0aW9uOwpsYXlvdXQobG9jYXRpb24gPSAxKSBpbiBoYWxmNCBpbkNvbG9yOwpsYXlvdXQobG9jYXRpb24gPSAyKSBpbiBoYWxmMyBpblNoYWRvd1BhcmFtczsKbGF5b3V0KGxvY2F0aW9uID0gMCkgb3V0IGhhbGYzIHZpblNoYWRvd1BhcmFtc19TMDsKbGF5b3V0KGxvY2F0aW9uID0gMSkgb3V0IGhhbGY0IHZpbkNvbG9yX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gUHJpbWl0aXZlIFByb2Nlc3NvciBSUmVjdFNoYWRvdwoJdmluU2hhZG93UGFyYW1zX1MwID0gaW5TaGFkb3dQYXJhbXM7Cgl2aW5Db2xvcl9TMCA9IGluQ29sb3I7CglmbG9hdDIgX3RtcF8wX2luUG9zaXRpb24gPSBpblBvc2l0aW9uOwoJc2tfUG9zaXRpb24gPSBfdG1wXzBfaW5Qb3NpdGlvbi54eTAxOwp9CgAAAAAAANQCAABsYXlvdXQoYmluZGluZz0wKSB1bmlmb3JtIHNhbXBsZXIyRCB1VGV4dHVyZVNhbXBsZXJfMF9TMDsKbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGhhbGYzIHZpblNoYWRvd1BhcmFtc19TMDsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgdmluQ29sb3JfUzA7CmxheW91dChsb2NhdGlvbiA9IDAsIGluZGV4ID0gMCkgb3V0IGhhbGY0IHNrX0ZyYWdDb2xvcjsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFJSZWN0U2hhZG93CgloYWxmMyBzaGFkb3dQYXJhbXM7CglzaGFkb3dQYXJhbXMgPSB2aW5TaGFkb3dQYXJhbXNfUzA7CgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmluQ29sb3JfUzA7CgloYWxmIGQgPSBsZW5ndGgoc2hhZG93UGFyYW1zLnh5KTsKCWZsb2F0MiB1diA9IGZsb2F0MihzaGFkb3dQYXJhbXMueiAqICgxLjAgLSBkKSwgMC41KTsKCWhhbGYgZmFjdG9yID0gc2FtcGxlKHVUZXh0dXJlU2FtcGxlcl8wX1MwLCB1dikuMDAwci5hOwoJaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNChmYWN0b3IpOwoJewoJCS8vIFhmZXIgUHJvY2Vzc29yOiBQb3J0ZXIgRHVmZgoJCXNrX0ZyYWdDb2xvciA9IG91dHB1dENvbG9yX1MwICogb3V0cHV0Q292ZXJhZ2VfUzA7Cgl9Cn0KAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYAAAAAEAAAAAAAAAAwAAAB0AAAAAAAAAAQAAAAkAAAAIAAAAAQAAAB4AAAAMAAAAAQAAABgAAAAAAAAAUAAAAAEAAAABAAAABQAAAAAAAAABAAAABQAAAAAAAAABAAAA/QAAAA==", "HVIAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAAAAAAAAHIAAKAAAAARBNQWUSCAAAADWFZ4ITAMAAAAAAAAEAAAAAZCEKRJJQGUCAAAAAAAAAAAAZ3G46EJYGAAAAAAAABAAAAACYCEAIAAAAAAABUAQIDEAAAAAAAAAABUABAAAAAEAAAAAIBEABAAAFAAAAAAAQAAAAAAAAAAAAAAAAAAAAAAAMGAIAAIAAAAAA": "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", "HVJAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAABAAAAAABBAMADSAB4QAAAAAEAAAAAAHIAAAAAAAIAAAAAQGIAAAAAAFAAAAAAAQAAAAAAAAAAAAAAAAAAAAAAAMGAIAAIAAAAAA": "CAAAAExTS1MbAgAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKCWxheW91dChvZmZzZXQ9MTYpIGZsb2F0NCB1aW5uZXJSZWN0X1MxOwoJbGF5b3V0KG9mZnNldD0zMikgaGFsZjIgdXJhZGl1c1BsdXNIYWxmX1MxOwp9CjsKbGF5b3V0KGxvY2F0aW9uID0gMCkgaW4gZmxvYXQyIHBvc2l0aW9uOwpsYXlvdXQobG9jYXRpb24gPSAxKSBpbiBoYWxmNCBjb2xvcjsKbGF5b3V0KGxvY2F0aW9uID0gMikgaW4gZmxvYXQyIGxvY2FsQ29vcmQ7CmxheW91dChsb2NhdGlvbiA9IDApIGZsYXQgb3V0IGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMSkgb3V0IGZsb2F0MiB2bG9jYWxDb29yZF9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFByaW1pdGl2ZSBQcm9jZXNzb3IgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgl2Y29sb3JfUzAgPSBjb2xvcjsKCXZsb2NhbENvb3JkX1MwID0gbG9jYWxDb29yZDsKCXNrX1Bvc2l0aW9uID0gcG9zaXRpb24ueHkwMTsKfQoAAAAAAD4EAABsYXlvdXQoYmluZGluZz0wKSB1bmlmb3JtIHNhbXBsZXIyRCB1VGV4dHVyZVNhbXBsZXJfMF9TMDsKbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKCWxheW91dChvZmZzZXQ9MTYpIGZsb2F0NCB1aW5uZXJSZWN0X1MxOwoJbGF5b3V0KG9mZnNldD0zMikgaGFsZjIgdXJhZGl1c1BsdXNIYWxmX1MxOwp9CjsKbGF5b3V0KGxvY2F0aW9uID0gMCkgZmxhdCBpbiBoYWxmNCB2Y29sb3JfUzA7CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGZsb2F0MiB2bG9jYWxDb29yZF9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMCwgaW5kZXggPSAwKSBvdXQgaGFsZjQgc2tfRnJhZ0NvbG9yOwpoYWxmNCBDaXJjdWxhclJSZWN0X1MxKGhhbGY0IF9pbnB1dCkgCnsKCWZsb2F0MiBkeHkwID0gdWlubmVyUmVjdF9TMS5MVCAtIHNrX0ZyYWdDb29yZC54eTsKCWZsb2F0MiBkeHkxID0gc2tfRnJhZ0Nvb3JkLnh5IC0gdWlubmVyUmVjdF9TMS5SQjsKCWZsb2F0MiBkeHkgPSBtYXgobWF4KGR4eTAsIGR4eTEpLCAwLjApOwoJaGFsZiBhbHBoYSA9IGhhbGYoc2F0dXJhdGUodXJhZGl1c1BsdXNIYWxmX1MxLnggLSBsZW5ndGgoZHh5KSkpOwoJcmV0dXJuIF9pbnB1dCAqIGFscGhhOwp9CnZvaWQgbWFpbigpIAp7CgkvLyBTdGFnZSAwLCBRdWFkUGVyRWRnZUFBR2VvbWV0cnlQcm9jZXNzb3IKCWhhbGY0IG91dHB1dENvbG9yX1MwOwoJb3V0cHV0Q29sb3JfUzAgPSB2Y29sb3JfUzA7CglmbG9hdDIgdGV4Q29vcmQ7Cgl0ZXhDb29yZCA9IHZsb2NhbENvb3JkX1MwOwoJb3V0cHV0Q29sb3JfUzAgPSAoKHNhbXBsZSh1VGV4dHVyZVNhbXBsZXJfMF9TMCwgdGV4Q29vcmQpICogb3V0cHV0Q29sb3JfUzApKTsKCWNvbnN0IGhhbGY0IG91dHB1dENvdmVyYWdlX1MwID0gaGFsZjQoMSk7CgloYWxmNCBvdXRwdXRfUzE7CglvdXRwdXRfUzEgPSBDaXJjdWxhclJSZWN0X1MxKG91dHB1dENvdmVyYWdlX1MwKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dF9TMTsKCX0KfQoAAAEAAAABAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGAAAAABAAAAAAAAAAMAAAAdAAAAAAAAAAEAAAAJAAAACAAAAAEAAAAdAAAADAAAAAEAAAAUAAAAAAAAAFAAAAABAAAAAQAAAAUAAAAAAAAAAQAAAAUAAAAAAAAAAQAAAAAAAAA=", "HUJAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAQAAAAAAQQGAARAGQWMHGBRIAAAAABQAAAAAAAAAAHIAAAAAAAIAAAAAQGIABIAAAAAAEAAAAH5AAAAAAAAAAAAAAAAADBQCAACAAAAAAA": "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", "HUIAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAAAAAALUAQAAEAQAAAAGQCBAMQAAAIAAAAAACQAGAAAAAQAAAAAAAQQGAAAABDAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAABQAAACAAAAAAA": "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", "HUQAAAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAACEA2CZQ4YGFAAAAAAGAAAAAAAAAAA5AAAAAAABAAAAACAZAAEMAAAAAAQAAAAAAAAAAAAAAAAAAAAAAAMGAIAAIAAAAAA": "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", "HUJAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAQAAAAAAQQGABZAA6IAAAAACAAAAAADUAAAAAAAEAAAAAIDEAAAAAAEMAAAAAAQAAAAAAAAAAAAAAAAAAAAAAAMGAIAAIAAAAAA": "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", "AZAQ5AADQAAAOAEARAFQJAABBADIB7777777777777777777777767YAAAAAAAAAACAMVQGOCU7TCAAAACAOSKJ7FYFAAAAAAEAAAAAGAAAABRHUYG4YGAAAAAAIXSG7B4AAAAAQAAAAAIBDU2T7ZOBIAAAAABAAAAABQAAAAAINGB7HBYBAAAAAFQRH6PYAAAAIAAAAACAAZRXUYG4YGAAAAAAAAAAAWARLBCH47UAAAAAAAEAAAABSASF4RXYPAAAAAAAAAAAKAAYAAAACAAAAAAACCAYAKAAAAAABAAAAB7IAAAAAAAAAAAAAAAAAYMAQAAAAAAAAA": "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", "CIAAAAAAQAARQAAYQAAAAGFYQAABRAAAAEEAAAAAAARAEAEABYAAAAEAAAAAAAEEBQAAAACQAAAAAAIAAAAAAAAAAAAAAAAAAAAAAAGDAEAAEAAAAAAA": "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", "GEMAAAYAAEHAAAARC4EAAAQWBQAAAAAAAAAQAAAAIBCAAAGQAEAAAAAQAAAABAEQAEAAAACQAAAAAAIAAAAP2AAAAAAAAAAAAAAAAAGDAEAAAAAAAAAA": "CAAAAExTS1P2AQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBpblBvc2l0aW9uOwpsYXlvdXQobG9jYXRpb24gPSAxKSBpbiBoYWxmNCBpbkNvbG9yOwpsYXlvdXQobG9jYXRpb24gPSAyKSBpbiBoYWxmMyBpblNoYWRvd1BhcmFtczsKbGF5b3V0KGxvY2F0aW9uID0gMCkgb3V0IGhhbGYzIHZpblNoYWRvd1BhcmFtc19TMDsKbGF5b3V0KGxvY2F0aW9uID0gMSkgb3V0IGhhbGY0IHZpbkNvbG9yX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gUHJpbWl0aXZlIFByb2Nlc3NvciBSUmVjdFNoYWRvdwoJdmluU2hhZG93UGFyYW1zX1MwID0gaW5TaGFkb3dQYXJhbXM7Cgl2aW5Db2xvcl9TMCA9IGluQ29sb3I7CglmbG9hdDIgX3RtcF8wX2luUG9zaXRpb24gPSBpblBvc2l0aW9uOwoJc2tfUG9zaXRpb24gPSBfdG1wXzBfaW5Qb3NpdGlvbi54eTAxOwp9CgAAAAAAANQCAABsYXlvdXQoYmluZGluZz0wKSB1bmlmb3JtIHNhbXBsZXIyRCB1VGV4dHVyZVNhbXBsZXJfMF9TMDsKbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGhhbGYzIHZpblNoYWRvd1BhcmFtc19TMDsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgdmluQ29sb3JfUzA7CmxheW91dChsb2NhdGlvbiA9IDAsIGluZGV4ID0gMCkgb3V0IGhhbGY0IHNrX0ZyYWdDb2xvcjsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFJSZWN0U2hhZG93CgloYWxmMyBzaGFkb3dQYXJhbXM7CglzaGFkb3dQYXJhbXMgPSB2aW5TaGFkb3dQYXJhbXNfUzA7CgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmluQ29sb3JfUzA7CgloYWxmIGQgPSBsZW5ndGgoc2hhZG93UGFyYW1zLnh5KTsKCWZsb2F0MiB1diA9IGZsb2F0MihzaGFkb3dQYXJhbXMueiAqICgxLjAgLSBkKSwgMC41KTsKCWhhbGYgZmFjdG9yID0gc2FtcGxlKHVUZXh0dXJlU2FtcGxlcl8wX1MwLCB1dikuMDAwci5hOwoJaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNChmYWN0b3IpOwoJewoJCS8vIFhmZXIgUHJvY2Vzc29yOiBQb3J0ZXIgRHVmZgoJCXNrX0ZyYWdDb2xvciA9IG91dHB1dENvbG9yX1MwICogb3V0cHV0Q292ZXJhZ2VfUzA7Cgl9Cn0KAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYAAAAAEAAAAAAAAAAwAAAB0AAAAAAAAAAQAAAAkAAAAIAAAAAQAAAB4AAAAMAAAAAQAAABgAAAAAAAAAUAAAAAEAAAABAAAABQAAAAAAAAABAAAABQAAAAAAAAABAAAA/QAAAA==", "HUQAAAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAAKAAYAAAACAAAAAAACCAYAABIAAAAAAEAAAAAAAAAAAAAAAAAAAAAAADBQCAACAAAAAAA": "CAAAAExTS1NMAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDApIGZsYXQgb3V0IGhhbGY0IHZjb2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFByaW1pdGl2ZSBQcm9jZXNzb3IgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgl2Y29sb3JfUzAgPSBjb2xvcjsKCXNrX1Bvc2l0aW9uID0gcG9zaXRpb24ueHkwMTsKfQoAAAAAswEAAGxheW91dCAoYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMCwgaW5kZXggPSAwKSBvdXQgaGFsZjQgc2tfRnJhZ0NvbG9yOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmNvbG9yX1MwOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABUAAAAAQAAAAAAAAACAAAAHQAAAAAAAAABAAAACQAAAAgAAAABAAAADAAAAAAAAABQAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAAAAAAA", "HUIAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAAAAAALUAQIAAAAAAABAEAAAABJQQAAAAAACAIAAAAAWCBAAAIBAAAAANAECAZAAAAQAAAAAAFAAMAAAABAAAAAAABBANDAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAABQAAACAAAAAAA": "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", "HUJAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAQAAAAAAQQGAAZAABQAAAAAAAACAAAAADYCAAIAAAAAWBRAAAABAAAAANAEIQCAAAAAAAAAAAAAUABQAAAAEAAAAAAAEEBQAAAAIYAAAAABAAAAB7IAAAAAAAAAAAQAAAAAYMAQAAQAAAAAA": "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", "HUQACAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAAKAAYAAAACAAAAAAACCAYAABDAAAAAAEAAAAAAAAAAAAAAAAAAAAAAADBQCAAAAAAAAAA": "CAAAAExTS1NHAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDApIG91dCBoYWxmNCB2Y29sb3JfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmNvbG9yX1MwID0gY29sb3I7Cglza19Qb3NpdGlvbiA9IHBvc2l0aW9uLnh5MDE7Cn0KAAAAAACuAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMCwgaW5kZXggPSAwKSBvdXQgaGFsZjQgc2tfRnJhZ0NvbG9yOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmNvbG9yX1MwOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAVAAAAAEAAAAAAAAAAgAAAB0AAAAAAAAAAQAAAAkAAAAIAAAAAQAAAAwAAAAAAAAARgAAAAEAAAABAAAABQAAAAAAAAABAAAABQAAAAAAAAABAAAAAAAAAA==", "AYQQ5AADQAAAOAEARAFQJAABBADIB7777777777777777777777767YAAAAAAAAAAAAOQAAAAAAAQAAAABAMQAAAABIAAAAAAEAAAAAAAAAAAAAAAAAAAAAAADBQCAAAAAAAAAA": "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", "CIAQAAAAIABCQAAYQAAAAGEAQAAAQ4AAAEMLQQABDCAMAAIIAAAAAABCAIAKQDHMLTYRGAYAAAAJRHXS4ORAAAAAGAAAAADAAAAAAQDMEXABWJAAAAAGAEPZ7MAQAAAAAIAAAADEYT2JIHYXAUAABAABAAAAAAYAAAAGEKYB3YQACAAAACF4RXYPAAAAAIAAAAACBA5RSUAG7EAAAAAAAAAAABMBCWCE7Z7AAAAAQAAAAAAADGBELZHPA4AAAAAAAAAABUABAAAAAEAAAAAIBEABABDAAAAAAEAAAAAAAAAAAAAAAAAAAAAAADBQCAACAAAAAAA": "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", "HUQACAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAADEAAGAAAAAAAAAIAAAAAPAIABAAAAACYGEAAAAEAAAABUARCAIAAAAAAAAAAAACQAGAAAAAQAAAAAAAQQGAAAABIAAAAAAEAAAAH5AAAAAAAAAAAAAAAAADBQCAAAAAAAAAA": "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", "HUJAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAQAAAAAAQQGABZAA6IAAAAACAAAAAADUAAAAAAAEAAAAAIDEAAAAAAFAAAAAAAQAAAA7UAAAAAAAAAAAAAAAAAMGAIAAIAAAAAA": "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", "HUIAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAAAAAALUAQMAAAAAAABAEAAAABJYQAAAAAACAIAAAAAWCBAAAIBAAAAANAECAZAAAAQAAAAAAFAAMAAAABAAAAAAABBANIAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAABQAAACAAAAAAA": "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", "FAAQMYAAMAAAEADAAABAEYAAAICIAB5AABQAAAQAEDAACATAAABAGYAAAICSBYQCA4AAAAAAEAZIA62YSBDACAAAGAAAAAAAAAAABAAOAAAABAAAAAAABBAMABDAAAAAAEAAAAAAAAAAAAAAAAACAAAAADBQCAAAAAAAAAA": "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", "HVIAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAAAAAAAAGQBIAAABAAAAANAEAAAAAAAAAAAAAABQAADAAAAAAAAAEAAAAAHQEAAQAAAAAMDCQAAACAAAAA2AIRAEAAAAAAAAAAAAAIADQAAAAIAAAAAAAIIDFAAAAAAAQAAAAAAAAAAAAAAAAAAAAAAAMGAIAAIAAAAAA": "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", "HUIAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAAAAAALUAQBAEAQAAAAGQCBAMQACAIAAAAAACQAGAAAAAQAAAAAAAQQGAAAABIAAAAAAEAAAAAAAAAAAAAAAAACAAAAAABQAAACAAAAAAA": "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", "B2ABSAAABQAAIAABBYAAB7777777777774ABICAAAAAAAAAAAAAABUABAAAAAEAAAAAIBEABAAAAARQAAAAACAAAAAAAAAAAAAAAAAAAAAAABQYBAAAAAAAAAA": "CAAAAExTS1P7AQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKCWxheW91dChvZmZzZXQ9MTYpIGhhbGY0IHVDb2xvcl9TMDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBpblBvc2l0aW9uOwpsYXlvdXQobG9jYXRpb24gPSAxKSBpbiBoYWxmIGluQ292ZXJhZ2U7CmxheW91dChsb2NhdGlvbiA9IDApIG91dCBoYWxmNCB2Y29sb3JfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIERlZmF1bHRHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgY29sb3IgPSB1Q29sb3JfUzA7Cgljb2xvciA9IGNvbG9yICogaW5Db3ZlcmFnZTsKCXZjb2xvcl9TMCA9IGNvbG9yOwoJZmxvYXQyIF90bXBfMV9pblBvc2l0aW9uID0gaW5Qb3NpdGlvbjsKCWZsb2F0MiBfdG1wXzNfaW5Qb3NpdGlvbiA9IGluUG9zaXRpb247Cglza19Qb3NpdGlvbiA9IF90bXBfMV9pblBvc2l0aW9uLnh5MDE7Cn0KAAAAAADMAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKCWxheW91dChvZmZzZXQ9MTYpIGhhbGY0IHVDb2xvcl9TMDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMCwgaW5kZXggPSAwKSBvdXQgaGFsZjQgc2tfRnJhZ0NvbG9yOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgRGVmYXVsdEdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmNvbG9yX1MwOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAABAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFQAAAABAAAAAAAAAAIAAAAdAAAAAAAAAAEAAAAcAAAACAAAAAEAAAAMAAAAAAAAAEYAAAABAAAAAQAAAAUAAAAAAAAAAQAAAAUAAAAAAAAAAQAAAAAAAAA=", "HUIAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAAAAAALUAQLAAAAAAABAEAAAABJWQAAAAAACAIAAAAAWCBAAAIBAAAAANAECAZAAAAQAAAAAAFAAMAAAABAAAAAAABBANDAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAABQAAACAAAAAAA": "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", "HUQAAAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAAKAAYAAAACAAAAAAACCAYAABDAAAAAAEAAAAAAAAAAAAAAAAAAAAAAADBQCAACAAAAAAA": "CAAAAExTS1NMAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDApIGZsYXQgb3V0IGhhbGY0IHZjb2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFByaW1pdGl2ZSBQcm9jZXNzb3IgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgl2Y29sb3JfUzAgPSBjb2xvcjsKCXNrX1Bvc2l0aW9uID0gcG9zaXRpb24ueHkwMTsKfQoAAAAAswEAAGxheW91dCAoYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMCwgaW5kZXggPSAwKSBvdXQgaGFsZjQgc2tfRnJhZ0NvbG9yOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmNvbG9yX1MwOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABUAAAAAQAAAAAAAAACAAAAHQAAAAAAAAABAAAACQAAAAgAAAABAAAADAAAAAAAAABGAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAAAAAAA", "AYQA5AADQAAAOAEARAFQJAABBADIB7777777777777777777777767YAAAAAAAAAAAAMXQGOCU7TCAAAACAOSKJ7FYFAAAAAAMAAAAAGAAAAAKE666BBAAIAAAABNEN7D4AAAABAAAAAAQCGJRH7S4KRAAAAAGAAAAADAAAAABAPDPAXQQEAAAAAWCEPZ7IAAAAAAAQAAAADFKDY3YFUEBAAAAAAAAAAQAKYCRPE54DQAAAABAAAAAEQEFMEJ7T6AAAAAAAAAAAIAA6YAMAAACAAAAAABUABAAAAAEAAAAAIBEABAAAFAAAAAAAQAAAAAAAAAAAAAAAAAAAAAAAMGAIAAAAAAAAA": "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", "HVIAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAAAAAAAAFIBTWV34ISAIAAAAEYT7ZOFIQAAAADAAAAABQAAAAAIFGB7HB2BAAAAAKOJIDKEAAAAEAAAAAAAAZGE66LR2FAEAAAYAAAAAMAAAAACAJQPRYO4IAAAAATSLAYRABAAAAABAAAAAGIMFGB7HB2BAAAAAAAAAAQAKXCWRSCACQAAAABAAAAAEQEGTSKA2RAAAAAAAAAAAAAHIAAAAAAAIAAAAAQGIAIYAAAAABAAAAAAAAAAAAAAAAAAAAAAAAYMAQAAQAAAAAA": "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", "AZAQ5AADQAAAOAEARAFQJAABBADIB7777777777777777777777767YAAAAAAAAAACAMVQGOCU7TCAAAACAOSKJ7FYFAAAAAAEAAAAAGAAAABRHUYG4YGAAAAAAIXSG7B4AAAAAQAAAAAIBDU2T7ZOBIAAAAABAAAAABQAAAAAINGB7HBYBAAAAAFQRH6PYAAAAIAAAAACAAZRXUYG4YGAAAAAAAAAAAWARLBCH47UAAAAAAAEAAAABSASF4RXYPAAAAAAAAAAAKAAYAAAACAAAAAAACCAYAIYAAAAABAAAAAAAAAAAAAAAAAAAAAAAAYMAQAAAAAAAAA": "CAAAAExTS1PlAwAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKCWxheW91dChvZmZzZXQ9MTYpIGZsb2F0M3gzIHVsb2NhbE1hdHJpeF9TMDsKCWxheW91dChvZmZzZXQ9NjQpIGhhbGY0IHVzdGFydF9TMV9jMF9jMDsKCWxheW91dChvZmZzZXQ9NzIpIGhhbGY0IHVlbmRfUzFfYzBfYzA7CglsYXlvdXQob2Zmc2V0PTgwKSBmbG9hdDN4MyB1bWF0cml4X1MxX2MwX2MxOwoJbGF5b3V0KG9mZnNldD0xMjgpIGhhbGY0IHVsZWZ0Qm9yZGVyQ29sb3JfUzFfYzA7CglsYXlvdXQob2Zmc2V0PTEzNikgaGFsZjQgdXJpZ2h0Qm9yZGVyQ29sb3JfUzFfYzA7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgaW5Qb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgaW5Db2xvcjsKbGF5b3V0KGxvY2F0aW9uID0gMikgaW4gZmxvYXQ0IGluQ2lyY2xlRWRnZTsKbGF5b3V0KGxvY2F0aW9uID0gMCkgb3V0IGZsb2F0NCB2aW5DaXJjbGVFZGdlX1MwOwpsYXlvdXQobG9jYXRpb24gPSAxKSBvdXQgaGFsZjQgdmluQ29sb3JfUzA7CmxheW91dChsb2NhdGlvbiA9IDIpIG91dCBmbG9hdDIgdlRyYW5zZm9ybWVkQ29vcmRzXzVfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIENpcmNsZUdlb21ldHJ5UHJvY2Vzc29yCgl2aW5DaXJjbGVFZGdlX1MwID0gaW5DaXJjbGVFZGdlOwoJdmluQ29sb3JfUzAgPSBpbkNvbG9yOwoJZmxvYXQyIF90bXBfMF9pblBvc2l0aW9uID0gaW5Qb3NpdGlvbjsKCWZsb2F0MiBfdG1wXzFfaW5Qb3NpdGlvbiA9IGZsb2F0M3gyKHVsb2NhbE1hdHJpeF9TMCkgKiBpblBvc2l0aW9uLnh5MTsKCXNrX1Bvc2l0aW9uID0gX3RtcF8wX2luUG9zaXRpb24ueHkwMTsKCXsKCQl2VHJhbnNmb3JtZWRDb29yZHNfNV9TMCA9IGZsb2F0M3gyKHVtYXRyaXhfUzFfYzBfYzEpICogX3RtcF8xX2luUG9zaXRpb24ueHkxOwoJfQp9CgAAAAAAAABnCQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKCWxheW91dChvZmZzZXQ9MTYpIGZsb2F0M3gzIHVsb2NhbE1hdHJpeF9TMDsKCWxheW91dChvZmZzZXQ9NjQpIGhhbGY0IHVzdGFydF9TMV9jMF9jMDsKCWxheW91dChvZmZzZXQ9NzIpIGhhbGY0IHVlbmRfUzFfYzBfYzA7CglsYXlvdXQob2Zmc2V0PTgwKSBmbG9hdDN4MyB1bWF0cml4X1MxX2MwX2MxOwoJbGF5b3V0KG9mZnNldD0xMjgpIGhhbGY0IHVsZWZ0Qm9yZGVyQ29sb3JfUzFfYzA7CglsYXlvdXQob2Zmc2V0PTEzNikgaGFsZjQgdXJpZ2h0Qm9yZGVyQ29sb3JfUzFfYzA7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDQgdmluQ2lyY2xlRWRnZV9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgdmluQ29sb3JfUzA7CmxheW91dChsb2NhdGlvbiA9IDIpIGluIGZsb2F0MiB2VHJhbnNmb3JtZWRDb29yZHNfNV9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMCwgaW5kZXggPSAwKSBvdXQgaGFsZjQgc2tfRnJhZ0NvbG9yOwpoYWxmNCBTaW5nbGVJbnRlcnZhbENvbG9yaXplcl9TMV9jMF9jMChoYWxmNCBfaW5wdXQsIGZsb2F0MiBfY29vcmRzKSAKewoJaGFsZjQgX3RtcF8wX2luQ29sb3IgPSBfaW5wdXQ7CglmbG9hdDIgX3RtcF8xX2Nvb3JkcyA9IF9jb29yZHM7CglyZXR1cm4gaGFsZjQobWl4KHVzdGFydF9TMV9jMF9jMCwgdWVuZF9TMV9jMF9jMCwgaGFsZihfdG1wXzFfY29vcmRzLngpKSk7Cn0KaGFsZjQgTGluZWFyTGF5b3V0X1MxX2MwX2MxX2MwKGhhbGY0IF9pbnB1dCkgCnsKCWhhbGY0IF90bXBfMl9pbkNvbG9yID0gX2lucHV0OwoJZmxvYXQyIF90bXBfM19jb29yZHMgPSB2VHJhbnNmb3JtZWRDb29yZHNfNV9TMDsKCXJldHVybiBoYWxmNChoYWxmNChoYWxmKF90bXBfM19jb29yZHMueCkgKyA5Ljk5OTk5OTc0NzM3ODc1MTZlLTA2LCAxLjAsIDAuMCwgMC4wKSk7Cn0KaGFsZjQgTWF0cml4RWZmZWN0X1MxX2MwX2MxKGhhbGY0IF9pbnB1dCkgCnsKCXJldHVybiBMaW5lYXJMYXlvdXRfUzFfYzBfYzFfYzAoX2lucHV0KTsKfQpoYWxmNCBDbGFtcGVkR3JhZGllbnRfUzFfYzAoaGFsZjQgX2lucHV0KSAKewoJaGFsZjQgX3RtcF80X2luQ29sb3IgPSBfaW5wdXQ7CgloYWxmNCB0ID0gTWF0cml4RWZmZWN0X1MxX2MwX2MxKF90bXBfNF9pbkNvbG9yKTsKCWhhbGY0IG91dENvbG9yOwoJaWYgKCFib29sKGludCgxKSkgJiYgdC55IDwgMC4wKSAKCXsKCQlvdXRDb2xvciA9IGhhbGY0KDAuMCk7Cgl9CgllbHNlIGlmICh0LnggPCAwLjApIAoJewoJCW91dENvbG9yID0gdWxlZnRCb3JkZXJDb2xvcl9TMV9jMDsKCX0KCWVsc2UgaWYgKHQueCA+IDEuMCkgCgl7CgkJb3V0Q29sb3IgPSB1cmlnaHRCb3JkZXJDb2xvcl9TMV9jMDsKCX0KCWVsc2UgCgl7CgkJb3V0Q29sb3IgPSBTaW5nbGVJbnRlcnZhbENvbG9yaXplcl9TMV9jMF9jMChfdG1wXzRfaW5Db2xvciwgZmxvYXQyKGhhbGYyKHQueCwgMC4wKSkpOwoJfQoJaWYgKGJvb2woaW50KDApKSkgCgl7CgkJb3V0Q29sb3IueHl6ICo9IG91dENvbG9yLnc7Cgl9CglyZXR1cm4gaGFsZjQob3V0Q29sb3IpOwp9CmhhbGY0IERpc2FibGVDb3ZlcmFnZUFzQWxwaGFfUzEoaGFsZjQgX2lucHV0KSAKewoJX2lucHV0ID0gQ2xhbXBlZEdyYWRpZW50X1MxX2MwKF9pbnB1dCk7CgloYWxmNCBfdG1wXzVfaW5Db2xvciA9IF9pbnB1dDsKCXJldHVybiBoYWxmNChfaW5wdXQpOwp9CnZvaWQgbWFpbigpIAp7CgkvLyBTdGFnZSAwLCBDaXJjbGVHZW9tZXRyeVByb2Nlc3NvcgoJZmxvYXQ0IGNpcmNsZUVkZ2U7CgljaXJjbGVFZGdlID0gdmluQ2lyY2xlRWRnZV9TMDsKCWhhbGY0IG91dHB1dENvbG9yX1MwOwoJb3V0cHV0Q29sb3JfUzAgPSB2aW5Db2xvcl9TMDsKCWZsb2F0IGQgPSBsZW5ndGgoY2lyY2xlRWRnZS54eSk7CgloYWxmIGRpc3RhbmNlVG9PdXRlckVkZ2UgPSBoYWxmKGNpcmNsZUVkZ2UueiAqICgxLjAgLSBkKSk7CgloYWxmIGVkZ2VBbHBoYSA9IHNhdHVyYXRlKGRpc3RhbmNlVG9PdXRlckVkZ2UpOwoJaGFsZiBkaXN0YW5jZVRvSW5uZXJFZGdlID0gaGFsZihjaXJjbGVFZGdlLnogKiAoZCAtIGNpcmNsZUVkZ2UudykpOwoJaGFsZiBpbm5lckFscGhhID0gc2F0dXJhdGUoZGlzdGFuY2VUb0lubmVyRWRnZSk7CgllZGdlQWxwaGEgKj0gaW5uZXJBbHBoYTsKCWhhbGY0IG91dHB1dENvdmVyYWdlX1MwID0gaGFsZjQoZWRnZUFscGhhKTsKCWhhbGY0IG91dHB1dF9TMTsKCW91dHB1dF9TMSA9IERpc2FibGVDb3ZlcmFnZUFzQWxwaGFfUzEob3V0cHV0Q29sb3JfUzApOwoJewoJCS8vIFhmZXIgUHJvY2Vzc29yOiBQb3J0ZXIgRHVmZgoJCXNrX0ZyYWdDb2xvciA9IG91dHB1dF9TMSAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABgAAAAAQAAAAAAAAADAAAAHQAAAAAAAAABAAAACQAAAAgAAAABAAAAHwAAAAwAAAABAAAAHAAAAAAAAABGAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAAAAAAA", "JEEAAAIAAEHAAAAAAAAAAAAAAAAIQAAAAAAIBEABAAAAACQAAAAAIAAAAD6QAAAAAEAAAAAAAAAAAAQAAAAAAAAAAA": "CAAAAExTS1MXAgAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKCWxheW91dChvZmZzZXQ9MTYpIGZsb2F0NCB1YWZmaW5lTWF0cml4X1MwOwoJbGF5b3V0KG9mZnNldD0zMikgZmxvYXQyIHV0cmFuc2xhdGVfUzA7CglsYXlvdXQob2Zmc2V0PTQwKSBoYWxmNCB1Y29sb3JfUzA7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgaW5wdXRQb2ludDsKdm9pZCBtYWluKCkgCnsKCS8vIFByaW1pdGl2ZSBQcm9jZXNzb3IgdGVzc2VsbGF0ZV9TaW1wbGVUcmlhbmdsZVNoYWRlcgoJZmxvYXQyeDIgQUZGSU5FX01BVFJJWCA9IGZsb2F0MngyKHVhZmZpbmVNYXRyaXhfUzApOwoJZmxvYXQyIFRSQU5TTEFURSA9IHV0cmFuc2xhdGVfUzA7CglmbG9hdDIgbG9jYWxjb29yZCA9IGlucHV0UG9pbnQ7CglmbG9hdDIgdmVydGV4cG9zID0gQUZGSU5FX01BVFJJWCAqIGxvY2FsY29vcmQgKyBUUkFOU0xBVEU7Cglza19Qb3NpdGlvbiA9IHZlcnRleHBvcy54eTAxOwp9CgAAAAAAuwEAAGxheW91dCAoYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7CglsYXlvdXQob2Zmc2V0PTE2KSBmbG9hdDQgdWFmZmluZU1hdHJpeF9TMDsKCWxheW91dChvZmZzZXQ9MzIpIGZsb2F0MiB1dHJhbnNsYXRlX1MwOwoJbGF5b3V0KG9mZnNldD00MCkgaGFsZjQgdWNvbG9yX1MwOwp9CjsKbGF5b3V0KGxvY2F0aW9uID0gMCwgaW5kZXggPSAwKSBvdXQgaGFsZjQgc2tfRnJhZ0NvbG9yOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgdGVzc2VsbGF0ZV9TaW1wbGVUcmlhbmdsZVNoYWRlcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzAgPSB1Y29sb3JfUzA7Cgljb25zdCBoYWxmNCBvdXRwdXRDb3ZlcmFnZV9TMCA9IGhhbGY0KDEpOwoJewoJCS8vIFhmZXIgUHJvY2Vzc29yOiBEaXNhYmxlIENvbG9yCgl9Cn0KAAAAAAABAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADAAAAABAAAAAAAAAAEAAAAdAAAAAAAAAAEAAAAIAAAAAAAAAAoAAAAAAAAAAAAAAP0AAAA=", "CMRQCIAABBYAAAEIXBAAACDQMAABRAFAAAAAAAAAAAAAAAEABYAAAAEAAAAAAAEEBQAAAACQAAAAAAIAAAAP2AAAAAAAAAAAAAAAAAGDAEAAAAAAAAAA": "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", "JAAQAAAABAAACAABBYAAAKAAAMAAGEAAAABRAEAAAEHCAAAAAAAABCAAAAAABAEQAEAAAACQAAAAAAIAAAAP2AAAAAAQAAAACAAAAAACAAAAAAAAAAAA": "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", "HUIAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAAAAAALUAQBAAAQAAAAGQCBAMQACAAAAAAAACQAGAAAAAQAAAAAAAQQGAAAABDAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAABQAAACAAAAAAA": "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", "HVIAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAAAAAAAAGIBIAAABAAAAANAEAAAAAAAAAAAAAABAAOAAAABAAAAAAABBAMAAAABIAAAAAAEAAAAAAAAAAAAAAAAAAAAAAADBQCAACAAAAAAA": "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", "HVJAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAABAAAAAABBAMABCANBEYOMDCSAAAAADAAAAAAAAAAAGQAEAAAAAQAAAABAEQAFIAAAAAAEAAAAH5AAAAAAAAAAAAAAAAADBQCAACAAAAAAA": "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", "HUQAAAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAAKAAYAAAACAAAAAAACCAYAABDAAAAAAEAAAAAAAAAAAAAAAAACAAAAADBQCAACAAAAAAA": "CAAAAExTS1NMAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDApIGZsYXQgb3V0IGhhbGY0IHZjb2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFByaW1pdGl2ZSBQcm9jZXNzb3IgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgl2Y29sb3JfUzAgPSBjb2xvcjsKCXNrX1Bvc2l0aW9uID0gcG9zaXRpb24ueHkwMTsKfQoAAAAAswEAAGxheW91dCAoYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMCwgaW5kZXggPSAwKSBvdXQgaGFsZjQgc2tfRnJhZ0NvbG9yOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmNvbG9yX1MwOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABUAAAAAQAAAAAAAAACAAAAHQAAAAAAAAABAAAACQAAAAgAAAABAAAADAAAAAAAAABGAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAAAAAAA", "HUQACAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAADEAAGAAAAAAAAAIAAAAAPAIABAAAAACYGEAAAAEAAAABUARCAIAAAAAAAAAAAACQAGAAAAAQAAAAAAAQQGAAAABDAAAAAAEAAAAAAAAAAAAAAAAAAAAAAADBQCAAAAAAAAAA": "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", "HUQACAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAAKAAYAAAACAAAAAAACCAYAABDAAAAAAEAAAAAAAAAAAAAAAAACAAAAADBQCAAAAAAAAAA": "CAAAAExTS1NHAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDApIG91dCBoYWxmNCB2Y29sb3JfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmNvbG9yX1MwID0gY29sb3I7Cglza19Qb3NpdGlvbiA9IHBvc2l0aW9uLnh5MDE7Cn0KAAAAAACuAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMCwgaW5kZXggPSAwKSBvdXQgaGFsZjQgc2tfRnJhZ0NvbG9yOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmNvbG9yX1MwOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAVAAAAAEAAAAAAAAAAgAAAB0AAAAAAAAAAQAAAAkAAAAIAAAAAQAAAAwAAAAAAAAARgAAAAEAAAABAAAABQAAAAAAAAABAAAABQAAAAAAAAABAAAAAAAAAA==", "HUQACAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAADEAAGAAAAAAAAAIAAAAAPAIABAAAAACYGEAAAAEAAAABUARCAIAAAAAAAAAAAACQAGAAAAAQAAAAAAAQQGAAAABIAAAAAAEAAAAAAAAAAAAAAAAAAAAAAADBQCAAAAAAAAAA": "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", "HUJAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAQAAAAAAQQGAARAGQWMHGBRIAAAAABQAAAAAAAAAAHIAAAAAAAIAAAAAQGIABIAAAAAAEAAAAAAAAAAAAAAAAAAAAAAADBQCAACAAAAAAA": "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", "HUIAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAAAAAALUAQAAAAQAAAAGQCBAMQAAAAAAAAAACQAGAAAAAQAAAAAAAQQGAAAABDAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAABQAAACAAAAAAA": "CAAAAExTS1O5AQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKCWxheW91dChvZmZzZXQ9MTYpIGZsb2F0M3gzIHVtYXRyaXhfUzE7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGZsb2F0MiBsb2NhbENvb3JkOwpsYXlvdXQobG9jYXRpb24gPSAwKSBvdXQgZmxvYXQyIHZUcmFuc2Zvcm1lZENvb3Jkc18yX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gUHJpbWl0aXZlIFByb2Nlc3NvciBRdWFkUGVyRWRnZUFBR2VvbWV0cnlQcm9jZXNzb3IKCXNrX1Bvc2l0aW9uID0gcG9zaXRpb24ueHkwMTsKCXsKCQl2VHJhbnNmb3JtZWRDb29yZHNfMl9TMCA9IGZsb2F0M3gyKHVtYXRyaXhfUzEpICogbG9jYWxDb29yZC54eTE7Cgl9Cn0KAAAAAAAAAAIDAABsYXlvdXQoYmluZGluZz0wKSB1bmlmb3JtIHNhbXBsZXIyRCB1VGV4dHVyZVNhbXBsZXJfMF9TMTsKbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKCWxheW91dChvZmZzZXQ9MTYpIGZsb2F0M3gzIHVtYXRyaXhfUzE7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgdlRyYW5zZm9ybWVkQ29vcmRzXzJfUzA7CmxheW91dChsb2NhdGlvbiA9IDAsIGluZGV4ID0gMCkgb3V0IGhhbGY0IHNrX0ZyYWdDb2xvcjsKaGFsZjQgVGV4dHVyZUVmZmVjdF9TMV9jMChoYWxmNCBfaW5wdXQpIAp7CglyZXR1cm4gc2FtcGxlKHVUZXh0dXJlU2FtcGxlcl8wX1MxLCB2VHJhbnNmb3JtZWRDb29yZHNfMl9TMCk7Cn0KaGFsZjQgTWF0cml4RWZmZWN0X1MxKGhhbGY0IF9pbnB1dCkgCnsKCXJldHVybiBUZXh0dXJlRWZmZWN0X1MxX2MwKF9pbnB1dCk7Cn0Kdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzAgPSBoYWxmNCgxKTsKCWNvbnN0IGhhbGY0IG91dHB1dENvdmVyYWdlX1MwID0gaGFsZjQoMSk7CgloYWxmNCBvdXRwdXRfUzE7CglvdXRwdXRfUzEgPSBNYXRyaXhFZmZlY3RfUzEob3V0cHV0Q29sb3JfUzApOwoJewoJCS8vIFhmZXIgUHJvY2Vzc29yOiBQb3J0ZXIgRHVmZgoJCXNrX0ZyYWdDb2xvciA9IG91dHB1dF9TMSAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPAAAAAEAAAAAAAAAAgAAAB0AAAAAAAAAAQAAAB0AAAAIAAAAAQAAABAAAAAAAAAARgAAAAAAAAABAAAAAAAAAA==", "B2ABSAAABQAAIAABBYAAB7777777777774ABICAAAAAAAAAAAAAABUABAAAAAEAAAAAIBEABAAAAAUAAAAAACAAAAD6QAAAAAAAAAAAAAAAABQYBAAAAAAAAAA": "CAAAAExTS1P7AQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKCWxheW91dChvZmZzZXQ9MTYpIGhhbGY0IHVDb2xvcl9TMDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBpblBvc2l0aW9uOwpsYXlvdXQobG9jYXRpb24gPSAxKSBpbiBoYWxmIGluQ292ZXJhZ2U7CmxheW91dChsb2NhdGlvbiA9IDApIG91dCBoYWxmNCB2Y29sb3JfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIERlZmF1bHRHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgY29sb3IgPSB1Q29sb3JfUzA7Cgljb2xvciA9IGNvbG9yICogaW5Db3ZlcmFnZTsKCXZjb2xvcl9TMCA9IGNvbG9yOwoJZmxvYXQyIF90bXBfMV9pblBvc2l0aW9uID0gaW5Qb3NpdGlvbjsKCWZsb2F0MiBfdG1wXzNfaW5Qb3NpdGlvbiA9IGluUG9zaXRpb247Cglza19Qb3NpdGlvbiA9IF90bXBfMV9pblBvc2l0aW9uLnh5MDE7Cn0KAAAAAADMAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKCWxheW91dChvZmZzZXQ9MTYpIGhhbGY0IHVDb2xvcl9TMDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMCwgaW5kZXggPSAwKSBvdXQgaGFsZjQgc2tfRnJhZ0NvbG9yOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgRGVmYXVsdEdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmNvbG9yX1MwOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAABAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFQAAAABAAAAAAAAAAIAAAAdAAAAAAAAAAEAAAAcAAAACAAAAAEAAAAMAAAAAAAAAFAAAAABAAAAAQAAAAUAAAAAAAAAAQAAAAUAAAAAAAAAAQAAAP0AAAA=", "AYQQ5AADQAAAOAEARAFQJAABBADIB7777777777777777777777767YAAAAAAAAAAAAOQAAAAAAAQAAAABAMQAAAABDAAAAAAEAAAAAAAAAAAAAAAAAAAAAAADBQCAAAAAAAAAA": "CAAAAExTS1N5AgAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKCWxheW91dChvZmZzZXQ9MTYpIGZsb2F0NCB1bG9jYWxNYXRyaXhfUzA7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgaW5Qb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgaW5Db2xvcjsKbGF5b3V0KGxvY2F0aW9uID0gMikgaW4gZmxvYXQ0IGluQ2lyY2xlRWRnZTsKbGF5b3V0KGxvY2F0aW9uID0gMCkgb3V0IGZsb2F0NCB2aW5DaXJjbGVFZGdlX1MwOwpsYXlvdXQobG9jYXRpb24gPSAxKSBvdXQgaGFsZjQgdmluQ29sb3JfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIENpcmNsZUdlb21ldHJ5UHJvY2Vzc29yCgl2aW5DaXJjbGVFZGdlX1MwID0gaW5DaXJjbGVFZGdlOwoJdmluQ29sb3JfUzAgPSBpbkNvbG9yOwoJZmxvYXQyIF90bXBfMF9pblBvc2l0aW9uID0gaW5Qb3NpdGlvbjsKCWZsb2F0MiBfdG1wXzFfaW5Qb3NpdGlvbiA9IHVsb2NhbE1hdHJpeF9TMC54eiAqIGluUG9zaXRpb24gKyB1bG9jYWxNYXRyaXhfUzAueXc7Cglza19Qb3NpdGlvbiA9IF90bXBfMF9pblBvc2l0aW9uLnh5MDE7Cn0KAAAAAAAAAF0DAABsYXlvdXQgKGJpbmRpbmc9MCkgdW5pZm9ybSB1bmlmb3JtQnVmZmVyCnsKCWxheW91dChvZmZzZXQ9MCkgZmxvYXQ0IHNrX1JUQWRqdXN0OwoJbGF5b3V0KG9mZnNldD0xNikgZmxvYXQ0IHVsb2NhbE1hdHJpeF9TMDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0NCB2aW5DaXJjbGVFZGdlX1MwOwpsYXlvdXQobG9jYXRpb24gPSAxKSBpbiBoYWxmNCB2aW5Db2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMCwgaW5kZXggPSAwKSBvdXQgaGFsZjQgc2tfRnJhZ0NvbG9yOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgQ2lyY2xlR2VvbWV0cnlQcm9jZXNzb3IKCWZsb2F0NCBjaXJjbGVFZGdlOwoJY2lyY2xlRWRnZSA9IHZpbkNpcmNsZUVkZ2VfUzA7CgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmluQ29sb3JfUzA7CglmbG9hdCBkID0gbGVuZ3RoKGNpcmNsZUVkZ2UueHkpOwoJaGFsZiBkaXN0YW5jZVRvT3V0ZXJFZGdlID0gaGFsZihjaXJjbGVFZGdlLnogKiAoMS4wIC0gZCkpOwoJaGFsZiBlZGdlQWxwaGEgPSBzYXR1cmF0ZShkaXN0YW5jZVRvT3V0ZXJFZGdlKTsKCWhhbGYgZGlzdGFuY2VUb0lubmVyRWRnZSA9IGhhbGYoY2lyY2xlRWRnZS56ICogKGQgLSBjaXJjbGVFZGdlLncpKTsKCWhhbGYgaW5uZXJBbHBoYSA9IHNhdHVyYXRlKGRpc3RhbmNlVG9Jbm5lckVkZ2UpOwoJZWRnZUFscGhhICo9IGlubmVyQWxwaGE7CgloYWxmNCBvdXRwdXRDb3ZlcmFnZV9TMCA9IGhhbGY0KGVkZ2VBbHBoYSk7Cgl7CgkJLy8gWGZlciBQcm9jZXNzb3I6IFBvcnRlciBEdWZmCgkJc2tfRnJhZ0NvbG9yID0gb3V0cHV0Q29sb3JfUzAgKiBvdXRwdXRDb3ZlcmFnZV9TMDsKCX0KfQoAAAAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABgAAAAAQAAAAAAAAADAAAAHQAAAAAAAAABAAAACQAAAAgAAAABAAAAHwAAAAwAAAABAAAAHAAAAAAAAABGAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAAAAAAA", "HUJAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAQAAAAAAQQGACQAGAAAAAQAAAAAAAQQGAAAIYAAAAABAAAAAAAAAAAAAAAAAAAAAAAAYMAQAAQAAAAAA": "CAAAAExTS1NdAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gZmxvYXQyIGxvY2FsQ29vcmQ7CmxheW91dChsb2NhdGlvbiA9IDApIG91dCBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmxvY2FsQ29vcmRfUzAgPSBsb2NhbENvb3JkOwoJc2tfUG9zaXRpb24gPSBwb3NpdGlvbi54eTAxOwp9CgAAAAAAAABTAgAAbGF5b3V0KGJpbmRpbmc9MCkgdW5pZm9ybSBzYW1wbGVyMkQgdVRleHR1cmVTYW1wbGVyXzBfUzA7CmxheW91dCAoYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CmxheW91dChsb2NhdGlvbiA9IDAsIGluZGV4ID0gMCkgb3V0IGhhbGY0IHNrX0ZyYWdDb2xvcjsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzAgPSBoYWxmNCgxKTsKCWZsb2F0MiB0ZXhDb29yZDsKCXRleENvb3JkID0gdmxvY2FsQ29vcmRfUzA7CglvdXRwdXRDb2xvcl9TMCA9ICgoc2FtcGxlKHVUZXh0dXJlU2FtcGxlcl8wX1MwLCB0ZXhDb29yZCkgKiBoYWxmNCgxKSkpOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABUAAAAAQAAAAAAAAACAAAAHQAAAAAAAAABAAAAHQAAAAgAAAABAAAAEAAAAAAAAABGAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAAAAAAA", "HVIAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAAAAAAAAGQBIAAABAAAAANAEAAAAAAAAAAAAAABQAADAAAAAAAAAEAAAAAHQEAAQAAAAAMDCQAAACAAAAA2AIRAEAAAAAAAAAAAAAIADQAAAAIAAAAAAAIIDEMAAAAAAQAAAA7UAAAAAAAAAAAAAAAAAMGAIAAIAAAAAA": "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", "JAAAAAAABAAACAABBYAAAIAAAIAAGEAAAABRAEAAAAAAAAEIAAAAAAEASAAQAAAABIAAAAAEAAAAB7IAAAAACAAAAAAAAAAAAIAAAAAAAAAAA": "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", "HVIAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAAAAAAAAGQBIAAABAAAAANAEAAAAAAAAAAAAAABQAADAAAAAAAAAEAAAAAHQEAAQAAAAAMDCQAAACAAAAA2AIRAEAAAAAAAAAAAAAIADQAAAAIAAAAAAAIIDFAAAAAAAQAAAA7UAAAAAAAAAAAAAAAAAMGAIAAIAAAAAA": "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", "HUIAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAAAAAALUAQAAAAQAAAAGQCBAMQAAAAAAAAAACQAGAAAAAQAAAAAAAQQGAAAABIAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAABQAAACAAAAAAA": "CAAAAExTS1O5AQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKCWxheW91dChvZmZzZXQ9MTYpIGZsb2F0M3gzIHVtYXRyaXhfUzE7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGZsb2F0MiBsb2NhbENvb3JkOwpsYXlvdXQobG9jYXRpb24gPSAwKSBvdXQgZmxvYXQyIHZUcmFuc2Zvcm1lZENvb3Jkc18yX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gUHJpbWl0aXZlIFByb2Nlc3NvciBRdWFkUGVyRWRnZUFBR2VvbWV0cnlQcm9jZXNzb3IKCXNrX1Bvc2l0aW9uID0gcG9zaXRpb24ueHkwMTsKCXsKCQl2VHJhbnNmb3JtZWRDb29yZHNfMl9TMCA9IGZsb2F0M3gyKHVtYXRyaXhfUzEpICogbG9jYWxDb29yZC54eTE7Cgl9Cn0KAAAAAAAAAAIDAABsYXlvdXQoYmluZGluZz0wKSB1bmlmb3JtIHNhbXBsZXIyRCB1VGV4dHVyZVNhbXBsZXJfMF9TMTsKbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKCWxheW91dChvZmZzZXQ9MTYpIGZsb2F0M3gzIHVtYXRyaXhfUzE7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgdlRyYW5zZm9ybWVkQ29vcmRzXzJfUzA7CmxheW91dChsb2NhdGlvbiA9IDAsIGluZGV4ID0gMCkgb3V0IGhhbGY0IHNrX0ZyYWdDb2xvcjsKaGFsZjQgVGV4dHVyZUVmZmVjdF9TMV9jMChoYWxmNCBfaW5wdXQpIAp7CglyZXR1cm4gc2FtcGxlKHVUZXh0dXJlU2FtcGxlcl8wX1MxLCB2VHJhbnNmb3JtZWRDb29yZHNfMl9TMCk7Cn0KaGFsZjQgTWF0cml4RWZmZWN0X1MxKGhhbGY0IF9pbnB1dCkgCnsKCXJldHVybiBUZXh0dXJlRWZmZWN0X1MxX2MwKF9pbnB1dCk7Cn0Kdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzAgPSBoYWxmNCgxKTsKCWNvbnN0IGhhbGY0IG91dHB1dENvdmVyYWdlX1MwID0gaGFsZjQoMSk7CgloYWxmNCBvdXRwdXRfUzE7CglvdXRwdXRfUzEgPSBNYXRyaXhFZmZlY3RfUzEob3V0cHV0Q29sb3JfUzApOwoJewoJCS8vIFhmZXIgUHJvY2Vzc29yOiBQb3J0ZXIgRHVmZgoJCXNrX0ZyYWdDb2xvciA9IG91dHB1dF9TMSAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPAAAAAEAAAAAAAAAAgAAAB0AAAAAAAAAAQAAAB0AAAAIAAAAAQAAABAAAAAAAAAAUAAAAAAAAAABAAAAAAAAAA==", "HUJAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAQAAAAAAQQGABZAA6IAAAAACAAAAAADUAAAAAAAEAAAAAIDEAAAAAAFAAAAAAAQAAAAAAAAAAAAAAAAAAAAAAAMGAIAAIAAAAAA": "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", "HUIAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAAAAAALUAQMAAAAAAAAAEAAAABJYQAAAAAAAAIAAAAAWCBAAAABAAAAANAECAZAAAAAAAAAAAFAAMAAAABAAAAAAABBANDAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAABQAAACAAAAAAA": "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", "HUIAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAAAAAADUAANAAAAAAAAAIAAAABLAIABAAAAABAEGABBAMAAAAAAAAAAAAB2AAAAAAACAAAAAEBSAAAAARQAAAAACAAAAAAAAAAAAAAAAABAAAAAAAYAAAAAAAAAAA": "CAAAAExTS1O/AQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKCWxheW91dChvZmZzZXQ9MTYpIGZsb2F0M3gzIHVtYXRyaXhfUzFfYzA7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGZsb2F0MiBsb2NhbENvb3JkOwpsYXlvdXQobG9jYXRpb24gPSAwKSBvdXQgZmxvYXQyIHZUcmFuc2Zvcm1lZENvb3Jkc18zX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gUHJpbWl0aXZlIFByb2Nlc3NvciBRdWFkUGVyRWRnZUFBR2VvbWV0cnlQcm9jZXNzb3IKCXNrX1Bvc2l0aW9uID0gcG9zaXRpb24ueHkwMTsKCXsKCQl2VHJhbnNmb3JtZWRDb29yZHNfM19TMCA9IGZsb2F0M3gyKHVtYXRyaXhfUzFfYzApICogbG9jYWxDb29yZC54eTE7Cgl9Cn0KAAAAAACOAwAAbGF5b3V0KGJpbmRpbmc9MCkgdW5pZm9ybSBzYW1wbGVyMkQgdVRleHR1cmVTYW1wbGVyXzBfUzE7CmxheW91dCAoYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7CglsYXlvdXQob2Zmc2V0PTE2KSBmbG9hdDN4MyB1bWF0cml4X1MxX2MwOwp9CjsKbGF5b3V0KGxvY2F0aW9uID0gMCkgaW4gZmxvYXQyIHZUcmFuc2Zvcm1lZENvb3Jkc18zX1MwOwpsYXlvdXQobG9jYXRpb24gPSAwLCBpbmRleCA9IDApIG91dCBoYWxmNCBza19GcmFnQ29sb3I7CmhhbGY0IFRleHR1cmVFZmZlY3RfUzFfYzBfYzAoaGFsZjQgX2lucHV0KSAKewoJcmV0dXJuIHNhbXBsZSh1VGV4dHVyZVNhbXBsZXJfMF9TMSwgdlRyYW5zZm9ybWVkQ29vcmRzXzNfUzApOwp9CmhhbGY0IE1hdHJpeEVmZmVjdF9TMV9jMChoYWxmNCBfaW5wdXQpIAp7CglyZXR1cm4gVGV4dHVyZUVmZmVjdF9TMV9jMF9jMChfaW5wdXQpOwp9CmhhbGY0IEJsZW5kX1MxKGhhbGY0IF9zcmMsIGhhbGY0IF9kc3QpIAp7CgkvLyBCbGVuZCBtb2RlOiBNb2R1bGF0ZQoJcmV0dXJuIGJsZW5kX21vZHVsYXRlKE1hdHJpeEVmZmVjdF9TMV9jMChfc3JjKSwgX3NyYyk7Cn0Kdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzAgPSBoYWxmNCgxKTsKCWNvbnN0IGhhbGY0IG91dHB1dENvdmVyYWdlX1MwID0gaGFsZjQoMSk7CgloYWxmNCBvdXRwdXRfUzE7CglvdXRwdXRfUzEgPSBCbGVuZF9TMShvdXRwdXRDb2xvcl9TMCwgaGFsZjQoMSkpOwoJewoJCS8vIFhmZXIgUHJvY2Vzc29yOiBQb3J0ZXIgRHVmZgoJCXNrX0ZyYWdDb2xvciA9IG91dHB1dF9TMSAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPAAAAAEAAAAAAAAAAgAAAB0AAAAAAAAAAQAAAB0AAAAIAAAAAQAAABAAAAAAAAAARgAAAAAAAAABAAAAAAAAAA==", "HUJAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAQAAAAAAQQGAAZAABQAAAAAAAACAAAAADYCAAIAAAAAWBRAAAABAAAAANAEIQCAAAAAAAAAAAAAUABQAAAAEAAAAAAAEEBQAAAAKAAAAAABAAAAB7IAAAAAAAAAAAQAAAAAYMAQAAQAAAAAA": "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", "GEMAAAYAAEHAAAARC4EAAAQWBQAAAAAAAAAQAAAAIBCAAAGQAEAAAAAQAAAABAEQAEAAAACQAAAAAAIAAAAAAAAAAAAAAAAAAAAAAAGDAEAAAAAAAAAA": "CAAAAExTS1P2AQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBpblBvc2l0aW9uOwpsYXlvdXQobG9jYXRpb24gPSAxKSBpbiBoYWxmNCBpbkNvbG9yOwpsYXlvdXQobG9jYXRpb24gPSAyKSBpbiBoYWxmMyBpblNoYWRvd1BhcmFtczsKbGF5b3V0KGxvY2F0aW9uID0gMCkgb3V0IGhhbGYzIHZpblNoYWRvd1BhcmFtc19TMDsKbGF5b3V0KGxvY2F0aW9uID0gMSkgb3V0IGhhbGY0IHZpbkNvbG9yX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gUHJpbWl0aXZlIFByb2Nlc3NvciBSUmVjdFNoYWRvdwoJdmluU2hhZG93UGFyYW1zX1MwID0gaW5TaGFkb3dQYXJhbXM7Cgl2aW5Db2xvcl9TMCA9IGluQ29sb3I7CglmbG9hdDIgX3RtcF8wX2luUG9zaXRpb24gPSBpblBvc2l0aW9uOwoJc2tfUG9zaXRpb24gPSBfdG1wXzBfaW5Qb3NpdGlvbi54eTAxOwp9CgAAAAAAANQCAABsYXlvdXQoYmluZGluZz0wKSB1bmlmb3JtIHNhbXBsZXIyRCB1VGV4dHVyZVNhbXBsZXJfMF9TMDsKbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGhhbGYzIHZpblNoYWRvd1BhcmFtc19TMDsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgdmluQ29sb3JfUzA7CmxheW91dChsb2NhdGlvbiA9IDAsIGluZGV4ID0gMCkgb3V0IGhhbGY0IHNrX0ZyYWdDb2xvcjsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFJSZWN0U2hhZG93CgloYWxmMyBzaGFkb3dQYXJhbXM7CglzaGFkb3dQYXJhbXMgPSB2aW5TaGFkb3dQYXJhbXNfUzA7CgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmluQ29sb3JfUzA7CgloYWxmIGQgPSBsZW5ndGgoc2hhZG93UGFyYW1zLnh5KTsKCWZsb2F0MiB1diA9IGZsb2F0MihzaGFkb3dQYXJhbXMueiAqICgxLjAgLSBkKSwgMC41KTsKCWhhbGYgZmFjdG9yID0gc2FtcGxlKHVUZXh0dXJlU2FtcGxlcl8wX1MwLCB1dikuMDAwci5hOwoJaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNChmYWN0b3IpOwoJewoJCS8vIFhmZXIgUHJvY2Vzc29yOiBQb3J0ZXIgRHVmZgoJCXNrX0ZyYWdDb2xvciA9IG91dHB1dENvbG9yX1MwICogb3V0cHV0Q292ZXJhZ2VfUzA7Cgl9Cn0KAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYAAAAAEAAAAAAAAAAwAAAB0AAAAAAAAAAQAAAAkAAAAIAAAAAQAAAB4AAAAMAAAAAQAAABgAAAAAAAAAUAAAAAEAAAABAAAABQAAAAAAAAABAAAABQAAAAAAAAABAAAAAAAAAA==", "HUIAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAAAAAALUAQMAAAAAAIAAEAAAABJYQAAAAAQAAIAAAAAWCBACAABAAAAANAECAZAAEAAAAAAAAFAAMAAAABAAAAAAABBANDAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAABQAAACAAAAAAA": "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", "HUJAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAQAAAAAAQQGACQAGAAAAAQAAAAAAAQQGAAAIYAAAAABAAAAAAAAAAAAAAAAAAQAAAAAYMAQAAQAAAAAA": "CAAAAExTS1NdAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gZmxvYXQyIGxvY2FsQ29vcmQ7CmxheW91dChsb2NhdGlvbiA9IDApIG91dCBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmxvY2FsQ29vcmRfUzAgPSBsb2NhbENvb3JkOwoJc2tfUG9zaXRpb24gPSBwb3NpdGlvbi54eTAxOwp9CgAAAAAAAABTAgAAbGF5b3V0KGJpbmRpbmc9MCkgdW5pZm9ybSBzYW1wbGVyMkQgdVRleHR1cmVTYW1wbGVyXzBfUzA7CmxheW91dCAoYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CmxheW91dChsb2NhdGlvbiA9IDAsIGluZGV4ID0gMCkgb3V0IGhhbGY0IHNrX0ZyYWdDb2xvcjsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzAgPSBoYWxmNCgxKTsKCWZsb2F0MiB0ZXhDb29yZDsKCXRleENvb3JkID0gdmxvY2FsQ29vcmRfUzA7CglvdXRwdXRDb2xvcl9TMCA9ICgoc2FtcGxlKHVUZXh0dXJlU2FtcGxlcl8wX1MwLCB0ZXhDb29yZCkgKiBoYWxmNCgxKSkpOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABUAAAAAQAAAAAAAAACAAAAHQAAAAAAAAABAAAAHQAAAAgAAAABAAAAEAAAAAAAAABGAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAAAAAAA", "GEMAAAYAAEHAAAARC4EAAAQWBQAAAAAAAAAQAAAAIBCAAAGQAEAAAAAQAAAABAEQAEAAAACQAAAAAAIAAAAAAAAAAAAAAAAAEAAAAAGDAEAAAAAAAAAA": "CAAAAExTS1P2AQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBpblBvc2l0aW9uOwpsYXlvdXQobG9jYXRpb24gPSAxKSBpbiBoYWxmNCBpbkNvbG9yOwpsYXlvdXQobG9jYXRpb24gPSAyKSBpbiBoYWxmMyBpblNoYWRvd1BhcmFtczsKbGF5b3V0KGxvY2F0aW9uID0gMCkgb3V0IGhhbGYzIHZpblNoYWRvd1BhcmFtc19TMDsKbGF5b3V0KGxvY2F0aW9uID0gMSkgb3V0IGhhbGY0IHZpbkNvbG9yX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gUHJpbWl0aXZlIFByb2Nlc3NvciBSUmVjdFNoYWRvdwoJdmluU2hhZG93UGFyYW1zX1MwID0gaW5TaGFkb3dQYXJhbXM7Cgl2aW5Db2xvcl9TMCA9IGluQ29sb3I7CglmbG9hdDIgX3RtcF8wX2luUG9zaXRpb24gPSBpblBvc2l0aW9uOwoJc2tfUG9zaXRpb24gPSBfdG1wXzBfaW5Qb3NpdGlvbi54eTAxOwp9CgAAAAAAANQCAABsYXlvdXQoYmluZGluZz0wKSB1bmlmb3JtIHNhbXBsZXIyRCB1VGV4dHVyZVNhbXBsZXJfMF9TMDsKbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGhhbGYzIHZpblNoYWRvd1BhcmFtc19TMDsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgdmluQ29sb3JfUzA7CmxheW91dChsb2NhdGlvbiA9IDAsIGluZGV4ID0gMCkgb3V0IGhhbGY0IHNrX0ZyYWdDb2xvcjsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFJSZWN0U2hhZG93CgloYWxmMyBzaGFkb3dQYXJhbXM7CglzaGFkb3dQYXJhbXMgPSB2aW5TaGFkb3dQYXJhbXNfUzA7CgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmluQ29sb3JfUzA7CgloYWxmIGQgPSBsZW5ndGgoc2hhZG93UGFyYW1zLnh5KTsKCWZsb2F0MiB1diA9IGZsb2F0MihzaGFkb3dQYXJhbXMueiAqICgxLjAgLSBkKSwgMC41KTsKCWhhbGYgZmFjdG9yID0gc2FtcGxlKHVUZXh0dXJlU2FtcGxlcl8wX1MwLCB1dikuMDAwci5hOwoJaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNChmYWN0b3IpOwoJewoJCS8vIFhmZXIgUHJvY2Vzc29yOiBQb3J0ZXIgRHVmZgoJCXNrX0ZyYWdDb2xvciA9IG91dHB1dENvbG9yX1MwICogb3V0cHV0Q292ZXJhZ2VfUzA7Cgl9Cn0KAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAYAAAAAEAAAAAAAAAAwAAAB0AAAAAAAAAAQAAAAkAAAAIAAAAAQAAAB4AAAAMAAAAAQAAABgAAAAAAAAAUAAAAAEAAAABAAAABQAAAAAAAAABAAAABQAAAAAAAAABAAAAAAAAAA==", "HUQAAAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAAKAAYAAAACAAAAAAACCAYAABIAAAAAAEAAAAH5AAAAAAAAAAAAAAAAADBQCAAAAAAAAAA": "CAAAAExTS1NMAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDApIGZsYXQgb3V0IGhhbGY0IHZjb2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFByaW1pdGl2ZSBQcm9jZXNzb3IgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgl2Y29sb3JfUzAgPSBjb2xvcjsKCXNrX1Bvc2l0aW9uID0gcG9zaXRpb24ueHkwMTsKfQoAAAAAswEAAGxheW91dCAoYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMCwgaW5kZXggPSAwKSBvdXQgaGFsZjQgc2tfRnJhZ0NvbG9yOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmNvbG9yX1MwOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABUAAAAAQAAAAAAAAACAAAAHQAAAAAAAAABAAAACQAAAAgAAAABAAAADAAAAAAAAABQAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAD9AAAA", "AYQA5AADQAAAOAEARAFQJAABBADIB7777777777777777777777767YAAAAAAAAAAAAOQAAAAAAAQAAAABAMQAAAABIAAAAAAEAAAAAAAAAAAAAAAAAAAAAAADBQCAAAAAAAAAA": "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", "HUJAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAQAAAAAAQQGACQAGAAAAAQAAAAAAAQQGAAAIYAAAAABAAAAB7IAAAAACAAAAAIAAAAAYMAQAAQAAAAAA": "CAAAAExTS1NdAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gZmxvYXQyIGxvY2FsQ29vcmQ7CmxheW91dChsb2NhdGlvbiA9IDApIG91dCBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmxvY2FsQ29vcmRfUzAgPSBsb2NhbENvb3JkOwoJc2tfUG9zaXRpb24gPSBwb3NpdGlvbi54eTAxOwp9CgAAAAAAAABTAgAAbGF5b3V0KGJpbmRpbmc9MCkgdW5pZm9ybSBzYW1wbGVyMkQgdVRleHR1cmVTYW1wbGVyXzBfUzA7CmxheW91dCAoYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CmxheW91dChsb2NhdGlvbiA9IDAsIGluZGV4ID0gMCkgb3V0IGhhbGY0IHNrX0ZyYWdDb2xvcjsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzAgPSBoYWxmNCgxKTsKCWZsb2F0MiB0ZXhDb29yZDsKCXRleENvb3JkID0gdmxvY2FsQ29vcmRfUzA7CglvdXRwdXRDb2xvcl9TMCA9ICgoc2FtcGxlKHVUZXh0dXJlU2FtcGxlcl8wX1MwLCB0ZXhDb29yZCkgKiBoYWxmNCgxKSkpOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABUAAAAAQAAAAAAAAACAAAAHQAAAAAAAAABAAAAHQAAAAgAAAABAAAAEAAAAAAAAABGAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAD9AAAA", "HUIAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAAAAAADUAANAAAAAAAAAIAAAABLAIABAAAAABAEGABBAMAAAAAAAAAAAAB2AAAAAAACAAAAAEBSAAAAAUAAAAAACAAAAAAAAAAAAAAAAABAAAAAAAYAAAAAAAAAAA": "CAAAAExTS1O/AQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKCWxheW91dChvZmZzZXQ9MTYpIGZsb2F0M3gzIHVtYXRyaXhfUzFfYzA7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGZsb2F0MiBsb2NhbENvb3JkOwpsYXlvdXQobG9jYXRpb24gPSAwKSBvdXQgZmxvYXQyIHZUcmFuc2Zvcm1lZENvb3Jkc18zX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gUHJpbWl0aXZlIFByb2Nlc3NvciBRdWFkUGVyRWRnZUFBR2VvbWV0cnlQcm9jZXNzb3IKCXNrX1Bvc2l0aW9uID0gcG9zaXRpb24ueHkwMTsKCXsKCQl2VHJhbnNmb3JtZWRDb29yZHNfM19TMCA9IGZsb2F0M3gyKHVtYXRyaXhfUzFfYzApICogbG9jYWxDb29yZC54eTE7Cgl9Cn0KAAAAAACOAwAAbGF5b3V0KGJpbmRpbmc9MCkgdW5pZm9ybSBzYW1wbGVyMkQgdVRleHR1cmVTYW1wbGVyXzBfUzE7CmxheW91dCAoYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7CglsYXlvdXQob2Zmc2V0PTE2KSBmbG9hdDN4MyB1bWF0cml4X1MxX2MwOwp9CjsKbGF5b3V0KGxvY2F0aW9uID0gMCkgaW4gZmxvYXQyIHZUcmFuc2Zvcm1lZENvb3Jkc18zX1MwOwpsYXlvdXQobG9jYXRpb24gPSAwLCBpbmRleCA9IDApIG91dCBoYWxmNCBza19GcmFnQ29sb3I7CmhhbGY0IFRleHR1cmVFZmZlY3RfUzFfYzBfYzAoaGFsZjQgX2lucHV0KSAKewoJcmV0dXJuIHNhbXBsZSh1VGV4dHVyZVNhbXBsZXJfMF9TMSwgdlRyYW5zZm9ybWVkQ29vcmRzXzNfUzApOwp9CmhhbGY0IE1hdHJpeEVmZmVjdF9TMV9jMChoYWxmNCBfaW5wdXQpIAp7CglyZXR1cm4gVGV4dHVyZUVmZmVjdF9TMV9jMF9jMChfaW5wdXQpOwp9CmhhbGY0IEJsZW5kX1MxKGhhbGY0IF9zcmMsIGhhbGY0IF9kc3QpIAp7CgkvLyBCbGVuZCBtb2RlOiBNb2R1bGF0ZQoJcmV0dXJuIGJsZW5kX21vZHVsYXRlKE1hdHJpeEVmZmVjdF9TMV9jMChfc3JjKSwgX3NyYyk7Cn0Kdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzAgPSBoYWxmNCgxKTsKCWNvbnN0IGhhbGY0IG91dHB1dENvdmVyYWdlX1MwID0gaGFsZjQoMSk7CgloYWxmNCBvdXRwdXRfUzE7CglvdXRwdXRfUzEgPSBCbGVuZF9TMShvdXRwdXRDb2xvcl9TMCwgaGFsZjQoMSkpOwoJewoJCS8vIFhmZXIgUHJvY2Vzc29yOiBQb3J0ZXIgRHVmZgoJCXNrX0ZyYWdDb2xvciA9IG91dHB1dF9TMSAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPAAAAAEAAAAAAAAAAgAAAB0AAAAAAAAAAQAAAB0AAAAIAAAAAQAAABAAAAAAAAAAUAAAAAAAAAABAAAAAAAAAA==", "HTQAAGAABBYAAAEIXBAAAGEAMAAAAAAAAAAAAAAAQAHAAAAAQAAAAAAAQQGAAAAAIYAAAAABAAAAAAAAAAAAAAAAAAAAAAAAYMAQAAAAAAAAA": "CAAAAExTS1PhAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBpblBvc2l0aW9uOwpsYXlvdXQobG9jYXRpb24gPSAxKSBpbiBoYWxmNCBpbkNvbG9yOwpsYXlvdXQobG9jYXRpb24gPSAyKSBpbiBmbG9hdDQgaW5RdWFkRWRnZTsKbGF5b3V0KGxvY2F0aW9uID0gMCkgb3V0IGZsb2F0NCB2UXVhZEVkZ2VfUzA7CmxheW91dChsb2NhdGlvbiA9IDEpIG91dCBoYWxmNCB2aW5Db2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFByaW1pdGl2ZSBQcm9jZXNzb3IgUXVhZEVkZ2UKCXZRdWFkRWRnZV9TMCA9IGluUXVhZEVkZ2U7Cgl2aW5Db2xvcl9TMCA9IGluQ29sb3I7CglmbG9hdDIgX3RtcF8wX2luUG9zaXRpb24gPSBpblBvc2l0aW9uOwoJc2tfUG9zaXRpb24gPSBfdG1wXzBfaW5Qb3NpdGlvbi54eTAxOwp9CgAAAAAAAAC8AwAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0NCB2UXVhZEVkZ2VfUzA7CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGhhbGY0IHZpbkNvbG9yX1MwOwpsYXlvdXQobG9jYXRpb24gPSAwLCBpbmRleCA9IDApIG91dCBoYWxmNCBza19GcmFnQ29sb3I7CnZvaWQgbWFpbigpIAp7CgkvLyBTdGFnZSAwLCBRdWFkRWRnZQoJaGFsZjQgb3V0cHV0Q29sb3JfUzA7CglvdXRwdXRDb2xvcl9TMCA9IHZpbkNvbG9yX1MwOwoJaGFsZiBlZGdlQWxwaGE7CgloYWxmMiBkdXZkeCA9IGhhbGYyKGRGZHgodlF1YWRFZGdlX1MwLnh5KSk7CgloYWxmMiBkdXZkeSA9IGhhbGYyKGRGZHkodlF1YWRFZGdlX1MwLnh5KSk7CglpZiAodlF1YWRFZGdlX1MwLnogPiAwLjAgJiYgdlF1YWRFZGdlX1MwLncgPiAwLjApIAoJewoJCWVkZ2VBbHBoYSA9IGhhbGYobWluKG1pbih2UXVhZEVkZ2VfUzAueiwgdlF1YWRFZGdlX1MwLncpICsgMC41LCAxLjApKTsKCX0KCWVsc2UgCgl7CgkJaGFsZjIgZ0YgPSBoYWxmMihoYWxmKDIuMCp2UXVhZEVkZ2VfUzAueCpkdXZkeC54IC0gZHV2ZHgueSksICAgICAgICAgICAgICAgICBoYWxmKDIuMCp2UXVhZEVkZ2VfUzAueCpkdXZkeS54IC0gZHV2ZHkueSkpOwoJCWVkZ2VBbHBoYSA9IGhhbGYodlF1YWRFZGdlX1MwLngqdlF1YWRFZGdlX1MwLnggLSB2UXVhZEVkZ2VfUzAueSk7CgkJZWRnZUFscGhhID0gc2F0dXJhdGUoMC41IC0gZWRnZUFscGhhIC8gbGVuZ3RoKGdGKSk7Cgl9CgloYWxmNCBvdXRwdXRDb3ZlcmFnZV9TMCA9IGhhbGY0KGVkZ2VBbHBoYSk7Cgl7CgkJLy8gWGZlciBQcm9jZXNzb3I6IFBvcnRlciBEdWZmCgkJc2tfRnJhZ0NvbG9yID0gb3V0cHV0Q29sb3JfUzAgKiBvdXRwdXRDb3ZlcmFnZV9TMDsKCX0KfQoBAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABgAAAAAQAAAAAAAAADAAAAHQAAAAAAAAABAAAACQAAAAgAAAABAAAAHwAAAAwAAAABAAAAHAAAAAAAAABGAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAAAAAAA", "HUQAAAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAAKAAYAAAACAAAAAAYEIBAAAAFAAAAAAQAAAAH5AAAAAAIAAAAAAAAAAABQAAACAAAAAAA": "CAAAAExTS1NMAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDApIGZsYXQgb3V0IGhhbGY0IHZjb2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFByaW1pdGl2ZSBQcm9jZXNzb3IgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgl2Y29sb3JfUzAgPSBjb2xvcjsKCXNrX1Bvc2l0aW9uID0gcG9zaXRpb24ueHkwMTsKfQoAAAAA1wEAAGxheW91dCAoYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMCwgaW5kZXggPSAwKSBvdXQgaGFsZjQgc2tfRnJhZ0NvbG9yOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmNvbG9yX1MwOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJCXNrX0ZyYWdDb2xvciA9IHNrX0ZyYWdDb2xvci5hMDAwOwoJfQp9CgAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8AAAAAQAAAAAAAAACAAAAHQAAAAAAAAABAAAACQAAAAgAAAABAAAADAAAAAAAAAAKAAAAAAAAAAEAAAD9AAAA", "HUQAAAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAABAAIAAAAAAIIDAAAFAAAAAAAQAAAA7UAAAAABAAAAAAAAAAAAEAAAAIAAAAAA": "CAAAAExTS1NMAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDApIGZsYXQgb3V0IGhhbGY0IHZjb2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFByaW1pdGl2ZSBQcm9jZXNzb3IgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgl2Y29sb3JfUzAgPSBjb2xvcjsKCXNrX1Bvc2l0aW9uID0gcG9zaXRpb24ueHkwMTsKfQoAAAAAgAEAAGxheW91dCAoYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMCwgaW5kZXggPSAwKSBvdXQgaGFsZjQgc2tfRnJhZ0NvbG9yOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmNvbG9yX1MwOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogRGlzYWJsZSBDb2xvcgoJfQp9CgAAAAABAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwAAAABAAAAAAAAAAIAAAAdAAAAAAAAAAEAAAAJAAAACAAAAAEAAAAMAAAAAAAAAFAAAAAAAAAAAAAAAP0AAAA=", "HUJAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAQAAAAAAQQGAAZAABQAAAAAAAACAAAAADYCAAIAAAAAWBRAAAABAAAAANAEIQCAAAAAAAAAAAAAUABQAAAAEAAAAAAAEEBQAAAAIYAAAAABAAAAB7IAAAAAAAAAAAAAAAAAYMAQAAQAAAAAA": "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", "DAQAAAAAAABGAABAYAAQAIHCAIAYAQUBAEAAAAAAEAAAAAAAAAAAAAB2AAAAAAACAAAAAEBSAAAAARQAAAAACAAAAD6QAAAAAAAAAAAAAAAABQYBAAAAAAAAAA": "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", "HUJAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAQAAAAAAQQGACQAGAAAAAQAAAAAAAQQGAAAKAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAYMAQAAQAAAAAA": "CAAAAExTS1NdAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gZmxvYXQyIGxvY2FsQ29vcmQ7CmxheW91dChsb2NhdGlvbiA9IDApIG91dCBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmxvY2FsQ29vcmRfUzAgPSBsb2NhbENvb3JkOwoJc2tfUG9zaXRpb24gPSBwb3NpdGlvbi54eTAxOwp9CgAAAAAAAABTAgAAbGF5b3V0KGJpbmRpbmc9MCkgdW5pZm9ybSBzYW1wbGVyMkQgdVRleHR1cmVTYW1wbGVyXzBfUzA7CmxheW91dCAoYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CmxheW91dChsb2NhdGlvbiA9IDAsIGluZGV4ID0gMCkgb3V0IGhhbGY0IHNrX0ZyYWdDb2xvcjsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzAgPSBoYWxmNCgxKTsKCWZsb2F0MiB0ZXhDb29yZDsKCXRleENvb3JkID0gdmxvY2FsQ29vcmRfUzA7CglvdXRwdXRDb2xvcl9TMCA9ICgoc2FtcGxlKHVUZXh0dXJlU2FtcGxlcl8wX1MwLCB0ZXhDb29yZCkgKiBoYWxmNCgxKSkpOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABUAAAAAQAAAAAAAAACAAAAHQAAAAAAAAABAAAAHQAAAAgAAAABAAAAEAAAAAAAAABQAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAAAAAAA", "HUQAAAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAABAAIAAAAAAIIDAAAFAAAAAAAQAAAA7UAAAAABAAAAAAAAAAAAEAAAAAAAAAAA": "CAAAAExTS1NMAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDApIGZsYXQgb3V0IGhhbGY0IHZjb2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFByaW1pdGl2ZSBQcm9jZXNzb3IgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgl2Y29sb3JfUzAgPSBjb2xvcjsKCXNrX1Bvc2l0aW9uID0gcG9zaXRpb24ueHkwMTsKfQoAAAAAgAEAAGxheW91dCAoYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMCwgaW5kZXggPSAwKSBvdXQgaGFsZjQgc2tfRnJhZ0NvbG9yOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmNvbG9yX1MwOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogRGlzYWJsZSBDb2xvcgoJfQp9CgAAAAABAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwAAAABAAAAAAAAAAIAAAAdAAAAAAAAAAEAAAAJAAAACAAAAAEAAAAMAAAAAAAAAFAAAAAAAAAAAAAAAP0AAAA=", "HUIAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAAAAAALUAQBAEAQAAAAGQCBAMQACAIAAAAAACQAGAAAAAQAAAAAAAQQGAAAABIAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAABQAAACAAAAAAA": "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", "HUQAAAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAAHEADZAAAAAAIAAAAAAOQAAAAAAAQAAAABAMQAAAAAAUAAAAAACAAAAD6QAAAAAAAAAAAAAAAABQYBAABAAAAAAA": "CAAAAExTS1OiAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKCWxheW91dChvZmZzZXQ9MTYpIGZsb2F0NCB1aW5uZXJSZWN0X1MxOwoJbGF5b3V0KG9mZnNldD0zMikgaGFsZjIgdXJhZGl1c1BsdXNIYWxmX1MxOwp9CjsKbGF5b3V0KGxvY2F0aW9uID0gMCkgaW4gZmxvYXQyIHBvc2l0aW9uOwpsYXlvdXQobG9jYXRpb24gPSAxKSBpbiBoYWxmNCBjb2xvcjsKbGF5b3V0KGxvY2F0aW9uID0gMCkgZmxhdCBvdXQgaGFsZjQgdmNvbG9yX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gUHJpbWl0aXZlIFByb2Nlc3NvciBRdWFkUGVyRWRnZUFBR2VvbWV0cnlQcm9jZXNzb3IKCXZjb2xvcl9TMCA9IGNvbG9yOwoJc2tfUG9zaXRpb24gPSBwb3NpdGlvbi54eTAxOwp9CgAAAAAAAFgDAABsYXlvdXQgKGJpbmRpbmc9MCkgdW5pZm9ybSB1bmlmb3JtQnVmZmVyCnsKCWxheW91dChvZmZzZXQ9MCkgZmxvYXQ0IHNrX1JUQWRqdXN0OwoJbGF5b3V0KG9mZnNldD0xNikgZmxvYXQ0IHVpbm5lclJlY3RfUzE7CglsYXlvdXQob2Zmc2V0PTMyKSBoYWxmMiB1cmFkaXVzUGx1c0hhbGZfUzE7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMCwgaW5kZXggPSAwKSBvdXQgaGFsZjQgc2tfRnJhZ0NvbG9yOwpoYWxmNCBDaXJjdWxhclJSZWN0X1MxKGhhbGY0IF9pbnB1dCkgCnsKCWZsb2F0MiBkeHkwID0gdWlubmVyUmVjdF9TMS5MVCAtIHNrX0ZyYWdDb29yZC54eTsKCWZsb2F0MiBkeHkxID0gc2tfRnJhZ0Nvb3JkLnh5IC0gdWlubmVyUmVjdF9TMS5SQjsKCWZsb2F0MiBkeHkgPSBtYXgobWF4KGR4eTAsIGR4eTEpLCAwLjApOwoJaGFsZiBhbHBoYSA9IGhhbGYoc2F0dXJhdGUodXJhZGl1c1BsdXNIYWxmX1MxLnggLSBsZW5ndGgoZHh5KSkpOwoJcmV0dXJuIF9pbnB1dCAqIGFscGhhOwp9CnZvaWQgbWFpbigpIAp7CgkvLyBTdGFnZSAwLCBRdWFkUGVyRWRnZUFBR2VvbWV0cnlQcm9jZXNzb3IKCWhhbGY0IG91dHB1dENvbG9yX1MwOwoJb3V0cHV0Q29sb3JfUzAgPSB2Y29sb3JfUzA7Cgljb25zdCBoYWxmNCBvdXRwdXRDb3ZlcmFnZV9TMCA9IGhhbGY0KDEpOwoJaGFsZjQgb3V0cHV0X1MxOwoJb3V0cHV0X1MxID0gQ2lyY3VsYXJSUmVjdF9TMShvdXRwdXRDb3ZlcmFnZV9TMCk7Cgl7CgkJLy8gWGZlciBQcm9jZXNzb3I6IFBvcnRlciBEdWZmCgkJc2tfRnJhZ0NvbG9yID0gb3V0cHV0Q29sb3JfUzAgKiBvdXRwdXRfUzE7Cgl9Cn0KAQAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAVAAAAAEAAAAAAAAAAgAAAB0AAAAAAAAAAQAAAAkAAAAIAAAAAQAAAAwAAAAAAAAAUAAAAAEAAAABAAAABQAAAAAAAAABAAAABQAAAAAAAAABAAAA/QAAAA==", "CIAAAAAAQAARQAAYQAAAAGFYQAABRAAAAEEAAAAAAARAEAEABYAAAAEAAAAAAAEEBQAAAACGAAAAAAIAAAAAAAAAAAAAAAAAAAAAAAGDAEAAEAAAAAAA": "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", "HVIAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAAAAAAAAHIAAKAAAAARBNQWUSCAAAADWFZ4ITAMAAAAAAAAEAAAAAZCEKRJJQGUCAAAAAAAAAAAAZ3G46EJYGAAAAAAAABAAAAACYCEAIAAAAAAABUAQIDEAAAAAAAAAABUABAAAAAEAAAAAIBEABAAAEMAAAAAAQAAAAAAAAAAAAAAAAAAAAAAAMGAIAAIAAAAAA": "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", "HUQAAAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAAKAAYAAAACAAAAAAACCAYAABDAAAAAAEAAAAH5AAAAAAIAAAABAAAAADBQCAACAAAAAAA": "CAAAAExTS1NMAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDApIGZsYXQgb3V0IGhhbGY0IHZjb2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFByaW1pdGl2ZSBQcm9jZXNzb3IgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgl2Y29sb3JfUzAgPSBjb2xvcjsKCXNrX1Bvc2l0aW9uID0gcG9zaXRpb24ueHkwMTsKfQoAAAAAswEAAGxheW91dCAoYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMCwgaW5kZXggPSAwKSBvdXQgaGFsZjQgc2tfRnJhZ0NvbG9yOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmNvbG9yX1MwOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABUAAAAAQAAAAAAAAACAAAAHQAAAAAAAAABAAAACQAAAAgAAAABAAAADAAAAAAAAABGAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAD9AAAA", "HUJAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAQAAAAAAQQGACQAGAAAAAQAAAAAAAQQGAAAKAAAAAABAAAAB7IAAAAAAAAAAAAAAAAAYMAQAAQAAAAAA": "CAAAAExTS1NdAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gZmxvYXQyIGxvY2FsQ29vcmQ7CmxheW91dChsb2NhdGlvbiA9IDApIG91dCBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmxvY2FsQ29vcmRfUzAgPSBsb2NhbENvb3JkOwoJc2tfUG9zaXRpb24gPSBwb3NpdGlvbi54eTAxOwp9CgAAAAAAAABTAgAAbGF5b3V0KGJpbmRpbmc9MCkgdW5pZm9ybSBzYW1wbGVyMkQgdVRleHR1cmVTYW1wbGVyXzBfUzA7CmxheW91dCAoYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CmxheW91dChsb2NhdGlvbiA9IDAsIGluZGV4ID0gMCkgb3V0IGhhbGY0IHNrX0ZyYWdDb2xvcjsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzAgPSBoYWxmNCgxKTsKCWZsb2F0MiB0ZXhDb29yZDsKCXRleENvb3JkID0gdmxvY2FsQ29vcmRfUzA7CglvdXRwdXRDb2xvcl9TMCA9ICgoc2FtcGxlKHVUZXh0dXJlU2FtcGxlcl8wX1MwLCB0ZXhDb29yZCkgKiBoYWxmNCgxKSkpOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABUAAAAAQAAAAAAAAACAAAAHQAAAAAAAAABAAAAHQAAAAgAAAABAAAAEAAAAAAAAABQAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAD9AAAA", "JAAQAAAABAAACAABBYAAAKAAAMAAGEAAAABRAEAAAEHCAAAAAAAABCAAAAAABAEQAEAAAACGAAAAAAIAAAAP2AAAAAAQAAAACAAAAAACAAAAAAAAAAAA": "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", "HUJAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAQAAAAAAQQGACQAGAAAAAQAAAAAAAQQGAAAIYAAAAABAAAAB7IAAAAAAAAAAAAAAAAAYMAQAAQAAAAAA": "CAAAAExTS1NdAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gZmxvYXQyIGxvY2FsQ29vcmQ7CmxheW91dChsb2NhdGlvbiA9IDApIG91dCBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmxvY2FsQ29vcmRfUzAgPSBsb2NhbENvb3JkOwoJc2tfUG9zaXRpb24gPSBwb3NpdGlvbi54eTAxOwp9CgAAAAAAAABTAgAAbGF5b3V0KGJpbmRpbmc9MCkgdW5pZm9ybSBzYW1wbGVyMkQgdVRleHR1cmVTYW1wbGVyXzBfUzA7CmxheW91dCAoYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CmxheW91dChsb2NhdGlvbiA9IDAsIGluZGV4ID0gMCkgb3V0IGhhbGY0IHNrX0ZyYWdDb2xvcjsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzAgPSBoYWxmNCgxKTsKCWZsb2F0MiB0ZXhDb29yZDsKCXRleENvb3JkID0gdmxvY2FsQ29vcmRfUzA7CglvdXRwdXRDb2xvcl9TMCA9ICgoc2FtcGxlKHVUZXh0dXJlU2FtcGxlcl8wX1MwLCB0ZXhDb29yZCkgKiBoYWxmNCgxKSkpOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABUAAAAAQAAAAAAAAACAAAAHQAAAAAAAAABAAAAHQAAAAgAAAABAAAAEAAAAAAAAABGAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAD9AAAA", "AYQA5AADQAAAOAEARAFQJAABBADIB7777777777777777777777767YAAAAAAAAAAAAOQAAAAAAAQAAAABAMQAAAABIAAAAAAEAAAAH5AAAAAAAAAAAAAAAAADBQCAAAAAAAAAA": "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", "B2ABSAAABQAAIAABBYAAB7777777777774ABICAAAAAAAAAAAAAABUABAAAAAEAAAAAIBEABAAAAARQAAAAACAAAAD6QAAAAAAAAAAAAAAAABQYBAAAAAAAAAA": "CAAAAExTS1P7AQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKCWxheW91dChvZmZzZXQ9MTYpIGhhbGY0IHVDb2xvcl9TMDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBpblBvc2l0aW9uOwpsYXlvdXQobG9jYXRpb24gPSAxKSBpbiBoYWxmIGluQ292ZXJhZ2U7CmxheW91dChsb2NhdGlvbiA9IDApIG91dCBoYWxmNCB2Y29sb3JfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIERlZmF1bHRHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgY29sb3IgPSB1Q29sb3JfUzA7Cgljb2xvciA9IGNvbG9yICogaW5Db3ZlcmFnZTsKCXZjb2xvcl9TMCA9IGNvbG9yOwoJZmxvYXQyIF90bXBfMV9pblBvc2l0aW9uID0gaW5Qb3NpdGlvbjsKCWZsb2F0MiBfdG1wXzNfaW5Qb3NpdGlvbiA9IGluUG9zaXRpb247Cglza19Qb3NpdGlvbiA9IF90bXBfMV9pblBvc2l0aW9uLnh5MDE7Cn0KAAAAAADMAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKCWxheW91dChvZmZzZXQ9MTYpIGhhbGY0IHVDb2xvcl9TMDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMCwgaW5kZXggPSAwKSBvdXQgaGFsZjQgc2tfRnJhZ0NvbG9yOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgRGVmYXVsdEdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmNvbG9yX1MwOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAABAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFQAAAABAAAAAAAAAAIAAAAdAAAAAAAAAAEAAAAcAAAACAAAAAEAAAAMAAAAAAAAAEYAAAABAAAAAQAAAAUAAAAAAAAAAQAAAAUAAAAAAAAAAQAAAP0AAAA=", "HVIAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAAAAAAAAGQBIAAABAAAAANAEAAAAAAAAAAAAAABQAADAAAAAAAAAEAAAAAHQEAAQAAAAAMDCQAAACAAAAA2AIRAEAAAAAAAAAAAAAIADQAAAAIAAAAAAAIIDEMAAAAAAQAAAAAAAAAAAAAAAAAAAAAAAMGAIAAIAAAAAA": "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", "CIAAAAAAQAARQAAYQAAAAGFYQAABRAAAAEEAAAAAAARAEAEABYAAAAEAAAAAAAEEBQAAAACGAAAAAAIAAAAP2AAAAAAAAAAAAAAAAAGDAEAAEAAAAAAA": "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", "HVIAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAAAAAAAAGIBIAAABAAAAANAEAAAAAAAAAAAAAABAAOAAAABAAAAAAABBAMAAAABDAAAAAAEAAAAH5AAAAAAAAAAAAAAAAADBQCAACAAAAAAA": "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", "HUIAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAAAAAALUAQBAEAQAAAAGQCBAMQACAIAAAAAACQAGAAAAAQAAAAAAAQQGAAAABDAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAABQAAACAAAAAAA": "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", "HUQAAAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAAKAAYAAAACAAAAAAACCAYAABIAAAAAAEAAAAH5AAAAAAAAAAAAAAAAADBQCAACAAAAAAA": "CAAAAExTS1NMAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDApIGZsYXQgb3V0IGhhbGY0IHZjb2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFByaW1pdGl2ZSBQcm9jZXNzb3IgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgl2Y29sb3JfUzAgPSBjb2xvcjsKCXNrX1Bvc2l0aW9uID0gcG9zaXRpb24ueHkwMTsKfQoAAAAAswEAAGxheW91dCAoYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMCwgaW5kZXggPSAwKSBvdXQgaGFsZjQgc2tfRnJhZ0NvbG9yOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmNvbG9yX1MwOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABUAAAAAQAAAAAAAAACAAAAHQAAAAAAAAABAAAACQAAAAgAAAABAAAADAAAAAAAAABQAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAD9AAAA", "HUQACAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAAKAAYAAAACAAAAAAACCAYAABIAAAAAAEAAAAAAAAAAAAAAAAACAAAAADBQCAAAAAAAAAA": "CAAAAExTS1NHAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDApIG91dCBoYWxmNCB2Y29sb3JfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmNvbG9yX1MwID0gY29sb3I7Cglza19Qb3NpdGlvbiA9IHBvc2l0aW9uLnh5MDE7Cn0KAAAAAACuAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMCwgaW5kZXggPSAwKSBvdXQgaGFsZjQgc2tfRnJhZ0NvbG9yOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmNvbG9yX1MwOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAVAAAAAEAAAAAAAAAAgAAAB0AAAAAAAAAAQAAAAkAAAAIAAAAAQAAAAwAAAAAAAAAUAAAAAEAAAABAAAABQAAAAAAAAABAAAABQAAAAAAAAABAAAAAAAAAA==", "AYQA5AADQAAAOAEARAFQJAABBADIB7777777777777777777777767YAAAAAAAAAAAAMXQGOCU7TCAAAACAOSKJ7FYFAAAAAAMAAAAAGAAAAAKE666BBAAIAAAABNEN7D4AAAABAAAAAAQCGJRH7S4KRAAAAAGAAAAADAAAAABAPDPAXQQEAAAAAWCEPZ7IAAAAAAAQAAAADFKDY3YFUEBAAAAAAAAAAQAKYCRPE54DQAAAABAAAAAEQEFMEJ7T6AAAAAAAAAAAIAA6YAMAAACAAAAAABUABAAAAAEAAAAAIBEABAAAEMAAAAAAQAAAAAAAAAAAAAAAAAAAAAAAMGAIAAAAAAAAA": "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", "AYQA5AADQAAAOAEARAFQJAABBADIB7777777777777777777777767YAAAAAAAAAAAAOQAAAAAAAQAAAABAMQAAAABDAAAAAAEAAAAAAAAAAAAAAAAAAAAAAADBQCAAAAAAAAAA": "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", "AYQA5AADQAAAOAEARAFQJAABBADIB7777777777777777777777767YAAAAAAAAAAAAOQAAAAAAAQAAAABAMQAAAABIAAAAAAEAAAAH5AAAAAAAAAAACAAAAADBQCAAAAAAAAAA": "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", "HUIAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAAAAAALUAQMAAAAAAIAAEAAAABJYQAAAAAQAAIAAAAAWCBACAABAAAAANAECAZAAEAAAAAAAAFAAMAAAABAAAAAAABBANIAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAABQAAACAAAAAAA": "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", "HVIAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAAAAAAAAFIBTWV34ISAIAAAAEYT7ZOFIQAAAADAAAAABQAAAAAIFWCLQA2EQAAAALBCH47UAAAAAAAEAAAABSMJ5MVD4LAIAABQAAAAAIAAIAAAALDFIAN6IAAAAAQBC6J3YHAAAAAEAAAAAJBQOYJKADOSAAAAAAAAAAACWAQLBCP47QAAAAIAAAAAEABTASF4XXAMAAAAAAAAAAB2AAAAAAACAAAAAEBSAAKAAAAAABAAAAAAAAAAAAAAAAAAQAAAAAQEAQAAQAAAAAA": "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", "HVIAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAAAAAAAAFQBTWV34ISAIAAAAAAAACAAAAAVQEAAQAAAAAQCDAAQQGAAAAAAAAAAAA4IAPAAACAAAAAAAEABYAAAAEAAAAAAAEEBQAKAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAYMAQAAQAAAAAA": "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", "HVIACAAAABQAAGAAAQ4AAAAAGQQAARC4GAAAIOCAAD6P7777777777YDAAAAAAAAAAAFIBTWV34ISAIAAAAEYT7ZOFIQAAAABAAAAABQAAAAAQHRXQLYICAAAAALBCH47UAAAAAAAEAAAABSMJ5MVD4LAIAAAQAAAAAIAAIAAAAIVZ55EBCAAAAAQBC6J3YHAAAAAEAAAAAJAQOF6NPBAIQAAAAAAAAAACWAQLBCP47QAAAAIAAAAAEABTASF4XXAMAAAAAAAAAAB2AAAAAAACAAAAAEBSAAIYAAAAABAAAAAAAAAAAAAAAAAAAAAAAAYMAQAAAAAAAAA": "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", "AYQQ5AADQAAAOAEARAFQJAABBADIB7777777777777777777777767YAAAAAAAAAAAAOQAAAAAAAQAAAABAMQAAAABDAAAAAAEAAAAH5AAAAAAAAAAAAAAAAADBQCAAAAAAAAAA": "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", "HVJAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAABAAAAAABBAMABAAOAAAABAAAAAAABBAMAAAKAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAYMAQAAQAAAAAA": "CAAAAExTS1PFAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDIpIGluIGZsb2F0MiBsb2NhbENvb3JkOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IG91dCBoYWxmNCB2Y29sb3JfUzA7CmxheW91dChsb2NhdGlvbiA9IDEpIG91dCBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmNvbG9yX1MwID0gY29sb3I7Cgl2bG9jYWxDb29yZF9TMCA9IGxvY2FsQ29vcmQ7Cglza19Qb3NpdGlvbiA9IHBvc2l0aW9uLnh5MDE7Cn0KAAAAAAAAAJkCAABsYXlvdXQoYmluZGluZz0wKSB1bmlmb3JtIHNhbXBsZXIyRCB1VGV4dHVyZVNhbXBsZXJfMF9TMDsKbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGZsYXQgaW4gaGFsZjQgdmNvbG9yX1MwOwpsYXlvdXQobG9jYXRpb24gPSAxKSBpbiBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CmxheW91dChsb2NhdGlvbiA9IDAsIGluZGV4ID0gMCkgb3V0IGhhbGY0IHNrX0ZyYWdDb2xvcjsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzA7CglvdXRwdXRDb2xvcl9TMCA9IHZjb2xvcl9TMDsKCWZsb2F0MiB0ZXhDb29yZDsKCXRleENvb3JkID0gdmxvY2FsQ29vcmRfUzA7CglvdXRwdXRDb2xvcl9TMCA9ICgoc2FtcGxlKHVUZXh0dXJlU2FtcGxlcl8wX1MwLCB0ZXhDb29yZCkgKiBvdXRwdXRDb2xvcl9TMCkpOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAAABAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGAAAAABAAAAAAAAAAMAAAAdAAAAAAAAAAEAAAAJAAAACAAAAAEAAAAdAAAADAAAAAEAAAAUAAAAAAAAAFAAAAABAAAAAQAAAAUAAAAAAAAAAQAAAAUAAAAAAAAAAQAAAAAAAAA=", "HUJAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAQAAAAAAQQGAARAGQWMHGBRIAAAAABQAAAAAAAAAAHIAAAAAAAIAAAAAQGIABDAAAAAAEAAAAAAAAAAAAAAAAAAAAAAADBQCAACAAAAAAA": "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", "HUIAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAAAAAALUAQAAEAQAAAAGQCBAMQAAAIAAAAAACQAGAAAAAQAAAAAAAQQGAAAABIAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAABQAAACAAAAAAA": "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", "HVIAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAAAAAAAAGIBIAAABAAAAANAEAAAAAAAAAAAAAABAAOAAAABAAAAAAABBAMAAAABIAAAAAAEAAAAH5AAAAAAAAAAAAAAAAADBQCAACAAAAAAA": "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", "AYQA5AADQAAAOAEARAFQJAABBADIB7777777777777777777777767YAAAAAAAAAACAMQAHOMFARUBIAADAAAAAAAAAAAAAAHIAAAAAAAIAAAAAQGIAEMAAAAAAQAAAAAAAAAAAAAAAAAIAAAAAMGAIAAAAAAAAA": "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", "CIAQAAAAIABCQAAYQAAAAGEAQAAAQ4AAAEMLQQABDCAMAAIIAAAAAABCAIAKQDHMLTYRGAYAAAAJRHXS4ORAAAAAGAAAAADAAAAAAQDMEXABWJAAAAAGAEPZ7MAQAAAAAIAAAADEYT2JIHYXAUAABAABAAAAAAYAAAAGEKYB3YQACAAAACF4RXYPAAAAAIAAAAACBA5RSUAG7EAAAAAAAAAAABMBCWCE7Z7AAAAAQAAAAAAADGBELZHPA4AAAAAAAAAABUABAAAAAEAAAAAIBEABABIAAAAAAEAAAAAAAAAAAAAAAAACAAAAADBQCAACAAAAAAA": "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", "HVIAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAAAAAAAAFQBUW3IF4MAAAAAAEYT7ZOFIQAAAADAAAAABQAAAAAYFVV5UBASAAAAALBCH47UAAAAAAAEAAAABSMJ5MVD4LAIAABQAAAAAIAAIAAAAKW7KFQNAAEAAAQBC6J3YHAAAAAEAAAAAJBQOVX2RECIABAAAAAAAAACWAQLBCP47QAAAAIAAAAAEABTASF4XXAMAAAAAAAAAABLAAABAAAAAAAAGQCAAAAAAAAAAAAAAOQAAAAAAAQAAAABAMQAAAIYAAAAABAAAAAAAAAAAAAAAAAAAAAAAAYMAQAAQAAAAAA": "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", "HVIAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAAAAAAAAGIBIAAABAAAAANAEAAAAAAAAAAAAAABAAOAAAABAAAAAAABBAMAAAABDAAAAAAEAAAAAAAAAAAAAAAAAAAAAAADBQCAACAAAAAAA": "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", "HUJAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAQAAAAAAQQGAAZAABQAAAAAAAACAAAAADYCAAIAAAAAWBRAAAABAAAAANAEIQCAAAAAAAAAAAAAUABQAAAAEAAAAAAAEEBQAAAAIYAAAAABAAAAAAAAAAAAAAAAAAQAAAAAYMAQAAQAAAAAA": "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", "HUIAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAAAAAALUAQBAEAQAAAAGQCBAMQACAIAAAAAACQAGAAAAAQAAAAAAAQQGAAAABDAAAAAAEAAAAAAAAAAAAAAAAACAAAAAABQAAACAAAAAAA": "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", "AYQA5AADQAAAOAEARAFQJAABBADIB7777777777777777777777767YAAAAAAAAAACAMQAHOMFARUBIAADAAAAAAAAAAAAAAHIAAAAAAAIAAAAAQGIAFAAAAAAAQAAAAAAAAAAAAAAAAAIAAAAAMGAIAAAAAAAAA": "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", "HUQAAAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAACEA2CZQ4YGFAAAAAAGAAAAAAAAAAA5AAAAAAABAAAAACAZAAFAAAAAAAQAAAA7UAAAAAAAAAAAAAAAAAMGAIAAIAAAAAA": "CAAAAExTS1NyAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKCWxheW91dChvZmZzZXQ9MTYpIGZsb2F0NCB1Y2lyY2xlX1MxOwp9CjsKbGF5b3V0KGxvY2F0aW9uID0gMCkgaW4gZmxvYXQyIHBvc2l0aW9uOwpsYXlvdXQobG9jYXRpb24gPSAxKSBpbiBoYWxmNCBjb2xvcjsKbGF5b3V0KGxvY2F0aW9uID0gMCkgZmxhdCBvdXQgaGFsZjQgdmNvbG9yX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gUHJpbWl0aXZlIFByb2Nlc3NvciBRdWFkUGVyRWRnZUFBR2VvbWV0cnlQcm9jZXNzb3IKCXZjb2xvcl9TMCA9IGNvbG9yOwoJc2tfUG9zaXRpb24gPSBwb3NpdGlvbi54eTAxOwp9CgAAAAAAAGgEAABjb25zdCBpbnQga0ZpbGxBQV9TMSA9IDE7CmNvbnN0IGludCBrSW52ZXJzZUZpbGxCV19TMSA9IDI7CmNvbnN0IGludCBrSW52ZXJzZUZpbGxBQV9TMSA9IDM7CmxheW91dCAoYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7CglsYXlvdXQob2Zmc2V0PTE2KSBmbG9hdDQgdWNpcmNsZV9TMTsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGZsYXQgaW4gaGFsZjQgdmNvbG9yX1MwOwpsYXlvdXQobG9jYXRpb24gPSAwLCBpbmRleCA9IDApIG91dCBoYWxmNCBza19GcmFnQ29sb3I7CmhhbGY0IENpcmNsZV9TMShoYWxmNCBfaW5wdXQpIAp7CgloYWxmNCBfdG1wXzBfaW5Db2xvciA9IF9pbnB1dDsKCWhhbGYgZDsKCWlmIChpbnQoMSkgPT0ga0ludmVyc2VGaWxsQldfUzEgfHwgaW50KDEpID09IGtJbnZlcnNlRmlsbEFBX1MxKSAKCXsKCQlkID0gaGFsZigobGVuZ3RoKCh1Y2lyY2xlX1MxLnh5IC0gc2tfRnJhZ0Nvb3JkLnh5KSAqIHVjaXJjbGVfUzEudykgLSAxLjApICogdWNpcmNsZV9TMS56KTsKCX0KCWVsc2UgCgl7CgkJZCA9IGhhbGYoKDEuMCAtIGxlbmd0aCgodWNpcmNsZV9TMS54eSAtIHNrX0ZyYWdDb29yZC54eSkgKiB1Y2lyY2xlX1MxLncpKSAqIHVjaXJjbGVfUzEueik7Cgl9CglpZiAoaW50KDEpID09IGtGaWxsQUFfUzEgfHwgaW50KDEpID09IGtJbnZlcnNlRmlsbEFBX1MxKSAKCXsKCQlyZXR1cm4gaGFsZjQoX2lucHV0ICogc2F0dXJhdGUoZCkpOwoJfQoJZWxzZSAKCXsKCQlyZXR1cm4gaGFsZjQoZCA+IDAuNSA/IF9pbnB1dCA6IGhhbGY0KDAuMCkpOwoJfQp9CnZvaWQgbWFpbigpIAp7CgkvLyBTdGFnZSAwLCBRdWFkUGVyRWRnZUFBR2VvbWV0cnlQcm9jZXNzb3IKCWhhbGY0IG91dHB1dENvbG9yX1MwOwoJb3V0cHV0Q29sb3JfUzAgPSB2Y29sb3JfUzA7Cgljb25zdCBoYWxmNCBvdXRwdXRDb3ZlcmFnZV9TMCA9IGhhbGY0KDEpOwoJaGFsZjQgb3V0cHV0X1MxOwoJb3V0cHV0X1MxID0gQ2lyY2xlX1MxKG91dHB1dENvdmVyYWdlX1MwKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dF9TMTsKCX0KfQoBAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABUAAAAAQAAAAAAAAACAAAAHQAAAAAAAAABAAAACQAAAAgAAAABAAAADAAAAAAAAABQAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAD9AAAA", "HVJAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAABAAAAAABBAMABAAOAAAABAAAAAAABBAMAAAKAAAAAABAAAAB7IAAAAAAAAAAAAAAAAAYMAQAAQAAAAAA": "CAAAAExTS1PFAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDIpIGluIGZsb2F0MiBsb2NhbENvb3JkOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IG91dCBoYWxmNCB2Y29sb3JfUzA7CmxheW91dChsb2NhdGlvbiA9IDEpIG91dCBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmNvbG9yX1MwID0gY29sb3I7Cgl2bG9jYWxDb29yZF9TMCA9IGxvY2FsQ29vcmQ7Cglza19Qb3NpdGlvbiA9IHBvc2l0aW9uLnh5MDE7Cn0KAAAAAAAAAJkCAABsYXlvdXQoYmluZGluZz0wKSB1bmlmb3JtIHNhbXBsZXIyRCB1VGV4dHVyZVNhbXBsZXJfMF9TMDsKbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGZsYXQgaW4gaGFsZjQgdmNvbG9yX1MwOwpsYXlvdXQobG9jYXRpb24gPSAxKSBpbiBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CmxheW91dChsb2NhdGlvbiA9IDAsIGluZGV4ID0gMCkgb3V0IGhhbGY0IHNrX0ZyYWdDb2xvcjsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzA7CglvdXRwdXRDb2xvcl9TMCA9IHZjb2xvcl9TMDsKCWZsb2F0MiB0ZXhDb29yZDsKCXRleENvb3JkID0gdmxvY2FsQ29vcmRfUzA7CglvdXRwdXRDb2xvcl9TMCA9ICgoc2FtcGxlKHVUZXh0dXJlU2FtcGxlcl8wX1MwLCB0ZXhDb29yZCkgKiBvdXRwdXRDb2xvcl9TMCkpOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAAABAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGAAAAABAAAAAAAAAAMAAAAdAAAAAAAAAAEAAAAJAAAACAAAAAEAAAAdAAAADAAAAAEAAAAUAAAAAAAAAFAAAAABAAAAAQAAAAUAAAAAAAAAAQAAAAUAAAAAAAAAAQAAAP0AAAA=", "FAAQMYAAMAAAEADAAABAEYAAAICIAB5AABQAAAQAEDAACATAAABAGYAAAICSBYQCA4AAAAAAEAZIA62YSBDACAAAGAAAAAAAAAAABAAOAAAABAAAAAAABBAMABIAAAAAAEAAAAAAAAAAAAAAAAACAAAAADBQCAAAAAAAAAA": "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", "HUJAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAQAAAAAAQQGACQAGAAAAAQAAAAAAAQQGAAAKAAAAAABAAAAAAAAAAAAAAAAAAQAAAAAYMAQAAQAAAAAA": "CAAAAExTS1NdAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gZmxvYXQyIGxvY2FsQ29vcmQ7CmxheW91dChsb2NhdGlvbiA9IDApIG91dCBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmxvY2FsQ29vcmRfUzAgPSBsb2NhbENvb3JkOwoJc2tfUG9zaXRpb24gPSBwb3NpdGlvbi54eTAxOwp9CgAAAAAAAABTAgAAbGF5b3V0KGJpbmRpbmc9MCkgdW5pZm9ybSBzYW1wbGVyMkQgdVRleHR1cmVTYW1wbGVyXzBfUzA7CmxheW91dCAoYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CmxheW91dChsb2NhdGlvbiA9IDAsIGluZGV4ID0gMCkgb3V0IGhhbGY0IHNrX0ZyYWdDb2xvcjsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzAgPSBoYWxmNCgxKTsKCWZsb2F0MiB0ZXhDb29yZDsKCXRleENvb3JkID0gdmxvY2FsQ29vcmRfUzA7CglvdXRwdXRDb2xvcl9TMCA9ICgoc2FtcGxlKHVUZXh0dXJlU2FtcGxlcl8wX1MwLCB0ZXhDb29yZCkgKiBoYWxmNCgxKSkpOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABUAAAAAQAAAAAAAAACAAAAHQAAAAAAAAABAAAAHQAAAAgAAAABAAAAEAAAAAAAAABQAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAAAAAAA", "AYQQ5AADQAAAOAEARAFQJAABBADIB7777777777777777777777767YAAAAAAAAAAAAOQAAAAAAAQAAAABAMQAAAABIAAAAAAEAAAAH5AAAAAAAAAAAAAAAAADBQCAAAAAAAAAA": "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", "JAAQAAAABAAACAABBYAAAKAAAMAAGEAAAABRAEAAAEHCAAAAAAAABCAAAAAABAEQAEAAAAAKAAAAABAAAAAP2AAAAAAQAAAAAAAAAAACAAAAAAAAAAAA": "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", "AYQA5AADQAAAOAEARAFQJAABBADIB7777777777777777777777767YAAAAAAAAAAAAMXQGOCU7TCAAAACAOSKJ7FYFAAAAAAMAAAAAGAAAAAKE666BBAAIAAAABNEN7D4AAAABAAAAAAQCGJRH7S4KRAAAAAGAAAAADAAAAABAPDPAXQQEAAAAAWCEPZ7IAAAAAAAQAAAADFKDY3YFUEBAAAAAAAAAAQAKYCRPE54DQAAAABAAAAAEQEFMEJ7T6AAAAAAAAAAAIAA6YAMAAACAAAAAABUABAAAAAEAAAAAIBEABAAAFAAAAAAAQAAAA7UAAAAAAAAAAAAAAAAAMGAIAAAAAAAAA": "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", "HVJAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAABAAAAAABBAMABCANBEYOMDCSAAAAADAAAAAAAAAAAGQAEAAAAAQAAAABAEQAFDAAAAAAEAAAAAAAAAAAAAAAAAAAAAAADBQCAACAAAAAAA": "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", "DAQAAAAAAABGAABAYAAQAIHCAIAYAQUBAEAAAAAAEAAAAAAAAAAAAAB2AAAAAAACAAAAAEBSAAAAARQAAAAACAAAAAAAAAAAAAAAAAAAAAAABQYBAAAAAAAAAA": "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", "HVIAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAAAAAAAAHIAAKAAAAARBNQWUSCAAAADWFZ4ITAMAAAAAAAAEAAAAAZCEKRJJQGUCAAAAAAAAAAAAZ3G46EJYGAAAAAAAABAAAAACYCEAIAAAAAAABUAQIDEAAAAAAAAAABUABAAAAAEAAAAAIBEABAAAFAAAAAAAQAAAA7UAAAAAAAAAAAAAAAAAMGAIAAIAAAAAA": "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", "HUJAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAQAAAAAAQQGAAZAABQAAAAAAAACAAAAADYCAAIAAAAAWBRAAAABAAAAANAEIQCAAAAAAAAAAAAAUABQAAAAEAAAAAAAEEBQAAAAIYAAAAABAAAAAAAAAAAAAAAAAAAAAAAAYMAQAAQAAAAAA": "CAAAAExTS1MRAgAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKCWxheW91dChvZmZzZXQ9MTYpIGZsb2F0M3gzIHVtYXRyaXhfUzFfYzBfYzA7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGZsb2F0MiBsb2NhbENvb3JkOwpsYXlvdXQobG9jYXRpb24gPSAwKSBvdXQgZmxvYXQyIHZsb2NhbENvb3JkX1MwOwpsYXlvdXQobG9jYXRpb24gPSAxKSBvdXQgZmxvYXQyIHZUcmFuc2Zvcm1lZENvb3Jkc180X1MwOwp2b2lkIG1haW4oKSAKewoJLy8gUHJpbWl0aXZlIFByb2Nlc3NvciBRdWFkUGVyRWRnZUFBR2VvbWV0cnlQcm9jZXNzb3IKCXZsb2NhbENvb3JkX1MwID0gbG9jYWxDb29yZDsKCXNrX1Bvc2l0aW9uID0gcG9zaXRpb24ueHkwMTsKCXsKCQl2VHJhbnNmb3JtZWRDb29yZHNfNF9TMCA9IGZsb2F0M3gyKHVtYXRyaXhfUzFfYzBfYzApICogcG9zaXRpb24ueHkxOwoJfQp9CgAAAAAAAADLBAAAbGF5b3V0KGJpbmRpbmc9MCkgdW5pZm9ybSBzYW1wbGVyMkQgdVRleHR1cmVTYW1wbGVyXzBfUzA7CmxheW91dChiaW5kaW5nPTEpIHVuaWZvcm0gc2FtcGxlcjJEIHVUZXh0dXJlU2FtcGxlcl8wX1MxOwpsYXlvdXQgKGJpbmRpbmc9MCkgdW5pZm9ybSB1bmlmb3JtQnVmZmVyCnsKCWxheW91dChvZmZzZXQ9MCkgZmxvYXQ0IHNrX1JUQWRqdXN0OwoJbGF5b3V0KG9mZnNldD0xNikgZmxvYXQzeDMgdW1hdHJpeF9TMV9jMF9jMDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiB2bG9jYWxDb29yZF9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gZmxvYXQyIHZUcmFuc2Zvcm1lZENvb3Jkc180X1MwOwpsYXlvdXQobG9jYXRpb24gPSAwLCBpbmRleCA9IDApIG91dCBoYWxmNCBza19GcmFnQ29sb3I7CmhhbGY0IFRleHR1cmVFZmZlY3RfUzFfYzBfYzBfYzAoaGFsZjQgX2lucHV0KSAKewoJcmV0dXJuIHNhbXBsZSh1VGV4dHVyZVNhbXBsZXJfMF9TMSwgdlRyYW5zZm9ybWVkQ29vcmRzXzRfUzApLjAwMHI7Cn0KaGFsZjQgTWF0cml4RWZmZWN0X1MxX2MwX2MwKGhhbGY0IF9pbnB1dCkgCnsKCXJldHVybiBUZXh0dXJlRWZmZWN0X1MxX2MwX2MwX2MwKF9pbnB1dCk7Cn0KaGFsZjQgRGV2aWNlU3BhY2VfUzFfYzAoaGFsZjQgX2lucHV0KSAKewoJcmV0dXJuIE1hdHJpeEVmZmVjdF9TMV9jMF9jMChfaW5wdXQpOwp9CmhhbGY0IEJsZW5kX1MxKGhhbGY0IF9zcmMsIGhhbGY0IF9kc3QpIAp7CgkvLyBCbGVuZCBtb2RlOiBEc3RJbgoJcmV0dXJuIGJsZW5kX2RzdF9pbihEZXZpY2VTcGFjZV9TMV9jMChfc3JjKSwgX3NyYyk7Cn0Kdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzAgPSBoYWxmNCgxKTsKCWZsb2F0MiB0ZXhDb29yZDsKCXRleENvb3JkID0gdmxvY2FsQ29vcmRfUzA7CglvdXRwdXRDb2xvcl9TMCA9ICgoc2FtcGxlKHVUZXh0dXJlU2FtcGxlcl8wX1MwLCB0ZXhDb29yZCkgKiBoYWxmNCgxKSkpOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCWhhbGY0IG91dHB1dF9TMTsKCW91dHB1dF9TMSA9IEJsZW5kX1MxKG91dHB1dENvdmVyYWdlX1MwLCBoYWxmNCgxKSk7Cgl7CgkJLy8gWGZlciBQcm9jZXNzb3I6IFBvcnRlciBEdWZmCgkJc2tfRnJhZ0NvbG9yID0gb3V0cHV0Q29sb3JfUzAgKiBvdXRwdXRfUzE7Cgl9Cn0KAAAAAAABAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFQAAAABAAAAAAAAAAIAAAAdAAAAAAAAAAEAAAAdAAAACAAAAAEAAAAQAAAAAAAAAEYAAAABAAAAAQAAAAUAAAAAAAAAAQAAAAUAAAAAAAAAAQAAAAAAAAA=", "HUQACAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAADEAAGAAAAAAAAAIAAAAAPAIABAAAAACYGEAAAAEAAAABUARCAIAAAAAAAAAAAACQAGAAAAAQAAAAAAAQQGAAAABDAAAAAAEAAAAH5AAAAAAAAAAAAAAAAADBQCAAAAAAAAAA": "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", "HUJAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAQAAAAAAQQGAAZAABQAAAAAAAACAAAAADYCAAIAAAAAWBRAAAABAAAAANAEIQCAAAAAAAAAAAAAUABQAAAAEAAAAAAAEEBQAAAAKAAAAAABAAAAB7IAAAAAAAAAAAAAAAAAYMAQAAQAAAAAA": "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", "FAAQMYAAMAAAEADAAABAEYAAAICIAB5AABQAAAQAEDAACATAAABAGYAAAICSBYQCA4AAAAAAEABWAAAAAAAAAIAAAAAPAIAAAAAQAAAAFMDAAEAAAAAEAQ2AIQAAAAAAAAAAAAAAHIAAAAAAAIAAAAAQGIAAAAAAIYAAAAABAAAAB7IAAAAAAAAAAAAAAAAAYMAQAAAAAAAAA": "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", "HUQAAAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAACEA2CZQ4YGFAAAAAAGAAAAAAAAAAA5AAAAAAABAAAAACAZAAFAAAAAAAQAAAAAAAAAAAAAAAAAAAAAAAMGAIAAIAAAAAA": "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", "AYQA5AADQAAAOAEARAFQJAABBADIB7777777777777777777777767YAAAAAAAAAAAAOQAAAAAAAQAAAABAMQAAAABDAAAAAAEAAAAH5AAAAAAAAAAAAAAAAADBQCAAAAAAAAAA": "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", "AZAQ5AADQAAAOAEARAFQJAABBADIB7777777777777777777777767YAAAAAAAAAACAMVQGOCU7TCAAAACAOSKJ7FYFAAAAAAEAAAAAGAAAABRHUYG4YGAAAAAAIXSG7B4AAAAAQAAAAAIBDU2T7ZOBIAAAAABAAAAABQAAAAAINGB7HBYBAAAAAFQRH6PYAAAAIAAAAACAAZRXUYG4YGAAAAAAAAAAAWARLBCH47UAAAAAAAEAAAABSASF4RXYPAAAAAAAAAAAKAAYAAAACAAAAAAACCAYAIYAAAAABAAAAB7IAAAAAAAAAAAAAAAAAYMAQAAAAAAAAA": "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", "HVJAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAABAAAAAABBAMABAAOAAAABAAAAAAABBAMAAAKAAAAAABAAAAAAAAAAAAAAAAAAQAAAAAYMAQAAQAAAAAA": "CAAAAExTS1PFAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDIpIGluIGZsb2F0MiBsb2NhbENvb3JkOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IG91dCBoYWxmNCB2Y29sb3JfUzA7CmxheW91dChsb2NhdGlvbiA9IDEpIG91dCBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmNvbG9yX1MwID0gY29sb3I7Cgl2bG9jYWxDb29yZF9TMCA9IGxvY2FsQ29vcmQ7Cglza19Qb3NpdGlvbiA9IHBvc2l0aW9uLnh5MDE7Cn0KAAAAAAAAAJkCAABsYXlvdXQoYmluZGluZz0wKSB1bmlmb3JtIHNhbXBsZXIyRCB1VGV4dHVyZVNhbXBsZXJfMF9TMDsKbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGZsYXQgaW4gaGFsZjQgdmNvbG9yX1MwOwpsYXlvdXQobG9jYXRpb24gPSAxKSBpbiBmbG9hdDIgdmxvY2FsQ29vcmRfUzA7CmxheW91dChsb2NhdGlvbiA9IDAsIGluZGV4ID0gMCkgb3V0IGhhbGY0IHNrX0ZyYWdDb2xvcjsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzA7CglvdXRwdXRDb2xvcl9TMCA9IHZjb2xvcl9TMDsKCWZsb2F0MiB0ZXhDb29yZDsKCXRleENvb3JkID0gdmxvY2FsQ29vcmRfUzA7CglvdXRwdXRDb2xvcl9TMCA9ICgoc2FtcGxlKHVUZXh0dXJlU2FtcGxlcl8wX1MwLCB0ZXhDb29yZCkgKiBvdXRwdXRDb2xvcl9TMCkpOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAAABAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGAAAAABAAAAAAAAAAMAAAAdAAAAAAAAAAEAAAAJAAAACAAAAAEAAAAdAAAADAAAAAEAAAAUAAAAAAAAAFAAAAABAAAAAQAAAAUAAAAAAAAAAQAAAAUAAAAAAAAAAQAAAAAAAAA=", "HUQACAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAAKAAYAAAACAAAAAAACCAYAABIAAAAAAEAAAAAAAAAAAAAAAAAAAAAAADBQCAAAAAAAAAA": "CAAAAExTS1NHAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDApIG91dCBoYWxmNCB2Y29sb3JfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmNvbG9yX1MwID0gY29sb3I7Cglza19Qb3NpdGlvbiA9IHBvc2l0aW9uLnh5MDE7Cn0KAAAAAACuAQAAbGF5b3V0IChiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMCwgaW5kZXggPSAwKSBvdXQgaGFsZjQgc2tfRnJhZ0NvbG9yOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmNvbG9yX1MwOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAVAAAAAEAAAAAAAAAAgAAAB0AAAAAAAAAAQAAAAkAAAAIAAAAAQAAAAwAAAAAAAAAUAAAAAEAAAABAAAABQAAAAAAAAABAAAABQAAAAAAAAABAAAAAAAAAA=="}}
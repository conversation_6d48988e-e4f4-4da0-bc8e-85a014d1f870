library equipment;

import 'package:common/network/client.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:equipment/UI/clp/clp.dart';
import 'package:equipment/UI/feedback/feedback_screen.dart';
import 'package:equipment/UI/user_info/user_info_screen.dart';
import 'package:equipment/UI/widgets_listing_page/equipment_widgets_listing_page.dart';
import 'package:equipment/blocs/clp/clp_bloc.dart';
import 'package:equipment/blocs/feedback/feedback_bloc.dart';
import 'package:equipment/blocs/user_info/user_info_bloc.dart';
import 'package:equipment/blocs/widgets_listing_page/widgets_listing_bloc.dart';
import 'package:equipment/constants/constants.dart';
import 'package:equipment/network/clp_repository.dart';
import 'package:equipment/network/equipment_listing_page_repository.dart';
import 'package:equipment/network/equipment_user_info_repository.dart';
import 'package:equipment/network/feedback_repository.dart';
import 'package:flutter/material.dart';
import 'package:equipment/factory/widget_builder.dart'
    as equipment_action_handler;
import 'package:enum_to_string/enum_to_string.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'UI/stats/stats_screen.dart';
import 'blocs/stats/stats_bloc.dart';
import 'network/equipmentStats_repository.dart';
import 'UI/post_session/video_player_screen.dart';
import 'blocs/video_player/video_player_bloc.dart';
import 'blocs/video_player/video_player_repository.dart';

class Equipment {
  String _routeName(RouteNames routeName) {
    return '/${EnumToString.convertToString(routeName)}';
  }

  IWidgetBuilder get widgetBuilder {
    return equipment_action_handler.WidgetBuilder();
  }

  Map<String, WidgetBuilder> getRoutes(
      NetworkClient networkClient, Function onClose) {
    EquipmentCLPRepository equipmentCLPRepository =
        EquipmentCLPRepository(networkClient);
    EquipmentFeedbackRepository equipmentFeedbackRepository =
        EquipmentFeedbackRepository(networkClient);
    EquipmentStatsRepository equipmentStatsRepository =
        EquipmentStatsRepository(networkClient);
    VideoPlayerRepository videoPlayerRepository =
        VideoPlayerRepository(networkClient);
    EquipmentWidgetListingPageRepository equipmentWidgetListingPageRepository =
        EquipmentWidgetListingPageRepository(networkClient);
    EquipmentUserInfoRepository equipmentUserInfoRepository =
        EquipmentUserInfoRepository(networkClient);
    return {
      _routeName(RouteNames.equipmentclp): (_) => BlocProvider(
            create: (context) =>
                EquipmentCLPBloc(repository: equipmentCLPRepository),
            child: const EquipmentCLP(),
          ),
      _routeName(RouteNames.equipmentfeedback): (_) => BlocProvider(
            create: (context) =>
                EquipmentFeedbackBloc(repository: equipmentFeedbackRepository),
            child: const FeedbackScreen(),
          ),
      _routeName(RouteNames.fl_equipmentstats): (_) => BlocProvider(
            create: (context) =>
                EquipmentStatsBloc(repository: equipmentStatsRepository),
            child: const StatsScreen(),
          ),
      _routeName(RouteNames.equipmentvideoplayer): (_) => BlocProvider(
            create: (context) =>
                EquipmentVideoPlayerBloc(repository: videoPlayerRepository),
            child: const SessionVideoPlayer(),
          ),
      _routeName(RouteNames.cultrowwidgetslistpage): (_) => BlocProvider(
            create: (context) => EquipmentWidgetListingPageBloc(
                repository: equipmentWidgetListingPageRepository),
            child: const EquipmentWidgetsListingPage(),
          ),
      _routeName(RouteNames.equipmentuserinfo): (_) => BlocProvider(
            create: (context) =>
                EquipmentUserInfoBloc(repository: equipmentUserInfoRepository),
            child: const EquipmentUserInfoScreen(),
          ),
    };
  }
}

import 'package:common/ui/widget_builder.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:equipment/UI/clp/widgets/cult_row_upcoming_class_widget.dart';
import 'package:flutter/material.dart';

class WidgetBuilder implements IWidgetBuilder {
  @override
  Widget? buildWidget(widgetData, {BuilderInfo? builderInfo}) {
    if (widgetData['widgetType'] == null) return null;
    WidgetTypes? widgetType =
        EnumToString.fromString(WidgetTypes.values, widgetData['widgetType']);

    if (widgetType != null) {
      WidgetInfo widgetInfo = widgetData['widgetMetric'] != null
          ? WidgetInfo.fromJson(widgetData['widgetMetric'], widgetType)
          : WidgetInfo(
              widgetType: widgetType,
              widgetMetric: WidgetMetric(widgetName: "", widgetId: ""));

      switch (widgetType) {
        case WidgetTypes.CULT_ROW_UPCOMING_CLASSES_WIDGET:
          return CultRowUpcomingClassWidget(
              widgetData: CultRowUpcomingClassWidgetData.fromJson(
                  widgetType: widgetType,
                  widgetInfo: widgetInfo,
                  widgetData: widgetData));
      }
      return null;
    }
  }
}

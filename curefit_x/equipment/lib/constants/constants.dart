import 'package:flutter/material.dart';

enum RouteNames {
  equipmentclp,
  equipmentfeedback,
  cultrowwidgetslistpage,
  fl_equipmentfeedback,
  fl_equipmentstats,
  equipmentvideoplayer,
  equipmentuserinfo
}

const Color themeColor = Color.fromRGBO(56, 65, 102, 1.0);

const feedbackScreenTitle = "How was your session?";
const SUBMIT = "SUBMIT";
const PlaceHolderText = "Tell us more...";

const statsScreenTitle = "Session Stats";
const NEXT = "NEXT";

const successfulFeedbackText = "Feedback Submitted Successfully...";
const successfulInfoUploadTest = "Info Updated Successfully...";

const exitQuestion = "Are you sure you want to exit this session?";
const genderQuestion = "Please select your gender";
const dateOfBirthQuestion = "What is your date of birth?";
const dateOfBirthHintText = "Select your birth date";
const weightQuestion = "What is your current weight?";
const heightQuestion = "What is your current height?";
const previous = "PREVIOUS";
const next = "NEXT";
const live = "LIVE";

import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/ble/state.dart';
import 'package:common/constants/analytics_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

import 'events.dart';
import 'model.dart';

class VideoPLayerUtils {
  static String getFormatedDate(DateTime? dateToConvert) {
    if (dateToConvert != null) {
      final DateFormat formatter = DateFormat('dd-MM-yyyy HH:mm:ss a');
      final String formatted = formatter.format(dateToConvert);
      return formatted;
    }
    return "";
  }

  static String getTimeDifference(DateTime? startDate, DateTime? endDate) {
    if (startDate != null && endDate != null) {
      final difference = endDate.difference(startDate).inSeconds;
      return difference.toString();
    }
    return "0";
  }

  static bool isWorkOutGreaterThanMinTimeSpecified(DateTime? startDate) {
    const timeInSecond = 300; // min time for workout
    final endDate = DateTime.now();
    if (startDate != null) {
      final difference = endDate.difference(startDate).inSeconds;
      if (difference > timeInSecond) {
        return true;
      }
    }
    return false;
  }

  static SubmitClassDetailEvent getSubmitClassDetailEvent(
    BuildContext context,
    VideoPlayerScreenArguments? videoPlayerScreenArguments,
    BleDeviceData? bleDeviceData,
    ResistanceModel resistanceModel,
    StrideModel strideModel,
  ) {
    DateTime endTime = DateTime.now();
    String userSessionDuration = VideoPLayerUtils.getTimeDifference(
        videoPlayerScreenArguments?.startTime, endTime);

    var event = SubmitClassDetailEvent(
      challengeId: videoPlayerScreenArguments?.challenge_id,
      userStartTime: VideoPLayerUtils.getFormatedDate(
          videoPlayerScreenArguments?.startTime),
      userEndTime: VideoPLayerUtils.getFormatedDate(endTime),
      trainersEmail: videoPlayerScreenArguments?.trainers_email,
      workoutId: videoPlayerScreenArguments?.workout_id,
      streamType: videoPlayerScreenArguments?.stream_type,
      calorie: bleDeviceData?.caloriesValue ?? "",
      distance: bleDeviceData?.distanceValue ?? "",
      power: bleDeviceData?.powerValue ?? "",
      avgRpm: bleDeviceData?.frequencyValue ?? "",
      avgResistance:
          resistanceModel.avgResistance(userSessionDuration).toStringAsFixed(2),
      userSessionDuration: userSessionDuration,
      challengeType: videoPlayerScreenArguments?.challengeType,
      avgStrides: strideModel.avgStride(userSessionDuration).toStringAsFixed(2),
    );

    AnalyticsRepository analyticsRepository =
        RepositoryProvider.of<AnalyticsRepository>(context);
    analyticsRepository.logButtonClickEvent(extraInfo: {
      "vertical": vertical_d2c,
      "actionType": "workout_performed",
      "buttonType": "submit_class_detail",
      "pageId": "d2c_video_player",
      "pageName": "d2c_video_player",
      "title": event.workoutId,
      "subtitle": event.trainersEmail,
      "productId": event.challengeId,
      "productType": event.challengeType,
      "productName": event.streamType,
      "text": {
        "user_start_time": event.userStartTime,
        "user_end_time": event.userEndTime,
        "calorie": event.calorie,
        "distance": event.distance,
        "power": event.power,
        "avg_rpm": event.avgRpm,
        "avg_resistance": event.avgResistance,
        "user_session_duration": event.userSessionDuration,
        "challenge_type": event.challengeType,
        "avg_strides": event.avgStrides
      }.toString(),
    });

    return event;
  }
}

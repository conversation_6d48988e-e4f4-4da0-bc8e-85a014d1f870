import 'package:equipment/blocs/video_player/state.dart';

abstract class EquipmentVideoPlayerEvent {
  EquipmentVideoPlayerEvent() : super();
}

class VideoPlayerEvent extends EquipmentVideoPlayerEvent {
  final Duration? position;
  final Duration? totalDuration;
  final bool isLive;
  final bool isBuffering;
  VideoPlayerEvent(
      {this.position,
      this.totalDuration,
      required this.isLive,
      required this.isBuffering})
      : super();

  @override
  String toString() => 'LoadEquipmentFeedbackEvent';
}

class InternetStatusEvent extends EquipmentVideoPlayerEvent {
  final bool isInternetAvailable;
  InternetStatusEvent({required this.isInternetAvailable});
  @override
  String toString() => 'InternetStatusEvent';
}

class BluetoothEvent extends EquipmentVideoPlayerEvent {
  final bool isBluetoothDeviceAvailable;
  BluetoothEvent({required this.isBluetoothDeviceAvailable});
  @override
  String toString() => 'BluetoothEvent';
}

class LeaderboardEvent extends EquipmentVideoPlayerEvent {
  LeaderBoardModel leaderBoardModel;
  LeaderboardEvent({required this.leaderBoardModel});
  @override
  String toString() => 'LeaderboardEvent';
}

class BackPressEvent extends EquipmentVideoPlayerEvent {
  final bool isBackPressed;
  BackPressEvent({required this.isBackPressed});
  @override
  String toString() => 'BackPressEvent';
}

class ShowLoaderEvent extends EquipmentVideoPlayerEvent {
  final bool showLoader;
  ShowLoaderEvent({required this.showLoader});
  @override
  String toString() => 'ShowLoaderEvent';
}

class SubmitClassDetailEvent extends EquipmentVideoPlayerEvent {
  String? challengeId;
  String? userStartTime;
  String? userEndTime;
  String? trainersEmail;
  String? workoutId;
  String? streamType;
  String? calorie;
  String? distance;
  String? power;
  String? avgRpm;
  String? avgResistance;
  String? userSessionDuration;
  String? challengeType;
  String? avgStrides;
  SubmitClassDetailEvent(
      {this.challengeId,
      this.userStartTime,
      this.userEndTime,
      this.trainersEmail,
      this.workoutId,
      this.streamType,
      this.calorie,
      this.distance,
      this.power,
      this.avgRpm,
      this.avgResistance,
      this.userSessionDuration,
      this.challengeType,
        this.avgStrides
      });
  @override
  String toString() => 'SubmitClassDetailEvent';
}

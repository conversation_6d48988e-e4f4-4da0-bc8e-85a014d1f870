/*
  timestamp
  bluetoothConnectivityState
  leaderboardState
  InternetConnectivityState
  VideoPlayerCurrentState
  VideoPlayerBackState
  VideoPLayerEndSessionState
*/

import 'dart:ffi';

class EquipmentVideoPlayerState {
  Duration? position;
  Duration? totalDuration;
  bool? isLive;
  bool? isInternetAvailable;
  bool? isBluetoothDeviceAvailable;
  bool? isBackPressed;
  bool? showLoader;
  bool? isBuffering;
  BluetoothDataModel? distanceModel = BluetoothDataModel();
  BluetoothDataModel? calorieModel = BluetoothDataModel();
  BluetoothDataModel? powerModel = BluetoothDataModel();
  BluetoothDataModel? avgRpmModel = BluetoothDataModel();
  BluetoothDataModel? avgResistance = BluetoothDataModel();

  LeaderBoardModel? leaderBoardModel;

  EquipmentVideoPlayerState(
      {this.position,
      this.totalDuration,
      this.isLive,
      this.isInternetAvailable = true,
      this.isBluetoothDeviceAvailable,
      this.isBackPressed = false,
      this.showLoader = false,
      this.isBuffering = false,
      this.distanceModel ,
        this.calorieModel,
        this.powerModel,
        this.avgRpmModel,
        this.avgResistance,
      this.leaderBoardModel});

  String calculateTime() {
    if (position == null || totalDuration == null) {
      return "";
    }
    String currentTime =
        "${position?.inHours.toString().padLeft(2, '0')}:${position?.inMinutes.remainder(60).toString().padLeft(2, '0')}:${(position?.inSeconds.remainder(60).toString().padLeft(2, '0'))}";
    String totalTime =
        "${totalDuration?.inHours.toString().padLeft(2, '0')}:${totalDuration?.inMinutes.remainder(60).toString().padLeft(2, '0')}:${(totalDuration?.inSeconds.remainder(60).toString().padLeft(2, '0'))}";
    return "$currentTime/$totalTime";
  }

  bool isVideoPlaybackFinished() {
      if (position == null || totalDuration == null || position == Duration(seconds: 0) || totalDuration == Duration(seconds: 0)) {
        return false;
      }
      if (position! >= totalDuration!) {
        return true;
      } else {
        return false;
      }
  }

  EquipmentVideoPlayerState copyWith({
    Duration? position,
    Duration? totalDuration,
    bool? isInternetAvailable,
    bool? isLive,
    bool? isBluetoothDeviceAvailable,
    bool? isBackPressed,
    bool? showLoader,
    bool? isBuffering,
    BluetoothDataModel? distanceModel,
    BluetoothDataModel? calorieModel,
    BluetoothDataModel? powerModel,
    BluetoothDataModel? avgRpmModel,
    BluetoothDataModel? avgResistance,
    LeaderBoardModel? leaderBoardModel,
  }) {
    return EquipmentVideoPlayerState(
      position: position ?? this.position,
      totalDuration: totalDuration ?? this.totalDuration,
      isInternetAvailable: isInternetAvailable ?? this.isInternetAvailable,
      isLive: isLive ?? this.isLive,
      isBluetoothDeviceAvailable:
          isBluetoothDeviceAvailable ?? this.isBluetoothDeviceAvailable,
      isBackPressed: isBackPressed ?? this.isBackPressed,
      showLoader: showLoader ?? this.showLoader,
      isBuffering: isBuffering ?? this.isBuffering,
      distanceModel: distanceModel ?? this.distanceModel,
      calorieModel: calorieModel ?? this.calorieModel,
      powerModel: powerModel ?? this.powerModel,
      avgRpmModel: avgRpmModel ?? this.avgRpmModel,
      avgResistance: avgResistance ?? this.avgResistance,
      leaderBoardModel: leaderBoardModel ?? this.leaderBoardModel,
    );
  }

  // @override
  // List<Object> get props => [position!, totalDuration!, isLive!,bleList!,leaderBoardModel!,];

  @override
  String toString() => 'EquipmentVideoPlayerDuration';
}

class EquipmentVideoPlayerFailed extends EquipmentVideoPlayerState {
  final String? error;
  EquipmentVideoPlayerFailed([this.error]) : super();

  @override
  String toString() => 'EquipmentVideoPlayerFailed';
}

class VideoPlayerClassDetailSubmited extends EquipmentVideoPlayerState {
  VideoPlayerClassDetailSubmited() : super();

  @override
  String toString() => 'VideoPlayerClassDetailSubmited';
}

// class BluetoothDataModelList {
//   List<BluetoothDataModel>? bleDataList;
// }

class BluetoothDataModel {
  late String? iconName;
  late Double? unitValue;
  late String? unit;
  BluetoothDataModel({
    this.iconName,
    this.unitValue,
    this.unit,
  });
}

class LeaderBoardModel {
  UserModel? currentUser;
  List<UserModel>? overallLeaderboard;

  LeaderBoardModel({this.currentUser, this.overallLeaderboard});

  LeaderBoardModel.fromJson(Map<String, dynamic> json) {
    currentUser = json['current_user'] != null
        ? new UserModel.fromJson(json['current_user'])
        : null;
    if (json['overall_leaderboard'] != null) {
      overallLeaderboard = <UserModel>[];
      json['overall_leaderboard'].forEach((v) {
        overallLeaderboard!.add(new UserModel.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.currentUser != null) {
      data['current_user'] = this.currentUser!.toJson();
    }
    if (this.overallLeaderboard != null) {
      data['overall_leaderboard'] =
          this.overallLeaderboard!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class UserModel {
  String? rank;
  String? userName;
  String? score;
  String? city;

  UserModel({this.rank, this.userName, this.score, this.city});

  UserModel.fromJson(Map<String, dynamic> json) {
    rank = json['rank'];
    userName = json['user_name'];
    score = json['score'];
    city = json['city'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['rank'] = this.rank;
    data['user_name'] = this.userName;
    data['score'] = this.score;
    data['city'] = this.city;
    return data;
  }
}

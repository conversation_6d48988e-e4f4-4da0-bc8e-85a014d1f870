

import 'package:flutter/foundation.dart';

class UserEntry {
  final int rank;
  final String score;
  final String name;
  final String city;

  const UserEntry({
    required this.rank,
    required this.score,
    required this.name,
    required this.city,
  });

  factory UserEntry.fromJson(Map<String, dynamic> json) {
    return UserEntry(
      rank: json['rank'],
      score: json['score'],
      name: json['user_name'],
      city: json['city'],
    );
  }
}

class LeaderboardResponse {
  final UserEntry user;
  final List<UserEntry> leaderboard;

  const LeaderboardResponse({
    required this.user,
    required this.leaderboard,
  });

  factory LeaderboardResponse.fromJson(Map<String, dynamic> json) {
    return LeaderboardResponse(
        user: UserEntry.fromJson(json['current_user']),
        leaderboard: json['overall_leaderboard']
            .map<UserEntry>((item) => UserEntry.fromJson(item))
            .toList());
  }
}


class VideoPlayerScreenArguments {
  String productId = '';
  String challenge_id = '';
  String trainers_email = '';
  String workout_id = '';
  String stream_type = '';
  String media_id = '';
  String sessionDuration = '1';
  String challengeType = '';
  DateTime startTime = DateTime.now();
  String userName = '';
  String userEmail = '';
  String height = '';
  String weight = '';
  String gender = '';
  String birthDate = '';
  String userID = '';
  VideoPlayerScreenArguments(Map<String, dynamic> params) {
    productId = params["productId"] ?? "";
    challenge_id = params["challenge_id"] ?? "";
    trainers_email = params["trainers_email"] ?? "";
    workout_id = params["workout_id"] ?? "";
    stream_type = params["stream_type"] ?? "";
    media_id = params["media_id"] ?? "";
    sessionDuration = params["duration"] ?? "";
    challengeType = params["challenge_type"] ?? "";
    startTime = DateTime.now();
    userName = params["user_name"] ?? "";
    userEmail = params["user_email"] ?? "";
    height = params["height"] ?? "";
    weight = params["weight"] ?? "";
    gender = params["gender"] ?? "";
    birthDate = params["birthDate"] ?? "";
    userID = params["user_id"] ?? "";
  }

  bool isLive() {
    if (stream_type.toLowerCase() == "Live".toLowerCase()) {
      return true;
    }
    return false;
  }

  String videoUrl() {
    if (isLive() == true) {
      return "https://cdn.jwplayer.com/live/events/$media_id.m3u8";
    }
    return "https://cdn.jwplayer.com/manifests/$media_id.m3u8";
  }

  Duration? getEndDuration() {
    try {
      final sessionTimeInMinute = int.parse(sessionDuration);
      return Duration(minutes: sessionTimeInMinute);
    } catch (e) {
      return null;
    }
  }
}

class ResistanceModel {
  double finalResValue = 0;
  final timeDiffValue = 0.5;

  void addResistance(String res) {
    final resD = double.tryParse(res);
    if (resD != null) {
        double resistance = timeDiffValue * resD;
        finalResValue += resistance;
    }
    if (kDebugMode) {
      print("===========================>------------------>$finalResValue");
    }
  }

  double avgResistance(String finalDuration) {
    final duration = double.tryParse(finalDuration);
    if (duration != null && duration > 0) {
      final double avgResistanceFinalValue = finalResValue / duration;
      if (kDebugMode) {
        print(
            "===========================>final resistance $finalResValue ------------------> duration $duration  avgResistanceFinalValue $avgResistanceFinalValue");
      }
      return avgResistanceFinalValue;
    }
    return 0;
  }
}

class StrideModel {
  double finalStrideValue = 0;
  final timeDiffValue = 0.5;

  void addStride(String stride) {
    final str = double.tryParse(stride);
    if (str != null) {
      double resistance = timeDiffValue * str;
      finalStrideValue += resistance;
    }
    if (kDebugMode) {
      print("===========================>------------------StrideModel >$finalStrideValue");
    }
  }

  double avgStride(String finalDuration) {
    final duration = double.tryParse(finalDuration);
    if (duration != null && duration > 0) {
      final double avgStrideFinalValue = finalStrideValue / duration;
      if (kDebugMode) {
        print(
            "===========================>------------------StrideModel>$finalStrideValue ------------------> duration $duration  avgResistanceFinalValue $avgStrideFinalValue");
      }
      return avgStrideFinalValue;
    }
    return 0;
  }
}

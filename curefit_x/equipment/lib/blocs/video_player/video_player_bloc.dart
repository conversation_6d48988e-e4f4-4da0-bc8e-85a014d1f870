import 'package:common/constants/common_constants.dart';
import 'package:common/network/client.dart';
import 'package:equipment/blocs/video_player/state.dart';
import 'package:equipment/blocs/video_player/video_player_repository.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'events.dart';

class EquipmentVideoPlayerBloc
    extends Bloc<EquipmentVideoPlayerEvent, EquipmentVideoPlayerState> {
  final VideoPlayerRepository repository;
  EquipmentVideoPlayerBloc({required this.repository})
      : super(EquipmentVideoPlayerState()) {
    on<VideoPlayerEvent>(_onTimeChange);
    on<InternetStatusEvent>(_onInternetConnectivityChange);
    on<BluetoothEvent>(_onBlueToothDataChange);
    on<LeaderboardEvent>(_onLeaderBoardChange);
    on<BackPressEvent>(_onBackPressed);
    on<SubmitClassDetailEvent>(_submitClassDetail);
    on<ShowLoaderEvent>(_onShowLoader);
  }

  void _onShowLoader(
      ShowLoaderEvent event, Emitter<EquipmentVideoPlayerState> emit) {
    emit(state.copyWith(
      showLoader: event.showLoader,
    ));
  }

  void _onTimeChange(
      VideoPlayerEvent event, Emitter<EquipmentVideoPlayerState> emit) {
    emit(state.copyWith(
        position: event.position,
        totalDuration: event.totalDuration,
        isLive: event.isLive,
        isBuffering: event.isBuffering));
  }

  void _onInternetConnectivityChange(
      InternetStatusEvent event, Emitter<EquipmentVideoPlayerState> emit) {
    emit(state.copyWith(
      isInternetAvailable: event.isInternetAvailable,
    ));
  }

  void _onBlueToothDataChange(
      BluetoothEvent event, Emitter<EquipmentVideoPlayerState> emit) {
    emit(state.copyWith(
      isBluetoothDeviceAvailable: event.isBluetoothDeviceAvailable,
    ));
  }

  void _onLeaderBoardChange(
      LeaderboardEvent event, Emitter<EquipmentVideoPlayerState> emit) {
    emit(state.copyWith(
      leaderBoardModel: event.leaderBoardModel,
    ));
  }

  void _onBackPressed(
      BackPressEvent event, Emitter<EquipmentVideoPlayerState> emit) {
    emit(state.copyWith(
      isBackPressed: event.isBackPressed,
    ));
  }

  Future<void> _submitClassDetail(SubmitClassDetailEvent event,
      Emitter<EquipmentVideoPlayerState> emit) async {
    try {
      emit(state.copyWith(
        showLoader: true,
      ));

      final response = await repository.endSession({
        "challenge_id": event.challengeId,
        "user_start_time": event.userStartTime,
        "user_end_time": event.userEndTime,
        "trainers_email": event.trainersEmail,
        "workout_id": event.workoutId,
        "stream_type": event.streamType,
        "calorie": event.calorie,
        "distance": event.distance,
        "power": event.power,
        "avg_rpm": event.avgRpm,
        "avg_resistance": event.avgResistance,
        "user_session_duration": event.userSessionDuration,
        "challenge_type": event.challengeType,
        "avg_strides": event.avgStrides
      });
      if (response == null || response == false) {
        emit(EquipmentVideoPlayerFailed(ERROR_MSG));
      } else if (response['success'] == true) {
        emit(VideoPlayerClassDetailSubmited());
      }
    } on NetworkException catch (exception) {
      emit(EquipmentVideoPlayerFailed(exception.subTitle));
    } catch (e) {
      emit(EquipmentVideoPlayerFailed(ERROR_MSG));
    }
  }
}

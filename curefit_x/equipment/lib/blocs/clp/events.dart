abstract class EquipmentCLPEvent {
  EquipmentCLPEvent() : super();
}

class LoadEquipmentCLPEvent extends EquipmentCLPEvent {
  final bool? showLoader;
  final String pageId;
  LoadEquipmentCLPEvent({this.showLoader, required this.pageId}) : super();

  @override
  String toString() => 'LoadEquipmentCLPEvent';
}

class ResetEquipmentCLPEvent extends EquipmentCLPEvent {
  @override
  String toString() => 'ResetEquipmentCLPEvent';
}

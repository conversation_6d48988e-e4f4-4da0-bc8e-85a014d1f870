import 'dart:convert';

import 'package:common/constants/common_constants.dart';
import 'package:common/network/client.dart';
import 'package:equipment/blocs/clp/events.dart';
import 'package:equipment/blocs/clp/state.dart';
import 'package:equipment/network/clp_repository.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:common/network/clp/clp_repository.dart';

class EquipmentCLPBloc extends Bloc<EquipmentCLPEvent, EquipmentCLPState> {
  final EquipmentCLPRepository repository;

  EquipmentCLPBloc({required this.repository}) : super(EquipmentCLPIdle()) {
    on<LoadEquipmentCLPEvent>((event, emit) async {
      await _mapEquipmentCLPToState(event, emit);
    });
  }

  Future<void> _mapEquipmentCLPToState(
      LoadEquipmentCLPEvent event, Emitter<EquipmentCLPState> emit) async {
    try {
      emit(EquipmentCLPLoading(showLoader: event.showLoader));
      final response = await repository.getClpPage(event.pageId, {});

      if (response == null) {
        emit(EquipmentCLPLoadFailed(ERROR_MSG));
      }

      emit(EquipmentCLPLoaded(
          widgets: response['body'], pageName: response['name']));
    } on NetworkException catch (exception) {
      emit(EquipmentCLPLoadFailed(exception.subTitle));
    } catch (e) {
      emit(EquipmentCLPLoadFailed(ERROR_MSG));
    }
  }
}

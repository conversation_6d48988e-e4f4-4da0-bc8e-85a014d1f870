abstract class EquipmentCLPState {
  EquipmentCLPState() : super();
}

class EquipmentCLPIdle extends EquipmentCLPState {
  @override
  String toString() => 'IdleState';
}

class EquipmentCLPLoading extends EquipmentCLPState {
  bool? showLoader;
  EquipmentCLPLoading({this.showLoader = true});

  @override
  String toString() => 'EquipmentCLPLoading';
}

class EquipmentCLPLoaded extends EquipmentCLPState {
  List widgets = [];
  String? pageName;
  EquipmentCLPLoaded({required this.widgets, this.pageName}) : super();

  @override
  String toString() => 'EquipmentCLPLoaded';
}

class EquipmentCLPLoadFailed extends EquipmentCLPState {
  final String? error;
  EquipmentCLPLoadFailed([this.error]) : super();

  @override
  String toString() => 'EquipmentCLPLoadFailed';
}

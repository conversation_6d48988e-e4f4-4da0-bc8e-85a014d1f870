abstract class EquipmentUserInfoEvent {
  EquipmentUserInfoEvent() : super();
}

class LoadEquipmentUserInfoEvent extends EquipmentUserInfoEvent {
  final bool? showLoader;
  LoadEquipmentUserInfoEvent({
    this.showLoader,
  }) : super();

  @override
  String toString() => 'LoadEquipmentUserInfoEvent';
}

class UserWeightEnteredEvent extends EquipmentUserInfoEvent {
  String weight;
  UserWeightEnteredEvent({required this.weight}) : super();
  @override
  String toString() => 'UserWeightEnteredEvent';
}

class UserHeightEnteredEvent extends EquipmentUserInfoEvent {
  String height;
  UserHeightEnteredEvent({required this.height}) : super();
  @override
  String toString() => 'UserHeightEnteredEvent';
}

class UserGenderEnteredEvent extends EquipmentUserInfoEvent {
  String gender;
  UserGenderEnteredEvent({required this.gender}) : super();
  @override
  String toString() => 'UserGenderEnteredEvent';
}

class UserDOBEnteredEvent extends EquipmentUserInfoEvent {
  String dob;
  UserDOBEnteredEvent({required this.dob}) : super();
  @override
  String toString() => 'UserDOBEnteredEvent';
}

class EquipmentUserInfoEnteredEvent extends EquipmentUserInfoEvent {
  String? gender;
  double? weight;
  double? height;
  EquipmentUserInfoEnteredEvent({this.gender, this.height, this.weight})
      : super();
  @override
  String toString() => 'EquipmentUserInfoEnteredEvent';
}

class ResetEquipmentUserInfoEvent extends EquipmentUserInfoEvent {
  @override
  String toString() => 'ResetEquipmentUserInfoEvent';
}

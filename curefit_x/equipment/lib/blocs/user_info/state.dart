abstract class EquipmentUserInfoState {
  EquipmentUserInfoState() : super();
}

class EquipmentUserInfoIdle extends EquipmentUserInfoState {
  @override
  String toString() => 'EquipmentUserInfoIdle';
}

class EquipmentUserInfoLoading extends EquipmentUserInfoState {
  bool? showLoader;
  EquipmentUserInfoLoading({this.showLoader = true});

  @override
  String toString() => 'EquipmentUserInfoLoading';
}

class EquipmentUserInfoLoaded extends EquipmentUserInfoState {
  bool isUserInfoUploadSuccessful;
  EquipmentUserInfoLoaded({required this.isUserInfoUploadSuccessful}) : super();

  @override
  String toString() => 'EquipmentUserInfoLoaded';
}

class EquipmentUserInfoEntered extends EquipmentUserInfoState {
  EquipmentUserInfoEntered() : super();

  @override
  String toString() => 'EquipmentUserInfoEntered';
}

class EquipmentInvalidUserInfo extends EquipmentUserInfoState {
  String? errorMsg;
  EquipmentInvalidUserInfo([this.errorMsg]) : super();
  @override
  String toString() => 'EquipmentInvalidUserInfo';
}

class EquipmentUserInfoUpdateFailed extends EquipmentUserInfoState {
  final String? error;
  EquipmentUserInfoUpdateFailed([this.error]) : super();

  @override
  String toString() => 'EquipmentUserInfoUpdateFailed';
}

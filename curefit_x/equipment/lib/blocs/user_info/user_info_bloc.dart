import 'package:common/constants/common_constants.dart';
import 'package:common/network/client.dart';
import 'package:equipment/blocs/user_info/events.dart';
import 'package:equipment/blocs/user_info/state.dart';
import 'package:equipment/network/equipment_user_info_repository.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class EquipmentUserInfoBloc
    extends Bloc<EquipmentUserInfoEvent, EquipmentUserInfoState> {
  final EquipmentUserInfoRepository repository;
  String? gender;
  String? dob;
  String? weight;
  String? height;

  EquipmentUserInfoBloc({required this.repository})
      : super(EquipmentUserInfoIdle()) {
    on<LoadEquipmentUserInfoEvent>((event, emit) async {
      await _mapEquipmentUserInfoToState(event, emit);
    });

    on<UserWeightEnteredEvent>((event, emit) {
      weight = event.weight;
    });
    on<UserHeightEnteredEvent>((event, emit) {
      height = event.height;
    });
    on<UserGenderEnteredEvent>((event, emit) {
      gender = event.gender;
    });
    on<UserDOBEnteredEvent>((event, emit) {
      dob = event.dob;
    });
  }

  Future<void> _mapEquipmentUserInfoToState(EquipmentUserInfoEvent event,
      Emitter<EquipmentUserInfoState> emit) async {
    try {
      if (isInValidUserInfo()) {
        emit(EquipmentInvalidUserInfo('Please fill all the details.'));
        return;
      }
      emit(EquipmentUserInfoLoading());
      bool? response = await repository.uploadUserInfo({
        "height": double.parse(height!),
        "weight": double.parse(weight!),
        "birthDate": dob,
        "gender": gender
      });
      if (response == null || response == false) {
        emit(EquipmentUserInfoUpdateFailed(ERROR_MSG));
      }
      emit(EquipmentUserInfoLoaded(
          isUserInfoUploadSuccessful: response ?? false));
    } on NetworkException catch (exception) {
      emit(EquipmentUserInfoUpdateFailed(exception.subTitle));
    } catch (e) {
      emit(EquipmentUserInfoUpdateFailed(ERROR_MSG));
    }
  }

  bool isInValidUserInfo() {
    if (gender == null || dob == null || weight == null || height == null) {
      return true;
    } else if (dob!.isEmpty) {
      return true;
    } else if (weight!.isEmpty) {
      return true;
    } else if (height!.isEmpty) {
      return true;
    } else if (gender!.isEmpty) {
      return true;
    }
    return false;
  }
}

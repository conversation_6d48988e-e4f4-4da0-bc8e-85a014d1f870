abstract class CultRowUpcomingClassWidgetState {
  CultRowUpcomingClassWidgetState() : super();
}

class CultRowUpcomingClassWidgetIdle extends CultRowUpcomingClassWidgetState {
  CultRowUpcomingClassWidgetIdle() : super();
  @override
  String toString() => 'CultRowUpcomingClassWidgetIdle';
}

class CultRowUpcomingClassWidgetLoading
    extends CultRowUpcomingClassWidgetState {
  CultRowUpcomingClassWidgetLoading() : super();
  @override
  String toString() => 'CultRowUpcomingClassWidgetLoading';
}

class CultRowUpcomingClassWidgetBookingSuccess
    extends CultRowUpcomingClassWidgetState {
  String? buttonText;
  CultRowUpcomingClassWidgetBookingSuccess({this.buttonText = 'BOOKED'});
  @override
  String toString() => 'CultRowUpcomingClassWidgetBookingSuccess';
}

class CultRowUpcomingClassWidgetBookingFailed
    extends CultRowUpcomingClassWidgetState {
  String? errorMsg;
  CultRowUpcomingClassWidgetBookingFailed([this.errorMsg]) : super();
  @override
  String toString() => 'CultRowUpcomingClassWidgetBookingFailed';
}

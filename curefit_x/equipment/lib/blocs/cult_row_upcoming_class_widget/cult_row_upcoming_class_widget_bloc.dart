import 'package:common/constants/common_constants.dart';
import 'package:common/network/client.dart';
import 'package:equipment/blocs/cult_row_upcoming_class_widget/cult_row_upcoming_class_widget_event.dart';
import 'package:equipment/blocs/cult_row_upcoming_class_widget/cult_row_upcoming_class_widget_state.dart';
import 'package:equipment/network/cultrow_upcoming_class_book_repository.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class CultRowUpcomingClassBloc extends Bloc<CultRowUpcomingClassWidgetEvent,
    CultRowUpcomingClassWidgetState> {
  final CultRowUpcomingClassBookRepository repository;

  CultRowUpcomingClassBloc({required this.repository})
      : super(CultRowUpcomingClassWidgetIdle()) {
    on<BookCultRowUpcomingClassEvent>((event, emit) async {
      await _mapCultRowClassBookingToState(event, emit);
    });
  }

  Future<void> _mapCultRowClassBookingToState(
      BookCultRowUpcomingClassEvent event,
      Emitter<CultRowUpcomingClassWidgetState> emit) async {
    try {
      emit(CultRowUpcomingClassWidgetLoading());
      final response = await repository.bookCultRowClass(event.meta);
      if (response == null) {
        emit(CultRowUpcomingClassWidgetBookingFailed(ERROR_MSG));
      }
      if (response['classBookedSuccessfully'] == true) {
        emit(CultRowUpcomingClassWidgetBookingSuccess(buttonText: 'BOOKED'));
      } else if (response['classBookedSuccessfully'] == false) {
        emit(CultRowUpcomingClassWidgetBookingFailed(ERROR_MSG));
      } else {
        emit(CultRowUpcomingClassWidgetBookingSuccess(buttonText: 'BOOKED'));
      }
    } on NetworkException catch (exception) {
      emit(CultRowUpcomingClassWidgetBookingFailed(exception.subTitle));
    } catch (e) {
      emit(CultRowUpcomingClassWidgetBookingFailed(ERROR_MSG));
    }
  }
}

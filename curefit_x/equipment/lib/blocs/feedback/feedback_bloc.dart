import 'package:common/constants/common_constants.dart';
import 'package:common/network/client.dart';
import 'package:equipment/blocs/feedback/events.dart';
import 'package:equipment/blocs/feedback/state.dart';
import 'package:equipment/network/feedback_repository.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class EquipmentFeedbackBloc
    extends Bloc<EquipmentFeedbackEvent, EquipmentFeedbackState> {
  final EquipmentFeedbackRepository repository;
  List<int> feedback = List.generate(4, (index) => -1);
  EquipmentFeedbackBloc({required this.repository})
      : super(EquipmentFeedbackIdle()) {
    on<FeedbackSelectedEvent>((event, emit) {
      feedback[event.row] = event.column;
      emit(EquipmentFeedbackSelected(feedback: feedback));
    });

    on<LoadEquipmentFeedbackEvent>((event, emit) async {
      await _mapEquipmentCLPToState(event, emit);
    });
  }

  Future<void> _mapEquipmentCLPToState(LoadEquipmentFeedbackEvent event,
      Emitter<EquipmentFeedbackState> emit) async {
    try {
      if (feedback[0] == -1 &&
          feedback[1] == -1 &&
          feedback[2] == -1 &&
          feedback[3] == -1) {
        emit(EquipmentInvalidFeedback(
            'Invalid Input.\nSelect at least one option.'));
        return;
      }
      emit(EquipmentFeedbackLoading(
          showLoader: event.showLoader, feedback: feedback));
      final response = await repository.uploadFeedback({
        'trainer_rating': feedback[0] != -1 ? feedback[0] + 1 : 0,
        'music_rating': feedback[1] != -1 ? feedback[1] + 1 : 0,
        'app_rating': feedback[2] != -1 ? feedback[2] + 1 : 0,
        'bike_rating': feedback[3] != -1 ? feedback[3] + 1 : 0,
        'leave_early_reason': event.feedbackString,
        'challenge_id': event.challengeId,
        'challenge_type': event.challengeType,
        'workout_id': event.workoutId,
        'trainers_email': event.trainersEmail
      });

      if (response == null || response == false) {
        emit(EquipmentFeedbackFailed(ERROR_MSG));
      }
      emit(EquipmentFeedbackLoaded(feedbackStatus: response));
    } on NetworkException catch (exception) {
      emit(EquipmentFeedbackFailed(exception.subTitle));
    } catch (e) {
      emit(EquipmentFeedbackFailed(ERROR_MSG));
    }
  }
}

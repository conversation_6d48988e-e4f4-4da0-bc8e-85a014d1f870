abstract class EquipmentFeedbackEvent {
  EquipmentFeedbackEvent() : super();
}

class LoadEquipmentFeedbackEvent extends EquipmentFeedbackEvent {
  final bool? showLoader;
  final String? feedbackString;
  final String challengeId;
  final String challengeType;
  final String trainersEmail;
  final String workoutId;
  LoadEquipmentFeedbackEvent(
      {this.showLoader,
      this.feedbackString,
      required this.challengeType,
      required this.challengeId,
      required this.trainersEmail,
      required this.workoutId})
      : super();

  @override
  String toString() => 'LoadEquipmentFeedbackEvent';
}

class FeedbackSelectedEvent extends EquipmentFeedbackEvent {
  int row;
  int column;
  FeedbackSelectedEvent({required this.row, required this.column});
  @override
  String toString() => 'FeedbackSelectedEvent';
}

class ResetEquipmentFeedbackEvent extends EquipmentFeedbackEvent {
  @override
  String toString() => 'ResetEquipmentFeedbackEvent';
}

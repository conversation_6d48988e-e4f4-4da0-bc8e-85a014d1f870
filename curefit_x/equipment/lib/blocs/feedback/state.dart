abstract class EquipmentFeedbackState {
  EquipmentFeedbackState() : super();
}

class EquipmentFeedbackIdle extends EquipmentFeedbackState {
  @override
  String toString() => 'IdleState';
}

class EquipmentFeedbackLoading extends EquipmentFeedbackState {
  bool? showLoader;
  List<int> feedback;
  EquipmentFeedbackLoading({this.showLoader = true, required this.feedback});

  @override
  String toString() => 'EquipmentFeedbackLoading';
}

class EquipmentFeedbackLoaded extends EquipmentFeedbackState {
  bool feedbackStatus;
  EquipmentFeedbackLoaded({required this.feedbackStatus}) : super();

  @override
  String toString() => 'EquipmentFeedbackLoaded';
}

class EquipmentFeedbackSelected extends EquipmentFeedbackState {
  List<int> feedback;
  EquipmentFeedbackSelected({required this.feedback}) : super();

  @override
  String toString() => 'EquipmentFeedbackSelected';
}

class EquipmentInvalidFeedback extends EquipmentFeedbackState {
  String? errorMsg;
  EquipmentInvalidFeedback([this.errorMsg]) : super();
  @override
  String toString() => 'EquipmentInvalidFeedback';
}

class EquipmentFeedbackFailed extends EquipmentFeedbackState {
  final String? error;
  EquipmentFeedbackFailed([this.error]) : super();

  @override
  String toString() => 'EquipmentFeedbackFailed';
}

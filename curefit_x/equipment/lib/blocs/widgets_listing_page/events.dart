abstract class EquipmentWidgetListingPageEvent {
  EquipmentWidgetListingPageEvent() : super();
}

class LoadEquipmentWidgetListingPageEvent
    extends EquipmentWidgetListingPageEvent {
  final bool? showLoader;
  final String pageType;
  LoadEquipmentWidgetListingPageEvent({this.showLoader, required this.pageType})
      : super();

  @override
  String toString() => 'LoadEquipmentWidgetListingPageEvent';
}

class ResetEquipmentCLPEvent extends EquipmentWidgetListingPageEvent {
  @override
  String toString() => 'ResetEquipmentCLPEvent';
}

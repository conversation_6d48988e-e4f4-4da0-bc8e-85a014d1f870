import 'package:common/constants/common_constants.dart';
import 'package:common/network/client.dart';
import 'package:equipment/blocs/widgets_listing_page/events.dart';
import 'package:equipment/blocs/widgets_listing_page/state.dart';
import 'package:equipment/network/equipment_listing_page_repository.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class EquipmentWidgetListingPageBloc extends Bloc<
    EquipmentWidgetListingPageEvent, EquipmentWidgetListingPageState> {
  final EquipmentWidgetListingPageRepository repository;

  EquipmentWidgetListingPageBloc({required this.repository})
      : super(EquipmentWidgetListingPageIdle()) {
    on<LoadEquipmentWidgetListingPageEvent>((event, emit) async {
      await _mapEquipmentWidgetListingPageToState(event, emit);
    });
  }

  Future<void> _mapEquipmentWidgetListingPageToState(
      LoadEquipmentWidgetListingPageEvent event,
      Emitter<EquipmentWidgetListingPageState> emit) async {
    try {
      emit(EquipmentWidgetListingPageLoading(showLoader: event.showLoader));
      final response =
          await repository.getEquipmentWidgetListingPage(event.pageType);

      if (response == null) {
        emit(EquipmentWidgetListingPageLoadFailed(ERROR_MSG));
      }

      emit(EquipmentWidgetListingPageLoaded(
          widgets: response['widgets'], pageName: response['pageTitle']));
    } on NetworkException catch (exception) {
      emit(EquipmentWidgetListingPageLoadFailed(exception.subTitle));
    } catch (e) {
      emit(EquipmentWidgetListingPageLoadFailed(ERROR_MSG));
    }
  }
}

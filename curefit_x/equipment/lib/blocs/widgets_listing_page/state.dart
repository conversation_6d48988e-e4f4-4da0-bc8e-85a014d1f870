abstract class EquipmentWidgetListingPageState {
  EquipmentWidgetListingPageState() : super();
}

class EquipmentWidgetListingPageIdle extends EquipmentWidgetListingPageState {
  @override
  String toString() => 'IdleState';
}

class EquipmentWidgetListingPageLoading
    extends EquipmentWidgetListingPageState {
  bool? showLoader;
  EquipmentWidgetListingPageLoading({this.showLoader = true});

  @override
  String toString() => 'EquipmentWidgetListingPageLoading';
}

class EquipmentWidgetListingPageLoaded extends EquipmentWidgetListingPageState {
  List widgets = [];
  String? pageName;
  EquipmentWidgetListingPageLoaded({required this.widgets, this.pageName})
      : super();

  @override
  String toString() => 'EquipmentWidgetListingPageLoaded';
}

class EquipmentWidgetListingPageLoadFailed
    extends EquipmentWidgetListingPageState {
  final String? error;
  EquipmentWidgetListingPageLoadFailed([this.error]) : super();

  @override
  String toString() => 'EquipmentWidgetListingPageLoadFailed';
}

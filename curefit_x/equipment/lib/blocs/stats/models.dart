import 'package:flutter/material.dart' hide Action;
import 'StatsModel.dart';
import 'package:intl/intl.dart';
import 'package:common/action/action_handler.dart';

class StatsScreenArgument {
  String? iconName;
  String? rowName;
  String? unitValue;
  String? unit;
  StatsScreenArgument(this.iconName,this.rowName,this.unitValue,this.unit);
}


class StatsScreenArguments {
  String? trainerName;
  String? trainingName;
  String? trainerImageUrl;
  String? trainerDetail;
  List<StatsScreenArgument> listData = [];
  Action? action;
  StatsScreenArguments(StatsScreenModel model) {
    trainerName = model.data?.trainerDetails?.firstName;
    trainerImageUrl = model.data?.trainerDetails?.profilePic;
    trainingName = model.data?.challengeDetails?.name;
    action = model.data?.action;
    trainerDetail = getTrainerDetail(model);
    listData = [];
    listData.add(getCaloriesModel(model));
    listData.add(getDistanceModel(model));
    listData.add(getTimeModel(model));
    listData.add(getRPMModel(model));
    listData.add(getOutputModel(model));
    listData.add(getResistanceModel(model));
  }

  String getTrainerDetail(StatsScreenModel model) {
    DateFormat dateFormat = DateFormat("yyyy-MM-ddTHH:mm:ssZ");
    final time = model.data?.metric?.userStartTime ?? "";
    if (time == "") {
      return time;
    }
    DateTime dateTime = dateFormat.parse(time);
    String dateYear = DateFormat('dd MMM').format(dateTime);
    String timeAMPM = DateFormat('KK:mm a').format(dateTime);
    final formattedDateTime = "by ${model.data?.trainerDetails?.firstName ?? ""} On $dateYear at $timeAMPM";
    // On 17 Feb at 6:30pm
    return formattedDateTime;
  }

  StatsScreenArgument getCaloriesModel(StatsScreenModel model) {
    String? totalCalories = model.data?.metric?.calories.toString();
    String? distanceUnit = model.data?.metric?.caloriesUnit.toString();
    return StatsScreenArgument("calorieIcon.svg","Total Calories",totalCalories,distanceUnit);
  }

  StatsScreenArgument getDistanceModel(StatsScreenModel model) {
    String? totalDistance = model.data?.metric?.distance.toString();
    String? distanceUnit = model.data?.metric?.distanceUnit.toString();
    return StatsScreenArgument("distanceIcon.svg","Total Distance",totalDistance,distanceUnit);
  }

  StatsScreenArgument getTimeModel(StatsScreenModel model) {
    int totalTime = int.parse(model.data?.metric?.userSessionDuration ?? "0");
    String? timeUnit = "Min";
    return StatsScreenArgument("timeIcon.svg","Total Time",formatTime(totalTime),timeUnit);
  }

  String formatTime(int seconds) {
    return '${(Duration(seconds: seconds.toInt()))}'.split('.')[0].padLeft(8, '0');
  }

  StatsScreenArgument getRPMModel(StatsScreenModel model) {
    String? stride = model.data?.metric?.avgStrides?.toString();
    return StatsScreenArgument("rpmIcon.svg","Avg stride",stride,"");
  }

  StatsScreenArgument getOutputModel(StatsScreenModel model) {
    String? avgOutput = model.data?.metric?.power?.toInt().toString();
    String? outputUnit = model.data?.metric?.powerUnit;
    return StatsScreenArgument("outPutIcon.svg","Avg Output",avgOutput,outputUnit);
  }

  StatsScreenArgument getResistanceModel(StatsScreenModel model) {
    String? resistance = model.data?.metric?.avgResistance?.toInt().toString();
    return StatsScreenArgument("resistanceIcon.svg","Avg Resistance",resistance,"");
  }
}

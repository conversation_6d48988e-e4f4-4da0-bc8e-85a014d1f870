import 'package:common/action/action_handler.dart';

abstract class EquipmentStatsEvent {
  EquipmentStatsEvent() : super();
}

class LoadEquipmentStatsEvent extends EquipmentStatsEvent {
  final bool? showLoader;
  LoadEquipmentStatsEvent({this.showLoader}) : super();

  @override
  String toString() => 'LoadEquipmentStatsEvent';
}

class FetchStats extends EquipmentStatsEvent {
  StatsScreenPostedArguments? postedArgs;
  FetchStats({this.postedArgs}) : super();
}


class StatsScreenPostedArguments {
  String? challengeId;
  String? workoutId;

  StatsScreenPostedArguments(Map<String, dynamic> params) {
    challengeId = params['challenge_id'];
    workoutId = params['workout_id'];
  }
}

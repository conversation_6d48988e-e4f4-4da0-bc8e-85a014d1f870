


import 'package:common/action/action_handler.dart';

import 'models.dart';

abstract class StatsState {
  StatsState() : super();
}

class StatsIdle extends StatsState {
  @override
  String toString() => 'IdleState';
}

class StatsLoading extends StatsState {
  bool? showLoader;
  StatsLoading({this.showLoader = true});

  @override
  String toString() => 'StatsLoading';
}

class StatsLoaded extends StatsState {
  final StatsScreenArguments statsList;
  StatsLoaded({required this.statsList});
  @override
  String toString() => 'StatsLoaded';
}

class StatsFailed extends StatsState {
  final String? error;
  StatsFailed([this.error]) : super();

  @override
  String toString() => 'StatsFailed';
}

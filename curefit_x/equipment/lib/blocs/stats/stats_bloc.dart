import 'package:bloc/bloc.dart';
import 'package:common/constants/common_constants.dart';
import 'package:common/network/client.dart';
import 'package:equipment/blocs/stats/state.dart';

import '../../network/equipmentStats_repository.dart';
import 'StatsModel.dart';
import 'events.dart';
import 'models.dart';


class EquipmentStatsBloc extends Bloc<EquipmentStatsEvent, StatsState> {

  final EquipmentStatsRepository repository;

  EquipmentStatsBloc({required this.repository}) : super(StatsIdle()) {
    on<FetchStats>((event, emit) async {
      await _handleStats(event,emit);
    });
  }


  Future _handleStats(FetchStats event,
      Emitter<StatsState> emit) async {
    try {
      emit(StatsLoading(showLoader: true));
      final response = await repository.getStats({
        "challenge_id":event.postedArgs?.challengeId ?? "",
        "workout_id": event.postedArgs?.workoutId ?? ""
      });
      if (response == null) {
        emit(StatsFailed(ERROR_MSG));
      }
      StatsScreenArguments model = StatsScreenArguments(StatsScreenModel.fromJson(response));
      emit(StatsLoaded(statsList:model));
    } on NetworkException catch (exception) {
      emit(StatsFailed(exception.subTitle));
    } catch (e) {
      emit(StatsFailed(ERROR_MSG));
    }
  }
}

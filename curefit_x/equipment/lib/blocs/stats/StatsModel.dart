import 'dart:convert';

import 'package:common/action/action_handler.dart';

class StatsScreenModel {
  double? respCode;
  String? respMessage;
  Data? data;

  StatsScreenModel({this.respCode, this.respMessage, this.data});

  StatsScreenModel.fromJson(Map<String, dynamic> json) {
    respCode = json['resp_code'];
    respMessage = json['resp_message'];
    final jsonData = json['data'] as Map<String, dynamic>?;
    data = jsonData != null ? new Data.fromJson(jsonData) : null;
  }

}

class Data {
  Metric? metric;
  TrainerDetails? trainerDetails;
  ChallengeDetails? challengeDetails;
  String? screen;
  Action? action;

  Data({this.metric, this.trainerDetails,this.challengeDetails,this.screen, this.action});

  Data.fromJson(Map<String, dynamic> json) {
    final metricJsonData = json['metric'] as Map<String, dynamic>?;
    metric = metricJsonData != null ? new Metric.fromJson(metricJsonData) : null;
    final trainersJsonData = json['trainer_details'] as Map<String, dynamic>?;
    trainerDetails = trainersJsonData != null
        ? new TrainerDetails.fromJson(trainersJsonData)
        : null;
    final challengeDetailJsonData = json['challenge_details'] as Map<String, dynamic>?;
    challengeDetails = challengeDetailJsonData != null
        ? new ChallengeDetails.fromJson(challengeDetailJsonData)
        : null;
    screen = json['screen'];
    action = Action.fromJson(json['action']);
  }

}

class Metric {
  String? id;
  String? userId;
  String? userEmail;
  String? trainersEmail;
  String? userSessionDuration;
  String? workoutId;
  String? streamType;
  String? challengeType;
  String? challengeId;
  String? userStartTime;
  String? userEndTime;
  double? distance;
  double? calories;
  double? power;
  double? avgResistance;
  double? avgRpm;
  String? avgStrides;
  String? sID;
  String? deviceId;
  String? distanceUnit;
  String? caloriesUnit;
  String? powerUnit;

  Metric(
      {this.id,
        this.userId,
        this.userEmail,
        this.trainersEmail,
        this.userSessionDuration,
        this.workoutId,
        this.streamType,
        this.challengeType,
        this.challengeId,
        this.userStartTime,
        this.userEndTime,
        this.distance,
        this.calories,
        this.power,
        this.avgResistance,
        this.avgRpm,
        this.avgStrides,
        this.sID,
        this.deviceId,
        this.distanceUnit,
        this.caloriesUnit,
        this.powerUnit});

  Metric.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userId = json['user_id'];
    userEmail = json['user_email'];
    trainersEmail = json['trainers_email'];
    userSessionDuration = json['user_session_duration'];
    workoutId = json['workout_id'];
    streamType = json['stream_type'];
    challengeType = json['challenge_type'];
    challengeId = json['challenge_id'];
    userStartTime = json['user_start_time'];
    userEndTime = json['user_end_time'];
    distance = json['distance'];
    calories = json['calories'];
    power = json['power'];
    avgResistance = json['avg_resistance'];
    avgRpm = json['avg_rpm'];
    avgStrides = json['avg_strides'];
    sID = json['SID'];
    deviceId = json['device_id'];
    distanceUnit = json['distance_unit'];
    caloriesUnit = json['calories_unit'];
    powerUnit = json['power_unit'];
  }

}

class TrainerDetails {
  String? emailId;
  String? userName;
  String? profilePic;
  String? profileBigImage;
  String? firstName;
  String? lastName;
  double? trainersRating;
  String? channelId;
  String? weight;
  String? gender;

  TrainerDetails(
      {this.emailId,
        this.userName,
        this.profilePic,
        this.profileBigImage,
        this.firstName,
        this.lastName,
        this.trainersRating,
        this.channelId,
        this.weight,
        this.gender});

  TrainerDetails.fromJson(Map<String, dynamic> json) {
    emailId = json['email_id'];
    userName = json['user_name'];
    profilePic = json['profile_pic'];
    profileBigImage = json['profile_big_image'];
    firstName = json['first_name'];
    lastName = json['last_name'];
    trainersRating = json['trainers_rating'];
    channelId = json['channel_id'];
    weight = json['weight'];
    gender = json['gender'];
  }

}

class ChallengeDetails {
  String? name;
  String? description;

  ChallengeDetails({this.name, this.description});

  ChallengeDetails.fromJson(Map<String, dynamic> json) {
    name = json['name'];
    description = json['description'];
  }

}

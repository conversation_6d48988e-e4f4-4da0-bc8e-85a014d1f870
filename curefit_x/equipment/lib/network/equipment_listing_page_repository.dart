import 'package:common/network/client.dart';

class EquipmentWidgetListingPageRepository {
  final NetworkClient client;

  EquipmentWidgetListingPageRepository(this.client);

  Future<dynamic> getEquipmentWidgetListingPage(String pageType,
      {String? baseUrl}) async {
    final response = await client.post(
        '/v2/cultrow/widgetListPage', {'pageType': pageType}, baseUrl);
    return response;
  }
}

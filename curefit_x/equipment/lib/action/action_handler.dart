import 'package:common/action/action_handler.dart' as ActionHandler;
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/constants/action_constants.dart';
import 'package:equipment/network/clp_repository.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../blocs/cult_row_upcoming_class_widget/cult_row_upcoming_class_widget_event.dart';

class EquipmentActionHandler extends ActionHandler.IActionHandler {
  late final EquipmentCLPRepository equipmentClientRepository;
  final GlobalKey<NavigatorState> navigatorKey;

  EquipmentActionHandler(equipmentClientRepository,
      {required this.navigatorKey}) {
    equipmentClientRepository =
        EquipmentCLPRepository(equipmentClientRepository);
  }

  @override
  bool handleAction(ActionHandler.Action action, ActionBloc actionBloc) {
    BuildContext? context = navigatorKey.currentContext;
    if (context == null) {
      return false;
    }
    switch (action.type) {
      case ActionTypes.BOOK_CULT_ROW_CLASS:
        Bloc? bloc = action.bloc;
        bloc?.add(BookCultRowUpcomingClassEvent(meta: action.meta));
        return true;
    }
    return false;
  }
}

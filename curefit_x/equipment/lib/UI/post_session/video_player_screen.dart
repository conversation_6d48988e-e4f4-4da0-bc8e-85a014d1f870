import 'dart:async';
import 'dart:convert';

import 'package:common/action/action_handler.dart';
import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/blocs/ble/ble_bloc.dart';
import 'package:common/blocs/ble/events.dart';
import 'package:common/blocs/ble/state.dart';
import 'package:common/constants/analytics_constants.dart';
import 'package:common/ui/alert_view.dart';
import 'package:common/util/dev_mode.dart';
import 'package:common/util/orientation.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:equipment/constants/constants.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart' hide Action;
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:video_player_cf/video_player.dart';
import 'package:wakelock/wakelock.dart';
import 'package:web_socket_channel/web_socket_channel.dart';

import '../../blocs/video_player/events.dart';
import '../../blocs/video_player/model.dart';
import '../../blocs/video_player/state.dart';
import '../../blocs/video_player/utils.dart';
import '../../blocs/video_player/video_player_bloc.dart';
import 'Widget/ble_widget.dart';
import 'Widget/leaderboard_widget.dart';
import 'Widget/liveStatus_widget.dart';
import 'Widget/reconnecting_widget.dart';
import 'Widget/videoplayer_widget.dart';

class SessionVideoPlayer extends StatefulWidget {
  const SessionVideoPlayer({Key? key}) : super(key: key);

  @override
  State<SessionVideoPlayer> createState() => _SessionVideoPlayerState();
}

class _SessionVideoPlayerState extends State<SessionVideoPlayer> {
  VideoPlayerController? _controller;
  WebSocketChannel? _channel;
  final Connectivity _connectivity = Connectivity();
  late StreamSubscription<ConnectivityResult> _connectivitySubscription;
  VideoPlayerScreenArguments? videoPlayerScreenArguments;
  double finalResValue = 0;
  ResistanceModel resistanceModel = ResistanceModel();
  StrideModel strideModel = StrideModel();
  BleDeviceData? bleDeviceData;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      videoPlayerScreenArguments = getScreenArguments();
      initializeWebSocket();
      BleDeviceBloc bleBloc = BlocProvider.of<BleDeviceBloc>(context);
      print("bleBloc.state: ${bleBloc.state}");
      if (bleBloc.state is BleDeviceConnected) {
        print("bleBloc.state: ${bleBloc.state}");
        bleBloc.add(FetchServiceAndCharacteristicsDeviceEvent(
          device: bleBloc.connectedDevice!,
          height: videoPlayerScreenArguments!.height,
          weight: videoPlayerScreenArguments!.weight,
          gender: videoPlayerScreenArguments!.gender,
          birthDate: videoPlayerScreenArguments!.birthDate,
        ));
        initializeVideoPLayer();
      }
      // else {
      //   showErrorAlert(
      //       context: context,
      //       title: "Connect cultROW to begin",
      //       onClose: () {
      //         Navigator.pop(context);
      //         showBottomTray(context: context, child: const BleScreen());
      //       });
      // }
    });
    initConnectivity();
    _connectivitySubscription =
        _connectivity.onConnectivityChanged.listen(_UpdateConnectionState);
    Wakelock.enable();
    // initializeVideoPLayer();
    lockToLandscape();
  }

  Future<void> initializeWebSocket() async {
    // Websocket Connectivity
    String? streamType = videoPlayerScreenArguments?.stream_type;
    String? challengeId = videoPlayerScreenArguments?.challenge_id;
    if (streamType != null && challengeId != null) {
      Uri wsUri = await getWebsocketUri(streamType, challengeId);
      // ignore: avoid_print
      print("Connecting Websocket: ${wsUri.toString()}");
      _channel = WebSocketChannel.connect(wsUri);
    } else {
      // ignore: avoid_print
      print("Ignoring websocket, stream_type or challenge_id is null");
    }
  }

  Future<void> initializeVideoPLayer() async {
    Duration? currentPosition = const Duration(milliseconds: 0);
    if (_controller?.value.isInitialized == true) {
      if (videoPlayerScreenArguments?.isLive() == false) {
        currentPosition = _controller?.value.position;
      }
      _controller?.dispose();
    }
    _controller = VideoPlayerController();
    await _controller?.setNetworkDataSource(
        videoPlayerScreenArguments?.videoUrl() ??
            "https://cdn.jwplayer.com/live/events",
        useCache: true);
    if (currentPosition != null) {
      _controller?.seekTo(currentPosition);
    }
    _controller?.play();
    setState(() {});
    _controller?.addListener(listener);

    AnalyticsRepository analyticsRepository =
        RepositoryProvider.of<AnalyticsRepository>(context);
    analyticsRepository.logButtonClickEvent(extraInfo: {
      "vertical": vertical_d2c,
      "actionType": "workout_start",
      "buttonType": "video_playback_start",
      "pageId": "d2c_video_player",
      "pageName": "d2c_video_player",
      "title": videoPlayerScreenArguments?.workout_id,
      "subtitle": videoPlayerScreenArguments?.trainers_email,
      "productId": videoPlayerScreenArguments?.challenge_id,
      "productType": videoPlayerScreenArguments?.challengeType,
      "productName": videoPlayerScreenArguments?.stream_type,
      "text": videoPlayerScreenArguments.toString(),
    });
  }

  void listener() {
    // print("<---------- object changes ---------->");
    // var pos = _controller?.value.position;
    // var isBuffering = _controller?.value.isBuffering;
    // var errorDesc = _controller?.value.errorDescription;
    // var isPlaying = _controller?.value.isPlaying;
    // var totalDuration = _controller?.value.duration;
    // print("isPlaying $isPlaying position ---->$pos buffring ---->$isBuffering errorDesc --->$errorDesc totalDuration ---> $totalDuration");
    final bloc = BlocProvider.of<EquipmentVideoPlayerBloc>(context);
    bloc.add(VideoPlayerEvent(
        position: _controller?.value.position,
        totalDuration: videoPlayerScreenArguments?.isLive() == true
            ? videoPlayerScreenArguments?.getEndDuration()
            : _controller?.value.duration,
        isLive: (videoPlayerScreenArguments?.stream_type == "Live"),
        isBuffering: _controller?.value.isBuffering ?? false));
  }

  VideoPlayerScreenArguments? getScreenArguments() {
    final ScreenArguments? args =
        ModalRoute.of(context)!.settings.arguments as ScreenArguments?;
    if (args != null) {
      return VideoPlayerScreenArguments(args.params);
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        var videoPlayerBloc =
            BlocProvider.of<EquipmentVideoPlayerBloc>(context);
        videoPlayerBloc.add(BackPressEvent(isBackPressed: true));
        return false;
      },
      child: BlocConsumer<BleDeviceBloc, BleDeviceState>(
        listener: ((context, state) {
          if (state is BleDeviceConnected) {
            if (kDebugMode) {
              print("BleDeviceBloc-BleDeviceConnected");
            }
            BleDeviceBloc bleBloc = BlocProvider.of<BleDeviceBloc>(context);
            bleBloc.subscription!.cancel();
            bleBloc.subscription1!.cancel();
            bleBloc.add(FetchServiceAndCharacteristicsDeviceEvent(
              device: bleBloc.connectedDevice!,
              height: videoPlayerScreenArguments!.height,
              weight: videoPlayerScreenArguments!.weight,
              gender: videoPlayerScreenArguments!.gender,
              birthDate: videoPlayerScreenArguments!.birthDate,
            ));
          } else if (state is BleDeviceDisconnect) {
            if (kDebugMode) {
              print("BleDeviceBloc-BleDeviceDisconnect");
            }
          } else if (state is BleDeviceData) {
            bleDeviceData = state;
            resistanceModel.addResistance(state.resistanceValue);
            strideModel.addStride(state.frequencyValue);
            DateTime endTime = DateTime.now();
            String userSessionDuration = VideoPLayerUtils.getTimeDifference(
                videoPlayerScreenArguments?.startTime, endTime);
            if (kDebugMode) {
              print('Resistance - ' + state.resistanceValue);
              print('Power - ' + state.powerValue);
              print('Frequency - ' + state.frequencyValue);
              print('Calories - ' + state.caloriesValue);
              print('Distance - ' + state.distanceValue);
            }
            // send the data over websocket!
            var socketPacket = {
              "user_id": videoPlayerScreenArguments?.userID,
              "challenge_id": videoPlayerScreenArguments?.challenge_id,
              "workout_id": videoPlayerScreenArguments?.workout_id,
              "stream_type": videoPlayerScreenArguments?.stream_type,
              "user_name": videoPlayerScreenArguments?.userName,
              "user_email": videoPlayerScreenArguments?.userEmail,
              "challenge_type": videoPlayerScreenArguments?.challengeType,
              "rpm": "0",
              "resistance": state.resistanceValue,
              "aggregated_power": state.powerValue,
              "total_power": state.powerValue,
              "calories_burned": state.caloriesValue,
              "distance": state.distanceValue,
              "city": 'Kolkata',
              // "stride":
              //     strideModel.avgStride(userSessionDuration).toStringAsFixed(2),
              "time": "${DateTime.now().millisecondsSinceEpoch}"
            };

            if (kDebugMode) {
              print(jsonEncode(socketPacket));
            }

            _channel?.sink.add(jsonEncode(socketPacket));
          }
        }),
        builder: (context, state) {
          if (kDebugMode) {
            print("==========================>--------------->$state");
          }
          if (state is BleDeviceConnected || state is BleDeviceData) {
            return BlocListener<EquipmentVideoPlayerBloc,
                EquipmentVideoPlayerState>(
              listener: (context, state) {
                if (state is EquipmentVideoPlayerFailed) {
                  showErrorAlert(
                      context: context,
                      title: state.error,
                      onClose: () {
                        Navigator.popUntil(
                            context,
                            (route) =>
                                route.settings.name ==
                                '/${EnumToString.convertToString(RouteNames.equipmentclp)}');
                      });
                } else if (state is VideoPlayerClassDetailSubmited) {
                  // resetScreenArgs();
                  ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
                  if (VideoPLayerUtils.isWorkOutGreaterThanMinTimeSpecified(
                      videoPlayerScreenArguments?.startTime)) {
                    String actionUrl =
                        "curefit://fl_equipmentstats?challenge_id=${videoPlayerScreenArguments?.challenge_id ?? ""}&workout_id=${videoPlayerScreenArguments?.workout_id ?? ""}"
                        "&trainers_email=${videoPlayerScreenArguments?.trainers_email ?? ""}";
                    PerformActionEvent event = PerformActionEvent(
                        Action.fromJson(
                            {"url": actionUrl, "actionType": "NAVIGATION"}));
                    actionBloc.add(event);
                  } else {
                    Navigator.popUntil(
                        context,
                        (route) =>
                            route.settings.name ==
                            '/${EnumToString.convertToString(RouteNames.equipmentclp)}');
                  }
                  resetScreenArgs();
                }
              },
              child: Scaffold(
                extendBodyBehindAppBar: true,
                appBar: AppBar(
                  elevation: 0, // 1
                  leading: BackButton(
                      color: Colors.white,
                      onPressed: () {
                        var videoPlayerBloc =
                            BlocProvider.of<EquipmentVideoPlayerBloc>(context);
                        videoPlayerBloc
                            .add(BackPressEvent(isBackPressed: true));
                        // var a = true;
                        _channel?.sink
                            .add('<Data for challenge will be sent here>');
                      }),
                  // title: const Text("Exit"),
                  centerTitle: false,
                  backgroundColor: Colors.transparent,
                ),
                body: Stack(
                  children: [
                    Center(
                      child: _controller?.value.isInitialized == true
                          ? VideoPlayer(_controller!)
                          : Container(
                              color: Colors.black,
                            ),
                    ),
                    StreamBuilder(
                      stream: _channel?.stream,
                      builder: (context, snapshot) {
                        if (snapshot.hasData) {
                          print(snapshot.data.toString());
                          final json = jsonDecode(snapshot.data.toString());
                          final leaderboard =
                              LeaderboardResponse.fromJson(json);
                          return LeaderBoardWidget(data: leaderboard);
                        }
                        return Container();
                      },
                    ),
                    state is BleDeviceData
                        ? BleConnectivity(data: state)
                        : Container(),
                    const LiveStatusWidget(),
                    const ReconnectingWidget(),
                    ExitSessionPopUp(
                      videoPlayerScreenArguments: videoPlayerScreenArguments,
                      bleDeviceData: bleDeviceData,
                      resistanceModel: resistanceModel,
                      strideModel: strideModel,
                    ),
                    VideoFinishedPopUp(
                      videoPlayerScreenArguments: videoPlayerScreenArguments,
                      bleDeviceData: bleDeviceData,
                      resistanceModel: resistanceModel,
                      strideModel: strideModel,
                    ),
                    const LoaderWidget(),
                  ],
                ),
              ),
            );
          } else {
            return const BleNotConnected();
          }
        },
      ),
    );
  }

  Future<void> initConnectivity() async {
    late ConnectivityResult result;
    try {
      result = await _connectivity.checkConnectivity();
    } on PlatformException catch (e) {
      print("Error Occurred: ${e.toString()} ");
      return;
    }
    if (!mounted) {
      return Future.value(null);
    }
    return _UpdateConnectionState(result);
  }

  Future<void> _UpdateConnectionState(ConnectivityResult result) async {
    final videoPlayerBloc = BlocProvider.of<EquipmentVideoPlayerBloc>(context);
    if (result == ConnectivityResult.mobile ||
        result == ConnectivityResult.wifi) {
      showStatus(result, true);
      videoPlayerBloc.add(InternetStatusEvent(isInternetAvailable: true));
      if (_controller?.value.hasError == true) {
        initializeVideoPLayer();
      }
    } else {
      showStatus(result, false);
      videoPlayerBloc.add(InternetStatusEvent(isInternetAvailable: false));
    }
  }

  void showStatus(ConnectivityResult result, bool status) {
    // var res = result.toString();
    // print("==================> $res =============> $status");
    // final snackBar = SnackBar(
    //     content:
    //     Text("${status ? 'ONLINE\n' : 'OFFLINE\n'}${result.toString()} "),
    //     backgroundColor: status ? Colors.green : Colors.red);
    // ScaffoldMessenger.of(context).showSnackBar(snackBar);
  }

  void resetScreenArgs() {
    lockToPortrait();
    _controller?.dispose();
    _channel?.sink.close();
    Wakelock.disable();
    _connectivitySubscription.cancel();
    if (bleDeviceData?.devices != null) {
      BleDeviceBloc bleBloc = BlocProvider.of<BleDeviceBloc>(context);
      bleBloc.add(DisconnectBleDeviceEvent(device: bleDeviceData!.devices));
      bleBloc.subscription!.cancel();
      bleBloc.subscription1!.cancel();
      bleBloc.resetDefaultValue();
    }
  }

  @override
  void dispose() {
    super.dispose();
    resetScreenArgs();
  }
}

Future<Uri> getWebsocketUri(String streamType, String challengeId) async {
  bool useStage = await useStageMode();
  String wsBase = useStage
      ? "stagingrt.ap-south-1.elasticbeanstalk.com/ws"
      : "rtserver.tread.fit/ws";
  String wsType = streamType == live ? "real_time_v3" : "on_demand_v3";
  return Uri.parse("ws://$wsBase/$wsType/$challengeId/");
}

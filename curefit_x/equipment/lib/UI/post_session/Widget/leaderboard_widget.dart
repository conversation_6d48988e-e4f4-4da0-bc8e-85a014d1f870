import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:flutter/material.dart' hide Action;
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../blocs/video_player/model.dart';
import '../../../blocs/video_player/state.dart';
import '../../../blocs/video_player/video_player_bloc.dart';



class LeaderBoardWidget extends StatefulWidget {
  final LeaderboardResponse data;
  const LeaderBoardWidget({Key? key, required this.data}) : super(key: key);

  @override
  State<LeaderBoardWidget> createState() => _LeaderBoardWidgetState();
}

class _LeaderBoardWidgetState extends State<LeaderBoardWidget> {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<EquipmentVideoPlayerBloc, EquipmentVideoPlayerState>(
      builder: (context, state) {
        return Container(
          padding: const EdgeInsets.fromLTRB(0, 20, 20, 30),
          // color: Colors.red,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: widget.data.leaderboard.length,
            itemBuilder: (context, index) {
              final data = widget.data.leaderboard[index];
              return getLeaderBoardIcon(data,widget.data.user);
            },
          ),
        );
      },
    );
  }

  Widget getLeaderBoardIcon(UserEntry userData,UserEntry currentUser) {
    return Container(
      // color: Colors.red,
      margin: const EdgeInsets.fromLTRB(0, 5, 0, 5),
      child: Align(
        alignment: Alignment.centerLeft,
        child: Container(
          // color: Color(0xFFFFFF).withOpacity(.25),
          decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
                colors: [
                  const Color(0xFFFFFF).withOpacity(.25),
                  const Color(0xFFFFFF).withOpacity(.25),
                  Colors.transparent
                ],
              )),
          padding: const EdgeInsets.fromLTRB(10, 5, 10, 5),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "#${userData.rank}",
                style: AuroraTheme.of(context).textStyle(TypescaleValues.H3),
              ),
              const SizedBox(
                width: 50,
                child: CircleAvatar(
                    radius: 20,
                    backgroundColor: Colors.black87,
                    backgroundImage:
                    NetworkImage("https://trainer-pic.s3.ap-south-1.amazonaws.com/default_profile_picture.jpg")),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(children: [
                    Text(userData.score,
                        style: AuroraTheme.of(context)
                            .textStyle(TypescaleValues.H3)),
                    Container(
                      margin: const EdgeInsets.fromLTRB(10, 0, 0, 0),
                      width: 20,
                      height: 20,
                      // color: Colors.red,
                      child: (userData.rank == currentUser.rank) ? SvgPicture.asset("assets/equipment/polygon_green.svg") : Container(),
                    )
                  ]),
                  Text(userData.name,
                      style:
                      AuroraTheme.of(context).textStyle(TypescaleValues.H1))
                ],
              )
            ],
          ),
        ),
      ),
    );
  }
}

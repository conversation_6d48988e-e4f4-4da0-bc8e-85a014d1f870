import 'package:common/ui/atoms/secondary_button.dart';
import 'package:common/ui/bluetooth_connectivity/ble_screen.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/molecules/bottom_tray.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/blocs/ble/state.dart';
import 'package:equipment/constants/constants.dart';
import 'package:flutter/material.dart' hide Action;
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../blocs/video_player/events.dart';
import '../../../blocs/video_player/model.dart';
import '../../../blocs/video_player/state.dart';
import '../../../blocs/video_player/utils.dart';
import '../../../blocs/video_player/video_player_bloc.dart';

class ExitSessionPopUp extends StatefulWidget {
  final VideoPlayerScreenArguments? videoPlayerScreenArguments;
  final BleDeviceData? bleDeviceData;
  final ResistanceModel resistanceModel;
  final StrideModel strideModel;
  const ExitSessionPopUp(
      {required this.videoPlayerScreenArguments,
      required this.bleDeviceData,
      required this.resistanceModel,
      required this.strideModel});

  @override
  State<ExitSessionPopUp> createState() => _ExitSessionPopUpState();
}

class _ExitSessionPopUpState extends State<ExitSessionPopUp> {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<EquipmentVideoPlayerBloc, EquipmentVideoPlayerState>(
        builder: (context, state) {
      if (state.isBackPressed != true || state.isVideoPlaybackFinished()) {
        return Container();
      }
      return Stack(
        children: [
          Container(
            color: const Color.fromRGBO(0, 0, 0, 0.8),
          ),
          Container(
            // color: Colors.red,
            alignment: Alignment.centerLeft,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                    margin: const EdgeInsets.all(20),
                    child: Text(
                      exitQuestion,
                      style:
                          AuroraTheme.of(context).textStyle(TypescaleValues.H1),
                    )),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      margin: const EdgeInsets.fromLTRB(0, 0, 10, 0),
                      child: SecondaryButton(
                        () {
                          var videoPlayerBloc =
                              BlocProvider.of<EquipmentVideoPlayerBloc>(
                                  context);
                          videoPlayerBloc
                              .add(ShowLoaderEvent(showLoader: true));
                          videoPlayerBloc
                              .add(VideoPLayerUtils.getSubmitClassDetailEvent(
                            context,
                            widget.videoPlayerScreenArguments,
                            widget.bleDeviceData,
                            widget.resistanceModel,
                            widget.strideModel,
                          ));
                        },
                        "Yes",
                        expanded: false,
                        buttonType: SecondaryButtonType.BIG,
                      ),
                    ),
                    Container(
                      margin: const EdgeInsets.fromLTRB(10, 0, 0, 0),
                      child: SecondaryButton(
                        () {
                          var videoPlayerBloc =
                              BlocProvider.of<EquipmentVideoPlayerBloc>(
                                  context);
                          videoPlayerBloc
                              .add(BackPressEvent(isBackPressed: false));
                        },
                        "No",
                        expanded: false,
                        buttonType: SecondaryButtonType.BIG,
                      ),
                    )
                  ],
                )
              ],
            ),
          )
        ],
      );
    });
  }
}

class VideoFinishedPopUp extends StatefulWidget {
  final VideoPlayerScreenArguments? videoPlayerScreenArguments;
  final BleDeviceData? bleDeviceData;
  final ResistanceModel resistanceModel;
  final StrideModel strideModel;

  const VideoFinishedPopUp(
      {required this.videoPlayerScreenArguments,
      required this.bleDeviceData,
      required this.resistanceModel,
      required this.strideModel});

  @override
  State<VideoFinishedPopUp> createState() => _VideoFinishedPopUpState();
}

class _VideoFinishedPopUpState extends State<VideoFinishedPopUp> {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<EquipmentVideoPlayerBloc, EquipmentVideoPlayerState>(
        builder: (context, state) {
      if (state.isVideoPlaybackFinished() != true) {
        return Container();
      }
      return Stack(
        children: [
          Container(
            color: const Color.fromRGBO(0, 0, 0, 0.8),
          ),
          Container(
            // color: Colors.red,
            alignment: Alignment.centerLeft,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                    margin: const EdgeInsets.all(20),
                    child: Text(
                      "Session Ended",
                      style:
                          AuroraTheme.of(context).textStyle(TypescaleValues.H1),
                    )),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      margin: const EdgeInsets.fromLTRB(0, 0, 10, 0),
                      child: SecondaryButton(
                        () {
                          var videoPlayerBloc =
                              BlocProvider.of<EquipmentVideoPlayerBloc>(
                                  context);
                          videoPlayerBloc
                              .add(ShowLoaderEvent(showLoader: true));
                          videoPlayerBloc
                              .add(VideoPLayerUtils.getSubmitClassDetailEvent(
                            context,
                            widget.videoPlayerScreenArguments,
                            widget.bleDeviceData,
                            widget.resistanceModel,
                            widget.strideModel,
                          ));
                        },
                        "Next",
                        expanded: false,
                        buttonType: SecondaryButtonType.BIG,
                      ),
                    ),
                  ],
                )
              ],
            ),
          )
        ],
      );
    });
  }
}

class LoaderWidget extends StatelessWidget {
  const LoaderWidget({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<EquipmentVideoPlayerBloc, EquipmentVideoPlayerState>(
        builder: (context, state) {
      if (state.showLoader != true) {
        return Container();
      }
      return const FancyLoadingIndicator();
    });
  }
}

class BleNotConnected extends StatefulWidget {
  const BleNotConnected({Key? key}) : super(key: key);

  @override
  State<BleNotConnected> createState() => _BleNotConnectedState();
}

class _BleNotConnectedState extends State<BleNotConnected> {
  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
            // color: Colors.red,
            ),
        Container(
          // color: Colors.red,
          alignment: Alignment.centerLeft,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                  margin: const EdgeInsets.all(20),
                  child: Text(
                    "Connect cultROW to begin",
                    style:
                        AuroraTheme.of(context).textStyle(TypescaleValues.H1),
                  )),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    margin: const EdgeInsets.fromLTRB(0, 0, 10, 0),
                    child: SecondaryButton(
                      () {
                        Navigator.pop(context);
                        showBottomTray(
                            context: context, child: const BleScreen());
                      },
                      "OK",
                      expanded: false,
                      buttonType: SecondaryButtonType.BIG,
                    ),
                  ),
                ],
              )
            ],
          ),
        )
      ],
    );
  }
}

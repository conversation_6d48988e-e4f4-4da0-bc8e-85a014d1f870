
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/blocs/ble/state.dart';
import 'package:flutter/material.dart' hide Action;
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../blocs/video_player/state.dart';
import '../../../blocs/video_player/video_player_bloc.dart';


class BleConnectivity extends StatefulWidget {
  final BleDeviceData data;
  const BleConnectivity({Key? key, required this.data}) : super(key: key);

  @override
  State<BleConnectivity> createState() => _BleConnectivityState();
}

class _BleConnectivityState extends State<BleConnectivity> {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<EquipmentVideoPlayerBloc, EquipmentVideoPlayerState>(
      builder: (context, state) {
        return Container(
          alignment: Alignment.bottomRight,
          // color: Colors.red,
          padding: const EdgeInsets.fromLTRB(20, 20, 0, 0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Column(
                mainAxisAlignment: MainAxisAlignment.end,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  getTime(state.isLive, state.calculateTime()),
                  Container(
                    // color: Color(0xFFFFFF).withOpacity(.25),
                    decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.centerRight,
                          end: Alignment.centerLeft,
                          colors: [
                            const Color(0xFFFFFF).withOpacity(.25),
                            const Color(0xFFFFFF).withOpacity(.25),
                            const Color(0xFFFFFF).withOpacity(.3),
                            const Color(0xFFFFFF).withOpacity(.25),
                            Colors.transparent
                          ],
                        )),
                    padding: const EdgeInsets.fromLTRB(100, 0, 20, 0),
                    child: Row(
                      children: getListOFWidget(widget.data),
                    ),
                  )
                ],
              )
            ],
          ),
        );
      },
    );
  }

  Widget getTime(bool? isLive, String time) {
    if (isLive == true) {
      return Container();
    } else {
      return Container(
        margin: const EdgeInsets.fromLTRB(0, 0, 10, 10),
        child: Row(
          children: [
            Text(time,
                style: AuroraTheme.of(context).textStyle(TypescaleValues.H1)),
          ],
        ),
      );
    }
  }

  List<Widget> getListOFWidget(BleDeviceData state) {
    final children = <Widget>[];
    children.add(getValueWithIcon(
        "metrics_distance.svg", "Meters", state.distanceValue.toString()));
    children.add(getValueWithIcon(
        "metrics_calorie.svg", "KCal", state.caloriesValue.toString()));
    children.add(getValueWithIcon(
        "metrics_stride.svg", "Stride", state.frequencyValue.toString()));
    children.add(getValueWithIcon(
        "metrics_resistance.svg", "Resi", state.resistanceValue.toString()));
    return children;
  }

  Widget getValueWithIcon(String icon, String label, String value) {
    return Container(
      // height: 58,
      // color: Color(0xFFFFFF).withOpacity(.25),
      child: Row(
        children: [
          Container(
            // color: Colors.green,
              margin: const EdgeInsets.fromLTRB(20, 0, 5, 0),
              child: SvgPicture.asset(
                "assets/equipment/" + icon,
                width: 24,
                height: 24,
              )),
          Column(
            children: [
              Container(
                // color: Colors.green,
                  margin: const EdgeInsets.fromLTRB(5, 5, 5, 0),
                  child: Text(
                    value,
                    style:
                    AuroraTheme.of(context).textStyle(TypescaleValues.H1),
                  )),
              Container(
                // color: Colors.green,
                  margin: const EdgeInsets.fromLTRB(5, 0, 5, 5),
                  child: Text(
                    label,
                    style:
                    AuroraTheme.of(context).textStyle(TypescaleValues.P4),
                  )),
            ],
          )
        ],
      ),
    );
  }
}

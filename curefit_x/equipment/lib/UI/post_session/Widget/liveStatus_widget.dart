import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:flutter/material.dart' hide Action;
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../blocs/video_player/state.dart';
import '../../../blocs/video_player/video_player_bloc.dart';


class LiveStatusWidget extends StatefulWidget {
  const LiveStatusWidget({Key? key}) : super(key: key);

  @override
  State<LiveStatusWidget> createState() => _LiveStatusWidgetState();
}

class _LiveStatusWidgetState extends State<LiveStatusWidget> {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<EquipmentVideoPlayerBloc, EquipmentVideoPlayerState>(
      buildWhen: (previous, current) => previous.isLive != current.isLive,
      builder: (context, state) {
        if (state.isLive != true) {
          return Container();
        }
        return Container(
          padding: const EdgeInsets.all(30),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Container(
                decoration: BoxDecoration(
                  color: const Color(0xffe30000),
                  borderRadius: BorderRadius.circular(3),
                ),
                padding: const EdgeInsets.fromLTRB(10, 2, 10, 2),
                child: Text(
                  "• LIVE",
                  style: AuroraTheme.of(context).textStyle(TypescaleValues.P1),
                ),
              )
            ],
          ),
        );
      },
    );
  }
}

import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:flutter/material.dart' hide Action;
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../../blocs/video_player/state.dart';
import '../../../blocs/video_player/video_player_bloc.dart';



class ReconnectingWidget extends StatefulWidget {
  const ReconnectingWidget({Key? key}) : super(key: key);

  @override
  State<ReconnectingWidget> createState() => _ReconnectingWidgetState();
}

class _ReconnectingWidgetState extends State<ReconnectingWidget> {
  @override
  Widget build(BuildContext context) {
    return BlocBuilder<EquipmentVideoPlayerBloc, EquipmentVideoPlayerState>(
        builder: (context, state) {
          if (state.isBuffering == false) {
            return Container();
          }
          return Stack(
            children: [
              Container(
                color: const Color.fromRGBO(0, 0, 0, 0.8),
              ),
              Container(
                // color: Colors.red,
                alignment: Alignment.centerLeft,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // Container(
                    //     margin: EdgeInsets.all(20),
                    //     child: Text("Reconnecting...",
                    //       style: AuroraTheme.of(context).textStyle(TypescaleValues.H1),
                    //     )),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          margin: const EdgeInsets.fromLTRB(0, 0, 0, 0),
                          width: 30,
                          height: 30,
                          // color: Colors.red,
                          child:
                          SvgPicture.asset("assets/equipment/loaderImage.svg"),
                        ),
                        Container(
                            margin: const EdgeInsets.only(left: 10),
                            child: Text(
                              "Reconnecting...",
                              style: AuroraTheme.of(context)
                                  .textStyle(TypescaleValues.H1),
                            )),
                      ],
                    )
                  ],
                ),
              )
            ],
          );
        });
  }
}

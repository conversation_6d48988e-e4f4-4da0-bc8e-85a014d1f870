import 'package:common/font/cf_icons.dart';
import 'package:common/ui/theme/Colors.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/util/util.dart';
import 'package:flutter/material.dart';

List<String> rowHeaderText = ['Trainer', 'Music', 'App', 'cultRow'];
List<String> scenicRowHeaderText = ['Scenery', 'Sound', 'App', 'cultRow'];

List<String> rowItemText = ['Worst', 'Bad', 'Good', 'Awesome'];
List<IconData> rowIcons = [
  CFIcons.add,
  CFIcons.achievement,
  CFIcons.achievement,
  CFIcons.delete_bin
];

class FeedbackRow extends StatelessWidget {
  final int rowIndex;
  final Function(int columnIndex) callback;
  final List<int> feedback;
  final String streamType;
  const FeedbackRow(
      {Key? key,
      required this.rowIndex,
      required this.streamType,
      required this.callback,
      required this.feedback})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Text(
          streamType == 'SCENIC'
              ? scenicRowHeaderText[rowIndex]
              : rowHeaderText[rowIndex],
          style: AuroraTheme.of(context).textStyle(TypescaleValues.H1),
        ),
        const SizedBox(
          height: Spacings.x3,
        ),
        Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
          for (int i = 0; i < 4; i++)
            feedbackItem(columnIndex: i, context: context)
        ])
      ],
    );
  }

  Widget feedbackItem(
      {required int columnIndex, required BuildContext context}) {
    Color textColor = ColorPalette.white40;
    bool isSelected = false;
    if (feedback.isNotEmpty && feedback[rowIndex] == columnIndex) {
      textColor = ColorPalette.white;
      isSelected = true;
    }
    return InkWell(
      onTap: () {
        callback(columnIndex);
      },
      child: SizedBox(
        width: MediaQuery.of(context).size.width / 5,
        child: Column(
          children: [
            Stack(children: [
              backgroundCircle(context, isSelected),
              eyes(context),
              Positioned(
                child: Transform.translate(
                    offset: Offset(-1, 0), child: getSmileIcon(columnIndex)),
                bottom: MediaQuery.of(context).size.width / 20,
                right: MediaQuery.of(context).size.width / 20,
                left: MediaQuery.of(context).size.width / 20,
              )
            ]),
            const SizedBox(
              height: Spacings.x1,
            ),
            Text(
              rowItemText[columnIndex].overflow,
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
              style: AuroraTheme.of(context)
                  .textStyle(TypescaleValues.H4, color: textColor),
            )
          ],
        ),
      ),
    );
  }

  Container backgroundCircle(BuildContext context, bool isSelected) {
    return Container(
      width: MediaQuery.of(context).size.width / 5,
      height: MediaQuery.of(context).size.width / 5,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: LinearGradient(
          begin: Alignment.bottomCenter,
          end: Alignment.topCenter,
          colors: isSelected
              ? [
                  const Color(0xfff99b2c),
                  const Color(0xffffcf7b),
                ]
              : [
                  const Color(0xfffbda61).withOpacity(.4), // non-selected
                  const Color(0xffffbc6d).withOpacity(.4), // non-selected
                ],
        ),
      ),
    );
  }

  Positioned eyes(BuildContext context) {
    return Positioned(
        top: MediaQuery.of(context).size.width / 18,
        right: MediaQuery.of(context).size.width / 20,
        left: MediaQuery.of(context).size.width / 20,
        child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: const [
              Icon(
                CFIcons.black_circle,
                size: 10,
              ),
              Icon(
                CFIcons.black_circle,
                size: 10,
              )
            ]));
  }

  Widget getSmileIcon(int columnIndex) {
    switch (columnIndex) {
      case 0:
        return const Icon(
          CFIcons.bad_smile,
          size: 15,
        );
      case 1:
        return const Icon(
          CFIcons.sad_smile,
          size: 15,
        );
      case 2:
        return const Icon(
          CFIcons.good_smile,
          size: 15,
        );
      default:
        return const Icon(
          CFIcons.full_smile,
          size: 15,
        );
    }
  }
}

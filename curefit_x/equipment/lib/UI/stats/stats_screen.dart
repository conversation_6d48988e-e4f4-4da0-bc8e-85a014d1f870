import 'dart:io';

import 'package:common/action/action_handler.dart';
import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/ui/alert_view.dart';
import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/atoms/secondary_button.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/organisms/titlebar/frosty_app_bar.dart';
import 'package:common/ui/sliver/sliver_stack.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/util/theme.dart';
import 'package:equipment/blocs/stats/events.dart';
import 'package:flutter/material.dart' hide Action;
import 'package:enum_to_string/enum_to_string.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../blocs/stats/models.dart';
import '../../blocs/stats/state.dart';
import '../../blocs/stats/stats_bloc.dart';
import '../../constants/constants.dart';
import 'package:common/action/action_handler.dart' as action_handler;
import 'package:flutter_svg/flutter_svg.dart';

// void main() {
//   runApp(const StatsScreen());
// }

class StatsScreen extends StatelessWidget {
  const StatsScreen({Key? key}) : super(key: key);

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return StatsPage();
  }
}

class StatsPage extends StatefulWidget {
  const StatsPage({Key? key}) : super(key: key);
  @override
  State<StatsPage> createState() => _StatsPageState();
}

class _StatsPageState extends State<StatsPage> {
  late ScrollController _scrollController;
  Action? nextAction;
  StatsScreenPostedArguments? statsScreenPostedArguments;
  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      statsScreenPostedArguments = getScreenArguments();
      refresh();
    });
  }

  StatsScreenPostedArguments? getScreenArguments() {
    final action_handler.ScreenArguments? args = ModalRoute.of(context)!
        .settings
        .arguments as action_handler.ScreenArguments?;
    if (args != null) {
      return StatsScreenPostedArguments(args.params);
    }
    return null;
  }

  refresh() {
    final statsBloc = BlocProvider.of<EquipmentStatsBloc>(context);
    statsBloc.add(FetchStats(postedArgs: statsScreenPostedArguments));
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<EquipmentStatsBloc, StatsState>(
        builder: (context, state) {
      return WillPopScope(
        onWillPop: () async {
          return false;
        },
        child: blocScaffold(LayoutBuilder(
          builder: (context, constraints) {
            if (constraints.maxWidth > 0) {
              return Stack(
                children: [
                  Positioned.fill(
                      child: Container(
                    color: themeColor,
                  )),
                  Aurora(
                    hasBackgroundColor: false,
                    canvasTheme: CanvasTheme.CLASSIC,
                    size: constraints.biggest,
                  ),
                  Positioned.fill(child: buildWidgets()),
                  Positioned(
                      bottom: 0,
                      left: Spacings.x4,
                      right: Spacings.x4,
                      child: SafeArea(
                          child: Column(children: [
                        const SizedBox(
                          height: Spacings.x5,
                        ),
                        PrimaryButton(() {
                          // ActionBloc actionBloc =
                          //     BlocProvider.of<ActionBloc>(context);
                          // PerformActionEvent event =
                          //     PerformActionEvent(action_handler.Action.fromJson({
                          //   "actionType": "NAVIGATION",
                          //   "url":
                          //       "curefit://equipmentfeedback?challenge_id=b7eafa43-f34a-4ba2-b406-5c5369955511&workout_id=2c42e035-eff0-474d-b34d-ec2da20a541f&trainer_email=<EMAIL>&leave_early_reason="
                          // }));
                          // actionBloc.add(event);
                          if (state is StatsLoaded &&
                              state.statsList.action != null) {
                            // if (state.statsList.action != null) {
                            ActionBloc actionBloc =
                                BlocProvider.of<ActionBloc>(context);
                            PerformActionEvent event =
                                PerformActionEvent(state.statsList.action!);
                            actionBloc.add(event);
                            // }
                          }
                        }, NEXT)
                      ])))
                ],
              );
            }
            return Container();
          },
        )),
      );
    });
  }

  Widget buildWidgets() {
    List widgets = [];
    return BlocListener<EquipmentStatsBloc, StatsState>(
      listener: (context, state) {
        if (state is StatsFailed) {
          showErrorAlert(
              context: context,
              title: state.error,
              onClose: () {
                refresh();
              });
        }
      },
      child: BlocBuilder<EquipmentStatsBloc, StatsState>(
        builder: (context, state) {
          if (state is StatsLoading && state.showLoader == true) {
            return const PageLoadingIndicator();
          }
          if (state is StatsIdle || state is StatsFailed) {
            return Container();
          }
          final double topPaddingAppBar = Platform.isAndroid ? Spacings.x1 : 0;
          if (state is StatsLoaded) {
            nextAction = state.statsList.action;
            return Column(
              children: [
                Expanded(
                  child:
                      CustomScrollView(controller: _scrollController, slivers: [
                    SliverFrostyAppBar(
                      topPadding: topPaddingAppBar,
                      floating: true,
                      backButtonEnabled: false,
                      pinned: false,
                      title: statsScreenTitle,
                      scrollController: _scrollController,
                      extent: Platform.isAndroid ? 90 : 100,
                    ),
                    SliverList(
                        delegate: SliverChildBuilderDelegate((context, index) {
                      if (index == 0) {
                        return Container(
                          margin: EdgeInsets.fromLTRB(20, 20, 30, 30),
                          child: getProfileWidget(state.statsList),
                        );
                      }
                      return getStatsDetail(
                          state.statsList.listData[index - 1]);
                    }, childCount: state.statsList.listData.length + 1))
                  ]),
                ),
              ],
            );
          } else {
            return Container();
          }
        },
      ),
    );
  }

  Widget blocScaffold(Widget child) {
    return Padding(
        padding: EdgeInsets.zero,
        child: Scaffold(
          resizeToAvoidBottomInset: false,
          extendBodyBehindAppBar: true,
          body: child,
        ));
  }

  Widget getProfileWidget(StatsScreenArguments screenArgs) {
    return Row(
      children: [
        const SizedBox(width: 15),
        CircleAvatar(
            radius: 25,
            backgroundColor: Colors.black87,
            backgroundImage: NetworkImage(screenArgs.trainerImageUrl != null
                ? screenArgs.trainerImageUrl!
                : "https://trainer-pic.s3.ap-south-1.amazonaws.com/default_profile_picture.jpg")),
        const SizedBox(
          width: 25,
        ),
        Flexible(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(screenArgs.trainingName ?? "",
                  style: AuroraTheme.of(context).textStyle(TypescaleValues.H4),
                  overflow: TextOverflow.ellipsis),
              Text(
                "${screenArgs.trainerDetail}",
                style: AuroraTheme.of(context).textStyle(TypescaleValues.P5),
              )
            ],
          ),
        )
      ],
    );
  }

  Widget getStatsDetail(StatsScreenArgument args) {
    return Container(
      height: 64,
      margin: const EdgeInsets.fromLTRB(14, 5, 14, 5),
      padding: const EdgeInsets.all(10.0),
      decoration: myBoxDecoration(),
      child: Align(
        alignment: Alignment.center,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Row(
              children: [
                Container(
                    width: 30,
                    height: 30,
                    child: SvgPicture.asset(
                        "assets/equipment/${args.iconName ?? ""}")),
                const SizedBox(width: 10),
                Text(args.rowName ?? "",
                    style:
                        AuroraTheme.of(context).textStyle(TypescaleValues.P5)),
              ],
            ),
            Row(
              children: [
                Text(args.unitValue ?? "",
                    style:
                        AuroraTheme.of(context).textStyle(TypescaleValues.H1)),
                const SizedBox(width: 5),
                Text(args.unit ?? "",
                    style:
                        AuroraTheme.of(context).textStyle(TypescaleValues.P3))
              ],
            )
          ],
        ),
      ),
    );
  }

  BoxDecoration myBoxDecoration() {
    return BoxDecoration(
        borderRadius: const BorderRadius.all(Radius.circular(10)),
        color: const Color(0x00ffffff).withOpacity(0.11));
  }
}

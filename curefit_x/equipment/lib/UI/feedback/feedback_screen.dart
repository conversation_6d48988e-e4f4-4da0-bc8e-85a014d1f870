import 'package:common/action/action_handler.dart';
import 'package:common/analytics/analytics_repository.dart';
import 'package:common/constants/analytics_constants.dart';
import 'package:common/constants/common_constants.dart';
import 'package:common/ui/alert_view.dart';
import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/organisms/search_bar.dart' as AuroraSearchBar;
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:equipment/UI/components/feedback_row.dart';
import 'package:equipment/blocs/feedback/events.dart';
import 'package:equipment/blocs/feedback/feedback_bloc.dart';
import 'package:equipment/blocs/feedback/state.dart';
import 'package:equipment/constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class FeedbackScreen extends StatefulWidget {
  const FeedbackScreen({Key? key}) : super(key: key);

  @override
  _FeedbackScreenState createState() => _FeedbackScreenState();
}

class _FeedbackScreenState extends State<FeedbackScreen>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  late TextEditingController _textEditingController;
  FeedbackScreenArguments? screenArguments;
  String streamType = '';
  @override
  void initState() {
    super.initState();
    _textEditingController = TextEditingController();
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      screenArguments = getScreenArguments();
      setState(() {
        streamType = screenArguments?.streamType ?? '';
      });
    });
  }

  FeedbackScreenArguments? getScreenArguments() {
    final ScreenArguments? args =
        ModalRoute.of(context)!.settings.arguments as ScreenArguments?;
    if (args != null) {
      return FeedbackScreenArguments(args.params);
    }
    return null;
  }

  Widget blocScaffold(Widget child) {
    return Scaffold(
      appBar: AppBar(
        title: Text(feedbackScreenTitle),
      ),
      resizeToAvoidBottomInset: true,
      extendBodyBehindAppBar: true,
      body: child,
    );
  }

  void _showToastMsg(String message, BuildContext context) async {
    ScaffoldMessenger.of(context)
        .showSnackBar(SnackBar(
          content: Text(
            message,
            style: AuroraTheme.of(context).textStyle(TypescaleValues.P1),
            textAlign: TextAlign.center,
          ),
          duration: const Duration(seconds: 1),
        ))
        .closed
        .then((value) {
      Navigator.popUntil(
          context,
          (route) =>
              route.settings.name ==
              '/${EnumToString.convertToString(RouteNames.equipmentclp)}');
    });
  }

  Widget buildWidgets() {
    final feedbackBloc = BlocProvider.of<EquipmentFeedbackBloc>(context);
    List<int> feedbackList = [];
    return SafeArea(
      child: BlocListener<EquipmentFeedbackBloc, EquipmentFeedbackState>(
        listener: (context, state) {
          if (state is EquipmentInvalidFeedback) {
            showErrorAlert(
                context: context, title: state.errorMsg, onClose: () {});
          }
          if (state is EquipmentFeedbackLoaded) {
            if (state.feedbackStatus) {
              _showToastMsg(successfulFeedbackText, context);
            } else {
              _showToastMsg(ERROR_MSG, context);
            }
          } else if (state is EquipmentFeedbackFailed) {
            showErrorAlert(
                context: context,
                title: state.error,
                onClose: () {
                  // Navigator.pop(context);
                });
          }
        },
        child: BlocBuilder<EquipmentFeedbackBloc, EquipmentFeedbackState>(
          builder: (context, state) {
            if (state is EquipmentFeedbackIdle) {
              feedbackList = List.generate(4, (index) => -1);
            } else if (state is EquipmentFeedbackSelected) {
              feedbackList = state.feedback;
            } else if (state is EquipmentFeedbackLoaded) {}
            return Column(
              mainAxisSize: MainAxisSize.max,
              children: [
                Expanded(
                  child: SingleChildScrollView(
                      padding: const EdgeInsets.only(bottom: 80),
                      physics: const AlwaysScrollableScrollPhysics(),
                      child: Column(
                        children: [
                          for (int i = 0; i < 4; i++)
                            Padding(
                                padding: const EdgeInsets.only(
                                    bottom: Spacings.x5,
                                    left: Spacings.x4,
                                    right: Spacings.x4),
                                child: FeedbackRow(
                                  streamType: streamType,
                                  feedback: feedbackList,
                                  callback: (int columnIndex) {
                                    feedbackBloc.add(FeedbackSelectedEvent(
                                        column: columnIndex, row: i));
                                  },
                                  rowIndex: i,
                                )),
                          Padding(
                            padding: const EdgeInsets.symmetric(
                                horizontal: Spacings.x4),
                            child: AuroraSearchBar.SearchBar(
                              showPrefixIcon: false,
                              onTextChanged: (String str) {},
                              enabled: true,
                              placeholderText: PlaceHolderText,
                              textEditingController: _textEditingController,
                            ),
                          ),
                        ],
                      )),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return blocScaffold(LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth > 0) {
          return Stack(
            children: [
              Positioned.fill(
                  child: Container(
                color: themeColor,
              )),
              Aurora(
                hasBackgroundColor: false,
                canvasTheme: CanvasTheme.CLASSIC,
                size: constraints.biggest,
              ),
              buildWidgets(),
              BlocBuilder<EquipmentFeedbackBloc, EquipmentFeedbackState>(
                  builder: (context, state) {
                if (state is EquipmentFeedbackLoading) {
                  return const PageLoadingIndicator();
                }
                return Container();
              }),
              Positioned(
                  bottom: 0,
                  left: Spacings.x4,
                  right: Spacings.x4,
                  child: SafeArea(
                      child: Column(children: [
                    const SizedBox(
                      height: Spacings.x5,
                    ),
                    PrimaryButton(() {
                      final bloc =
                          BlocProvider.of<EquipmentFeedbackBloc>(context);
                      bloc.add(LoadEquipmentFeedbackEvent(
                        challengeType: screenArguments?.challengeType ?? '',
                        challengeId: screenArguments?.challengeId ?? '',
                        trainersEmail: screenArguments?.trainersEmail ?? '',
                        workoutId: screenArguments?.workoutId ?? '',
                        feedbackString: _textEditingController.text,
                      ));
                      RepositoryProvider.of<AnalyticsRepository>(context)
                          .logButtonClickEvent(extraInfo: {
                        "vertical": vertical_d2c,
                        "pageId": "feedback_screen",
                        "buttonType": "submit_feedback",
                        "productType": screenArguments?.challengeType ?? '',
                        "productId": screenArguments?.challengeId ?? '',
                        "subtitle": screenArguments?.trainersEmail ?? '',
                        "contentId": screenArguments?.workoutId ?? '',
                        "text": _textEditingController.text,
                        "actionText": bloc.feedback.toString(),
                        "trainer_rating":
                            bloc.feedback[0] != -1 ? bloc.feedback[0] + 1 : 0,
                        "music_rating":
                            bloc.feedback[1] != -1 ? bloc.feedback[1] + 1 : 0,
                        "app_rating":
                            bloc.feedback[2] != -1 ? bloc.feedback[2] + 1 : 0,
                        "bike_rating":
                            bloc.feedback[3] != -1 ? bloc.feedback[3] + 1 : 0,
                      });
                    }, SUBMIT)
                  ])))
            ],
          );
        }
        return Container();
      },
    ));
  }
}

class FeedbackScreenArguments {
  late String challengeId;
  late String trainersEmail;
  late String workoutId;
  late String challengeType;
  late String streamType;
  FeedbackScreenArguments(Map<String, dynamic> params) {
    challengeId = params['challenge_id'];
    trainersEmail = params['trainers_email'];
    workoutId = params['workout_id'];
    streamType = params['stream_type'];
    challengeType = params['challenge_type'];
  }
}

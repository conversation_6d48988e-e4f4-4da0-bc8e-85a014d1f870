import 'package:common/action/action_handler.dart';
import 'package:common/analytics/analytics_repository.dart';
import 'package:common/constants/action_constants.dart';
import 'package:common/constants/analytics_constants.dart';
import 'package:common/constants/common_constants.dart';
import 'package:common/font/cf_icons.dart';
import 'package:common/ui/alert_view.dart';
import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/atoms/secondary_button.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/theme/Colors.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/util/action_util.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:equipment/blocs/user_info/events.dart';
import 'package:equipment/blocs/user_info/state.dart';
import 'package:equipment/blocs/user_info/user_info_bloc.dart';
import 'package:equipment/constants/constants.dart';
import 'package:flutter/material.dart' hide Action;
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';

mixin InputValidationMixin {
  bool isWeightValid(double weight) {
    return (weight >= 25 && weight <= 180) ? true : false;
  }

  bool isHeightValid(int height) {
    return (height >= 100 && height <= 230) ? true : false;
  }
}

class EquipmentUserInfoScreen extends StatefulWidget {
  const EquipmentUserInfoScreen({Key? key}) : super(key: key);

  @override
  _EquipmentUserInfoScreenState createState() =>
      _EquipmentUserInfoScreenState();
}

class _EquipmentUserInfoScreenState extends State<EquipmentUserInfoScreen>
    with
        TickerProviderStateMixin,
        WidgetsBindingObserver,
        InputValidationMixin {
  late TextEditingController _textEditingControllerGender;
  late TextEditingController _textEditingControllerDOB;
  late TextEditingController _textEditingControllerWeight;
  late TextEditingController _textEditingControllerHeight;
  String selectedGender = 'Male';
  late EquipmentUserInfoBloc bloc;
  final formGlobalKey = GlobalKey<FormState>();
  UserInfoScreenArguments? screenArguments;
  @override
  void initState() {
    super.initState();
    bloc = BlocProvider.of<EquipmentUserInfoBloc>(context);
    _textEditingControllerGender = TextEditingController();
    _textEditingControllerDOB = TextEditingController();
    _textEditingControllerHeight = TextEditingController();
    _textEditingControllerWeight = TextEditingController();
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      screenArguments = getScreenArguments();

      setState(() {
        if (screenArguments?.gender != 'null' &&
            screenArguments?.gender != null) {
          _textEditingControllerGender.text = screenArguments?.gender ?? 'male';
          bloc.add(UserGenderEnteredEvent(
              gender: _textEditingControllerGender.text));
        }
        if (screenArguments?.height != 'null' &&
            screenArguments?.height != null) {
          _textEditingControllerHeight.text = screenArguments?.height ?? '';
          int height = _textEditingControllerHeight.text.isNotEmpty
              ? double.parse(_textEditingControllerHeight.text).round()
              : -1;
          _textEditingControllerHeight.text =
              height > -1 ? height.toString() : '';
          bloc.add(UserHeightEnteredEvent(
              height: height > -1 ? height.toString() : ''));
        }
        if (screenArguments?.weight != 'null' &&
            screenArguments?.weight != null) {
          _textEditingControllerWeight.text = screenArguments?.weight ?? '';
          bloc.add(UserWeightEnteredEvent(
              weight: _textEditingControllerWeight.text));
        }

        if (screenArguments?.dob != 'null' && screenArguments?.dob != null) {
          _textEditingControllerDOB.text = screenArguments?.dob ?? '';
          bloc.add(UserDOBEnteredEvent(dob: _textEditingControllerDOB.text));
        }
      });
    });
  }

  UserInfoScreenArguments? getScreenArguments() {
    final ScreenArguments? args =
        ModalRoute.of(context)!.settings.arguments as ScreenArguments?;
    if (args != null) {
      return UserInfoScreenArguments(args.params);
    }
    return null;
  }

  Widget blocScaffold(Widget child) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      extendBodyBehindAppBar: true,
      body: child,
    );
  }

  void _showToastMsg(
      String message, BuildContext context, bool navigateToNextScreen) async {
    ScaffoldMessenger.of(context)
        .showSnackBar(SnackBar(
          content: Text(
            message,
            style: AuroraTheme.of(context).textStyle(TypescaleValues.P1),
            textAlign: TextAlign.center,
          ),
          duration: const Duration(seconds: 1),
        ))
        .closed
        .then((value) {
      if (navigateToNextScreen) {
        clickActionWithAnalytics(
            screenArguments?.action != null
                ? Action(
                    meta: {
                        "height": _textEditingControllerHeight.text,
                        "weight": _textEditingControllerWeight.text,
                        "gender": _textEditingControllerGender.text,
                        "birthDate": _textEditingControllerDOB.text
                      },
                    type: EnumToString.fromString(ActionTypes.values,
                        screenArguments!.action['actionType']),
                    title: screenArguments!.action['title'],
                    url: screenArguments!.action['url'])
                : Action.fromJson(null),
            context,
            null,
            {});
      }
    });
  }

  Widget buildWidgets() {
    return SafeArea(
      child: BlocListener<EquipmentUserInfoBloc, EquipmentUserInfoState>(
        listener: (context, state) {
          if (state is EquipmentInvalidUserInfo) {
            _showToastMsg(ERROR_MSG, context, false);
            // showErrorAlert(
            //     context: context, title: state.errorMsg, onClose: () {});
          }
          if (state is EquipmentUserInfoLoaded) {
            if (state.isUserInfoUploadSuccessful) {
              _showToastMsg(successfulInfoUploadTest, context, true);
            } else {
              _showToastMsg(ERROR_MSG, context, false);
            }
          } else if (state is EquipmentUserInfoUpdateFailed) {
            _showToastMsg(ERROR_MSG, context, false);
            showErrorAlert(
                context: context, title: state.error, onClose: () {});
          }
        },
        child: BlocBuilder<EquipmentUserInfoBloc, EquipmentUserInfoState>(
          builder: (context, state) {
            final auroraTheme = AuroraTheme.of(context);
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                SingleChildScrollView(
                    scrollDirection: Axis.vertical,
                    physics: const AlwaysScrollableScrollPhysics(),
                    child: Form(
                      key: formGlobalKey,
                      autovalidateMode: AutovalidateMode.onUserInteraction,
                      child: Column(
                        children: [
                          getGender(auroraTheme),
                          getDOB(auroraTheme),
                          getCurrentWeight(auroraTheme),
                          getCurrentHeight(auroraTheme),
                          renderButtons(),
                        ],
                      ),
                    )),
              ],
            );
          },
        ),
      ),
    );
  }

  Padding renderButtons() {
    return Padding(
      padding: const EdgeInsets.only(
        right: Spacings.x3,
        left: Spacings.x3,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Expanded(
            child: SecondaryButton(
              () {
                Navigator.pop(context);
              },
              previous,
              buttonType: SecondaryButtonType.SMALL,
              expanded: true,
            ),
          ),
          const SizedBox(
            width: Spacings.x2,
          ),
          Expanded(
            child: PrimaryButton(
              () {
                bloc.add(LoadEquipmentUserInfoEvent());
                RepositoryProvider.of<AnalyticsRepository>(context)
                    .logButtonClickEvent(extraInfo: {
                  "vertical": vertical_d2c,
                  "buttonType": "submit_userinfo",
                  "pageId": "userInfoScreen",
                  "pageName": "userInfoScreen",
                  "title": "NEXT",
                  "text": {
                    "height": bloc.height,
                    "weight": bloc.weight,
                    "dob": bloc.dob,
                    "gender": bloc.gender
                  }.toString(),
                  "height": bloc.height,
                  "weight": bloc.weight,
                  "birthDate": bloc.dob,
                  "gender": bloc.gender
                });
              },
              next,
              buttonType: PrimaryButtonType.SMALL,
            ),
          )
        ],
      ),
    );
  }

  Widget getGender(AuroraThemeData auroraTheme) {
    return Padding(
      padding: const EdgeInsets.only(
          top: Spacings.x10,
          right: Spacings.x3,
          left: Spacings.x3,
          bottom: Spacings.x7),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Text(genderQuestion,
            style: auroraTheme.textStyle(TypescaleValues.H2,
                color: ColorPalette.white)),
        TextFormField(
          controller: _textEditingControllerGender,
          style:
              auroraTheme.textStyle(TypescaleValues.H3, color: Colors.white60),
          decoration: InputDecoration(
              enabledBorder: const UnderlineInputBorder(
                borderSide: BorderSide(
                  color: ColorPalette.white40,
                ),
              ),
              suffixIcon: IntrinsicHeight(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    InkWell(
                      onTap: () {
                        setState(() {
                          _textEditingControllerGender.text = 'male';
                        });
                        bloc.add(UserGenderEnteredEvent(gender: 'male'));
                      },
                      child: Icon(
                        CFIcons.male,
                        size: 20,
                        color: _textEditingControllerGender.text == 'male'
                            ? ColorPalette.white
                            : ColorPalette.white60,
                      ),
                    ),
                    const SizedBox(
                      height: 25,
                      child: VerticalDivider(
                        width: Spacings.x3,
                        thickness: 1,
                        indent: 1,
                        endIndent: 0,
                        color: Colors.white,
                      ),
                    ),
                    InkWell(
                      onTap: () {
                        setState(() {
                          _textEditingControllerGender.text = 'female';
                        });
                        bloc.add(UserGenderEnteredEvent(gender: 'female'));
                      },
                      child: Icon(
                        CFIcons.female,
                        size: 20,
                        color: _textEditingControllerGender.text == 'female'
                            ? ColorPalette.white
                            : ColorPalette.white60,
                      ),
                    ),
                  ],
                ),
              ),
              // labelText: defaultGender,
              // floatingLabelBehavior: FloatingLabelBehavior.never,
              hintStyle: auroraTheme.textStyle(TypescaleValues.H2),
              labelStyle: auroraTheme.textStyle(TypescaleValues.H2)),
        ),
      ]),
    );
  }

  Widget getDOB(AuroraThemeData auroraTheme) {
    return Padding(
      padding: const EdgeInsets.only(
          right: Spacings.x3, left: Spacings.x3, bottom: Spacings.x7),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Text(dateOfBirthQuestion,
            style: auroraTheme.textStyle(TypescaleValues.H2,
                color: ColorPalette.white)),
        TextFormField(
          controller: _textEditingControllerDOB,
          showCursor: true,
          readOnly: true,
          onTap: () {
            showDatePicker(
                    initialEntryMode: DatePickerEntryMode.calendarOnly,
                    context: context,
                    initialDate: DateTime.now(),
                    firstDate: DateTime(1950, 1, 1),
                    lastDate: DateTime(2050, 12, 31))
                .then((date) {
              if (date != null) {
                DateFormat formatter = DateFormat('yyyy-MM-dd');
                final String formatted = formatter.format(date);
                bloc.add(UserDOBEnteredEvent(dob: formatted));
                _textEditingControllerDOB.text = formatted;
              }
            });
          },
          style:
              auroraTheme.textStyle(TypescaleValues.H3, color: Colors.white60),
          decoration: InputDecoration(
            enabledBorder: const UnderlineInputBorder(
              borderSide: BorderSide(
                color: ColorPalette.white40,
              ),
            ),
            hintText: dateOfBirthHintText,
            hintStyle: auroraTheme.textStyle(TypescaleValues.P5,
                color: ColorPalette.white60),
          ),
        ),
      ]),
    );
  }

  Widget getCurrentWeight(AuroraThemeData auroraTheme) {
    return Padding(
      padding: const EdgeInsets.only(
          right: Spacings.x3, left: Spacings.x3, bottom: Spacings.x7),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Text(weightQuestion,
            style: auroraTheme.textStyle(TypescaleValues.H3,
                color: ColorPalette.white)),
        TextFormField(
          controller: _textEditingControllerWeight,
          validator: weightValidator,
          onChanged: onWeightChanged,
          style:
              auroraTheme.textStyle(TypescaleValues.H3, color: Colors.white60),
          keyboardType: const TextInputType.numberWithOptions(decimal: true),
          decoration: InputDecoration(
            floatingLabelBehavior: FloatingLabelBehavior.never,
            enabledBorder: const UnderlineInputBorder(
              borderSide: BorderSide(
                color: ColorPalette.white40,
              ),
            ),
            suffixIcon: Text(
              'kg',
              style: auroraTheme.textStyle(TypescaleValues.H3,
                  color: Colors.white60),
            ),
          ),
        ),
      ]),
    );
  }

  void onWeightChanged(String str) {
    bloc.add(UserWeightEnteredEvent(weight: str));
  }

  String? weightValidator(weight) {
    if (weight != null && weight.isNotEmpty) {
      if (isWeightValid(double.parse(weight))) return null;
      return 'Weight must be between 25kg to 180kg';
    }
    return null;
  }

  String? heightValidator(height) {
    if (height != null && height.isNotEmpty) {
      if (isHeightValid(double.parse(height).round())) return null;
      return 'Height must be between 100cm to 230cm';
    }
    return null;
  }

  Widget getCurrentHeight(AuroraThemeData auroraTheme) {
    return Padding(
      padding: const EdgeInsets.only(
          right: Spacings.x3, left: Spacings.x3, bottom: Spacings.x7),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Text(heightQuestion,
            style: auroraTheme.textStyle(TypescaleValues.H3,
                color: ColorPalette.white)),
        TextFormField(
          controller: _textEditingControllerHeight,
          onChanged: onHeightChanged,
          validator: heightValidator,
          style:
              auroraTheme.textStyle(TypescaleValues.H3, color: Colors.white60),
          keyboardType: TextInputType.number,
          inputFormatters: [FilteringTextInputFormatter.digitsOnly],
          decoration: InputDecoration(
            floatingLabelBehavior: FloatingLabelBehavior.never,
            enabledBorder: const UnderlineInputBorder(
              borderSide: BorderSide(
                color: ColorPalette.white40,
              ),
            ),
            suffixIcon: Text(
              'cm',
              style: auroraTheme.textStyle(TypescaleValues.H3,
                  color: Colors.white60),
            ),
          ),
        ),
      ]),
    );
  }

  void onHeightChanged(String str) {
    bloc.add(UserHeightEnteredEvent(height: str));
  }

  @override
  Widget build(BuildContext context) {
    return blocScaffold(LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth > 0) {
          return Stack(
            children: [
              Positioned.fill(
                  child: Container(
                color: themeColor,
              )),
              Aurora(
                hasBackgroundColor: false,
                canvasTheme: CanvasTheme.CLASSIC,
                size: constraints.biggest,
              ),
              buildWidgets(),
              BlocBuilder<EquipmentUserInfoBloc, EquipmentUserInfoState>(
                  builder: (context, state) {
                if (state is EquipmentUserInfoLoading) {
                  return const PageLoadingIndicator();
                }
                return Container();
              }),
            ],
          );
        }
        return Container();
      },
    ));
  }
}

class UserInfoScreenArguments {
  String? height;
  String? weight;
  String? dob;
  String? gender;
  dynamic action;
  UserInfoScreenArguments(Map<String, dynamic> params) {
    height = params['height'];
    weight = params['weight'];
    gender = params['gender'];
    dob = params['birthDate'];
    action = params['action'];
  }
}

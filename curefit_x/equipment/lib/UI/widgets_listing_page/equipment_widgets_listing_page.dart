import 'dart:io';

import 'package:common/action/action_handler.dart';
import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/ui/alert_view.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/organisms/titlebar/frosty_app_bar.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:common/ui/widget_factory.dart';
import 'package:common/ui/widgets/empty_cart_widget.dart';
import 'package:equipment/blocs/widgets_listing_page/widgets_listing_bloc.dart';
import 'package:equipment/blocs/widgets_listing_page/events.dart';
import 'package:equipment/blocs/widgets_listing_page/state.dart';
import 'package:equipment/constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class EquipmentWidgetsListingPage extends StatefulWidget {
  const EquipmentWidgetsListingPage({Key? key}) : super(key: key);

  @override
  _EquipmentWidgetsListingPageState createState() =>
      _EquipmentWidgetsListingPageState();
}

class _EquipmentWidgetsListingPageState
    extends State<EquipmentWidgetsListingPage>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  late ScrollController _scrollController;
  EquipmentWidgetsListingPageScreenArguments? screenArguments;
  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      screenArguments = getScreenArguments();
      refresh(context: context, showLoader: true);
    });
  }

  EquipmentWidgetsListingPageScreenArguments? getScreenArguments() {
    final ScreenArguments? args =
        ModalRoute.of(context)!.settings.arguments as ScreenArguments?;
    if (args != null) {
      return EquipmentWidgetsListingPageScreenArguments(args.params);
    }
    return null;
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  onBackPress() {
    if (Navigator.canPop(context)) {
      Navigator.pop(context);
    } else if (!Navigator.canPop(context)) {
      ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
      actionBloc.add(CloseApplicationEvent(shouldReset: false));
    }
  }

  Widget buildWidgets() {
    List widgets = [];
    String pageTitle = '';
    return BlocListener<EquipmentWidgetListingPageBloc,
        EquipmentWidgetListingPageState>(
      listener: (context, state) {
        if (state is EquipmentWidgetListingPageLoadFailed) {
          showErrorAlert(
              context: context,
              title: state.error,
              onClose: () {
                Navigator.pop(context);
              });
        }
      },
      child: BlocBuilder<EquipmentWidgetListingPageBloc,
          EquipmentWidgetListingPageState>(
        builder: (context, state) {
          if (state is EquipmentWidgetListingPageLoaded) {
            widgets = state.widgets;
            pageTitle = state.pageName ?? '';
          }

          WidgetFactory widgetFactory =
              RepositoryProvider.of<WidgetFactory>(context);

          final double topPaddingAppBar = Platform.isAndroid ? Spacings.x5 : 0;
          return CustomScrollView(controller: _scrollController, slivers: [
            SliverFrostyAppBar(
              topPadding: topPaddingAppBar,
              floating: true,
              pinned: false,
              scrollController: _scrollController,
              title: pageTitle,
              extent: 100,
              onBackPressed: onBackPress,
            ),
            SliverList(
                delegate: SliverChildBuilderDelegate((context, index) {
              return widgetFactory.createWidget(widgets[index]);
            }, childCount: widgets.length))
          ]);

          return Container();
        },
      ),
    );
  }

  refresh({required BuildContext context, bool showLoader = false}) {
    final storeBloc = BlocProvider.of<EquipmentWidgetListingPageBloc>(context);
    storeBloc.add(LoadEquipmentWidgetListingPageEvent(
        showLoader: showLoader, pageType: screenArguments?.pageType ?? ''));
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    switch (state) {
      case AppLifecycleState.resumed:
        logPageView();
        break;
      case AppLifecycleState.inactive:
        break;
      case AppLifecycleState.paused:
        break;
      case AppLifecycleState.detached:
        break;
    }
  }

  logPageView() {
    AnalyticsRepository analyticsRepository =
        RepositoryProvider.of<AnalyticsRepository>(context);
    analyticsRepository.logPageViewEvent(
        pageId: EnumToString.convertToString(RouteNames.cultrowwidgetslistpage),
        eventInfo: {});
  }

  @override
  Widget build(BuildContext context) {
    return blocScaffold(LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth > 0) {
          return Stack(
            children: [
              Positioned.fill(
                  child: Container(
                color: themeColor,
              )),
              Aurora(
                hasBackgroundColor: false,
                canvasTheme: CanvasTheme.CLASSIC,
                size: constraints.biggest,
              ),
              Positioned.fill(child: buildWidgets()),
              BlocBuilder<EquipmentWidgetListingPageBloc,
                  EquipmentWidgetListingPageState>(
                builder: (context, state) {
                  if (state is EquipmentWidgetListingPageLoading) {
                    return const FancyLoadingIndicator();
                  } else if (state is EquipmentWidgetListingPageLoaded &&
                      state.widgets.first['widgetType'] ==
                          'EMPTY_DIAGNOSTIC_CART_LISTING_WIDGET') {
                    return Padding(
                      padding: const EdgeInsets.only(bottom: Spacings.x6),
                      child: Center(
                        child: EmptyCartWidget(
                          emptyCartWidgetData: EmptyCartWidgetData.fromJson(
                              state.widgets.first,
                              WidgetTypes
                                  .EMPTY_DIAGNOSTIC_CART_LISTING_WIDGET,
                              null),
                        ),
                      ),
                    );
                  }

                  return Container();
                },
              ),
            ],
          );
        }
        return Container();
      },
    ));
  }

  Widget blocScaffold(Widget child) {
    return Padding(
        padding: EdgeInsets.zero,
        child: Scaffold(
          resizeToAvoidBottomInset: false,
          extendBodyBehindAppBar: true,
          body: child,
        ));
  }
}

class EquipmentWidgetsListingPageScreenArguments {
  String pageType = '';
  EquipmentWidgetsListingPageScreenArguments(Map<String, dynamic> params) {
    pageType = params["pageType"];
  }
}

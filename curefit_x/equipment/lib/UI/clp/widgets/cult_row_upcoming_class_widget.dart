import 'package:common/action/action_handler.dart';
import 'package:common/analytics/analytics_repository.dart';
import 'package:common/constants/analytics_constants.dart';
import 'package:common/network/client.dart';
import 'package:common/ui/theme/Colors.dart';
import 'package:common/util/action_util.dart';
import 'package:equipment/blocs/cult_row_upcoming_class_widget/cult_row_upcoming_class_widget_bloc.dart';
import 'package:equipment/network/cultrow_upcoming_class_book_repository.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:common/ui/header/card_header.dart';
import 'package:common/ui/molecules/inline_tabs.dart';
import 'package:common/ui/molecules/listview_smallthumbnail.dart';
import 'package:common/ui/organisms/small_thumbnail_module.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:flutter/material.dart' hide Action;

import '../../../blocs/cult_row_upcoming_class_widget/cult_row_upcoming_class_widget_state.dart';

class CultRowUpcomingClassWidget extends StatefulWidget {
  final CultRowUpcomingClassWidgetData widgetData;
  const CultRowUpcomingClassWidget({Key? key, required this.widgetData})
      : super(key: key);

  @override
  _CultRowUpcomingClassWidgetState createState() =>
      _CultRowUpcomingClassWidgetState();
}

class _CultRowUpcomingClassWidgetState
    extends State<CultRowUpcomingClassWidget> {
  int selectedTopTabIndex = 0;
  int currentSelectedRowIndex = -1;
  List<int> selectedRowIndex = [];
  List<int> snackBarShown =
      []; // To fix the repetitive rendering of snack bars and loaders
  List<int> loadingIndicatorShown =
      []; // To fix the repetitive rendering of snack bars and loaders
  @override
  Widget build(BuildContext context) {
    int selectedTabIndex = 0;
    List<ChipData> chipsData = widget.widgetData.sections.map<ChipData>((e) {
      return ChipData(
          buttonText: e.title,
          isSelected: selectedTabIndex++ == selectedTopTabIndex ? true : false);
    }).toList();
    return BlocProvider<CultRowUpcomingClassBloc>(
      create: (context) => CultRowUpcomingClassBloc(
          repository: CultRowUpcomingClassBookRepository(
              RepositoryProvider.of<NetworkClient>(context))),
      child: BlocBuilder<CultRowUpcomingClassBloc,
          CultRowUpcomingClassWidgetState>(builder: (context, state) {
        List<ListViewSmallThumbnailData> items = [];
        int currentRowIndex = -1;
        for (var e in widget.widgetData
            .items[widget.widgetData.sections[selectedTopTabIndex].id]!) {
          Action? newAction;
          currentRowIndex++;
          if ('BOOK_CULT_ROW_CLASS' ==
              EnumToString.convertToString(e.textAction?.type)) {
            Bloc bloc = BlocProvider.of<CultRowUpcomingClassBloc>(context);
            String buttonText = e.textAction?.title ?? 'BOOK';

            if (state is CultRowUpcomingClassWidgetLoading &&
                selectedRowIndex.contains(currentRowIndex)) {
              if (snackBarShown.contains(currentRowIndex) &&
                  loadingIndicatorShown.contains(currentRowIndex)) {
                buttonText = 'BOOKED';
              } else {
                buttonText = 'Loading';
              }
              loadingIndicatorShown.add(currentSelectedRowIndex);
            } else if (state is CultRowUpcomingClassWidgetBookingSuccess &&
                selectedRowIndex.contains(currentRowIndex)) {
              buttonText = state.buttonText ?? 'BOOKED';
              if (!snackBarShown.contains(currentRowIndex) &&
                  !snackBarShown.contains(currentSelectedRowIndex)) {
                snackBarShown.add(currentSelectedRowIndex);
                snackBar(context);
              }
            }
            newAction = Action.fromAction(
                Action(title: buttonText, type: e.textAction!.type, bloc: bloc),
                e.textAction!.meta!);
          }
          items.add(ListViewSmallThumbnailData(
              image: e.image,
              imageLength: 70,
              heading: e.heading,
              title: e.title,
              textAction: newAction ?? e.textAction,
              isTextActionVariant: true,
              isTextActionIcon: true));
        }

        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: Spacings.x4),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              if (widget.widgetData.widgetHeaderData != null)
                WidgetHeader(
                    cardHeaderData: widget.widgetData.widgetHeaderData!,
                    onActionPress: (Action? action) {
                      performHeaderClickAction(
                          action, context, widget.widgetData.widgetInfo);
                    }),
              if (widget.widgetData.widgetHeaderData != null)
                const SizedBox(
                  height: Spacings.x2,
                ),
              InlineTabs(
                callBack: ((selected, {index}) {
                  RepositoryProvider.of<AnalyticsRepository>(context)
                      .logButtonClickEvent(extraInfo: {
                    "vertical": vertical_d2c,
                    "pageId": widget.widgetData.widgetInfo?.pageId,
                    "pageName": widget.widgetData.widgetInfo?.pageName,
                    "pageType": widget.widgetData.widgetInfo?.pageType,
                    "widgetType": widget.widgetData.widgetInfo?.widgetType,
                    "title": widget.widgetData.sections[index!].title,
                  });
                  if (selected) {
                    setState(() {
                      selectedTopTabIndex = index;
                    });
                  }
                }),
                chips: chipsData,
              ),
              SmallThumbnailModule(
                  textActionCallback: (int index) {
                    final textAction = widget
                        .widgetData
                        .items[widget.widgetData.sections[selectedTopTabIndex]
                            .title]?[index]
                        .textAction;
                    RepositoryProvider.of<AnalyticsRepository>(context)
                        .logWidgetClick(
                            widgetInfo: widget.widgetData.widgetInfo,
                            extraInfo: {
                          'buttonText': textAction?.title ?? '',
                          'challengeId':
                              textAction?.meta?['challenge_id'] ?? '',
                          'workout_id': textAction?.meta?['workout_id'] ?? '',
                          'userId': textAction?.meta?['userId'] ?? ''
                        });
                    setState(() {
                      selectedRowIndex.add(index);
                      currentSelectedRowIndex = index;
                    });
                  },
                  data: SmallThumbnailModuleData(
                    isListDivider: true,
                    items: items,
                  ))
            ],
          ),
        );

        // return Container();
      }),
    );
  }

  void performHeaderClickAction(Action? action, BuildContext context,
      [WidgetInfo? widgetInfo]) {
    if (action != null) {
      clickActionWithAnalytics(action, context, widgetInfo, {});
    }
  }

  void snackBar(BuildContext context) async {
    ScaffoldMessenger.of(context).showSnackBar(const SnackBar(
        backgroundColor: ColorPalette.statusPositive,
        content: Text('Successfully Booked the class'),
        duration: Duration(seconds: 2)));
  }
}

class Section {
  String title;
  String id;
  Section({required this.title, required this.id});
}

class CultRowUpcomingClassWidgetData implements IWidgetData {
  @override
  WidgetInfo? widgetInfo;

  @override
  WidgetTypes widgetType;
  WidgetHeaderData? widgetHeaderData;
  List<Section> sections;
  Map<String, List<ListViewSmallThumbnailData>> items = {};
  CultRowUpcomingClassWidgetData(
      {required this.widgetType,
      required this.sections,
      this.widgetHeaderData,
      required this.items,
      this.widgetInfo});

  factory CultRowUpcomingClassWidgetData.fromJson(
      {required WidgetTypes widgetType,
      WidgetInfo? widgetInfo,
      dynamic widgetData}) {
    List<Section> sections = widgetData['sections']
        .map<Section>((e) => Section(title: e['title'], id: e['id']))
        .toList();
    WidgetHeaderData? widgetHeaderData;
    if (widgetData['header'] != null) {
      widgetHeaderData = WidgetHeaderData.fromJson(widgetData['header']);
    }
    Map<String, List<ListViewSmallThumbnailData>> items = {};
    widgetData['data'].entries.forEach((entry) {
      items[entry.key] = entry.value
          .map<ListViewSmallThumbnailData>((e) => ListViewSmallThumbnailData(
              image: e['imageUrl'],
              imageLength: 70,
              heading: e['subtitle'],
              title: e['title'],
              textAction: e['rightAction'] != null
                  ? Action.fromJson(e['rightAction'])
                  : null,
              isTextActionVariant: true,
              isTextActionIcon: true))
          .toList();
      return entry;
    });
    return CultRowUpcomingClassWidgetData(
        widgetType: widgetType,
        items: items,
        sections: sections,
        widgetHeaderData: widgetHeaderData,
        widgetInfo: widgetInfo);
  }
}

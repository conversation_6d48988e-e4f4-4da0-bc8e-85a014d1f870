import 'dart:io';

import 'package:common/action/action_handler.dart';
import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/blocs/navigation/navigation_bloc.dart';
import 'package:common/blocs/navigation/state.dart';
import 'package:common/ui/alert_view.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/bluetooth_connectivity/ble_screen.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/molecules/bottom_tray.dart';
import 'package:common/ui/organisms/titlebar/frosty_app_bar.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/widget_factory.dart';
import 'package:common/util/orientation.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:equipment/blocs/clp/clp_bloc.dart';
import 'package:equipment/blocs/clp/events.dart';
import 'package:equipment/blocs/clp/state.dart';
import 'package:equipment/constants/constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:navigation_history_observer/navigation_history_observer.dart';

class EquipmentCLP extends StatefulWidget {
  const EquipmentCLP({Key? key}) : super(key: key);

  @override
  _EquipmentCLPState createState() => _EquipmentCLPState();
}

class _EquipmentCLPState extends State<EquipmentCLP>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  late ScrollController _scrollController;
  EquipmentClpScreenArguments? screenArguments;
  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      screenArguments = getScreenArguments();
      refresh(context: context, showLoader: true);
      if (screenArguments?.showDeviceConnectionSheet ?? false) {
        showBottomTray(context: context, child: const BleScreen());
      }
    });
    lockToPortrait();
  }

  EquipmentClpScreenArguments? getScreenArguments() {
    final ScreenArguments? args =
        ModalRoute.of(context)!.settings.arguments as ScreenArguments?;
    if (args != null) {
      return EquipmentClpScreenArguments(args.params);
    }
    return null;
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  onBackPress() {
    if (Navigator.canPop(context)) {
      Navigator.pop(context);
    } else if (!Navigator.canPop(context)) {
      ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
      actionBloc.add(CloseApplicationEvent(shouldReset: false));
    }
  }

  Widget buildWidgets() {
    List widgets = [];
    return MultiBlocListener(
      listeners: [
        BlocListener<EquipmentCLPBloc, EquipmentCLPState>(
          listener: (context, state) {
            if (state is EquipmentCLPLoadFailed) {
              showErrorAlert(
                  context: context,
                  title: state.error,
                  onClose: () {
                    Navigator.pop(context);
                  });
            }
          },
        ),
        BlocListener<NavigationBloc, NavigationState>(
          listener: (context, state) {
            if (state is NavigationStackUpdated &&
                state.action == NavigationStackAction.pop &&
                state.route?.settings.name ==
                    '/${EnumToString.convertToString(RouteNames.equipmentclp)}') {
              refresh(context: context);
            }
          },
        ),
      ],
      child: BlocBuilder<EquipmentCLPBloc, EquipmentCLPState>(
        builder: (context, state) {
          if (state is EquipmentCLPLoaded) {
            widgets = state.widgets;
          }

          WidgetFactory widgetFactory =
              RepositoryProvider.of<WidgetFactory>(context);

          final double topPaddingAppBar = Platform.isAndroid ? Spacings.x5 : 0;
          return CustomScrollView(controller: _scrollController, slivers: [
            SliverFrostyAppBar(
              topPadding: topPaddingAppBar,
              floating: true,
              pinned: false,
              scrollController: _scrollController,
              title: 'cultROW',
              extent: Platform.isAndroid ? 90 : 100,
              onBackPressed: onBackPress,
            ),
            SliverList(
                delegate: SliverChildBuilderDelegate((context, index) {
              return widgetFactory.createWidget(widgets[index]);
            }, childCount: widgets.length))
          ]);
        },
      ),
    );
  }

  refresh({required BuildContext context, bool showLoader = false}) {
    final storeBloc = BlocProvider.of<EquipmentCLPBloc>(context);
    storeBloc.add(LoadEquipmentCLPEvent(
        showLoader: showLoader,
        pageId: screenArguments?.pageId ?? 'cultrowlist'));
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    switch (state) {
      case AppLifecycleState.resumed:
        logPageView();
        refresh(context: context);
        break;
      case AppLifecycleState.inactive:
        break;
      case AppLifecycleState.paused:
        break;
      case AppLifecycleState.detached:
        break;
    }
  }

  logPageView() {
    AnalyticsRepository analyticsRepository =
        RepositoryProvider.of<AnalyticsRepository>(context);
    analyticsRepository.logPageViewEvent(
        pageId: EnumToString.convertToString(RouteNames.equipmentclp),
        eventInfo: {});
  }

  @override
  Widget build(BuildContext context) {
    return blocScaffold(LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth > 0) {
          return Stack(
            children: [
              Positioned.fill(
                  child: Container(
                color: themeColor,
              )),
              Aurora(
                hasBackgroundColor: false,
                canvasTheme: CanvasTheme.CLASSIC,
                size: constraints.biggest,
              ),
              Positioned.fill(child: buildWidgets()),
              BlocBuilder<EquipmentCLPBloc, EquipmentCLPState>(
                builder: (context, state) {
                  if (state is EquipmentCLPLoading) {
                    return const PageLoadingIndicator();
                  }
                  return Container();
                },
              ),
            ],
          );
        }
        return Container();
      },
    ));
  }

  Widget blocScaffold(Widget child) {
    return Padding(
        padding: EdgeInsets.zero,
        child: Scaffold(
          resizeToAvoidBottomInset: true,
          extendBodyBehindAppBar: true,
          body: child,
        ));
  }
}

class EquipmentClpScreenArguments {
  String pageId = '';
  bool? showDeviceConnectionSheet;
  EquipmentClpScreenArguments(Map<String, dynamic> params) {
    pageId = params["pageId"];
    showDeviceConnectionSheet = params['connect_device'] == "true";
  }
}

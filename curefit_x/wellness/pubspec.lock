# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  _flutterfire_internals:
    dependency: transitive
    description:
      name: _flutterfire_internals
      sha256: "37a42d06068e2fe3deddb2da079a8c4d105f241225ba27b7122b37e9865fd8f7"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.35"
  advance_pdf_viewer_fork:
    dependency: transitive
    description:
      name: advance_pdf_viewer_fork
      sha256: b011f30fd73e909a54719335dc20824a33e88a52f728c926ca6b9dcfb9100e45
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  animated_flip_counter:
    dependency: transitive
    description:
      name: animated_flip_counter
      sha256: "73f852d84c461c3e4c1ddf320bee334dde8dba89441922ab11a8013be0b2fad1"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.4"
  animated_size_and_fade:
    dependency: transitive
    description:
      name: animated_size_and_fade
      sha256: a5ad19076f5720cbd5f02db27052ad6a8ccab0fc14e0122f26c2ef6b49904fc5
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  animations:
    dependency: transitive
    description:
      name: animations
      sha256: b70b084d3a3895aab7764bd505b89e72c199e5edcd1e4e3af0c76b52c2c7c5b6
      url: "https://pub.dev"
    source: hosted
    version: "2.0.2"
  app_badge_plus:
    dependency: transitive
    description:
      name: app_badge_plus
      sha256: cda59a8194352e308c824af6099bd338917b17d065a3bce6818013f032099611
      url: "https://pub.dev"
    source: hosted
    version: "1.1.6"
  app_settings:
    dependency: transitive
    description:
      name: app_settings
      sha256: "476df1d85cec143c3d27dd1c7451629a59c0c5ccf70a0adcbfa92a0a2d928705"
      url: "https://pub.dev"
    source: hosted
    version: "5.2.0"
  archive:
    dependency: transitive
    description:
      name: archive
      sha256: eb33140ede1b4039f4ad631f7bf3cfa58e24514e8bf87184bc32f17541af87fc
      url: "https://pub.dev"
    source: hosted
    version: "3.3.0"
  args:
    dependency: transitive
    description:
      name: args
      sha256: b003c3098049a51720352d219b0bb5f219b60fbfb68e7a4748139a06a5676515
      url: "https://pub.dev"
    source: hosted
    version: "2.3.1"
  async:
    dependency: transitive
    description:
      name: async
      sha256: "947bfcf187f74dbc5e146c9eb9c0f10c9f8b30743e341481c1e2ed3ecc18c20c"
      url: "https://pub.dev"
    source: hosted
    version: "2.11.0"
  audioplayers:
    dependency: transitive
    description:
      name: audioplayers
      sha256: c05c6147124cd63e725e861335a8b4d57300b80e6e92cea7c145c739223bbaef
      url: "https://pub.dev"
    source: hosted
    version: "5.2.1"
  audioplayers_android:
    dependency: transitive
    description:
      name: audioplayers_android
      sha256: b00e1a0e11365d88576320ec2d8c192bc21f1afb6c0e5995d1c57ae63156acb5
      url: "https://pub.dev"
    source: hosted
    version: "4.0.3"
  audioplayers_darwin:
    dependency: transitive
    description:
      name: audioplayers_darwin
      sha256: "3034e99a6df8d101da0f5082dcca0a2a99db62ab1d4ddb3277bed3f6f81afe08"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.2"
  audioplayers_linux:
    dependency: transitive
    description:
      name: audioplayers_linux
      sha256: "60787e73fefc4d2e0b9c02c69885402177e818e4e27ef087074cf27c02246c9e"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  audioplayers_platform_interface:
    dependency: transitive
    description:
      name: audioplayers_platform_interface
      sha256: "365c547f1bb9e77d94dd1687903a668d8f7ac3409e48e6e6a3668a1ac2982adb"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.0"
  audioplayers_web:
    dependency: transitive
    description:
      name: audioplayers_web
      sha256: "22cd0173e54d92bd9b2c80b1204eb1eb159ece87475ab58c9788a70ec43c2a62"
      url: "https://pub.dev"
    source: hosted
    version: "4.1.0"
  audioplayers_windows:
    dependency: transitive
    description:
      name: audioplayers_windows
      sha256: "9536812c9103563644ada2ef45ae523806b0745f7a78e89d1b5fb1951de90e1a"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  back_button_interceptor:
    dependency: transitive
    description:
      name: back_button_interceptor
      sha256: b85977faabf4aeb95164b3b8bf81784bed4c54ea1aef90a036ab6927ecf80c5a
      url: "https://pub.dev"
    source: hosted
    version: "8.0.4"
  bloc:
    dependency: transitive
    description:
      name: bloc
      sha256: "3820f15f502372d979121de1f6b97bfcf1630ebff8fe1d52fb2b0bfa49be5b49"
      url: "https://pub.dev"
    source: hosted
    version: "8.1.2"
  blur_bottom_bar:
    dependency: transitive
    description:
      name: blur_bottom_bar
      sha256: c675cc13a7df9374cfd7b305dfef1d4b089c6ec5aaa31fd4a1f77ae86f363afc
      url: "https://pub.dev"
    source: hosted
    version: "1.0.3"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      sha256: "6cfb5af12253eaf2b368f07bacc5a80d1301a071c73360d746b7f2e32d762c66"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  built_collection:
    dependency: transitive
    description:
      name: built_collection
      sha256: "376e3dd27b51ea877c28d525560790aee2e6fbb5f20e2f85d5081027d94e2100"
      url: "https://pub.dev"
    source: hosted
    version: "5.1.1"
  cached_network_image:
    dependency: transitive
    description:
      name: cached_network_image
      sha256: f98972704692ba679db144261172a8e20feb145636c617af0eb4022132a6797f
      url: "https://pub.dev"
    source: hosted
    version: "3.3.0"
  cached_network_image_platform_interface:
    dependency: transitive
    description:
      name: cached_network_image_platform_interface
      sha256: "56aa42a7a01e3c9db8456d9f3f999931f1e05535b5a424271e9a38cabf066613"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  cached_network_image_web:
    dependency: transitive
    description:
      name: cached_network_image_web
      sha256: "759b9a9f8f6ccbb66c185df805fac107f05730b1dab9c64626d1008cca532257"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  carousel_slider:
    dependency: transitive
    description:
      path: "."
      ref: HEAD
      resolved-ref: "2c3cf5503782fd4b60033667eb27695919318ce5"
      url: "https://github.com/curefit/flutter_carousel_slider.git"
    source: git
    version: "4.0.0"
  carp_serializable:
    dependency: transitive
    description:
      name: carp_serializable
      sha256: f039f8ea22e9437aef13fe7e9743c3761c76d401288dcb702eadd273c3e4dcef
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  characters:
    dependency: transitive
    description:
      name: characters
      sha256: f71061c654a3380576a52b451dd5532377954cf9dbd272a78fc8479606670803
      url: "https://pub.dev"
    source: hosted
    version: "1.4.0"
  charcode:
    dependency: transitive
    description:
      name: charcode
      sha256: fb98c0f6d12c920a02ee2d998da788bca066ca5f148492b7085ee23372b12306
      url: "https://pub.dev"
    source: hosted
    version: "1.3.1"
  clock:
    dependency: transitive
    description:
      name: clock
      sha256: fddb70d9b5277016c77a80201021d40a2247104d9f4aa7bab7157b7e3f05b84b
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  collection:
    dependency: transitive
    description:
      name: collection
      sha256: "2f5709ae4d3d59dd8f7cd309b4e023046b57d8a6c82130785d2b0e5868084e76"
      url: "https://pub.dev"
    source: hosted
    version: "1.19.1"
  common:
    dependency: transitive
    description:
      path: common
      ref: "common-v0.0.1942-firebase"
      resolved-ref: "73433adef656657a04fe7aca81a2c1edc97ebad6"
      url: "**************:curefit/flutter-packages.git"
    source: git
    version: "0.0.1941"
  connectivity_plus:
    dependency: transitive
    description:
      name: connectivity_plus
      sha256: e8eba38c50768741057c5ba0adda72bf79869e1db1f4957c5669f618ef5c359d
      url: "https://pub.dev"
    source: hosted
    version: "2.3.6"
  connectivity_plus_linux:
    dependency: transitive
    description:
      name: connectivity_plus_linux
      sha256: "3caf859d001f10407b8e48134c761483e4495ae38094ffcca97193f6c271f5e2"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.1"
  connectivity_plus_macos:
    dependency: transitive
    description:
      name: connectivity_plus_macos
      sha256: "918e08992649c85bfdb80b07d5de3e17257f776c1db8c76cadabc517f234edee"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.4"
  connectivity_plus_platform_interface:
    dependency: transitive
    description:
      name: connectivity_plus_platform_interface
      sha256: cab163c8d1b2327297950f171e962f35300f247b19f0bc52dff48c12762e837e
      url: "https://pub.dev"
    source: hosted
    version: "1.2.1"
  connectivity_plus_web:
    dependency: transitive
    description:
      name: connectivity_plus_web
      sha256: b9141dbab92c607758946f2f38eb209d828655dc2535b44b1a9f09c9d4a22fbf
      url: "https://pub.dev"
    source: hosted
    version: "1.2.3"
  connectivity_plus_windows:
    dependency: transitive
    description:
      name: connectivity_plus_windows
      sha256: "535b0404b4d5605c4dd8453d67e5d6d2ea0dd36e3b477f50f31af51b0aeab9dd"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.2"
  core:
    dependency: "direct main"
    description:
      path: "../core"
      relative: true
    source: path
    version: "0.0.1"
  cross_file:
    dependency: transitive
    description:
      name: cross_file
      sha256: "0b0036e8cccbfbe0555fd83c1d31a6f30b77a96b598b35a5d36dd41f718695e9"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.3+4"
  crypto:
    dependency: transitive
    description:
      name: crypto
      sha256: aa274aa7774f8964e4f4f38cc994db7b6158dd36e9187aaceaddc994b35c6c67
      url: "https://pub.dev"
    source: hosted
    version: "3.0.2"
  csslib:
    dependency: transitive
    description:
      name: csslib
      sha256: d1cd6d6e4b39a4ad295204722b8608f19981677b223f3e942c0b5a33dcf57ec0
      url: "https://pub.dev"
    source: hosted
    version: "0.17.1"
  dash_chat_2:
    dependency: transitive
    description:
      path: "packages/Dash-Chat-2"
      ref: e3fd64db35c2fe56200ed06e33c66e07d33d66c4
      resolved-ref: e3fd64db35c2fe56200ed06e33c66e07d33d66c4
      url: "**************:curefit/flutter-plugins.git"
    source: git
    version: "0.0.6"
  dbus:
    dependency: transitive
    description:
      name: dbus
      sha256: "365c771ac3b0e58845f39ec6deebc76e3276aa9922b0cc60840712094d9047ac"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.10"
  device_apps:
    dependency: transitive
    description:
      name: device_apps
      sha256: e84dc74d55749993fd671148cc0bd53096e1be0c268fc364285511b1d8a4c19b
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  device_calendar:
    dependency: transitive
    description:
      path: "."
      ref: "76a731a0815bb83c4e07d945584b3c47506e8684"
      resolved-ref: "76a731a0815bb83c4e07d945584b3c47506e8684"
      url: "**************:curefit/device_calendar.git"
    source: git
    version: "4.3.1"
  device_info_plus:
    dependency: transitive
    description:
      name: device_info_plus
      sha256: a7fd703482b391a87d60b6061d04dfdeab07826b96f9abd8f5ed98068acc0074
      url: "https://pub.dev"
    source: hosted
    version: "10.1.2"
  device_info_plus_platform_interface:
    dependency: transitive
    description:
      name: device_info_plus_platform_interface
      sha256: "282d3cf731045a2feb66abfe61bbc40870ae50a3ed10a4d3d217556c35c8c2ba"
      url: "https://pub.dev"
    source: hosted
    version: "7.0.1"
  dotted_border:
    dependency: transitive
    description:
      name: dotted_border
      sha256: "108837e11848ca776c53b30bc870086f84b62ed6e01c503ed976e8f8c7df9c04"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  easy_debounce:
    dependency: transitive
    description:
      name: easy_debounce
      sha256: f082609cfb8f37defb9e37fc28bc978c6712dedf08d4c5a26f820fa10165a236
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
  enum_to_string:
    dependency: transitive
    description:
      name: enum_to_string
      sha256: bd9e83a33b754cb43a75b36a9af2a0b92a757bfd9847d2621ca0b1bed45f8e7a
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  equatable:
    dependency: transitive
    description:
      name: equatable
      sha256: c2b87cb7756efdf69892005af546c56c0b5037f54d2a88269b4f347a505e3ca2
      url: "https://pub.dev"
    source: hosted
    version: "2.0.5"
  expand_tap_area:
    dependency: transitive
    description:
      name: expand_tap_area
      sha256: b3e178b5036a3eaa7950e2d02a01c536f34e5c1eb953dc0b3c15f06b1c9f75ac
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      sha256: "5368f224a74523e8d2e7399ea1638b37aecfca824a3cc4dfdf77bf1fa905ac44"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.3"
  ffi:
    dependency: transitive
    description:
      name: ffi
      sha256: "7bf0adc28a23d395f19f3f1eb21dd7cfd1dd9f8e1c50051c069122e6853bc878"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  file:
    dependency: transitive
    description:
      name: file
      sha256: "1b92bec4fc2a72f59a8e15af5f52cd441e4a7860b49499d69dfa817af20e925d"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.4"
  firebase_analytics:
    dependency: transitive
    description:
      name: firebase_analytics
      sha256: dbf1e7ab22cfb1f4a4adb103b46a26276b4edc593d4a78ef6fb942bafc92e035
      url: "https://pub.dev"
    source: hosted
    version: "10.10.7"
  firebase_analytics_platform_interface:
    dependency: transitive
    description:
      name: firebase_analytics_platform_interface
      sha256: "3729b74f8cf1d974a27ba70332ecb55ff5ff560edc8164a6469f4a055b429c37"
      url: "https://pub.dev"
    source: hosted
    version: "3.10.8"
  firebase_analytics_web:
    dependency: transitive
    description:
      name: firebase_analytics_web
      sha256: "019cd7eee74254d33fbd2e29229367ce33063516bf6b3258a341d89e3b0f1655"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.7+7"
  firebase_core:
    dependency: transitive
    description:
      name: firebase_core
      sha256: "26de145bb9688a90962faec6f838247377b0b0d32cc0abecd9a4e43525fc856c"
      url: "https://pub.dev"
    source: hosted
    version: "2.32.0"
  firebase_core_platform_interface:
    dependency: transitive
    description:
      name: firebase_core_platform_interface
      sha256: "8bcfad6d7033f5ea951d15b867622a824b13812178bfec0c779b9d81de011bbb"
      url: "https://pub.dev"
    source: hosted
    version: "5.4.2"
  firebase_core_web:
    dependency: transitive
    description:
      name: firebase_core_web
      sha256: eb3afccfc452b2b2075acbe0c4b27de62dd596802b4e5e19869c1e926cbb20b3
      url: "https://pub.dev"
    source: hosted
    version: "2.24.0"
  firebase_crashlytics:
    dependency: transitive
    description:
      name: firebase_crashlytics
      sha256: "5125b7f3fcef2bfdd7e071afe7edcefd9597968003e44e073456c773d91694ee"
      url: "https://pub.dev"
    source: hosted
    version: "3.4.9"
  firebase_crashlytics_platform_interface:
    dependency: transitive
    description:
      name: firebase_crashlytics_platform_interface
      sha256: "359197344def001589c84f8d1d57c05f6e2e773f559205610ce58c25e2045a57"
      url: "https://pub.dev"
    source: hosted
    version: "3.6.16"
  firebase_messaging:
    dependency: transitive
    description:
      name: firebase_messaging
      sha256: "980259425fa5e2afc03e533f33723335731d21a56fd255611083bceebf4373a8"
      url: "https://pub.dev"
    source: hosted
    version: "14.7.10"
  firebase_messaging_platform_interface:
    dependency: transitive
    description:
      name: firebase_messaging_platform_interface
      sha256: "54e283a0e41d81d854636ad0dad73066adc53407a60a7c3189c9656e2f1b6107"
      url: "https://pub.dev"
    source: hosted
    version: "4.5.18"
  firebase_messaging_web:
    dependency: transitive
    description:
      name: firebase_messaging_web
      sha256: "90dc7ed885e90a24bb0e56d661d4d2b5f84429697fd2cbb9e5890a0ca370e6f4"
      url: "https://pub.dev"
    source: hosted
    version: "3.5.18"
  fl_chart:
    dependency: transitive
    description:
      name: fl_chart
      sha256: "00b74ae680df6b1135bdbea00a7d1fc072a9180b7c3f3702e4b19a9943f5ed7d"
      url: "https://pub.dev"
    source: hosted
    version: "0.66.2"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_bloc:
    dependency: transitive
    description:
      name: flutter_bloc
      sha256: "7b84d9777db3e30a5051c6718331be57e4cfc0c2424be82ac1771392cad7dbe8"
      url: "https://pub.dev"
    source: hosted
    version: "8.0.1"
  flutter_blue_plus:
    dependency: transitive
    description:
      name: flutter_blue_plus
      sha256: "6003c12eb254e948c8eb1356685663a72604c6878c23005c598e50b99896d732"
      url: "https://pub.dev"
    source: hosted
    version: "1.27.6"
  flutter_cache_manager:
    dependency: transitive
    description:
      name: flutter_cache_manager
      sha256: "8207f27539deb83732fdda03e259349046a39a4c767269285f449ade355d54ba"
      url: "https://pub.dev"
    source: hosted
    version: "3.3.1"
  flutter_contacts:
    dependency: transitive
    description:
      name: flutter_contacts
      sha256: "388d32cd33f16640ee169570128c933b45f3259bddbfae7a100bb49e5ffea9ae"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.9+2"
  flutter_exif_rotation:
    dependency: transitive
    description:
      name: flutter_exif_rotation
      sha256: "642841fa8dbedb0ac7b7f005dd24a606ce237ab2de0fedef7b3419d58b97f2a7"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.1"
  flutter_js:
    dependency: transitive
    description:
      name: flutter_js
      sha256: d19cde4bad2f7c301ff5f69d7b3452ff91f2ca89d153569dd03e544579d8610d
      url: "https://pub.dev"
    source: hosted
    version: "0.8.1"
  flutter_keyboard_visibility:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility
      sha256: "40d25e00e511fc7e0735d79002db28c2d4736773e5933c45bf239ad1fb80661c"
      url: "https://pub.dev"
    source: hosted
    version: "5.3.0"
  flutter_keyboard_visibility_platform_interface:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility_platform_interface
      sha256: e43a89845873f7be10cb3884345ceb9aebf00a659f479d1c8f4293fcb37022a4
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  flutter_keyboard_visibility_web:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility_web
      sha256: d3771a2e752880c79203f8d80658401d0c998e4183edca05a149f5098ce6e3d1
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  flutter_lints:
    dependency: "direct dev"
    description:
      name: flutter_lints
      sha256: b543301ad291598523947dc534aaddc5aaad597b709d2426d3a0e0d44c5cb493
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  flutter_markdown:
    dependency: transitive
    description:
      name: flutter_markdown
      sha256: "8a7c06488c33d0f9b1318d7509fae626a00d9e24b817be7fe6ec018251d8db3f"
      url: "https://pub.dev"
    source: hosted
    version: "0.6.10"
  flutter_native_timezone:
    dependency: transitive
    description:
      path: "."
      ref: a9669f7e84fb704dc85dc7c88f771f36d52bf318
      resolved-ref: a9669f7e84fb704dc85dc7c88f771f36d52bf318
      url: "**************:curefit/flutter_native_timezone.git"
    source: git
    version: "2.0.1"
  flutter_parsed_text:
    dependency: transitive
    description:
      name: flutter_parsed_text
      sha256: "529cf5793b7acdf16ee0f97b158d0d4ba0bf06e7121ef180abe1a5b59e32c1e2"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  flutter_platform_alert:
    dependency: transitive
    description:
      name: flutter_platform_alert
      sha256: "29a27c81660468bfb7746bc78205f79e07f18ca785e0aeaf70eac28d7a011edb"
      url: "https://pub.dev"
    source: hosted
    version: "0.6.1"
  flutter_plugin_android_lifecycle:
    dependency: transitive
    description:
      name: flutter_plugin_android_lifecycle
      sha256: "6ffe524cd6a7d49d99b2bf979a4f6ad82304c639cea4c8d3d0f8cf1aff24e74a"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.6"
  flutter_spinkit:
    dependency: transitive
    description:
      name: flutter_spinkit
      sha256: "77a2117c0517ff909221f3160b8eb20052ab5216107581168af574ac1f05dff8"
      url: "https://pub.dev"
    source: hosted
    version: "5.1.0"
  flutter_staggered_animations:
    dependency: transitive
    description:
      name: flutter_staggered_animations
      sha256: "32ea23c17d655d72f7cf94c87f847a79963cbfc34570456aa7ca44e59d9129d5"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  flutter_svg:
    dependency: transitive
    description:
      name: flutter_svg
      sha256: "6ff9fa12892ae074092de2fa6a9938fb21dbabfdaa2ff57dc697ff912fc8d4b2"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.6"
  flutter_test:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_tindercard:
    dependency: transitive
    description:
      path: "."
      ref: d070b02cef050abc1055dfd25873975acb234b4c
      resolved-ref: d070b02cef050abc1055dfd25873975acb234b4c
      url: "**************:curefit/flutter_tindercard.git"
    source: git
    version: "0.2.0"
  flutter_typeahead:
    dependency: transitive
    description:
      name: flutter_typeahead
      sha256: "612a9c08554d49de02106dd06a666702e02347fe31cff47d41cef2a6a88dd01f"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.7"
  flutter_web_plugins:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_widget_from_html_core:
    dependency: transitive
    description:
      name: flutter_widget_from_html_core
      sha256: "86d40a9f26d10011664df057c950e9c348ee1a7dbf141f295a07b0075ffd780b"
      url: "https://pub.dev"
    source: hosted
    version: "0.14.9"
  focus_detector_v2:
    dependency: transitive
    description:
      name: focus_detector_v2
      sha256: d4abc4c755ba894238ab92f42f6eee7ade78aa285199e112f45926c7053f90c6
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0+1"
  gap:
    dependency: transitive
    description:
      name: gap
      sha256: f19387d4e32f849394758b91377f9153a1b41d79513ef7668c088c77dbc6955d
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  geolocator:
    dependency: transitive
    description:
      name: geolocator
      sha256: d2ec66329cab29cb297d51d96c067d457ca519dca8589665fa0b82ebacb7dbe4
      url: "https://pub.dev"
    source: hosted
    version: "13.0.2"
  geolocator_android:
    dependency: transitive
    description:
      name: geolocator_android
      sha256: "7aefc530db47d90d0580b552df3242440a10fe60814496a979aa67aa98b1fd47"
      url: "https://pub.dev"
    source: hosted
    version: "4.6.1"
  geolocator_apple:
    dependency: transitive
    description:
      name: geolocator_apple
      sha256: "6154ea2682563f69fc0125762ed7e91e7ed85d0b9776595653be33918e064807"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.8+1"
  geolocator_platform_interface:
    dependency: transitive
    description:
      name: geolocator_platform_interface
      sha256: "386ce3d9cce47838355000070b1d0b13efb5bc430f8ecda7e9238c8409ace012"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.4"
  geolocator_web:
    dependency: transitive
    description:
      name: geolocator_web
      sha256: "2ed69328e05cd94e7eb48bb0535f5fc0c0c44d1c4fa1e9737267484d05c29b5e"
      url: "https://pub.dev"
    source: hosted
    version: "4.1.1"
  geolocator_windows:
    dependency: transitive
    description:
      name: geolocator_windows
      sha256: "53da08937d07c24b0d9952eb57a3b474e29aae2abf9dd717f7e1230995f13f0e"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.3"
  health:
    dependency: transitive
    description:
      path: "packages/health"
      ref: "336ea7b9e2c93726b9a84a5ffe010130c69e24d5"
      resolved-ref: "336ea7b9e2c93726b9a84a5ffe010130c69e24d5"
      url: "**************:curefit/Health.git"
    source: git
    version: "11.1.1"
  hive:
    dependency: transitive
    description:
      name: hive
      sha256: "8dcf6db979d7933da8217edcec84e9df1bdb4e4edc7fc77dbd5aa74356d6d941"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.3"
  html:
    dependency: transitive
    description:
      name: html
      sha256: bfef906cbd4e78ef49ae511d9074aebd1d2251482ef601a280973e8b58b51bbf
      url: "https://pub.dev"
    source: hosted
    version: "0.15.0"
  http:
    dependency: transitive
    description:
      name: http
      sha256: b9c29a161230ee03d3ccf545097fccd9b87a5264228c5d348202e0f0c28f9010
      url: "https://pub.dev"
    source: hosted
    version: "1.2.2"
  http_parser:
    dependency: transitive
    description:
      name: http_parser
      sha256: e362d639ba3bc07d5a71faebb98cde68c05bfbcfbbb444b60b6f60bb67719185
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0"
  hydrated_bloc:
    dependency: transitive
    description:
      name: hydrated_bloc
      sha256: "24994e61f64904d911683cce1a31dc4ef611619da5253f1de2b7b8fc6f79a118"
      url: "https://pub.dev"
    source: hosted
    version: "9.1.2"
  image_cropper:
    dependency: transitive
    description:
      name: image_cropper
      sha256: fe37d9a129411486e0d93089b61bd326d05b89e78ad4981de54b560725bf5bd5
      url: "https://pub.dev"
    source: hosted
    version: "8.0.2"
  image_cropper_for_web:
    dependency: transitive
    description:
      name: image_cropper_for_web
      sha256: "34256c8fb7fcb233251787c876bb37271744459b593a948a2db73caa323034d0"
      url: "https://pub.dev"
    source: hosted
    version: "6.0.2"
  image_cropper_platform_interface:
    dependency: transitive
    description:
      name: image_cropper_platform_interface
      sha256: e8e9d2ca36360387aee39295ce49029362ae4df3071f23e8e71f2b81e40b7531
      url: "https://pub.dev"
    source: hosted
    version: "7.0.0"
  image_picker:
    dependency: transitive
    description:
      name: image_picker
      sha256: f3712cd190227fb92e0960cb0ce22928ba042c7183b16864ade83b259adf8ea6
      url: "https://pub.dev"
    source: hosted
    version: "0.8.5+3"
  image_picker_android:
    dependency: transitive
    description:
      name: image_picker_android
      sha256: "08cfcbf6eeab76948fb45064bf080044531e068b4814946137e75cb42487d651"
      url: "https://pub.dev"
    source: hosted
    version: "0.8.5"
  image_picker_for_web:
    dependency: transitive
    description:
      name: image_picker_for_web
      sha256: "60f306ffbdcada4bc8b2691acc420258a1b758e102c87c4f94fb568d640f0e0e"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.8"
  image_picker_ios:
    dependency: transitive
    description:
      name: image_picker_ios
      sha256: c1af1bd2223362771e161a72c5361c762a7719f822583762b3ddd1971bcea081
      url: "https://pub.dev"
    source: hosted
    version: "0.8.5+5"
  image_picker_platform_interface:
    dependency: transitive
    description:
      name: image_picker_platform_interface
      sha256: "9ec26d410ff46f483c5519c29c02ef0e02e13a543f882b152d4bfd2f06802f80"
      url: "https://pub.dev"
    source: hosted
    version: "2.10.0"
  in_app_review:
    dependency: transitive
    description:
      name: in_app_review
      sha256: "99869244d09adc76af16bf8fd731dd13cef58ecafd5917847589c49f378cbb30"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.9"
  in_app_review_platform_interface:
    dependency: transitive
    description:
      name: in_app_review_platform_interface
      sha256: fed2c755f2125caa9ae10495a3c163aa7fab5af3585a9c62ef4a6920c5b45f10
      url: "https://pub.dev"
    source: hosted
    version: "2.0.5"
  infinite_listview:
    dependency: transitive
    description:
      name: infinite_listview
      sha256: f6062c1720eb59be553dfa6b89813d3e8dd2f054538445aaa5edaddfa5195ce6
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  intl:
    dependency: transitive
    description:
      name: intl
      sha256: "3bc132a9dbce73a7e4a21a17d06e1878839ffbf975568bc875c60537824b0c4d"
      url: "https://pub.dev"
    source: hosted
    version: "0.18.1"
  js:
    dependency: transitive
    description:
      name: js
      sha256: "5528c2f391ededb7775ec1daa69e65a2d61276f7552de2b5f7b8d34ee9fd4ab7"
      url: "https://pub.dev"
    source: hosted
    version: "0.6.5"
  json_annotation:
    dependency: transitive
    description:
      name: json_annotation
      sha256: "1ce844379ca14835a50d2f019a3099f419082cfdd231cd86a142af94dd5c6bb1"
      url: "https://pub.dev"
    source: hosted
    version: "4.9.0"
  leak_tracker:
    dependency: transitive
    description:
      name: leak_tracker
      sha256: "8dcda04c3fc16c14f48a7bb586d4be1f0d1572731b6d81d51772ef47c02081e0"
      url: "https://pub.dev"
    source: hosted
    version: "11.0.1"
  leak_tracker_flutter_testing:
    dependency: transitive
    description:
      name: leak_tracker_flutter_testing
      sha256: "1dbc140bb5a23c75ea9c4811222756104fbcd1a27173f0c34ca01e16bea473c1"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.10"
  leak_tracker_testing:
    dependency: transitive
    description:
      name: leak_tracker_testing
      sha256: "8d5a2d49f4a66b49744b23b018848400d23e54caf9463f4eb20df3eb8acb2eb1"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.2"
  lints:
    dependency: transitive
    description:
      name: lints
      sha256: a2c3d198cb5ea2e179926622d433331d8b58374ab8f29cdda6e863bd62fd369c
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  loader_overlay:
    dependency: transitive
    description:
      name: loader_overlay
      sha256: f3dd16e26cf705f64dd6e8ad36b1b6c15c1f58cb6ecf1f6edeb259f296eb4774
      url: "https://pub.dev"
    source: hosted
    version: "4.0.4+1"
  logging:
    dependency: transitive
    description:
      name: logging
      sha256: "623a88c9594aa774443aa3eb2d41807a48486b5613e67599fb4c41c0ad47c340"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  lottie:
    dependency: transitive
    description:
      path: "."
      ref: "v3.0.0-alpha.3"
      resolved-ref: c7066ca444ddcb34705362be0c50b02924629b0d
      url: "**************:xvrh/lottie-flutter.git"
    source: git
    version: "3.0.0-alpha.3"
  markdown:
    dependency: transitive
    description:
      name: markdown
      sha256: "39caf989ccc72c63e87b961851a74257141938599ed2db45fbd9403fee0db423"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.0"
  matcher:
    dependency: transitive
    description:
      name: matcher
      sha256: dc58c723c3c24bf8d3e2d3ad3f2f9d7bd9cf43ec6feaa64181775e60190153f2
      url: "https://pub.dev"
    source: hosted
    version: "0.12.17"
  material_color_utilities:
    dependency: transitive
    description:
      name: material_color_utilities
      sha256: f7142bb1154231d7ea5f96bc7bde4bda2a0945d2806bb11670e30b850d56bdec
      url: "https://pub.dev"
    source: hosted
    version: "0.11.1"
  math_expressions:
    dependency: transitive
    description:
      name: math_expressions
      sha256: db0b72d867491c4e53a1c773e2708d5d6e94bbe06be07080fc9f896766b9cd3d
      url: "https://pub.dev"
    source: hosted
    version: "2.5.0"
  meta:
    dependency: transitive
    description:
      name: meta
      sha256: e3641ec5d63ebf0d9b41bd43201a66e3fc79a65db5f61fc181f04cd27aab950c
      url: "https://pub.dev"
    source: hosted
    version: "1.16.0"
  mime:
    dependency: transitive
    description:
      name: mime
      sha256: dab22e92b41aa1255ea90ddc4bc2feaf35544fd0728e209638cad041a6e3928a
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  mime_type:
    dependency: transitive
    description:
      name: mime_type
      sha256: "2ad6e67d3d2de9ac0f8ef5352d998fd103cb21351ae8c02fb0c78b079b37d275"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  modal_bottom_sheet:
    dependency: transitive
    description:
      name: modal_bottom_sheet
      sha256: "3bba63c62d35c931bce7f8ae23a47f9a05836d8cb3c11122ada64e0b2f3d718f"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0-pre"
  navigation_history_observer:
    dependency: transitive
    description:
      name: navigation_history_observer
      sha256: "0e9bf4914eea89baa84f5882581e20541be73e1a14d0cbf1f279a695aff71cc3"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  nested:
    dependency: transitive
    description:
      name: nested
      sha256: "03bac4c528c64c95c722ec99280375a6f2fc708eec17c7b3f07253b626cd2a20"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  nhttp:
    dependency: transitive
    description:
      path: "."
      ref: "6bbaaa9ad8c53e28cd185f02dfb53b793e7c423c"
      resolved-ref: "6bbaaa9ad8c53e28cd185f02dfb53b793e7c423c"
      url: "**************:curefit/flutter-native-http.git"
    source: git
    version: "1.0.2"
  nm:
    dependency: transitive
    description:
      name: nm
      sha256: "2c9aae4127bdc8993206464fcc063611e0e36e72018696cd9631023a31b24254"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.0"
  no_screenshot:
    dependency: transitive
    description:
      name: no_screenshot
      sha256: ec3d86d7ee89a09c3a3939c1003012536ba4b3fcb4f8cbd23d87ada595c99258
      url: "https://pub.dev"
    source: hosted
    version: "0.3.1"
  numberpicker:
    dependency: transitive
    description:
      name: numberpicker
      sha256: "73723bd13c940ebcd9e5f0ed56b4874588c1748a9a6a38254f97ad627715142e"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  octo_image:
    dependency: transitive
    description:
      name: octo_image
      sha256: "45b40f99622f11901238e18d48f5f12ea36426d8eced9f4cbf58479c7aa2430d"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  outline_gradient_button:
    dependency: transitive
    description:
      name: outline_gradient_button
      sha256: "89acc90fcf2e46db680193eb4b64a64e9d37cb2499c12074f02bc358f5074cac"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0+1"
  package_info_plus:
    dependency: transitive
    description:
      path: "packages/package_info_plus/package_info_plus"
      ref: crashlytics-fix
      resolved-ref: a6bf23e4e564a2b4ad8a45d4e4a676feb2984ad1
      url: "**************:curefit/plus_plugins.git"
    source: git
    version: "5.0.1"
  package_info_plus_platform_interface:
    dependency: transitive
    description:
      name: package_info_plus_platform_interface
      sha256: "9bc8ba46813a4cc42c66ab781470711781940780fd8beddd0c3da62506d3a6c6"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  path:
    dependency: transitive
    description:
      name: path
      sha256: "75cca69d1490965be98c73ceaea117e8a04dd21217b37b292c9ddbec0d955bc5"
      url: "https://pub.dev"
    source: hosted
    version: "1.9.1"
  path_drawing:
    dependency: transitive
    description:
      name: path_drawing
      sha256: bbb1934c0cbb03091af082a6389ca2080345291ef07a5fa6d6e078ba8682f977
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  path_parsing:
    dependency: transitive
    description:
      name: path_parsing
      sha256: e3e67b1629e6f7e8100b367d3db6ba6af4b1f0bb80f64db18ef1fbabd2fa9ccf
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  path_provider:
    dependency: transitive
    description:
      name: path_provider
      sha256: "3087813781ab814e4157b172f1a11c46be20179fcc9bea043e0fba36bc0acaa2"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.15"
  path_provider_android:
    dependency: transitive
    description:
      name: path_provider_android
      sha256: dfaa152e93c3a6fec632482928c770b2156dfb873582e99fbd6ac3b3de651d4c
      url: "https://pub.dev"
    source: hosted
    version: "2.0.14"
  path_provider_foundation:
    dependency: transitive
    description:
      name: path_provider_foundation
      sha256: "916731ccbdce44d545414dd9961f26ba5fbaa74bcbb55237d8e65a623a8c7297"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.4"
  path_provider_linux:
    dependency: transitive
    description:
      name: path_provider_linux
      sha256: ab0987bf95bc591da42dffb38c77398fc43309f0b9b894dcc5d6f40c4b26c379
      url: "https://pub.dev"
    source: hosted
    version: "2.1.7"
  path_provider_platform_interface:
    dependency: transitive
    description:
      name: path_provider_platform_interface
      sha256: "27dc7a224fcd07444cb5e0e60423ccacea3e13cf00fc5282ac2c918132da931d"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.4"
  path_provider_windows:
    dependency: transitive
    description:
      name: path_provider_windows
      sha256: "1cb68ba4cd3a795033de62ba1b7b4564dace301f952de6bfb3cd91b202b6ee96"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.7"
  pedantic:
    dependency: transitive
    description:
      name: pedantic
      sha256: "67fc27ed9639506c856c840ccce7594d0bdcd91bc8d53d6e52359449a1d50602"
      url: "https://pub.dev"
    source: hosted
    version: "1.11.1"
  permission_handler:
    dependency: transitive
    description:
      name: permission_handler
      sha256: bc56bfe9d3f44c3c612d8d393bd9b174eb796d706759f9b495ac254e4294baa5
      url: "https://pub.dev"
    source: hosted
    version: "10.4.5"
  permission_handler_android:
    dependency: transitive
    description:
      name: permission_handler_android
      sha256: "59c6322171c29df93a22d150ad95f3aa19ed86542eaec409ab2691b8f35f9a47"
      url: "https://pub.dev"
    source: hosted
    version: "10.3.6"
  permission_handler_apple:
    dependency: transitive
    description:
      name: permission_handler_apple
      sha256: "99e220bce3f8877c78e4ace901082fb29fa1b4ebde529ad0932d8d664b34f3f5"
      url: "https://pub.dev"
    source: hosted
    version: "9.1.4"
  permission_handler_platform_interface:
    dependency: transitive
    description:
      name: permission_handler_platform_interface
      sha256: "6760eb5ef34589224771010805bea6054ad28453906936f843a8cc4d3a55c4a4"
      url: "https://pub.dev"
    source: hosted
    version: "3.12.0"
  permission_handler_windows:
    dependency: transitive
    description:
      name: permission_handler_windows
      sha256: cc074aace208760f1eee6aa4fae766b45d947df85bc831cde77009cdb4720098
      url: "https://pub.dev"
    source: hosted
    version: "0.1.3"
  petitparser:
    dependency: transitive
    description:
      name: petitparser
      sha256: "49392a45ced973e8d94a85fdb21293fbb40ba805fc49f2965101ae748a3683b4"
      url: "https://pub.dev"
    source: hosted
    version: "5.1.0"
  phone_number:
    dependency: transitive
    description:
      name: phone_number
      sha256: c66d3e2f2c69ce95f10bb923957049b1e5b8c6efea02d7366353fa3b80d4338a
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  photo_view:
    dependency: transitive
    description:
      name: photo_view
      sha256: "8036802a00bae2a78fc197af8a158e3e2f7b500561ed23b4c458107685e645bb"
      url: "https://pub.dev"
    source: hosted
    version: "0.14.0"
  pin_code_fields:
    dependency: transitive
    description:
      name: pin_code_fields
      sha256: "4c0db7fbc889e622e7c71ea54b9ee624bb70c7365b532abea0271b17ea75b729"
      url: "https://pub.dev"
    source: hosted
    version: "8.0.1"
  platform:
    dependency: transitive
    description:
      name: platform
      sha256: "4a451831508d7d6ca779f7ac6e212b4023dd5a7d08a27a63da33756410e32b76"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  plugin_platform_interface:
    dependency: transitive
    description:
      name: plugin_platform_interface
      sha256: "4820fbfdb9478b1ebae27888254d445073732dae3d6ea81f0b7e06d5dedc3f02"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.8"
  popover:
    dependency: transitive
    description:
      name: popover
      sha256: "586192140ed4413779a02e4e4e14ff1ad040346e15bdc5361a16178e9a01f933"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.6+3"
  process:
    dependency: transitive
    description:
      name: process
      sha256: "53fd8db9cec1d37b0574e12f07520d582019cb6c44abf5479a01505099a34a09"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.4"
  provider:
    dependency: transitive
    description:
      name: provider
      sha256: "8d7d4c2df46d6a6270a4e10404bfecb18a937e3e00f710c260d0a10415ce6b7b"
      url: "https://pub.dev"
    source: hosted
    version: "6.0.3"
  qr:
    dependency: transitive
    description:
      name: qr
      sha256: "5c4208b4dc0d55c3184d10d83ee0ded6212dc2b5e2ba17c5a0c0aab279128d21"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  qr_flutter:
    dependency: transitive
    description:
      name: qr_flutter
      sha256: c5c121c54cb6dd837b9b9d57eb7bc7ec6df4aee741032060c8833a678c80b87e
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0"
  queue:
    dependency: transitive
    description:
      name: queue
      sha256: "910cd076645aa42870f275e469d7fac629f8b2c10e461a71478f9091b69f30e4"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0+1"
  readmore:
    dependency: transitive
    description:
      name: readmore
      sha256: "99c2483202f7c7e98c50834d72be2b119aefecf4497ca1960ae9d5f418eb1481"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  rrule:
    dependency: transitive
    description:
      name: rrule
      sha256: "535fec11dfe8882c89f14bb0a99ae08fb04d8cf1771db1e9ebb9869b1d414dd1"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.14"
  rxdart:
    dependency: transitive
    description:
      name: rxdart
      sha256: "933db29250b286ecfe08a552f991f0e9f245f3f8ba1e5fb37f2f55d1f82888cb"
      url: "https://pub.dev"
    source: hosted
    version: "0.27.4"
  screen_protector:
    dependency: transitive
    description:
      name: screen_protector
      sha256: "541bdcd341de1e38026b5b94cc2a74cd95299d2c51150735165c4b445fa0209a"
      url: "https://pub.dev"
    source: hosted
    version: "1.4.2"
  screenshot:
    dependency: transitive
    description:
      name: screenshot
      sha256: "63817697a7835e6ce82add4228e15d233b74d42975c143ad8cfe07009fab866b"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  scroll_to_index:
    dependency: transitive
    description:
      name: scroll_to_index
      sha256: "5df1597b5bfa1703c02962a7478a187a8c9dedaeb871aedbf7874ee0d58a0bba"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  share_plus:
    dependency: transitive
    description:
      name: share_plus
      sha256: f582d5741930f3ad1bf0211d358eddc0508cc346e5b4b248bd1e569c995ebb7a
      url: "https://pub.dev"
    source: hosted
    version: "4.5.3"
  share_plus_linux:
    dependency: transitive
    description:
      name: share_plus_linux
      sha256: dc32bf9f1151b9864bb86a997c61a487967a08f2e0b4feaa9a10538712224da4
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  share_plus_macos:
    dependency: transitive
    description:
      name: share_plus_macos
      sha256: "44daa946f2845045ecd7abb3569b61cd9a55ae9cc4cbec9895b2067b270697ae"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  share_plus_platform_interface:
    dependency: transitive
    description:
      name: share_plus_platform_interface
      sha256: bdc228d4111f4b7ef2d949e5f13521adab03a386e4013c80ea4a9df79ef1d9d1
      url: "https://pub.dev"
    source: hosted
    version: "3.1.2"
  share_plus_web:
    dependency: transitive
    description:
      name: share_plus_web
      sha256: eaef05fa8548b372253e772837dd1fbe4ce3aca30ea330765c945d7d4f7c9935
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  share_plus_windows:
    dependency: transitive
    description:
      name: share_plus_windows
      sha256: "3a21515ae7d46988d42130cd53294849e280a5de6ace24bae6912a1bffd757d4"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  shared_preferences:
    dependency: transitive
    description:
      name: shared_preferences
      sha256: "76917b7d4b9526b2ba416808a7eb9fb2863c1a09cf63ec85f1453da240fa818a"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.15"
  shared_preferences_android:
    dependency: transitive
    description:
      name: shared_preferences_android
      sha256: "853801ce6ba7429ec4e923e37317f32a57c903de50b8c33ffcfbdb7e6f0dd39c"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.12"
  shared_preferences_ios:
    dependency: transitive
    description:
      name: shared_preferences_ios
      sha256: "585a14cefec7da8c9c2fb8cd283a3bb726b4155c0952afe6a0caaa7b2272de34"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  shared_preferences_linux:
    dependency: transitive
    description:
      name: shared_preferences_linux
      sha256: "28aefc1261746e7bad3d09799496054beb84e8c4ffcdfed7734e17b4ada459a5"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  shared_preferences_macos:
    dependency: transitive
    description:
      name: shared_preferences_macos
      sha256: fbb94bf296576f49be37a1496d5951796211a8db0aa22cc0d68c46440dad808c
      url: "https://pub.dev"
    source: hosted
    version: "2.0.4"
  shared_preferences_platform_interface:
    dependency: transitive
    description:
      name: shared_preferences_platform_interface
      sha256: "992f0fdc46d0a3c0ac2e5859f2de0e577bbe51f78a77ee8f357cbe626a2ad32d"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  shared_preferences_web:
    dependency: transitive
    description:
      name: shared_preferences_web
      sha256: a4b5bc37fe1b368bbc81f953197d55e12f49d0296e7e412dfe2d2d77d6929958
      url: "https://pub.dev"
    source: hosted
    version: "2.0.4"
  shared_preferences_windows:
    dependency: transitive
    description:
      name: shared_preferences_windows
      sha256: "97f7ab9a7da96d9cf19581f5de520ceb529548498bd6b5e0ccd02d68a0d15eba"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  shimmer:
    dependency: transitive
    description:
      name: shimmer
      sha256: "1f1009b5845a1f88f1c5630212279540486f97409e9fc3f63883e71070d107bf"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  sliding_up_panel:
    dependency: transitive
    description:
      name: sliding_up_panel
      sha256: "578e90956a6212d1e406373250b2436a0f3afece29aee3c24c8360094d6cf968"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0+1"
  source_span:
    dependency: transitive
    description:
      name: source_span
      sha256: "53e943d4206a5e30df338fd4c6e7a077e02254531b138a15aec3bd143c1a8b3c"
      url: "https://pub.dev"
    source: hosted
    version: "1.10.0"
  sprintf:
    dependency: transitive
    description:
      name: sprintf
      sha256: "1fc9ffe69d4df602376b52949af107d8f5703b77cda567c4d7d86a0693120f23"
      url: "https://pub.dev"
    source: hosted
    version: "7.0.0"
  sqflite:
    dependency: transitive
    description:
      name: sqflite
      sha256: "51c09d414ca74b1cd4a5880d63763ebd2033a4fc6d921708c7c1e98c603735d4"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.2+1"
  sqflite_common:
    dependency: transitive
    description:
      name: sqflite_common
      sha256: b504fc5b4576a05586a0bb99d9bcc0d37a78d9d5ed68b96c361d5d3a8e538275
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1+1"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      sha256: "8b27215b45d22309b5cddda1aa2b19bdfec9df0e765f2de506401c071d38d1b1"
      url: "https://pub.dev"
    source: hosted
    version: "1.12.1"
  store_redirect:
    dependency: transitive
    description:
      name: store_redirect
      sha256: "0f00455cc7e3ea896daac5db45fca3a09560eb7ba53a79533df3b80506047286"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      sha256: "969e04c80b8bcdf826f8f16579c7b14d780458bd97f56d107d3950fdbeef059d"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      sha256: "556692adab6cfa87322a115640c11f13cb77b3f076ddcc5d6ae3c20242bedcde"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  sync_http:
    dependency: transitive
    description:
      name: sync_http
      sha256: "7f0cd72eca000d2e026bcd6f990b81d0ca06022ef4e32fb257b30d3d1014a961"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.1"
  syncfusion_flutter_core:
    dependency: transitive
    description:
      name: syncfusion_flutter_core
      sha256: "85f96b7b06f32a60b19ab0bb8c32bf162a0474f2d94f4844384be1118e6b4954"
      url: "https://pub.dev"
    source: hosted
    version: "26.2.11"
  syncfusion_flutter_gauges:
    dependency: transitive
    description:
      name: syncfusion_flutter_gauges
      sha256: "587329c7043ebe526de2edef0814cd9f7ee662a74ce9e81566e90eb80c4c9168"
      url: "https://pub.dev"
    source: hosted
    version: "26.2.11"
  synchronized:
    dependency: transitive
    description:
      name: synchronized
      sha256: a7f0790927c0806ae0d5eb061c713748fa6070ef0037e391a2d53c3844c09dc2
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0+2"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      sha256: a29248a84fbb7c79282b40b8c72a1209db169a2e0542bce341da992fe1bc7e84
      url: "https://pub.dev"
    source: hosted
    version: "1.2.1"
  test_api:
    dependency: transitive
    description:
      name: test_api
      sha256: "522f00f556e73044315fa4585ec3270f1808a4b186c936e612cab0b565ff1e00"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.6"
  time:
    dependency: transitive
    description:
      name: time
      sha256: "83427e11d9072e038364a5e4da559e85869b227cf699a541be0da74f14140124"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.3"
  timezone:
    dependency: transitive
    description:
      name: timezone
      sha256: "1cfd8ddc2d1cfd836bc93e67b9be88c3adaeca6f40a00ca999104c30693cdca0"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.2"
  top_modal_sheet:
    dependency: transitive
    description:
      name: top_modal_sheet
      sha256: "404249c2cec151a5872197601bce2913ff88bb3b714ddae0223756308e7ccf08"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  twilio_conversations:
    dependency: transitive
    description:
      path: "."
      ref: "717b4dc2273b821dbdb241a745e8e6146c6ea7e3"
      resolved-ref: "717b4dc2273b821dbdb241a745e8e6146c6ea7e3"
      url: "**************:curefit/twilio-flutter-conversations.git"
    source: git
    version: "0.1.4"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      sha256: "53bdf7e979cfbf3e28987552fd72f637e63f3c8724c9e56d9246942dc2fa36ee"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0"
  url_launcher:
    dependency: transitive
    description:
      name: url_launcher
      sha256: f118f30f4301453d155c236a17d9ba53ec02a342591290aff91ae0fda4dc5428
      url: "https://pub.dev"
    source: hosted
    version: "6.1.3"
  url_launcher_android:
    dependency: transitive
    description:
      name: url_launcher_android
      sha256: "1ccd353c1bff66b49863527c02759f4d06b92744bd9777c96a00ca6a9e8e1d2f"
      url: "https://pub.dev"
    source: hosted
    version: "6.0.17"
  url_launcher_ios:
    dependency: transitive
    description:
      name: url_launcher_ios
      sha256: "6ba7dddee26c9fae27c9203c424631109d73c8fa26cfa7bc3e35e751cb87f62e"
      url: "https://pub.dev"
    source: hosted
    version: "6.0.17"
  url_launcher_linux:
    dependency: transitive
    description:
      name: url_launcher_linux
      sha256: "360fa359ab06bcb4f7c5cd3123a2a9a4d3364d4575d27c4b33468bd4497dd094"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  url_launcher_macos:
    dependency: transitive
    description:
      name: url_launcher_macos
      sha256: a9b3ea9043eabfaadfa3fb89de67a11210d85569086d22b3854484beab8b3978
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  url_launcher_platform_interface:
    dependency: transitive
    description:
      name: url_launcher_platform_interface
      sha256: "6c9ca697a5ae218ce56cece69d46128169a58aa8653c1b01d26fcd4aad8c4370"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  url_launcher_web:
    dependency: transitive
    description:
      name: url_launcher_web
      sha256: "81fe91b6c4f84f222d186a9d23c73157dc4c8e1c71489c4d08be1ad3b228f1aa"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.16"
  url_launcher_windows:
    dependency: transitive
    description:
      name: url_launcher_windows
      sha256: e3c3b16d3104260c10eea3b0e34272aaa57921f83148b0619f74c2eced9b7ef1
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  uuid:
    dependency: transitive
    description:
      name: uuid
      sha256: e03928880bdbcbf496fb415573f5ab7b1ea99b9b04f669c01104d085893c3134
      url: "https://pub.dev"
    source: hosted
    version: "4.0.0"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      sha256: d530bd74fea330e6e364cda7a85019c434070188383e1cd8d9777ee586914c5b
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  video_player_cf:
    dependency: transitive
    description:
      path: "packages/video_player/video_player"
      ref: "9656e3ceae07a707b21f9997aa2c72261aa24c26"
      resolved-ref: "9656e3ceae07a707b21f9997aa2c72261aa24c26"
      url: "**************:curefit/flutter-plugins.git"
    source: git
    version: "2.2.10"
  video_player_platform_interface_cf:
    dependency: transitive
    description:
      path: "packages/video_player/video_player_platform_interface"
      ref: e1830f5cb4fb3aee289d88f5c48d04fe6fac94b7
      resolved-ref: e1830f5cb4fb3aee289d88f5c48d04fe6fac94b7
      url: "**************:curefit/flutter-plugins.git"
    source: git
    version: "5.0.0"
  video_player_web_cf:
    dependency: transitive
    description:
      path: "packages/video_player/video_player_web"
      ref: f51bbd641cd36040e6000fcec269dd27fb152168
      resolved-ref: f51bbd641cd36040e6000fcec269dd27fb152168
      url: "**************:curefit/flutter-plugins.git"
    source: git
    version: "2.0.5"
  visibility_detector:
    dependency: transitive
    description:
      name: visibility_detector
      sha256: dd5cc11e13494f432d15939c3aa8ae76844c42b723398643ce9addb88a5ed420
      url: "https://pub.dev"
    source: hosted
    version: "0.4.0+2"
  vm_service:
    dependency: transitive
    description:
      name: vm_service
      sha256: "3923c89304b715fb1eb6423f017651664a03bf5f4b29983627c4da791f74a4ec"
      url: "https://pub.dev"
    source: hosted
    version: "14.2.1"
  wakelock:
    dependency: transitive
    description:
      path: wakelock
      ref: "423db35ebc536e2f61ffef063567232aef24f9da"
      resolved-ref: "423db35ebc536e2f61ffef063567232aef24f9da"
      url: "**************:curefit/wakelock.git"
    source: git
    version: "0.6.2"
  wakelock_platform_interface:
    dependency: transitive
    description:
      name: wakelock_platform_interface
      sha256: "1f4aeb81fb592b863da83d2d0f7b8196067451e4df91046c26b54a403f9de621"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.0"
  web:
    dependency: transitive
    description:
      name: web
      sha256: cd3543bd5798f6ad290ea73d210f423502e71900302dde696f8bff84bf89a1cb
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  webview_flutter:
    dependency: transitive
    description:
      name: webview_flutter
      sha256: "25e1b6e839e8cbfbd708abc6f85ed09d1727e24e08e08c6b8590d7c65c9a8932"
      url: "https://pub.dev"
    source: hosted
    version: "4.7.0"
  webview_flutter_android:
    dependency: transitive
    description:
      name: webview_flutter_android
      sha256: f038ee2fae73b509dde1bc9d2c5a50ca92054282de17631a9a3d515883740934
      url: "https://pub.dev"
    source: hosted
    version: "3.16.0"
  webview_flutter_platform_interface:
    dependency: transitive
    description:
      name: webview_flutter_platform_interface
      sha256: d937581d6e558908d7ae3dc1989c4f87b786891ab47bb9df7de548a151779d8d
      url: "https://pub.dev"
    source: hosted
    version: "2.10.0"
  webview_flutter_wkwebview:
    dependency: transitive
    description:
      name: webview_flutter_wkwebview
      sha256: f12f8d8a99784b863e8b85e4a9a5e3cf1839d6803d2c0c3e0533a8f3c5a992a7
      url: "https://pub.dev"
    source: hosted
    version: "3.13.0"
  win32:
    dependency: transitive
    description:
      name: win32
      sha256: "5a751eddf9db89b3e5f9d50c20ab8612296e4e8db69009788d6c8b060a84191c"
      url: "https://pub.dev"
    source: hosted
    version: "4.1.4"
  win32_registry:
    dependency: transitive
    description:
      name: win32_registry
      sha256: "1c52f994bdccb77103a6231ad4ea331a244dbcef5d1f37d8462f713143b0bfae"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  xdg_directories:
    dependency: transitive
    description:
      name: xdg_directories
      sha256: "060b6e1c891d956f72b5ac9463466c37cce3fa962a921532fc001e86fe93438e"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.0+1"
  xml:
    dependency: transitive
    description:
      name: xml
      sha256: "979ee37d622dec6365e2efa4d906c37470995871fe9ae080d967e192d88286b5"
      url: "https://pub.dev"
    source: hosted
    version: "6.2.2"
sdks:
  dart: ">=3.8.0-0 <4.0.0"
  flutter: ">=3.22.0"

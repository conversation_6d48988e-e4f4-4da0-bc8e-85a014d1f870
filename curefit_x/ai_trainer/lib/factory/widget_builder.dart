import 'package:aitrainer/UI/widgets/fp_footer_widget.dart';
import 'package:aitrainer/UI/widgets/fp_lose_weight_widget.dart';
import 'package:aitrainer/UI/widgets/fp_pt_widget.dart';
import 'package:aitrainer/UI/widgets/graph_widget.dart';
import 'package:aitrainer/UI/widgets/journey_widget.dart';
import 'package:aitrainer/UI/widgets/landing_header_widget.dart';
import 'package:aitrainer/UI/widgets/muscle_info_widget.dart';
import 'package:aitrainer/UI/widgets/overlay_textual_action_widget.dart';
import 'package:aitrainer/UI/widgets/testimonial_widget.dart';
import 'package:aitrainer/UI/widgets/workoutCardWidgetV2.dart';
import 'package:aitrainer/UI/widgets/workout_detail_overlay_widget.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:flutter/material.dart';

import '../UI/widgets/fp_overlay_autoplay_video.dart';
import '../UI/widgets/overlay_exercise_stopwatch_widget.dart';

class WidgetBuilder implements IWidgetBuilder {
  @override
  Widget? buildWidget(payload, {BuilderInfo? builderInfo}) {
    if (payload['widgetType'] == null) return null;
    WidgetTypes? widgetType =
        EnumToString.fromString(WidgetTypes.values, payload['widgetType']);

    if (widgetType != null) {
      WidgetInfo widgetInfo = payload['widgetMetric'] != null
          ? WidgetInfo.fromJson(payload, widgetType)
          : WidgetInfo(
              widgetType: widgetType,
              widgetMetric: WidgetMetric(widgetName: "", widgetId: ""));

      switch (widgetType) {
        case WidgetTypes.MUSCLE_INFO_WIDGET:
          return MuscleInfoWidget(
              widgetData: MuscleInfoWidgetData.fromJson(
                  payload, widgetInfo, widgetType));

        case WidgetTypes.FP_TESTIMONIAL_WIDGET:
          return TestimonialWidget(
              widgetData: TestimonialWidgetData.fromJson(
                  payload, widgetInfo, widgetType));

        case WidgetTypes.PLAN_INFO_HEADER:
          return LandingHeaderWidget(
              widgetData: LandingHeaderWidgetData.fromJson(
                  payload, widgetInfo, widgetType));

        case WidgetTypes.FP_GRAPH:
          return GraphWidget(
              widgetData:
                  GraphWidgetData.fromJson(payload, widgetInfo, widgetType));
        case WidgetTypes.FP_JOURNEY_WIDGET:
          return FPJourneyWidget(
              widgetData: FPJourneyWidgetData.fromJson(
                  payload, widgetInfo, widgetType));
        case WidgetTypes.FP_PT_WIDGET:
          return FPptWidget(
              widgetData:
                  FPptWidgetData.fromJson(payload, widgetInfo, widgetType));
        case WidgetTypes.FP_MUSCLE_GAIN_WIDGET:
          return FPLoseWeightWidget(
              widgetData: FPLoseWeightWidgetData.fromJson(
                  payload, widgetInfo, widgetType));
        case WidgetTypes.FP_FOOTER_WIDGET:
          return FPFooterWidget(
              widgetData:
                  FPFooterWidgetData.fromJson(payload, widgetInfo, widgetType));
        case WidgetTypes.FP_OVERLAY_AUTOPLAY_VIDEO:
          return FPVideoOverlayWidget(
              widgetData: FPVideoOverlayWidgetData.fromJson(
                  payload, widgetInfo, widgetType));
        case WidgetTypes.OVERLAY_EXERCISE_STOPWATCH_WIDGET:
          return FPExerciseStopwatchWidget(
              widgetData: FPExerciseStopwatchWidgetData.fromJson(
                  payload, widgetInfo, widgetType));
        case WidgetTypes.WORKOUT_DETAIL_OVERLAY_WIDGET:
          return FPWorkoutDetailWidget(
              widgetData: FPWorkoutDetailWidgetData.fromJson(
                  payload, widgetInfo, widgetType));
        case WidgetTypes.OVERLAY_TEXTUAL_ACTION_WIDGET:
          return FPTextualWidget(
              widgetData: FPTextualWidgetData.fromJson(
                  payload, widgetInfo, widgetType));

        case WidgetTypes.WORKOUT_CARD_WIDGET_V2:
          return WorkoutCardWidgetV2(
              widgetData: WorkoutCardWidgetV2Data.fromJson(
                  payload, widgetInfo, widgetType));
      }
    }
    return null;
  }
}

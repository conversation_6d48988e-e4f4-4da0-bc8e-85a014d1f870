import 'package:common/ui/organisms/page_header_detail.dart';
import 'package:common/ui/organisms/story_carousel.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:common/video/video_player_registry.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/src/repository_provider.dart';

class LandingHeaderWidgetData implements IWidgetData {
  @override
  WidgetInfo? widgetInfo;
  @override
  WidgetTypes widgetType;
  final String text;
  final String name;
  final String? image;
  List<TitleFooterData>? tags;

  LandingHeaderWidgetData({
    this.widgetInfo,
    required this.widgetType,
    required this.text,
    required this.name,
    this.image,
    this.tags,
  });
  factory LandingHeaderWidgetData.fromJson(
      dynamic widgetData, WidgetInfo widgetInfo, WidgetTypes widgetType) {
    return LandingHeaderWidgetData(
      widgetType: widgetType,
      widgetInfo: widgetInfo,
      text: widgetData['text'],
      name: widgetData['name'],
      image: widgetData['image'],
      tags: widgetData['tags']
          .where((e) => e != null)
          .map<TitleFooterData>((e) => TitleFooterData(text: e))
          .toList(),
    );
  }
}

class LandingHeaderWidget extends StatelessWidget {
  LandingHeaderWidgetData widgetData;
  LandingHeaderWidget({Key? key, required this.widgetData}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return PageHeaderDetail(
      widgetData: PageHeaderDetailData(
        title: widgetData.text,
        videoPlayerRegistry:
            RepositoryProvider.of<VideoPlayerRegistry>(context),
        items: [StoryCardItem(image: widgetData.image ?? "")],
        titleFooter: widgetData.tags,
        topText: widgetData.name,
        // progressBarPadding: Spacings.x9,
      ),
    );
  }
}

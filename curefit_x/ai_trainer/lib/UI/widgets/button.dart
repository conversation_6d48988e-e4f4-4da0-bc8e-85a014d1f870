import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/theme/Colors.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:flutter/material.dart';

enum TrainerButtonType { BIG, SMALL }

class TrainerButton extends StatelessWidget {
  final Function? onPressed;
  final Color textColor;
  final bool enabled;
  final IconData? iconData;
  final IconData? rightIconData;
  final bool? emptyCircle;
  final double? height;
  final double? iconSize;
  final TrainerButtonType buttonType;
  final double? borderRadius;
  final double verticalPadding;
  final double? rightIconSize;
  final String? leftIconUrl;
  final String? rightIconUrl;
  final List<String> text;
  const TrainerButton(this.onPressed,
      {Key? key,
      this.textColor = Colors.white,
      this.buttonType = TrainerButtonType.BIG,
      this.borderRadius,
      this.height,
      required this.text,
      this.iconData,
      this.rightIconData,
      this.iconSize = 20,
      this.enabled = true,
      this.emptyCircle = false,
      this.rightIconSize,
      this.leftIconUrl,
      this.rightIconUrl,
      this.verticalPadding = Spacings.x2})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return BlurView(
        blurType: BlurType.LOW,
        borderRadius:
            borderRadius ?? AuroraTheme.of(context).buttonCornerRadius,
        child: Material(
            color: Colors.transparent,
            child: InkWell(
                borderRadius: BorderRadius.circular(
                    AuroraTheme.of(context).buttonCornerRadius),
                onTap: enabled ? onPressed as void Function()? : null,
                child:
                    Container(height: height, child: button(context)))));
  }

  Widget button(BuildContext context) {
    TypescaleValues textStyle = buttonType == TrainerButtonType.BIG
        ? TypescaleValues.P2
        : TypescaleValues.P5;

    return Padding(
        padding: const EdgeInsets.only(
          left: Spacings.x4,
          right: Spacings.x4,
          top: Spacings.x3,
          bottom: Spacings.x3,
        ),
        child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            // mainAxisSize: MainAxisSize.min,
            children: [
              if (iconData != null || leftIconUrl != null)
                Container(
                    height: iconSize! * 2,
                    width: iconSize! * 2,
                    decoration: BoxDecoration(
                      color: ColorPalette.white10,
                      borderRadius: BorderRadius.circular(iconSize!),
                    ),
                    child: leftIconUrl != null
                        ? Image.network(getMediaUrl(leftIconUrl!))
                        : Icon(
                            iconData,
                            size: iconSize,
                            color: enabled
                                ? textColor
                                : textColor.withOpacity(0.2),
                          )),
              if (iconData != null || leftIconUrl != null)
                const Padding(padding: EdgeInsets.only(left: Spacings.x3)),
              if (text.isNotEmpty)
                Expanded(
                  flex: 1,
                  child: Align(
                      alignment: Alignment.centerLeft,
                      child: RichText(
                        text: TextSpan(
                          style: AuroraTheme.of(context).textStyle(
                            textStyle,
                            color: enabled
                                ? buttonType == TrainerButtonType.SMALL
                                    ? textColor.withOpacity(0.6)
                                    : textColor
                                : textColor.withOpacity(0.2),
                          ),
                          children: [
                            TextSpan(text: text[0]),
                            if (text.length > 1)
                              TextSpan(
                                  text: text[1],
                                  style: AuroraTheme.of(context).textStyle(
                                    TypescaleValues.P3,
                                    color: enabled
                                        ? textColor
                                        : textColor.withOpacity(0.2),
                                  )),
                            if (text.length > 2)
                              TextSpan(text: text[2]),
                            if (text.length > 3)
                              TextSpan(text: text[3]),
                          ],
                        ),
                      )),
                ),
              if ((rightIconData != null || rightIconUrl != null) &&
                  emptyCircle == false)
                Padding(
                    padding: const EdgeInsets.only(left: Spacings.x2),
                    child: rightIconUrl != null
                        ? Image.network(getMediaUrl(rightIconUrl!))
                        : Icon(
                            rightIconData,
                            size: rightIconSize ?? iconSize,
                            color: enabled
                                ? textColor
                                : textColor.withOpacity(0.2),
                          )),
              if (emptyCircle == true)
                Container(
                  height: rightIconSize ?? iconSize,
                  width: rightIconSize ?? iconSize,
                  decoration: BoxDecoration(
                    border: Border.all(width: 2, color: ColorPalette.white25),
                    borderRadius:
                        BorderRadius.circular(rightIconSize ?? iconSize!),
                  ),
                ),
            ]));
  }
}

import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/atoms/context_tags/live_interactive_tag.dart';
import 'package:common/ui/atoms/context_tags/premium_tag.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/organisms/story_carousel.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:common/util/color.dart';
import 'package:flutter/material.dart';
import 'package:common/action/action_handler.dart' as action_handler;

class FPLoseWeightWidgetData implements IWidgetData {
  @override
  WidgetInfo? widgetInfo;
  @override
  WidgetTypes widgetType;

  final String? image;
  final String? heading;
  String? subHeading;
  String? description;

  FPLoseWeightWidgetData(
      {this.widgetInfo,
      required this.widgetType,
      this.image,
      this.heading,
      this.subHeading,
      this.description});

  factory FPLoseWeightWidgetData.fromJson(
      dynamic widgetData, WidgetInfo widgetInfo, WidgetTypes widgetType) {
    return FPLoseWeightWidgetData(
        widgetType: widgetType,
        widgetInfo: widgetInfo,
        heading: widgetData['heading'],
        image: widgetData['image'],
        subHeading: widgetData['subHeading'],
        description: widgetData['description']);
  }
}

class FPLoseWeightWidget extends StatelessWidget {
  FPLoseWeightWidgetData widgetData;
  FPLoseWeightWidget({Key? key, required this.widgetData}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      // crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        CFNetworkImage(
          imageUrl: getImageUrl(context,
              imagePath: widgetData.image ?? "", width: 186),
          fit: BoxFit.cover,
          width: scale(context, 182),
          height: scale(context, 174),
          errorWidget: (context, url, error) => const Icon(Icons.error),
        ),
        SizedBox(height: Spacings.x4),
        Text(widgetData.heading ?? "",
            style: AuroraThemeData().textStyle(TypescaleValues.H1,
                color: Color(int.parse("0xffffffff")))),
        SizedBox(height: scale(context, 5)),
        Text(widgetData.subHeading ?? "",
            textAlign: TextAlign.center,
            style: AuroraThemeData().textStyle(TypescaleValues.P1,
                color: Color(int.parse("0xffffffff")))),
        SizedBox(height: scale(context, 10)),
        Padding(
            padding: const EdgeInsets.symmetric(horizontal: Spacings.x4),
            child: Text(widgetData.description ?? "",
                textAlign: TextAlign.center,
                style: AuroraThemeData().textStyle(TypescaleValues.P5,
                    color: Color(int.parse("0x99ffffff"))))),
      ],
    );
  }
}

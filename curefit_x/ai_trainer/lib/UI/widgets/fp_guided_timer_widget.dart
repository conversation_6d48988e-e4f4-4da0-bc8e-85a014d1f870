import 'dart:async';

import 'package:common/font/cf_icons.dart';
import 'package:common/ui/theme/Colors.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../blocs/guided_screen/guided_bloc.dart';
import '../../blocs/guided_screen/guided_event.dart';

class FPGuidedTimerWidget extends StatefulWidget {
  int startTime;
  bool isOverlayVisible;

  FPGuidedTimerWidget({
    Key? key,
    required this.startTime,
    required this.isOverlayVisible,
  }) : super(key: key);

  @override
  State<FPGuidedTimerWidget> createState() => _FPGuidedTimerWidgetState();
}

class _FPGuidedTimerWidgetState extends State<FPGuidedTimerWidget>
    with SingleTickerProviderStateMixin {
  String? displayTime;
  Timer? timer;

  @override
  void initState() {
    super.initState();

    if (widget.startTime > 0) {
      updateTime();
      timer = Timer.periodic(
          const Duration(seconds: 1),
          (timer) => {
                if (mounted)
                  {
                    setState(() {
                      updateTime();
                    })
                  }
              });
    }
  }

  @override
  void deactivate() {
    super.deactivate();
    timer?.cancel();
  }

  void updateTime() {
    var delta = DateTime.now().millisecondsSinceEpoch - widget.startTime;
    var duration = Duration(milliseconds: delta);
    if (duration.inHours > 0) {
      displayTime =
          "${duration.inHours.toString()}:${duration.inMinutes.remainder(60).toString().padLeft(2, '0')}:${(duration.inSeconds.remainder(60)).toString().padLeft(2, '0')}";
    } else {
      displayTime =
          "${duration.inMinutes.remainder(60).toString().padLeft(2, '0')}:${(duration.inSeconds.remainder(60)).toString().padLeft(2, '0')}";
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
        padding: EdgeInsets.only(
            left: scale(context, Spacings.x4),
            top: scaleHeight(context, Spacings.x9),
            right: scale(context, 24)),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            (widget.isOverlayVisible
                ? IconButton(
                    padding:
                        const EdgeInsets.only(right: Spacings.x3, top: Spacings.x2),
                    alignment: Alignment.topCenter,
                    color: Colors.red,
                    onPressed: () {
                      BlocProvider.of<GuidedPageBloc>(context)
                          .add(CloseGuidedPageEvent());
                    },
                    icon: const Icon(
                      CFIcons.chevron_left,
                      color: ColorPalette.white,
                      size: 17,
                      semanticLabel: "chevron_left",
                    ),
                  )
                : const SizedBox(
                    width: 4,
                  )),
            Padding(
              padding: const EdgeInsets.only(top: Spacings.x1),
              child: Row(
                children: [
                  if(displayTime != null && displayTime != "")
                    Container(
                      width: 7,
                      height: 7,
                      decoration: BoxDecoration(color: Colors.red,borderRadius: BorderRadius.circular(5)),
                    ),
                  const SizedBox(width: 6,),
                  Padding(
                    padding: const EdgeInsets.only(bottom: Spacings.x1),
                    child: Text(displayTime ?? "",
                        textAlign: TextAlign.center,
                        style: AuroraThemeData().textStyle(TypescaleValues.P4,
                            color: widget.isOverlayVisible
                                ? Colors.white
                                : Colors.black)
                    ),
                  ),
                ],
              ),
            ),
          ],
        ));
  }
}

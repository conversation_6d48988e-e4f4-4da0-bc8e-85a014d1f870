import 'dart:async';
import 'dart:ui';

import 'package:aitrainer/constants/constants.dart';
import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/constants/action_constants.dart';
import 'package:common/font/cf_icons.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/atoms/secondary_button.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/Colors.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:common/util/color.dart';
import 'package:flutter/material.dart';
import 'package:lottie/lottie.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../blocs/guided_screen/guided_bloc.dart';
import '../../blocs/guided_screen/guided_event.dart';
import 'package:common/action/action_handler.dart' as ActionHandler;
import '../../blocs/log_modal/log_modal_bloc.dart';
import 'package:enum_to_string/enum_to_string.dart';

class FPExerciseStopwatchWidgetData implements IWidgetData {
  @override
  WidgetInfo? widgetInfo;
  @override
  WidgetTypes widgetType;

  final String? restText;
  final int totalTime;
  final List<Color> linearGradient;
  final List<ActionHandler.Action>? restActions;
  final bool isSmallDevice;

  FPExerciseStopwatchWidgetData({
    this.widgetInfo,
    required this.widgetType,
    this.restText,
    required this.totalTime,
    required this.linearGradient,
    this.restActions,
    required this.isSmallDevice,
  });

  factory FPExerciseStopwatchWidgetData.fromJson(
      dynamic widgetData, WidgetInfo widgetInfo, WidgetTypes widgetType) {
    List<Color> lineargradientData = [];
    if (widgetData['linearGradientValue'] != null) {
      lineargradientData = (widgetData['linearGradientValue'] as List)
          .map((e) => HexColor.fromHex(e))
          .toList();
    }

    List<ActionHandler.Action>? actions;
    if (widgetData['restActions'] != null) {
      actions = [];
      var actionsInJson = widgetData['restActions'] as List;
      for (int i = 0; i < actionsInJson.length; i++) {
        actions.add(ActionHandler.Action.fromJson(actionsInJson[i]));
      }
    }

    return FPExerciseStopwatchWidgetData(
      widgetType: widgetType,
      widgetInfo: widgetInfo,
      restText: widgetData['restText'],
      totalTime: widgetData['totalTime'],
      linearGradient: lineargradientData,
      restActions: actions,
      isSmallDevice: widgetData['isSmallDevice'],
    );
  }
}

class FPExerciseStopwatchWidget extends StatefulWidget {
  FPExerciseStopwatchWidgetData widgetData;
  FPExerciseStopwatchWidget({Key? key, required this.widgetData})
      : super(key: key);

  @override
  State<FPExerciseStopwatchWidget> createState() =>
      _FPExerciseStopwatchWidgetState();
}

class _FPExerciseStopwatchWidgetState extends State<FPExerciseStopwatchWidget> {
  Timer? _timer;
  late int currentTime;
  bool isPaused = false;

  var devHeight = window.physicalSize.height / window.devicePixelRatio;


  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    currentTime = widget.widgetData.totalTime;
    startTimer();
    logPageView();
  }

  logPageView() {
    AnalyticsRepository analyticsRepository =
    RepositoryProvider.of<AnalyticsRepository>(context);
    analyticsRepository.logPageViewEvent(
        pageId: EnumToString.convertToString(RouteNames.guidedscreen),
        pageName: EnumToString.convertToString(RouteNames.guidedscreen),
        eventInfo: {
          "detailstring1": "REST"
        }
    );
  }

  void pauseTimer() {
    if (_timer != null) {
      _timer!.cancel();
      isPaused = true;
    }
  }

  void resumeTimer() {
    startTimer();
    isPaused = false;
  }

  void startTimer() {
    _timer = Timer.periodic(
        const Duration(seconds: 1),
        (t) => {
              renderIfMounted(() => {
                    if (currentTime > 0) {currentTime--} else {openNextScreen()}
                  })
            });
  }

  void renderIfMounted(Function() a) {
    if (mounted) {
      setState(() {
        a();
      });
    }
  }

  void changeState() {
    if (isPaused) {
      resumeTimer();
    } else {
      pauseTimer();
    }
  }

  void openNextScreen() {
    var bloc = BlocProvider.of<GuidedPageBloc>(context);
    bloc.add(NextSwipeGuidedPageEvent());
  }

  @override
  Widget build(BuildContext context) {
    return blocScaffold(LayoutBuilder(
        builder: (context, constraints)
    {
      return Stack(

          children: [
            Aurora(
              size: constraints.biggest,
              context: context,
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                getTopBackAndCloseIcons(),
                SizedBox(
                  height: scaleWithHeight(15, devHeight),
                ),
                Text(widget.widgetData.restText ?? "",
                    style: const TextStyle(fontSize: 30,fontFamily: "inter", fontWeight: FontWeight.w400, color: ColorPalette.white)
                ),
                SizedBox(
                  height: scaleWithHeight((widget.widgetData.isSmallDevice)? 10: 50, devHeight),
                ),
                InkWell(
                  onTap: () {
                    changeState();
                  },
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      Lottie.asset(
                        'assets/rest.json',
                        frameRate: FrameRate(50),
                        fit: BoxFit.fill,
                        height: scale(context, 300),
                        width: scale(context, 300),
                        alignment: Alignment.center,
                      ),
                      Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(currentTime.toString(),
                              style: AuroraThemeData().textStyle(
                                TypescaleValues.H5,
                                color: Colors.white,
                              )),
                          Text("Rest",
                              style: AuroraThemeData().textStyle(
                                TypescaleValues.H2,
                                color: Colors.white,
                              )),
                        ],
                      )
                    ],
                  ),
                ),
                if (widget.widgetData.restActions != null) getlogButton()
              ],
            )
          ]
      );
    }));

  }

  Widget blocScaffold(Widget child) {
    return Padding(
        padding: EdgeInsets.zero,
        child: Scaffold(
          resizeToAvoidBottomInset: false,
          extendBodyBehindAppBar: true,
          body: child,
        ));
  }

  onLogPressed(dynamic loggingExerciseIds) {
    ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
    final logModalBloc = BlocProvider.of<LogModalBloc>(context);
    ActionHandler.Action action = widget.widgetData.restActions![0];
    action.bloc = logModalBloc;
    ActionHandler.Action newAction =
        ActionHandler.Action.fromAction(action, {});
    actionBloc.add(PerformActionEvent(newAction));
  }

  getlogButton() {
    return Column(children: [
      InkWell(
        onTap: (){
          onLogPressed(widget.widgetData.restActions![0]?.meta!['loggingExerciseIds']);
        },
        child: Container(
            width: scaleWithHeight(50, devHeight),
            height: scaleWithHeight( 50, devHeight),
            decoration: BoxDecoration(
                color: ColorPalette.white10,
                borderRadius: BorderRadius.circular(scale(context, 35))),
            child: SizedBox(
              width: scaleWithHeight(42, devHeight),
              height: scaleWithHeight(42, devHeight),
              child: CFNetworkImage(
                imageUrl: getImageUrl(context,
                    width: 20,
                    imagePath: widget.widgetData.restActions![0]?.meta!['iconUrl'] ?? ""),
                fit: BoxFit.scaleDown,
                placeholder: (context, url) {return Container(color: Colors.transparent,);},
                errorWidget: (context, url, error) =>
                const Icon(Icons.error),
              ),
            )),
      ),
      SizedBox(
        height: scaleWithHeight(Spacings.x1, devHeight),
      ),
      Text(widget.widgetData.restActions![0]?.meta!['subTitle'] ?? "",
          style: AuroraThemeData().textStyle(TypescaleValues.P8,
              color: ColorPalette.white))
    ]);
  }

  getTopBackAndCloseIcons() {
    return Padding(
      padding: EdgeInsets.only(
          left: scale(context, Spacings.x4),
          top: scaleWithHeight(44, devHeight),
          right: scale(context, 24)),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          IconButton(
            padding: const EdgeInsets.only(right: Spacings.x3, top: Spacings.x2),
            alignment: Alignment.topCenter,
            onPressed: () {
              var blocproviderr = BlocProvider.of<GuidedPageBloc>(context);
              blocproviderr.add(CloseGuidedPageEvent());
            },
            icon: const Icon(
              CFIcons.chevron_left,
              color: ColorPalette.white,
              size: 17,
              semanticLabel: "chevron_left",
            ),
          ),
        ],
      ),
    );
  }
}

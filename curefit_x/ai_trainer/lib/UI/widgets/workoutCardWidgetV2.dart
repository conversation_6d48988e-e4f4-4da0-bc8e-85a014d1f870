import 'dart:ui';

import 'package:aitrainer/action/action_handler.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/constants/action_constants.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/molecules/bottom_tray.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/src/bloc_provider.dart';
import 'package:common/action/action_handler.dart' as action_handler;

import '../../blocs/replace_modal/replace_bloc.dart';
import '../../blocs/replace_modal/replace_model.dart';

class MediaData {
  final String? imageUri;
  final String? videoUri;
  final double? aspectRatio;
  final int? playIconFractionDivider;

  MediaData(
      {this.imageUri,
      this.videoUri,
      this.aspectRatio,
      this.playIconFractionDivider});
}

class WorkoutCardWidgetV2Data implements IWidgetData {
  @override
  WidgetInfo? widgetInfo;
  @override
  WidgetTypes widgetType;

  final int? index;
  final String? title;
  final List<dynamic>? sets;
  final String? tag;
  final String? tagColor;
  final String? imageUrl;
  final dynamic action;
  final dynamic media;
  final dynamic bloc;
  final String? cycleDay;
  final String? tenantId;
  final String? microCycleSequence;

  WorkoutCardWidgetV2Data(
      {this.widgetInfo,
      required this.widgetType,
      this.index,
      this.title,
      this.sets,
      this.tag,
      this.tagColor,
      this.imageUrl,
      this.action,
      this.media,
      this.bloc,
      this.cycleDay,
      this.tenantId,
      this.microCycleSequence});

  factory WorkoutCardWidgetV2Data.fromJson(
      dynamic widgetData, WidgetInfo widgetInfo, WidgetTypes widgetType) {
    return WorkoutCardWidgetV2Data(
        widgetType: widgetType,
        widgetInfo: widgetInfo,
        index: widgetData['index'],
        title: widgetData['title'].toString(),
        sets: widgetData['sets'],
        tag: widgetData['tag'].toString(),
        tagColor: widgetData['tagColor'].toString(),
        imageUrl: widgetData['imageUrl'].toString(),
        action: widgetData['action'],
        media: widgetData['media'],
        bloc: widgetData['bloc'],
        cycleDay: widgetData['cycleDay'],
        tenantId: widgetData['tenantId'],
        microCycleSequence: widgetData['microCycleSequence']);
  }
}

class WorkoutCardWidgetV2 extends StatelessWidget {
  final dynamic widgetData;
  const WorkoutCardWidgetV2({Key? key, required this.widgetData})
      : super(key: key);

  onTapAction(BuildContext context) {
    print("hey here");
    // final replaceModalBloc = BlocProvider.of<ReplaceModalBloc>(context);
    dynamic actionData = widgetData?.action;
    print(widgetData.bloc);
    ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);

    actionData['meta']['cycleDay'] = widgetData?.cycleDay;
    actionData['meta']['tenantId'] = widgetData?.tenantId;
    actionData['meta']['microCycleSequence'] = widgetData?.microCycleSequence;
    action_handler.Action action = action_handler.Action.fromJson(actionData);
    action.bloc = widgetData.bloc;

    action_handler.Action action1 =
        action_handler.Action.fromAction(action, {});

    actionBloc.add(PerformActionEvent(action1));
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    dynamic tagColor = Colors.white;
    if (widgetData?.tagColor == "#000000d9") {
      tagColor = Colors.white;
    } else if (widgetData?.tagColor == "#ffa300") {
      tagColor = Colors.yellow;
    } else if (widgetData?.tagColor == "#3888ff") {
      tagColor = Colors.blue;
    }
    return Padding(
      padding: const EdgeInsets.all(14),
      child: InkWell(
        onTap: () => onTapAction(context),
        child: Row(
          children: [
            SizedBox(
              height: scale(context, Spacings.x18),
              child: ClipRRect(
                borderRadius:
                    BorderRadius.circular(scale(context, Spacings.x1)),
                child: CFNetworkImage(
                  imageUrl: getImageUrl(
                    context,
                    imagePath: widgetData.media['imageUri'] ?? "",
                  ),
                  fit: BoxFit.cover,
                  width: scale(context, Spacings.x18),
                ),
              ),
            ),
            SizedBox(
              width: scale(context, Spacings.x3),
            ),
            // Image.network("https://www.google.com/images/branding/googlelogo/2x/googlelogo_light_color_272x92dp.png"),
            Flexible(
              child: Column(
                children: [
                  SizedBox(
                    height: scale(context, 4),
                  ),
                  Text(
                    widgetData?.tag ?? "",
                    style: TextStyle(fontSize: 11, color: tagColor),
                  ),
                  SizedBox(
                    height: scale(context, 5),
                  ),
                  Text(
                    widgetData?.title ?? "",
                    style: AuroraTheme.of(context)
                        .textStyle(TypescaleValues.P1, color: Colors.white),
                  ),
                ],
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
              ),
            )
          ],
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
        ),
      ),
    );
  }
}

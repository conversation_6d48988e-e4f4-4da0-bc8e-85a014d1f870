import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/atoms/context_tags/live_interactive_tag.dart';
import 'package:common/ui/atoms/context_tags/premium_tag.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/organisms/story_carousel.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:common/util/color.dart';
import 'package:flutter/material.dart';
import 'package:common/action/action_handler.dart' as action_handler;

class FPFooterWidgetData implements IWidgetData {
  @override
  WidgetInfo? widgetInfo;
  @override
  WidgetTypes widgetType;

  final String? image;
  final String? label;
  String? description;

  FPFooterWidgetData(
      {this.widgetInfo,
      required this.widgetType,
      this.image,
      this.label,
      this.description});

  factory FPFooterWidgetData.from<PERSON>son(
      dynamic widgetData, WidgetInfo widgetInfo, WidgetTypes widgetType) {
    return FPFooterWidgetData(
        widgetType: widgetType,
        widgetInfo: widgetInfo,
        label: widgetData['label'],
        image: widgetData['image'],
        description: widgetData['description']);
  }
}

class FPFooterWidget extends StatelessWidget {
  FPFooterWidgetData widgetData;
  FPFooterWidget({Key? key, required this.widgetData}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
        // color: Color(int.parse("0x26ffffff")),
        child: Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(height: Spacings.x10),
        CFNetworkImage(
          imageUrl: getImageUrl(context,
              imagePath: widgetData.image ?? "", width: 54),
          fit: BoxFit.cover,
          width: scale(context, 52),
          height: scale(context, 52),
          errorWidget: (context, url, error) => const Icon(Icons.error),
        ),
        SizedBox(height: Spacings.x2),
        Text(widgetData.label ?? "",
            style: AuroraThemeData().textStyle(TypescaleValues.H1,
                color: Color(int.parse("0xffffffff")))),
        SizedBox(height: Spacings.x2),
        Padding(
            padding: const EdgeInsets.symmetric(horizontal: Spacings.x4),
            child: Text(widgetData.description ?? "",
                textAlign: TextAlign.center,
                style: AuroraThemeData().textStyle(TypescaleValues.P3,
                    color: Color(int.parse("0x99ffffff"))))),
        SizedBox(height: Spacings.x8),
      ],
    ));
  }
}

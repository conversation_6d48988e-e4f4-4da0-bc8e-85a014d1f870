

import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/Colors.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class FocusPointWidget extends StatefulWidget {

  bool isOverlayHidden;
  Color? repColor;
  Color? unitColor;
  dynamic exerciseExecutionData;
  dynamic exerciseStopWatchData;
  bool isSmallDevice;
  dynamic loggedWeight;
  dynamic loggedWeightUnit;
  bool isLooped;
  bool isTimeBasedExercise;
  int currentTime;
  double opacityAnimationValue;
  Function? handleReplay;
  Function? handlePlay;
  List<String>? pointsOfPerformance;

  FocusPointWidget({
    Key? key,
    required this.isOverlayHidden,
    this.repColor,
    this.unitColor,
    this.exerciseExecutionData,
    this.exerciseStopWatchData,
    required this.isSmallDevice,
    this.loggedWeight,
    this.loggedWeightUnit,
    required this.isLooped,
    required this.isTimeBasedExercise,
    required this.currentTime,
    required this.opacityAnimationValue,
    this.handleReplay,
    this.handlePlay,
    this.pointsOfPerformance,
  }) : super(key: key);

  @override
  State<FocusPointWidget> createState() => _FocusPointWidgetState();
}

class _FocusPointWidgetState extends State<FocusPointWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController controller;
  late Animation<double> animation;
  int index = -2;
  bool completed = false;
  int pointsOfPerformanceLength = 0;

  @override
  void initState() {
    super.initState();
    if (widget.pointsOfPerformance != null) {
      pointsOfPerformanceLength = widget.pointsOfPerformance!.length;
    } else {
      pointsOfPerformanceLength = 0;
    }
    controller = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );


    animation = controller
      ..addStatusListener((status) {
        if (status == AnimationStatus.completed && (!completed || widget.isLooped)) {

          controller.reset();

          setState(() {
            if (mounted) {
              if(widget.isOverlayHidden) {
                index++;
              }
              controller.forward();
            }
          });
          if (index == pointsOfPerformanceLength) {
            index = -1;
            completed = true;
          }
        }
    });
    controller.forward();

  }

  changeIndexValue() {
    index = -2;
    setState(() {

    });
  }

  @override
  void deactivate() {
    super.deactivate();
    controller.stop();
  }

  @override
  void dispose() {
    controller.clearStatusListeners();
    super.dispose();
  }

  Widget RepsWidget() {

    return FractionallySizedBox(
      widthFactor: 0.5,
      child: Row(
            children: [
              Column(children: [
                Text(widget.exerciseExecutionData['count'].toString(),
                    style: AuroraTheme.of(context).textStyle(
                      (widget.isSmallDevice)? TypescaleValues.H7: TypescaleValues.H5 ,
                      color: widget.repColor,
                    )),
                Text(widget.exerciseExecutionData['exerciseExecutionUnit'],
                    style: AuroraTheme.of(context).textStyle(
                      TypescaleValues.P4,
                      color: widget.unitColor,
                    )),
              ]),
              if (widget.loggedWeightUnit != null)
                Column(
                  children:[
                    Text('×',
                      style: AuroraTheme.of(context).textStyle(
                        TypescaleValues.H8,
                        color: widget.repColor,
                      )),
                    const SizedBox(height: 20,),
                  ]
                ),
              if (widget.loggedWeightUnit != null)
                Column(children: [
                  Text(widget.loggedWeight.toString(),
                      style: AuroraTheme.of(context).textStyle(
                        TypescaleValues.H5,
                        color: widget.repColor,
                      )),
                  Text(widget.loggedWeightUnit,
                      style: AuroraTheme.of(context).textStyle(
                        TypescaleValues.P4,
                        color: widget.unitColor,
                      )),
                ]),
            ],
            mainAxisAlignment: MainAxisAlignment.spaceAround,
          ),
    );
  }

  formatTime(time, includePad){
    if(includePad) {
      return time.toString().padLeft(2, "0");
    } else {
      return time.toString();
    }
  }

  Widget getExerciseStopWatchWidget() {

    return Row(mainAxisAlignment: MainAxisAlignment.center, children: [
      Opacity(
        opacity: widget.opacityAnimationValue,
        child: InkWell(
          onTap: widget.isOverlayHidden
              ? null
              : () => {
            // restart
            widget.handleReplay!(widget.exerciseStopWatchData['totalTime'])
          },
          child: BlurView(
            blurType: BlurType.LOW,
            borderRadius: 25,
            child: Container(
              width: scale(context, 40),
              height: scale(context, 40),
              decoration: BoxDecoration(
                  color: ColorPalette.white.withOpacity(0.03),
                  borderRadius: BorderRadius.circular(scale(context, 20))),
              child: CFNetworkImage(
                imageUrl: getImageUrl(context,
                    imagePath: widget.exerciseStopWatchData['restartIcon'] ?? ""),
                fit: BoxFit.scaleDown,
                errorWidget: (context, url, error) => const Icon(Icons.error),
              ),
            ),
          ),
        ),
      ),
      SizedBox(width: scale(context, 40)),
      (widget.exerciseStopWatchData["hasMinsFormat"] != null && widget.exerciseStopWatchData["hasMinsFormat"] == true )?
      Column(children: [
        Row(
          children: [
            Text(
                (widget.currentTime!= null && widget.currentTime >= 0)? formatTime(widget.currentTime~/60, false) :0 ,
                style: AuroraTheme.of(context).textStyle(
                  (widget.isSmallDevice)? TypescaleValues.H7: TypescaleValues.H5 ,
                  color: widget.repColor,
                )),
            Text(
                ":",
                style: AuroraTheme.of(context).textStyle(
                  (widget.isSmallDevice)? TypescaleValues.H7: TypescaleValues.H5 ,
                  color: widget.repColor,
                )),
            Text(
                (widget.currentTime!= null && widget.currentTime >= 0)? formatTime((widget.currentTime%60).toInt(), true) :0 ,
                style: AuroraTheme.of(context).textStyle(
                  (widget.isSmallDevice)? TypescaleValues.H7: TypescaleValues.H5 ,
                  color: widget.repColor,
                )),
          ],
        ),
        Text(widget.exerciseStopWatchData['minsUnit'] ?? "",
            style: AuroraTheme.of(context).textStyle(
              TypescaleValues.P4,
              color: widget.unitColor,
            )),
      ])
          :Column(children: [
        Text(widget.currentTime.toString(),
            style: AuroraTheme.of(context).textStyle(
              (widget.isSmallDevice)? TypescaleValues.H7: TypescaleValues.H5 ,
              color: widget.repColor,
            )),
        Text(widget.exerciseStopWatchData['secsUnit'],
            style: AuroraTheme.of(context).textStyle(
              TypescaleValues.P4,
              color: widget.unitColor,
            )),
      ]),
      SizedBox(width: scale(context, 40)),
      Opacity(
        opacity: widget.opacityAnimationValue,
        child: InkWell(
          onTap: widget.isOverlayHidden
              ? null
              : () => {
            // restart
            widget.handlePlay!()
          },
          child: BlurView(
            blurType: BlurType.LOW,
            borderRadius: 25,
            child: Container(
              width: scale(context, 40),
              height: scale(context, 40),
              decoration: BoxDecoration(
                  color: ColorPalette.white.withOpacity(0.03),
                  borderRadius: BorderRadius.circular(scale(context, 20))),
              child: CFNetworkImage(
                imageUrl: getImageUrl(context,
                    imagePath: widget.exerciseStopWatchData['playIcon'] ?? ""),
                fit: BoxFit.scaleDown,
                errorWidget: (context, url, error) => const Icon(Icons.error),
              ),
            ),
          ),
        ),
      )
    ]);
  }

  @override
  Widget build(BuildContext context) {

    if(!widget.isOverlayHidden){
      changeIndexValue();
    }

    return Container(
      child:
      (index == -1 || index == -2)?
      widget.isTimeBasedExercise? getExerciseStopWatchWidget() : RepsWidget():
      FractionallySizedBox(
        alignment: Alignment.center,
        widthFactor: 0.85,
        child: Text(
            widget.pointsOfPerformance != null? widget.pointsOfPerformance![index] : "",
            textAlign: TextAlign.center,
            style: const TextStyle(fontSize: 30,fontFamily: "inter", fontWeight: FontWeight.w500, color: ColorPalette.black,)
        ),
      ),
    );
  }
}

import 'package:common/ui/video/video_screen.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:flutter/material.dart';

class FPVideoOverlayWidgetData implements IWidgetData {
  @override
  WidgetInfo? widgetInfo;
  @override
  WidgetTypes widgetType;

  final String? video;
  final bool shouldFlip;
  final int itemIndex;
  final int pageIndex;
  double? aspectRatio;


  FPVideoOverlayWidgetData({
    this.widgetInfo,
    required this.widgetType,
    this.video,
    required this.shouldFlip,
    required this.itemIndex,
    required this.pageIndex,
    this.aspectRatio,
  });

  factory FPVideoOverlayWidgetData.fromJson(
      dynamic widgetData, WidgetInfo widgetInfo, WidgetTypes widgetType) {
    return FPVideoOverlayWidgetData(
      widgetType: widgetType,
      widgetInfo: widgetInfo,
      video: widgetData['media']['videoUri'],
      aspectRatio: widgetData['media']['aspectRatio'],
      itemIndex: widgetData['itemIndex'],
      pageIndex: widgetData['pageIndex'],
      shouldFlip: widgetData['shouldFlipVideo'] ?? false,
    );
  }
}

class FPVideoOverlayWidget extends StatelessWidget {
  FPVideoOverlayWidgetData widgetData;
  FPVideoOverlayWidget({Key? key, required this.widgetData}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    var devWidth = MediaQuery.of(context).size.width;
    if (widgetData.video != null) {
      return Container(
          width: devWidth,
          height: devWidth / (widgetData.aspectRatio ?? 16 / 9),
          color: Colors.white,
          child: Transform.scale(
              scaleX: widgetData.shouldFlip ? -1 : 1,
              child: (widgetData.itemIndex == widgetData.pageIndex)? VideoScreen(
                heroTag: "32897",
                videoUrl: widgetData.video!,
                isMuted: true,
                looping: true,
              ): Container()
          ));
    }
    return Container();
  }
}

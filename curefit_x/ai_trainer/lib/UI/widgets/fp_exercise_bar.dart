import 'dart:ui';

import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/util/color.dart';
import 'package:flutter/material.dart';

class FpExerciseBarWidget extends StatefulWidget {
  bool isOverlayVisible;
  int itemIndex;
  List<int> sectionLastIndexList;

  FpExerciseBarWidget({
    Key? key,
    required this.itemIndex,
    required this.sectionLastIndexList,
    required this.isOverlayVisible,
  }) : super(key: key);

  @override
  State<FpExerciseBarWidget> createState() => _FpExerciseBarWidgetState();
}

enum ColorState { filled, empty }

class Bar {
  ColorState color;
  double width;
  Bar({required this.color, required this.width});

  @override
  String toString() {
    return "Bar color-" + color.toString() + " width-" + width.toString();
  }
}

class CurrentBar extends Bar {
  ColorState color1;
  double width1;
  CurrentBar({
    required color,
    required width,
    required this.color1,
    required this.width1,
  }) : super(color: color, width: width);

  @override
  String toString() {
    return "Current Bar color-" +
        color.toString() +
        " width-" +
        width.toString() +
        " color1-" +
        color1.toString() +
        " width1-" +
        width1.toString();
  }
}

class _FpExerciseBarWidgetState extends State<FpExerciseBarWidget> {
  List<Bar> barList = [];

  @override
  void initState() {
    super.initState();

    var devWidth = window.physicalSize.width / window.devicePixelRatio;
    int sectionCount = widget.sectionLastIndexList.length;
    num padding = (scaleWithWidth(Spacings.x4, devWidth) * 2);

    var availableWidth =
        devWidth - padding - (scaleWithWidth(Spacings.x1, devWidth) * (sectionCount - 1));
    int start = 0;
    int selectedItemIndex = widget.itemIndex;
    for (var i = 0; i < sectionCount; i++) {
      int end = widget.sectionLastIndexList[i];
      double barWidth = ((end - start + 1) /
              (widget.sectionLastIndexList[sectionCount - 1] + 1)) *
          availableWidth;
      if (selectedItemIndex < start) {
        //  empty
        barList.add(Bar(color: ColorState.empty, width: barWidth));
      } else if (selectedItemIndex <= end) {
        // half
        var w =
            ((selectedItemIndex - start + 1) / (end - start + 1) * barWidth);
        barList.add(CurrentBar(
            color: ColorState.filled,
            width: w,
            color1: ColorState.empty,
            width1: barWidth - w));
      } else {
        // filled
        barList.add(Bar(color: ColorState.filled, width: barWidth));
      }
      start = end + 1;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
        padding: EdgeInsets.only(
          left: scale(context, Spacings.x4),
          top: scale(context, Spacings.x10),
          right: scale(context, Spacings.x4),
        ),
        child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: barList
                .map<Widget>(
                    (e) => (e is CurrentBar) ? getCurrentBar(e) : getBar(e))
                .toList()));
  }

  Container getBar(Bar e) {
    return Container(
      width: e.width,
      height: 2,
      decoration: BoxDecoration(
        color: getColor(e.color),
        borderRadius: BorderRadius.circular(2),
      ),
    );
  }

  Row getCurrentBar(CurrentBar e) {
    return Row(
      children: [
        Container(
          width: e.width,
          height: 2,
          decoration: BoxDecoration(
            color: getColor(e.color),
            borderRadius: const BorderRadius.horizontal(
              left: Radius.circular(2),
              right: Radius.circular(0),
            ),
          ),
        ),
        Container(
          width: e.width1,
          height: 2,
          decoration: BoxDecoration(
            color: getColor(e.color1),
            borderRadius: const BorderRadius.horizontal(
              left: Radius.circular(0),
              right: Radius.circular(2),
            ),
          ),
        )
      ],
    );
  }

  getColor(ColorState color) {
    if (color == ColorState.filled) {
      return HexColor.fromHex(widget.isOverlayVisible ? "#EFF1F6" : "#000000");
    }
    return HexColor.fromHex(
        widget.isOverlayVisible ? "#4CEFF1F6" : "#32000000");
  }
}

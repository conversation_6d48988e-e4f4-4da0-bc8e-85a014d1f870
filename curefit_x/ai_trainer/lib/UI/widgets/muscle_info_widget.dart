import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:flutter/material.dart';

class MuscleFocus {
  final String? image;
  final String? text;

  MuscleFocus(this.image, this.text);

  factory MuscleFocus.fromJson(payload) {
    return MuscleFocus(payload['image'], payload['text']);
  }
}

class MuscleInfoWidgetData implements IWidgetData {
  @override
  WidgetInfo? widgetInfo;
  @override
  WidgetTypes widgetType;

  final String? title;
  String? description;
  List<MuscleFocus>? dayWiseSplit;

  MuscleInfoWidgetData(
      {this.widgetInfo,
      required this.widgetType,
      this.title,
      this.description,
      this.dayWiseSplit});

  factory MuscleInfoWidgetData.from<PERSON>son(
      dynamic widgetData, WidgetInfo widgetInfo, WidgetTypes widgetType) {
    return MuscleInfoWidgetData(
        widgetType: widgetType,
        widgetInfo: widgetInfo,
        title: widgetData['title'],
        description: widgetData['description'],
        dayWiseSplit: widgetData['dayWiseSplit']
            .map<MuscleFocus>((e) => MuscleFocus.fromJson(e))
            .toList());
  }
}

class MuscleInfoWidget extends StatelessWidget {
  MuscleInfoWidgetData widgetData;
  MuscleInfoWidget({Key? key, required this.widgetData}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      // color: Color(int.parse("0x26ffffff")),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const SizedBox(height: Spacings.x4),
          Text(widgetData.title ?? "",
              style: AuroraThemeData().textStyle(TypescaleValues.H1,
                  color: Color(int.parse("0xffffffff")))),
          const SizedBox(height: Spacings.x4),
          SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Container(
                padding: const EdgeInsets.fromLTRB(Spacings.x2, 0, 0, 0),
                child: Row(
                    children: (widgetData.dayWiseSplit
                            ?.map<Widget>(
                                (e) => getMuscleFocusWidget(e, context))
                            .toList()) ??
                        []),
              )),
          const SizedBox(height: Spacings.x8),
          Padding(
              padding: const EdgeInsets.symmetric(horizontal: Spacings.x4),
              child: Text(widgetData.description ?? "",
                  textAlign: TextAlign.center,
                  style: AuroraThemeData().textStyle(TypescaleValues.P8,
                      color: Color(int.parse("0x99ffffff"))))),
        ],
      ),
    );
  }

  getMuscleFocusWidget(MuscleFocus muscleFocus, BuildContext context) {
    return Container(
        padding: const EdgeInsets.fromLTRB(0, 0, Spacings.x2, 0),
        child: Column(
          children: [
            CFNetworkImage(
              imageUrl: getImageUrl(context,
                  imagePath: muscleFocus.image ?? "", width: 136),
              fit: BoxFit.cover,
              width: scale(context, 133),
              height: scale(context, 245),
              errorWidget: (context, url, error) => const Icon(Icons.error),
            ),
            const SizedBox(height: Spacings.x2),
            Text(muscleFocus.text ?? "",
                style: AuroraThemeData().textStyle(TypescaleValues.P3,
                    color: Color(int.parse("0xffffffff")))),
          ],
        ));
  }
}

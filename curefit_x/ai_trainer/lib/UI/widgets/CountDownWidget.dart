import 'dart:ui';

import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:flutter/material.dart';
import 'package:video_player_cf/video_player.dart';

class CountDownWidget extends StatefulWidget {
  Function onCountDownFinish;
  final List<String>? POPData;

  CountDownWidget({Key? key, required this.onCountDownFinish, this.POPData})
      : super(key: key);

  @override
  State<CountDownWidget> createState() => _CountDownWidgetState();
}

class _CountDownWidgetState extends State<CountDownWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController controller;
  late Animation<double> animation;
  late VideoPlayerController _videoPlayerController;
  late Animation<double> scale;
  late List<String> countDownText;
  int POPDataLength = 0;

  int index = 0;


  @override
  void initState() {
    super.initState();


    List<String> countDownArray= [];

    if (widget.POPData != null) {
      POPDataLength = widget.POPData!.length;
    }
    if(POPDataLength > 3) POPDataLength = 3;
    if(widget.POPData != null){
      for(int i=0 ;i<POPDataLength; i++){
        countDownArray.add(widget.POPData![i]);
        countDownArray.add(widget.POPData![i]);
      }
    }
    countDownArray.addAll(["3","2","1"]);
    countDownText = countDownArray;
    setState((){});

    controller = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _videoPlayerController = VideoPlayerController(
        videoPlayerOptions: VideoPlayerOptions(mixWithOthers: true));
    _videoPlayerController.setAssetDataSource('assets/beeps.mp3');

    animation = controller
      ..addStatusListener((status) {
        if (status == AnimationStatus.completed) {
          controller.reset();
          setState(() {
            if (mounted) {
              if ((POPDataLength> 0 && index == (POPDataLength*2)-1) || (POPDataLength == 0 && index == 0)) {
                _videoPlayerController.play();
              }
              index++;
              controller.forward();
            }
          });
          if (index == countDownText.length-1) {
            closeAnimation();
          }
        }
      });
    controller.forward();
  }

  @override
  void deactivate() {
    super.deactivate();
    controller.stop();
    _videoPlayerController.pause();
  }

  @override
  void dispose() {
    controller.clearStatusListeners();
    _videoPlayerController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Text(countDownText[index],
        textAlign: TextAlign.center,
        style: const TextStyle(fontSize: 30,fontFamily: "inter", fontWeight: FontWeight.w500, color: Colors.black,)
    );
  }

  void closeAnimation() {
    Future.delayed(const Duration(milliseconds: 500), () {
      controller.stop();
      _videoPlayerController?.pause();
      if (mounted) {
        widget.onCountDownFinish();
      }
    });
  }
}

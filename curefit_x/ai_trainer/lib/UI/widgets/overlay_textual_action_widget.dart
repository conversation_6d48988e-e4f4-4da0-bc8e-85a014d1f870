import 'dart:ui';

import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/Colors.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import '../../blocs/guided_screen/guided_bloc.dart';
import '../../blocs/guided_screen/guided_event.dart';

class FPTextualWidgetData implements IWidgetData {
  @override
  WidgetInfo? widgetInfo;
  @override
  WidgetTypes widgetType;

  final String? prefix;
  final String? suffix;
  final String? title;
  final String? icon;
  final String? rightIcon;
  final bool isSmallDevice;

  FPTextualWidgetData({
    this.widgetInfo,
    required this.widgetType,
    this.prefix,
    this.title,
    this.icon,
    this.suffix,
    this.rightIcon,
    required this.isSmallDevice,
  });

  factory FPTextualWidgetData.fromJson(
      dynamic widgetData, WidgetInfo widgetInfo, WidgetTypes widgetType) {
    return FPTextualWidgetData(
      widgetType: widgetType,
      widgetInfo: widgetInfo,
      prefix: widgetData['prefix'],
      suffix: widgetData['suffix'],
      icon: widgetData['icon'],
      title: widgetData['title'].toString(),
      rightIcon: widgetData['rightIcon'],
      isSmallDevice: widgetData['isSmallDevice'],
    );
  }
}

class FPTextualWidget extends StatefulWidget {
  FPTextualWidgetData widgetData;
  FPTextualWidget({Key? key, required this.widgetData}) : super(key: key);

  @override
  State<FPTextualWidget> createState() => _FPTextualWidgetState();
}

class _FPTextualWidgetState extends State<FPTextualWidget> {
  @override
  Widget build(BuildContext context) {
    return Column(mainAxisAlignment: MainAxisAlignment.end,
        children: [getNextExerciseWidget(widget.widgetData)]);
  }

  var devHeight = window.physicalSize.height / window.devicePixelRatio;

  // (widget.widgetData.isSmallDevice)? Spacings.x5 : Spacings.x9;

  getNextExerciseWidget(textualAction) {
    return Container(
      padding: EdgeInsets.only(
          left: scale(context, Spacings.x4),
          right: scale(context, Spacings.x4),
          bottom: scale(context, (widget.widgetData.isSmallDevice)? 18 : 42)),
      child: InkWell(
        onTap: () {
          openNextScreen();
        },
        child: Container(
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(scale(context, 10)),
              color: ColorPalette.white10
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Padding(
                padding: const EdgeInsets.all(Spacings.x2),
                child: ClipRRect(
                    borderRadius: BorderRadius.circular(scale(context, 5)),
                    child: CFNetworkImage(
                      imageUrl: getImageUrl(
                        context,
                        imagePath: textualAction.icon ?? "",
                        width: scale(context, 61).toInt()
                      ),
                      fit: BoxFit.fill,
                      width: scale(context, 61),
                      height: scale(context, 61),
                    )),
              ),
              const SizedBox(width: Spacings.x1),
              Expanded(
                  child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  Text(textualAction.prefix ?? "",
                      style: AuroraTheme.of(context).textStyle(
                        TypescaleValues.P5,
                        color: Colors.white38,
                      )),
                  Text(textualAction.title ?? "",
                      maxLines: 2,
                      softWrap: true,
                      overflow: TextOverflow.ellipsis,
                      style: AuroraTheme.of(context).textStyle(
                        TypescaleValues.P1,
                        color: Colors.white,
                      )),
                  // Text(
                  //     textualAction.suffix == null
                  //         ? ""
                  //         : textualAction.suffix.toString(),
                  //     style: AuroraTheme.of(context).textStyle(
                  //       TypescaleValues.P8,
                  //       color: Colors.white60,
                  //     )),
                ],
              )),
              const SizedBox(width: 20,),
              SizedBox(
                width: 35,
                height: 35,
                child: CFNetworkImage(
                  imageUrl: getImageUrl(context,
                      width: 14,
                      imagePath:textualAction.rightIcon.toString() ?? ""),
                  fit: BoxFit.scaleDown,
                  errorWidget: (context, url, error) =>
                  const Icon(Icons.error),
                ),
              ),
              const SizedBox(width: 20,)
            ],
          ),
        ),
      ),
    );
  }

  void openNextScreen() {
    var bloc = BlocProvider.of<GuidedPageBloc>(context);
    bloc.add(NextSwipeGuidedPageEvent());
  }
}

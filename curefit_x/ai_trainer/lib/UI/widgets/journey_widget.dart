import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:flutter/material.dart';

class Steps {
  final String? header;
  final String? description;

  Steps(this.header, this.description);

  factory Steps.fromJson(payload) {
    return Steps(payload['header'], payload['description']);
  }
}

class FPJourneyWidgetData implements IWidgetData {
  @override
  WidgetInfo? widgetInfo;
  @override
  WidgetTypes widgetType;

  final String? image;
  final String? title;
  List<Steps>? stepsList;

  FPJourneyWidgetData(
      {this.widgetInfo,
      required this.widgetType,
      this.image,
      this.title,
      this.stepsList});

  factory FPJourneyWidgetData.from<PERSON>son(
      dynamic widgetData, WidgetInfo widgetInfo, WidgetTypes widgetType) {
    return FPJourneyWidgetData(
        widgetType: widgetType,
        widgetInfo: widgetInfo,
        image: widgetData['image'],
        title: widgetData['title'],
        stepsList: widgetData['weeklySummary']
            .map<Steps>((e) => Steps.fromJson(e))
            .toList());
  }
}

class FPJourneyWidget extends StatelessWidget {
  FPJourneyWidgetData widgetData;
  FPJourneyWidget({Key? key, required this.widgetData}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
        // color: Color(int.parse("0x26ffffff")),
        child: Column(
      children: [
        SizedBox(
          height: Spacings.x16,
        ),
        CFNetworkImage(
          imageUrl: getImageUrl(context,
              imagePath: widgetData.image ?? "", width: 45),
          fit: BoxFit.cover,
          width: scale(context, 42),
          height: scale(context, 42),
          errorWidget: (context, url, error) => const Icon(Icons.error),
        ),
        SizedBox(
          height: Spacings.x6,
        ),
        Text(widgetData.title ?? "",
            style: AuroraThemeData().textStyle(TypescaleValues.H1,
                color: Color(int.parse("0xffffffff")))),
        SizedBox(
          height: Spacings.x7,
        ),
        ...(widgetData.stepsList
                ?.map((e) => getLevelWidget(context, e))
                .toList()) ??
            [],
        SizedBox(
          height: Spacings.x4,
        ),
      ],
    ));
    // getLevelWidget(context);
  }

  Widget getLevelWidget(BuildContext context, Steps steps) {
    var stepsList = widgetData.stepsList;
    return IntrinsicHeight(
        child: Padding(
            padding: const EdgeInsets.symmetric(
                vertical: 0, horizontal: Spacings.x10),
            child: Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
              Column(
                children: [
                  const SizedBox(height: Spacings.x2),
                  Image.asset(
                    'assets/bullet.png',
                    width: scale(context, 20),
                    height: scale(context, 20),
                  ),
                  const SizedBox(
                    height: Spacings.x1,
                  ),
                  if (stepsList != null &&
                      steps != stepsList[stepsList.length - 1])
                    Expanded(
                        child: Container(
                      width: scale(context, 1),
                      height: scale(context, 40),
                      color: Color(int.parse("0x99ffffff")),
                      child: null,
                    ))
                ],
              ),
              Padding(
                  padding: const EdgeInsets.fromLTRB(Spacings.x3, 0, 0, 0),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(steps.header ?? "",
                          style: AuroraThemeData().textStyle(TypescaleValues.P4,
                              color: Color(int.parse("0xffffffff")))),
                      Text(steps.description ?? "",
                          style: AuroraThemeData().textStyle(TypescaleValues.P5,
                              color: Color(int.parse("0x99ffffff")))),
                    ],
                  ))
            ])));
  }
}

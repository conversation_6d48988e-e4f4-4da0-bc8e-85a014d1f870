import 'dart:ffi';

import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:common/util/color.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';

class GraphWidgetData implements IWidgetData {
  @override
  WidgetInfo? widgetInfo;
  @override
  WidgetTypes widgetType;

  final String heading;
  double currentWeight;
  double targetWeight;
  String subHeading;
  String unit;
  List<String> months;

  GraphWidgetData({
    this.widgetInfo,
    required this.widgetType,
    required this.heading,
    required this.currentWeight,
    required this.targetWeight,
    required this.subHeading,
    required this.unit,
    required this.months,
  });

  factory GraphWidgetData.fromJson(
      dynamic widgetData, WidgetInfo widgetInfo, WidgetTypes widgetType) {
    return GraphWidgetData(
        widgetType: widgetType,
        widgetInfo: widgetInfo,
        currentWeight: double.parse(widgetData['currentWeight'].toString()),
        targetWeight: double.parse(widgetData['targetWeight'].toString()),
        heading: widgetData['heading'],
        subHeading: widgetData['subHeading'],
        months: widgetData['months'].map<String>((e) => e as String).toList(),
        unit: widgetData['unit']);
  }
}

class GraphWidget extends StatefulWidget {
  GraphWidgetData widgetData;
  GraphWidget({Key? key, required this.widgetData}) : super(key: key);

  @override
  _GraphWidgetState createState() => _GraphWidgetState(widgetData);
}

class _GraphWidgetState extends State<GraphWidget> {
  final List<Color> lineColor = [HexColor.fromHex("#0FE498")];
  final List<Color> areaColor = [Colors.red, Colors.yellow, Colors.green];
  GraphWidgetData widgetData;

  _GraphWidgetState(this.widgetData);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Text(widgetData.heading,
                style: AuroraThemeData().textStyle(TypescaleValues.H8,
                    color: Color(int.parse("0xffffffff")))),
            const SizedBox(width: Spacings.x1),
            Text(widgetData.unit,
                style: AuroraThemeData().textStyle(TypescaleValues.H3,
                    color: Color(int.parse("0xffffffff")))),
          ],
        ),
        const SizedBox(height: Spacings.x1),
        Text(widgetData.subHeading,
            style: AuroraThemeData().textStyle(TypescaleValues.P5,
                color: Color(int.parse("0x99ffffff")))),
        const SizedBox(height: Spacings.x5),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: Spacings.x7),
          child: AspectRatio(
            aspectRatio: 1.6,
            child: LineChart(
              mainData(widgetData),
            ),
          ),
        ),
      ],
    );
  }

  String bottomTitleWidgets(double value) {
    if (value == 0) {
      return "      " + widgetData.months[0];
    } else if (value == 1) {
      return widgetData.months[1];
    } else if (value == 2) {
      return widgetData.months[2];
    }
    return '';
  }

  String leftTitleWidgets(double value) {
    return value.toString();
  }

  LineChartData mainData(GraphWidgetData widgetData) {
    return LineChartData(
      gridData: FlGridData(
        drawVerticalLine: false,
        drawHorizontalLine: true,
        horizontalInterval: 0.5,
        verticalInterval: 0.25,
        getDrawingHorizontalLine: (value) {
          return const FlLine(
            color: Color(0xff37434d),
            strokeWidth: 1,
          );
        },
        getDrawingVerticalLine: (value) {
          return const FlLine(
            color: Color(0xff37434d),
            strokeWidth: 1,
          );
        },
      ),
      titlesData: FlTitlesData(
        show: true,
        rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
        topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
        bottomTitles: AxisTitles(sideTitles: SideTitles(
          showTitles: true,
          reservedSize: scale(context, 40),
          getTitlesWidget: (value, meta) {
            return Text(bottomTitleWidgets(value),
              style: AuroraTheme.of(context).textStyle(
                  TypescaleValues.P5,
                  color: Color(int.parse("0x99ffffff"))),
            );
          },
        )),
        leftTitles: AxisTitles(sideTitles: SideTitles(
          showTitles: true,
          reservedSize: scale(context, 40),
          getTitlesWidget: (value, meta) {
            return Text(leftTitleWidgets(value),
              style: AuroraTheme.of(context).textStyle(
                  TypescaleValues.P5,
                  color: Color(int.parse("0x99ffffff"))),
            );
          },
        )),
      ),
      borderData: FlBorderData(
          show: true,
          border: const Border(
              bottom: BorderSide(color: Color(0xff37434d), width: 1),
              top: BorderSide(color: Color(0xff37434d), width: 1))),
      minX: 0,
      maxX: 1.1,
      minY: widgetData.targetWeight,
      maxY: widgetData.currentWeight,
      lineBarsData: [
        LineChartBarData(
          spots: getSpots(widgetData),
          isCurved: true,
          gradient: LinearGradient(
            colors: areaColor
          ),
          barWidth: 5,
          isStrokeCapRound: true,
          dotData: const FlDotData(
            show: false,
          ),
        ),
      ],
    );
  }

  getSpots(GraphWidgetData widgetData) {
    double startX = 0;
    double startY = widgetData.currentWeight;

    double endX = 1;
    double endY = widgetData.targetWeight;

    double bezierX = 0.8;
    double bezierY = widgetData.currentWeight -
        (widgetData.currentWeight - widgetData.targetWeight) * 0.1;

    double bezierX1 = 0.7;
    double bezierY1 = widgetData.currentWeight -
        (widgetData.currentWeight - widgetData.targetWeight) * 0.9;

    List<FlSpot> flList = [];
    flList.add(FlSpot(startX, startY));

    for (double t = 0.0; t <= 1; t += 0.01) {
      double x = ((1 - t) * (1 - t) * (1 - t) * startX +
          3 * (1 - t) * (1 - t) * t * bezierX +
          3 * (1 - t) * t * t * bezierX1 +
          t * t * t * endX);
      double y = ((1 - t) * (1 - t) * (1 - t) * startY +
          3 * (1 - t) * (1 - t) * t * bezierY +
          3 * (1 - t) * t * t * bezierY1 +
          t * t * t * endY);
      flList.add(FlSpot(x, y));
    }
    flList.add(FlSpot(endX, endY));
    return flList;
  }
}

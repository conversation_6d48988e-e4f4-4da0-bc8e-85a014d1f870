import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/constants/action_constants.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/atoms/secondary_button.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:flutter/material.dart' hide Action;

import 'package:common/action/action_handler.dart';
import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/action/action_handler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class FPptWidgetData implements IWidgetData {
  @override
  WidgetInfo? widgetInfo;
  @override
  WidgetTypes widgetType;

  final String? text;
  final String? subText;
  final String buttonText;
  String infoImage;
  List<String>? trainerImage;
  Action action;

  FPptWidgetData({
    this.widgetInfo,
    required this.widgetType,
    this.text,
    this.subText,
    required this.infoImage,
    required this.buttonText,
    this.trainerImage,
    required this.action,
  });

  factory FPptWidgetData.fromJson(
      dynamic widgetData, WidgetInfo widgetInfo, WidgetTypes widgetType) {
    return FPptWidgetData(
        widgetType: widgetType,
        widgetInfo: widgetInfo,
        text: widgetData['text'],
        subText: widgetData['subText'],
        infoImage: widgetData['infoImage'],
        buttonText: widgetData['buttonText'],
        action: Action.fromJson(widgetData['action']),
        trainerImage: widgetData['trainerImage']
            .map<String>((e) => e as String)
            .toList());
  }
}

class FPptWidget extends StatelessWidget {
  FPptWidgetData widgetData;
  FPptWidget({Key? key, required this.widgetData}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    var screenWidth = MediaQuery.of(context).size.width - Spacings.x4;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (widgetData.trainerImage != null &&
            widgetData.trainerImage!.isNotEmpty)
          getTopImageWidget(context, widgetData.trainerImage!),
        SizedBox(
          height: Spacings.x4,
        ),
        Text(widgetData.text ?? "",
            style: AuroraThemeData().textStyle(TypescaleValues.H1,
                color: Color(int.parse("0xffffffff")))),
        Text(widgetData.subText ?? "",
            style: AuroraThemeData().textStyle(TypescaleValues.P5,
                color: Color(int.parse("0x99ffffff")))),
        const SizedBox(
          height: Spacings.x4,
        ),
        CFNetworkImage(
          imageUrl: getImageUrl(context,
              imagePath: widgetData.infoImage, width: screenWidth.toInt()),
          fit: BoxFit.cover,
          width: screenWidth,
          height: screenWidth / 5,
          errorWidget: (context, url, error) => const Icon(Icons.error),
        ),
        SizedBox(
          height: Spacings.x4,
        ),
        SecondaryButton(
          () {
            ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
            AnalyticsRepository analyticsRepository =
                RepositoryProvider.of<AnalyticsRepository>(context);
            analyticsRepository.logButtonClickEvent(extraInfo: {
              if (widgetData.action.analyticsData != null)
                ...widgetData.action.analyticsData!
            });

            actionBloc.add(PerformActionEvent(widgetData.action));

            // Action action = Action(type: ActionTypes.NAVIGATION, url: "curefit://guidedscreen?cycleDay=1&mesoCycleId=721902&exerciseId=323934861_SINGLE_1&microCycleSequence=2&tenantId=1&totalMemory=**********");
            // // 0?mesoCycleId=721902&exerciseId=323934861_SINGLE_1&microCycleSequence=1&tenantId=1&totalMemory=**********

            // actionBloc.add(PerformActionEvent(action));
          },
          widgetData.buttonText,
          expanded: false,
          buttonType: SecondaryButtonType.BIG,
        )
      ],
    );
  }

  Widget getTopImageWidget(BuildContext context, List<String> trainerImage) {
    var trainerMap = trainerImage.asMap();
    var len = trainerMap.length;
    return Container(
        width: (scale(context, 70) * len) - (scale(context, 20) * (len - 1)),
        height: scale(context, 70),
        child: Stack(
            alignment: AlignmentDirectional.center,
            children: trainerMap.entries
                .map((e) => Positioned(
                    left: e.key * scale(context, 50),
                    child: ClipRRect(
                        borderRadius: BorderRadius.circular(scale(context, 35)),
                        child: CFNetworkImage(
                          imageUrl: getImageUrl(context,
                              imagePath: e.value, width: 74),
                          fit: BoxFit.cover,
                          width: scale(context, 70),
                          height: scale(context, 70),
                          errorWidget: (context, url, error) =>
                              const Icon(Icons.error),
                        ))))
                .toList()));
  }
}

import 'package:common/font/cf_icons.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/atoms/context_tags/live_interactive_tag.dart';
import 'package:common/ui/atoms/context_tags/premium_tag.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/organisms/story_carousel.dart';
import 'package:common/ui/theme/Colors.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:common/util/color.dart';
import 'package:flutter/material.dart';
import 'package:common/action/action_handler.dart' as action_handler;

class TestimonialWidgetData implements IWidgetData {
  @override
  WidgetInfo? widgetInfo;
  @override
  WidgetTypes widgetType;

  final String? title;
  final String? image;
  String? subTitle;
  String? message;

  TestimonialWidgetData(
      {this.widgetInfo,
      required this.widgetType,
      this.image,
      this.title,
      this.subTitle,
      this.message});

  factory TestimonialWidgetData.fromJson(
      dynamic widgetData, WidgetInfo widgetInfo, WidgetTypes widgetType) {
    return TestimonialWidgetData(
        widgetType: widgetType,
        widgetInfo: widgetInfo,
        title: widgetData['name'],
        image: widgetData['image'],
        subTitle: widgetData['experience'],
        message: widgetData['message']);
  }
}

class TestimonialWidget extends StatelessWidget {
  TestimonialWidgetData widgetData;
  TestimonialWidget({Key? key, required this.widgetData}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        ClipRRect(
            borderRadius: BorderRadius.circular(scale(context, 30)),
            child: CFNetworkImage(
              imageUrl: getImageUrl(context,
                  imagePath: widgetData.image ?? "", width: 64),
              fit: BoxFit.cover,
              width: scale(context, 60),
              height: scale(context, 60),
              errorWidget: (context, url, error) => const Icon(Icons.error),
            )),
        SizedBox(height: scale(context, 10)),
        Text(widgetData.title ?? "",
            style: AuroraThemeData().textStyle(TypescaleValues.H4,
                color: Color(int.parse("0xffffffff")))),
        SizedBox(height: scale(context, 5)),
        Text(widgetData.subTitle ?? "",
            textAlign: TextAlign.center,
            style: AuroraThemeData().textStyle(TypescaleValues.P8,
                color: Color(int.parse("0xffffffff")))),
        SizedBox(height: scale(context, 10)),
        Stack(
          alignment: AlignmentDirectional.topCenter,
          children: [
            Icon(
              CFIcons.quote,
              color: ColorPalette.white10,
              size: scale(context, 55),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(
                  vertical: Spacings.x4, horizontal: Spacings.x6),
              child: Text(widgetData.message ?? "",
                  textAlign: TextAlign.center,
                  style: AuroraThemeData().textStyle(TypescaleValues.P9,
                      color: Color(int.parse("0x99ffffff")))),
            )
          ],
        )
      ],
    );
  }
}

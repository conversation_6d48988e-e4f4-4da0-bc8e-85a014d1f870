import 'dart:async';
import 'dart:convert';
import 'dart:ui';

import 'package:aitrainer/UI/modals/LogModal.dart';
import 'package:aitrainer/UI/modals/ReplaceModal.dart';
import 'package:aitrainer/UI/widgets/CountDownWidget.dart';
import 'package:aitrainer/UI/widgets/fp_exercise_bar.dart';
import 'package:aitrainer/UI/widgets/fp_guided_timer_widget.dart';
import 'package:aitrainer/blocs/guided_screen/guided_event.dart';
import 'package:aitrainer/blocs/log_modal/log_modal_bloc.dart';
import 'package:common/action/action_handler.dart' as ah;
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/constants/action_constants.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/blur_view.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/molecules/bottom_tray.dart';
import 'package:common/ui/theme/Colors.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/ui/widget_builder.dart';
import 'package:common/util/color.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:video_player_cf/video_player.dart';


import '../../blocs/guided_screen/guided_bloc.dart';
import '../../blocs/replace_modal/replace_bloc.dart';
import 'package:common/analytics/analytics_repository.dart';
import 'package:enum_to_string/enum_to_string.dart';

import '../../constants/constants.dart';
import 'FocusPointWidget.dart';

class FPWorkoutDetailWidgetData implements IWidgetData {
  @override
  WidgetInfo? widgetInfo;
  @override
  WidgetTypes widgetType;

  final List<Color> linearGradient;
  final dynamic rightActions;
  final dynamic exerciseStopWatch;
  final dynamic textualAction;
  final dynamic exerciseExecutionWidget;
  final int startTime;
  final int itemIndex;
  final List<int> sectionLastIndexList;
  final String? cycleDay;
  final String? tenantId;
  final String? microCycleSequence;
  final bool isSmallDevice;
  final List<String>? muscleFocusDark;
  final List<String>? pointsOfPerformance;

  FPWorkoutDetailWidgetData(
    this.itemIndex,
    this.sectionLastIndexList, {
    this.rightActions,
    this.exerciseStopWatch,
    this.widgetInfo,
    required this.widgetType,
    required this.linearGradient,
    required this.textualAction,
    this.exerciseExecutionWidget,
    required this.startTime,
    this.cycleDay,
    this.tenantId,
    this.microCycleSequence,
    required this.isSmallDevice,
    this.muscleFocusDark,
    this.pointsOfPerformance,
  });

  factory FPWorkoutDetailWidgetData.fromJson(
      dynamic widgetData, WidgetInfo widgetInfo, WidgetTypes widgetType) {

    List<String>? muscleFocusDark = [];
    if(widgetData['muscleFocusDark'] != null){
      for( var item in widgetData['muscleFocusDark']){
        muscleFocusDark.add(item);
      }
      if(muscleFocusDark.isEmpty){
        muscleFocusDark = null;
      }
    }

    List<String>? pointsOfPerformance = [];
    if(widgetData['pointsOfPerformance'] != null){
      for(var item in widgetData['pointsOfPerformance']){
        pointsOfPerformance.add(item);
      }
      if(pointsOfPerformance.isEmpty){
        pointsOfPerformance = null;
      }
    }
    return FPWorkoutDetailWidgetData(
      widgetData['itemIndex'],
      (widgetData['sectionLastIndexList'] as List).map<int>((e) => e).toList(),
      widgetType: widgetType,
      widgetInfo: widgetInfo,
      linearGradient: (widgetData['linearGradient'] as List)
          .map((e) => HexColor.fromHex(e).withOpacity(0.80))
          .toList(),
      rightActions: widgetData?['rightActions'],
      textualAction: widgetData?['textualAction'],
      exerciseStopWatch: widgetData?['exerciseStopWatch'],
      exerciseExecutionWidget: widgetData?['exerciseExecutionWidget'],
      startTime: widgetData?['startTime'],
      cycleDay: widgetData?['cycleDay'],
      tenantId: widgetData?['tenantId'],
      microCycleSequence: widgetData?['microCycleSequence'],
      isSmallDevice: widgetData['isSmallDevice'],
      muscleFocusDark: muscleFocusDark,
        pointsOfPerformance: pointsOfPerformance,
    );
  }
}

class FPWorkoutDetailWidget extends StatefulWidget {
  FPWorkoutDetailWidgetData widgetData;
  FPWorkoutDetailWidget({Key? key, required this.widgetData}) : super(key: key);

  @override
  State<FPWorkoutDetailWidget> createState() => _FPWorkoutDetailWidgetState();
}

class _FPWorkoutDetailWidgetState extends State<FPWorkoutDetailWidget>
    with SingleTickerProviderStateMixin {
  bool isOverlayHidden = false;
  Color qtColor = ColorPalette.white;

  Timer? _timer;
  late int currentTime;
  late int timeElasped;

  Duration animDuration = const Duration(milliseconds: 300);
  late AnimationController animController;
  late CurvedAnimation curvedAnimation;
  late Animation<double> floatingButtonAnimation;
  late Animation<double> muscleFocusAnimation;
  late Animation<double> exerciseInfoAnimation;
  late Animation<Color?> colorAnimation;
  late Animation<Color?> unitColorAnimation;
  late Animation<double> opacityAnimation;
  late VideoPlayerController _Last10SecvideoPlayerController;
  late VideoPlayerController _Last5SecvideoPlayerController;

  // List<String> pointOfFocus = ["First Point","Second Point","Third Point","Fourth Point","Fifth Point"];
  // late AnimationController pointOfFocusController;
  // late Animation<double> pointOfFocusAnimation;
  // int pointOfFocusIndex = -1;

  late bool isTimeBasedExercise;
  bool isTimerDisappearedOnce = false;
  bool showCountDown = false;
  bool showMuscleFocusImage = false;

  logPageView(Map<String, dynamic> eventInfo) {
    AnalyticsRepository analyticsRepository =
    RepositoryProvider.of<AnalyticsRepository>(context);
    analyticsRepository.logPageViewEvent(
        pageId: EnumToString.convertToString(RouteNames.guidedscreen),
        pageName: EnumToString.convertToString(RouteNames.guidedscreen),
        eventInfo: eventInfo,
    );
  }

  // @override
  // void setState(VoidCallback fn){
  //   if(mounted){
  //     super.setState(fn);
  //   }
  // }

  @override
  void initState() {
    super.initState();

    isTimeBasedExercise = widget.widgetData.exerciseStopWatch != null;

    Map<String, dynamic> eventInfo = {};
    if(isTimeBasedExercise) {
      eventInfo["detailstring1"] = widget.widgetData.exerciseStopWatch["exerciseName"]?? "";
      eventInfo["detailbool1"] = true;
    } else {
      eventInfo["detailstring1"] = widget.widgetData.exerciseExecutionWidget?["exerciseName"] ?? "";
      eventInfo["detailbool1"] = false;
    }

    try {
      _Last10SecvideoPlayerController = VideoPlayerController(
          videoPlayerOptions: VideoPlayerOptions(mixWithOthers: true));
      _Last10SecvideoPlayerController.setAssetDataSource('assets/10seconds.mp3');

    } catch (err) {
      print('error setting asset');
    }

    try {
      _Last5SecvideoPlayerController = VideoPlayerController(
          videoPlayerOptions: VideoPlayerOptions(mixWithOthers: true));
      _Last5SecvideoPlayerController.setAssetDataSource('assets/beeps.mp3');

    } catch (err) {
      print('error setting asset 5 secs');
    }


    logPageView(eventInfo);

    animController = AnimationController(
      duration: animDuration,
      vsync: this,
    );
    curvedAnimation = CurvedAnimation(
      parent: animController,
      curve: Curves.decelerate,
    );

    var devHeight = window.physicalSize.height / window.devicePixelRatio;

    var topSpacing = scaleWithHeight(80, devHeight);
    floatingButtonAnimation = Tween<double>(
      begin: topSpacing,
      end:topSpacing - 50,
    ).animate(curvedAnimation);

    muscleFocusAnimation = Tween<double>(
      begin: topSpacing + 100,
      end: topSpacing + 100 - 50,
    ).animate(curvedAnimation);

    opacityAnimation = Tween<double>(
      begin: 1,
      end: 0,
    ).animate(curvedAnimation);

    colorAnimation = ColorTween(
      begin: ColorPalette.white,
      end: ColorPalette.black,
    ).animate(curvedAnimation);

    unitColorAnimation = ColorTween(
      begin: ColorPalette.white60,
      end: ColorPalette.black,
    ).animate(curvedAnimation);

    double end = -1 * scaleWithHeight(100, devHeight);
    // double end = -1 * scaleWithHeight(
    //     (widget.widgetData.isSmallDevice)? 180: 100,
    //     devHeight
    // );
    double begin = (widget.widgetData.isSmallDevice)? Spacings.x5 : Spacings.x9;
    exerciseInfoAnimation = Tween<double>(
      begin: begin,
      end: end,
    ).animate(curvedAnimation)
      ..addStatusListener((status) {
        if (AnimationStatus.completed == status) {
          if (isTimeBasedExercise) {
            if (currentTime ==
                widget.widgetData.exerciseStopWatch['totalTime']) {
              showCountDown = true;
            }  else {
              startTimer();
            }
          }
        }
      });

    if (isTimeBasedExercise) {
      var exerciseStopWatch = widget.widgetData.exerciseStopWatch;
      currentTime = exerciseStopWatch['totalTime'];
    }

    // overlay auto-disappear
    if (!isTimerDisappearedOnce) {
      Timer(
          const Duration(seconds: 7),
          () => {
                if (!isTimerDisappearedOnce)
                  {
                    renderIfMounted(() => {
                          if (!isOverlayHidden) {invertOverlay()}
                        })
                  }
              });
    }

    if(!showMuscleFocusImage){
      Timer(
        const Duration(seconds: 4),
          () => {
            showMuscleFocusFunction()
          }
      );
    }
  }

  void showMuscleFocusFunction(){
    if(!showMuscleFocusImage && mounted){
      showMuscleFocusImage = true;
      setState(() {

      });
    }
  }



  void renderIfMounted(Function() a) {
    if (mounted) {
      setState(() {
        a();
      });
    }
  }

  @override
  void dispose() {
    cancelTimer();
    animController.clearListeners();
    animController.dispose();
    _Last10SecvideoPlayerController?.dispose();
    _Last5SecvideoPlayerController?.dispose();
    super.dispose();
  }

  void cancelTimer() {
    _timer?.cancel();
    _timer = null;
  }

  void startTimer() {
    _timer ??= Timer.periodic(
        const Duration(seconds: 1),
        (t) => {
              renderIfMounted(() {
                    if (currentTime > 0) {
                      currentTime--;
                      if (currentTime == 10) {
                        // play last 10 sec sound
                        _Last10SecvideoPlayerController.play();
                      } else if (currentTime == 4) {
                        // play beep mp3
                        _Last5SecvideoPlayerController.play();
                      }
                    }
                    else {
                      cancelTimer();
                      if (widget.widgetData.sectionLastIndexList[2] != widget.widgetData.itemIndex){
                        openNextScreen();
                      }
                      else{
                        // show pause screen
                        invertOverlay();
                      }
                    }
                  })
            });
  }

  handleOverlayTap(){
    if(isOverlayHidden) {
      showMuscleFocusImage = true;
    }
    setState(() {
      invertOverlay();
    });
  }

  Widget placeholderForMuscleFocus() {
    return Container(
      color: ColorPalette.transparent,
    );
  }


  Widget getMuscleFocusRow(){
    var devHeight = MediaQuery.of(context).size.height;
    List<String>? muscleFocusDark = widget.widgetData.muscleFocusDark;
    List<Widget> muscleFocusDarkWidgets = [];
    if(muscleFocusDark != null) {
      for (var item in muscleFocusDark) {
        muscleFocusDarkWidgets.add(
            SizedBox(
              width: scaleWithHeight(Spacings.x20, devHeight),
              height: scaleWithHeight((2* Spacings.x20), devHeight),
              child: CFNetworkImage(
                imageUrl: getImageUrl(context,
                    width: 400,
                    imagePath: item),
                fit: BoxFit.scaleDown,
                placeholder: (context, url){ return placeholderForMuscleFocus();},
                errorWidget: (context, url, error) =>
                const Icon(Icons.error),
              ),
            )
        );
      }
    }

    return SizedBox(
      width: scaleWithHeight(200, devHeight),
      height: scaleWithHeight(200, devHeight),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: muscleFocusDarkWidgets
      )
    );
  }

  @override
  Widget build(BuildContext context) {
    var devWidth = MediaQuery.of(context).size.width;
    var devHeight = MediaQuery.of(context).size.height;
    var isRepBasedExercise = widget.widgetData.exerciseExecutionWidget != null;
    var subHeader = isRepBasedExercise
        ? widget.widgetData.exerciseExecutionWidget['subheader']
        : widget.widgetData.exerciseStopWatch?['subheader'];
    return GestureDetector(
        onTap: () => {
          handleOverlayTap()
        },
        child: AnimatedContainer(
            duration: animDuration,
            decoration: BoxDecoration(
              color: isOverlayHidden? ColorPalette.transparent :Colors.black.withOpacity(0.77)
            ),
            child: Stack(children: [
              FPGuidedTimerWidget(
                startTime: widget.widgetData.startTime,
                isOverlayVisible: !isOverlayHidden,
              ),
              AnimatedBuilder(
                  animation: curvedAnimation,
                  builder: (_, child) => Stack(children: <Widget>[
                        Positioned(
                          top: floatingButtonAnimation.value,
                          left: 0,
                          width: devWidth,
                          child: Opacity(
                            opacity: opacityAnimation.value,
                            child: Column(
                              children: [
                                (subHeader != null)?
                                  Opacity(
                                    opacity: opacityAnimation.value,
                                    child: Text(
                                      subHeader,
                                      style: AuroraTheme.of(context).textStyle(
                                        TypescaleValues.P6,
                                        color: ColorPalette.statusPositive,
                                      ),
                                    ),
                                  )
                                : const SizedBox(height: Spacings.x3,),
                                const SizedBox(height: Spacings.x1),
                                Padding(
                                  padding: EdgeInsets.symmetric(horizontal: scale(context, Spacings.x6)),
                                  child: Opacity(
                                    opacity: opacityAnimation.value,
                                    child: Text(
                                      isRepBasedExercise
                                          ? widget.widgetData.exerciseExecutionWidget['exerciseName']
                                          : widget.widgetData.exerciseStopWatch['exerciseName'],
                                      textAlign: TextAlign.center,
                                      maxLines: 2,
                                      overflow: TextOverflow.ellipsis,
                                      style: const TextStyle(fontSize: 30,fontFamily: "inter", fontWeight: FontWeight.w400, color: ColorPalette.white)
                                    ),
                                  ),
                                ),
                              ]
                            ),
                          ),
                        ),
                        if(showMuscleFocusImage)
                          Positioned(
                          top: muscleFocusAnimation.value,
                            left: 0,
                            width: devWidth,
                            child: Opacity(
                              opacity: opacityAnimation.value,
                              child: getMuscleFocusRow()
                            )
                        ),
                        Positioned(
                          bottom: exerciseInfoAnimation.value,
                          left: 0,
                          width: devWidth,
                          child: getExerciseInfo(
                            widget.widgetData.exerciseStopWatch,
                            widget.widgetData.exerciseExecutionWidget,
                            widget.widgetData.textualAction,
                          ),
                        ),
                        Positioned(
                          bottom: (widget.widgetData.isSmallDevice)? Spacings.x3: Spacings.x5,
                          left: 0,
                          width: devWidth,
                          child: FpExerciseBarWidget(
                            itemIndex: widget.widgetData.itemIndex,
                            sectionLastIndexList:
                                widget.widgetData.sectionLastIndexList,
                            isOverlayVisible: !isOverlayHidden,
                          ),
                        ),
                      ]))
            ])));
  }

  getFloatingActionRow(rightActions) {
    var devHeight = window.physicalSize.height / window.devicePixelRatio;
    if (rightActions != null) {
      var rightActionsList = rightActions as List;
      if (rightActionsList.isNotEmpty) {
        return Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: getFloatingActions(rightActionsList),
        );
      } else {
        return SizedBox(
          height: scaleWithHeight(60, devHeight),
          width: 20,
        );
      }
    } else {
      return SizedBox(
        height: scaleWithHeight(60, devHeight),
        width: 20,
      );
    }
    return Container();
  }

  getFloatingActions(List<dynamic> rightActionsList) {
    var devHeight = window.physicalSize.height / window.devicePixelRatio;
    List<Widget> widgets = [];
    for (var i = 0; i < rightActionsList.length; i++) {
      var rightAction = rightActionsList[i];
      if (i != 0) {
        widgets.add(SizedBox(
          width: scale(context, 10),
        ));
      }
      widgets.add(InkWell(
          onTap: isOverlayHidden
              ? null
              : () => {onFloatingButtonPressed(rightAction)},
          child: Padding(
            padding: const EdgeInsets.only(left: Spacings.x2, right: Spacings.x2),
            child: Column(children: [
              BlurView(
                blurType: BlurType.LOW,
                borderRadius: scaleWithHeight( Spacings.x5, devHeight),
                child: Container(
                    width: scaleWithHeight(Spacings.x10, devHeight),
                    height: scaleWithHeight( Spacings.x10, devHeight),
                    decoration: BoxDecoration(
                        color: ColorPalette.white.withOpacity(0.03),
                        borderRadius: BorderRadius.circular(scaleWithHeight( Spacings.x5, devHeight),)),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        SizedBox(
                          width: scaleWithHeight(42, devHeight),
                          height: scaleWithHeight(42, devHeight),
                          child: CFNetworkImage(
                            imageUrl: getImageUrl(context,
                                width: 25,
                                imagePath: rightAction['iconUrl'] ?? ""),
                                fit: BoxFit.scaleDown,
                            errorWidget: (context, url, error) =>
                                const Icon(Icons.error),
                          ),
                        )
                      ],
                    )),
              ),
              const SizedBox(
                height: Spacings.x1,
              ),
              Text(rightAction['title']?.toString() ?? "",
                  style: AuroraThemeData().textStyle(TypescaleValues.P8,
                      color: ColorPalette.white60))
            ]),
          )));
    }
    return widgets;
  }

  isLastCard(){
    if(widget.widgetData.sectionLastIndexList[2] == widget.widgetData.itemIndex) {
      return true;
    } else {
      return false;
    }
  }

  getExerciseInfo(exerciseStopWatch, exerciseExecutionWidget, textualAction) {
    var isRepBasedExercise = exerciseExecutionWidget != null;
    var devHeight = window.physicalSize.height / window.devicePixelRatio;
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        isRepBasedExercise
            ? getExerciseExecutionWidget(exerciseExecutionWidget)
            : getExerciseStopWatch(exerciseStopWatch),
        SizedBox(height: scaleWithHeight(widget.widgetData.isSmallDevice ? Spacings.x2 : Spacings.x8, devHeight)),
        getFloatingActionRow(widget.widgetData.rightActions),
        SizedBox(height: scaleWithHeight(widget.widgetData.isSmallDevice ? Spacings.x2 : Spacings.x6, devHeight)),
        (textualAction != null) ? getNextExerciseWidget(textualAction):
        (isLastCard() && (isRepBasedExercise || (!isRepBasedExercise && currentTime == 0))) ?
        Padding(
          padding: const EdgeInsets.only(left: Spacings.x4,right: Spacings.x4, bottom: Spacings.x3, top: Spacings.x4),
          child: PrimaryButton(() {BlocProvider.of<GuidedPageBloc>(context)
              .add(WorkoutCompleteEvent()); }, "Complete Workout"),
        ):
        SizedBox(height: scaleWithHeight(65, devHeight),)
      ],
    );
  }

  void handleReplay(int totalTime){
    setState(() {
      currentTime = totalTime;
      invertOverlay();
    });
  }

  void handlePlay(){
    setState(() {
      invertOverlay();
    });
  }

  getExerciseExecutionWidget(exerciseExecutionWidget) {
    var itemsList = exerciseExecutionWidget['items'] as List;
    var item = itemsList[0];
    var loggedWeight = exerciseExecutionWidget != null &&
            exerciseExecutionWidget['loggedWeight'] != null
        ? exerciseExecutionWidget['loggedWeight']
        : null;
    var loggedWeightUnit = exerciseExecutionWidget != null &&
            exerciseExecutionWidget['loggedWeightUnit'] != null
        ? exerciseExecutionWidget['loggedWeightUnit']
        : null;
    return FocusPointWidget(
      isOverlayHidden: isOverlayHidden,
      repColor: colorAnimation.value,
      unitColor: unitColorAnimation.value,
      exerciseExecutionData: item,
      exerciseStopWatchData: null,
      isSmallDevice: widget.widgetData.isSmallDevice,
      loggedWeight: loggedWeight,
      loggedWeightUnit:loggedWeightUnit,
      isLooped: true,
      isTimeBasedExercise: false,
      currentTime: 0,
      opacityAnimationValue: 0,
      handleReplay: null,
      handlePlay: null,
      pointsOfPerformance: widget.widgetData.pointsOfPerformance,
    );
  }

  formatTime(time){
    return time.toString().padLeft(2,"0");
  }

  getExerciseStopWatch(exerciseStopWatch) {
    if (showCountDown) {
      return CountDownWidget(onCountDownFinish: onCountDownFinish, POPData: widget.widgetData.pointsOfPerformance );
    } else {
      return FocusPointWidget(
          isOverlayHidden: isOverlayHidden,
          repColor: colorAnimation.value,
          unitColor: unitColorAnimation.value,
          exerciseExecutionData: null,
          exerciseStopWatchData: exerciseStopWatch,
          isSmallDevice: widget.widgetData.isSmallDevice,
          loggedWeight: null,
          loggedWeightUnit: null,
          isLooped: false,
          isTimeBasedExercise: true,
          currentTime: currentTime,
          opacityAnimationValue: opacityAnimation.value,
          handleReplay: handleReplay,
          handlePlay: handlePlay,
          pointsOfPerformance: widget.widgetData.pointsOfPerformance
      );
    }
  }

  getNextWidgetImage(textualAction) {
    if (textualAction['title'] == null || textualAction['title'] == "Rest") {
      return Padding(
          padding: const EdgeInsets.all(Spacings.x2),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(scale(context, Spacings.x1)),
            child: Container(
              width: scale(context, 61),
              height: scale(context, 61),
              color: Colors.black26,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    textualAction["duration"] ?? "",
                      style: AuroraTheme.of(context).textStyle(
                        TypescaleValues.H9,
                        color: Colors.white.withOpacity(0.9),
                      )
                  ),
                  Text(
                    textualAction["units"] ?? "",
                      style: AuroraTheme.of(context).textStyle(
                        TypescaleValues.TAGTEXT,
                        color: ColorPalette.white60,
                      )
                  )
                ],
              ),
            ),
          ),
      );
    } else {
      return Padding(
        padding: const EdgeInsets.all(Spacings.x2),
        child: ClipRRect(
            borderRadius: BorderRadius.circular(scale(context, Spacings.x1)),
            child: CFNetworkImage(
              imageUrl:
                  getImageUrl(
                      context,
                      imagePath: textualAction['icon'] ?? "",
                      width: scale(context, 61).toInt(),
                  ),
              fit: BoxFit.fill,
              width: scale(context, 61),
              height: scale(context, 61),
            )
        ),
      );
    }
  }

  getNextExerciseWidget(textualAction) {
    var devHeight = window.physicalSize.height / window.devicePixelRatio;
    return Padding(
        padding: EdgeInsets.symmetric(horizontal: scaleWithHeight(Spacings.x4, devHeight)),
        child:InkWell(
              onTap: () {
                openNextScreen();
              },
              child: Container(
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(scaleWithHeight(Spacings.x2, devHeight)),
                    color: ColorPalette.white10
                ),
                child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      getNextWidgetImage(textualAction),
                      SizedBox(width: scaleWithHeight(Spacings.x1, devHeight)),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: [
                            Text(textualAction["prefix"] ?? "",
                                style: AuroraTheme.of(context).textStyle(
                                  TypescaleValues.P5,
                                  color: Colors.white38,
                                )),
                            Text(textualAction['title'].toString(),
                                maxLines: 2,
                                softWrap: true,
                                // textAlign: TextAlign.center,
                                overflow: TextOverflow.ellipsis,
                                style: AuroraTheme.of(context).textStyle(
                                  TypescaleValues.P1,
                                  color: Colors.white,
                                )),
                          ],
                        ),
                      ),
                      SizedBox(width: scaleWithHeight(20, devHeight),),
                      SizedBox(
                        width: scaleWithHeight(35, devHeight),
                        height: scaleWithHeight(35, devHeight),
                        child: CFNetworkImage(
                          imageUrl: getImageUrl(context,
                              width: 14,
                              imagePath:textualAction["rightIcon"] ?? ""),
                          fit: BoxFit.scaleDown,
                          errorWidget: (context, url, error) =>
                          const Icon(Icons.error),
                        ),
                      ),
                      SizedBox(width: scaleWithHeight(20, devHeight),)
                    ],
                  ),
              ))
    );
  }




  logPausePageView() {

    var isTimeBasedExercise = widget.widgetData.exerciseStopWatch != null;

    Map<String, dynamic> eventInfo = {};
    if(isTimeBasedExercise) {
      eventInfo["detailstring1"] = widget.widgetData.exerciseStopWatch["exerciseName"]?? "";
      eventInfo["detailbool1"] = true;
    } else {
      eventInfo["detailstring1"] = widget.widgetData.exerciseExecutionWidget?["exerciseName"] ?? "";
      eventInfo["detailbool1"] = false;
    }

    AnalyticsRepository analyticsRepository =
    RepositoryProvider.of<AnalyticsRepository>(context);
    analyticsRepository.logPageViewEvent(
        pageId: "FitnessPlan Pause Screen flutter",
        eventInfo: eventInfo);
  }

  logPausePageClose() {

    var isTimeBasedExercise = widget.widgetData.exerciseStopWatch != null;

    Map<String, dynamic> eventInfo = {};
    if(isTimeBasedExercise) {
      eventInfo["detailstring1"] = widget.widgetData.exerciseStopWatch["exerciseName"]?? "";
      eventInfo["detailbool1"] = true;
    } else {
      eventInfo["detailstring1"] = widget.widgetData.exerciseExecutionWidget?["exerciseName"] ?? "";
      eventInfo["detailbool1"] = false;
    }
  }

  void invertOverlay() {
    isOverlayHidden = !isOverlayHidden;
    if (isOverlayHidden) {
      // overlay disappear
      animController.forward();
      // if (isTimeBasedExercise) {
        isTimerDisappearedOnce = true;
      // }
      logPausePageClose();
    } else {
      showCountDown = false;
      cancelTimer();
      animController.reverse();
      logPausePageView();
    }
  }

  void onCountDownFinish() {
    setState(() => {stopCountDown()});
  }

  void stopCountDown() {
    showCountDown = false;
    startTimer();
  }

  void openNextScreen() {
    _Last10SecvideoPlayerController?.pause();
    _Last5SecvideoPlayerController?.pause();
    var bloc = BlocProvider.of<GuidedPageBloc>(context);
    bloc.add(NextSwipeGuidedPageEvent());
  }

  getReplacementExerciseCard() {
    return Row(
      children: const [
        Expanded(flex: 1, child: Text("alok")),
        Expanded(flex: 1, child: Text("jaiswal"))
      ],
    );
  }

  void onFloatingButtonPressed(rightAction) {
    isTimerDisappearedOnce = true;
    if ("PLAY_VIDEO" == rightAction['actionType']) {
      ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);

      var uri = Uri.dataFromString(rightAction['url']);
      Map<String, String> params = uri.queryParameters;
      var videoUrl = params['absoluteVideoUrl'];
      ah.Action action =
          ah.Action(type: ActionTypes.PLAY_VIDEO_FLUTTER, url: videoUrl);

      actionBloc.add(PerformActionEvent(action));
    } else if ("GET_ALTERNATIVES_FOR_EXERCISE" == rightAction['actionType']) {
      var guidedPageBloc = BlocProvider.of<GuidedPageBloc>(context);
      final replaceModalBloc = BlocProvider.of<ReplaceModalBloc>(context);
      final String title =
          rightAction!['meta']!['onCompleteAction']!['meta']!['title'] ??
              "Swap";
      final int exerciseEntryId = rightAction!['meta']!['exerciseEntryId'];
      showBottomTray(
          context: context,
          showTopNotch: true,
          child: ReplaceModal(
              guidedPageBloc: guidedPageBloc,
              replaceModalBloc: replaceModalBloc,
              modalTitle: title,
              exerciseEntryId: exerciseEntryId,
              cycleDay: widget.widgetData.cycleDay,
              tenantId: widget.widgetData.tenantId,
              microCycleSequence: widget.widgetData.microCycleSequence));
    } else if ("LOG_EXERCISE" == rightAction['actionType']) {
      final logModalBloc = BlocProvider.of<LogModalBloc>(context);
      final loggingExerciseIds =
          rightAction!['meta']!['loggingExerciseIds']! ?? [];
      showModalBottomSheet(
          context: context,
          backgroundColor: Colors.black,
          isScrollControlled: true,
          enableDrag: false,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20.0),
          ),
          builder: (BuildContext context) {
            return Padding(
              padding: EdgeInsets.only(
                  bottom: MediaQuery.of(context).viewInsets.bottom),
              child: LogModal(
                  logModalBloc: logModalBloc,
                  loggingExerciseIds: loggingExerciseIds),
            );
          });
    }
  }
}

import 'dart:convert';

import 'package:aitrainer/blocs/log_modal/log_modal_bloc.dart';
import 'package:common/action/action_handler.dart';
import 'package:common/constants/action_constants.dart';
import 'package:common/font/cf_icons.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/ui/alert_view.dart';
import 'package:common/ui/atoms/action_button.dart';
import 'package:common/ui/atoms/input_fields/input_field.dart';
import 'package:common/ui/atoms/twin_button.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/theme/Colors.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/src/bloc_consumer.dart';
import 'package:screenshot/screenshot.dart';
import 'package:common/action/action_handler.dart' as ActionHandler;

import '../../blocs/log_modal/log_modal_model.dart';

class LogModal extends StatefulWidget {
  final LogModalBloc logModalBloc;
  final List<dynamic> loggingExerciseIds;

  const LogModal(
      {Key? key, required this.logModalBloc, required this.loggingExerciseIds})
      : super(key: key);
  @override
  State<LogModal> createState() => _LogModalState();
}

class _LogModalState extends State<LogModal> with WidgetsBindingObserver {
  late ScrollController _scrollController;
  List<bool> selectedUnit = [];

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    WidgetsBinding.instance?.addObserver(this);
    WidgetsBinding.instance?.addPostFrameCallback((timeStamp) {
      refresh(context: context);
    });
  }

  refresh({required BuildContext context}) {
    widget.logModalBloc
        .add(LoadLogModalEvent(exerciseIds: widget.loggingExerciseIds));
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<LogModalBloc, LogModalState>(
      bloc: widget.logModalBloc,
      listener: (context, state) {
        if (state is LogModalFailed) {
          showErrorAlert(
              context: context,
              title: state.error,
              onClose: () {
                Navigator.pop(context);
              });
        }
      },
      builder: buildWidgets,
    );
  }

  Widget buildWidgets(context, state) {
    if (state is LogModalLoading && state.showLoader) {
      return const SizedBox(height: 400, child: FancyLoadingIndicator());
    } else if (state is LogModalLoaded) {
      return LogModalContent(
          logModalState: state, logModalBloc: widget.logModalBloc);
    }
    return Container();
  }
}

class LogModalContent extends StatefulWidget {
  final dynamic logModalState;
  final LogModalBloc logModalBloc;
  const LogModalContent(
      {Key? key, required this.logModalState, required this.logModalBloc})
      : super(key: key);

  @override
  State<LogModalContent> createState() => _LogModalContentState();
}

class _LogModalContentState extends State<LogModalContent> {
  List<int> selectedOptions = [];

  @override
  void initState() {
    super.initState();
    List<ExerciseLogEntriesData>? exerciseLogEntries =
        widget.logModalState.data.exerciseLogEntries;
    for (var i = 0;
        exerciseLogEntries != null && i < exerciseLogEntries.length;
        i++) {
      if (exerciseLogEntries[i].toggleSwitch != null) {
        selectedOptions
            .add(exerciseLogEntries[i].toggleSwitch['selectedIndex']);
      } else {
        selectedOptions.add(0);
      }
    }
  }

  void changeUnits(int index, bool isFirst) {
    RegExp regex = RegExp(r'([.]*0)(?!.*\d)');
    List<ExerciseLogEntriesData>? exerciseLogEntries =
        widget.logModalState.data.exerciseLogEntries;
    if (exerciseLogEntries != null) {
      int newSelectedOption = isFirst ? 0 : 1;
      selectedOptions[index] = newSelectedOption;
      dynamic updatedOption =
          exerciseLogEntries[index].toggleSwitch["options"][newSelectedOption];

      List<FpExecutionLogsData>? fpExecutionLogs =
          exerciseLogEntries[index].fpExecutionLogs;
      for (var i = 0;
          fpExecutionLogs != null && i < fpExecutionLogs.length;
          i++) {
        if (exerciseLogEntries[index].exerciseType == "TIMED") {
          String unit1 = updatedOption['displayOption'];
          fpExecutionLogs[i].unit1 = unit1;
          if (updatedOption['option'] == "MINUTE" ||
              updatedOption['option'] == "MIN") {
            if (fpExecutionLogs[i].value1 != null &&
                fpExecutionLogs[i].value1 != "") {
              fpExecutionLogs[i].value1 =
                  (double.parse(fpExecutionLogs[i].value1!) / 60)!
                      .toStringAsFixed(2)
                      .replaceAll(regex, "");
            } else {
              fpExecutionLogs[i].value1 = "";
            }
          } else if (updatedOption['option'] == "SECOND" ||
              updatedOption['option'] == "SEC") {
            if (fpExecutionLogs[i].value1 != null &&
                fpExecutionLogs[i].value1 != "") {
              // fpExecutionLogs[i].value1 = (double.parse(fpExecutionLogs[i].value1!) * 60)!.toStringAsFixed(2).replaceAll(regex,"");
              fpExecutionLogs[i].value1 = double.parse(
                      (double.parse(fpExecutionLogs[i].value1!) * 60)!
                          .toStringAsFixed(2))
                  .round()
                  .toStringAsFixed(2)
                  .replaceAll(regex, "");
            } else {
              fpExecutionLogs[i].value1 = "";
            }
          }
          exerciseLogEntries[index].templateLog?.unit1 = unit1;
        } else if (exerciseLogEntries[index].exerciseType == "WEIGHTED") {
          String unit2 = updatedOption['displayOption'];
          fpExecutionLogs[i].unit2 = unit2;
          if (updatedOption['option'] == "LBS") {
            print("print here");
            print(fpExecutionLogs[i].value2.runtimeType);
            if (fpExecutionLogs[i].value2 != null &&
                fpExecutionLogs[i].value2 != "") {
              fpExecutionLogs[i].value2 =
                  (double.parse(fpExecutionLogs[i].value2!) * 2.205)!
                      .toStringAsFixed(1)
                      .replaceAll(regex, "");
            } else {
              fpExecutionLogs[i].value2 = "";
            }
          } else if (updatedOption['option'] == "KG") {
            if (fpExecutionLogs[i].value2 != null &&
                fpExecutionLogs[i].value2 != "") {
              fpExecutionLogs[i].value2 =
                  (double.parse(fpExecutionLogs[i].value2!) / 2.205)!
                      .toStringAsFixed(1)
                      .replaceAll(regex, "");
            } else {
              fpExecutionLogs[i].value2 = "";
            }
          }
          exerciseLogEntries[index].templateLog?.unit2 = unit2;
        }
      }

      setState(() {
        selectedOptions;
        widget.logModalState;
      });
    }
  }

  void changeUnitFunction(int index, bool isFirst) {
    if ((isFirst && selectedOptions[index] == 0) ||
        (!isFirst && selectedOptions[index] == 1)) {
      return;
    }
    changeUnits(index, isFirst);
    List<ExerciseLogEntriesData>? exerciseLogEntries =
        widget.logModalState.data.exerciseLogEntries;
    if (exerciseLogEntries != null &&
        exerciseLogEntries[index].lateral == "UNILATERAL") {
      if (index - 1 >= 0 &&
          (exerciseLogEntries[index].thumbnailUrl ==
              exerciseLogEntries[index - 1].thumbnailUrl)) {
        changeUnits(index - 1, isFirst);
      } else if (index + 1 < exerciseLogEntries.length &&
          (exerciseLogEntries[index].thumbnailUrl ==
              exerciseLogEntries[index + 1].thumbnailUrl)) {
        changeUnits(index + 1, isFirst);
      }
    }
  }

  BoxDecoration boxDecorationSwitch(int index, int selectedIndexValue) {
    if (index == selectedIndexValue) {
      return BoxDecoration(
          color: Colors.white, borderRadius: BorderRadius.circular(15));
    } else {
      return BoxDecoration(borderRadius: BorderRadius.circular(15));
    }
  }

  Widget getExerciseWidgets(List<ExerciseLogEntriesData>? exerciseLogEntries) {
    List<Widget> widgets = [];

    for (var i = 0;
        exerciseLogEntries != null && i < exerciseLogEntries.length;
        i++) {
      widgets.add(SizedBox(
        width: MediaQuery.of(context).size.width,
        child: Column(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      const SizedBox(
                        width: 14,
                      ),
                      if (exerciseLogEntries[i].thumbnailUrl != null)
                        ClipRRect(
                          borderRadius:
                              BorderRadius.circular(scale(context, 10)),
                          child: CFNetworkImage(
                            imageUrl: getImageUrl(context,
                                imagePath:
                                    exerciseLogEntries[i].thumbnailUrl ?? ""),
                            fit: BoxFit.cover,
                            width: scale(context, 70),
                          ),
                        ),
                      const SizedBox(
                        width: 14,
                      ),
                      Flexible(
                        child: Text(
                          exerciseLogEntries[i].exerciseName ?? "",
                          style: AuroraTheme.of(context)
                              .textStyle(TypescaleValues.H4),
                        ),
                      ),
                    ],
                  ),
                ),
                if (exerciseLogEntries[i].toggleSwitch != null)
                  const SizedBox(
                    width: 14,
                  ),
                if (exerciseLogEntries[i].toggleSwitch != null)
                  Container(
                    width: 90,
                    height: 30,
                    decoration: BoxDecoration(
                        color: Colors.white24,
                        borderRadius: BorderRadius.circular(15)),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        InkWell(
                          onTap: () => changeUnitFunction(i, true),
                          child: Container(
                            width: 40,
                            padding: const EdgeInsets.only(top: 4, bottom: 4),
                            decoration:
                                boxDecorationSwitch(0, selectedOptions[i]),
                            child: Text(
                                exerciseLogEntries[i].toggleSwitch['options'][0]
                                        ['option'] ??
                                    "",
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                    fontSize: scale(context, 10),
                                    fontWeight: FontWeight.bold,
                                    fontFamily: "Inter",
                                    color: selectedOptions[i] == 0
                                        ? Colors.black
                                        : Colors.white)
                                // style: AuroraTheme.of(context).textStyle(TypescaleValues.P8, color: selectedOptions[i] == 0? Colors.black: Colors.white),
                                ),
                          ),
                        ),
                        InkWell(
                          onTap: () => changeUnitFunction(i, false),
                          child: Container(
                            width: 40,
                            padding: const EdgeInsets.only(top: 4, bottom: 4),
                            decoration:
                                boxDecorationSwitch(1, selectedOptions[i]),
                            child: Text(
                                exerciseLogEntries[i].toggleSwitch['options'][1]
                                        ['option'] ??
                                    "",
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                    fontSize: scale(context, 10),
                                    fontWeight: FontWeight.bold,
                                    fontFamily: "Inter",
                                    color: selectedOptions[i] == 1
                                        ? Colors.black
                                        : Colors.white)
                                // style: AuroraTheme.of(context).textStyle(TypescaleValues.P8, color: selectedOptions[i] == 1? Colors.black: Colors.white),
                                ),
                          ),
                        ),
                      ],
                    ),
                  ),
                const SizedBox(
                  width: 14,
                ),
              ],
            ),
            // const SizedBox(height: 10),
            LogExerciseExecutions(
              exerciseLogEntry: exerciseLogEntries[i],
            ),
            const SizedBox(
              height: 50,
            )
          ],
        ),
      ));
    }

    return Column(
      children: widgets,
    );
  }

  onSubmitPress(List<ExerciseLogEntriesData>? exerciseLogEntries) {
    List<FpExecutionLogsData>? fpExecutionLogs =
        exerciseLogEntries![0].fpExecutionLogs;
    List exerciseLogEntriesJson = [];
    for (ExerciseLogEntriesData exerciseLogEntry in exerciseLogEntries) {
      exerciseLogEntriesJson.add(exerciseLogEntry.toJson());
    }
    widget.logModalBloc.add(SubmitLogModalEvent(
        exerciseLogEntries: exerciseLogEntriesJson, context: context));
  }

  @override
  Widget build(BuildContext context) {
    List<ExerciseLogEntriesData>? exerciseLogEntries =
        widget.logModalState.data.exerciseLogEntries;

    final dynamic actions = [
      ActionHandler.Action(type: null, title: "CANCEL", variant: "secondary"),
      ActionHandler.Action(type: null, title: "SAVE", variant: "primary"),
    ];

    return Padding(
      padding: const EdgeInsets.only(top: 30, left: 10, right: 10, bottom: 40),
      child: SingleChildScrollView(
        child: Wrap(
          children: [
            Align(
              alignment: Alignment.centerLeft,
              child: Padding(
                padding: const EdgeInsets.only(top: 10, bottom: 5, left: 14),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.logModalState.data.title!,
                      style:
                          AuroraTheme.of(context).textStyle(TypescaleValues.H1),
                    ),
                    const SizedBox(
                      height: 5,
                    ),
                    Text(
                      widget.logModalState.data.subTitle!,
                      style: AuroraTheme.of(context)
                          .textStyle(TypescaleValues.P7, color: Colors.white54),
                    ),
                    const SizedBox(
                      height: 15,
                    )
                  ],
                ),
              ),
            ),
            if (widget.logModalState.data.exerciseLogEntries != null)
              getExerciseWidgets(exerciseLogEntries),
            const SizedBox(
              height: 20,
            ),
            Align(
              alignment: Alignment.center,
              child: SizedBox(
                width: MediaQuery.of(context).size.width * 0.8,
                child: TwinButton(
                    data: actions,
                    onPress: (action) {
                      if (action.title == "CANCEL") {
                        Navigator.pop(context);
                      } else {
                        onSubmitPress(exerciseLogEntries);
                      }
                    }),
              ),
            )
          ],
        ),
      ),
    );
  }
}

class LogExerciseExecutions extends StatefulWidget {
  ExerciseLogEntriesData exerciseLogEntry;
  // int number;

  LogExerciseExecutions({Key? key, required this.exerciseLogEntry})
      : super(key: key);

  @override
  State<LogExerciseExecutions> createState() => _LogExerciseExecutionsState();
}

class _LogExerciseExecutionsState extends State<LogExerciseExecutions> {
  final List<TextEditingController> _controllers1 = [];
  final List<TextEditingController> _controllers2 = [];

  @override
  void initState() {
    List<FpExecutionLogsData>? fpExecutionLogs =
        widget.exerciseLogEntry.fpExecutionLogs;
    // fpExecutionLogslocal = widget.exerciseLogEntry.fpExecutionLogs;
    int length1 = fpExecutionLogs != null ? fpExecutionLogs.length : 0;

    for (var i = 0; i < length1; i++) {
      _controllers1.add(TextEditingController());
      _controllers2.add(TextEditingController());
    }
    setState(() {
      _controllers1;
      _controllers2;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: fpExecutionLogsWidget(),
    );
  }

  // @override
  // void dispose() {
  //   for (final controller in _controllers1) {
  //     controller.dispose();
  //   }
  //   for (final controller in _controllers2) {
  //     controller.dispose();
  //   }
  //   super.dispose();
  // }

  addSetBtnPress() {
    List<FpExecutionLogsData>? fpExecutionLogs =
        widget.exerciseLogEntry.fpExecutionLogs;
    if (fpExecutionLogs != null && fpExecutionLogs.isNotEmpty) {
      FpExecutionLogsData newLogSet = FpExecutionLogsData.fromObject(
          fpExecutionLogs[fpExecutionLogs.length - 1]);
      fpExecutionLogs.add(newLogSet);
    } else {
      FpExecutionLogsData? newLogSet =
          FpExecutionLogsData.fromObject(widget.exerciseLogEntry.templateLog);
      fpExecutionLogs?.add(newLogSet!);
    }
    ExerciseLogEntriesData exerciseLogEntry = widget.exerciseLogEntry;
    exerciseLogEntry.fpExecutionLogs = fpExecutionLogs;
    _controllers1.add(TextEditingController());
    _controllers2.add(TextEditingController());
    setState(() {
      exerciseLogEntry:
      exerciseLogEntry;
      _controllers1;
      _controllers2;
    });
  }

  void onChanged(dynamic value, var index, bool isFirst) {
    List<FpExecutionLogsData>? fpExecutionLogs =
        widget.exerciseLogEntry.fpExecutionLogs;
    List<FpExecutionLogsData>? fpExecutionLogsNew = [];
    for (int i = 0;
        fpExecutionLogs != null && i < fpExecutionLogs.length;
        i++) {
      FpExecutionLogsData newSet =
          FpExecutionLogsData.fromObject(fpExecutionLogs[i]);
      if (i == index) {
        if (isFirst) {
          newSet.value1 = value;
        } else {
          newSet.value2 = value;
        }
      }
      fpExecutionLogsNew.add(newSet);
    }
    ExerciseLogEntriesData exerciseLogEntry = widget.exerciseLogEntry;
    exerciseLogEntry.fpExecutionLogs = fpExecutionLogsNew;

    setState(() {
      exerciseLogEntry;
    });
  }

  void onDelete(var index) {
    // widget.exerciseLogEntry.fpExecutionLogs?.removeAt(index);
    List<FpExecutionLogsData>? fpExecutionLogs =
        widget.exerciseLogEntry.fpExecutionLogs;
    List<FpExecutionLogsData>? fpExecutionLogsNew = [];
    for (int i = 0;
        fpExecutionLogs != null && i < fpExecutionLogs.length;
        i++) {
      if (i != index) {
        var len = fpExecutionLogs.length;
        FpExecutionLogsData newSet =
            FpExecutionLogsData.fromObject(fpExecutionLogs[i]);
        fpExecutionLogsNew.add(newSet);
      }
    }
    ExerciseLogEntriesData exerciseLogEntry = widget.exerciseLogEntry;
    exerciseLogEntry.fpExecutionLogs = fpExecutionLogsNew;

    if (fpExecutionLogs != null &&
        index < fpExecutionLogs.length &&
        index >= 0) {
      _controllers1.removeAt(index);
      _controllers2.removeAt(index);
    }

    setState(() {
      exerciseLogEntry:
      exerciseLogEntry;
      _controllers2;
      _controllers1;
    });
    // void rebuildAllChildren(BuildContext context) {

    // }
  }

  dynamic textControllerFunction(dynamic value, bool isFirst, int index) {
    if (isFirst) {
      _controllers1[index].text = value;
      // below line sets the cursor to end of the value
      _controllers1[index].selection = TextSelection.fromPosition(
          TextPosition(offset: _controllers1[index].text.length));
      return _controllers1[index];
    } else {
      _controllers2[index].text = value;
      // below line sets the cursor to end of the value
      _controllers2[index].selection = TextSelection.fromPosition(
          TextPosition(offset: _controllers2[index].text.length));
      return _controllers2[index];
    }
  }

  dynamic fpExecutionLogsWidget() {
    List<Widget> fpExecutionLogWidgetList = [];
    List<FpExecutionLogsData>? fpExecutionLogs =
        widget.exerciseLogEntry.fpExecutionLogs;
    for (var i = 0;
        fpExecutionLogs != null && i < fpExecutionLogs.length;
        i++) {
      fpExecutionLogWidgetList.add(Padding(
        padding:
            const EdgeInsets.only(top: 15, left: 14, right: 14, bottom: 10),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              flex: 2,
              child: Text(
                "Set " + (i + 1).toString(),
                style: AuroraTheme.of(context)
                    .textStyle(TypescaleValues.P4, color: Colors.white54),
              ),
            ),
            Expanded(
              flex: 7,
              child: Row(
                children: [
                  Container(
                    decoration: BoxDecoration(
                        border: BorderDirectional(
                            bottom: BorderSide(
                                width: 1,
                                color: Colors.white.withOpacity(0.2)))),
                    child: Row(
                      children: [
                        // InputField(
                        //   borderType: BorderType.UNDERLINE,
                        //   initialValue: widget.exerciseLogEntry.fpExecutionLogs![i].value1 ?? "",
                        //   onChanged: (value) => {
                        //     onChanged(value, i, true)
                        //   },
                        //   width: 50,
                        //   height: 40,
                        //   onCompleted: (dynamic)=>{},
                        //
                        // ),

                        SizedBox(
                          height: 25,
                          width: 50,
                          child: TextField(
                            maxLines: 1,
                            textAlign: TextAlign.center,
                            controller: textControllerFunction(
                                widget.exerciseLogEntry.fpExecutionLogs![i]
                                        .value1 ??
                                    "",
                                true,
                                i),
                            onChanged: (value) {
                              onChanged(value, i, true);
                            },
                            style: AuroraTheme.of(context).textStyle(
                                TypescaleValues.P2,
                                color: Colors.white),
                            keyboardType: const TextInputType.numberWithOptions(decimal: true),
                            inputFormatters: [
                              FilteringTextInputFormatter.allow(RegExp('[0-9.,]')),
                            ],
                            decoration: const InputDecoration(
                                isDense: true,
                                border: UnderlineInputBorder(
                                    borderSide: BorderSide.none),
                                contentPadding: EdgeInsets.zero),
                          ),
                        ),
                        Container(
                          alignment: Alignment.center,
                          height: 25,
                          child: Text(
                            fpExecutionLogs[i].unit1 ?? "",
                            textAlign: TextAlign.end,
                            style: AuroraTheme.of(context).textStyle(
                                TypescaleValues.P8,
                                color: Colors.white54),
                          ),
                        )
                      ],
                    ),
                  ),
                  const SizedBox(
                    width: 20,
                  ),
                  if (fpExecutionLogs[i].unit2 != null)
                    Text(
                      fpExecutionLogs[i].separator ?? "",
                      style: AuroraTheme.of(context)
                          .textStyle(TypescaleValues.P4, color: Colors.white54),
                    ),
                  if (fpExecutionLogs[i].unit2 != null)
                    const SizedBox(
                      width: 20,
                    ),
                  if (fpExecutionLogs[i].unit2 != null)
                    Container(
                      decoration: BoxDecoration(
                          border: BorderDirectional(
                              bottom: BorderSide(
                                  width: 1,
                                  color: Colors.white.withOpacity(0.2)))),
                      child: Row(
                        children: [
                          // InputField(
                          //   borderType: BorderType.UNDERLINE,
                          //   initialValue: widget.exerciseLogEntry.fpExecutionLogs![i].value2 ?? "",
                          //     onChanged: (value) => {
                          //       onChanged(value, i, false)
                          //     },
                          //   width: 50,
                          //   height: 40,
                          //     onCompleted: (dynamic)=>{},
                          //
                          // ),
                          SizedBox(
                            height: 25,
                            width: 50,
                            child: TextField(
                              textAlign: TextAlign.center,
                              maxLines: 1,
                              controller: textControllerFunction(
                                  widget.exerciseLogEntry.fpExecutionLogs![i]
                                          .value2 ??
                                      "",
                                  false,
                                  i),
                              onChanged: (value) {
                                onChanged(value, i, false);
                              },
                              style: AuroraTheme.of(context).textStyle(
                                  TypescaleValues.P2,
                                  color: Colors.white),
                              keyboardType: const TextInputType.numberWithOptions(decimal: true),
                              inputFormatters: [
                                FilteringTextInputFormatter.allow(RegExp('[0-9.,]')),
                              ],
                              decoration: const InputDecoration(
                                  contentPadding: EdgeInsets.zero,
                                  isDense: true,
                                  border: UnderlineInputBorder(
                                      borderSide: BorderSide.none)),
                            ),
                          ),
                          Container(
                            alignment: Alignment.center,
                            child: Text(
                              fpExecutionLogs[i].unit2 ?? "",
                              style: AuroraTheme.of(context).textStyle(
                                  TypescaleValues.P8,
                                  color: Colors.white54),
                            ),
                          )
                        ],
                      ),
                    )
                ],
              ),
            ),
            Expanded(
              flex: 1,
              child: InkWell(
                  onTap: () => onDelete(i),
                  child: Padding(
                    padding: const EdgeInsets.only(top: 5, bottom: 5),
                    child: Icon(
                      CFIcons.delete_bin,
                      color: ColorPalette.white80,
                      size: scale(context, 16),
                    ),
                  )),
            )
          ],
        ),
      ));
    }
    fpExecutionLogWidgetList.add(InkWell(
      onTap: () => addSetBtnPress(),
      child: Padding(
        padding: EdgeInsets.only(left: 14, top: 15),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Icon(
              CFIcons.add,
              color: ColorPalette.white,
              size: scale(context, 18),
            ),
            SizedBox(
              width: 5,
            ),
            Text(
              "ADD SET",
              style: AuroraTheme.of(context)
                  .textStyle(TypescaleValues.P3, color: Colors.white),
            )
          ],
        ),
      ),
    ));
    return fpExecutionLogWidgetList;
  }
}

import 'dart:ui';
import 'package:aitrainer/blocs/replace_modal/replace_bloc.dart';
import 'package:aitrainer/blocs/replace_modal/replace_event.dart';
import 'package:aitrainer/blocs/replace_modal/replace_state.dart';
import 'package:common/ui/alert_view.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/theme/Colors.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:common/ui/widget_factory.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/src/bloc_consumer.dart';
import 'package:flutter_bloc/src/bloc_provider.dart';
import 'package:flutter_bloc/src/repository_provider.dart';
import 'package:flutter_bloc/src/bloc_listener.dart';
import 'package:flutter_bloc/src/bloc_builder.dart';

import '../../blocs/guided_screen/guided_bloc.dart';

class ReplaceModal extends StatefulWidget {
  GuidedPageBloc guidedPageBloc;
  ReplaceModalBloc replaceModalBloc;
  String modalTitle;
  int exerciseEntryId;
  String? cycleDay;
  String? tenantId;
  String? microCycleSequence;

  ReplaceModal(
      {Key? key,
      required this.guidedPageBloc,
      required this.replaceModalBloc,
      required this.modalTitle,
      required this.exerciseEntryId,
      this.cycleDay,
      this.tenantId,
      this.microCycleSequence})
      : super(key: key);

  @override
  State<ReplaceModal> createState() => _ReplaceModalState();
}

class _ReplaceModalState extends State<ReplaceModal>
    with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance?.addObserver(this);
    WidgetsBinding.instance?.addPostFrameCallback((timeStamp) {
      refresh(context: context);
    });
  }

  refresh({required BuildContext context}) {
    // final replaceModalBloc = BlocProvider.of<ReplaceModalBloc>(context);

    widget.replaceModalBloc
        .add(LoadReplaceModalEvent(true, widget.exerciseEntryId));
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<ReplaceModalBloc, ReplaceModalState>(
        bloc: widget.replaceModalBloc,
        listener: (context, state) {
          if (state is ReplaceModalFailed) {
            showErrorAlert(
                context: context,
                title: state.error,
                onClose: () {
                  Navigator.pop(context);
                });
          }
        },
        child: BlocBuilder<ReplaceModalBloc, ReplaceModalState>(
            bloc: widget.replaceModalBloc, builder: buildWidgets));
    // return Container(height: 300,);
  }

  getReplacementExercise(dynamic data, dynamic guidedPageBloc) {
    List<Widget> widgets = [];
    dynamic widgetList = data?.widgets;
    WidgetFactory widgetFactory = RepositoryProvider.of<WidgetFactory>(context);

    for (var i = 0; i < widgetList.length; i++) {
      var widgetItem = widgetList[i];
      widgetItem['bloc'] = guidedPageBloc;
      var newWidget = widgetFactory.createWidget({
        ...widgetItem,
        "cycleDay": widget.cycleDay,
        "tenantId": widget.tenantId,
        "microCycleSequence": widget.microCycleSequence
      });
      if (newWidget != null) {
        widgets.add(newWidget);
      }
    }
    return widgets;
    // return [Container(height: 300, decoration: BoxDecoration(color: ColorPalette.errorRed),)];
  }

  Widget buildWidgets(context, state) {
    if (state is ReplaceModalLoading && state.showLoader) {
      return const FancyLoadingIndicator();
    } else if (state is ReplaceModalLoaded) {
      return Padding(
        padding: const EdgeInsets.only(left: 10, top: 10, bottom: 10),
        child: Column(children: [
          Align(
            alignment: Alignment.centerLeft,
            child: Padding(
              padding: const EdgeInsets.only(top: 10, bottom: 5, left: 14),
              child: Text(
                widget.modalTitle,
                textAlign: TextAlign.left,
                // style: const TextStyle(color: Colors.white, fontSize: 20, backgroundColor: Colors.yellow),
                style: AuroraTheme.of(context).textStyle(TypescaleValues.H2),
              ),
            ),
          ),
          ...getReplacementExercise(state.data, widget.guidedPageBloc)
        ]),
      );
      // return Container(height: 500,decoration: BoxDecoration(color: ColorPalette.cureFitPink),);
    }
    return Container();
  }
}

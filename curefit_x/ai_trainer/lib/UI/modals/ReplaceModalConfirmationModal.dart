import 'package:aitrainer/blocs/guided_screen/guided_bloc.dart';
import 'package:aitrainer/blocs/guided_screen/guided_event.dart';
import 'package:aitrainer/blocs/replace_modal/replace_bloc.dart';
import 'package:aitrainer/blocs/replace_modal/replace_event.dart';
import 'package:common/ui/molecules/bottom_tray.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/ui_toolkit.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class ReplaceModalConfirmationModal extends StatefulWidget {
  final GuidedPageBloc guidedPageBloc;
  final dynamic modalData;

  const ReplaceModalConfirmationModal({
    Key? key,
    required this.guidedPageBloc,
    this.modalData,
  }) : super(key: key);
  @override
  State<ReplaceModalConfirmationModal> createState() =>
      _ReplaceModalConfirmationModalState();
}

class _ReplaceModalConfirmationModalState
    extends State<ReplaceModalConfirmationModal> {
  handleTap(dynamic action) {
    print(widget.modalData!['oldExerciseId']);
    widget.guidedPageBloc.add(LoadReplaceSubmitEvent(
        true,
        widget.modalData!['oldExerciseId'],
        widget.modalData!['exerciseId'],
        action!['meta']!['alternativeType'],
        action!['meta']!['userWodId'],
        action!['meta']!['updateMesocycle'],
        widget.modalData!['cycleDay'],
        widget.modalData!['tenantId'],
        widget.modalData!['microCycleSequence'],
        context));
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> renderAction() {
      List<Widget> widgets = [];
      if (widget.modalData!['actions'] != null) {
        widget.modalData?['actions'].forEach((var action) => {
              widgets.add(Expanded(
                  flex: 1,
                  child: InkWell(
                    onTap: () => handleTap(action),
                    child: Text(
                      action?['title'] ?? "",
                      textAlign: TextAlign.center,
                      style: AuroraTheme.of(context).textStyle(
                        TypescaleValues.H4,
                      ),
                    ),
                  )))
            });
      }
      return widgets;
    }

    print(widget.modalData);
    return Padding(
      padding: EdgeInsets.only(top: 20, left: 25, right: 25, bottom: 60),
      child: Column(
        children: [
          Text(widget.modalData?['title'] ?? "",
              style: AuroraTheme.of(context).textStyle(TypescaleValues.H1)),
          SizedBox(
            height: scale(context, 30),
          ),
          Row(
            children: renderAction(),
          ),
        ],
      ),
    );
  }
}

import 'dart:convert';

import 'package:aitrainer/UI/widgets/button.dart';
import 'package:aitrainer/blocs/post_checkin_modal/post_checkin_bloc.dart';
import 'package:aitrainer/blocs/post_checkin_modal/post_checkin_model.dart';
import 'package:common/action/action_handler.dart';
import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/action/action_handler.dart' as action_handler;
import 'package:common/constants/action_constants.dart';
import 'package:common/font/cf_icons.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/model/error_prop_model.dart';
import 'package:common/ui/alert_view.dart';
import 'package:common/ui/atoms/action_button.dart';
import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/atoms/radio_button.dart';
import 'package:common/ui/atoms/secondary_button.dart';
import 'package:common/ui/atoms/single_select_chips.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/organisms/titlebar/classic_title_bar.dart';
import 'package:common/ui/screens/error_screen.dart';
import 'package:common/ui/theme/Colors.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/widget_factory.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:flutter/material.dart' hide Action;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:video_player_cf/video_player.dart';

import '../../blocs/weekly_streak/weekly_streak_bloc.dart';
import '../../blocs/weekly_streak/weekly_streak_event.dart';
import '../../blocs/weekly_streak/weekly_streak_model.dart';
import '../../blocs/weekly_streak/weekly_streak_state.dart';
import '../../constants/constants.dart';

class PostCheckinModal extends StatefulWidget {
  const PostCheckinModal({Key? key}) : super(key: key);
  @override
  _PostCheckinScreenState createState() => _PostCheckinScreenState();
}

class _PostCheckinScreenState extends State<PostCheckinModal>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  late ScrollController _scrollController;
  late VideoPlayerController _videoPlayerController;
  bool isCountInOutForPlan = false;
  bool updatingOptInAndOut = false;
  late PostCheckinData data;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _videoPlayerController = VideoPlayerController(
        videoPlayerOptions: VideoPlayerOptions(mixWithOthers: true));
    _videoPlayerController.setAssetDataSource('assets/check-in-success.mp3');
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      refresh(context: context, showLoader: true);
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _videoPlayerController.dispose();
    super.dispose();
  }

  bool checkSmallDevice(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    double height = MediaQuery.of(context).size.height;
    if (height * 0.54 <= width) {
      return true;
    } else {
      return false;
    }
  }

  refresh({required BuildContext context, bool showLoader = false}) {
    final ScreenArguments? args =
        ModalRoute.of(context)!.settings.arguments as ScreenArguments?;
    var params = args?.params;
    String? trainerId = params?["trainerId"]?.toString();
    String? centerId = params?["centerId"]?.toString();
    String? isPtTrainer = params?["isPtTrainer"]?.toString();
    final postCheckinBlock = BlocProvider.of<PostCheckinBloc>(context);

    postCheckinBlock.add(FetchPostCheckinDataEvent(
        trainerId: trainerId, centerId: centerId, isPtTrainer: isPtTrainer));
  }

  onBackPress() {
    if (Navigator.canPop(context)) {
      Navigator.pop(context);
    } else if (!Navigator.canPop(context)) {
      ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
      actionBloc.add(CloseApplicationEvent(shouldReset: false));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      body: Stack(
        children: [
          Aurora(size: MediaQuery.of(context).size, context: context),
          Positioned.fill(child: buildWidgets()),
          BlocBuilder<PostCheckinBloc, PostCheckinState>(
            builder: (context, state) {
              if (state is PostCheckingLoading && state.showLoader) {
                return const FancyLoadingIndicator();
              }
              return Container();
            },
          ),
          Positioned(
            right: 10.0,
            top: 50.0,
            child: IconButton(
              icon: const Icon(
                Icons.close,
                size: 25,
                color: Colors.white,
              ),
              onPressed: () {
                onBackPress();
              },
            ),
          )
        ],
      ),
    );
  }

  markPageViewed() {
    final ScreenArguments? args =
        ModalRoute.of(context)!.settings.arguments as ScreenArguments?;
    var params = args?.params;
    String? pageName = params?["pageName"]?.toString();
    bool singleTimeView = pageName == "First Checkin Post plan creation";

    if (singleTimeView) {
      ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
      UnsupportedActionEvent event = UnsupportedActionEvent(
          Action(type: ActionTypes.GYM_POST_CHECKIN_PAGE_VIEWED), false);
      actionBloc.add(event);
    }
  }

  Widget buildWidgets() {
    return BlocListener<PostCheckinBloc, PostCheckinState>(
      listener: (context, state) {
        if (state is PostCheckinFailed) {
          showErrorAlert(
              context: context,
              title: state.error,
              onClose: () {
                // Navigator.pop(context);
              });
        }
        if (state is PostCheckinLoaded) {
          _videoPlayerController?.play();
          markPageViewed();
          setState(() {
            data = state.data;
            isCountInOutForPlan = (state.data?.bottomAction?.meta?.countIn ??
                    state.data?.bottomAction?.meta?.countOut) ??
                false;
          });
        }

        if (state is OptInOutLoading && state.showLoader) {
          setState(() {
            updatingOptInAndOut = true;
          });
        }

        if (state is OptInOutLoaded && state.showLoader == false) {
          setState(() {
            updatingOptInAndOut = false;
          });
        }
      },
      child: BlocBuilder<PostCheckinBloc, PostCheckinState>(
          builder: (context, state) {
        bool smallDevice = checkSmallDevice(context);
        if (state is PostCheckinLoaded ||
            state is OptInOutLoading ||
            state is OptInOutLoaded) {
          return Padding(
              padding: EdgeInsets.only(top: smallDevice ? 0 : Spacings.x6),
              child: Column(
                children: data.topImageUrl != null
                    ? [
                        getTopView(data.topImageUrl!),
                        getBodyView(
                            data.body, data.topImageUrl != null ? true : false),
                        data.trainerDetails != null
                            ? getBottomView(data.trainerDetails!)
                            : Container(),
                        data.bottomAction != null
                            ? getFooter(data.bottomAction!)
                            : Container()
                      ]
                    : [
                        data.trainerDetails != null
                            ? getTopTrainerImage(data.trainerDetails!)
                            : Container(),
                        getBodyView(
                            data.body, data.topImageUrl != null ? true : false),
                        data.bottomAction != null
                            ? getFooter(data.bottomAction!)
                            : Container()
                      ],
              ));
        }
        if (state is PostCheckinFailed) {
          return ErrorScreen(errorInfo: UnknownError(callbackFunction: () {
            refresh(context: context);
          }));
        }
        return Container();
      }),
    );
  }

  getTopView(String url) {
    return Align(
        alignment: Alignment.center,
        child: SizedBox(
            width: MediaQuery.of(context).size.width *
                (checkSmallDevice(context) ? 0.75 : 0.90),
            height: MediaQuery.of(context).size.width *
                (checkSmallDevice(context) ? 0.75 : 0.90),
            child: Image.network(getMediaUrl(url), fit: BoxFit.fill)));
  }

  getBodyView(Body body, bool isTopImage) {
    return Align(
        alignment: Alignment.center,
        child: Column(children: [
          Padding(
              padding:
                  EdgeInsets.only(top: isTopImage ? Spacings.x1 : Spacings.x6)),
          Text(
            body.text ?? "",
            textAlign: TextAlign.center,
            style: AuroraTheme.of(context)
                .textStyle(TypescaleValues.P5, color: ColorPalette.white40),
          ),
          const Padding(padding: EdgeInsets.only(top: Spacings.x2)),
          Padding(
              padding: const EdgeInsets.symmetric(
                vertical: 0,
                horizontal: Spacings.x4,
              ),
              child: Text(
                body.body ?? "",
                textAlign: TextAlign.center,
                style: AuroraTheme.of(context)
                    .textStyle(TypescaleValues.H8, color: ColorPalette.white),
              )),
          const Padding(padding: EdgeInsets.only(top: Spacings.x5)),
          body.action != null
              ? SizedBox(
                  width: MediaQuery.of(context).size.width * 0.53,
                  child: SecondaryButton(
                    () {
                      ActionBloc actionBloc =
                          BlocProvider.of<ActionBloc>(context);
                      String? url = body.action?.url;
                      Action myAction =
                          Action(type: body.action?.action, url: url);
                      PerformActionEvent event = PerformActionEvent(Action(
                          type: ActionTypes.POP_THEN_NAVIGATE,
                          meta: {"action": myAction}));
                      actionBloc.add(event);
                    },
                    body.action?.title ?? body.action?.text ?? "",
                    buttonType: SecondaryButtonType.SMALL,
                    textColor: Colors.white,
                    rightIconData: CFIcons.chevron_right,
                    iconSize: 10,
                  ))
              : Container()
        ]));
  }

  getBottomView(TrainerDetails trainerDetails) {
    return Column(children: [
      const Padding(padding: EdgeInsets.only(top: Spacings.x8)),
      Container(
        width: MediaQuery.of(context).size.width * 0.6,
        decoration: BoxDecoration(
          border: Border.all(width: 0.5, color: ColorPalette.white20),
        ),
      ),
      const Padding(padding: EdgeInsets.only(top: Spacings.x5)),
      Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
              width: 70,
              height: 70,
              clipBehavior: Clip.hardEdge,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(35),
              ),
              child: Image.network(trainerDetails.image!, fit: BoxFit.fill)),
          const Padding(padding: EdgeInsets.only(left: 20.0)),
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                trainerDetails.post,
                style: AuroraTheme.of(context)
                    .textStyle(TypescaleValues.P6, color: ColorPalette.white60),
              ),
              const Padding(padding: EdgeInsets.only(top: Spacings.x1)),
              Text(
                trainerDetails.name,
                style: AuroraTheme.of(context)
                    .textStyle(TypescaleValues.H2, color: ColorPalette.white80),
              ),
              const Padding(padding: EdgeInsets.only(top: Spacings.x1)),
              Text(
                trainerDetails.experience ?? "",
                style: AuroraTheme.of(context)
                    .textStyle(TypescaleValues.P8, color: ColorPalette.white40),
              )
            ],
          )
        ],
      ),
    ]);
  }

  getFooter2(BottomAction action) {
    List<String> text = action?.meta?.text ?? [action.text ?? action.title!];
    return Expanded(
        flex: 1,
        child: Align(
            alignment: Alignment.bottomCenter,
            child: Padding(
                padding: const EdgeInsets.only(bottom: Spacings.x6),
                child: SizedBox(
                    width: MediaQuery.of(context).size.width * 0.9,
                    child: TrainerButton(
                      () {
                        final postCheckinBlock =
                            BlocProvider.of<PostCheckinBloc>(context);
                        if (action?.meta?.countIn == true) {
                          postCheckinBlock.add(OptInAndOptOutPlanEvent(
                              isOptedOut: !isCountInOutForPlan,
                              url: action?.url ?? ""));
                        } else if (action?.meta?.countOut == true) {
                          postCheckinBlock.add(OptInAndOptOutPlanEvent(
                              isOptedOut: isCountInOutForPlan,
                              url: action.url ?? ""));
                        }
                        isCountInOutForPlan = !isCountInOutForPlan;
                        setState(() {});
                      },
                      text: text,
                      buttonType: TrainerButtonType.BIG,
                      enabled: !updatingOptInAndOut,
                      textColor: Colors.white,
                      rightIconData: isCountInOutForPlan == false
                          ? CFIcons.check_circle
                          : null,
                      emptyCircle: isCountInOutForPlan == true,
                      borderRadius: 10,
                      iconSize: 20,
                      height: 80,
                    )))));
  }

  getFooter(BottomAction action) {
    if (action?.meta?.action == "COUNT_IN_OUT") {
      return getFooter2(action);
    }
    List<String> text = action?.meta?.text ?? [action.text ?? action.title!];
    return Expanded(
        flex: 1,
        child: Align(
            alignment: Alignment.bottomCenter,
            child: Padding(
                padding: const EdgeInsets.only(bottom: Spacings.x6),
                child: SizedBox(
                    width: MediaQuery.of(context).size.width * 0.9,
                    child: TrainerButton(
                      () {
                        ActionBloc actionBloc =
                            BlocProvider.of<ActionBloc>(context);
                        PerformActionEvent event = PerformActionEvent(Action(
                            type: ActionTypes.PUSH_AND_NAVIGATION,
                            url: action.url));
                        actionBloc.add(event);
                      },
                      text: text,
                      buttonType: TrainerButtonType.SMALL,
                      textColor: Colors.white,
                      rightIconData: CFIcons.chevron_right,
                      iconData: CFIcons.clock_tick,
                      borderRadius: 10,
                      iconSize: 20,
                      rightIconSize: 15,
                      height: 70,
                      rightIconUrl: action?.meta?.rightIconUrl,
                      leftIconUrl: action?.meta?.leftIconUrl,
                    )))));
  }

  getTopTrainerImage(TrainerDetails trainerDetails) {
    var width = MediaQuery.of(context).size.width * 0.5;
    var height = MediaQuery.of(context).size.width * 0.5;
    return Stack(
      children: [
        Align(
            alignment: Alignment.center,
            child: CFNetworkImage(
              imageUrl: getImageUrl(context,
                  imagePath: trainerDetails.backgroundImage!),
              fit: BoxFit.fill,
              width: MediaQuery.of(context).size.width,
              height: MediaQuery.of(context).size.width,
              errorWidget: (context, url, error) => const Icon(Icons.error),
            )),
        Positioned(
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            child: Align(
              alignment: Alignment.center,
              child: Container(
                  width: width,
                  height: height,
                  clipBehavior: Clip.hardEdge,
                  decoration: BoxDecoration(
                    border: Border.all(width: 5, color: ColorPalette.white25),
                    borderRadius: BorderRadius.circular(width / 2),
                  ),
                  child: Container(
                      width: width,
                      height: height,
                      clipBehavior: Clip.hardEdge,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(width / 2),
                      ),
                      child: CFNetworkImage(
                        imageUrl: getImageUrl(context,
                            imagePath: trainerDetails.image!),
                        fit: BoxFit.contain,
                        errorWidget: (context, url, error) =>
                            const Icon(Icons.error),
                      ))),
            )),
        Positioned(
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            child: Align(
              alignment: Alignment.center,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Padding(padding: EdgeInsets.only(top: height + 70)),
                  Text(
                    trainerDetails.post,
                    style: AuroraTheme.of(context).textStyle(TypescaleValues.P6,
                        color: ColorPalette.white60),
                  ),
                  const Padding(padding: EdgeInsets.only(top: Spacings.x1)),
                  Text(
                    trainerDetails.name,
                    style: AuroraTheme.of(context).textStyle(TypescaleValues.H2,
                        color: ColorPalette.white80),
                  ),
                  const Padding(padding: EdgeInsets.only(top: Spacings.x1)),
                  Text(
                    trainerDetails.experience ?? "",
                    style: AuroraTheme.of(context).textStyle(TypescaleValues.P8,
                        color: ColorPalette.white40),
                  )
                ],
              ),
            )),
      ],
    );
  }
}

import 'dart:async';
import 'dart:math';

import 'package:aitrainer/blocs/search_add_exercise/add_exercise_modal.dart';
import 'package:aitrainer/blocs/search_add_exercise/search_add_exercise_bloc.dart';
import 'package:aitrainer/constants/constants.dart';
import 'package:common/action/action_handler.dart' as action_handler;
import 'package:common/action/action_handler.dart';
import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/constants/action_constants.dart';
import 'package:common/font/cf_icons.dart';
import 'package:common/image/image_url_generator.dart';
import 'package:common/model/classic_thumbnail_listview_model.dart';
import 'package:common/model/error_prop_model.dart';
import 'package:common/model/muscle_focus_model.dart';
import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/components/cf_network_image.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/screens/error_screen.dart';
import 'package:common/ui/theme/Spacing.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/util/color.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:flutter/material.dart' hide Action;
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

class SearchExerciseScreen extends StatefulWidget {
  const SearchExerciseScreen({Key? key}) : super(key: key);

  @override
  State<SearchExerciseScreen> createState() => _SearchExerciseScreenState();
}

class _SearchExerciseScreenState extends State<SearchExerciseScreen>
    with WidgetsBindingObserver {
  List<Exercise> exercises = [];
  List<Exercise> selectedExercise = [];
  Timer? _debounce;
  var screenParams;
  PageFrom _pageFrom = PageFrom.AI_TRAINER;
  String? sessionId;

  @override
  void initState() {
    super.initState();

    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      refresh(context: context);
    });
  }

  @override
  void dispose() {
    _debounce?.cancel();
    super.dispose();
  }

  refresh({required BuildContext context}) {
    final ScreenArguments? args =
        ModalRoute.of(context)!.settings.arguments as ScreenArguments?;
    var params = args?.params;
    if (params != null) {
      screenParams = params;
    }
    String? sectionHeader =
        screenParams?["sectionHeader"]?.toString() ?? 'WARMUP';

    String? sessionId = screenParams?["sessionId"];
    String? date = screenParams?["date"];
    String? appliedFilters = screenParams?["appliedFilters"];

    String? page = screenParams?["pageFrom"];
    if (page != null) {
      _pageFrom = EnumToString.fromString(PageFrom.values, page)!;
    }

    appliedFilters = appliedFilters?.replaceAll('[', '').replaceAll(']', '');

    // Split by comma, trim whitespace, and convert to the correct case
    List<String> appliedFiltersList =
        (appliedFilters != null && appliedFilters != "")
            ? appliedFilters.split(',').map((e) => e.trim()).toList()
            : [];

    // Capitalize the first letter and lowercase the rest
    appliedFiltersList = appliedFiltersList.map((e) {
      String formatted = e.replaceAll("Muscle.", '');
      return formatted[0].toUpperCase() + formatted.substring(1).toLowerCase();
    }).toList();

    appliedFiltersList = appliedFiltersList.map((e) {
      if (e == "Full_body") {
        return "Full Body";
      }
      return e;
    }).toList();

    // Pass the correctly formatted appliedFiltersList to the event
    final searchAddExerciseBloc =
        BlocProvider.of<SearchAddExerciseBloc>(context);
    searchAddExerciseBloc.add(FetchExerciseToAddEvent(
        searchText: "",
        headerText: sectionHeader,
        appliedFilters: appliedFiltersList,
        sessionId: sessionId,
        date: date,
        pageFrom: EnumToString.convertToString(_pageFrom)));
  }

  search(
      {required BuildContext context,
      required String text,
      required List<String> appliedFilters}) {
    String? sectionHeader =
        screenParams?["sectionHeader"]?.toString() ?? 'WARMUP';
    String? sessionId = screenParams?["sessionId"];
    String? date = screenParams?["date"];
    final searchAddExerciseBloc =
        BlocProvider.of<SearchAddExerciseBloc>(context);
    searchAddExerciseBloc.add(FetchExerciseToAddOnSearchEvent(
        searchText: searchController.text,
        headerText: sectionHeader,
        appliedFilters: appliedFilters,
        sessionId: sessionId,
        date: date,
        pageFrom: EnumToString.convertToString(_pageFrom)));
  }

  selectFilter(
      {required BuildContext context, required List<String> appliedFilters}) {
    String? sectionHeader =
        screenParams?["sectionHeader"]?.toString() ?? 'WARMUP';
    String? sessionId = screenParams?["sessionId"];
    String? date = screenParams?["date"];
    final searchAddExerciseBloc =
        BlocProvider.of<SearchAddExerciseBloc>(context);
    searchAddExerciseBloc.add(FetchExerciseToAddOnSearchEvent(
        searchText: searchController.text,
        headerText: sectionHeader,
        appliedFilters: appliedFilters,
        date: date,
        showLoader: true,
        sessionId: sessionId,
        pageFrom: EnumToString.convertToString(_pageFrom)));
  }

  addExercise({required BuildContext context}) {
    String sectionHeader = screenParams?["sectionHeader"]?.toString() ?? '';
    String wodID = screenParams?["wodId"] ?? "0";
    int wodId = int.parse(wodID);
    final addExercise = BlocProvider.of<SearchAddExerciseBloc>(context);
    addExercise.add(AddExerciseToUserWodEvent(
        wodId: wodId,
        shouldChangeAllSameWods: false,
        sectionHeader: sectionHeader,
        exercise: selectedExercise));
  }

  Widget renderBackButton() {
    return SizedBox(
        width: Spacings.x9,
        height: Spacings.x9,
        child: IconButton(
            onPressed: () {
              // handle nav to close flutter event
              if (Navigator.canPop(context)) {
                Navigator.pop(context);
              } else if (!Navigator.canPop(context)) {
                ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
                actionBloc.add(CloseApplicationEvent(shouldReset: false));
              }
            },
            icon: const Icon(
              CFIcons.chevron_left,
              color: Colors.white,
              size: 22,
              semanticLabel: "chevron_left",
            )));
  }

  showAddExerciseModal() {
    SearchAddExerciseBloc searchAddExerciseBloc =
        BlocProvider.of<SearchAddExerciseBloc>(context);

    showDialog(
        useSafeArea: false,
        context: context,
        builder: (builder) {
          return BlocProvider<SearchAddExerciseBloc>.value(
              value: searchAddExerciseBloc, //
              child:
                  BlocConsumer<SearchAddExerciseBloc, SearchAddExerciseState>(
                      buildWhen: (context, state) {
                return state is ForceRenderDialogBox ||
                    state is AddingExercise ||
                    state is AddingExerciseComplete;
              }, listenWhen: (context, state) {
                return state is ForceRenderDialogBox ||
                    state is ExerciseAddedSuccess;
              }, listener: (context, state) {
                if (state is ExerciseAddedSuccess) {
                  ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
                  UnsupportedActionEvent event = UnsupportedActionEvent(
                      action_handler.Action(
                          type: ActionTypes.REFETCH_USER_WORKOUT_PLAN),
                      false);
                  actionBloc.add(event);
                  onBackPress();
                  onBackPress();
                }
                if (state is ForceRenderDialogBox) {
                  if (selectedExercise.isEmpty) {
                    onBackPress();
                  }
                }
              }, builder: (context, state) {
                return SizedBox(
                    height: 300,
                    child: Scaffold(
                        backgroundColor:
                            canvasBackgroundColor(CanvasTheme.CLASSIC),
                        appBar: AppBar(
                          leading: renderBackButton(),
                          centerTitle: false,
                          titleSpacing: -5,
                          title: Text(
                            "Adding ${selectedExercise.length} ${selectedExercise.length > 1 ? "exercises" : "exercise"}",
                            style: AuroraTheme.of(context)
                                .textStyle(TypescaleValues.H1),
                          ),
                        ),
                        floatingActionButton: SizedBox(
                            width: MediaQuery.of(context).size.width -
                                Spacings.x4 * 2,
                            child: PrimaryButton(
                              () {
                                if (state is AddingExercise) {
                                  return;
                                }
                                addExercise(context: context);
                                // int count = 0;
                                // Navigator.of(context).popUntil((_) => count++ >= 2);
                              },
                              "CONFIRM",
                              enabled: selectedExercise.isNotEmpty,
                            )),
                        body: Stack(children: [
                          Aurora(
                            size: MediaQuery.of(context).size,
                            context: context,
                          ),
                          Column(
                            children: [
                              Expanded(
                                  child: Padding(
                                      padding: const EdgeInsets.only(
                                          // top: MediaQuery.of(context)
                                          //         .size
                                          //         .height *
                                          //     .1,
                                          left: Spacings.x4,
                                          right: Spacings.x4),
                                      child: ListView(
                                          padding: EdgeInsets.zero,
                                          // controller: _scrollController,
                                          children: <Widget>[
                                            const SizedBox(height: 15),
                                            exerciseAddedListWidget(),
                                          ]))),
                            ],
                          ),
                          state is AddingExercise
                              ? const Positioned(
                                  top: 0,
                                  left: 0,
                                  right: 0,
                                  bottom: 0,
                                  child: Center(
                                    child: FancyLoadingIndicator(),
                                  ))
                              : Container()
                        ])));
              }));
        });
  }

  onBackPress() {
    if (Navigator.canPop(context)) {
      Navigator.pop(context);
    } else if (!Navigator.canPop(context)) {
      ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
      actionBloc.add(CloseApplicationEvent(shouldReset: false));
    }
  }

  TextEditingController searchController = TextEditingController();
  bool isRemoveIconVisible = false;

  @override
  Widget build(BuildContext context) {
    final searchAddExerciseBloc =
        BlocProvider.of<SearchAddExerciseBloc>(context);

    return Scaffold(
        extendBodyBehindAppBar: true,
        backgroundColor: canvasBackgroundColor(CanvasTheme.CLASSIC),
        appBar: AppBar(
          leading: renderBackButton(),
          centerTitle: false,
          titleSpacing: -2,
          title: Text(
            (_pageFrom != null && _pageFrom != PageFrom.AI_TRAINER)
                ? "Add and Log Exercises"
                : "Select exercises to add",
            style: AuroraTheme.of(context).textStyle(TypescaleValues.H1),
          ),
        ),
        floatingActionButton: selectedExercise.isNotEmpty
            ? SizedBox(
                width: MediaQuery.of(context).size.width - 40,
                child: PrimaryButton(
                  () {
                    showAddExerciseModal();
                  },
                  "ADD ${selectedExercise.length} ${selectedExercise.length > 1 ? "EXERCISES" : "EXERCISE"}",
                  enabled: selectedExercise.isNotEmpty,
                ))
            : Container(),
        body: BlocProvider<SearchAddExerciseBloc>.value(
            value: searchAddExerciseBloc, //
            child: BlocConsumer<SearchAddExerciseBloc, SearchAddExerciseState>(
                listener: (context, state) {
              if (state is SearchAddExerciseLoaded) {
                setState(() {
                  exercises = state.data.exercise?.map((e) {
                        List<Exercise> alreadyAdded = selectedExercise
                            .where(
                                (element) => element.exerciseId == e.exerciseId)
                            .toList();
                        if (alreadyAdded.isNotEmpty) {
                          e.isSelected = true;
                        }
                        return e;
                      }).toList() ??
                      [];
                });
              }
              if (state is SearchAddExerciseFailed) {
                exercises = [];
              }
            }, buildWhen: (context, state) {
              if (state is SearchAddExerciseLoaded ||
                  state is SearchAddExerciseLoading ||
                  state is SearchAddExerciseFailed) {
                return true;
              }
              return false;
            }, builder: (context, state) {
              if (state is SearchAddExerciseLoaded) {
                return Stack(children: [
                  Aurora(
                    size: MediaQuery.of(context).size,
                    context: context,
                  ),
                  Column(children: [
                    Expanded(
                        child: Padding(
                            padding: EdgeInsets.only(
                                top: MediaQuery.of(context).size.height * 0.13),
                            child: ListView(
                                padding: EdgeInsets.zero,
                                // controller: _scrollController,
                                children: <Widget>[
                                  Padding(
                                      padding: const EdgeInsets.only(
                                          top: Spacings.x1,
                                          left: Spacings.x4,
                                          right: Spacings.x4),
                                      child: searchBarWidget(state)),
                                  const SizedBox(height: 15),
                                  Padding(
                                      padding: const EdgeInsets.only(
                                          left: Spacings.x4),
                                      child: chipWidget(context, state)),
                                  const SizedBox(height: 15),
                                  Padding(
                                      padding: const EdgeInsets.only(
                                          left: Spacings.x4,
                                          right: Spacings.x4),
                                      child: exerciseListWidget(
                                          state
                                              .data.activityStoreAttributeEntry,
                                          state)),
                                ])))
                  ]),
                ]);
              } else if (state is SearchAddExerciseLoading) {
                return const FancyLoadingIndicator();
              }
              if (state is SearchAddExerciseFailed) {
                return ErrorScreen(
                    errorInfo: UnknownError(callbackFunction: () {
                  refresh(context: context);
                }));
              } else {
                return Container();
              }
            })));
  }

  Widget searchBarWidget(SearchAddExerciseLoaded state) {
    return Row(children: [
      Expanded(
          child: Container(
        height: 42,
        decoration: BoxDecoration(
            color: Colors.white10, borderRadius: BorderRadius.circular(5)),
        child: TextField(
          style: AuroraTheme.of(context).textStyle(TypescaleValues.P2),
          controller: searchController,
          textAlignVertical: TextAlignVertical.center,
          decoration: InputDecoration(
            prefixIcon: const Icon(
              CFIcons.search_icon,
              size: 20,
              color: Colors.white,
            ),
            hintStyle: AuroraTheme.of(context).textStyle(TypescaleValues.P5,
                color: Colors.white.withOpacity(0.6)),
            hintText: "Search exercises",
            contentPadding: const EdgeInsets.only(left: 8, bottom: 8),
            suffixIcon: Visibility(
              visible: isRemoveIconVisible,
              child: IconButton(
                onPressed: () {
                  searchController.text = "";
                  isRemoveIconVisible = false;
                  setState(() {});
                  search(
                      context: context,
                      text: "",
                      appliedFilters: state.data.appliedFilters);
                },
                icon: const Icon(Icons.close, color: Colors.white),
              ),
            ),
            border: InputBorder.none,
          ),
          onChanged: (String? text) {
            text == ""
                ? isRemoveIconVisible = false
                : isRemoveIconVisible = true;
            if ((_debounce?.isActive ?? false) && text != "") {
              _debounce?.cancel();
            } else {
              search(
                  context: context,
                  text: text!.replaceAll("\n", "") ?? "",
                  appliedFilters: state.data.appliedFilters);
              _debounce = Timer(const Duration(milliseconds: 500), () {});
            }
          },
        ),
      ))
    ]);
  }

  Widget chipWidget(BuildContext context, SearchAddExerciseLoaded state) {
    return SizedBox(
      height: 50,
      child: ListView(
          scrollDirection: Axis.horizontal,
          padding: EdgeInsets.zero,
          shrinkWrap: true,
          children: List.generate(state.data.applicableFilters.length, (index) {
            String filterName = state.data.applicableFilters[index];
            bool isSelected = state.data.appliedFilters.contains(filterName);
            return Row(
              children: [
                GestureDetector(
                    onTap: () {
                      List<String> appliedFilters = state.data.appliedFilters;
                      if (isSelected) {
                        appliedFilters.remove(filterName);
                      } else {
                        appliedFilters.add(filterName);
                      }
                      selectFilter(
                          context: context, appliedFilters: appliedFilters);
                    },
                    child: Container(
                        height: 35,
                        constraints: const BoxConstraints(minWidth: 100),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(30),
                            color:
                                isSelected ? Colors.white : Colors.transparent,
                            border: Border.all(
                                color: isSelected
                                    ? Colors.transparent
                                    : Colors.white10)),
                        child: Padding(
                            padding:
                                const EdgeInsets.symmetric(horizontal: 15.0),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  filterName,
                                  style: isSelected
                                      ? AuroraTheme.of(context).textStyle(
                                          TypescaleValues.P3,
                                          color: Colors.black)
                                      : AuroraTheme.of(context).textStyle(
                                          TypescaleValues.P5,
                                          color: Colors.white),
                                ),
                                const SizedBox(
                                  width: 2,
                                ),
                                isSelected
                                    ? const Icon(
                                        Icons.close,
                                        size: 15,
                                      )
                                    : const Icon(
                                        Icons.add,
                                        size: 15,
                                        color: Colors.white,
                                      )
                              ],
                            )))),
                const SizedBox(
                  width: 10,
                )
              ],
            );
          })),
    );
  }

  Widget exerciseListWidget(
      List<ActivityStoreAttributeEntry> activityStoreAttributeEntry,
      SearchAddExerciseLoaded state) {
    return SizedBox(
      height: MediaQuery.of(context).size.height * .8,
      child: ListView.builder(
          padding: const EdgeInsets.only(bottom: 180, top: 0),
          itemCount: exercises.length,
          itemBuilder: (context, index) {
            final exercise = exercises[index];
            final exerciseAttribute = activityStoreAttributeEntry.isNotEmpty
                ? activityStoreAttributeEntry[index]
                : null;
            return Container(
              constraints: const BoxConstraints(
                  minHeight: 80, minWidth: double.infinity),
              child: InkWell(
                onTap: () {
                  FocusScope.of(context).unfocus();
                  if (activityStoreAttributeEntry.isNotEmpty &&
                      _pageFrom != PageFrom.AI_TRAINER) {
                    RepositoryProvider.of<AnalyticsRepository>(context)
                        .logButtonClickEvent(extraInfo: {
                      "pageId": "SEARCH_EXERCISE",
                      "button": "LOG",
                      "appliedFilters": state.data.appliedFilters.toString(),
                      "searchText": searchController.text
                    });
                    BlocProvider.of<ActionBloc>(context).add(PerformActionEvent(
                        Action(
                            type: activityStoreAttributeEntry[index]
                                .logClickAction!
                                .type,
                            meta: {
                          ...?activityStoreAttributeEntry[index]
                              .logClickAction
                              ?.meta,
                          "loggingData": ClassicThumbnailListViewData(
                              title: activityStoreAttributeEntry[index].title,
                              subTitle:
                                  activityStoreAttributeEntry[index].subTitle,
                              imageUrl:
                                  activityStoreAttributeEntry[index].imageUrl,
                              action: activityStoreAttributeEntry[index]
                                  .logClickAction,
                              imageClickAction:
                                  activityStoreAttributeEntry[index]
                                      .imageClickAction,
                              meta: {
                                ...?activityStoreAttributeEntry[index]
                                    .logClickAction
                                    ?.meta,
                                'saveLogButtonTitleText':
                                    activityStoreAttributeEntry[index]
                                        .logClickAction
                                        ?.title,
                                'logUnit':
                                    activityStoreAttributeEntry[index].logUnit,
                                'formula': activityStoreAttributeEntry[index]
                                    .repMaxEvaluationExpression,
                                'weight': 0.0,
                                'loggedExerciseState':
                                    activityStoreAttributeEntry[index]
                                        .loggedExerciseState,
                                'reps': 0,
                                'activityStoreAttributeEntry':
                                    activityStoreAttributeEntry[index]
                              })
                        })));

                    //---------------
                  } else {
                    Iterable<Exercise> alreadyAdded = selectedExercise.where(
                        (element) => element.exerciseId == exercise.exerciseId);
                    if (exercise.isSelected) {
                      HapticFeedback.heavyImpact();
                    }
                    var isSelected = !exercise.isSelected;
                    exercise.isSelected = isSelected;

                    if (isSelected) {
                      if (alreadyAdded.isEmpty) {
                        selectedExercise.add(exercise);
                      }
                    } else {
                      selectedExercise.removeWhere((element) =>
                          element.exerciseId == exercise.exerciseId);
                    }
                  }
                  setState(() {});
                },
                child: Padding(
                    padding: const EdgeInsets.only(bottom: 15),
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        color: exercise.isSelected
                            ? HexColor.fromHex("#00FF00").withOpacity(0.1)
                            : Colors.white10,
                      ),
                      child: Column(
                        children: [
                          if (exerciseAttribute != null &&
                              exerciseAttribute.exerciseLogType ==
                                  ExerciseLogType.KEY_EXERCISE)
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Padding(
                                  padding: const EdgeInsets.only(
                                      left: 15, top: 8, bottom: 8),
                                  child: Text("Key Exercise",
                                      style: AuroraTheme.of(context).textStyle(
                                          TypescaleValues.P8,
                                          color: Colors.white60)),
                                ),
                                Container(
                                  height: 1,
                                  color: Colors.white10,
                                )
                              ],
                            ),
                          Padding(
                            padding: const EdgeInsets.only(
                                top: Spacings.x3,
                                bottom: Spacings.x3,
                                left: Spacings.x3,
                                right: Spacings.x4),
                            child: Row(
                              // mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              children: [
                                Row(
                                  children: [
                                    ClipRRect(
                                      borderRadius: BorderRadius.circular(10.0),
                                      child: CFNetworkImage(
                                        imageUrl: getImageUrl(context,
                                            imagePath: exercise.imageUrl ?? "",
                                            width: 50),
                                        fit: BoxFit.cover,
                                        width: 50,
                                        height: 50,
                                        errorWidget: (context, url, error) =>
                                            const Icon(Icons.error),
                                      ),
                                    ),
                                    const SizedBox(
                                      width: Spacings.x3,
                                    ),
                                    Column(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        SizedBox(
                                            width: MediaQuery.of(context)
                                                    .size
                                                    .width *
                                                0.5,
                                            child: Text(exercise.exerciseName,
                                                style: AuroraTheme.of(context)
                                                    .textStyle(
                                                        TypescaleValues.P2))),
                                        const SizedBox(
                                          height: 5,
                                        ),
                                        Text(exercise.muscleFocus.toUpperCase(),
                                            style: AuroraTheme.of(context)
                                                .textStyle(TypescaleValues.P8,
                                                    color: Colors.white60)),
                                        exerciseAttribute != null
                                            ? Padding(
                                                padding: EdgeInsets.only(
                                                  top: exerciseAttribute
                                                              .exercises?[0]
                                                              .exerciseExecution
                                                              ?.workoutSets
                                                              ?.any((set) =>
                                                                  set.weight !=
                                                                      null &&
                                                                  set.weight!
                                                                      .isNotEmpty) ==
                                                          true
                                                      ? 5.0
                                                      : 0.0,
                                                ),
                                                child: exerciseAttribute
                                                            .exercises?[0]
                                                            .exerciseExecution
                                                            ?.workoutSets
                                                            ?.any((set) =>
                                                                set.weight !=
                                                                    null &&
                                                                set.weight!
                                                                    .isNotEmpty) ==
                                                        true
                                                    ? Text.rich(
                                                        TextSpan(
                                                          children: [
                                                            TextSpan(
                                                              text:
                                                                  'Last Logged: ',
                                                              // Add "Last Logged: " before the workout data
                                                              style: AuroraTheme
                                                                      .of(context)
                                                                  .textStyle(
                                                                TypescaleValues
                                                                    .P8,
                                                                color: Colors
                                                                    .white60,
                                                              ),
                                                            ),
                                                            TextSpan(
                                                              text: exerciseAttribute
                                                                      .exercises?[
                                                                          0]
                                                                      .exerciseExecution
                                                                      ?.workoutSets
                                                                      ?.map(
                                                                          (set) {
                                                                        // Generate text for each set with valid weight
                                                                        if (set.weight !=
                                                                                null &&
                                                                            set.weight!.isNotEmpty) {
                                                                          return '${set.weight![0]} KG × ${set.reps}';
                                                                        } else {
                                                                          return null;
                                                                        }
                                                                      })
                                                                      .where((s) =>
                                                                          s !=
                                                                          null)
                                                                      .join(
                                                                          ', ') ??
                                                                  'No workout data',
                                                              style: AuroraTheme
                                                                      .of(context)
                                                                  .textStyle(
                                                                TypescaleValues
                                                                    .P8,
                                                                color: const Color(
                                                                    0xFF0FE498), // Set the color to #0FE498
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      )
                                                    : SizedBox.shrink(),
                                              )
                                            : Container(),
                                      ],
                                    ),
                                  ],
                                ),
                                const Spacer(),
                                (_pageFrom != null &&
                                        _pageFrom != PageFrom.AI_TRAINER)
                                    ? Container(
                                        height: 30,
                                        width: 50,
                                        // Adjust width to fit both text and icon
                                        child: Center(
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.center,
                                            children: [
                                              // Add spacing between the icon and text if needed
                                              Text(
                                                'Log',
                                                style: TextStyle(
                                                  color: Colors.white,
                                                ),
                                              ),
                                              SizedBox(width: 10),
                                              Padding(
                                                padding: const EdgeInsets.only(
                                                    top: 2.0),
                                                child: Icon(
                                                  CFIcons.chevron_right,
                                                  color: Colors.white,
                                                  size: 15,
                                                  semanticLabel:
                                                      "chevron_right",
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      )
                                    : Container(
                                        height: 30,
                                        width: 30,
                                        decoration: BoxDecoration(
                                          color: Colors.white10,
                                          borderRadius:
                                              BorderRadius.circular(8.0),
                                        ),
                                        child: Center(
                                          child: exercise.isSelected
                                              ? Icon(
                                                  Icons.check,
                                                  color: Colors.green
                                                      .withOpacity(0.7),
                                                )
                                              : const Icon(
                                                  Icons.add,
                                                  color: Colors.white,
                                                ),
                                        ),
                                      )
                              ],
                            ),
                          ),
                        ],
                      ),
                    )),
              ),
            );
          }),
    );
  }

  Widget exerciseAddedListWidget() {
    return SizedBox(
      height: MediaQuery.of(context).size.height * .75,
      child: ListView(
          scrollDirection: Axis.vertical,
          padding: EdgeInsets.zero,
          shrinkWrap: true,
          children: List.generate(selectedExercise.length, (index) {
            final exercise = selectedExercise[index];
            return Container(
              constraints: const BoxConstraints(
                  minHeight: 80, minWidth: double.infinity),
              child: InkWell(
                onTap: () {
                  FocusScope.of(context).unfocus();
                  exercise.isSelected = !exercise.isSelected;
                  setState(() {});
                },
                child: Padding(
                    padding: const EdgeInsets.only(bottom: 15),
                    child: Container(
                      decoration: BoxDecoration(
                        color: Colors.white10,
                        borderRadius: BorderRadius.circular(10.0),
                      ),
                      child: Padding(
                          padding: const EdgeInsets.all(15.0),
                          child: Column(
                            children: [
                              Row(
                                children: [
                                  Row(
                                    children: [
                                      ClipRRect(
                                        borderRadius:
                                            BorderRadius.circular(10.0),
                                        child: SizedBox.fromSize(
                                          // size: const Size.fromRadius(30),
                                          child: CFNetworkImage(
                                            imageUrl: getImageUrl(context,
                                                imagePath:
                                                    exercise.imageUrl ?? "",
                                                width: 50),
                                            fit: BoxFit.cover,
                                            width: 50,
                                            height: 50,
                                            errorWidget:
                                                (context, url, error) =>
                                                    const Icon(Icons.error),
                                          ),
                                        ),
                                      ),
                                      const SizedBox(
                                        width: Spacings.x3,
                                      ),
                                      Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          SizedBox(
                                              width: MediaQuery.of(context)
                                                      .size
                                                      .width *
                                                  0.5,
                                              child: Text(exercise.exerciseName,
                                                  style: AuroraTheme.of(context)
                                                      .textStyle(
                                                          TypescaleValues.P1))),
                                          const SizedBox(
                                            height: 8,
                                          ),
                                          Text(exercise.muscleFocus,
                                              style: AuroraTheme.of(context)
                                                  .textStyle(TypescaleValues.P8,
                                                      color: Colors.white60)),
                                        ],
                                      )
                                    ],
                                  ),
                                  const Spacer(),
                                  GestureDetector(
                                      onTap: () {
                                        // if (state is )
                                        selectedExercise.removeWhere(
                                            (element) =>
                                                element.exerciseId ==
                                                exercise.exerciseId);
                                        var item = exercises.where((element) =>
                                            element.exerciseId ==
                                            exercise.exerciseId);
                                        if (item.isNotEmpty) {
                                          item.first.isSelected = false;
                                        }
                                        setState(() {});

                                        final searchAddExerciseBloc =
                                            BlocProvider.of<
                                                SearchAddExerciseBloc>(context);
                                        searchAddExerciseBloc.add(
                                            ForceRenderDialogBoxEvent(
                                                renderCount:
                                                    Random().nextInt(1000)));
                                      },
                                      child: Container(
                                        decoration: BoxDecoration(
                                          color: Colors.white10,
                                          borderRadius:
                                              BorderRadius.circular(8.0),
                                        ),
                                        child: const Padding(
                                          padding: EdgeInsets.all(5.0),
                                          child: Icon(
                                            Icons.delete_outline,
                                            color: Colors.white,
                                          ),
                                        ),
                                      ))
                                ],
                              ),
                            ],
                          )),
                    )),
              ),
            );
          })),
    );
  }

  Widget setTile() {
    return Row(
      children: [
        const Column(
          children: [
            Text(
              "SET",
              style: TextStyle(
                  letterSpacing: 0.5,
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.w700),
            ),
            SizedBox(height: 15),
            Text(
              "1",
              style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.w700),
            ),
          ],
        ),
        const SizedBox(width: 15),
        Column(
          children: [
            const Text(
              "KG",
              style: TextStyle(
                  letterSpacing: 0.5,
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.w700),
            ),
            const SizedBox(height: 15),
            Container(
              width: 70,
              height: 34,
              decoration: BoxDecoration(
                  shape: BoxShape.rectangle,
                  borderRadius: BorderRadius.circular(5),
                  color: Colors.white.withOpacity(0.3)),
              child: const Padding(
                padding: EdgeInsets.all(5.0),
                child: Text(
                  "",
                  style: TextStyle(
                      letterSpacing: 0.5,
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.w700),
                ),
              ),
            )
          ],
        ),
        const SizedBox(width: 15),
        Column(
          children: [
            const Text(
              "REPS",
              style: TextStyle(
                  letterSpacing: 0.5,
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.w700),
            ),
            const SizedBox(height: 15),
            Container(
              width: 70,
              height: 34,
              decoration: BoxDecoration(
                  shape: BoxShape.rectangle,
                  borderRadius: BorderRadius.circular(5),
                  color: Colors.white.withOpacity(0.3)),
              child: const Padding(
                padding: EdgeInsets.all(5.0),
                child: Text(
                  "10",
                  textAlign: TextAlign.center,
                  style: TextStyle(
                      letterSpacing: 0.5,
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.w500),
                ),
              ),
            )
          ],
        ),
        const SizedBox(width: 15),
        Column(
          children: [
            const Text(
              "",
              style: TextStyle(
                  letterSpacing: 0.5,
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.w700),
            ),
            const SizedBox(height: 15),
            Container(
              width: 28,
              height: 28,
              decoration: BoxDecoration(
                  shape: BoxShape.circle, color: Colors.white.withOpacity(0.3)),
              child: const Icon(
                Icons.delete_outline,
                size: 20,
                color: Colors.red,
              ),
            )
          ],
        ),
      ],
    );
  }
}

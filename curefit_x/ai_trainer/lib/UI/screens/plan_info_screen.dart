import 'package:common/action/action_handler.dart';
import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/constants/action_constants.dart';
import 'package:common/ui/alert_view.dart';
import 'package:common/ui/atoms/primary_button.dart';
import 'package:common/ui/aurora.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/organisms/titlebar/classic_title_bar.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/spacing.dart';
import 'package:common/ui/widget_factory.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:flutter/material.dart' hide Action;
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../blocs/weekly_streak/weekly_streak_bloc.dart';
import '../../blocs/weekly_streak/weekly_streak_event.dart';
import '../../blocs/weekly_streak/weekly_streak_model.dart';
import '../../blocs/weekly_streak/weekly_streak_state.dart';
import '../../constants/constants.dart';

class PlanInfoScreen extends StatefulWidget {
  const PlanInfoScreen({Key? key}) : super(key: key);
  @override
  _PlanInfoScreenState createState() => _PlanInfoScreenState();
}

class _PlanInfoScreenState extends State<PlanInfoScreen>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  late ScrollController _scrollController;

  // PlanInfoPageScreenArguments? screenArguments;
  // PlanInfoPageScreenArguments? getScreenArguments() {
  //   final ScreenArguments? args =
  //       ModalRoute.of(context)!.settings.arguments as ScreenArguments?;
  //   if (args != null) {
  //     return PlanInfoPageScreenArguments(args.params);
  //   }
  //   return null;
  // }

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      refresh(context: context, showLoader: true);
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  refresh({required BuildContext context, bool showLoader = false}) {
    final weeklyGoalBloc = BlocProvider.of<PlanInfoPageBloc>(context);
    weeklyGoalBloc.add(LoadPlanInfoPageEvent());
  }

  onBackPress() {
    // ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
    // AnalyticsRepository analyticsRepository =
    // RepositoryProvider.of<AnalyticsRepository>(context);
    // // analyticsRepository.logButtonClickEvent(extraInfo: {
    // //   if (widgetData.action.analyticsData != null)
    // //     ...widgetData.action.analyticsData!
    // // });
    //
    // // actionBloc.add(PerformActionEvent(widgetData.action));
    //
    // Action action = Action(type: ActionTypes.NAVIGATION, url: "curefit://guidedscreen?cycleDay=2&mesoCycleId=721902&exerciseId=323934861_SINGLE_1&microCycleSequence=3&tenantId=1&totalMemory=**********");
    // // 0?mesoCycleId=721902&exerciseId=323934861_SINGLE_1&microCycleSequence=1&tenantId=1&totalMemory=**********
    //
    // actionBloc.add(PerformActionEvent(action));

    if (Navigator.canPop(context)) {
      Navigator.pop(context);
    } else if (!Navigator.canPop(context)) {
      ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
      actionBloc.add(CloseApplicationEvent(shouldReset: false));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: ClassicTitleBar(
        context: context,
        hasBackground: false,
        onBackPressed: () {
          onBackPress();
        },
      ),
      body: Stack(
        children: [
          Aurora(size: MediaQuery.of(context).size, context: context),
          Positioned.fill(child: buildWidgets()),
          BlocBuilder<PlanInfoPageBloc, PlanInfoPageState>(
            builder: (context, state) {
              if (state is PlanInfoPageLoading && state.showLoader) {
                return const FancyLoadingIndicator();
              }
              return Container();
            },
          ),
        ],
      ),
    );
  }

  Widget buildWidgets() {
    return BlocListener<PlanInfoPageBloc, PlanInfoPageState>(
      listener: (context, state) {
        if (state is PlanInfoPageFailed) {
          showErrorAlert(
              context: context,
              title: state.error,
              onClose: () {
                Navigator.pop(context);
              });
        }
      },
      child: BlocBuilder<PlanInfoPageBloc, PlanInfoPageState>(
          builder: (context, state) {
        if (state is PlanInfoPageLoaded) {
          return Stack(
            children: [
              getPageWidgets(context, state.data),
              Positioned(
                  left: Spacings.x2,
                  bottom: Spacings.x3,
                  right: Spacings.x2,
                  child: PrimaryButton(() {
                    onBackPress();
                  }, "VIEW YOUR PLAN"))
            ],
          );
        }
        return Container();
      }),
    );
  }

  getPageWidgets(BuildContext context, PlanInfoPageData pageData) {
    WidgetFactory widgetFactory = RepositoryProvider.of<WidgetFactory>(context);
    return SingleChildScrollView(
        child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: (pageData.widgets as List)
                .map((e) => widgetFactory.createWidget(e))
                .toList()));
  }
}

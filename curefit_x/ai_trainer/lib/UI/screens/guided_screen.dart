import 'package:common/action/action_handler.dart';
import 'package:common/analytics/analytics_repository.dart';
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/blocs/action/events.dart';
import 'package:common/constants/action_constants.dart';
import 'package:common/ui/alert_view.dart';
import 'package:common/ui/loading_indicator.dart';
import 'package:common/ui/widget_factory.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart' hide Action;
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:wakelock/wakelock.dart';

import '../../blocs/guided_screen/guided_bloc.dart';
import '../../blocs/guided_screen/guided_event.dart';
import '../../blocs/guided_screen/guided_model.dart';
import '../../blocs/guided_screen/guided_state.dart';
import '../../constants/constants.dart';

class GuidedScreen extends StatefulWidget {
  const GuidedScreen({Key? key}) : super(key: key);

  @override
  _GuidedScreenState createState() => _GuidedScreenState();
}

class _GuidedScreenState extends State<GuidedScreen>
    with WidgetsBindingObserver {
  late PageController controller;
  late int startTime;
  bool isSmallDevice = false;

  @override
  void initState() {
    super.initState();

    try {
      SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
      ));
    } catch (e) {
      if (kDebugMode) {
        print("error setting transparent statusbar");
      }
    }

    WidgetsBinding.instance.addObserver(this);
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      final ScreenArguments? args =
          ModalRoute.of(context)!.settings.arguments as ScreenArguments?;
      logPageView();
      refresh(context: context, showLoader: true, args: args);
    });
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  bool checkSmallDevice(BuildContext context){
    double width = MediaQuery. of(context). size. width;
    double height = MediaQuery. of(context). size. height;
    if(height * 0.54 <= width){
      return true;
    } else {
      return false;
    }
  }

  logSwipeEvent(int? cardIndex) {
    AnalyticsRepository analyticsRepository =
    RepositoryProvider.of<AnalyticsRepository>(context);
    // print("Analytics: card swipe event");
    analyticsRepository.sendEvent(
        eventName: "SWIPE_WIDGET",
        eventInfo: {
          "widgetType": "EXERCISES_CAROUSEL_VIEW",
          "pageName": EnumToString.convertToString(RouteNames.guidedscreen),
          "dateTime": DateTime.now().millisecondsSinceEpoch.toString(),
          "cardIndex": cardIndex ?? -1
        },
        inspectorTab: InspectorTab.ANALYTICS
    );
  }

  logPageView() {
    AnalyticsRepository analyticsRepository =
    RepositoryProvider.of<AnalyticsRepository>(context);
    analyticsRepository.logPageViewEvent(
        pageId: EnumToString.convertToString(RouteNames.guidedscreen),
        eventInfo: {});
  }

  refresh({
    required BuildContext context,
    bool showLoader = false,
    ScreenArguments? args,
  }) {
    var params = args?.params;
    if (params != null) {
      startTime = -1;
      if (params['startTime'] != null) {
        startTime = int.tryParse(params['startTime']) ?? -1;
        if (startTime != -1) {
          startTime *= 1000;
        }
      }

      final weeklyGoalBloc = BlocProvider.of<GuidedPageBloc>(context);
      weeklyGoalBloc.add(LoadGuidedPageEvent(
        true,
        params['cycleDay'],
        params['microCycleSequence'],
        params['exerciseId'],
        params['tenantId'],
      ));
    }
    Wakelock.enable();
  }

  onBackPress() {
    Wakelock.disable();
    if (Navigator.canPop(context)) {
      Navigator.pop(context);
    } else if (!Navigator.canPop(context)) {
      ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);
      actionBloc.add(CloseApplicationEvent(shouldReset: false));
    }
  }

  onWorkoutComplete() {
    ActionBloc actionBloc = BlocProvider.of<ActionBloc>(context);

    Action action = Action(type: ActionTypes.COMPLETE_FLUTTER_WORKOUT);
    actionBloc.add(PerformActionEvent(action));

    onBackPress();
  }

  @override
  Widget build(BuildContext context) {
    isSmallDevice = checkSmallDevice(context);
    return Scaffold(
        body: Stack(
      children: [
        buildWidgets(),
        BlocBuilder<GuidedPageBloc, GuidedPageState>(
          builder: (context, state) {
            if (state is GuidedPageLoading && state.showLoader) {
              return const FancyLoadingIndicator();
            }
            return Container();
          },
        ),
      ],
    ));
  }

  Widget buildWidgets() {
    return BlocListener<GuidedPageBloc, GuidedPageState>(
      listener: (context, state) {
        if (state is GuidedPageFailed) {
          showErrorAlert(
              context: context,
              title: state.error,
              onClose: () {
                Navigator.pop(context);
              });
        } else if (state is GuidedPageClosed) {
          onBackPress();
        } else if (state is WorkoutCompleted) {
          onWorkoutComplete();
        } else if (state is GuidedPageSwipeNext) {
          controller.nextPage(
            duration: const Duration(milliseconds: 500),
            curve: Curves.decelerate,
          );
        }
      },
      child: BlocBuilder<GuidedPageBloc, GuidedPageState>(
        builder: (context, state) {
          if (state is GuidedPageLoaded) {
            return getPageWidgets(context, state.data);
          }
          return Container();
        },
        buildWhen: (previous, current) => current is! GuidedPageSwipeNext,
      ),
    );
  }

  void onPageChanged(int val){
    logSwipeEvent(val);
  }

  getPageWidgets(BuildContext context, GuidedPageData pageData) {
    controller = PageController(initialPage: pageData.selectedCardIndex);
    return PageView.builder(
      scrollDirection: Axis.horizontal,
      controller: controller,
      itemCount: (pageData.widgets as List).length,
      itemBuilder: (context, index) => getVideoPage(pageData, index),
      onPageChanged: onPageChanged
    );
  }

  getVideoPage(GuidedPageData pageData, index) {
    var widgetList = (pageData.widgets as List);
    var widget = widgetList[index];
    WidgetFactory widgetFactory = RepositoryProvider.of<WidgetFactory>(context);
    return Container(
      color: Colors.white,
      child: Stack(
        children: widget['carouselCard']
            .map<Widget>((e) => widgetFactory.createWidget({
                  ...e,
                  "startTime": startTime,
                  "itemIndex": widget['itemIndex'],
                  "sectionLastIndexList": pageData.sectionLastIndexList,
                  "cycleDay": pageData.cycleDay,
                  "tenantId": pageData.tenantId,
                  "microCycleSequence": pageData.microCycleSequence,
                  "linearGradientValue": widget['linearGradient'],
                  "isSmallDevice": isSmallDevice,
                  "muscleFocusDark": widget['muscleFocusDark'],
                  "pointsOfPerformance": widget['pointsOfPerformance'],
                  "pageIndex": index,
                }))
            .toList(),
      ),
    );
  }
}

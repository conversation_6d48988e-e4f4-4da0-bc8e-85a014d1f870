import 'dart:ui';

import 'package:aitrainer/UI/modals/PostCheckinModal.dart';
import 'package:aitrainer/UI/modals/ReplaceModalConfirmationModal.dart';
import 'package:aitrainer/blocs/guided_screen/guided_bloc.dart';
import 'package:aitrainer/blocs/log_modal/log_modal_bloc.dart';
import 'package:aitrainer/blocs/replace_modal/replace_bloc.dart';
import 'package:bloc/src/bloc.dart';
import 'package:common/action/action_handler.dart' as ActionHandler;
import 'package:common/blocs/action/action_bloc.dart';
import 'package:common/constants/action_constants.dart';
import 'package:common/network/client.dart';
import 'package:common/ui/molecules/bottom_tray.dart';
import 'package:flutter/material.dart';

import '../UI/modals/LogModal.dart';
import '../network/plan_info.dart';

class AiTrainerActionHandler extends ActionHandler.IActionHandler {
  late FitnessPlanRepository fitnessPlanRepository;
  final GlobalKey<NavigatorState> navigatorKey;

  AiTrainerActionHandler(
      {required NetworkClient client, required this.navigatorKey}) {
    fitnessPlanRepository = FitnessPlanRepository(networkClient: client);
  }

  @override
  bool handleAction(ActionHandler.Action action, ActionBloc actionBloc) {
    BuildContext? context = navigatorKey.currentContext;
    if (context == null) {
      return false;
    }
    switch (action.type) {
      case ActionTypes.SHOW_REPLACE_FP_EXERCISE_MODAL:
        print("acxtion SHOW_REPLACE_FP_EXERCISE_MODAL pressed");
        print(action.toJson());
        var guidedPageBloc = action?.bloc as GuidedPageBloc;

        showBottomTray(
            context: context,
            showTopNotch: true,
            child: ReplaceModalConfirmationModal(
                guidedPageBloc: guidedPageBloc, modalData: action?.meta));

        return true;

      case ActionTypes.LOG_EXERCISE:
        // print("in log exercise action");
        //
        // print(action.meta!['loggingExerciseIds']);

        var logModalBloc = action.bloc as LogModalBloc;
        showModalBottomSheet(
            context: context,
            backgroundColor: Colors.black,
            isScrollControlled: true,
            enableDrag: false,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20.0),
            ),
            builder: (BuildContext context) {
              return Padding(
                padding: EdgeInsets.only(
                    bottom: MediaQuery.of(context).viewInsets.bottom),
                child: LogModal(
                    logModalBloc: logModalBloc,
                    loggingExerciseIds: action.meta!['loggingExerciseIds']),
              );
            });
        return true;
    }
    return false;
  }
}

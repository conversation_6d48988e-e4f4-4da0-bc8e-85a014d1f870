import 'package:aitrainer/blocs/search_add_exercise/add_exercise_modal.dart';
import 'package:common/network/client.dart';

class FitnessPlanRepository {
  NetworkClient networkClient;

  FitnessPlanRepository({required this.networkClient});

  Future<dynamic> getPlanInfoScreen() async {
    final response = await networkClient.get(
        '/v2/fitnessplanner/planInfo', {"version": "2", "fromFlutter": "true"});
    return response;
  }

  Future<dynamic> getGuidedScreenData(
      cycleDay, exerciseId, microCycleSequence, tenantId) async {
    final response = await networkClient
        .get('/v2/fitnessplanner/guidedExerciseDetails/$cycleDay', {
      "cycleDay": cycleDay,
      "microCycleSequence": microCycleSequence,
      "exerciseId": exerciseId,
      "tenantId": tenantId,
      "fromFlutter": "true"
    });
    return response;
  }

  Future<dynamic> getAlternativeExerciseData(exerciseEntryId) async {
    final response = await networkClient
        .get('/v2/fitnessplanner/alternativeExercises/$exerciseEntryId');
    return response;
  }

  Future<dynamic> submitAlternativeExercise(
    exerciseId,
    newExerciseId,
    alternativeType,
    userWodId,
    updateMesoCycle,
  ) async {
    final response = await networkClient
        .post('/v2/fitnessplanner/exerciseEntry/$exerciseId/replace', {
      "exerciseId": newExerciseId,
      "alternativeType": alternativeType,
      "userWodId": userWodId,
      "updateMesoCycle": updateMesoCycle
    });
    return response;
  }

  Future<dynamic> getExercisesForLogging(exerciseIds) async {
    final response = await networkClient.post(
        '/v2/fitnessplanner/exercisesForLogging', {"exerciseIds": exerciseIds});
    return response;
  }

  Future<dynamic> submitExerciseLogs(exerciseLogEntries) async {
    final response = await networkClient.post(
        '/v2/fitnessplanner/submitExerciseLogs', exerciseLogEntries);
    return response;
  }

  Future<dynamic> searchExercise(
      searchText,
      sectionHeader,
      List<String> appliedFilters,
      String? sessionId,
      String pageFrom,
      String? date) async {
    String encodedFilters = Uri.encodeComponent(appliedFilters.join(","));
    final response =
        await networkClient.get('/v2/fitnessplanner/searchExercise/v2', {
      "searchText": searchText,
      "sectionHeader": sectionHeader,
      "appliedFilters": encodedFilters,
      if (sessionId != null) "sessionId": sessionId,
      "pageFrom": pageFrom,
      if (date != null) "date": date
    });
    return response;
  }

  Future<dynamic> addExercise(
      wodId, forAllWorkout, blockType, List<Exercise> exerciseList) async {
    final response =
        await networkClient.post('/v2/fitnessplanner/addExercise', {
      "userWodId": wodId,
      "shouldChangeAllSameWods": forAllWorkout,
      "addExerciseRequestV2List": exerciseList
          .map((e) => {"blockType": blockType, "exerciseId": e.exerciseId})
          .toList()
    });
    return response;
  }

  Future<dynamic> getPostCheckinData(trainerId, isPtTrainer, centerId) async {
    final response =
        await networkClient.get('/v2/fitnessplanner/postCheckinNuxScreen', {
      ...trainerId != null ? {"trainerId": trainerId} : {},
      ...isPtTrainer != null ? {"isPtTrainer": isPtTrainer} : {},
      ...centerId != null ? {"centerId": centerId} : {},
    });
    return response;
  }

  Future<dynamic> optInAndOutForPlanData(isOptedOut, url) async {
    final response = await networkClient
        .put('/v2/fitnessplanner$url', {"isOptedOut": isOptedOut});
    return response;
  }
}

part of 'post_checkin_bloc.dart';

@immutable
abstract class PostCheckinEvent {
  const PostCheckinEvent() : super();
}

class FetchPostCheckinDataEvent extends PostCheckinEvent {
  final String? trainerId;
  final String? isPtTrainer;
  final String? centerId;

  const FetchPostCheckinDataEvent({this.trainerId, this.isPtTrainer, this.centerId});

  @override
  String toString() => "FetchPostCheckinDataEvent";
}

class OptInAndOptOutPlanEvent extends PostCheckinEvent  {
  final bool isOptedOut;
  final String url;

  const OptInAndOptOutPlanEvent({required this.isOptedOut, required this.url});
  @override
  String toString() => "OptInAndOptOutPlanEvent";
}

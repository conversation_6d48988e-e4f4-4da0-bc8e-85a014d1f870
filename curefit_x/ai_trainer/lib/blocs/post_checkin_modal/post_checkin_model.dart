import 'dart:math';

import 'package:common/constants/action_constants.dart';
import 'package:enum_to_string/enum_to_string.dart';

class TrainerDetails {
  final String post;
  final String? backgroundImage;
  final String? image;
  final String name;
  final String? experience;
  final AssignInfo? assignInfo;

  TrainerDetails(
      {this.assignInfo,
      this.image, this.experience,
      required this.name,
      required this.post,
      this.backgroundImage});

  factory TrainerDetails.formJson(data) {
    dynamic assignInfoJSON = data['assignInfo'];
    AssignInfo? assignInfo =
        assignInfoJSON != null ? AssignInfo.formJson(assignInfoJSON) : null;
    return TrainerDetails(
        post: data['post'],
        experience: data['experience'],
        image: data['image'],
        backgroundImage: data['backgroundImage'],
        name: data['name']);
  }
}

class AssignInfo {
  final String icon;
  final String text;

  AssignInfo({required this.icon, required this.text});

  factory AssignInfo.formJson(data) {
    return AssignInfo(
      icon: data['icon'],
      text: data['text'],
    );
  }
}

class Body {
  final BottomAction? action;
  final String? text;
  final String? body;

  Body({this.action, this.text, this.body});

  factory Body.formJson(data) {
    return Body(
      action: data['action'] != null ? BottomAction.formJson(data['action']) : null,
      text: data['text'],
      body: data['body'],
    );
  }
}

class ActionMeta {
  final String? action;
  final bool? countIn;
  final bool? countOut;
  final List<String>? text;
  final String? leftIconUrl;
  final String? rightIconUrl;
  ActionMeta(
      {this.text,
      this.action,
      this.leftIconUrl,
      this.rightIconUrl,
      this.countIn,
      this.countOut});

  factory ActionMeta.formJson(data) {
    return ActionMeta(
        action: data['action'],
        text: data['text'] != null ? List<String>.from(data['text']) : null,
        leftIconUrl: data['leftIconUrl'],
        countIn: data['countIn'],
        countOut: data['countOut'],
        rightIconUrl: data['rightIconUrl']);
  }
}

class BottomAction {
  final String? url;
  final ActionTypes? action;
  final ActionMeta? meta;
  final String? text;
  final String? icon;
  final String? title;

  BottomAction(
      {this.url, this.action,
      this.meta,
      this.icon,
      this.text,
      this.title});

  factory BottomAction.formJson(data) {
    return BottomAction(
        url: data['url'],
        action: data['action'] != null ? ActionTypes.values.byName(data['action']) : null,
        meta: data['meta'] !=null ? ActionMeta.formJson(data['meta']) : null,
        text: data['text'],
        title: data['title'],
        icon: data['icon']);
  }
}

class PostCheckinData {
  final TrainerDetails? trainerDetails;
  final dynamic closeAction;
  final BottomAction? bottomAction;
  final Body body;
  final String? topImageUrl;

  PostCheckinData(
      {this.trainerDetails,
      this.closeAction,
      this.bottomAction,
      required this.body,
      this.topImageUrl});

  factory PostCheckinData.formJson(data) {
    var trainerDetails = data['trainerDetails'];
    var closeAction = data['closeAction'];
    var bottomAction = data['bottomAction'];
    var body = data['body'];
    var topImageUrl = data['topImageUrl'];

    print("trainerdata--- $trainerDetails");

    return PostCheckinData(
        trainerDetails: trainerDetails != null ? TrainerDetails.formJson(trainerDetails) : null,
        closeAction: closeAction,
        bottomAction: bottomAction != null ? BottomAction.formJson(bottomAction) : null,
        body: Body.formJson(body),
        topImageUrl: topImageUrl);
  }
}

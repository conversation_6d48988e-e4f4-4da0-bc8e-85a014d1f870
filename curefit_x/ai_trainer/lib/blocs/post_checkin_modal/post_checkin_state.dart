part of 'post_checkin_bloc.dart';

abstract class PostCheckinState {
  PostCheckinState() : super();
}

class PostCheckinIdle extends PostCheckinState {
  @override
  String tostring() => 'IdleState';
}

class PostCheckingLoading extends PostCheckinState {
  final bool showLoader;

  PostCheckingLoading({this.showLoader = true});

  @override
  String toString() => 'PostCheckinLoading';
}

class PostCheckinLoaded extends PostCheckinState {
  final PostCheckinData data;

  PostCheckinLoaded({required this.data});

  @override
  String toString() => 'PostCheckinLoaded';
}

class PostCheckinFailed extends PostCheckinState {
  final String? error;

  PostCheckinFailed([this.error]) : super();

  @override
  String toString() => 'PostCheckinFailed';
}

class OptInOutLoading extends PostCheckinState {
  final bool showLoader;

  OptInOutLoading({this.showLoader = true});

  @override
  String toString() => 'OptInOutLoading';
}

class OptInOutLoaded extends PostCheckinState {
  final bool showLoader;

  OptInOutLoaded({this.showLoader = false});

  @override
  String toString() => 'OptInOutLoaded';
}

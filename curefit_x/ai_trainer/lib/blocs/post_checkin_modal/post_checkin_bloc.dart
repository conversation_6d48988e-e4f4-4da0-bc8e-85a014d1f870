import 'dart:async';
import 'dart:convert';

import 'package:aitrainer/blocs/post_checkin_modal/post_checkin_model.dart';
import 'package:aitrainer/network/plan_info.dart';
import 'package:bloc/bloc.dart';
import 'package:common/constants/common_constants.dart';
import 'package:meta/meta.dart';

part 'post_checkin_event.dart';
part 'post_checkin_state.dart';

class PostCheckinBloc extends Bloc<PostCheckinEvent, PostCheckinState> {
  FitnessPlanRepository repository;
  PostCheckinData? postCheckinData;

  PostCheckinBloc({required this.repository}) : super(PostCheckinIdle()) {
    on<FetchPostCheckinDataEvent>((event, emit) async {
        await _makeApiCall(event, emit);
    });
    on<OptInAndOptOutPlanEvent>((event, emit) async {
      await _optInandOutCall(event, emit);
    });
  }

  Future<void> _makeApiCall(
      FetchPostCheckinDataEvent event, Emitter<PostCheckinState> emit) async {
    try {
      emit(PostCheckingLoading(showLoader: true));

      dynamic response =
      await repository.getPostCheckinData(
          event.trainerId, event.isPtTrainer, event.centerId);
      if (response == null) {
        emit(PostCheckinFailed(ERROR_MSG));
      }
      PostCheckinData postCheckingDataParsed =
          PostCheckinData.formJson(response);

      emit(PostCheckinLoaded(data: postCheckingDataParsed));
    } catch (e) {
      emit(PostCheckinFailed(ERROR_MSG));
    }
  }

  Future<void> _optInandOutCall(
      OptInAndOptOutPlanEvent event, Emitter<PostCheckinState> emit) async {
    try {
      emit(OptInOutLoading(showLoader: true));

      dynamic response =
      await repository.optInAndOutForPlanData(event.isOptedOut, event.url);

      emit(OptInOutLoaded(showLoader: false));
    } catch (e) {
      emit(OptInOutLoaded(showLoader: false));
    }
  }
}

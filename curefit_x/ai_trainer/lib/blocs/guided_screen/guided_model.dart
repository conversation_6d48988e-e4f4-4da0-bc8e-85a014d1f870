class GuidedPageData {
  dynamic widgets;
  int selectedCardIndex;
  dynamic sectionLastIndexList;
  final String? cycleDay;
  final String exerciseId;
  final String? tenantId;
  final String? microCycleSequence;

  GuidedPageData(
      {required this.widgets,
      required this.selectedCardIndex,
      required this.sectionLastIndexList,
      this.cycleDay,
      required this.exerciseId,
      this.microCycleSequence,
      this.tenantId});

  factory GuidedPageData.fromJson(data) {
    var widget = (data['widgets'] as List)[0];
    return GuidedPageData(
        widgets: widget['widgetizedExercises'],
        selectedCardIndex: widget['selectedCardIndex'],
        sectionLastIndexList: widget['sectionLastIndexList'],
        exerciseId: data['exerciseId'],
        cycleDay: data['cycleDay'],
        microCycleSequence: data['microCycleSequence'],
        tenantId: data['tenantId']);
  }
}

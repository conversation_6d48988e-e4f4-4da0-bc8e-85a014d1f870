import 'package:common/action/action_handler.dart';

abstract class GuidedPageEvent {
  GuidedPageEvent() : super();
}

class LoadGuidedPageEvent extends GuidedPageEvent {
  final bool showLoader;
  final String? cycleDay;
  final String exerciseId;
  final String? tenantId;
  final String? microCycleSequence;

  LoadGuidedPageEvent(this.showLoader, this.cycleDay, this.microCycleSequence,
      this.exerciseId, this.tenantId)
      : super();

  @override
  String toString() => "LoadGuidedPageEvent";
}

class CloseGuidedPageEvent extends GuidedPageEvent {}

class NextSwipeGuidedPageEvent extends GuidedPageEvent {}

class WorkoutCompleteEvent extends GuidedPageEvent {}

class LoadReplaceSubmitEvent extends GuidedPageEvent {
  final bool showLoader;
  final int exerciseEntryId;
  final String newExerciseId;
  final String alternativeType;
  final int userWodId;
  final bool updateMesocycle;
  final String? cycleDay;
  final String? tenantId;
  final String? microCycleSequence;
  final dynamic context;

  LoadReplaceSubmitEvent(
      this.showLoader,
      this.exerciseEntryId,
      this.newExerciseId,
      this.alternativeType,
      this.userWodId,
      this.updateMesocycle,
      this.cycleDay,
      this.tenantId,
      this.microCycleSequence,
      this.context)
      : super();

  @override
  String toString() => "LoadReplaceModalEvent";
}

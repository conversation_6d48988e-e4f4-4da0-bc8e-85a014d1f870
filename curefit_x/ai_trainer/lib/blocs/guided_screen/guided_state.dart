import 'guided_model.dart';

abstract class GuidedPageState {
  GuidedPageState() : super();
}

class GuidedPageIdle extends GuidedPageState {
  @override
  String toString() => 'IdleState';
}

class GuidedPageLoading extends GuidedPageState {
  final GuidedPageData? data;
  final bool showLoader;

  GuidedPageLoading({this.data, this.showLoader = true});

  @override
  String toString() => 'GuidedPageLoading';
}

class GuidedPageLoaded extends GuidedPageState {
  final GuidedPageData data;

  GuidedPageLoaded({required this.data});

  @override
  String toString() => 'GuidedPageLoaded';
}

class GuidedPageFailed extends GuidedPageState {
  final String? error;

  GuidedPageFailed([this.error]) : super();

  @override
  String toString() => 'GuidedPageFailed';
}

class GuidedPageClosed extends GuidedPageState {
  GuidedPageClosed() : super();

  @override
  String toString() => 'GuidedPageClosed';
}

class WorkoutCompleted extends GuidedPageState {
  WorkoutCompleted() : super();

  @override
  String toString() => 'WorkoutCompleted';
}

class GuidedPageSwipeNext extends GuidedPageState {
  GuidedPageSwipeNext() : super();

  @override
  String toString() => 'GuidedPageSwipeNext';
}

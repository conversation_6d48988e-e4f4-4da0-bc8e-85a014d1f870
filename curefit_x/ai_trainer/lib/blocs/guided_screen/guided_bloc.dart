import 'dart:convert';

import 'package:common/constants/common_constants.dart';
import 'package:common/network/client.dart';
import 'package:bloc/bloc.dart';
import 'package:flutter/cupertino.dart';
import '../../network/plan_info.dart';
import 'guided_state.dart';
import 'guided_event.dart';
import 'guided_model.dart';
import 'package:flutter/services.dart';

class GuidedPageBloc extends Bloc<GuidedPageEvent, GuidedPageState> {
  FitnessPlanRepository repository;

  GuidedPageBloc({required this.repository}) : super(GuidedPageIdle()) {
    on<LoadGuidedPageEvent>((event, emit) async {
      await _makeApiCall(event, emit);
    });
    on<CloseGuidedPageEvent>((event, emit) async {
      emit(GuidedPageClosed());
    });
    on<NextSwipeGuidedPageEvent>((event, emit) async {
      emit(GuidedPageSwipeNext());
    });
    on<WorkoutCompleteEvent>((event, emit) async {
      emit(WorkoutCompleted());
    });
    on<LoadReplaceSubmitEvent>((event, emit) async {
      await _makeSubmitApiCall(event, emit);
    });
  }

  Future<void> _makeApiCall(
      LoadGuidedPageEvent event, Emitter<GuidedPageState> emit) async {
    try {
      emit(GuidedPageLoading(showLoader: true));
      dynamic response = await repository.getGuidedScreenData(event.cycleDay,
          event.exerciseId, event.microCycleSequence, event.tenantId);
      // dynamic response = await rootBundle.loadString('assets/mocks/ai_landing.json');
      // response = json.decode(response);
      if (response == null) {
        emit(GuidedPageFailed(ERROR_MSG));
      }
      // response['navAction'] = event.navAction;

      response['exerciseId'] = event.exerciseId;
      response['cycleDay'] = event.cycleDay;
      response['microCycleSequence'] = event.microCycleSequence;
      response['tenantId'] = event.tenantId;
      emit(GuidedPageLoaded(data: GuidedPageData.fromJson(response)));
    } on NetworkException catch (e) {
      emit(GuidedPageFailed(e.subTitle));
    } catch (e) {
      emit(GuidedPageFailed(ERROR_MSG));
    }
  }

  Future<void> _makeSubmitApiCall(
      LoadReplaceSubmitEvent event, Emitter<GuidedPageState> emit) async {
    print('inside submit event here make an api call');
    print(event.updateMesocycle);
    try {
      dynamic response = await repository.submitAlternativeExercise(
          event.exerciseEntryId,
          event.newExerciseId,
          event.alternativeType,
          event.userWodId,
          event.updateMesocycle);

      if (response == null || !response['success']) {
        // emit(ReplaceModalFailed(ERROR_MSG));
      }
      int exerciseId = response['exerciseEntry']!['id'];
      // if(state is GuidedPageLoaded){
      add(LoadGuidedPageEvent(true, event.cycleDay, event.microCycleSequence,
          "$exerciseId", event.tenantId));
      // print(state.data);
      // }

      Navigator.pop(event.context);
    } on NetworkException catch (e) {
      // emit(ReplaceModalFailed(e.subTitle));
    } catch (e) {
      // emit(ReplaceModalFailed(ERROR_MSG));
    }
  }
}

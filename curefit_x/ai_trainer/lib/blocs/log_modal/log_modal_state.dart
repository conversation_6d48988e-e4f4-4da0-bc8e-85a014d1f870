part of 'log_modal_bloc.dart';

@immutable
abstract class LogModalState {
  LogModalState() : super();
}

class LogModalIdle extends LogModalState {
  @override
  String tostring() => 'IdleState';
}

class LogModalLoading extends LogModalState {
  final bool showLoader;

  LogModalLoading({this.showLoader = true});

  @override
  String toString() => 'LogModalLoading';
}

class LogModalLoaded extends LogModalState {
  final LogModalData data;

  LogModalLoaded({required this.data});

  @override
  String toString() => 'LogModalLoaded';
}

class LogModalFailed extends LogModalState {
  final String? error;

  LogModalFailed([this.error]) : super();

  @override
  String toString() => 'LogModalFailed';
}

import 'dart:async';
import 'dart:convert';

import 'package:bloc/bloc.dart';
import 'package:common/constants/common_constants.dart';
import 'package:common/network/client.dart';
import 'package:common/ui/theme/aurora_theme.dart';
import 'package:common/ui/theme/typescale.dart';
import 'package:common/ui/toast.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:meta/meta.dart';

import '../../network/plan_info.dart';
import 'log_modal_model.dart';

part 'log_modal_event.dart';
part 'log_modal_state.dart';

class LogModalBloc extends Bloc<LogModalEvent, LogModalState> {
  FitnessPlanRepository repository;
  LogModalData? logModalData;

  LogModalBloc({required this.repository, this.logModalData})
      : super(LogModalIdle()) {
    on<LoadLogModalEvent>((event, emit) async {
      await _makeApiCall(event, emit);
    });
    on<SubmitLogModalEvent>((event, emit) async {
      await _makeSubmitApiCall(event, emit);
    });
  }

  Future<void> _makeApiCall(
      LoadLogModalEvent event, Emitter<LogModalState> emit) async {
    try {
      emit(LogModalLoading(showLoader: true));

      dynamic response =
          await repository.getExercisesForLogging(event.exerciseIds);

      LogModalData logModalDataParsed = LogModalData.formJson(response);
      logModalData = logModalDataParsed;

      if (response == null) {
        emit(LogModalFailed(ERROR_MSG));
      }

      emit(LogModalLoaded(data: logModalDataParsed));
    } catch (e) {
      emit(LogModalFailed(ERROR_MSG));
    }
  }

  Future<void> _makeSubmitApiCall(
      SubmitLogModalEvent event, Emitter<LogModalState> emit) async {
    try {
      dynamic response =
          await repository.submitExerciseLogs(event.exerciseLogEntries);

      Toast.show("Exercise Logged!", event.context,
          duration: Toast.lengthLong,
          gravity: Toast.top,
          textStyle: AuroraTheme.of(event.context)
              .textStyle(TypescaleValues.P3, color: Colors.white),
          backgroundColor: Colors.black);

      Navigator.pop(event.context);
      // if
    } catch (e) {
      if (kDebugMode) {
        print(e);
      }
      Toast.show("Error: Logging exercise!", event.context,
          duration: Toast.lengthShort,
          gravity: Toast.top,
          textStyle: AuroraTheme.of(event.context)
              .textStyle(TypescaleValues.P3, color: Colors.black),
          backgroundColor: Colors.white);
      Navigator.pop(event.context);
    }
  }
}

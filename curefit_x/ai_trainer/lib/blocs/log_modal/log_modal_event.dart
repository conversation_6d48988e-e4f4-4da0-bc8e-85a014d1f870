part of 'log_modal_bloc.dart';

@immutable
abstract class LogModalEvent {
  const LogModalEvent() : super();
}

class LoadLogModalEvent extends LogModalEvent {
  final dynamic exerciseIds;

  const LoadLogModalEvent({required this.exerciseIds}) : super();

  @override
  String toString() => "LoadLogModalEvent";
}

class SubmitLogModalEvent extends LogModalEvent {
  final dynamic exerciseLogEntries;
  final BuildContext context;

  const SubmitLogModalEvent(
      {required this.exerciseLogEntries, required this.context})
      : super();

  @override
  String toString() => "LoadLogModalEvent";
}

import 'dart:convert';

class LogModalData {
  final String? title;
  final String? subTitle;
  final List<ExerciseLogEntriesData>? exerciseLogEntries;
  final num? muscleLevel;

  LogModalData(
      {this.title, this.subTitle, this.exerciseLogEntries, this.muscleLevel});

  factory LogModalData.formJson(data) {
    var title = data['title'];
    var subTitle = data['subTitle'];
    var exerciseLogEntriesJson = data['exerciseLogEntries'] as List;
    List<ExerciseLogEntriesData> exerciseLogEntries = [];
    for (var i = 0; i < exerciseLogEntriesJson.length; i++) {
      exerciseLogEntries
          .add(ExerciseLogEntriesData.fromJson(exerciseLogEntriesJson[i]));
    }
    var muscleLevel = data['muscleLevel'];
    return LogModalData(
        title: title,
        subTitle: subTitle,
        exerciseLogEntries: exerciseLogEntries,
        muscleLevel: muscleLevel);
  }
}

class ExerciseLogEntriesData {
  final num? tenantId;
  final String? userId;
  final num? exerciseId;
  final String? herculesExerciseId;
  final String? executionType;
  final dynamic meta;
  final String? thumbnailUrl;
  final String? exerciseName;
  final String? exerciseType;
  final String? lateral;
  final dynamic toggleSwitch;
  final FpExecutionLogsData? templateLog;
  List<FpExecutionLogsData>? fpExecutionLogs;

  ExerciseLogEntriesData(
      {this.tenantId,
      this.userId,
      this.exerciseId,
      this.herculesExerciseId,
      this.executionType,
      this.meta,
      this.thumbnailUrl,
      this.exerciseName,
      this.exerciseType,
      this.lateral,
      this.toggleSwitch,
      this.templateLog,
      this.fpExecutionLogs});

  factory ExerciseLogEntriesData.fromJson(data) {
    var fpExecutionLogsJson = data['fpExecutionLogs'] as List;
    List<FpExecutionLogsData> fpExecutionLogs = [];
    for (var i = 0; i < fpExecutionLogsJson.length; i++) {
      fpExecutionLogs.add(FpExecutionLogsData.fromJson(fpExecutionLogsJson[i]));
    }
    FpExecutionLogsData templateLog =
        FpExecutionLogsData.fromJson(data['templateLog']);
    var toggleSwitchdata = null;
    if (data['toggleSwitch'] != null) {
      toggleSwitchdata = data['toggleSwitch'];
    }

    return ExerciseLogEntriesData(
        tenantId: data['tenantId'],
        userId: data['userId'],
        exerciseId: data['exerciseId'],
        herculesExerciseId: data['herculesExerciseId'],
        executionType: data['executionType'],
        meta: data['meta'],
        thumbnailUrl: data['thumbnailUrl'],
        exerciseName: data['exerciseName'],
        exerciseType: data['exerciseType'],
        lateral: data['lateral'],
        toggleSwitch: toggleSwitchdata,
        templateLog: templateLog,
        fpExecutionLogs: fpExecutionLogs);
  }

  Map toJson() {
    dynamic templateLogJson = null;

    final templateLog = this.templateLog;
    if (templateLog != null) {
      templateLogJson = templateLog.toJson();
    }
    final fpExecutionLogs = this.fpExecutionLogs;
    dynamic fpExecutionLogsJson = [];
    if (fpExecutionLogs != null) {
      for (var fpExecutionLog in fpExecutionLogs) {
        fpExecutionLogsJson.add(fpExecutionLog.toJson());
      }
    }

    Map<String, dynamic> jsonData = {
      "tenantId": tenantId,
      "userId": userId,
      "exerciseId": exerciseId,
      "herculesExerciseId": herculesExerciseId,
      "executionType": executionType,
      "meta": meta,
      "thumbnailUrl": thumbnailUrl,
      "exerciseName": exerciseName,
      "exerciseType": exerciseType,
      "lateral": lateral,
      "toggleSwitch": toggleSwitch,
      "templateLog": templateLogJson,
      "fpExecutionLogs": fpExecutionLogsJson
    };
    return jsonData;
  }
}

class FpExecutionLogsData {
  final dynamic sequence;
  final dynamic unilateralDirection;
  final dynamic weight;
  final dynamic duration;
  final dynamic count;
  final dynamic distance;
  String? value1;
  String? unit1;
  final String? separator;
  String? value2;
  String? unit2;

  FpExecutionLogsData(
      {this.sequence,
      this.unilateralDirection,
      this.weight,
      this.duration,
      this.count,
      this.distance,
      this.value1,
      this.unit1,
      this.separator,
      this.value2,
      this.unit2});

  factory FpExecutionLogsData.fromJson(data) {
    return FpExecutionLogsData(
        sequence: data['sequence'],
        unilateralDirection: data['unilateralDirection'],
        weight: data['weight'],
        duration: data['duration'],
        count: data['count'],
        distance: data['distance'],
        value1: data['value1'],
        unit1: data['unit1'],
        separator: data['separator'],
        value2: data['value2'],
        unit2: data['unit2']);
  }

  factory FpExecutionLogsData.fromObject(fpExecutionLogsData) {
    return FpExecutionLogsData(
        sequence: fpExecutionLogsData.sequence,
        unilateralDirection: fpExecutionLogsData.unilateralDirection,
        weight: fpExecutionLogsData.weight,
        duration: fpExecutionLogsData.duration,
        count: fpExecutionLogsData.count,
        distance: fpExecutionLogsData.distance,
        value1: fpExecutionLogsData.value1,
        unit1: fpExecutionLogsData.unit1,
        separator: fpExecutionLogsData.separator,
        value2: fpExecutionLogsData.value2,
        unit2: fpExecutionLogsData.unit2);
  }

  Map toJson() {
    Map<String, dynamic> jsonData = {
      "sequence": sequence,
      "unilateralDirection": unilateralDirection,
      "weight": weight,
      "duration": duration,
      "count": count,
      "distance": distance,
      "value1": value1,
      "unit1": unit1,
      "separator": separator,
      "value2": value2,
      "unit2": unit2
    };
    return jsonData;
  }
}

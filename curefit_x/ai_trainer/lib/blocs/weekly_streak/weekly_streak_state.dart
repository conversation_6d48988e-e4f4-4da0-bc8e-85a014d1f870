import 'weekly_streak_model.dart';

abstract class PlanInfoPageState {
  PlanInfoPageState() : super();
}

class PlanInfoPageIdle extends PlanInfoPageState {
  @override
  String toString() => 'IdleState';
}

class PlanInfoPageLoading extends PlanInfoPageState {
  final PlanInfoPageData? data;
  final bool showLoader;

  PlanInfoPageLoading({this.data, this.showLoader = true});

  @override
  String toString() => 'PlanInfoPageLoading';
}

class PlanInfoPageLoaded extends PlanInfoPageState {
  final PlanInfoPageData data;

  PlanInfoPageLoaded({required this.data});

  @override
  String toString() => 'PlanInfoPageLoaded';
}

class PlanInfoPageFailed extends PlanInfoPageState {
  final String? error;

  PlanInfoPageFailed([this.error]) : super();

  @override
  String toString() => 'PlanInfoPageFailed';
}

import 'dart:convert';

import 'package:common/constants/common_constants.dart';
import 'package:common/network/client.dart';
import 'package:bloc/bloc.dart';
import '../../network/plan_info.dart';
import 'weekly_streak_state.dart';
import 'weekly_streak_event.dart';
import 'weekly_streak_model.dart';
import 'package:flutter/services.dart';

class PlanInfoPageBloc extends Bloc<PlanInfoPageEvent, PlanInfoPageState> {
  FitnessPlanRepository repository;
  PlanInfoPageBloc({required this.repository}) : super(PlanInfoPageIdle()) {
    on<LoadPlanInfoPageEvent>((event, emit) async {
      await _makeApiCall(event, emit);
    });
  }

  Future<void> _makeApiCall(
      LoadPlanInfoPageEvent event, Emitter<PlanInfoPageState> emit) async {
    try {
      emit(PlanInfoPageLoading(showLoader: true));
      dynamic response = await repository.getPlanInfoScreen();
      // dynamic response = await rootBundle.loadString('assets/mocks/ai_landing.json');
      // response = json.decode(response);
      if (response == null) {
        emit(PlanInfoPageFailed(ERROR_MSG));
      }
      // response['navAction'] = event.navAction;
      emit(PlanInfoPageLoaded(data: PlanInfoPageData.fromJson(response)));
    } on NetworkException catch (e) {
      emit(PlanInfoPageFailed(e.subTitle));
    } catch (e) {
      emit(PlanInfoPageFailed(ERROR_MSG));
    }
  }
}

import 'package:flutter/cupertino.dart';
import 'package:flutter_bloc/src/bloc_provider.dart';
import 'package:aitrainer/blocs/guided_screen/guided_bloc.dart';
import 'package:aitrainer/blocs/replace_modal/replace_model.dart';
import 'package:common/constants/common_constants.dart';
import 'package:aitrainer/blocs/replace_modal/replace_event.dart';
import 'package:aitrainer/blocs/replace_modal/replace_state.dart';
import 'package:bloc/bloc.dart';
import 'package:common/network/client.dart';
import '../../network/plan_info.dart';

class ReplaceModalBloc extends Bloc<ReplaceModalEvent, ReplaceModalState> {
  FitnessPlanRepository repository;

  ReplaceModalBloc({required this.repository}) : super(ReplaceModalIdle()) {
    on<LoadReplaceModalEvent>((event, emit) async {
      await _makeApiCall(event, emit);
    });
  }

  Future<void> _makeApiCall(
      LoadReplaceModalEvent event, Emitter<ReplaceModalState> emit) async {
    try {
      emit(ReplaceModalLoading(showLoader: true));
      dynamic response =
          await repository.getAlternativeExerciseData(event.exerciseEntryId);

      if (response == null) {
        emit(ReplaceModalFailed(ERROR_MSG));
      }

      emit(ReplaceModalLoaded(data: ReplaceModalData.fromJson(response)));
    } on NetworkException catch (e) {
      emit(ReplaceModalFailed(e.subTitle));
    } catch (e) {
      emit(ReplaceModalFailed(ERROR_MSG));
    }
  }
}

//   const url = `${BASE_URL}/exerciseEntry/${exerciseId}/replace?tenantId=${tenantId}`;
// let response;
// const body = {
//   exerciseId: newExerciseId,
//   alternativeType,
//   userWodId,
//   updateMesoCycle,
// };
// }

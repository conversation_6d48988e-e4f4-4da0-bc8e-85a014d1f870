import 'package:aitrainer/blocs/replace_modal/replace_model.dart';

abstract class ReplaceModalState {
  ReplaceModalState() : super();
}

class ReplaceModalIdle extends ReplaceModalState {
  @override
  String toString() => 'IdleState';
}

class ReplaceModalLoading extends ReplaceModalState {
  final bool showLoader;

  ReplaceModalLoading({this.showLoader = true});

  @override
  String toString() => 'ReplaceModalLoading';
}

class ReplaceModalLoaded extends ReplaceModalState {
  final ReplaceModalData data;

  ReplaceModalLoaded({required this.data});

  @override
  String toString() => 'ReplaceModalLoaded';
}

class ReplaceModalFailed extends ReplaceModalState {
  final String? error;

  ReplaceModalFailed([this.error]) : super();

  @override
  String toString() => 'ReplaceModalFailed';
}

class ReplaceModalConfirmation extends ReplaceModalState {
  @override
  String toString() => 'ReplaceModalConfirmation';
}

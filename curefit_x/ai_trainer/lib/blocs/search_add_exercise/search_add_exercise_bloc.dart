import 'dart:async';

import 'package:aitrainer/blocs/search_add_exercise/add_exercise_modal.dart';
import 'package:aitrainer/network/plan_info.dart';
import 'package:bloc/bloc.dart';
import 'package:common/constants/common_constants.dart';
import 'package:common/network/client.dart';
import 'package:meta/meta.dart';

part 'search_add_exercise_event.dart';
part 'search_add_exercise_state.dart';

class SearchAddExerciseBloc
    extends Bloc<SearchAddExerciseEvent, SearchAddExerciseState> {
  // mock k lie
  // loadMockJson(String fileName) async {
  //   String data = await rootBundle.loadString('assets/mocks/$fileName');
  //   return json.decode(data);
  // }

  FitnessPlanRepository repository;
  List<String> appliedFilters = [];
  SearchAddExerciseBloc({required this.repository})
      : super(SearchAddExerciseInitial()) {
    on<FetchExerciseToAddEvent>((event, emit) async {
      await fetchInitialExercise(event, emit);
    });
    on<FetchExerciseToAddOnSearchEvent>((event, emit) async {
      await fetchExercise(event, emit);
    });
    on<AddExerciseToUserWodEvent>((event, emit) async {
      await addExercise(event, emit);
    });
    on<ForceRenderDialogBoxEvent>((event, emit) async {
      emit(ForceRenderDialogBox(renderCount: event.renderCount));
    });
  }

  Future<void> fetchInitialExercise(FetchExerciseToAddEvent event,
      Emitter<SearchAddExerciseState> emit) async {
    try {
      emit(SearchAddExerciseLoading(showLoader: true));
      dynamic response = await repository.searchExercise(
          event.searchText,
          event.headerText,
          event.appliedFilters,
          event.sessionId,
          event.pageFrom,
          event.date);
      // var response = await loadMockJson("searchExce.json"); // mock k lie
      if (response == null) {
        emit(SearchAddExerciseFailed(ERROR_MSG));
      }
      emit(SearchAddExerciseLoaded(
          data: SearchExerciseResponse.formJson(response)));
    } on NetworkException catch (e) {
      emit(SearchAddExerciseFailed(e.subTitle));
    } catch (e) {
      emit(SearchAddExerciseFailed(ERROR_MSG));
    }
  }

  Future<void> fetchExercise(FetchExerciseToAddOnSearchEvent event,
      Emitter<SearchAddExerciseState> emit) async {
    try {
      if (event.showLoader != null && event.showLoader!) {
        emit(SearchAddExerciseLoading(showLoader: true));
      }
      dynamic response = await repository.searchExercise(
          event.searchText,
          event.headerText,
          event.appliedFilters,
          event.sessionId,
          event.pageFrom,
          event.date);
      // var response = await loadMockJson("searchExce.json"); // mock k lie
      if (response == null) {
        emit(SearchAddExerciseFailed(ERROR_MSG));
      }
      emit(SearchAddExerciseLoaded(
          data: SearchExerciseResponse.formJson(response)));
    } on NetworkException catch (e) {
      emit(SearchAddExerciseFailed(e.subTitle));
    } catch (e) {
      emit(SearchAddExerciseFailed(ERROR_MSG));
    }
  }

  Future<void> addExercise(AddExerciseToUserWodEvent event,
      Emitter<SearchAddExerciseState> emit) async {
    try {
      emit(AddingExercise());
      dynamic response = await repository.addExercise(event.wodId,
          event.shouldChangeAllSameWods, event.sectionHeader, event.exercise);
      if (response == null) {
        emit(SearchAddExerciseFailed(ERROR_MSG));
      }
      emit(ExerciseAddedSuccess());
      // emit(SearchAddExerciseLoaded(data: SearchExerciseResponse.formJson(response)));
    } on NetworkException catch (e) {
      emit(SearchAddExerciseFailed(e.subTitle));
    } catch (e) {
      emit(SearchAddExerciseFailed(ERROR_MSG));
    } finally {
      emit(AddingExerciseComplete());
    }
  }
}

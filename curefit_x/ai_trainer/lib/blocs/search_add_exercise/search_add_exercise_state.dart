part of 'search_add_exercise_bloc.dart';

@immutable
abstract class SearchAddExerciseState {}

class SearchAddExerciseInitial extends SearchAddExerciseState {}

class SearchAddExerciseLoading extends SearchAddExerciseState {
  final bool showLoader;

  SearchAddExerciseLoading({this.showLoader = true});

  @override
  String toString() => 'SearchAddExerciseLoading';
}

class SearchAddExerciseLoaded extends SearchAddExerciseState {
  final SearchExerciseResponse data;

  SearchAddExerciseLoaded({required this.data});

  @override
  String toString() => 'SearchAddExerciseLoaded';
}

class SearchAddExerciseFailed extends SearchAddExerciseState {
  final String? error;

  SearchAddExerciseFailed([this.error]) : super();

  @override
  String toString() => 'SearchAddExerciseFailed';
}

class ForceRenderDialogBox extends SearchAddExerciseState {
  final int? renderCount;

  ForceRenderDialogBox({this.renderCount}) : super();

  @override
  String toString() => 'ForceRenderDialogBox';
}

class AddingExercise extends SearchAddExerciseState {
  AddingExercise() : super();

  @override
  String toString() => 'FetchingExercise';
}

class AddingExerciseComplete extends SearchAddExerciseState {
  AddingExerciseComplete() : super();

  @override
  String toString() => 'ExerciseFetchComplete';
}


class ExerciseAddedSuccess extends SearchAddExerciseState {
  ExerciseAddedSuccess() : super();

  @override
  String toString() => 'ExerciseAddedSuccess';
}


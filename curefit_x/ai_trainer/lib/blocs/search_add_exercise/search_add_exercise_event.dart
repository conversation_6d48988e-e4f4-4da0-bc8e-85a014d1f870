part of 'search_add_exercise_bloc.dart';

@immutable
abstract class SearchAddExerciseEvent {}

class FetchExerciseToAddEvent extends SearchAddExerciseEvent {
  final String? searchText;
  final String? headerText;
  final List<String> appliedFilters;
  String? sessionId;
  final String pageFrom;
  String? date;
  FetchExerciseToAddEvent(
      {required this.searchText,
      required this.headerText,
      required this.appliedFilters,
      this.sessionId,
      required this.pageFrom,
      this.date});

  @override
  String toString() => "FetchExerciseToAddEvent";
}

class FetchExerciseToAddOnSearchEvent extends SearchAddExerciseEvent {
  final String? searchText;
  final String? headerText;
  final List<String> appliedFilters;
  String? sessionId;
  String? date;
  bool? showLoader;
  final String pageFrom;

  FetchExerciseToAddOnSearchEvent(
      {required this.searchText,
      required this.headerText,
      required this.appliedFilters,
      this.sessionId,
      this.date,
      this.showLoader,
      required this.pageFrom});

  @override
  String toString() => "FetchExerciseToAddOnSearchEvent";
}

class AddExerciseToUserWodEvent extends SearchAddExerciseEvent {
  final int wodId;
  final bool? shouldChangeAllSameWods;
  final String sectionHeader;
  final List<Exercise> exercise;

  AddExerciseToUserWodEvent(
      {required this.wodId,
      required this.shouldChangeAllSameWods,
      required this.sectionHeader,
      required this.exercise});

  @override
  String toString() => "AddExerciseToUserWodEvent";
}

class ForceRenderDialogBoxEvent extends SearchAddExerciseEvent {
  final int? renderCount;

  ForceRenderDialogBoxEvent({required this.renderCount});

  @override
  String toString() => "ForceRenderDialogBoxEvent";
}

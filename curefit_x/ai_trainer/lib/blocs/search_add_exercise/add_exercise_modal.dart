// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'package:common/model/muscle_focus_model.dart';

class Exercise {
  final String exerciseName;
  final String imageUrl;
  final String exerciseId;
  final String muscleFocus;
  //mock k lie
  bool isSelected = false;
  Exercise({
    required this.exerciseName,
    required this.imageUrl,
    required this.muscleFocus,
    required this.exerciseId,
    this.isSelected = false,
  });

  factory Exercise.formJson(data) {
    String exerciseName = data['name'] as String;
    String imageUrl = data['imageUrl'] as String;
    String muscleFocus = data['muscleFocus'] as String;
    String exerciseId = data['exerciseId'] as String;

    return Exercise(
        exerciseName: exerciseName,
        imageUrl: imageUrl,
        muscleFocus: muscleFocus,
        exerciseId: exerciseId);
  }
}

class BodyPart {
  final String muscleFocus;
  bool isSelected = false;
  BodyPart({
    required this.muscleFocus,
    this.isSelected = false,
  });
}

class SearchExerciseResponse {
  final List<Exercise>? exercise;
  final List<String> appliedFilters;
  final List<String> applicableFilters;
  final List<ActivityStoreAttributeEntry> activityStoreAttributeEntry;

  SearchExerciseResponse({
    required this.exercise,
    required this.applicableFilters,
    required this.activityStoreAttributeEntry,
    required this.appliedFilters,
  });

  factory SearchExerciseResponse.formJson(data, {String? sessionId}) {
    var exerciseList = data['searchExercises'] as List;
    List<Exercise> processedExercise = [];

    for (var i = 0; i < exerciseList.length; i++) {
      processedExercise.add(Exercise.formJson(exerciseList[i]));
    }

    List<String> applicableFilters = data['applicableFilters'] != null
        ? List<String>.from(data['applicableFilters'])
        : [];
    List<String> appliedFilters = data['appliedFilters'] != null
        ? List<String>.from(data['appliedFilters'])
        : [];

    var activityStoreAttributeEntry =
        (data['exercisesActivityStoreAttributeEntry'] as List?) ?? [];

    List<ActivityStoreAttributeEntry> processedActivityStoreAttributeEntry = [];
    // Populate finalMap and extract sessionId if available
    for (var entry in activityStoreAttributeEntry) {
      var attributeEntry = ActivityStoreAttributeEntry.fromJson(entry);

      processedActivityStoreAttributeEntry.add(attributeEntry);
    }

    return SearchExerciseResponse(
      exercise: processedExercise,
      applicableFilters: applicableFilters,
      activityStoreAttributeEntry: processedActivityStoreAttributeEntry,
      appliedFilters: appliedFilters,
    );
  }
}

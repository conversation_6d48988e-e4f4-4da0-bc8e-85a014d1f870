import 'package:aitrainer/UI/modals/LogModal.dart';
import 'package:aitrainer/UI/modals/PostCheckinModal.dart';
import 'package:aitrainer/UI/screens/search_exercise.dart';
import 'package:aitrainer/blocs/guided_screen/guided_bloc.dart';
import 'package:aitrainer/blocs/log_modal/log_modal_bloc.dart';
import 'package:aitrainer/blocs/post_checkin_modal/post_checkin_bloc.dart';
import 'package:aitrainer/blocs/replace_modal/replace_bloc.dart';
import 'package:aitrainer/blocs/search_add_exercise/search_add_exercise_bloc.dart';
import 'package:common/network/client.dart';
import 'package:enum_to_string/enum_to_string.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'UI/screens/guided_screen.dart';
import 'UI/screens/plan_info_screen.dart';
import 'blocs/weekly_streak/weekly_streak_bloc.dart';
import 'constants/constants.dart';
import 'network/plan_info.dart';
import 'factory/widget_builder.dart' as fitness_widget_builder;
import 'package:common/ui/widget_builder.dart' as common_widget_builder;

class AITrainer {
  String _routeName(RouteNames routeName) {
    return '/${EnumToString.convertToString(routeName)}';
  }

  common_widget_builder.IWidgetBuilder get widgetBuilder {
    return fitness_widget_builder.WidgetBuilder();
  }
  //
  // Map<String, WidgetBuilder> tabRoute(NetworkClient networkClient) {
  //   return {
  //     _routeName(RouteNames.fitnesshub): (_) =>
  //         _clpRoute(networkClient, TabMode.EMBEDDED),
  //   };
  // }
  //
  // Widget _clpRoute(NetworkClient networkClient, TabMode tabMode) {
  //   return BlocProvider(
  //       create: (BuildContext context) =>
  //           PageBloc(repository: CLPRepository(networkClient)),
  //       child: BasicTabPage(
  //           tabPageId: EnumToString.convertToString(RouteNames.fitnesshubclp),
  //           tabMode: tabMode,
  //           appTab: AppTabs.FITNESSHUB,
  //           pageId: EnumToString.convertToString(RouteNames.fitnesshub)));
  // }

  Map<String, WidgetBuilder> getRoutes(
      NetworkClient networkClient, Function onClose) {
    return {
      _routeName(RouteNames.fitnessplaninfopage): (_) =>
          MultiBlocProvider(providers: [
            BlocProvider(
                create: (context) => PlanInfoPageBloc(
                      repository:
                          FitnessPlanRepository(networkClient: networkClient),
                    )),
          ], child: const PlanInfoScreen()),
      _routeName(RouteNames.guidedscreen): (_) => MultiBlocProvider(providers: [
            BlocProvider(
                create: (context) => GuidedPageBloc(
                      repository:
                          FitnessPlanRepository(networkClient: networkClient),
                    )),
            BlocProvider(
                create: (context) => ReplaceModalBloc(
                      repository:
                          FitnessPlanRepository(networkClient: networkClient),
                    )),
            BlocProvider(
                create: (context) => LogModalBloc(
                      repository:
                          FitnessPlanRepository(networkClient: networkClient),
                    ))
          ], child: const GuidedScreen()),
      _routeName(RouteNames.gympostcheckinscreen): (_) => MultiBlocProvider(providers: [
        BlocProvider(
            create: (context) => GuidedPageBloc(
              repository:
              FitnessPlanRepository(networkClient: networkClient),
            )),
        BlocProvider(
            create: (context) => PostCheckinBloc(
              repository:
              FitnessPlanRepository(networkClient: networkClient),
            )),
      ], child: const PostCheckinModal()),
      _routeName(RouteNames.addexercisescreen): (_) =>
          MultiBlocProvider(providers: [
            BlocProvider(
                create: (context) => SearchAddExerciseBloc(
                  repository:
                  FitnessPlanRepository(networkClient: networkClient),
                )),
          ], child: const SearchExerciseScreen()),
    };
  }
}

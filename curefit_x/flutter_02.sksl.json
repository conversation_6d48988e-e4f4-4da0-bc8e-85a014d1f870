{"platform": "ios", "name": "<PERSON><PERSON><PERSON>’s iPhone", "engineRevision": "54a7145303f0dd9d0f93424a2e124eb4abef5091", "data": {"HWQACAAAABAAADAAAIOAAAAADIIAAIRODAAP577774DSAIAA737777YBAAAAAAAAAAAKAAYAAAACAAAAAAACCAYAABIAAAAAAEAAAAAAAAAAAAAAAAAAAAAAADBQCAAAAAAAAAA": "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", "DASAAAAAAAAAAAEAAFQAAIGAAEAOB77776PUEAIBAAAAAABAAAAAAABAMQAAAOQAAAAAAAQAAAABAMQAAAAFAAAAAAAQAAAA7UAAAAAAAAAAAAAAAAAMGAIAAAAAAAAA": "DAAAAExTS1OYAgAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7CglsYXlvdXQob2Zmc2V0PTE2KSBmbG9hdDIgdUF0bGFzU2l6ZUludl9TMDsKCWxheW91dChvZmZzZXQ9MjQpIGhhbGY0IHVDb2xvcl9TMDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBpblBvc2l0aW9uOwpsYXlvdXQobG9jYXRpb24gPSAxKSBpbiB1c2hvcnQyIGluVGV4dHVyZUNvb3JkczsKbGF5b3V0KGxvY2F0aW9uID0gMCkgb3V0IGZsb2F0MiB2VGV4dHVyZUNvb3Jkc19TMDsKbGF5b3V0KGxvY2F0aW9uID0gMSkgZmxhdCBvdXQgZmxvYXQgdlRleEluZGV4X1MwOwp2b2lkIG1haW4oKSAKewoJLy8gUHJpbWl0aXZlIFByb2Nlc3NvciBCaXRtYXBUZXh0CglpbnQgdGV4SWR4ID0gMDsKCWZsb2F0MiB1bm9ybVRleENvb3JkcyA9IGZsb2F0MihpblRleHR1cmVDb29yZHMueCwgaW5UZXh0dXJlQ29vcmRzLnkpOwoJdlRleHR1cmVDb29yZHNfUzAgPSB1bm9ybVRleENvb3JkcyAqIHVBdGxhc1NpemVJbnZfUzA7Cgl2VGV4SW5kZXhfUzAgPSBmbG9hdCh0ZXhJZHgpOwoJZmxvYXQyIF90bXBfMV9pblBvc2l0aW9uID0gaW5Qb3NpdGlvbjsKCXNrX1Bvc2l0aW9uID0gaW5Qb3NpdGlvbi54eTAxOwp9CgAAAACuAgAAbGF5b3V0KG1ldGFsLCBiaW5kaW5nPTApIHNhbXBsZXIyRCB1VGV4dHVyZVNhbXBsZXJfMF9TMDsKbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7CglsYXlvdXQob2Zmc2V0PTE2KSBmbG9hdDIgdUF0bGFzU2l6ZUludl9TMDsKCWxheW91dChvZmZzZXQ9MjQpIGhhbGY0IHVDb2xvcl9TMDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiB2VGV4dHVyZUNvb3Jkc19TMDsKbGF5b3V0KGxvY2F0aW9uID0gMSkgZmxhdCBpbiBmbG9hdCB2VGV4SW5kZXhfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBTdGFnZSAwLCBCaXRtYXBUZXh0CgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdUNvbG9yX1MwOwoJaGFsZjQgdGV4Q29sb3I7Cgl7CgkJdGV4Q29sb3IgPSBzYW1wbGUodVRleHR1cmVTYW1wbGVyXzBfUzAsIHZUZXh0dXJlQ29vcmRzX1MwKTsKCX0KCW91dHB1dENvbG9yX1MwID0gb3V0cHV0Q29sb3JfUzAgKiB0ZXhDb2xvcjsKCWNvbnN0IGhhbGY0IG91dHB1dENvdmVyYWdlX1MwID0gaGFsZjQoMSk7Cgl7CgkJLy8gWGZlciBQcm9jZXNzb3I6IFBvcnRlciBEdWZmCgkJc2tfRnJhZ0NvbG9yID0gb3V0cHV0Q29sb3JfUzAgKiBvdXRwdXRDb3ZlcmFnZV9TMDsKCX0KfQoAAAAAAAABAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFQAAAABAAAAAAAAAAIAAAAdAAAAAAAAAAEAAAANAAAACAAAAAEAAAAMAAAAAAAAAFAAAAABAAAAAQAAAAUAAAAAAAAAAQAAAAUAAAAAAAAAAQAAAP0AAAA=", "AYQA5AADQAAAOAEARAFQJAABBADIB7777777777777777777777767YAAAAAAAAAACAMWAEQYX3QQAIAACAO2VADHYAAAAEALJ3XKFISAAAAABQAAAAEAAAAAA2JTW5YCMAQAAGAEENLQDAAAAAAAACAAAAAAQCGWZJQ36AAAAAAA2W52VKUQAAAAAMAAAAAAAAQAAGQMRXOGTQEAAAABB3I4AZAAAAAQAAAAAAADGUXKV2XEEAQAADAAAAAAAAEAAAEBE5ZRU5RCAAAAAOKFAOLAAAAAAAEAAAAAMQQAIAABIGJ3TDJ2CAAAAAACAAAAAZNEZDO4NHAIAAAAAAAAAEACVYYQBROAMAAAAAIAAAABEBBQ5UOAMQAAAAAAAAAACABKBAABAAAAAFAEEQCEAAAAAAAAAAAQAB5QAYAAAEAAAAAAA4IAPAAACAAAAAAAAAB2AAAAAAACAAAAAEBSAAAABIAAAAAAEAAAAH5AAAAAAAAAAAAAAAAADBQCAAAAAAAAAA": "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", "HVIAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAAAAAAAAFIBUAFS7EOCAAAAAGZJY26AAQAAAA2S5KXK4QAAAAAMAAAAAGBT7RWQMQIAAAACDWRYBSAAAAAAAAAAAQAAAADHMU4NPAAMAAAAFIOVLVOIIBAAAGAAAAADAJ5Y3JGMEAAAAAB3I4AZIAAAAAAAIAAAADEUXLV2XEEAQAADAAAAAAQA64PU3GCAAAAAA4UKA4WAAAAAAAIAAAABSHBT7RWQMQIAAAAAAAAAEACVYYQBROAMAAAAAIAAAABEBBQ5UOAMQAAAAAAAAAACABKBAABAAAAAFAEEQCEAAAAAAAAAAAAAOQAAAAAAAQAAAABAMQAACQAAAAAAIAAAAP2AAAAAAAAAAAAAAAAAGDAEBAAAAAAAAA": "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", "HVIAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAAAAAAAAFIBVWKMG7QAAAAAAGVXOVKVEAAAAADAAAAAAAAEAAAUEZEVBAOCIAAAAIO2HAGIAAAAEAAAAAAAAZVF2VOVZBAEAAAYAAAAAAABAAABAGLFQIDUSAAAAADSRIDSYAAAAAABAAAAADEEACAAAKAMSLQQHBEAAAAAAQAAAAGJJJSJKCA4EQAAAAAAAABAAVOGEAMLQDAAAAACAAAAAJAIMHNDQDEAAAAAAAAAAAQACYBEABQAAQAAAA7X7777YQGIAAAACGAAAAAAIAAAAAAAAAAAAAAAAAEAAAAAADAABAAAAAAAAA": "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", "HUQACAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAAKAAYAAAACAAAAAAACCAYAABIAAAAAAEAAAAH5AAAAAAAAAAAAAAAAADBQCAAAAAAAAAA": "DAAAAExTS1NOAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGhhbGY0IGNvbG9yOwpsYXlvdXQobG9jYXRpb24gPSAwKSBvdXQgaGFsZjQgdmNvbG9yX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gUHJpbWl0aXZlIFByb2Nlc3NvciBRdWFkUGVyRWRnZUFBR2VvbWV0cnlQcm9jZXNzb3IKCXZjb2xvcl9TMCA9IGNvbG9yOwoJc2tfUG9zaXRpb24gPSBwb3NpdGlvbi54eTAxOwp9CgAAAAAAAH0BAABsYXlvdXQgKG1ldGFsLCBiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGhhbGY0IHZjb2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzA7CglvdXRwdXRDb2xvcl9TMCA9IHZjb2xvcl9TMDsKCWNvbnN0IGhhbGY0IG91dHB1dENvdmVyYWdlX1MwID0gaGFsZjQoMSk7Cgl7CgkJLy8gWGZlciBQcm9jZXNzb3I6IFBvcnRlciBEdWZmCgkJc2tfRnJhZ0NvbG9yID0gb3V0cHV0Q29sb3JfUzAgKiBvdXRwdXRDb3ZlcmFnZV9TMDsKCX0KfQoAAAAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABUAAAAAQAAAAAAAAACAAAAHQAAAAAAAAABAAAACQAAAAgAAAABAAAADAAAAAAAAABQAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAD9AAAA", "FAAQMYAAMAAAEADAAABAEYAAAICIAB5AABQAAAQAMAAAEATAAABAIIGAAEDCBYQCA4AAAAAAAA5AAAAAAABAAAAACAZAAAAAKAAAAAABAAAAB7IAAAAAAAAAAAAAAAAAYMAQAAAAAAAAA": "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", "HVIAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAAAAAAAAFIBVWKMG7QAAAAAAGVXOVKVEAAAAADAAAAAAAAEAAAUEZEVBAOCIAAAAIO2HAGIAAAAEAAAAAAAAZVF2VOVZBAEAAAYAAAAAAABAAABAGLFQIDUSAAAAADSRIDSYAAAAAABAAAAADEEACAAAKAMSLQQHBEAAAAAAQAAAAGJJJSJKCA4EQAAAAAAAABAAVOGEAMLQDAAAAACAAAAAJAIMHNDQDEAAAAAAAAAAAAAOQAAAAAAAQAAAABAMQAAAAKAAAAAABAAAAAAAAAAAAAAAAAAQAAAAAYEAQEAAAAAAAA": "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", "CIAQAAAAIABCQAAYQAAAAGEAQAAAQ4AAAEMLQQABDCAMAAIIAAAAAABCAIALADAALF6I6EAAAAANQTRV4ABQAAAAVB2VOVZBAEAAAYAAAAAEAZMWBAOSIAAAAAOKFAOLAAAAAAAAAACAAAAAMRSDXVMAB4AAAAFA2ZOV3BIEAAAIAAIAAAAJKWJCOSIAAAAAOCEAMLQDAAAAACAAAAAJBEK2O52RKEQAAAAAMAAAABKGNCOQIEBAAAGAEENLQDAAAAAEAAAAABAAMK5TITUCAAIAAAAAAAAAWARA5UOAMUAAAAAAAEAAAABS4QIA2XAGAAAAAAAAAAALBAQAAAAQAAAAGQCEIBAAAAAAAAAAAAYAADIAAAAB3BX76AKAAAAAAMAAAAAIAAAABSCIQ7QT6PAFAAAMAAAAAAAAAAAAAAADUAAAAAAAEAAAAAIDEAAAKAAAAAABAAAAB7IAAAAAAAAAAAAAAAAAYMAQEAAAAAAAA": "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", "IYBAAAAABAAACAABBYAAAKAAAMAAGEAAAABRAEAAAEHCAAAAAAAABCAAAAAABAEQAEAAAACQAAAAAAIAAAAP2AAAAAAQAAAAGAAAAAACAAAAAAAAAAAA": "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", "HVJAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAABAAAAAABBAMADSAB4QAAAAAEAAAAAAHIAAAAAAAIAAAAAQGIAAAAAAFAAAAAAAQAAAA7UAAAAAAAAAAAAAAAAAMGAICAAAAAAAA": "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", "AYQA5AADQAAAOAEARAFQJAABBADIB7777777777777777777777767YAAAAAAAAAAAAMWAEQYX3QQAIAACAO2VADHYAAAAEALJ3XKFISAAAAABQAAAAEAAAAAA2JTW5YCMAQAAGAEENLQDAAAAAAAACAAAAAAQCGWZJQ36AAAAAAA2W52VKUQAAAAAMAAAAAAAAQAAGQMRXOGTQEAAAABB3I4AZAAAAAQAAAAAAADGUXKV2XEEAQAADAAAAAAAAEAAAEBE5ZRU5RCAAAAAOKFAOLAAAAAAAEAAAAAMQQAIAABIGJ3TDJ2CAAAAAACAAAAAZNEZDO4NHAIAAAAAAAAAEACVYYQBROAMAAAAAIAAAABEBBQ5UOAMQAAAAAAAAAACABKBAABAAAAAFAEEQCEAAAAAAAAAAAQAB5QAYAAAEAAAAAADIACAAAAAIAAAAAQCIACACQAAAAAAIAAAAAAAAAAAAAAAAAAAAAAAGDAEAAAAAAAAAA": "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", "HUJAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAQAAAAAAQQGACQAGAAAAAQAAAAAAAQQGAAAKAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAYMAQEAAAAAAAA": "DAAAAExTS1NkAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGZsb2F0MiBsb2NhbENvb3JkOwpsYXlvdXQobG9jYXRpb24gPSAwKSBvdXQgZmxvYXQyIHZsb2NhbENvb3JkX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gUHJpbWl0aXZlIFByb2Nlc3NvciBRdWFkUGVyRWRnZUFBR2VvbWV0cnlQcm9jZXNzb3IKCXZsb2NhbENvb3JkX1MwID0gbG9jYWxDb29yZDsKCXNrX1Bvc2l0aW9uID0gcG9zaXRpb24ueHkwMTsKfQoAAAAALgIAAGxheW91dChtZXRhbCwgYmluZGluZz0wKSBzYW1wbGVyMkQgdVRleHR1cmVTYW1wbGVyXzBfUzA7CmxheW91dCAobWV0YWwsIGJpbmRpbmc9MCkgdW5pZm9ybSB1bmlmb3JtQnVmZmVyCnsKCWxheW91dChvZmZzZXQ9MCkgZmxvYXQ0IHNrX1JUQWRqdXN0Owp9CjsKbGF5b3V0KGxvY2F0aW9uID0gMCkgaW4gZmxvYXQyIHZsb2NhbENvb3JkX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMCA9IGhhbGY0KDEpOwoJZmxvYXQyIHRleENvb3JkOwoJdGV4Q29vcmQgPSB2bG9jYWxDb29yZF9TMDsKCW91dHB1dENvbG9yX1MwID0gKGJsZW5kX21vZHVsYXRlKHNhbXBsZSh1VGV4dHVyZVNhbXBsZXJfMF9TMCwgdGV4Q29vcmQpLCBoYWxmNCgxKSkpOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAVAAAAAEAAAAAAAAAAgAAAB0AAAAAAAAAAQAAAB0AAAAIAAAAAQAAABAAAAAAAAAAUAAAAAEAAAABAAAABQAAAAAAAAABAAAABQAAAAAAAAABAAAAAAAAAA==", "HWIAAAAAAAUAADAAAIOAAAAADIIAB7X7777QGHAYAD7P7777777777YBAAAAAAAAAAACYA2AC3PSGBAAAAALMUYN7AAAAAAANLO5KVKIAAAAAGAAAAAAAAIAADIGI3XDJYCAAAAAQ5UOAMQAAAAAAAAAAEAAAAAZ3FHDLYADAAAABKDVK5LSCAIAABQAAAAAAACAAACASO4Y2OYRAAAAAHFCQHFQAAAAAABAAAAAMSSNMXK5QUCAAAEAAEAAAAAQAAAAATPGG3XEIAAAABYIQBROAMAAAAAQAAAABSCABAAABABGOMNXOIQAAAAAIAAAADEERE5ZRU5RCAAAAAAAAAAAK3CCCGVYBQAAAABAAAAAAQEGDSRIDSYAAAAAAAAAAAAFMEAAEAAAAAEAQ2AIQAAAAAAAAAAAAAHCADYAAAQAAAAAABAAOAAAABAAAAAAABBAMACQAAAAAAIAAAAP2AAAAAAAAAAAAAAAAAGDAEAAAAAAAAAA": "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", "IYBAAAAABAAACAABBYAAAKAAAMAAGEAAAABRAEAAAEHCAAAAAAAABCAAAAAABAEQAEAAAACGAAAAAAIAAAAP2AAAAAAQAAAAEAAAAAACAAAAAAAAAAAA": "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", "HUJAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAQAAAAAAQQGACQAGAAAAAQAAAAAAAQQGAAAIYAAAAABAAAAB7IAAAAAAAAAAAAAAAAAYMAQEAAAAAAAA": "DAAAAExTS1NkAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGZsb2F0MiBsb2NhbENvb3JkOwpsYXlvdXQobG9jYXRpb24gPSAwKSBvdXQgZmxvYXQyIHZsb2NhbENvb3JkX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gUHJpbWl0aXZlIFByb2Nlc3NvciBRdWFkUGVyRWRnZUFBR2VvbWV0cnlQcm9jZXNzb3IKCXZsb2NhbENvb3JkX1MwID0gbG9jYWxDb29yZDsKCXNrX1Bvc2l0aW9uID0gcG9zaXRpb24ueHkwMTsKfQoAAAAALgIAAGxheW91dChtZXRhbCwgYmluZGluZz0wKSBzYW1wbGVyMkQgdVRleHR1cmVTYW1wbGVyXzBfUzA7CmxheW91dCAobWV0YWwsIGJpbmRpbmc9MCkgdW5pZm9ybSB1bmlmb3JtQnVmZmVyCnsKCWxheW91dChvZmZzZXQ9MCkgZmxvYXQ0IHNrX1JUQWRqdXN0Owp9CjsKbGF5b3V0KGxvY2F0aW9uID0gMCkgaW4gZmxvYXQyIHZsb2NhbENvb3JkX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMCA9IGhhbGY0KDEpOwoJZmxvYXQyIHRleENvb3JkOwoJdGV4Q29vcmQgPSB2bG9jYWxDb29yZF9TMDsKCW91dHB1dENvbG9yX1MwID0gKGJsZW5kX21vZHVsYXRlKHNhbXBsZSh1VGV4dHVyZVNhbXBsZXJfMF9TMCwgdGV4Q29vcmQpLCBoYWxmNCgxKSkpOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAVAAAAAEAAAAAAAAAAgAAAB0AAAAAAAAAAQAAAB0AAAAIAAAAAQAAABAAAAAAAAAARgAAAAEAAAABAAAABQAAAAAAAAABAAAABQAAAAAAAAABAAAA/QAAAA==", "I4EAAAIAAEHAAAAAAAAAAAAAAAAIQAAAAAAIBEABAAAAAUAAAAAACAAAAD6QAAAAAEAAAABAAAAAAAQAAAAAAAAAAA": "DAAAAExTS1M2AgAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7CglsYXlvdXQob2Zmc2V0PTE2KSBmbG9hdDQgdWFmZmluZU1hdHJpeF9TMDsKCWxheW91dChvZmZzZXQ9MzIpIGZsb2F0MiB1dHJhbnNsYXRlX1MwOwoJbGF5b3V0KG9mZnNldD00MCkgaGFsZjQgdWNvbG9yX1MwOwp9CjsKbGF5b3V0KGxvY2F0aW9uID0gMCkgaW4gZmxvYXQyIGlucHV0UG9pbnQ7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIHRlc3NlbGxhdGVfU2ltcGxlVHJpYW5nbGVTaGFkZXIKCWZsb2F0MngyIEFGRklORV9NQVRSSVggPSBmbG9hdDJ4Mih1YWZmaW5lTWF0cml4X1MwLnh5LCB1YWZmaW5lTWF0cml4X1MwLnp3KTsKCWZsb2F0MiBUUkFOU0xBVEUgPSB1dHJhbnNsYXRlX1MwOwoJZmxvYXQyIGxvY2FsY29vcmQgPSBpbnB1dFBvaW50OwoJZmxvYXQyIHZlcnRleHBvcyA9IEFGRklORV9NQVRSSVggKiBsb2NhbGNvb3JkICsgVFJBTlNMQVRFOwoJc2tfUG9zaXRpb24gPSB2ZXJ0ZXhwb3MueHkwMTsKfQoAAAAAAACKAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7CglsYXlvdXQob2Zmc2V0PTE2KSBmbG9hdDQgdWFmZmluZU1hdHJpeF9TMDsKCWxheW91dChvZmZzZXQ9MzIpIGZsb2F0MiB1dHJhbnNsYXRlX1MwOwoJbGF5b3V0KG9mZnNldD00MCkgaGFsZjQgdWNvbG9yX1MwOwp9CjsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIHRlc3NlbGxhdGVfU2ltcGxlVHJpYW5nbGVTaGFkZXIKCWhhbGY0IG91dHB1dENvbG9yX1MwID0gdWNvbG9yX1MwOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogRGlzYWJsZSBDb2xvcgoJfQp9CgAAAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMAAAAAEAAAAAAAAAAQAAAB0AAAAAAAAAAQAAAAgAAAAAAAAAUAAAAAAAAAAAAAAA/QAAAA==", "HVJAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAABAAAAAABBAMABAAOAAAABAAAAAAABBAMAAAKAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAYMAQEAAAAAAAA": "DAAAAExTS1PMAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGhhbGY0IGNvbG9yOwpsYXlvdXQobG9jYXRpb24gPSAyKSBpbiBmbG9hdDIgbG9jYWxDb29yZDsKbGF5b3V0KGxvY2F0aW9uID0gMCkgZmxhdCBvdXQgaGFsZjQgdmNvbG9yX1MwOwpsYXlvdXQobG9jYXRpb24gPSAxKSBvdXQgZmxvYXQyIHZsb2NhbENvb3JkX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gUHJpbWl0aXZlIFByb2Nlc3NvciBRdWFkUGVyRWRnZUFBR2VvbWV0cnlQcm9jZXNzb3IKCXZjb2xvcl9TMCA9IGNvbG9yOwoJdmxvY2FsQ29vcmRfUzAgPSBsb2NhbENvb3JkOwoJc2tfUG9zaXRpb24gPSBwb3NpdGlvbi54eTAxOwp9CgAAAAB0AgAAbGF5b3V0KG1ldGFsLCBiaW5kaW5nPTApIHNhbXBsZXIyRCB1VGV4dHVyZVNhbXBsZXJfMF9TMDsKbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gZmxvYXQyIHZsb2NhbENvb3JkX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmNvbG9yX1MwOwoJZmxvYXQyIHRleENvb3JkOwoJdGV4Q29vcmQgPSB2bG9jYWxDb29yZF9TMDsKCW91dHB1dENvbG9yX1MwID0gKGJsZW5kX21vZHVsYXRlKHNhbXBsZSh1VGV4dHVyZVNhbXBsZXJfMF9TMCwgdGV4Q29vcmQpLCBvdXRwdXRDb2xvcl9TMCkpOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAABAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGAAAAABAAAAAAAAAAMAAAAdAAAAAAAAAAEAAAAJAAAACAAAAAEAAAAdAAAADAAAAAEAAAAUAAAAAAAAAFAAAAABAAAAAQAAAAUAAAAAAAAAAQAAAAUAAAAAAAAAAQAAAAAAAAA=", "HVIAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAAAAAAAAFIBVWKMG7QAAAAAAGVXOVKVEAAAAADAAAAAAAAEAAAUEZEVBAOCIAAAAIO2HAGIAAAAEAAAAAAAAZVF2VOVZBAEAAAYAAAAAAABAAABAGLFQIDUSAAAAADSRIDSYAAAAAABAAAAADEEACAAAKAMSLQQHBEAAAAAAQAAAAGJJJSJKCA4EQAAAAAAAABAAVOGEAMLQDAAAAACAAAAAJAIMHNDQDEAAAAAAAAAAAQACYBEABQABQAAAA7X7777YQGIAAAACQAAAAAAIAAAAAAAAAAAAAAAAAEAAAAAADAABAAAAAAAAA": "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", "HVIAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAAAAAAAAFIBVWKMG7QAAAAAAGVXOVKVEAAAAADAAAAAGQMRXOGTQEAAAABB3I4AZAAAAAQAAAAAAADGUXKV2XEEAQAADAAAAAAQETXGGTWEIAAAABZIUBZMAAAAAAAQAAAADEWDE5ZRU5BAAAAAAAAAAAAK7CCAGVYBQAAAABAAAAAAQEGDWRYBSQAAAAAAAAAAAAHIAAAAAAAIAAAAAQGIAFAAAAAAAQAAAAAAAAAAAAAAAAAIAAAAAICAACAAAAAAAA": "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", "HUQAAAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAAHEADZAAAAAAIAAAAAAOQAAAAAAAQAAAABAMQAAAAAAUAAAAAACAAAAAAAAAAAAAAAAAAAAAAABQYBAIAAAAAAAA": "DAAAAExTS1OpAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7CglsYXlvdXQob2Zmc2V0PTE2KSBmbG9hdDQgdWlubmVyUmVjdF9TMTsKCWxheW91dChvZmZzZXQ9MzIpIGhhbGYyIHVyYWRpdXNQbHVzSGFsZl9TMTsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDApIGZsYXQgb3V0IGhhbGY0IHZjb2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFByaW1pdGl2ZSBQcm9jZXNzb3IgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgl2Y29sb3JfUzAgPSBjb2xvcjsKCXNrX1Bvc2l0aW9uID0gcG9zaXRpb24ueHkwMTsKfQoAAAAAAAAAJwMAAGxheW91dCAobWV0YWwsIGJpbmRpbmc9MCkgdW5pZm9ybSB1bmlmb3JtQnVmZmVyCnsKCWxheW91dChvZmZzZXQ9MCkgZmxvYXQ0IHNrX1JUQWRqdXN0OwoJbGF5b3V0KG9mZnNldD0xNikgZmxvYXQ0IHVpbm5lclJlY3RfUzE7CglsYXlvdXQob2Zmc2V0PTMyKSBoYWxmMiB1cmFkaXVzUGx1c0hhbGZfUzE7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKaGFsZjQgQ2lyY3VsYXJSUmVjdF9TMShoYWxmNCBfaW5wdXQpIAp7CglmbG9hdDIgZHh5MCA9IHVpbm5lclJlY3RfUzEuTFQgLSBza19GcmFnQ29vcmQueHk7CglmbG9hdDIgZHh5MSA9IHNrX0ZyYWdDb29yZC54eSAtIHVpbm5lclJlY3RfUzEuUkI7CglmbG9hdDIgZHh5ID0gbWF4KG1heChkeHkwLCBkeHkxKSwgMC4wKTsKCWhhbGYgYWxwaGEgPSBoYWxmKHNhdHVyYXRlKHVyYWRpdXNQbHVzSGFsZl9TMS54IC0gbGVuZ3RoKGR4eSkpKTsKCXJldHVybiBfaW5wdXQgKiBhbHBoYTsKfQp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmNvbG9yX1MwOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCWhhbGY0IG91dHB1dF9TMTsKCW91dHB1dF9TMSA9IENpcmN1bGFyUlJlY3RfUzEob3V0cHV0Q292ZXJhZ2VfUzApOwoJewoJCS8vIFhmZXIgUHJvY2Vzc29yOiBQb3J0ZXIgRHVmZgoJCXNrX0ZyYWdDb2xvciA9IG91dHB1dENvbG9yX1MwICogb3V0cHV0X1MxOwoJfQp9CgABAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABUAAAAAQAAAAAAAAACAAAAHQAAAAAAAAABAAAACQAAAAgAAAABAAAADAAAAAAAAABQAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAAAAAAA", "HVIAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAAAAAAAAFIBVWKMG7QAAAAAAAAAQCAAAAAVQEEAQAAAAAQCDAAQQGAAAAEAAAAAAHIAAAAAAAIAAAAAQGIAAAAAAEMAAAAAAQAAAAAAAAAAAAAAAAAIAAAAAMGAICAAAAAAAA": "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", "HUQAAAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAAHEADZAAAAAAIAAAAAAOQAAAAAAAQAAAABAMQAAAAAAUAAAAAACAAAAD6QAAAAAAAAAAAAAAAABQYBAIAAAAAAAA": "DAAAAExTS1OpAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7CglsYXlvdXQob2Zmc2V0PTE2KSBmbG9hdDQgdWlubmVyUmVjdF9TMTsKCWxheW91dChvZmZzZXQ9MzIpIGhhbGYyIHVyYWRpdXNQbHVzSGFsZl9TMTsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDApIGZsYXQgb3V0IGhhbGY0IHZjb2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFByaW1pdGl2ZSBQcm9jZXNzb3IgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgl2Y29sb3JfUzAgPSBjb2xvcjsKCXNrX1Bvc2l0aW9uID0gcG9zaXRpb24ueHkwMTsKfQoAAAAAAAAAJwMAAGxheW91dCAobWV0YWwsIGJpbmRpbmc9MCkgdW5pZm9ybSB1bmlmb3JtQnVmZmVyCnsKCWxheW91dChvZmZzZXQ9MCkgZmxvYXQ0IHNrX1JUQWRqdXN0OwoJbGF5b3V0KG9mZnNldD0xNikgZmxvYXQ0IHVpbm5lclJlY3RfUzE7CglsYXlvdXQob2Zmc2V0PTMyKSBoYWxmMiB1cmFkaXVzUGx1c0hhbGZfUzE7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKaGFsZjQgQ2lyY3VsYXJSUmVjdF9TMShoYWxmNCBfaW5wdXQpIAp7CglmbG9hdDIgZHh5MCA9IHVpbm5lclJlY3RfUzEuTFQgLSBza19GcmFnQ29vcmQueHk7CglmbG9hdDIgZHh5MSA9IHNrX0ZyYWdDb29yZC54eSAtIHVpbm5lclJlY3RfUzEuUkI7CglmbG9hdDIgZHh5ID0gbWF4KG1heChkeHkwLCBkeHkxKSwgMC4wKTsKCWhhbGYgYWxwaGEgPSBoYWxmKHNhdHVyYXRlKHVyYWRpdXNQbHVzSGFsZl9TMS54IC0gbGVuZ3RoKGR4eSkpKTsKCXJldHVybiBfaW5wdXQgKiBhbHBoYTsKfQp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmNvbG9yX1MwOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCWhhbGY0IG91dHB1dF9TMTsKCW91dHB1dF9TMSA9IENpcmN1bGFyUlJlY3RfUzEob3V0cHV0Q292ZXJhZ2VfUzApOwoJewoJCS8vIFhmZXIgUHJvY2Vzc29yOiBQb3J0ZXIgRHVmZgoJCXNrX0ZyYWdDb2xvciA9IG91dHB1dENvbG9yX1MwICogb3V0cHV0X1MxOwoJfQp9CgABAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABUAAAAAQAAAAAAAAACAAAAHQAAAAAAAAABAAAACQAAAAgAAAABAAAADAAAAAAAAABQAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAD9AAAA", "DAQAAAAAAAAAAAAAAJQAAIGAAEACBYQCAGAEFAIBAAAAAABAAAAAAAAAAAAAAOQAAAAAAAQAAAABAMQAAAAEMAAAAAAQAAAA7UAAAAAAAAAAAAAAAAAMGAIAAAAAAAAA": "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", "FAAQMYAAMAAAEADAAABAEYAAAICIAB5AABQAAAQAMAAAEATAAABAIIGAAEDCBYQCA4AAAAAAAA5AAAAAAABAAAAACAZAAAAAKAAAAAABAAAAB7IAAAAAAAAAAAQAAAAAYMAQAAAAAAAAA": "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", "HUQACAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAAHEADZAAAAAAIAAAAAAOQAAAAAAAQAAAABAMQAAAAAAUAAAAAACAAAAD6QAAAAAAAAAAAAAAAABQYBAAAAAAAAAA": "DAAAAExTS1OkAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7CglsYXlvdXQob2Zmc2V0PTE2KSBmbG9hdDQgdWlubmVyUmVjdF9TMTsKCWxheW91dChvZmZzZXQ9MzIpIGhhbGYyIHVyYWRpdXNQbHVzSGFsZl9TMTsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDApIG91dCBoYWxmNCB2Y29sb3JfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmNvbG9yX1MwID0gY29sb3I7Cglza19Qb3NpdGlvbiA9IHBvc2l0aW9uLnh5MDE7Cn0KAAAAACIDAABsYXlvdXQgKG1ldGFsLCBiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKCWxheW91dChvZmZzZXQ9MTYpIGZsb2F0NCB1aW5uZXJSZWN0X1MxOwoJbGF5b3V0KG9mZnNldD0zMikgaGFsZjIgdXJhZGl1c1BsdXNIYWxmX1MxOwp9CjsKbGF5b3V0KGxvY2F0aW9uID0gMCkgaW4gaGFsZjQgdmNvbG9yX1MwOwpoYWxmNCBDaXJjdWxhclJSZWN0X1MxKGhhbGY0IF9pbnB1dCkgCnsKCWZsb2F0MiBkeHkwID0gdWlubmVyUmVjdF9TMS5MVCAtIHNrX0ZyYWdDb29yZC54eTsKCWZsb2F0MiBkeHkxID0gc2tfRnJhZ0Nvb3JkLnh5IC0gdWlubmVyUmVjdF9TMS5SQjsKCWZsb2F0MiBkeHkgPSBtYXgobWF4KGR4eTAsIGR4eTEpLCAwLjApOwoJaGFsZiBhbHBoYSA9IGhhbGYoc2F0dXJhdGUodXJhZGl1c1BsdXNIYWxmX1MxLnggLSBsZW5ndGgoZHh5KSkpOwoJcmV0dXJuIF9pbnB1dCAqIGFscGhhOwp9CnZvaWQgbWFpbigpIAp7CgkvLyBTdGFnZSAwLCBRdWFkUGVyRWRnZUFBR2VvbWV0cnlQcm9jZXNzb3IKCWhhbGY0IG91dHB1dENvbG9yX1MwOwoJb3V0cHV0Q29sb3JfUzAgPSB2Y29sb3JfUzA7Cgljb25zdCBoYWxmNCBvdXRwdXRDb3ZlcmFnZV9TMCA9IGhhbGY0KDEpOwoJaGFsZjQgb3V0cHV0X1MxOwoJb3V0cHV0X1MxID0gQ2lyY3VsYXJSUmVjdF9TMShvdXRwdXRDb3ZlcmFnZV9TMCk7Cgl7CgkJLy8gWGZlciBQcm9jZXNzb3I6IFBvcnRlciBEdWZmCgkJc2tfRnJhZ0NvbG9yID0gb3V0cHV0Q29sb3JfUzAgKiBvdXRwdXRfUzE7Cgl9Cn0KAAABAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABUAAAAAQAAAAAAAAACAAAAHQAAAAAAAAABAAAACQAAAAgAAAABAAAADAAAAAAAAABQAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAD9AAAA", "DAQAAAAAAAAAAAAAAJQAAIGAAEACBYQCAGAEFAIBAAAAAABAAAAAAAAAAAAAAOQAAAAAAAQAAAABAMQAAAAFAAAAAAAQAAAAAAAAAAAAAAAAAIAAAAAMGAIAAAAAAAAA": "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", "HUJAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAQAAAAAAQQGABZAA6IAAAAACAAAAAADUAAAAAAAEAAAAAIDEAAAAAAFAAAAAAAQAAAAAAAAAAAAAAAAAAAAAAAMGAICAAAAAAAA": "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", "HUJAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAQAAAAAAQQGACQAGAAAAAQAAAAAAAQQGAAAKAAAAAABAAAAB7IAAAAAAAAAAAAAAAAAYMAQAAAAAAAAA": "DAAAAExTS1NkAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGZsb2F0MiBsb2NhbENvb3JkOwpsYXlvdXQobG9jYXRpb24gPSAwKSBvdXQgZmxvYXQyIHZsb2NhbENvb3JkX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gUHJpbWl0aXZlIFByb2Nlc3NvciBRdWFkUGVyRWRnZUFBR2VvbWV0cnlQcm9jZXNzb3IKCXZsb2NhbENvb3JkX1MwID0gbG9jYWxDb29yZDsKCXNrX1Bvc2l0aW9uID0gcG9zaXRpb24ueHkwMTsKfQoAAAAALgIAAGxheW91dChtZXRhbCwgYmluZGluZz0wKSBzYW1wbGVyMkQgdVRleHR1cmVTYW1wbGVyXzBfUzA7CmxheW91dCAobWV0YWwsIGJpbmRpbmc9MCkgdW5pZm9ybSB1bmlmb3JtQnVmZmVyCnsKCWxheW91dChvZmZzZXQ9MCkgZmxvYXQ0IHNrX1JUQWRqdXN0Owp9CjsKbGF5b3V0KGxvY2F0aW9uID0gMCkgaW4gZmxvYXQyIHZsb2NhbENvb3JkX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMCA9IGhhbGY0KDEpOwoJZmxvYXQyIHRleENvb3JkOwoJdGV4Q29vcmQgPSB2bG9jYWxDb29yZF9TMDsKCW91dHB1dENvbG9yX1MwID0gKGJsZW5kX21vZHVsYXRlKHNhbXBsZSh1VGV4dHVyZVNhbXBsZXJfMF9TMCwgdGV4Q29vcmQpLCBoYWxmNCgxKSkpOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAVAAAAAEAAAAAAAAAAgAAAB0AAAAAAAAAAQAAAB0AAAAIAAAAAQAAABAAAAAAAAAAUAAAAAEAAAABAAAABQAAAAAAAAABAAAABQAAAAAAAAABAAAA/QAAAA==", "HWIAAAAAAAUAADAAAIOAAAAADIIAB7X7777QGHAYAD7P7777777777YBAAAAAAAAAAACYA2AC3PSGBAAAAALMUYN7AAAAAAANLO5KVKIAAAAAGAAAAAAAAIAADIGI3XDJYCAAAAAQ5UOAMQAAAAAAAAAAEAAAAAZ3FHDLYADAAAABKDVK5LSCAIAABQAAAAAAACAAACASO4Y2OYRAAAAAHFCQHFQAAAAAABAAAAAMSSNMXK5QUCAAAEAAEAAAAAQAAAAATPGG3XEIAAAABYIQBROAMAAAAAQAAAABSCABAAABABGOMNXOIQAAAAAIAAAADEERE5ZRU5RCAAAAAAAAAAAK3CCCGVYBQAAAABAAAAAAQEGDSRIDSYAAAAAAAAAAAAFMEAAEAAAAAEAQ2AIQAAAAAAAAAAAAAHCADYAAAQAAAAAABAAOAAAABAAAAAAABBAMACQAAAAAAIAAAAAAAAAAAAAAAAAAAAAAAGDAEAAAAAAAAAA": "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", "AYQA5AADQAAAOAEARAFQJAABBADIB7777777777777777777777767YAAAAAAAAAACAAYQADAAAEBB7BH46AKAAAYAAAAAAAAIAAAABS2JQ7QD2PAEAAAMAAAAAAAAAAAAAIADQAAAAIAAAAAAAIIDAAABIAAAAAAEAAAAH5AAAAAAAAAAACAAAAADBQCAAAAAAAAAA": "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", "HUQAAAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAAKAAYAAAACAAAAAAACCAYAABIAAAAAAEAAAAH5AAAAAAAAAAAAAAAAADBQCAQAAAAAAAA": "DAAAAExTS1NTAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGhhbGY0IGNvbG9yOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IG91dCBoYWxmNCB2Y29sb3JfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmNvbG9yX1MwID0gY29sb3I7Cglza19Qb3NpdGlvbiA9IHBvc2l0aW9uLnh5MDE7Cn0KAAAAAACCAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzA7CglvdXRwdXRDb2xvcl9TMCA9IHZjb2xvcl9TMDsKCWNvbnN0IGhhbGY0IG91dHB1dENvdmVyYWdlX1MwID0gaGFsZjQoMSk7Cgl7CgkJLy8gWGZlciBQcm9jZXNzb3I6IFBvcnRlciBEdWZmCgkJc2tfRnJhZ0NvbG9yID0gb3V0cHV0Q29sb3JfUzAgKiBvdXRwdXRDb3ZlcmFnZV9TMDsKCX0KfQoAAAAAAAABAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFQAAAABAAAAAAAAAAIAAAAdAAAAAAAAAAEAAAAJAAAACAAAAAEAAAAMAAAAAAAAAFAAAAABAAAAAQAAAAUAAAAAAAAAAQAAAAUAAAAAAAAAAQAAAP0AAAA=", "HVIAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAAAAAAAAFIBVWKMG7QAAAAAAAAAQCAAAAAVQEEAQAAAAAQCDAAQQGAAAAEAAAAAAHIAAAAAAAIAAAAAQGIAAAAAAFAAAAAAAQAAAAAAAAAAAAAAAAAIAAAAAMGAICAAAAAAAA": "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", "CIAQAAAAIABCQAAYQAAAAGEAQAAAQ4AAAEMLQQABDCAMAAIIAAAAAABCAIAKQDAALF6I6EAAAAANQTRV4ABQAAAAVB2VOVZBAEAAAYAAAAAEAZMWBAOSIAAAAAOKFAOLAAAAAAAAAACAAAAAMRSDXVMAB4AAAAFA2ZOV3BIEAAAIAAIAAAAJKWJCOSIAAAAAOCEAMLQDAAAAACAAAAAJBEK2O52RKEQAAAAAMAAAABKGNCOQIEBAAAGAEENLQDAAAAAEAAAAABAAMK5TITUCAAIAAAAAAAAAWARA5UOAMUAAAAAAAEAAAABS4QIA2XAGAAAAAAAAAAALBAQAAAAQAAAAGQCEIBAAAAAAAAAAACQAGAAAAAQAAAAAAAQQGAAAKAAAAAABAAAAB7IAAAAAAAAAAAAAAAAAYMAQEAAAAAAAA": "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", "DAQAAAAAAAAAAAAAAJQAAIGAAEACBYQCAGAEFAIBAAAAAABAAAAAAAAAAAAAAOQAAAAAAAQAAAABAMQAAAAEMAAAAAAQAAAAAAAAAAAAAAAAAAAAAAAMGAIAAAAAAAAA": "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", "HUQAAAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAABAAIAAAAAAIIDAAAEMAAAAAAQAAAA7UAAAAABAAAAAAAAAAAAEAAAAAAAAAAA": "DAAAAExTS1NTAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGhhbGY0IGNvbG9yOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IG91dCBoYWxmNCB2Y29sb3JfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmNvbG9yX1MwID0gY29sb3I7Cglza19Qb3NpdGlvbiA9IHBvc2l0aW9uLnh5MDE7Cn0KAAAAAABPAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzA7CglvdXRwdXRDb2xvcl9TMCA9IHZjb2xvcl9TMDsKCWNvbnN0IGhhbGY0IG91dHB1dENvdmVyYWdlX1MwID0gaGFsZjQoMSk7Cgl7CgkJLy8gWGZlciBQcm9jZXNzb3I6IERpc2FibGUgQ29sb3IKCX0KfQoAAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPAAAAAEAAAAAAAAAAgAAAB0AAAAAAAAAAQAAAAkAAAAIAAAAAQAAAAwAAAAAAAAARgAAAAAAAAAAAAAA/QAAAA==", "HUQACAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAAKAAYAAAACAAAAAAACCAYAABDAAAAAAEAAAAH5AAAAAAAAAAAAAAAAADBQCAAAAAAAAAA": "DAAAAExTS1NOAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGhhbGY0IGNvbG9yOwpsYXlvdXQobG9jYXRpb24gPSAwKSBvdXQgaGFsZjQgdmNvbG9yX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gUHJpbWl0aXZlIFByb2Nlc3NvciBRdWFkUGVyRWRnZUFBR2VvbWV0cnlQcm9jZXNzb3IKCXZjb2xvcl9TMCA9IGNvbG9yOwoJc2tfUG9zaXRpb24gPSBwb3NpdGlvbi54eTAxOwp9CgAAAAAAAH0BAABsYXlvdXQgKG1ldGFsLCBiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGhhbGY0IHZjb2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzA7CglvdXRwdXRDb2xvcl9TMCA9IHZjb2xvcl9TMDsKCWNvbnN0IGhhbGY0IG91dHB1dENvdmVyYWdlX1MwID0gaGFsZjQoMSk7Cgl7CgkJLy8gWGZlciBQcm9jZXNzb3I6IFBvcnRlciBEdWZmCgkJc2tfRnJhZ0NvbG9yID0gb3V0cHV0Q29sb3JfUzAgKiBvdXRwdXRDb3ZlcmFnZV9TMDsKCX0KfQoAAAAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABUAAAAAQAAAAAAAAACAAAAHQAAAAAAAAABAAAACQAAAAgAAAABAAAADAAAAAAAAABGAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAD9AAAA", "IYBAAAAABAAACAABBYAAAKAAAMAAGEAAAABRAEAAAEHCAAAAAAAABCAAAAAABAEQAEAAAACQAAAAAAIAAAAP2AAAAAAQAAAAEAAAAAACAAAAAAAAAAAA": "DAAAAExTS1P9DwAAY29uc3QgZmxvYXQgUFJFQ0lTSU9OID0gNC4wMDAwMDA7CmNvbnN0IGZsb2F0IE1BWF9GSVhFRF9SRVNPTFZFX0xFVkVMID0gNS4wMDAwMDA7CmNvbnN0IGZsb2F0IE1BWF9GSVhFRF9TRUdNRU5UUyA9IDMyLjAwMDAwMDsKbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7CglsYXlvdXQob2Zmc2V0PTE2KSBmbG9hdDQgdWFmZmluZU1hdHJpeF9TMDsKCWxheW91dChvZmZzZXQ9MzIpIGZsb2F0MiB1dHJhbnNsYXRlX1MwOwoJbGF5b3V0KG9mZnNldD00MCkgaGFsZjQgdWNvbG9yX1MwOwp9CjsKbGF5b3V0KGxvY2F0aW9uID0gMCkgaW4gZmxvYXQyIHJlc29sdmVMZXZlbF9hbmRfaWR4OwpsYXlvdXQobG9jYXRpb24gPSAxKSBpbiBmbG9hdDQgcDAxOwpsYXlvdXQobG9jYXRpb24gPSAyKSBpbiBmbG9hdDQgcDIzOwpsYXlvdXQobG9jYXRpb24gPSAzKSBpbiBmbG9hdDIgZmFuUG9pbnRBdHRyaWI7CmZsb2F0IHdhbmdzX2Zvcm11bGFfbWF4X2ZkaWZmX3AyKGZsb2F0MiBwMCwgZmxvYXQyIHAxLCBmbG9hdDIgcDIsIGZsb2F0MiBwMyxmbG9hdDJ4MiBtYXRyaXgpIAp7CglmbG9hdDIgZDAgPSBtYXRyaXggKiAoZm1hKGZsb2F0MigtMiksIHAxLCBwMikgKyBwMCk7CglmbG9hdDIgZDEgPSBtYXRyaXggKiAoZm1hKGZsb2F0MigtMiksIHAyLCBwMykgKyBwMSk7CglyZXR1cm4gbWF4KGRvdChkMCxkMCksIGRvdChkMSxkMSkpOwp9CmZsb2F0IHdhbmdzX2Zvcm11bGFfY3ViaWMoZmxvYXQgX3ByZWNpc2lvbl8sIGZsb2F0MiBwMCwgZmxvYXQyIHAxLCBmbG9hdDIgcDIsIGZsb2F0MiBwMyxmbG9hdDJ4MiBtYXRyaXgpIAp7CglmbG9hdCBtID0gd2FuZ3NfZm9ybXVsYV9tYXhfZmRpZmZfcDIocDAsIHAxLCBwMiwgcDMsIG1hdHJpeCk7CglyZXR1cm4gbWF4KGNlaWwoc3FydCgwLjc1ICogX3ByZWNpc2lvbl8gKiBzcXJ0KG0pKSksIDEuMCk7Cn0KZmxvYXQgd2FuZ3NfZm9ybXVsYV9jdWJpY19sb2cyKGZsb2F0IF9wcmVjaXNpb25fLCBmbG9hdDIgcDAsIGZsb2F0MiBwMSwgZmxvYXQyIHAyLCBmbG9hdDIgcDMsZmxvYXQyeDIgbWF0cml4KSAKewoJZmxvYXQgbSA9IHdhbmdzX2Zvcm11bGFfbWF4X2ZkaWZmX3AyKHAwLCBwMSwgcDIsIHAzLCBtYXRyaXgpOwoJcmV0dXJuIGNlaWwobG9nMihtYXgoMC41NjI1ICogX3ByZWNpc2lvbl8gKiBfcHJlY2lzaW9uXyAqIG0sIDEuMCkpICogLjI1KTsKfQpmbG9hdCB3YW5nc19mb3JtdWxhX2NvbmljX3AyKGZsb2F0IF9wcmVjaXNpb25fLCBmbG9hdDIgcDAsIGZsb2F0MiBwMSwgZmxvYXQyIHAyLCBmbG9hdCB3KSAKewoJZmxvYXQyIEMgPSAobWluKG1pbihwMCwgcDEpLCBwMikgKyBtYXgobWF4KHAwLCBwMSksIHAyKSkgKiAwLjU7CglwMCAtPSBDOwoJcDEgLT0gQzsKCXAyIC09IEM7CglmbG9hdCBtID0gc3FydChtYXgobWF4KGRvdChwMCxwMCksIGRvdChwMSxwMSkpLCBkb3QocDIscDIpKSk7CglmbG9hdDIgZHAgPSBmbWEoZmxvYXQyKC0yLjAgKiB3KSwgcDEsIHAwKSArIHAyOwoJZmxvYXQgZHcgPSBhYnMoZm1hKC0yLjAsIHcsIDIuMCkpOwoJZmxvYXQgcnBfbWludXNfMSA9IG1heCgwLjAsIGZtYShtLCBfcHJlY2lzaW9uXywgLTEuMCkpOwoJZmxvYXQgbnVtZXIgPSBsZW5ndGgoZHApICogX3ByZWNpc2lvbl8gKyBycF9taW51c18xICogZHc7CglmbG9hdCBkZW5vbSA9IDQgKiBtaW4odywgMS4wKTsKCXJldHVybiBudW1lci9kZW5vbTsKfQpmbG9hdCB3YW5nc19mb3JtdWxhX2NvbmljKGZsb2F0IF9wcmVjaXNpb25fLCBmbG9hdDIgcDAsIGZsb2F0MiBwMSwgZmxvYXQyIHAyLCBmbG9hdCB3KSAKewoJZmxvYXQgbjIgPSB3YW5nc19mb3JtdWxhX2NvbmljX3AyKF9wcmVjaXNpb25fLCBwMCwgcDEsIHAyLCB3KTsKCXJldHVybiBtYXgoY2VpbChzcXJ0KG4yKSksIDEuMCk7Cn0KZmxvYXQgd2FuZ3NfZm9ybXVsYV9jb25pY19sb2cyKGZsb2F0IF9wcmVjaXNpb25fLCBmbG9hdDIgcDAsIGZsb2F0MiBwMSwgZmxvYXQyIHAyLCBmbG9hdCB3KSAKewoJZmxvYXQgbjIgPSB3YW5nc19mb3JtdWxhX2NvbmljX3AyKF9wcmVjaXNpb25fLCBwMCwgcDEsIHAyLCB3KTsKCXJldHVybiBjZWlsKGxvZzIobWF4KG4yLCAxLjApKSAqIC41KTsKfQpib29sIGlzX2NvbmljX2N1cnZlKCkgCnsKCXJldHVybiBpc2luZihwMjMudyk7Cn0KYm9vbCBpc190cmlhbmd1bGFyX2NvbmljX2N1cnZlKCkgCnsKCXJldHVybiBpc2luZihwMjMueik7Cn0KZmxvYXQgbGRleHBfcG9ydGFibGUoZmxvYXQgeCwgZmxvYXQgcCkgCnsKCXJldHVybiB4ICogZXhwMihwKTsKfQp2b2lkIG1haW4oKSAKewoJLy8gUHJpbWl0aXZlIFByb2Nlc3NvciB0ZXNzZWxsYXRlX01pZGRsZU91dFNoYWRlcgoJZmxvYXQyeDIgQUZGSU5FX01BVFJJWCA9IGZsb2F0MngyKHVhZmZpbmVNYXRyaXhfUzAueHksIHVhZmZpbmVNYXRyaXhfUzAuencpOwoJZmxvYXQyIFRSQU5TTEFURSA9IHV0cmFuc2xhdGVfUzA7CglmbG9hdCByZXNvbHZlTGV2ZWwgPSByZXNvbHZlTGV2ZWxfYW5kX2lkeC54OwoJZmxvYXQgaWR4SW5SZXNvbHZlTGV2ZWwgPSByZXNvbHZlTGV2ZWxfYW5kX2lkeC55OwoJZmxvYXQyIGxvY2FsY29vcmQ7CglpZiAocmVzb2x2ZUxldmVsIDwgMCkgCgl7CgkJbG9jYWxjb29yZCA9IGZhblBvaW50QXR0cmliOwoJfQoJZWxzZSBpZiAoaXNfdHJpYW5ndWxhcl9jb25pY19jdXJ2ZSgpKSAKCXsKCQlsb2NhbGNvb3JkID0gKHJlc29sdmVMZXZlbCAhPSAwKSA/IHAwMS56dzogKGlkeEluUmVzb2x2ZUxldmVsICE9IDApID8gcDIzLnh5OiBwMDEueHk7Cgl9CgllbHNlIAoJewoJCWZsb2F0MiBwMD1wMDEueHksIHAxPXAwMS56dywgcDI9cDIzLnh5LCBwMz1wMjMuenc7CgkJZmxvYXQgdyA9IC0xOwoJCWZsb2F0IG1heFJlc29sdmVMZXZlbDsKCQlpZiAoaXNfY29uaWNfY3VydmUoKSkgCgkJewoJCQl3ID0gcDMueDsKCQkJbWF4UmVzb2x2ZUxldmVsID0gd2FuZ3NfZm9ybXVsYV9jb25pY19sb2cyKFBSRUNJU0lPTiwgQUZGSU5FX01BVFJJWCAqIHAwLEFGRklORV9NQVRSSVggKiBwMSxBRkZJTkVfTUFUUklYICogcDIsIHcpOwoJCQlwMSAqPSB3OwoJCQlwMyA9IHAyOwoJCX0KCQllbHNlIAoJCXsKCQkJbWF4UmVzb2x2ZUxldmVsID0gd2FuZ3NfZm9ybXVsYV9jdWJpY19sb2cyKFBSRUNJU0lPTiwgcDAsIHAxLCBwMiwgcDMsQUZGSU5FX01BVFJJWCk7CgkJfQoJCWlmIChyZXNvbHZlTGV2ZWwgPiBtYXhSZXNvbHZlTGV2ZWwpIAoJCXsKCQkJaWR4SW5SZXNvbHZlTGV2ZWwgPSBmbG9vcihsZGV4cF9wb3J0YWJsZShpZHhJblJlc29sdmVMZXZlbCxtYXhSZXNvbHZlTGV2ZWwgLSByZXNvbHZlTGV2ZWwpKTsKCQkJcmVzb2x2ZUxldmVsID0gbWF4UmVzb2x2ZUxldmVsOwoJCX0KCQlmbG9hdCBmaXhlZFZlcnRleElEID0gZmxvb3IoLjUgKyBsZGV4cF9wb3J0YWJsZShpZHhJblJlc29sdmVMZXZlbCwgTUFYX0ZJWEVEX1JFU09MVkVfTEVWRUwgLSByZXNvbHZlTGV2ZWwpKTsKCQlpZiAoMCA8IGZpeGVkVmVydGV4SUQgJiYgZml4ZWRWZXJ0ZXhJRCA8IE1BWF9GSVhFRF9TRUdNRU5UUykgCgkJewoJCQlmbG9hdCBUID0gZml4ZWRWZXJ0ZXhJRCAqICgxIC8gTUFYX0ZJWEVEX1NFR01FTlRTKTsKCQkJZmxvYXQyIGFiID0gbWl4KHAwLCBwMSwgVCk7CgkJCWZsb2F0MiBiYyA9IG1peChwMSwgcDIsIFQpOwoJCQlmbG9hdDIgY2QgPSBtaXgocDIsIHAzLCBUKTsKCQkJZmxvYXQyIGFiYyA9IG1peChhYiwgYmMsIFQpOwoJCQlmbG9hdDIgYmNkID0gbWl4KGJjLCBjZCwgVCk7CgkJCWZsb2F0MiBhYmNkID0gbWl4KGFiYywgYmNkLCBUKTsKCQkJZmxvYXQgdSA9IG1peCgxLjAsIHcsIFQpOwoJCQlmbG9hdCB2ID0gdyArIDEgLSB1OwoJCQlmbG9hdCB1diA9IG1peCh1LCB2LCBUKTsKCQkJbG9jYWxjb29yZCA9ICh3IDwgMCkgP2FiY2Q6YWJjL3V2OwoJCX0KCQllbHNlIAoJCXsKCQkJbG9jYWxjb29yZCA9IChmaXhlZFZlcnRleElEID09IDApID8gcDAueHkgOiBwMy54eTsKCQl9Cgl9CglmbG9hdDIgdmVydGV4cG9zID0gQUZGSU5FX01BVFJJWCAqIGxvY2FsY29vcmQgKyBUUkFOU0xBVEU7Cglza19Qb3NpdGlvbiA9IHZlcnRleHBvcy54eTAxOwp9CgAAAAAAAACFAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7CglsYXlvdXQob2Zmc2V0PTE2KSBmbG9hdDQgdWFmZmluZU1hdHJpeF9TMDsKCWxheW91dChvZmZzZXQ9MzIpIGZsb2F0MiB1dHJhbnNsYXRlX1MwOwoJbGF5b3V0KG9mZnNldD00MCkgaGFsZjQgdWNvbG9yX1MwOwp9CjsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIHRlc3NlbGxhdGVfTWlkZGxlT3V0U2hhZGVyCgloYWxmNCBvdXRwdXRDb2xvcl9TMCA9IHVjb2xvcl9TMDsKCWNvbnN0IGhhbGY0IG91dHB1dENvdmVyYWdlX1MwID0gaGFsZjQoMSk7Cgl7CgkJLy8gWGZlciBQcm9jZXNzb3I6IERpc2FibGUgQ29sb3IKCX0KfQoAAAAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABYAAAAAQAAAAIAAAABAAAAHQAAAAAAAAABAAAACAAAAAMAAAAfAAAAAAAAAAIAAAAfAAAAEAAAAAIAAAAdAAAAIAAAAAIAAAAoAAAAUAAAAAAAAAAAAAAA/QAAAA==", "AYQA5AADQAAAOAEARAFQJAABBADIB7777777777777777777777767YAAAAAAAAAAAAOQAAAAAAAQAAAABAMQAAAABIAAAAAAEAAAAAAAAAAAAAAAAACAAAAADBQCAAAAAAAAAA": "DAAAAExTS1OAAgAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7CglsYXlvdXQob2Zmc2V0PTE2KSBmbG9hdDQgdWxvY2FsTWF0cml4X1MwOwp9CjsKbGF5b3V0KGxvY2F0aW9uID0gMCkgaW4gZmxvYXQyIGluUG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGhhbGY0IGluQ29sb3I7CmxheW91dChsb2NhdGlvbiA9IDIpIGluIGZsb2F0NCBpbkNpcmNsZUVkZ2U7CmxheW91dChsb2NhdGlvbiA9IDApIG91dCBmbG9hdDQgdmluQ2lyY2xlRWRnZV9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMSkgb3V0IGhhbGY0IHZpbkNvbG9yX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gUHJpbWl0aXZlIFByb2Nlc3NvciBDaXJjbGVHZW9tZXRyeVByb2Nlc3NvcgoJdmluQ2lyY2xlRWRnZV9TMCA9IGluQ2lyY2xlRWRnZTsKCXZpbkNvbG9yX1MwID0gaW5Db2xvcjsKCWZsb2F0MiBfdG1wXzBfaW5Qb3NpdGlvbiA9IGluUG9zaXRpb247CglmbG9hdDIgX3RtcF8xX2luUG9zaXRpb24gPSB1bG9jYWxNYXRyaXhfUzAueHogKiBpblBvc2l0aW9uICsgdWxvY2FsTWF0cml4X1MwLnl3OwoJc2tfUG9zaXRpb24gPSBfdG1wXzBfaW5Qb3NpdGlvbi54eTAxOwp9CgAAAACbAgAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7CglsYXlvdXQob2Zmc2V0PTE2KSBmbG9hdDQgdWxvY2FsTWF0cml4X1MwOwp9CjsKbGF5b3V0KGxvY2F0aW9uID0gMCkgaW4gZmxvYXQ0IHZpbkNpcmNsZUVkZ2VfUzA7CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGhhbGY0IHZpbkNvbG9yX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgQ2lyY2xlR2VvbWV0cnlQcm9jZXNzb3IKCWZsb2F0NCBjaXJjbGVFZGdlOwoJY2lyY2xlRWRnZSA9IHZpbkNpcmNsZUVkZ2VfUzA7CgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmluQ29sb3JfUzA7CglmbG9hdCBkID0gbGVuZ3RoKGNpcmNsZUVkZ2UueHkpOwoJaGFsZiBkaXN0YW5jZVRvT3V0ZXJFZGdlID0gaGFsZihjaXJjbGVFZGdlLnogKiAoMS4wIC0gZCkpOwoJaGFsZiBlZGdlQWxwaGEgPSBzYXR1cmF0ZShkaXN0YW5jZVRvT3V0ZXJFZGdlKTsKCWhhbGY0IG91dHB1dENvdmVyYWdlX1MwID0gaGFsZjQoZWRnZUFscGhhKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABgAAAAAQAAAAAAAAADAAAAHQAAAAAAAAABAAAACQAAAAgAAAABAAAAHwAAAAwAAAABAAAAHAAAAAAAAABQAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAAAAAAA", "CIAAAAAAQAARQAAYQAAAAGFYQAABRAAAAEEAAAAAAARAEAEABYAAAAEAAAAAAAEEBQAAAACQAAAAAAIAAAAP2AAAAAAAAAAAAAAAAAGDAEBAAAAAAAAA": "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", "HUQAAAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAABAAIAAAAAAIIDAAAEMAAAAAAQAAAA7UAAAAABAAAAAEAAAAAAEAACAAAAAAAA": "DAAAAExTS1NTAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGhhbGY0IGNvbG9yOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IG91dCBoYWxmNCB2Y29sb3JfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmNvbG9yX1MwID0gY29sb3I7Cglza19Qb3NpdGlvbiA9IHBvc2l0aW9uLnh5MDE7Cn0KAAAAAABPAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzA7CglvdXRwdXRDb2xvcl9TMCA9IHZjb2xvcl9TMDsKCWNvbnN0IGhhbGY0IG91dHB1dENvdmVyYWdlX1MwID0gaGFsZjQoMSk7Cgl7CgkJLy8gWGZlciBQcm9jZXNzb3I6IERpc2FibGUgQ29sb3IKCX0KfQoAAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPAAAAAEAAAAAAAAAAgAAAB0AAAAAAAAAAQAAAAkAAAAIAAAAAQAAAAwAAAAAAAAARgAAAAAAAAAAAAAA/QAAAA==", "HVJAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAABAAAAAABBAMABAAOAAAABAAAAAAABBAMAAAIYAAAAABAAAAB7IAAAAAAAAAAAAAAAAAYMAQEAAAAAAAA": "DAAAAExTS1PMAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGhhbGY0IGNvbG9yOwpsYXlvdXQobG9jYXRpb24gPSAyKSBpbiBmbG9hdDIgbG9jYWxDb29yZDsKbGF5b3V0KGxvY2F0aW9uID0gMCkgZmxhdCBvdXQgaGFsZjQgdmNvbG9yX1MwOwpsYXlvdXQobG9jYXRpb24gPSAxKSBvdXQgZmxvYXQyIHZsb2NhbENvb3JkX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gUHJpbWl0aXZlIFByb2Nlc3NvciBRdWFkUGVyRWRnZUFBR2VvbWV0cnlQcm9jZXNzb3IKCXZjb2xvcl9TMCA9IGNvbG9yOwoJdmxvY2FsQ29vcmRfUzAgPSBsb2NhbENvb3JkOwoJc2tfUG9zaXRpb24gPSBwb3NpdGlvbi54eTAxOwp9CgAAAAB0AgAAbGF5b3V0KG1ldGFsLCBiaW5kaW5nPTApIHNhbXBsZXIyRCB1VGV4dHVyZVNhbXBsZXJfMF9TMDsKbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gZmxvYXQyIHZsb2NhbENvb3JkX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmNvbG9yX1MwOwoJZmxvYXQyIHRleENvb3JkOwoJdGV4Q29vcmQgPSB2bG9jYWxDb29yZF9TMDsKCW91dHB1dENvbG9yX1MwID0gKGJsZW5kX21vZHVsYXRlKHNhbXBsZSh1VGV4dHVyZVNhbXBsZXJfMF9TMCwgdGV4Q29vcmQpLCBvdXRwdXRDb2xvcl9TMCkpOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAABAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGAAAAABAAAAAAAAAAMAAAAdAAAAAAAAAAEAAAAJAAAACAAAAAEAAAAdAAAADAAAAAEAAAAUAAAAAAAAAEYAAAABAAAAAQAAAAUAAAAAAAAAAQAAAAUAAAAAAAAAAQAAAP0AAAA=", "HWQACAAAABAAADAAAIOAAAAADIIAAIRODAAP577774DSAIAA737777YBAAAAAAAAAAAKAAYAAAACAAAAAAACCAYAABDAAAAAAEAAAAAAAAAAAAAAAAAAAAAAADBQCAAAAAAAAAA": "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", "GEMAAAYAAEHAAAARC4EAAAQWBQAAAAAAAAAQAAAAIBCAAAGQAEAAAAAQAAAABAEQAEAAAACQAAAAAAIAAAAP2AAAAAAAAAAAAAAAAAGDAEAAAAAAAAAA": "DAAAAExTS1P9AQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgaW5Qb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgaW5Db2xvcjsKbGF5b3V0KGxvY2F0aW9uID0gMikgaW4gaGFsZjMgaW5TaGFkb3dQYXJhbXM7CmxheW91dChsb2NhdGlvbiA9IDApIG91dCBoYWxmMyB2aW5TaGFkb3dQYXJhbXNfUzA7CmxheW91dChsb2NhdGlvbiA9IDEpIG91dCBoYWxmNCB2aW5Db2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFByaW1pdGl2ZSBQcm9jZXNzb3IgUlJlY3RTaGFkb3cKCXZpblNoYWRvd1BhcmFtc19TMCA9IGluU2hhZG93UGFyYW1zOwoJdmluQ29sb3JfUzAgPSBpbkNvbG9yOwoJZmxvYXQyIF90bXBfMF9pblBvc2l0aW9uID0gaW5Qb3NpdGlvbjsKCXNrX1Bvc2l0aW9uID0gX3RtcF8wX2luUG9zaXRpb24ueHkwMTsKfQoAAAAAAAAAogIAAGxheW91dChtZXRhbCwgYmluZGluZz0wKSBzYW1wbGVyMkQgdVRleHR1cmVTYW1wbGVyXzBfUzA7CmxheW91dCAobWV0YWwsIGJpbmRpbmc9MCkgdW5pZm9ybSB1bmlmb3JtQnVmZmVyCnsKCWxheW91dChvZmZzZXQ9MCkgZmxvYXQ0IHNrX1JUQWRqdXN0Owp9CjsKbGF5b3V0KGxvY2F0aW9uID0gMCkgaW4gaGFsZjMgdmluU2hhZG93UGFyYW1zX1MwOwpsYXlvdXQobG9jYXRpb24gPSAxKSBpbiBoYWxmNCB2aW5Db2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFJSZWN0U2hhZG93CgloYWxmMyBzaGFkb3dQYXJhbXM7CglzaGFkb3dQYXJhbXMgPSB2aW5TaGFkb3dQYXJhbXNfUzA7CgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmluQ29sb3JfUzA7CgloYWxmIGQgPSBsZW5ndGgoc2hhZG93UGFyYW1zLnh5KTsKCWZsb2F0MiB1diA9IGZsb2F0MihzaGFkb3dQYXJhbXMueiAqICgxLjAgLSBkKSwgMC41KTsKCWhhbGYgZmFjdG9yID0gc2FtcGxlKHVUZXh0dXJlU2FtcGxlcl8wX1MwLCB1dikuMDAwci5hOwoJaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNChmYWN0b3IpOwoJewoJCS8vIFhmZXIgUHJvY2Vzc29yOiBQb3J0ZXIgRHVmZgoJCXNrX0ZyYWdDb2xvciA9IG91dHB1dENvbG9yX1MwICogb3V0cHV0Q292ZXJhZ2VfUzA7Cgl9Cn0KAAAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABgAAAAAQAAAAAAAAADAAAAHQAAAAAAAAABAAAACQAAAAgAAAABAAAAHgAAAAwAAAABAAAAGAAAAAAAAABQAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAD9AAAA", "HVJAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAABAAAAAABBAMABAAOAAAABAAAAAAABBAMAAAKAAAAAABAAAAAAAAAAAAAAAAAAQAAAAAYMAQEAAAAAAAA": "DAAAAExTS1PMAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGhhbGY0IGNvbG9yOwpsYXlvdXQobG9jYXRpb24gPSAyKSBpbiBmbG9hdDIgbG9jYWxDb29yZDsKbGF5b3V0KGxvY2F0aW9uID0gMCkgZmxhdCBvdXQgaGFsZjQgdmNvbG9yX1MwOwpsYXlvdXQobG9jYXRpb24gPSAxKSBvdXQgZmxvYXQyIHZsb2NhbENvb3JkX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gUHJpbWl0aXZlIFByb2Nlc3NvciBRdWFkUGVyRWRnZUFBR2VvbWV0cnlQcm9jZXNzb3IKCXZjb2xvcl9TMCA9IGNvbG9yOwoJdmxvY2FsQ29vcmRfUzAgPSBsb2NhbENvb3JkOwoJc2tfUG9zaXRpb24gPSBwb3NpdGlvbi54eTAxOwp9CgAAAAB0AgAAbGF5b3V0KG1ldGFsLCBiaW5kaW5nPTApIHNhbXBsZXIyRCB1VGV4dHVyZVNhbXBsZXJfMF9TMDsKbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gZmxvYXQyIHZsb2NhbENvb3JkX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmNvbG9yX1MwOwoJZmxvYXQyIHRleENvb3JkOwoJdGV4Q29vcmQgPSB2bG9jYWxDb29yZF9TMDsKCW91dHB1dENvbG9yX1MwID0gKGJsZW5kX21vZHVsYXRlKHNhbXBsZSh1VGV4dHVyZVNhbXBsZXJfMF9TMCwgdGV4Q29vcmQpLCBvdXRwdXRDb2xvcl9TMCkpOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAABAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGAAAAABAAAAAAAAAAMAAAAdAAAAAAAAAAEAAAAJAAAACAAAAAEAAAAdAAAADAAAAAEAAAAUAAAAAAAAAFAAAAABAAAAAQAAAAUAAAAAAAAAAQAAAAUAAAAAAAAAAQAAAAAAAAA=", "HUQACAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAAKAAYAAAACAAAAAAACCAYAABDAAAAAAEAAAAAAAAAAAAAAAAAAAAAAADBQCAAAAAAAAAA": "DAAAAExTS1NOAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGhhbGY0IGNvbG9yOwpsYXlvdXQobG9jYXRpb24gPSAwKSBvdXQgaGFsZjQgdmNvbG9yX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gUHJpbWl0aXZlIFByb2Nlc3NvciBRdWFkUGVyRWRnZUFBR2VvbWV0cnlQcm9jZXNzb3IKCXZjb2xvcl9TMCA9IGNvbG9yOwoJc2tfUG9zaXRpb24gPSBwb3NpdGlvbi54eTAxOwp9CgAAAAAAAH0BAABsYXlvdXQgKG1ldGFsLCBiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGhhbGY0IHZjb2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzA7CglvdXRwdXRDb2xvcl9TMCA9IHZjb2xvcl9TMDsKCWNvbnN0IGhhbGY0IG91dHB1dENvdmVyYWdlX1MwID0gaGFsZjQoMSk7Cgl7CgkJLy8gWGZlciBQcm9jZXNzb3I6IFBvcnRlciBEdWZmCgkJc2tfRnJhZ0NvbG9yID0gb3V0cHV0Q29sb3JfUzAgKiBvdXRwdXRDb3ZlcmFnZV9TMDsKCX0KfQoAAAAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABUAAAAAQAAAAAAAAACAAAAHQAAAAAAAAABAAAACQAAAAgAAAABAAAADAAAAAAAAABGAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAAAAAAA", "HUIAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAAAAAALUAQBAEAQAAAAGQCBAMQACAIAAAAAACQAGAAAAAQAAAAAAAQQGAAAABDAAAAAAEAAAAAAAAAAAAAAAAACAAAAAABQAAQAAAAAAAA": "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", "DAQAAAAAAAAAAAAAAJQAAIGAAEACBYQCAGAEFAIBAAAAAABAAAAAAAAAAAAAAOQAAAAAAAQAAAABAMQAAAAEMAAAAAAQAAAAAAAAAAAAAAAAAIAAAAAMGAIAAAAAAAAA": "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", "HUIAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAAAAAALUAQBAEAQAAAAGQCBAMQACAIAAAAAACQAGAAAAAQAAAAAAAQQGAAAABIAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAABQAAQAAAAAAAA": "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", "AYQQ5AADQAAAOAEARAFQJAABBADIB7777777777777777777777767YAAAAAAAAAAAAOQAAAAAAAQAAAABAMQAAAABIAAAAAAEAAAAAAAAAAAAAAAAAAAAAAADBQCAAAAAAAAAA": "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", "HUQAAAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAADEAANAAAAA2RNJRSBIAAAAABQAAAABAAAAAAAZCEFUCYTDECQAAGAAAAAAAAAAAAAAOQAAAAAAAQAAAABAMQAABIAAAAAAEAAAAH5AAAAAAAAAAAAAAAAADBQCAQAAAAAAAA": "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", "HUQAAAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAAKAAYAAAACAAAAAAACCAYAABIAAAAAAEAAAAAAAAAAAAAAAAAAAAAAADBQCAQAAAAAAAA": "DAAAAExTS1NTAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGhhbGY0IGNvbG9yOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IG91dCBoYWxmNCB2Y29sb3JfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmNvbG9yX1MwID0gY29sb3I7Cglza19Qb3NpdGlvbiA9IHBvc2l0aW9uLnh5MDE7Cn0KAAAAAACCAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzA7CglvdXRwdXRDb2xvcl9TMCA9IHZjb2xvcl9TMDsKCWNvbnN0IGhhbGY0IG91dHB1dENvdmVyYWdlX1MwID0gaGFsZjQoMSk7Cgl7CgkJLy8gWGZlciBQcm9jZXNzb3I6IFBvcnRlciBEdWZmCgkJc2tfRnJhZ0NvbG9yID0gb3V0cHV0Q29sb3JfUzAgKiBvdXRwdXRDb3ZlcmFnZV9TMDsKCX0KfQoAAAAAAAABAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFQAAAABAAAAAAAAAAIAAAAdAAAAAAAAAAEAAAAJAAAACAAAAAEAAAAMAAAAAAAAAFAAAAABAAAAAQAAAAUAAAAAAAAAAQAAAAUAAAAAAAAAAQAAAAAAAAA=", "HUQAAAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAAKAAYAAAACAAAAAAACCAYAABIAAAAAAEAAAAH5AAAAAAAAAAACAAAAADBQCAQAAAAAAAA": "DAAAAExTS1NTAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGhhbGY0IGNvbG9yOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IG91dCBoYWxmNCB2Y29sb3JfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmNvbG9yX1MwID0gY29sb3I7Cglza19Qb3NpdGlvbiA9IHBvc2l0aW9uLnh5MDE7Cn0KAAAAAACCAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzA7CglvdXRwdXRDb2xvcl9TMCA9IHZjb2xvcl9TMDsKCWNvbnN0IGhhbGY0IG91dHB1dENvdmVyYWdlX1MwID0gaGFsZjQoMSk7Cgl7CgkJLy8gWGZlciBQcm9jZXNzb3I6IFBvcnRlciBEdWZmCgkJc2tfRnJhZ0NvbG9yID0gb3V0cHV0Q29sb3JfUzAgKiBvdXRwdXRDb3ZlcmFnZV9TMDsKCX0KfQoAAAAAAAABAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFQAAAABAAAAAAAAAAIAAAAdAAAAAAAAAAEAAAAJAAAACAAAAAEAAAAMAAAAAAAAAFAAAAABAAAAAQAAAAUAAAAAAAAAAQAAAAUAAAAAAAAAAQAAAP0AAAA=", "HUJAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAQAAAAAAQQGABZAA6IAAAAACAAAAAADUAAAAAAAEAAAAAIDEAAAAAAFAAAAAAAQAAAA7UAAAAAAAAAAAAAAAAAMGAICAAAAAAAA": "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", "HVIAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAAAAAAAAFIBVWKMG7QAAAAAAAEAQCAAAAAVREEAQAAAAAQCDAAQQGAABAEAAAAAAHIAAAAAAAIAAAAAQGIAAAAAAFAAAAAAAQAAAAAAAAAAAAAAAAAIAAAAAMGAICAAAAAAAA": "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", "DYBQAAAAAEAAAAAQAABQAAIOAAABCFYIAAKAUDAAAAAAAAABAAAAAAAAAAANAAIAAAABAAAAACAJAAIAAAAFAAAAAAAQAAAAAAAAAAAAAAAAAAAAAAAMGAIAAAAAAAAA": "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", "CIAQAAAAIABCQAAYQAAAAGEAQAAAQ4AAAEMLQQABDCAMAAIIAAAAAABCAIAKQDAALF6I6EAAAAANQTRV4ABQAAAAVB2VOVZBAEAAAYAAAAAEAZMWBAOSIAAAAAOKFAOLAAAAAAAAAACAAAAAMRSDXVMAB4AAAAFA2ZOV3BIEAAAIAAIAAAAJKWJCOSIAAAAAOCEAMLQDAAAAACAAAAAJBEK2O52RKEQAAAAAMAAAABKGNCOQIEBAAAGAEENLQDAAAAAEAAAAABAAMK5TITUCAAIAAAAAAAAAWARA5UOAMUAAAAAAAEAAAABS4QIA2XAGAAAAAAAAAAALBAQAAAAQAAAAGQCEIBAAAAAAAAAAACQAGAAAAAQAAAAAAAQQGAAAIYAAAAABAAAAAAAAAAAAAAAAAAAAAAAAYMAQEAAAAAAAA": "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", "HUJAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAQAAAAAAQQGAAZAADIAAAAGULKMMQKAAAAAAMAAAAAIAAAAAAGIRBNAWEYZAUAABQAAAAAAAAAAAAADUAAAAAAAEAAAAAIDEAAAIYAAAAABAAAAB7IAAAAACAAAAAIAAAAAYMAQEAAAAAAAA": "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", "AYQA5AADQAAAOAEARAFQJAABBADIB7777777777777777777777767YAAAAAAAAAAAAMWAEQYX3QQAIAACAO2VADHYAAAAEALJ3XKFISAAAAABQAAAAEAAAAAA2JTW5YCMAQAAGAEENLQDAAAAAAAACAAAAAAQCGWZJQ36AAAAAAA2W52VKUQAAAAAMAAAAAAAAQAAGQMRXOGTQEAAAABB3I4AZAAAAAQAAAAAAADGUXKV2XEEAQAADAAAAAAAAEAAAEBE5ZRU5RCAAAAAOKFAOLAAAAAAAEAAAAAMQQAIAABIGJ3TDJ2CAAAAAACAAAAAZNEZDO4NHAIAAAAAAAAAEACVYYQBROAMAAAAAIAAAABEBBQ5UOAMQAAAAAAAAAACABKBAABAAAAAFAEEQCEAAAAAAAAAAAQAB5QAYAAAEAAAAAADIACAAAAAIAAAAAQCIACACGAAAAAAIAAAAAAAAAAAAAAAAAAAAAAAGDAEAAAAAAAAAA": "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", "CMRQCIAABBYAAAEIXBAAACDQMAABRAFAAAAAAAAAAAAAAAEABYAAAAEAAAAAAAEEBQAAAACQAAAAAAIAAAAP2AAAAAAAAAAAAAAAAAGDAEAAAAAAAAAA": "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", "HVJAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAABAAAAAABBAMADSAB4QAAAAAEAAAAAAHIAAAAAAAIAAAAAQGIAAAAAAFAAAAAAAQAAAAAAAAAAAAAAAAAAAAAAAMGAICAAAAAAAA": "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", "HUQAAAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAABAAIAAAAAAIIDAAAEMAAAAAAQAAAA7UAAAAABAAAAAAAAAAAAEAACAAAAAAAA": "DAAAAExTS1NTAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGhhbGY0IGNvbG9yOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IG91dCBoYWxmNCB2Y29sb3JfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmNvbG9yX1MwID0gY29sb3I7Cglza19Qb3NpdGlvbiA9IHBvc2l0aW9uLnh5MDE7Cn0KAAAAAABPAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzA7CglvdXRwdXRDb2xvcl9TMCA9IHZjb2xvcl9TMDsKCWNvbnN0IGhhbGY0IG91dHB1dENvdmVyYWdlX1MwID0gaGFsZjQoMSk7Cgl7CgkJLy8gWGZlciBQcm9jZXNzb3I6IERpc2FibGUgQ29sb3IKCX0KfQoAAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPAAAAAEAAAAAAAAAAgAAAB0AAAAAAAAAAQAAAAkAAAAIAAAAAQAAAAwAAAAAAAAARgAAAAAAAAAAAAAA/QAAAA==", "HUJAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAQAAAAAAQQGACQAGAAAAAQAAAAAAAQQGAAAIYAAAAABAAAAAAAAAAAAAAAAAAAAAAAAYMAQEAAAAAAAA": "DAAAAExTS1NkAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGZsb2F0MiBsb2NhbENvb3JkOwpsYXlvdXQobG9jYXRpb24gPSAwKSBvdXQgZmxvYXQyIHZsb2NhbENvb3JkX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gUHJpbWl0aXZlIFByb2Nlc3NvciBRdWFkUGVyRWRnZUFBR2VvbWV0cnlQcm9jZXNzb3IKCXZsb2NhbENvb3JkX1MwID0gbG9jYWxDb29yZDsKCXNrX1Bvc2l0aW9uID0gcG9zaXRpb24ueHkwMTsKfQoAAAAALgIAAGxheW91dChtZXRhbCwgYmluZGluZz0wKSBzYW1wbGVyMkQgdVRleHR1cmVTYW1wbGVyXzBfUzA7CmxheW91dCAobWV0YWwsIGJpbmRpbmc9MCkgdW5pZm9ybSB1bmlmb3JtQnVmZmVyCnsKCWxheW91dChvZmZzZXQ9MCkgZmxvYXQ0IHNrX1JUQWRqdXN0Owp9CjsKbGF5b3V0KGxvY2F0aW9uID0gMCkgaW4gZmxvYXQyIHZsb2NhbENvb3JkX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMCA9IGhhbGY0KDEpOwoJZmxvYXQyIHRleENvb3JkOwoJdGV4Q29vcmQgPSB2bG9jYWxDb29yZF9TMDsKCW91dHB1dENvbG9yX1MwID0gKGJsZW5kX21vZHVsYXRlKHNhbXBsZSh1VGV4dHVyZVNhbXBsZXJfMF9TMCwgdGV4Q29vcmQpLCBoYWxmNCgxKSkpOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAVAAAAAEAAAAAAAAAAgAAAB0AAAAAAAAAAQAAAB0AAAAIAAAAAQAAABAAAAAAAAAARgAAAAEAAAABAAAABQAAAAAAAAABAAAABQAAAAAAAAABAAAAAAAAAA==", "HUQAAAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAAKAAYAAAACAAAAAAACCAYAABDAAAAAAEAAAAAAAAAAAAAAAAACAAAAADBQCAQAAAAAAAA": "DAAAAExTS1NTAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGhhbGY0IGNvbG9yOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IG91dCBoYWxmNCB2Y29sb3JfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmNvbG9yX1MwID0gY29sb3I7Cglza19Qb3NpdGlvbiA9IHBvc2l0aW9uLnh5MDE7Cn0KAAAAAACCAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzA7CglvdXRwdXRDb2xvcl9TMCA9IHZjb2xvcl9TMDsKCWNvbnN0IGhhbGY0IG91dHB1dENvdmVyYWdlX1MwID0gaGFsZjQoMSk7Cgl7CgkJLy8gWGZlciBQcm9jZXNzb3I6IFBvcnRlciBEdWZmCgkJc2tfRnJhZ0NvbG9yID0gb3V0cHV0Q29sb3JfUzAgKiBvdXRwdXRDb3ZlcmFnZV9TMDsKCX0KfQoAAAAAAAABAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFQAAAABAAAAAAAAAAIAAAAdAAAAAAAAAAEAAAAJAAAACAAAAAEAAAAMAAAAAAAAAEYAAAABAAAAAQAAAAUAAAAAAAAAAQAAAAUAAAAAAAAAAQAAAAAAAAA=", "HWQACAAAABAAADAAAIOAAAAADIIAAIRODAAP577774DSAIAA737777YBAAAAAAAAAAAKAAYAAAACAAAAAAACCAYAABIAAAAAAEAAAAH5AAAAAAAAAAAAAAAAADBQCAAAAAAAAAA": "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", "HUQACAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAAKAAYAAAACAAAAAAACCAYAABDAAAAAAEAAAAAAAAAAAAAAAAACAAAAADBQCAAAAAAAAAA": "DAAAAExTS1NOAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGhhbGY0IGNvbG9yOwpsYXlvdXQobG9jYXRpb24gPSAwKSBvdXQgaGFsZjQgdmNvbG9yX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gUHJpbWl0aXZlIFByb2Nlc3NvciBRdWFkUGVyRWRnZUFBR2VvbWV0cnlQcm9jZXNzb3IKCXZjb2xvcl9TMCA9IGNvbG9yOwoJc2tfUG9zaXRpb24gPSBwb3NpdGlvbi54eTAxOwp9CgAAAAAAAH0BAABsYXlvdXQgKG1ldGFsLCBiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGhhbGY0IHZjb2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzA7CglvdXRwdXRDb2xvcl9TMCA9IHZjb2xvcl9TMDsKCWNvbnN0IGhhbGY0IG91dHB1dENvdmVyYWdlX1MwID0gaGFsZjQoMSk7Cgl7CgkJLy8gWGZlciBQcm9jZXNzb3I6IFBvcnRlciBEdWZmCgkJc2tfRnJhZ0NvbG9yID0gb3V0cHV0Q29sb3JfUzAgKiBvdXRwdXRDb3ZlcmFnZV9TMDsKCX0KfQoAAAAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABUAAAAAQAAAAAAAAACAAAAHQAAAAAAAAABAAAACQAAAAgAAAABAAAADAAAAAAAAABGAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAAAAAAA", "HVIAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAAAAAAAAGIBIAAABAAAAANAEAAAAAAAAAAAAAABAAOAAAABAAAAAAABBAMAAAABDAAAAAAEAAAAH5AAAAAAIAAAABAAAAADBQCAQAAAAAAAA": "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", "GEMAAAYAAEHAAAARC4EAAAQWBQAAAAAAAAAQAAAAIBCAAAGQAEAAAAAQAAAABAEQAEAAAACGAAAAAAIAAAAAAAAAAAAAAAAAAAAAAAGDAEAAAAAAAAAA": "DAAAAExTS1P9AQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgaW5Qb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgaW5Db2xvcjsKbGF5b3V0KGxvY2F0aW9uID0gMikgaW4gaGFsZjMgaW5TaGFkb3dQYXJhbXM7CmxheW91dChsb2NhdGlvbiA9IDApIG91dCBoYWxmMyB2aW5TaGFkb3dQYXJhbXNfUzA7CmxheW91dChsb2NhdGlvbiA9IDEpIG91dCBoYWxmNCB2aW5Db2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFByaW1pdGl2ZSBQcm9jZXNzb3IgUlJlY3RTaGFkb3cKCXZpblNoYWRvd1BhcmFtc19TMCA9IGluU2hhZG93UGFyYW1zOwoJdmluQ29sb3JfUzAgPSBpbkNvbG9yOwoJZmxvYXQyIF90bXBfMF9pblBvc2l0aW9uID0gaW5Qb3NpdGlvbjsKCXNrX1Bvc2l0aW9uID0gX3RtcF8wX2luUG9zaXRpb24ueHkwMTsKfQoAAAAAAAAAogIAAGxheW91dChtZXRhbCwgYmluZGluZz0wKSBzYW1wbGVyMkQgdVRleHR1cmVTYW1wbGVyXzBfUzA7CmxheW91dCAobWV0YWwsIGJpbmRpbmc9MCkgdW5pZm9ybSB1bmlmb3JtQnVmZmVyCnsKCWxheW91dChvZmZzZXQ9MCkgZmxvYXQ0IHNrX1JUQWRqdXN0Owp9CjsKbGF5b3V0KGxvY2F0aW9uID0gMCkgaW4gaGFsZjMgdmluU2hhZG93UGFyYW1zX1MwOwpsYXlvdXQobG9jYXRpb24gPSAxKSBpbiBoYWxmNCB2aW5Db2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFJSZWN0U2hhZG93CgloYWxmMyBzaGFkb3dQYXJhbXM7CglzaGFkb3dQYXJhbXMgPSB2aW5TaGFkb3dQYXJhbXNfUzA7CgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmluQ29sb3JfUzA7CgloYWxmIGQgPSBsZW5ndGgoc2hhZG93UGFyYW1zLnh5KTsKCWZsb2F0MiB1diA9IGZsb2F0MihzaGFkb3dQYXJhbXMueiAqICgxLjAgLSBkKSwgMC41KTsKCWhhbGYgZmFjdG9yID0gc2FtcGxlKHVUZXh0dXJlU2FtcGxlcl8wX1MwLCB1dikuMDAwci5hOwoJaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNChmYWN0b3IpOwoJewoJCS8vIFhmZXIgUHJvY2Vzc29yOiBQb3J0ZXIgRHVmZgoJCXNrX0ZyYWdDb2xvciA9IG91dHB1dENvbG9yX1MwICogb3V0cHV0Q292ZXJhZ2VfUzA7Cgl9Cn0KAAAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABgAAAAAQAAAAAAAAADAAAAHQAAAAAAAAABAAAACQAAAAgAAAABAAAAHgAAAAwAAAABAAAAGAAAAAAAAABGAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAAAAAAA", "HVJAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAABAAAAAABBAMABAAOAAAABAAAAAAABBAMAAAKAAAAAABAAAAB7IAAAAAAAAAAAAAAAAAYMAQEAAAAAAAA": "DAAAAExTS1PMAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGhhbGY0IGNvbG9yOwpsYXlvdXQobG9jYXRpb24gPSAyKSBpbiBmbG9hdDIgbG9jYWxDb29yZDsKbGF5b3V0KGxvY2F0aW9uID0gMCkgZmxhdCBvdXQgaGFsZjQgdmNvbG9yX1MwOwpsYXlvdXQobG9jYXRpb24gPSAxKSBvdXQgZmxvYXQyIHZsb2NhbENvb3JkX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gUHJpbWl0aXZlIFByb2Nlc3NvciBRdWFkUGVyRWRnZUFBR2VvbWV0cnlQcm9jZXNzb3IKCXZjb2xvcl9TMCA9IGNvbG9yOwoJdmxvY2FsQ29vcmRfUzAgPSBsb2NhbENvb3JkOwoJc2tfUG9zaXRpb24gPSBwb3NpdGlvbi54eTAxOwp9CgAAAAB0AgAAbGF5b3V0KG1ldGFsLCBiaW5kaW5nPTApIHNhbXBsZXIyRCB1VGV4dHVyZVNhbXBsZXJfMF9TMDsKbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gZmxvYXQyIHZsb2NhbENvb3JkX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmNvbG9yX1MwOwoJZmxvYXQyIHRleENvb3JkOwoJdGV4Q29vcmQgPSB2bG9jYWxDb29yZF9TMDsKCW91dHB1dENvbG9yX1MwID0gKGJsZW5kX21vZHVsYXRlKHNhbXBsZSh1VGV4dHVyZVNhbXBsZXJfMF9TMCwgdGV4Q29vcmQpLCBvdXRwdXRDb2xvcl9TMCkpOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAABAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGAAAAABAAAAAAAAAAMAAAAdAAAAAAAAAAEAAAAJAAAACAAAAAEAAAAdAAAADAAAAAEAAAAUAAAAAAAAAFAAAAABAAAAAQAAAAUAAAAAAAAAAQAAAAUAAAAAAAAAAQAAAP0AAAA=", "HUQAAAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAAKAAYAAAACAAAAAAACCAYAABIAAAAAAEAAAAAAAAAAAAAAAAACAAAAADBQCAQAAAAAAAA": "DAAAAExTS1NTAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGhhbGY0IGNvbG9yOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IG91dCBoYWxmNCB2Y29sb3JfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmNvbG9yX1MwID0gY29sb3I7Cglza19Qb3NpdGlvbiA9IHBvc2l0aW9uLnh5MDE7Cn0KAAAAAACCAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzA7CglvdXRwdXRDb2xvcl9TMCA9IHZjb2xvcl9TMDsKCWNvbnN0IGhhbGY0IG91dHB1dENvdmVyYWdlX1MwID0gaGFsZjQoMSk7Cgl7CgkJLy8gWGZlciBQcm9jZXNzb3I6IFBvcnRlciBEdWZmCgkJc2tfRnJhZ0NvbG9yID0gb3V0cHV0Q29sb3JfUzAgKiBvdXRwdXRDb3ZlcmFnZV9TMDsKCX0KfQoAAAAAAAABAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFQAAAABAAAAAAAAAAIAAAAdAAAAAAAAAAEAAAAJAAAACAAAAAEAAAAMAAAAAAAAAFAAAAABAAAAAQAAAAUAAAAAAAAAAQAAAAUAAAAAAAAAAQAAAAAAAAA=", "CIAQAAAAIABCQAAYQAAAAGEAQAAAQ4AAAEMLQQABDCAMAAIIAAAAAABCAIALADAALF6I6EAAAAANQTRV4ABQAAAAVB2VOVZBAEAAAYAAAAAEAZMWBAOSIAAAAAOKFAOLAAAAAAAAAACAAAAAMRSDXVMAB4AAAAFA2ZOV3BIEAAAIAAIAAAAJKWJCOSIAAAAAOCEAMLQDAAAAACAAAAAJBEK2O52RKEQAAAAAMAAAABKGNCOQIEBAAAGAEENLQDAAAAAEAAAAABAAMK5TITUCAAIAAAAAAAAAWARA5UOAMUAAAAAAAEAAAABS4QIA2XAGAAAAAAAAAAALBAQAAAAQAAAAGQCEIBAAAAAAAAAAAAYAADIAAAAB3BX76AKAAAAAAMAAAAAIAAAABSCIQ7QT6PAFAAAMAAAAAAAAAAAAAAADUAAAAAAAEAAAAAIDEAAAIYAAAAABAAAAAAAAAAAAAAAAAAAAAAAAYMAQEAAAAAAAA": "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", "AYQQ5AADQAAAOAEARAFQJAABBADIB7777777777777777777777767YAAAAAAAAAAAAOQAAAAAAAQAAAABAMQAAAABDAAAAAAEAAAAAAAAAAAAAAAAAAAAAAADBQCAAAAAAAAAA": "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", "HUIAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAAAAAALUASJ3EZYN2AAAAAAAIAEAAAABSCQKL3IYIJ2AAAAAAAIAEAAAABLBCABAAAAABAEGABBAMAAACAAAAAAAOQAAAAAAAQAAAABAMQAAAAAAUAAAAAACAAAAAAAAAAAAAAAAAAAAAAAAAYAAAAAAAAAAA": "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", "HUJAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAQAAAAAAQQGAAZAADIAAAAGULKMMQKAAAAAAEAAAAAIAAAAAAGIRBNAWEYZAUAAAQAAAAAAAAAAAAADUAAAAAAAEAAAAAIDEAAAKAAAAAABAAAAB7IAAAAAAAAAAAAAAAAAYMAQEAAAAAAAA": "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", "IYBAAAAABAAACAABBYAAAKAAAMAAGEAAAABRAEAAAEHCAAAAAAAABCAAAAAABAEQAEAAAAAKAAAAABAAAAAP2AAAAAAQAAAAAAAAAAACAAAAAAAAAAAA": "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", "HVIACAAAABQAAGAAAQ4AAAAAGQQAARC4GAAAIOCAAD6P7777777777YDAAAAAAAAAAAFIBUAFS7EOCAAAAAGZJY26AAQAAAAWBMXUYZRAAAAADAAAAABQAAAAAIAAAAAAABAAADAZ7Y3IGIEAAAABB3I4AZAAAAAAAAAAAIAAAABTWKOGXQAGAAAABQLH5GGMIAAAAAYAAAAAMAAAAACAAAAAAAAIAAAYCPOG2JTBAAAAAAO2HAGKAAAAAAACAAAAAZMEZXJRXCQAAAAGAAAAADAAAAAAQAAAAAAACAAACAD3R6TMYIAAAAADSRIDSYAAAAAABAAAAADEEACAAAGBT7RWQMQIAAAQAAAAAAADHMXGPDNAYAQAAAAAAAAAYCFDSRIDSYAAAAAAAQAAAAGJSBBDK4AYAAAAAAAAAAAMACQCAACAAAAA2AIRAEAAAAAAAAAAACAA4AAAACAAAAAAACCAZIAAAAAAEAAAAH5AAAAAAAAAAAAAAAAADBQCAAAAAAAAAA": "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", "GEMAAAYAAEHAAAARC4EAAAQWBQAAAAAAAAAQAAAAIBCAAAGQAEAAAAAQAAAABAEQAEAAAACQAAAAAAIAAAAAAAAAAAAAAAAAAAAAAAGDAEAAAAAAAAAA": "DAAAAExTS1P9AQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgaW5Qb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgaW5Db2xvcjsKbGF5b3V0KGxvY2F0aW9uID0gMikgaW4gaGFsZjMgaW5TaGFkb3dQYXJhbXM7CmxheW91dChsb2NhdGlvbiA9IDApIG91dCBoYWxmMyB2aW5TaGFkb3dQYXJhbXNfUzA7CmxheW91dChsb2NhdGlvbiA9IDEpIG91dCBoYWxmNCB2aW5Db2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFByaW1pdGl2ZSBQcm9jZXNzb3IgUlJlY3RTaGFkb3cKCXZpblNoYWRvd1BhcmFtc19TMCA9IGluU2hhZG93UGFyYW1zOwoJdmluQ29sb3JfUzAgPSBpbkNvbG9yOwoJZmxvYXQyIF90bXBfMF9pblBvc2l0aW9uID0gaW5Qb3NpdGlvbjsKCXNrX1Bvc2l0aW9uID0gX3RtcF8wX2luUG9zaXRpb24ueHkwMTsKfQoAAAAAAAAAogIAAGxheW91dChtZXRhbCwgYmluZGluZz0wKSBzYW1wbGVyMkQgdVRleHR1cmVTYW1wbGVyXzBfUzA7CmxheW91dCAobWV0YWwsIGJpbmRpbmc9MCkgdW5pZm9ybSB1bmlmb3JtQnVmZmVyCnsKCWxheW91dChvZmZzZXQ9MCkgZmxvYXQ0IHNrX1JUQWRqdXN0Owp9CjsKbGF5b3V0KGxvY2F0aW9uID0gMCkgaW4gaGFsZjMgdmluU2hhZG93UGFyYW1zX1MwOwpsYXlvdXQobG9jYXRpb24gPSAxKSBpbiBoYWxmNCB2aW5Db2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFJSZWN0U2hhZG93CgloYWxmMyBzaGFkb3dQYXJhbXM7CglzaGFkb3dQYXJhbXMgPSB2aW5TaGFkb3dQYXJhbXNfUzA7CgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmluQ29sb3JfUzA7CgloYWxmIGQgPSBsZW5ndGgoc2hhZG93UGFyYW1zLnh5KTsKCWZsb2F0MiB1diA9IGZsb2F0MihzaGFkb3dQYXJhbXMueiAqICgxLjAgLSBkKSwgMC41KTsKCWhhbGYgZmFjdG9yID0gc2FtcGxlKHVUZXh0dXJlU2FtcGxlcl8wX1MwLCB1dikuMDAwci5hOwoJaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNChmYWN0b3IpOwoJewoJCS8vIFhmZXIgUHJvY2Vzc29yOiBQb3J0ZXIgRHVmZgoJCXNrX0ZyYWdDb2xvciA9IG91dHB1dENvbG9yX1MwICogb3V0cHV0Q292ZXJhZ2VfUzA7Cgl9Cn0KAAAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABgAAAAAQAAAAAAAAADAAAAHQAAAAAAAAABAAAACQAAAAgAAAABAAAAHgAAAAwAAAABAAAAGAAAAAAAAABQAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAAAAAAA", "HUQAAAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAAKAAYAAAACAAAAAAYEIBAAAAFAAAAAAQAAAAH5AAAAAAIAAAAAAAAAAABQAAQAAAAAAAA": "DAAAAExTS1NTAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGhhbGY0IGNvbG9yOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IG91dCBoYWxmNCB2Y29sb3JfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmNvbG9yX1MwID0gY29sb3I7Cglza19Qb3NpdGlvbiA9IHBvc2l0aW9uLnh5MDE7Cn0KAAAAAACmAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzA7CglvdXRwdXRDb2xvcl9TMCA9IHZjb2xvcl9TMDsKCWNvbnN0IGhhbGY0IG91dHB1dENvdmVyYWdlX1MwID0gaGFsZjQoMSk7Cgl7CgkJLy8gWGZlciBQcm9jZXNzb3I6IFBvcnRlciBEdWZmCgkJc2tfRnJhZ0NvbG9yID0gb3V0cHV0Q29sb3JfUzAgKiBvdXRwdXRDb3ZlcmFnZV9TMDsKCQlza19GcmFnQ29sb3IgPSBza19GcmFnQ29sb3IuYTAwMDsKCX0KfQoAAAAAAAABAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwAAAABAAAAAAAAAAIAAAAdAAAAAAAAAAEAAAAJAAAACAAAAAEAAAAMAAAAAAAAAAoAAAAAAAAAAQAAAP0AAAA=", "HVIAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAAAAAAAAFIBVWKMG7QAAAAAAGVXOVKVEAAAAADAAAAAAAAEAAAUEZEVBAOCIAAAAIO2HAGIAAAAEAAAAAAAAZVF2VOVZBAEAAAYAAAAAAABAAABAGLFQIDUSAAAAADSRIDSYAAAAAABAAAAADEEACAAAKAMSLQQHBEAAAAAAQAAAAGJJJSJKCA4EQAAAAAAAABAAVOGEAMLQDAAAAACAAAAAJAIMHNDQDEAAAAAAAAAAAAAOQAAAAAAAQAAAABAMQAAAAIYAAAAABAAAAAAAAAAAAAAAAAAQAAAAAYEAQEAAAAAAAA": "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", "HUQAAAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAAKAAYAAAACAAAAAAACCAYAABIAAAAAAEAAAAH5AAAAAAAAAAAAAAAAADBQCAAAAAAAAAA": "DAAAAExTS1NTAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGhhbGY0IGNvbG9yOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IG91dCBoYWxmNCB2Y29sb3JfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmNvbG9yX1MwID0gY29sb3I7Cglza19Qb3NpdGlvbiA9IHBvc2l0aW9uLnh5MDE7Cn0KAAAAAACCAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzA7CglvdXRwdXRDb2xvcl9TMCA9IHZjb2xvcl9TMDsKCWNvbnN0IGhhbGY0IG91dHB1dENvdmVyYWdlX1MwID0gaGFsZjQoMSk7Cgl7CgkJLy8gWGZlciBQcm9jZXNzb3I6IFBvcnRlciBEdWZmCgkJc2tfRnJhZ0NvbG9yID0gb3V0cHV0Q29sb3JfUzAgKiBvdXRwdXRDb3ZlcmFnZV9TMDsKCX0KfQoAAAAAAAABAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFQAAAABAAAAAAAAAAIAAAAdAAAAAAAAAAEAAAAJAAAACAAAAAEAAAAMAAAAAAAAAFAAAAABAAAAAQAAAAUAAAAAAAAAAQAAAAUAAAAAAAAAAQAAAP0AAAA=", "HVIAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAAAAAAAAFIBVWKMG7QAAAAAAGVXOVKVEAAAAADAAAAAGQMRXOGTQEAAAABB3I4AZAAAAAQAAAAAAADGUXKV2XEEAQAADAAAAAAQETXGGTWEIAAAABZIUBZMAAAAAAAQAAAADEWDE5ZRU5BAAAAAAAAAAAAK7CCAGVYBQAAAABAAAAAAQEGDWRYBSQAAAAAAAAAAAAHIAAAAAAAIAAAAAQGIAEMAAAAAAQAAAAAAAAAAAAAAAAAIAAAAAICAACAAAAAAAA": "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", "AYQA5AADQAAAOAEARAFQJAABBADIB7777777777777777777777767YAAAAAAAAAAAAOQAAAAAAAQAAAABAMQAAAABIAAAAAAEAAAAAAAAAAAAAAAAAAAAAAADBQCAAAAAAAAAA": "DAAAAExTS1OAAgAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7CglsYXlvdXQob2Zmc2V0PTE2KSBmbG9hdDQgdWxvY2FsTWF0cml4X1MwOwp9CjsKbGF5b3V0KGxvY2F0aW9uID0gMCkgaW4gZmxvYXQyIGluUG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGhhbGY0IGluQ29sb3I7CmxheW91dChsb2NhdGlvbiA9IDIpIGluIGZsb2F0NCBpbkNpcmNsZUVkZ2U7CmxheW91dChsb2NhdGlvbiA9IDApIG91dCBmbG9hdDQgdmluQ2lyY2xlRWRnZV9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMSkgb3V0IGhhbGY0IHZpbkNvbG9yX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gUHJpbWl0aXZlIFByb2Nlc3NvciBDaXJjbGVHZW9tZXRyeVByb2Nlc3NvcgoJdmluQ2lyY2xlRWRnZV9TMCA9IGluQ2lyY2xlRWRnZTsKCXZpbkNvbG9yX1MwID0gaW5Db2xvcjsKCWZsb2F0MiBfdG1wXzBfaW5Qb3NpdGlvbiA9IGluUG9zaXRpb247CglmbG9hdDIgX3RtcF8xX2luUG9zaXRpb24gPSB1bG9jYWxNYXRyaXhfUzAueHogKiBpblBvc2l0aW9uICsgdWxvY2FsTWF0cml4X1MwLnl3OwoJc2tfUG9zaXRpb24gPSBfdG1wXzBfaW5Qb3NpdGlvbi54eTAxOwp9CgAAAACbAgAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7CglsYXlvdXQob2Zmc2V0PTE2KSBmbG9hdDQgdWxvY2FsTWF0cml4X1MwOwp9CjsKbGF5b3V0KGxvY2F0aW9uID0gMCkgaW4gZmxvYXQ0IHZpbkNpcmNsZUVkZ2VfUzA7CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGhhbGY0IHZpbkNvbG9yX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgQ2lyY2xlR2VvbWV0cnlQcm9jZXNzb3IKCWZsb2F0NCBjaXJjbGVFZGdlOwoJY2lyY2xlRWRnZSA9IHZpbkNpcmNsZUVkZ2VfUzA7CgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmluQ29sb3JfUzA7CglmbG9hdCBkID0gbGVuZ3RoKGNpcmNsZUVkZ2UueHkpOwoJaGFsZiBkaXN0YW5jZVRvT3V0ZXJFZGdlID0gaGFsZihjaXJjbGVFZGdlLnogKiAoMS4wIC0gZCkpOwoJaGFsZiBlZGdlQWxwaGEgPSBzYXR1cmF0ZShkaXN0YW5jZVRvT3V0ZXJFZGdlKTsKCWhhbGY0IG91dHB1dENvdmVyYWdlX1MwID0gaGFsZjQoZWRnZUFscGhhKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABgAAAAAQAAAAAAAAADAAAAHQAAAAAAAAABAAAACQAAAAgAAAABAAAAHwAAAAwAAAABAAAAHAAAAAAAAABQAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAAAAAAA", "HVIAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAAAAAAAAGIBIAAABAAAAANAEAAAAAAAAAAAAAABAAOAAAABAAAAAAABBAMAAAABIAAAAAAEAAAAH5AAAAAAIAAAABAAAAADBQCAQAAAAAAAA": "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", "DAQAAAAAAAAAAAAAAJQAAIGAAEACBYQCAGAEFAIBAAAAAABAAAAAAAAAAAAAAOQAAAAAAAQAAAABAMQAAAAFAAAAAAAQAAAA7UAAAAAAAAAAAAAAAAAMGAIAAAAAAAAA": "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", "HUQAAAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAABAAIAAAAAAIIDAAAFAAAAAAAQAAAA7UAAAAABAAAAAAAAAAAAEAAAAAAAAAAA": "DAAAAExTS1NTAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGhhbGY0IGNvbG9yOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IG91dCBoYWxmNCB2Y29sb3JfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmNvbG9yX1MwID0gY29sb3I7Cglza19Qb3NpdGlvbiA9IHBvc2l0aW9uLnh5MDE7Cn0KAAAAAABPAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzA7CglvdXRwdXRDb2xvcl9TMCA9IHZjb2xvcl9TMDsKCWNvbnN0IGhhbGY0IG91dHB1dENvdmVyYWdlX1MwID0gaGFsZjQoMSk7Cgl7CgkJLy8gWGZlciBQcm9jZXNzb3I6IERpc2FibGUgQ29sb3IKCX0KfQoAAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPAAAAAEAAAAAAAAAAgAAAB0AAAAAAAAAAQAAAAkAAAAIAAAAAQAAAAwAAAAAAAAAUAAAAAAAAAAAAAAA/QAAAA==", "AYQA5AADQAAAOAEARAFQJAABBADIB7777777777777777777777767YAAAAAAAAAAAAMWAEQYX3QQAIAACAO2VADHYAAAAEALJ3XKFISAAAAABQAAAAEAAAAAA2JTW5YCMAQAAGAEENLQDAAAAAAAACAAAAAAQCGWZJQ36AAAAAAA2W52VKUQAAAAAMAAAAAAAAQAAGQMRXOGTQEAAAABB3I4AZAAAAAQAAAAAAADGUXKV2XEEAQAADAAAAAAAAEAAAEBE5ZRU5RCAAAAAOKFAOLAAAAAAAEAAAAAMQQAIAABIGJ3TDJ2CAAAAAACAAAAAZNEZDO4NHAIAAAAAAAAAEACVYYQBROAMAAAAAIAAAABEBBQ5UOAMQAAAAAAAAAACABKBAABAAAAAFAEEQCEAAAAAAAAAAAQAB5QAYAAAEAAAAAADIACAAAAAIAAAAAQCIACACGAAAAAAIAAAAP2AAAAAAAAAAAAAAAAAGDAEAAAAAAAAAA": "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", "I4EAAAIAAEHAAAAAAAAAAAAAAAAIQAAAAAAIBEABAAAAARQAAAAACAAAAD6QAAAAAEAAAABAAAAAAAQAAAAAAAAAAA": "DAAAAExTS1M2AgAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7CglsYXlvdXQob2Zmc2V0PTE2KSBmbG9hdDQgdWFmZmluZU1hdHJpeF9TMDsKCWxheW91dChvZmZzZXQ9MzIpIGZsb2F0MiB1dHJhbnNsYXRlX1MwOwoJbGF5b3V0KG9mZnNldD00MCkgaGFsZjQgdWNvbG9yX1MwOwp9CjsKbGF5b3V0KGxvY2F0aW9uID0gMCkgaW4gZmxvYXQyIGlucHV0UG9pbnQ7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIHRlc3NlbGxhdGVfU2ltcGxlVHJpYW5nbGVTaGFkZXIKCWZsb2F0MngyIEFGRklORV9NQVRSSVggPSBmbG9hdDJ4Mih1YWZmaW5lTWF0cml4X1MwLnh5LCB1YWZmaW5lTWF0cml4X1MwLnp3KTsKCWZsb2F0MiBUUkFOU0xBVEUgPSB1dHJhbnNsYXRlX1MwOwoJZmxvYXQyIGxvY2FsY29vcmQgPSBpbnB1dFBvaW50OwoJZmxvYXQyIHZlcnRleHBvcyA9IEFGRklORV9NQVRSSVggKiBsb2NhbGNvb3JkICsgVFJBTlNMQVRFOwoJc2tfUG9zaXRpb24gPSB2ZXJ0ZXhwb3MueHkwMTsKfQoAAAAAAACKAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7CglsYXlvdXQob2Zmc2V0PTE2KSBmbG9hdDQgdWFmZmluZU1hdHJpeF9TMDsKCWxheW91dChvZmZzZXQ9MzIpIGZsb2F0MiB1dHJhbnNsYXRlX1MwOwoJbGF5b3V0KG9mZnNldD00MCkgaGFsZjQgdWNvbG9yX1MwOwp9CjsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIHRlc3NlbGxhdGVfU2ltcGxlVHJpYW5nbGVTaGFkZXIKCWhhbGY0IG91dHB1dENvbG9yX1MwID0gdWNvbG9yX1MwOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogRGlzYWJsZSBDb2xvcgoJfQp9CgAAAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMAAAAAEAAAAAAAAAAQAAAB0AAAAAAAAAAQAAAAgAAAAAAAAARgAAAAAAAAAAAAAA/QAAAA==", "AYQA5AADQAAAOAEARAFQJAABBADIB7777777777777777777777767YAAAAAAAAAAAAOQAAAAAAAQAAAABAMQAAAABIAAAAAAEAAAAH5AAAAAAAAAAAAAAAAADBQCAAAAAAAAAA": "DAAAAExTS1OAAgAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7CglsYXlvdXQob2Zmc2V0PTE2KSBmbG9hdDQgdWxvY2FsTWF0cml4X1MwOwp9CjsKbGF5b3V0KGxvY2F0aW9uID0gMCkgaW4gZmxvYXQyIGluUG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGhhbGY0IGluQ29sb3I7CmxheW91dChsb2NhdGlvbiA9IDIpIGluIGZsb2F0NCBpbkNpcmNsZUVkZ2U7CmxheW91dChsb2NhdGlvbiA9IDApIG91dCBmbG9hdDQgdmluQ2lyY2xlRWRnZV9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMSkgb3V0IGhhbGY0IHZpbkNvbG9yX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gUHJpbWl0aXZlIFByb2Nlc3NvciBDaXJjbGVHZW9tZXRyeVByb2Nlc3NvcgoJdmluQ2lyY2xlRWRnZV9TMCA9IGluQ2lyY2xlRWRnZTsKCXZpbkNvbG9yX1MwID0gaW5Db2xvcjsKCWZsb2F0MiBfdG1wXzBfaW5Qb3NpdGlvbiA9IGluUG9zaXRpb247CglmbG9hdDIgX3RtcF8xX2luUG9zaXRpb24gPSB1bG9jYWxNYXRyaXhfUzAueHogKiBpblBvc2l0aW9uICsgdWxvY2FsTWF0cml4X1MwLnl3OwoJc2tfUG9zaXRpb24gPSBfdG1wXzBfaW5Qb3NpdGlvbi54eTAxOwp9CgAAAACbAgAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7CglsYXlvdXQob2Zmc2V0PTE2KSBmbG9hdDQgdWxvY2FsTWF0cml4X1MwOwp9CjsKbGF5b3V0KGxvY2F0aW9uID0gMCkgaW4gZmxvYXQ0IHZpbkNpcmNsZUVkZ2VfUzA7CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGhhbGY0IHZpbkNvbG9yX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgQ2lyY2xlR2VvbWV0cnlQcm9jZXNzb3IKCWZsb2F0NCBjaXJjbGVFZGdlOwoJY2lyY2xlRWRnZSA9IHZpbkNpcmNsZUVkZ2VfUzA7CgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmluQ29sb3JfUzA7CglmbG9hdCBkID0gbGVuZ3RoKGNpcmNsZUVkZ2UueHkpOwoJaGFsZiBkaXN0YW5jZVRvT3V0ZXJFZGdlID0gaGFsZihjaXJjbGVFZGdlLnogKiAoMS4wIC0gZCkpOwoJaGFsZiBlZGdlQWxwaGEgPSBzYXR1cmF0ZShkaXN0YW5jZVRvT3V0ZXJFZGdlKTsKCWhhbGY0IG91dHB1dENvdmVyYWdlX1MwID0gaGFsZjQoZWRnZUFscGhhKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABgAAAAAQAAAAAAAAADAAAAHQAAAAAAAAABAAAACQAAAAgAAAABAAAAHwAAAAwAAAABAAAAHAAAAAAAAABQAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAD9AAAA", "HUIAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAAAAAALUAQBAEAQAAAAGQCBAMQACAIAAAAAACQAGAAAAAQAAAAAAAQQGAAAABIAAAAAAEAAAAAAAAAAAAAAAAACAAAAAABQAAQAAAAAAAA": "DAAAAExTS1PoAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7CglsYXlvdXQob2Zmc2V0PTE2KSBmbG9hdDQgdWNsYW1wX1MxX2MwOwoJbGF5b3V0KG9mZnNldD0zMikgZmxvYXQzeDMgdW1hdHJpeF9TMTsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gZmxvYXQyIGxvY2FsQ29vcmQ7CmxheW91dChsb2NhdGlvbiA9IDApIG91dCBmbG9hdDIgdlRyYW5zZm9ybWVkQ29vcmRzXzJfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJc2tfUG9zaXRpb24gPSBwb3NpdGlvbi54eTAxOwoJewoJCXZUcmFuc2Zvcm1lZENvb3Jkc18yX1MwID0gZmxvYXQzeDIodW1hdHJpeF9TMSkgKiBsb2NhbENvb3JkLnh5MTsKCX0KfQoAAAAA5QMAAGxheW91dChtZXRhbCwgYmluZGluZz0wKSBzYW1wbGVyMkQgdVRleHR1cmVTYW1wbGVyXzBfUzE7CmxheW91dCAobWV0YWwsIGJpbmRpbmc9MCkgdW5pZm9ybSB1bmlmb3JtQnVmZmVyCnsKCWxheW91dChvZmZzZXQ9MCkgZmxvYXQ0IHNrX1JUQWRqdXN0OwoJbGF5b3V0KG9mZnNldD0xNikgZmxvYXQ0IHVjbGFtcF9TMV9jMDsKCWxheW91dChvZmZzZXQ9MzIpIGZsb2F0M3gzIHVtYXRyaXhfUzE7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgdlRyYW5zZm9ybWVkQ29vcmRzXzJfUzA7CmhhbGY0IFRleHR1cmVFZmZlY3RfUzFfYzAoaGFsZjQgX2lucHV0KSAKewoJZmxvYXQyIGluQ29vcmQgPSB2VHJhbnNmb3JtZWRDb29yZHNfMl9TMDsKCWZsb2F0MiBzdWJzZXRDb29yZDsKCXN1YnNldENvb3JkLnggPSBpbkNvb3JkLng7CglzdWJzZXRDb29yZC55ID0gaW5Db29yZC55OwoJZmxvYXQyIGNsYW1wZWRDb29yZDsKCWNsYW1wZWRDb29yZCA9IGNsYW1wKHN1YnNldENvb3JkLCB1Y2xhbXBfUzFfYzAueHksIHVjbGFtcF9TMV9jMC56dyk7CgloYWxmNCB0ZXh0dXJlQ29sb3IgPSBzYW1wbGUodVRleHR1cmVTYW1wbGVyXzBfUzEsIGNsYW1wZWRDb29yZCk7CglyZXR1cm4gdGV4dHVyZUNvbG9yOwp9CmhhbGY0IE1hdHJpeEVmZmVjdF9TMShoYWxmNCBfaW5wdXQpIAp7CglyZXR1cm4gVGV4dHVyZUVmZmVjdF9TMV9jMChfaW5wdXQpOwp9CnZvaWQgbWFpbigpIAp7CgkvLyBTdGFnZSAwLCBRdWFkUGVyRWRnZUFBR2VvbWV0cnlQcm9jZXNzb3IKCWhhbGY0IG91dHB1dENvbG9yX1MwID0gaGFsZjQoMSk7Cgljb25zdCBoYWxmNCBvdXRwdXRDb3ZlcmFnZV9TMCA9IGhhbGY0KDEpOwoJaGFsZjQgb3V0cHV0X1MxOwoJb3V0cHV0X1MxID0gTWF0cml4RWZmZWN0X1MxKG91dHB1dENvbG9yX1MwKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRfUzEgKiBvdXRwdXRDb3ZlcmFnZV9TMDsKCX0KfQoAAAAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8AAAAAQAAAAAAAAACAAAAHQAAAAAAAAABAAAAHQAAAAgAAAABAAAAEAAAAAAAAABQAAAAAAAAAAEAAAAAAAAA", "HVIAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAAAAAAAAHIAH6777773ZY2J5SAAAAABWKOGXQAEAAAAAAAAEAAAAAZDEB2R32B4CAAAAAAAAAAAAZ3FHDLYADAAAAAAAABAAAAACYCEAIAAAAAAABUAQIDEAAAAAAAAAABUABAAAAAEAAAAAIBEABAAAFAAAAAAAQAAAA7UAAAAAAAAAAAIAAAAAMGAICAAAAAAAA": "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", "HUJAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAQAAAAAAQQGACQAGAAAAAQAAAAAAAQQGAAAKAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAYMAQAAAAAAAAA": "DAAAAExTS1NkAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGZsb2F0MiBsb2NhbENvb3JkOwpsYXlvdXQobG9jYXRpb24gPSAwKSBvdXQgZmxvYXQyIHZsb2NhbENvb3JkX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gUHJpbWl0aXZlIFByb2Nlc3NvciBRdWFkUGVyRWRnZUFBR2VvbWV0cnlQcm9jZXNzb3IKCXZsb2NhbENvb3JkX1MwID0gbG9jYWxDb29yZDsKCXNrX1Bvc2l0aW9uID0gcG9zaXRpb24ueHkwMTsKfQoAAAAALgIAAGxheW91dChtZXRhbCwgYmluZGluZz0wKSBzYW1wbGVyMkQgdVRleHR1cmVTYW1wbGVyXzBfUzA7CmxheW91dCAobWV0YWwsIGJpbmRpbmc9MCkgdW5pZm9ybSB1bmlmb3JtQnVmZmVyCnsKCWxheW91dChvZmZzZXQ9MCkgZmxvYXQ0IHNrX1JUQWRqdXN0Owp9CjsKbGF5b3V0KGxvY2F0aW9uID0gMCkgaW4gZmxvYXQyIHZsb2NhbENvb3JkX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMCA9IGhhbGY0KDEpOwoJZmxvYXQyIHRleENvb3JkOwoJdGV4Q29vcmQgPSB2bG9jYWxDb29yZF9TMDsKCW91dHB1dENvbG9yX1MwID0gKGJsZW5kX21vZHVsYXRlKHNhbXBsZSh1VGV4dHVyZVNhbXBsZXJfMF9TMCwgdGV4Q29vcmQpLCBoYWxmNCgxKSkpOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAVAAAAAEAAAAAAAAAAgAAAB0AAAAAAAAAAQAAAB0AAAAIAAAAAQAAABAAAAAAAAAAUAAAAAEAAAABAAAABQAAAAAAAAABAAAABQAAAAAAAAABAAAAAAAAAA==", "HUJAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAQAAAAAAQQGACQAGAAAAAQAAAAAAAQQGAAAIYAAAAABAAAAB7IAAAAACAAAAAIAAAAAYMAQEAAAAAAAA": "DAAAAExTS1NkAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGZsb2F0MiBsb2NhbENvb3JkOwpsYXlvdXQobG9jYXRpb24gPSAwKSBvdXQgZmxvYXQyIHZsb2NhbENvb3JkX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gUHJpbWl0aXZlIFByb2Nlc3NvciBRdWFkUGVyRWRnZUFBR2VvbWV0cnlQcm9jZXNzb3IKCXZsb2NhbENvb3JkX1MwID0gbG9jYWxDb29yZDsKCXNrX1Bvc2l0aW9uID0gcG9zaXRpb24ueHkwMTsKfQoAAAAALgIAAGxheW91dChtZXRhbCwgYmluZGluZz0wKSBzYW1wbGVyMkQgdVRleHR1cmVTYW1wbGVyXzBfUzA7CmxheW91dCAobWV0YWwsIGJpbmRpbmc9MCkgdW5pZm9ybSB1bmlmb3JtQnVmZmVyCnsKCWxheW91dChvZmZzZXQ9MCkgZmxvYXQ0IHNrX1JUQWRqdXN0Owp9CjsKbGF5b3V0KGxvY2F0aW9uID0gMCkgaW4gZmxvYXQyIHZsb2NhbENvb3JkX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMCA9IGhhbGY0KDEpOwoJZmxvYXQyIHRleENvb3JkOwoJdGV4Q29vcmQgPSB2bG9jYWxDb29yZF9TMDsKCW91dHB1dENvbG9yX1MwID0gKGJsZW5kX21vZHVsYXRlKHNhbXBsZSh1VGV4dHVyZVNhbXBsZXJfMF9TMCwgdGV4Q29vcmQpLCBoYWxmNCgxKSkpOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAVAAAAAEAAAAAAAAAAgAAAB0AAAAAAAAAAQAAAB0AAAAIAAAAAQAAABAAAAAAAAAARgAAAAEAAAABAAAABQAAAAAAAAABAAAABQAAAAAAAAABAAAA/QAAAA==", "HVIAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAAAAAAAAFIBVWKMG7QAAAAAAGVXOVKVEAAAAADAAAAAAAAEAAAACEC4AA4JAAAAAIO2HAGIAAAAEAAAAAAAAZVF2VOVZBAEAAAYAAAAAAABAAAAABAXIAHCIAAAAADSRIDSYAAAAAABAAAAADEEACAAAABCBOAAOEQAAAAAAQAAAAGIBEIFYABYSAAAAAAAAABAAVOGEAMLQDAAAAACAAAAAJAIMHNDQDEAAAAAAAAAAAAAOQAAAAAAAQAAAABAMQAAAAIYAAAAABAAAAAAAAAAAAAAAAAAQAAAAAQEAQEAAAAAAAA": "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", "FAAQMYAAMAAAEADAAABAEYAAAICIAB5AABQAAAQAMAAAEATAAABAIIGAAEDCBYQCA4AAAAAAEAB5AAAAADIGD6APJ4AQAABQAAAABAAAAAAIBDDUDD7MGUYAAAAAYAAAAAAAAAAAACQAGAAAAAQAAAAAAAQQGAAAKAAAAAABAAAAB7IAAAAAAAAAAAQAAAAAYMAQAAAAAAAAA": "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", "HUJAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAQAAAAAAQQGACQAGAAAAAQAAAAAAAQQGAAAIYAAAAABAAAAAAAAAAAAAAAAAAQAAAAAYMAQEAAAAAAAA": "DAAAAExTS1NkAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGZsb2F0MiBsb2NhbENvb3JkOwpsYXlvdXQobG9jYXRpb24gPSAwKSBvdXQgZmxvYXQyIHZsb2NhbENvb3JkX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gUHJpbWl0aXZlIFByb2Nlc3NvciBRdWFkUGVyRWRnZUFBR2VvbWV0cnlQcm9jZXNzb3IKCXZsb2NhbENvb3JkX1MwID0gbG9jYWxDb29yZDsKCXNrX1Bvc2l0aW9uID0gcG9zaXRpb24ueHkwMTsKfQoAAAAALgIAAGxheW91dChtZXRhbCwgYmluZGluZz0wKSBzYW1wbGVyMkQgdVRleHR1cmVTYW1wbGVyXzBfUzA7CmxheW91dCAobWV0YWwsIGJpbmRpbmc9MCkgdW5pZm9ybSB1bmlmb3JtQnVmZmVyCnsKCWxheW91dChvZmZzZXQ9MCkgZmxvYXQ0IHNrX1JUQWRqdXN0Owp9CjsKbGF5b3V0KGxvY2F0aW9uID0gMCkgaW4gZmxvYXQyIHZsb2NhbENvb3JkX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMCA9IGhhbGY0KDEpOwoJZmxvYXQyIHRleENvb3JkOwoJdGV4Q29vcmQgPSB2bG9jYWxDb29yZF9TMDsKCW91dHB1dENvbG9yX1MwID0gKGJsZW5kX21vZHVsYXRlKHNhbXBsZSh1VGV4dHVyZVNhbXBsZXJfMF9TMCwgdGV4Q29vcmQpLCBoYWxmNCgxKSkpOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAVAAAAAEAAAAAAAAAAgAAAB0AAAAAAAAAAQAAAB0AAAAIAAAAAQAAABAAAAAAAAAARgAAAAEAAAABAAAABQAAAAAAAAABAAAABQAAAAAAAAABAAAAAAAAAA==", "CIAAAAAAQAARQAAYQAAAAGFYQAABRAAAAEEAAAAAAARAEAEABYAAAAEAAAAAAAEEBQAAAACQAAAAAAIAAAAAAAAAAAAAAAAAAAAAAAGDAEBAAAAAAAAA": "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", "HUJAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAQAAAAAAQQGACQAGAAAAAQAAAAAAAQQGAAAKAAAAAABAAAAAAAAAAAAAAAAAAQAAAAAYMAQEAAAAAAAA": "DAAAAExTS1NkAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGZsb2F0MiBsb2NhbENvb3JkOwpsYXlvdXQobG9jYXRpb24gPSAwKSBvdXQgZmxvYXQyIHZsb2NhbENvb3JkX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gUHJpbWl0aXZlIFByb2Nlc3NvciBRdWFkUGVyRWRnZUFBR2VvbWV0cnlQcm9jZXNzb3IKCXZsb2NhbENvb3JkX1MwID0gbG9jYWxDb29yZDsKCXNrX1Bvc2l0aW9uID0gcG9zaXRpb24ueHkwMTsKfQoAAAAALgIAAGxheW91dChtZXRhbCwgYmluZGluZz0wKSBzYW1wbGVyMkQgdVRleHR1cmVTYW1wbGVyXzBfUzA7CmxheW91dCAobWV0YWwsIGJpbmRpbmc9MCkgdW5pZm9ybSB1bmlmb3JtQnVmZmVyCnsKCWxheW91dChvZmZzZXQ9MCkgZmxvYXQ0IHNrX1JUQWRqdXN0Owp9CjsKbGF5b3V0KGxvY2F0aW9uID0gMCkgaW4gZmxvYXQyIHZsb2NhbENvb3JkX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMCA9IGhhbGY0KDEpOwoJZmxvYXQyIHRleENvb3JkOwoJdGV4Q29vcmQgPSB2bG9jYWxDb29yZF9TMDsKCW91dHB1dENvbG9yX1MwID0gKGJsZW5kX21vZHVsYXRlKHNhbXBsZSh1VGV4dHVyZVNhbXBsZXJfMF9TMCwgdGV4Q29vcmQpLCBoYWxmNCgxKSkpOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAVAAAAAEAAAAAAAAAAgAAAB0AAAAAAAAAAQAAAB0AAAAIAAAAAQAAABAAAAAAAAAAUAAAAAEAAAABAAAABQAAAAAAAAABAAAABQAAAAAAAAABAAAAAAAAAA==", "HUIAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAAAAAALUASJ3EZYN2AAAAAAAIAEAAAABSCQKL3IYIJ2AAAAAAAIAEAAAABLBCABAAAAABAEGABBAMAAACAAAAAAAOQAAAAAAAQAAAABAMQAAAAAARQAAAAACAAAAAAAAAAAAAAAAAAAAAAAAAYAAIAAAAAAAA": "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", "HUJAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAQAAAAAAQQGACQAGAAAAAQAAAAAAAQQGAAAKAAAAAABAAAAB7IAAAAACAAAAAIAAAAAYMAQEAAAAAAAA": "DAAAAExTS1NkAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGZsb2F0MiBsb2NhbENvb3JkOwpsYXlvdXQobG9jYXRpb24gPSAwKSBvdXQgZmxvYXQyIHZsb2NhbENvb3JkX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gUHJpbWl0aXZlIFByb2Nlc3NvciBRdWFkUGVyRWRnZUFBR2VvbWV0cnlQcm9jZXNzb3IKCXZsb2NhbENvb3JkX1MwID0gbG9jYWxDb29yZDsKCXNrX1Bvc2l0aW9uID0gcG9zaXRpb24ueHkwMTsKfQoAAAAALgIAAGxheW91dChtZXRhbCwgYmluZGluZz0wKSBzYW1wbGVyMkQgdVRleHR1cmVTYW1wbGVyXzBfUzA7CmxheW91dCAobWV0YWwsIGJpbmRpbmc9MCkgdW5pZm9ybSB1bmlmb3JtQnVmZmVyCnsKCWxheW91dChvZmZzZXQ9MCkgZmxvYXQ0IHNrX1JUQWRqdXN0Owp9CjsKbGF5b3V0KGxvY2F0aW9uID0gMCkgaW4gZmxvYXQyIHZsb2NhbENvb3JkX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMCA9IGhhbGY0KDEpOwoJZmxvYXQyIHRleENvb3JkOwoJdGV4Q29vcmQgPSB2bG9jYWxDb29yZF9TMDsKCW91dHB1dENvbG9yX1MwID0gKGJsZW5kX21vZHVsYXRlKHNhbXBsZSh1VGV4dHVyZVNhbXBsZXJfMF9TMCwgdGV4Q29vcmQpLCBoYWxmNCgxKSkpOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAVAAAAAEAAAAAAAAAAgAAAB0AAAAAAAAAAQAAAB0AAAAIAAAAAQAAABAAAAAAAAAAUAAAAAEAAAABAAAABQAAAAAAAAABAAAABQAAAAAAAAABAAAA/QAAAA==", "FAIQMYAAMAAAEADAAABAEYAAAICIAB5AABQAAAQAMAAAEATAAABAIIGAAEDCBYQCA4AAAAAAAA5AAAAAAABAAAAACAZAAAAAKAAAAAABAAAAB7IAAAAAAAAAAAAAAAAAYMAQAAAAAAAAA": "DAAAAExTS1PmDAAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDQgcmFkaWlfc2VsZWN0b3I7CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGZsb2F0NCBjb3JuZXJfYW5kX3JhZGl1c19vdXRzZXRzOwpsYXlvdXQobG9jYXRpb24gPSAyKSBpbiBmbG9hdDQgYWFfYmxvYXRfYW5kX2NvdmVyYWdlOwpsYXlvdXQobG9jYXRpb24gPSAzKSBpbiBmbG9hdDQgcmFkaWlfeDsKbGF5b3V0KGxvY2F0aW9uID0gNCkgaW4gZmxvYXQ0IHJhZGlpX3k7CmxheW91dChsb2NhdGlvbiA9IDUpIGluIGZsb2F0NCBza2V3OwpsYXlvdXQobG9jYXRpb24gPSA2KSBpbiBmbG9hdDIgdHJhbnNsYXRlX2FuZF9sb2NhbHJvdGF0ZTsKbGF5b3V0KGxvY2F0aW9uID0gNykgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDApIGZsYXQgb3V0IGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMSkgb3V0IGZsb2F0MiB2YXJjY29vcmRfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIEZpbGxSUmVjdE9wOjpQcm9jZXNzb3IKCXZjb2xvcl9TMCA9IGNvbG9yOwoJZmxvYXQgYWFfYmxvYXRfbXVsdGlwbGllciA9IDA7CglmbG9hdDIgY29ybmVyID0gY29ybmVyX2FuZF9yYWRpdXNfb3V0c2V0cy54eTsKCWZsb2F0MiByYWRpdXNfb3V0c2V0ID0gY29ybmVyX2FuZF9yYWRpdXNfb3V0c2V0cy56dzsKCWZsb2F0MiBhYV9ibG9hdF9kaXJlY3Rpb24gPSBhYV9ibG9hdF9hbmRfY292ZXJhZ2UueHk7CglmbG9hdCBpc19saW5lYXJfY292ZXJhZ2UgPSBhYV9ibG9hdF9hbmRfY292ZXJhZ2UudzsKCWZsb2F0MiBwaXhlbGxlbmd0aCA9IGludmVyc2VzcXJ0KGZsb2F0Mihkb3Qoc2tldy54eiwgc2tldy54eiksIGRvdChza2V3Lnl3LCBza2V3Lnl3KSkpOwoJZmxvYXQ0IG5vcm1hbGl6ZWRfYXhpc19kaXJzID0gc2tldyAqIHBpeGVsbGVuZ3RoLnh5eHk7CglmbG9hdDIgYXhpc3dpZHRocyA9IChhYnMobm9ybWFsaXplZF9heGlzX2RpcnMueHkpICsgYWJzKG5vcm1hbGl6ZWRfYXhpc19kaXJzLnp3KSk7CglmbG9hdDIgYWFfYmxvYXRyYWRpdXMgPSBheGlzd2lkdGhzICogcGl4ZWxsZW5ndGggKiAuNTsKCWZsb2F0NCByYWRpaV9hbmRfbmVpZ2hib3JzID0gcmFkaWlfc2VsZWN0b3IqIGZsb2F0NHg0KHJhZGlpX3gsIHJhZGlpX3ksIHJhZGlpX3gueXh3eiwgcmFkaWlfeS53enl4KTsKCWZsb2F0MiByYWRpaSA9IHJhZGlpX2FuZF9uZWlnaGJvcnMueHk7CglmbG9hdDIgbmVpZ2hib3JfcmFkaWkgPSByYWRpaV9hbmRfbmVpZ2hib3JzLnp3OwoJZmxvYXQgY292ZXJhZ2VfbXVsdGlwbGllciA9IDE7CglpZiAoYW55KGdyZWF0ZXJUaGFuKGFhX2Jsb2F0cmFkaXVzLCBmbG9hdDIoMSkpKSkgCgl7CgkJY29ybmVyID0gbWF4KGFicyhjb3JuZXIpLCBhYV9ibG9hdHJhZGl1cykgKiBzaWduKGNvcm5lcik7CgkJY292ZXJhZ2VfbXVsdGlwbGllciA9IDEgLyAobWF4KGFhX2Jsb2F0cmFkaXVzLngsIDEpICogbWF4KGFhX2Jsb2F0cmFkaXVzLnksIDEpKTsKCQlyYWRpaSA9IGZsb2F0MigwKTsKCX0KCWZsb2F0IGNvdmVyYWdlID0gYWFfYmxvYXRfYW5kX2NvdmVyYWdlLno7CglpZiAoYW55KGxlc3NUaGFuKHJhZGlpLCBhYV9ibG9hdHJhZGl1cyAqIDEuNSkpKSAKCXsKCQlyYWRpaSA9IGZsb2F0MigwKTsKCQlhYV9ibG9hdF9kaXJlY3Rpb24gPSBzaWduKGNvcm5lcik7CgkJaWYgKGNvdmVyYWdlID4gLjUpIAoJCXsKCQkJYWFfYmxvYXRfZGlyZWN0aW9uID0gLWFhX2Jsb2F0X2RpcmVjdGlvbjsKCQl9CgkJaXNfbGluZWFyX2NvdmVyYWdlID0gMTsKCX0KCWVsc2UgCgl7CgkJcmFkaWkgPSBjbGFtcChyYWRpaSwgcGl4ZWxsZW5ndGggKiAxLjUsIDIgLSBwaXhlbGxlbmd0aCAqIDEuNSk7CgkJbmVpZ2hib3JfcmFkaWkgPSBjbGFtcChuZWlnaGJvcl9yYWRpaSwgcGl4ZWxsZW5ndGggKiAxLjUsIDIgLSBwaXhlbGxlbmd0aCAqIDEuNSk7CgkJZmxvYXQyIHNwYWNpbmcgPSAyIC0gcmFkaWkgLSBuZWlnaGJvcl9yYWRpaTsKCQlmbG9hdDIgZXh0cmFfcGFkID0gbWF4KHBpeGVsbGVuZ3RoICogLjA2MjUgLSBzcGFjaW5nLCBmbG9hdDIoMCkpOwoJCXJhZGlpIC09IGV4dHJhX3BhZCAqIC41OwoJfQoJZmxvYXQyIGFhX291dHNldCA9IGFhX2Jsb2F0X2RpcmVjdGlvbiAqIGFhX2Jsb2F0cmFkaXVzICogYWFfYmxvYXRfbXVsdGlwbGllcjsKCWZsb2F0MiB2ZXJ0ZXhwb3MgPSBjb3JuZXIgKyByYWRpdXNfb3V0c2V0ICogcmFkaWkgKyBhYV9vdXRzZXQ7CglpZiAoY292ZXJhZ2UgPiAuNSkgCgl7CgkJaWYgKGFhX2Jsb2F0X2RpcmVjdGlvbi54ICE9IDAgJiYgdmVydGV4cG9zLnggKiBjb3JuZXIueCA8IDApIAoJCXsKCQkJZmxvYXQgYmFja3NldCA9IGFicyh2ZXJ0ZXhwb3MueCk7CgkJCXZlcnRleHBvcy54ID0gMDsKCQkJdmVydGV4cG9zLnkgKz0gYmFja3NldCAqIHNpZ24oY29ybmVyLnkpICogcGl4ZWxsZW5ndGgueS9waXhlbGxlbmd0aC54OwoJCQljb3ZlcmFnZSA9IChjb3ZlcmFnZSAtIC41KSAqIGFicyhjb3JuZXIueCkgLyAoYWJzKGNvcm5lci54KSArIGJhY2tzZXQpICsgLjU7CgkJfQoJCWlmIChhYV9ibG9hdF9kaXJlY3Rpb24ueSAhPSAwICYmIHZlcnRleHBvcy55ICogY29ybmVyLnkgPCAwKSAKCQl7CgkJCWZsb2F0IGJhY2tzZXQgPSBhYnModmVydGV4cG9zLnkpOwoJCQl2ZXJ0ZXhwb3MueSA9IDA7CgkJCXZlcnRleHBvcy54ICs9IGJhY2tzZXQgKiBzaWduKGNvcm5lci54KSAqIHBpeGVsbGVuZ3RoLngvcGl4ZWxsZW5ndGgueTsKCQkJY292ZXJhZ2UgPSAoY292ZXJhZ2UgLSAuNSkgKiBhYnMoY29ybmVyLnkpIC8gKGFicyhjb3JuZXIueSkgKyBiYWNrc2V0KSArIC41OwoJCX0KCX0KCWZsb2F0MngyIHNrZXdtYXRyaXggPSBmbG9hdDJ4Mihza2V3Lnh5LCBza2V3Lnp3KTsKCWZsb2F0MiBkZXZjb29yZCA9IHZlcnRleHBvcyAqIHNrZXdtYXRyaXggKyB0cmFuc2xhdGVfYW5kX2xvY2Fscm90YXRlLnh5OwoJaWYgKDAgIT0gaXNfbGluZWFyX2NvdmVyYWdlKSAKCXsKCQl2YXJjY29vcmRfUzAueHkgPSBmbG9hdDIoMCwgY292ZXJhZ2UgKiBjb3ZlcmFnZV9tdWx0aXBsaWVyKTsKCX0KCWVsc2UgCgl7CgkJZmxvYXQyIGFyY2Nvb3JkID0gMSAtIGFicyhyYWRpdXNfb3V0c2V0KSArIGFhX291dHNldC9yYWRpaSAqIGNvcm5lcjsKCQl2YXJjY29vcmRfUzAueHkgPSBmbG9hdDIoYXJjY29vcmQueCsxLCBhcmNjb29yZC55KTsKCX0KCXNrX1Bvc2l0aW9uID0gZGV2Y29vcmQueHkwMTsKfQoAAAAAAADxAgAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gZmxvYXQyIHZhcmNjb29yZF9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIEZpbGxSUmVjdE9wOjpQcm9jZXNzb3IKCWhhbGY0IG91dHB1dENvbG9yX1MwOwoJb3V0cHV0Q29sb3JfUzAgPSB2Y29sb3JfUzA7CglmbG9hdCB4X3BsdXNfMT12YXJjY29vcmRfUzAueCwgeT12YXJjY29vcmRfUzAueTsKCWhhbGYgY292ZXJhZ2U7CglpZiAoMCA9PSB4X3BsdXNfMSkgCgl7CgkJY292ZXJhZ2UgPSBoYWxmKHkpOwoJfQoJZWxzZSAKCXsKCQlmbG9hdCBmbiA9IHhfcGx1c18xICogKHhfcGx1c18xIC0gMik7CgkJZm4gPSBmbWEoeSx5LCBmbik7CgkJZmxvYXQgZm53aWR0aCA9IGZ3aWR0aChmbik7CgkJY292ZXJhZ2UgPSAuNSAtIGhhbGYoZm4vZm53aWR0aCk7CgkJY292ZXJhZ2UgPSBjbGFtcChjb3ZlcmFnZSwgMCwgMSk7Cgl9Cgljb3ZlcmFnZSA9IChjb3ZlcmFnZSA+PSAuNSkgPyAxIDogMDsKCWhhbGY0IG91dHB1dENvdmVyYWdlX1MwID0gaGFsZjQoY292ZXJhZ2UpOwoJewoJCS8vIFhmZXIgUHJvY2Vzc29yOiBQb3J0ZXIgRHVmZgoJCXNrX0ZyYWdDb2xvciA9IG91dHB1dENvbG9yX1MwICogb3V0cHV0Q292ZXJhZ2VfUzA7Cgl9Cn0KAAAAAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAoAAAAAEAAAACAAAAAwAAAB8AAAAAAAAAAQAAAB8AAAAQAAAAAQAAAB8AAAAgAAAAAQAAADAAAAAFAAAAHwAAAAAAAAACAAAAHwAAABAAAAACAAAAHwAAACAAAAACAAAAHQAAADAAAAACAAAACQAAADgAAAACAAAAPAAAAFAAAAABAAAAAQAAAAUAAAAAAAAAAQAAAAUAAAAAAAAAAQAAAP0AAAA=", "HUJAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAQAAAAAAQQGABZAA6IAAAAACAAAAAADUAAAAAAAEAAAAAIDEAAAAAAEMAAAAAAQAAAA7UAAAAAAAAAAAAAAAAAMGAICAAAAAAAA": "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", "HUQACAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAAKAAYAAAACAAAAAAACCAYAABIAAAAAAEAAAAAAAAAAAAAAAAACAAAAADBQCAAAAAAAAAA": "DAAAAExTS1NOAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGhhbGY0IGNvbG9yOwpsYXlvdXQobG9jYXRpb24gPSAwKSBvdXQgaGFsZjQgdmNvbG9yX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gUHJpbWl0aXZlIFByb2Nlc3NvciBRdWFkUGVyRWRnZUFBR2VvbWV0cnlQcm9jZXNzb3IKCXZjb2xvcl9TMCA9IGNvbG9yOwoJc2tfUG9zaXRpb24gPSBwb3NpdGlvbi54eTAxOwp9CgAAAAAAAH0BAABsYXlvdXQgKG1ldGFsLCBiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGhhbGY0IHZjb2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzA7CglvdXRwdXRDb2xvcl9TMCA9IHZjb2xvcl9TMDsKCWNvbnN0IGhhbGY0IG91dHB1dENvdmVyYWdlX1MwID0gaGFsZjQoMSk7Cgl7CgkJLy8gWGZlciBQcm9jZXNzb3I6IFBvcnRlciBEdWZmCgkJc2tfRnJhZ0NvbG9yID0gb3V0cHV0Q29sb3JfUzAgKiBvdXRwdXRDb3ZlcmFnZV9TMDsKCX0KfQoAAAAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABUAAAAAQAAAAAAAAACAAAAHQAAAAAAAAABAAAACQAAAAgAAAABAAAADAAAAAAAAABQAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAAAAAAA", "HUQAAAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAAKAAYAAAACAAAAAAACCAYAABIAAAAAAEAAAAH5AAAAAAIAAAABAAAAADBQCAQAAAAAAAA": "DAAAAExTS1NTAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGhhbGY0IGNvbG9yOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IG91dCBoYWxmNCB2Y29sb3JfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmNvbG9yX1MwID0gY29sb3I7Cglza19Qb3NpdGlvbiA9IHBvc2l0aW9uLnh5MDE7Cn0KAAAAAACCAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzA7CglvdXRwdXRDb2xvcl9TMCA9IHZjb2xvcl9TMDsKCWNvbnN0IGhhbGY0IG91dHB1dENvdmVyYWdlX1MwID0gaGFsZjQoMSk7Cgl7CgkJLy8gWGZlciBQcm9jZXNzb3I6IFBvcnRlciBEdWZmCgkJc2tfRnJhZ0NvbG9yID0gb3V0cHV0Q29sb3JfUzAgKiBvdXRwdXRDb3ZlcmFnZV9TMDsKCX0KfQoAAAAAAAABAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFQAAAABAAAAAAAAAAIAAAAdAAAAAAAAAAEAAAAJAAAACAAAAAEAAAAMAAAAAAAAAFAAAAABAAAAAQAAAAUAAAAAAAAAAQAAAAUAAAAAAAAAAQAAAP0AAAA=", "HUJAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAQAAAAAAQQGACQAGAAAAAQAAAAAAAQQGAAAKAAAAAABAAAAB7IAAAAACAAAAAYAAAAAYMAQEAAAAAAAA": "DAAAAExTS1NkAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGZsb2F0MiBsb2NhbENvb3JkOwpsYXlvdXQobG9jYXRpb24gPSAwKSBvdXQgZmxvYXQyIHZsb2NhbENvb3JkX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gUHJpbWl0aXZlIFByb2Nlc3NvciBRdWFkUGVyRWRnZUFBR2VvbWV0cnlQcm9jZXNzb3IKCXZsb2NhbENvb3JkX1MwID0gbG9jYWxDb29yZDsKCXNrX1Bvc2l0aW9uID0gcG9zaXRpb24ueHkwMTsKfQoAAAAALgIAAGxheW91dChtZXRhbCwgYmluZGluZz0wKSBzYW1wbGVyMkQgdVRleHR1cmVTYW1wbGVyXzBfUzA7CmxheW91dCAobWV0YWwsIGJpbmRpbmc9MCkgdW5pZm9ybSB1bmlmb3JtQnVmZmVyCnsKCWxheW91dChvZmZzZXQ9MCkgZmxvYXQ0IHNrX1JUQWRqdXN0Owp9CjsKbGF5b3V0KGxvY2F0aW9uID0gMCkgaW4gZmxvYXQyIHZsb2NhbENvb3JkX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMCA9IGhhbGY0KDEpOwoJZmxvYXQyIHRleENvb3JkOwoJdGV4Q29vcmQgPSB2bG9jYWxDb29yZF9TMDsKCW91dHB1dENvbG9yX1MwID0gKGJsZW5kX21vZHVsYXRlKHNhbXBsZSh1VGV4dHVyZVNhbXBsZXJfMF9TMCwgdGV4Q29vcmQpLCBoYWxmNCgxKSkpOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAVAAAAAEAAAAAAAAAAgAAAB0AAAAAAAAAAQAAAB0AAAAIAAAAAQAAABAAAAAAAAAAUAAAAAEAAAABAAAABQAAAAAAAAABAAAABQAAAAAAAAABAAAA/QAAAA==", "AYQA5AADQAAAOAEARAFQJAABBADIB7777777777777777777777767YAAAAAAAAAAAAOQAAAAAAAQAAAABAMQAAAABDAAAAAAEAAAAAAAAAAAAAAAAAAAAAAADBQCAAAAAAAAAA": "DAAAAExTS1OAAgAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7CglsYXlvdXQob2Zmc2V0PTE2KSBmbG9hdDQgdWxvY2FsTWF0cml4X1MwOwp9CjsKbGF5b3V0KGxvY2F0aW9uID0gMCkgaW4gZmxvYXQyIGluUG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGhhbGY0IGluQ29sb3I7CmxheW91dChsb2NhdGlvbiA9IDIpIGluIGZsb2F0NCBpbkNpcmNsZUVkZ2U7CmxheW91dChsb2NhdGlvbiA9IDApIG91dCBmbG9hdDQgdmluQ2lyY2xlRWRnZV9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMSkgb3V0IGhhbGY0IHZpbkNvbG9yX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gUHJpbWl0aXZlIFByb2Nlc3NvciBDaXJjbGVHZW9tZXRyeVByb2Nlc3NvcgoJdmluQ2lyY2xlRWRnZV9TMCA9IGluQ2lyY2xlRWRnZTsKCXZpbkNvbG9yX1MwID0gaW5Db2xvcjsKCWZsb2F0MiBfdG1wXzBfaW5Qb3NpdGlvbiA9IGluUG9zaXRpb247CglmbG9hdDIgX3RtcF8xX2luUG9zaXRpb24gPSB1bG9jYWxNYXRyaXhfUzAueHogKiBpblBvc2l0aW9uICsgdWxvY2FsTWF0cml4X1MwLnl3OwoJc2tfUG9zaXRpb24gPSBfdG1wXzBfaW5Qb3NpdGlvbi54eTAxOwp9CgAAAACbAgAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7CglsYXlvdXQob2Zmc2V0PTE2KSBmbG9hdDQgdWxvY2FsTWF0cml4X1MwOwp9CjsKbGF5b3V0KGxvY2F0aW9uID0gMCkgaW4gZmxvYXQ0IHZpbkNpcmNsZUVkZ2VfUzA7CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGhhbGY0IHZpbkNvbG9yX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgQ2lyY2xlR2VvbWV0cnlQcm9jZXNzb3IKCWZsb2F0NCBjaXJjbGVFZGdlOwoJY2lyY2xlRWRnZSA9IHZpbkNpcmNsZUVkZ2VfUzA7CgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmluQ29sb3JfUzA7CglmbG9hdCBkID0gbGVuZ3RoKGNpcmNsZUVkZ2UueHkpOwoJaGFsZiBkaXN0YW5jZVRvT3V0ZXJFZGdlID0gaGFsZihjaXJjbGVFZGdlLnogKiAoMS4wIC0gZCkpOwoJaGFsZiBlZGdlQWxwaGEgPSBzYXR1cmF0ZShkaXN0YW5jZVRvT3V0ZXJFZGdlKTsKCWhhbGY0IG91dHB1dENvdmVyYWdlX1MwID0gaGFsZjQoZWRnZUFscGhhKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABgAAAAAQAAAAAAAAADAAAAHQAAAAAAAAABAAAACQAAAAgAAAABAAAAHwAAAAwAAAABAAAAHAAAAAAAAABGAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAAAAAAA", "AYQQ5AADQAAAOAEARAFQJAABBADIB7777777777777777777777767YAAAAAAAAAACAMUAEQYX3QQAIAACAO2VADHYAAAAEALJ3XKFISAAAAABQAAAAOYOM6G2BQAAAA4AIA2XAGAAAAAAAAEAAAAABAEPN2SBT4AAAAAAFV53VCUJAAAAAAYAAAADMHGPDNAYAQAAGAEENLQDAAAAACAAAAABAEM2W52VKUQAAAAAMAAAAAWDTXRWQMAIAABACDGRYBSAAAACAAAAAAQAGO4OM6G2BQAAAAAAAAAAFQEIHNDQDFAAAAAAABAAAAAMXECAGVYBQAAAAAAAAAACYIEAAAAEAAAABUARCAIAAAAAAAAAAAUABQAAAAEAAAAAAAEEBQAACQAAAAAAIAAAAP2AAAAAAAAAAAAAAAAAGDAEAAAAAAAAAA": "DAAAAExTS1NVBAAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7CglsYXlvdXQob2Zmc2V0PTE2KSBmbG9hdDQgdWxvY2FsTWF0cml4X1MwOwoJbGF5b3V0KG9mZnNldD0zMikgaGFsZjQgdXN0YXJ0X1MxX2MwX2MwX2MwOwoJbGF5b3V0KG9mZnNldD00MCkgaGFsZjQgdWVuZF9TMV9jMF9jMF9jMDsKCWxheW91dChvZmZzZXQ9NDgpIGZsb2F0M3gzIHVtYXRyaXhfUzFfYzBfYzBfYzE7CglsYXlvdXQob2Zmc2V0PTk2KSBoYWxmNCB1bGVmdEJvcmRlckNvbG9yX1MxX2MwX2MwOwoJbGF5b3V0KG9mZnNldD0xMDQpIGhhbGY0IHVyaWdodEJvcmRlckNvbG9yX1MxX2MwX2MwOwoJbGF5b3V0KG9mZnNldD0xMTIpIGZsb2F0M3gzIHVtYXRyaXhfUzFfYzE7CglsYXlvdXQob2Zmc2V0PTE2MCkgaGFsZiB1cmFuZ2VfUzE7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgaW5Qb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgaW5Db2xvcjsKbGF5b3V0KGxvY2F0aW9uID0gMikgaW4gZmxvYXQ0IGluQ2lyY2xlRWRnZTsKbGF5b3V0KGxvY2F0aW9uID0gMCkgb3V0IGZsb2F0NCB2aW5DaXJjbGVFZGdlX1MwOwpsYXlvdXQobG9jYXRpb24gPSAxKSBvdXQgaGFsZjQgdmluQ29sb3JfUzA7CmxheW91dChsb2NhdGlvbiA9IDIpIG91dCBmbG9hdDIgdlRyYW5zZm9ybWVkQ29vcmRzXzZfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIENpcmNsZUdlb21ldHJ5UHJvY2Vzc29yCgl2aW5DaXJjbGVFZGdlX1MwID0gaW5DaXJjbGVFZGdlOwoJdmluQ29sb3JfUzAgPSBpbkNvbG9yOwoJZmxvYXQyIF90bXBfMF9pblBvc2l0aW9uID0gaW5Qb3NpdGlvbjsKCWZsb2F0MiBfdG1wXzFfaW5Qb3NpdGlvbiA9IHVsb2NhbE1hdHJpeF9TMC54eiAqIGluUG9zaXRpb24gKyB1bG9jYWxNYXRyaXhfUzAueXc7Cglza19Qb3NpdGlvbiA9IF90bXBfMF9pblBvc2l0aW9uLnh5MDE7Cgl7CgkJdlRyYW5zZm9ybWVkQ29vcmRzXzZfUzAgPSBmbG9hdDN4Mih1bWF0cml4X1MxX2MwX2MwX2MxKSAqIF90bXBfMV9pblBvc2l0aW9uLnh5MTsKCX0KfQoAAAAAAAAAtgsAAGxheW91dChtZXRhbCwgYmluZGluZz0wKSBzYW1wbGVyMkQgdVRleHR1cmVTYW1wbGVyXzBfUzE7CmxheW91dCAobWV0YWwsIGJpbmRpbmc9MCkgdW5pZm9ybSB1bmlmb3JtQnVmZmVyCnsKCWxheW91dChvZmZzZXQ9MCkgZmxvYXQ0IHNrX1JUQWRqdXN0OwoJbGF5b3V0KG9mZnNldD0xNikgZmxvYXQ0IHVsb2NhbE1hdHJpeF9TMDsKCWxheW91dChvZmZzZXQ9MzIpIGhhbGY0IHVzdGFydF9TMV9jMF9jMF9jMDsKCWxheW91dChvZmZzZXQ9NDApIGhhbGY0IHVlbmRfUzFfYzBfYzBfYzA7CglsYXlvdXQob2Zmc2V0PTQ4KSBmbG9hdDN4MyB1bWF0cml4X1MxX2MwX2MwX2MxOwoJbGF5b3V0KG9mZnNldD05NikgaGFsZjQgdWxlZnRCb3JkZXJDb2xvcl9TMV9jMF9jMDsKCWxheW91dChvZmZzZXQ9MTA0KSBoYWxmNCB1cmlnaHRCb3JkZXJDb2xvcl9TMV9jMF9jMDsKCWxheW91dChvZmZzZXQ9MTEyKSBmbG9hdDN4MyB1bWF0cml4X1MxX2MxOwoJbGF5b3V0KG9mZnNldD0xNjApIGhhbGYgdXJhbmdlX1MxOwp9CjsKbGF5b3V0KGxvY2F0aW9uID0gMCkgaW4gZmxvYXQ0IHZpbkNpcmNsZUVkZ2VfUzA7CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGhhbGY0IHZpbkNvbG9yX1MwOwpsYXlvdXQobG9jYXRpb24gPSAyKSBpbiBmbG9hdDIgdlRyYW5zZm9ybWVkQ29vcmRzXzZfUzA7CmhhbGY0IFNpbmdsZUludGVydmFsQ29sb3JpemVyX1MxX2MwX2MwX2MwKGhhbGY0IF9pbnB1dCwgZmxvYXQyIF9jb29yZHMpIAp7CgloYWxmNCBfdG1wXzBfaW5Db2xvciA9IF9pbnB1dDsKCWZsb2F0MiBfdG1wXzFfY29vcmRzID0gX2Nvb3JkczsKCXJldHVybiBoYWxmNChtaXgodXN0YXJ0X1MxX2MwX2MwX2MwLCB1ZW5kX1MxX2MwX2MwX2MwLCBoYWxmKF90bXBfMV9jb29yZHMueCkpKTsKfQpoYWxmNCBMaW5lYXJMYXlvdXRfUzFfYzBfYzBfYzFfYzAoaGFsZjQgX2lucHV0KSAKewoJaGFsZjQgX3RtcF8yX2luQ29sb3IgPSBfaW5wdXQ7CglmbG9hdDIgX3RtcF8zX2Nvb3JkcyA9IHZUcmFuc2Zvcm1lZENvb3Jkc182X1MwOwoJcmV0dXJuIGhhbGY0KGhhbGY0KGhhbGYoX3RtcF8zX2Nvb3Jkcy54KSArIDFlLTA1LCAxLjAsIDAuMCwgMC4wKSk7Cn0KaGFsZjQgTWF0cml4RWZmZWN0X1MxX2MwX2MwX2MxKGhhbGY0IF9pbnB1dCkgCnsKCXJldHVybiBMaW5lYXJMYXlvdXRfUzFfYzBfYzBfYzFfYzAoX2lucHV0KTsKfQpoYWxmNCBDbGFtcGVkR3JhZGllbnRfUzFfYzBfYzAoaGFsZjQgX2lucHV0KSAKewoJaGFsZjQgX3RtcF80X2luQ29sb3IgPSBfaW5wdXQ7CgloYWxmNCB0ID0gTWF0cml4RWZmZWN0X1MxX2MwX2MwX2MxKF90bXBfNF9pbkNvbG9yKTsKCWhhbGY0IG91dENvbG9yOwoJaWYgKCFib29sKGludCgxKSkgJiYgdC55IDwgMC4wKSAKCXsKCQlvdXRDb2xvciA9IGhhbGY0KDAuMCk7Cgl9CgllbHNlIGlmICh0LnggPCAwLjApIAoJewoJCW91dENvbG9yID0gdWxlZnRCb3JkZXJDb2xvcl9TMV9jMF9jMDsKCX0KCWVsc2UgaWYgKHQueCA+IDEuMCkgCgl7CgkJb3V0Q29sb3IgPSB1cmlnaHRCb3JkZXJDb2xvcl9TMV9jMF9jMDsKCX0KCWVsc2UgCgl7CgkJb3V0Q29sb3IgPSBTaW5nbGVJbnRlcnZhbENvbG9yaXplcl9TMV9jMF9jMF9jMChfdG1wXzRfaW5Db2xvciwgZmxvYXQyKGhhbGYyKHQueCwgMC4wKSkpOwoJfQoJcmV0dXJuIGhhbGY0KG91dENvbG9yKTsKfQpoYWxmNCBEaXNhYmxlQ292ZXJhZ2VBc0FscGhhX1MxX2MwKGhhbGY0IF9pbnB1dCkgCnsKCV9pbnB1dCA9IENsYW1wZWRHcmFkaWVudF9TMV9jMF9jMChfaW5wdXQpOwoJaGFsZjQgX3RtcF81X2luQ29sb3IgPSBfaW5wdXQ7CglyZXR1cm4gaGFsZjQoX2lucHV0KTsKfQpoYWxmNCBUZXh0dXJlRWZmZWN0X1MxX2MxX2MwKGhhbGY0IF9pbnB1dCwgZmxvYXQyIF9jb29yZHMpIAp7CglyZXR1cm4gc2FtcGxlKHVUZXh0dXJlU2FtcGxlcl8wX1MxLCBfY29vcmRzKS4wMDByOwp9CmhhbGY0IE1hdHJpeEVmZmVjdF9TMV9jMShoYWxmNCBfaW5wdXQsIGZsb2F0MiBfY29vcmRzKSAKewoJcmV0dXJuIFRleHR1cmVFZmZlY3RfUzFfYzFfYzAoX2lucHV0LCBmbG9hdDN4Mih1bWF0cml4X1MxX2MxKSAqIF9jb29yZHMueHkxKTsKfQpoYWxmNCBEaXRoZXJfUzEoaGFsZjQgX2lucHV0KSAKewoJaGFsZjQgX3RtcF82X2luQ29sb3IgPSBfaW5wdXQ7CgloYWxmNCBjb2xvciA9IERpc2FibGVDb3ZlcmFnZUFzQWxwaGFfUzFfYzAoX3RtcF82X2luQ29sb3IpOwoJaGFsZiB2YWx1ZSA9IE1hdHJpeEVmZmVjdF9TMV9jMShfdG1wXzZfaW5Db2xvciwgc2tfRnJhZ0Nvb3JkLnh5KS53IC0gMC41OwoJcmV0dXJuIGhhbGY0KGhhbGY0KGNsYW1wKGNvbG9yLnh5eiArIHZhbHVlICogdXJhbmdlX1MxLCAwLjAsIGNvbG9yLncpLCBjb2xvci53KSk7Cn0Kdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIENpcmNsZUdlb21ldHJ5UHJvY2Vzc29yCglmbG9hdDQgY2lyY2xlRWRnZTsKCWNpcmNsZUVkZ2UgPSB2aW5DaXJjbGVFZGdlX1MwOwoJaGFsZjQgb3V0cHV0Q29sb3JfUzA7CglvdXRwdXRDb2xvcl9TMCA9IHZpbkNvbG9yX1MwOwoJZmxvYXQgZCA9IGxlbmd0aChjaXJjbGVFZGdlLnh5KTsKCWhhbGYgZGlzdGFuY2VUb091dGVyRWRnZSA9IGhhbGYoY2lyY2xlRWRnZS56ICogKDEuMCAtIGQpKTsKCWhhbGYgZWRnZUFscGhhID0gc2F0dXJhdGUoZGlzdGFuY2VUb091dGVyRWRnZSk7CgloYWxmIGRpc3RhbmNlVG9Jbm5lckVkZ2UgPSBoYWxmKGNpcmNsZUVkZ2UueiAqIChkIC0gY2lyY2xlRWRnZS53KSk7CgloYWxmIGlubmVyQWxwaGEgPSBzYXR1cmF0ZShkaXN0YW5jZVRvSW5uZXJFZGdlKTsKCWVkZ2VBbHBoYSAqPSBpbm5lckFscGhhOwoJaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNChlZGdlQWxwaGEpOwoJaGFsZjQgb3V0cHV0X1MxOwoJb3V0cHV0X1MxID0gRGl0aGVyX1MxKG91dHB1dENvbG9yX1MwKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRfUzEgKiBvdXRwdXRDb3ZlcmFnZV9TMDsKCX0KfQoAAAEAAAABAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGAAAAABAAAAAAAAAAMAAAAdAAAAAAAAAAEAAAAJAAAACAAAAAEAAAAfAAAADAAAAAEAAAAcAAAAAAAAAFAAAAABAAAAAQAAAAUAAAAAAAAAAQAAAAUAAAAAAAAAAQAAAP0AAAA=", "HVJAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAABAAAAAABBAMABAAOAAAABAAAAAAABBAMAAAKAAAAAABAAAAB7IAAAAACAAAAAIAAAAAYMAQEAAAAAAAA": "DAAAAExTS1PMAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGhhbGY0IGNvbG9yOwpsYXlvdXQobG9jYXRpb24gPSAyKSBpbiBmbG9hdDIgbG9jYWxDb29yZDsKbGF5b3V0KGxvY2F0aW9uID0gMCkgZmxhdCBvdXQgaGFsZjQgdmNvbG9yX1MwOwpsYXlvdXQobG9jYXRpb24gPSAxKSBvdXQgZmxvYXQyIHZsb2NhbENvb3JkX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gUHJpbWl0aXZlIFByb2Nlc3NvciBRdWFkUGVyRWRnZUFBR2VvbWV0cnlQcm9jZXNzb3IKCXZjb2xvcl9TMCA9IGNvbG9yOwoJdmxvY2FsQ29vcmRfUzAgPSBsb2NhbENvb3JkOwoJc2tfUG9zaXRpb24gPSBwb3NpdGlvbi54eTAxOwp9CgAAAAB0AgAAbGF5b3V0KG1ldGFsLCBiaW5kaW5nPTApIHNhbXBsZXIyRCB1VGV4dHVyZVNhbXBsZXJfMF9TMDsKbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gZmxvYXQyIHZsb2NhbENvb3JkX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmNvbG9yX1MwOwoJZmxvYXQyIHRleENvb3JkOwoJdGV4Q29vcmQgPSB2bG9jYWxDb29yZF9TMDsKCW91dHB1dENvbG9yX1MwID0gKGJsZW5kX21vZHVsYXRlKHNhbXBsZSh1VGV4dHVyZVNhbXBsZXJfMF9TMCwgdGV4Q29vcmQpLCBvdXRwdXRDb2xvcl9TMCkpOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAABAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGAAAAABAAAAAAAAAAMAAAAdAAAAAAAAAAEAAAAJAAAACAAAAAEAAAAdAAAADAAAAAEAAAAUAAAAAAAAAFAAAAABAAAAAQAAAAUAAAAAAAAAAQAAAAUAAAAAAAAAAQAAAP0AAAA=", "HUIAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAAAAAALUAQBAAAQAAAAGQCBAMQACAAAAAAAACQAGAAAAAQAAAAAAAQQGAAAABIAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAABQAAQAAAAAAAA": "DAAAAExTS1PoAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7CglsYXlvdXQob2Zmc2V0PTE2KSBmbG9hdDQgdWNsYW1wX1MxX2MwOwoJbGF5b3V0KG9mZnNldD0zMikgZmxvYXQzeDMgdW1hdHJpeF9TMTsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gZmxvYXQyIGxvY2FsQ29vcmQ7CmxheW91dChsb2NhdGlvbiA9IDApIG91dCBmbG9hdDIgdlRyYW5zZm9ybWVkQ29vcmRzXzJfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJc2tfUG9zaXRpb24gPSBwb3NpdGlvbi54eTAxOwoJewoJCXZUcmFuc2Zvcm1lZENvb3Jkc18yX1MwID0gZmxvYXQzeDIodW1hdHJpeF9TMSkgKiBsb2NhbENvb3JkLnh5MTsKCX0KfQoAAAAACAQAAGxheW91dChtZXRhbCwgYmluZGluZz0wKSBzYW1wbGVyMkQgdVRleHR1cmVTYW1wbGVyXzBfUzE7CmxheW91dCAobWV0YWwsIGJpbmRpbmc9MCkgdW5pZm9ybSB1bmlmb3JtQnVmZmVyCnsKCWxheW91dChvZmZzZXQ9MCkgZmxvYXQ0IHNrX1JUQWRqdXN0OwoJbGF5b3V0KG9mZnNldD0xNikgZmxvYXQ0IHVjbGFtcF9TMV9jMDsKCWxheW91dChvZmZzZXQ9MzIpIGZsb2F0M3gzIHVtYXRyaXhfUzE7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgdlRyYW5zZm9ybWVkQ29vcmRzXzJfUzA7CmhhbGY0IFRleHR1cmVFZmZlY3RfUzFfYzAoaGFsZjQgX2lucHV0KSAKewoJZmxvYXQyIGluQ29vcmQgPSB2VHJhbnNmb3JtZWRDb29yZHNfMl9TMDsKCWZsb2F0MiBzdWJzZXRDb29yZDsKCXN1YnNldENvb3JkLnggPSBpbkNvb3JkLng7CglzdWJzZXRDb29yZC55ID0gaW5Db29yZC55OwoJZmxvYXQyIGNsYW1wZWRDb29yZDsKCWNsYW1wZWRDb29yZC54ID0gY2xhbXAoc3Vic2V0Q29vcmQueCwgdWNsYW1wX1MxX2MwLngsIHVjbGFtcF9TMV9jMC56KTsKCWNsYW1wZWRDb29yZC55ID0gc3Vic2V0Q29vcmQueTsKCWhhbGY0IHRleHR1cmVDb2xvciA9IHNhbXBsZSh1VGV4dHVyZVNhbXBsZXJfMF9TMSwgY2xhbXBlZENvb3JkKTsKCXJldHVybiB0ZXh0dXJlQ29sb3I7Cn0KaGFsZjQgTWF0cml4RWZmZWN0X1MxKGhhbGY0IF9pbnB1dCkgCnsKCXJldHVybiBUZXh0dXJlRWZmZWN0X1MxX2MwKF9pbnB1dCk7Cn0Kdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzAgPSBoYWxmNCgxKTsKCWNvbnN0IGhhbGY0IG91dHB1dENvdmVyYWdlX1MwID0gaGFsZjQoMSk7CgloYWxmNCBvdXRwdXRfUzE7CglvdXRwdXRfUzEgPSBNYXRyaXhFZmZlY3RfUzEob3V0cHV0Q29sb3JfUzApOwoJewoJCS8vIFhmZXIgUHJvY2Vzc29yOiBQb3J0ZXIgRHVmZgoJCXNrX0ZyYWdDb2xvciA9IG91dHB1dF9TMSAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAABAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwAAAABAAAAAAAAAAIAAAAdAAAAAAAAAAEAAAAdAAAACAAAAAEAAAAQAAAAAAAAAFAAAAAAAAAAAQAAAAAAAAA=", "AYQA5AADQAAAOAEARAFQJAABBADIB7777777777777777777777767YAAAAAAAAAAAAOQAAAAAAAQAAAABAMQAAAABIAAAAAAEAAAAH5AAAAAAAAAAACAAAAADBQCAAAAAAAAAA": "DAAAAExTS1OAAgAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7CglsYXlvdXQob2Zmc2V0PTE2KSBmbG9hdDQgdWxvY2FsTWF0cml4X1MwOwp9CjsKbGF5b3V0KGxvY2F0aW9uID0gMCkgaW4gZmxvYXQyIGluUG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGhhbGY0IGluQ29sb3I7CmxheW91dChsb2NhdGlvbiA9IDIpIGluIGZsb2F0NCBpbkNpcmNsZUVkZ2U7CmxheW91dChsb2NhdGlvbiA9IDApIG91dCBmbG9hdDQgdmluQ2lyY2xlRWRnZV9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMSkgb3V0IGhhbGY0IHZpbkNvbG9yX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gUHJpbWl0aXZlIFByb2Nlc3NvciBDaXJjbGVHZW9tZXRyeVByb2Nlc3NvcgoJdmluQ2lyY2xlRWRnZV9TMCA9IGluQ2lyY2xlRWRnZTsKCXZpbkNvbG9yX1MwID0gaW5Db2xvcjsKCWZsb2F0MiBfdG1wXzBfaW5Qb3NpdGlvbiA9IGluUG9zaXRpb247CglmbG9hdDIgX3RtcF8xX2luUG9zaXRpb24gPSB1bG9jYWxNYXRyaXhfUzAueHogKiBpblBvc2l0aW9uICsgdWxvY2FsTWF0cml4X1MwLnl3OwoJc2tfUG9zaXRpb24gPSBfdG1wXzBfaW5Qb3NpdGlvbi54eTAxOwp9CgAAAACbAgAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7CglsYXlvdXQob2Zmc2V0PTE2KSBmbG9hdDQgdWxvY2FsTWF0cml4X1MwOwp9CjsKbGF5b3V0KGxvY2F0aW9uID0gMCkgaW4gZmxvYXQ0IHZpbkNpcmNsZUVkZ2VfUzA7CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGhhbGY0IHZpbkNvbG9yX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgQ2lyY2xlR2VvbWV0cnlQcm9jZXNzb3IKCWZsb2F0NCBjaXJjbGVFZGdlOwoJY2lyY2xlRWRnZSA9IHZpbkNpcmNsZUVkZ2VfUzA7CgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmluQ29sb3JfUzA7CglmbG9hdCBkID0gbGVuZ3RoKGNpcmNsZUVkZ2UueHkpOwoJaGFsZiBkaXN0YW5jZVRvT3V0ZXJFZGdlID0gaGFsZihjaXJjbGVFZGdlLnogKiAoMS4wIC0gZCkpOwoJaGFsZiBlZGdlQWxwaGEgPSBzYXR1cmF0ZShkaXN0YW5jZVRvT3V0ZXJFZGdlKTsKCWhhbGY0IG91dHB1dENvdmVyYWdlX1MwID0gaGFsZjQoZWRnZUFscGhhKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABgAAAAAQAAAAAAAAADAAAAHQAAAAAAAAABAAAACQAAAAgAAAABAAAAHwAAAAwAAAABAAAAHAAAAAAAAABQAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAD9AAAA", "HUIAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAAAAAALUASJ3EZYN2AAAAACAAAEAAAABSCQKL3IYIJ2AAAAACAAAEAAAABLRAABAAAAABAEGABBAMAAQAAAAAAAAOQAAAAAAAQAAAABAMQAAAAAAUAAAAAACAAAAAAAAAAAAAAAAAAAAAAAAAYAAIAAAAAAAA": "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", "AYQA5AADQAAAOAEARAFQJAABBADIB7777777777777777777777767YAAAAAAAAAAAAMWAEQYX3QQAIAACAO2VADHYAAAAEALJ3XKFISAAAAABQAAAAEAAAAAA2JTW5YCMAQAAGAEENLQDAAAAAAAACAAAAAAQCGWZJQ36AAAAAAA2W52VKUQAAAAAMAAAAAAAAQAAGQMRXOGTQEAAAABB3I4AZAAAAAQAAAAAAADGUXKV2XEEAQAADAAAAAAAAEAAAEBE5ZRU5RCAAAAAOKFAOLAAAAAAAEAAAAAMQQAIAABIGJ3TDJ2CAAAAAACAAAAAZNEZDO4NHAIAAAAAAAAAEACVYYQBROAMAAAAAIAAAABEBBQ5UOAMQAAAAAAAAAACABKBAABAAAAAFAEEQCEAAAAAAAAAAAQAB5QAYAAAEAAAAAADIACAAAAAIAAAAAQCIACACQAAAAAAIAAAAP2AAAAAAAAAAAAAAAAAGDAEAAAAAAAAAA": "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", "HUJAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAQAAAAAAQQGAAZAADIAAAAGULKMMQKAAAAAAMAAAAAIAAAAAAGIRBNAWEYZAUAABQAAAAAAAAAAAAADUAAAAAAAEAAAAAIDEAAAKAAAAAABAAAAB7IAAAAAAAAAAAAAAAAAYMAQEAAAAAAAA": "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", "HVJAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAABAAAAAABBAMABAAOAAAABAAAAAAABBAMAAAIYAAAAABAAAAAAAAAAAAAAAAAAQAAAAAYMAQEAAAAAAAA": "DAAAAExTS1PMAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGhhbGY0IGNvbG9yOwpsYXlvdXQobG9jYXRpb24gPSAyKSBpbiBmbG9hdDIgbG9jYWxDb29yZDsKbGF5b3V0KGxvY2F0aW9uID0gMCkgZmxhdCBvdXQgaGFsZjQgdmNvbG9yX1MwOwpsYXlvdXQobG9jYXRpb24gPSAxKSBvdXQgZmxvYXQyIHZsb2NhbENvb3JkX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gUHJpbWl0aXZlIFByb2Nlc3NvciBRdWFkUGVyRWRnZUFBR2VvbWV0cnlQcm9jZXNzb3IKCXZjb2xvcl9TMCA9IGNvbG9yOwoJdmxvY2FsQ29vcmRfUzAgPSBsb2NhbENvb3JkOwoJc2tfUG9zaXRpb24gPSBwb3NpdGlvbi54eTAxOwp9CgAAAAB0AgAAbGF5b3V0KG1ldGFsLCBiaW5kaW5nPTApIHNhbXBsZXIyRCB1VGV4dHVyZVNhbXBsZXJfMF9TMDsKbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gZmxvYXQyIHZsb2NhbENvb3JkX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmNvbG9yX1MwOwoJZmxvYXQyIHRleENvb3JkOwoJdGV4Q29vcmQgPSB2bG9jYWxDb29yZF9TMDsKCW91dHB1dENvbG9yX1MwID0gKGJsZW5kX21vZHVsYXRlKHNhbXBsZSh1VGV4dHVyZVNhbXBsZXJfMF9TMCwgdGV4Q29vcmQpLCBvdXRwdXRDb2xvcl9TMCkpOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAABAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGAAAAABAAAAAAAAAAMAAAAdAAAAAAAAAAEAAAAJAAAACAAAAAEAAAAdAAAADAAAAAEAAAAUAAAAAAAAAEYAAAABAAAAAQAAAAUAAAAAAAAAAQAAAAUAAAAAAAAAAQAAAAAAAAA=", "HUQACAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAAKAAYAAAACAAAAAAACCAYAABIAAAAAAEAAAAH5AAAAAAIAAAABAAAAADBQCAAAAAAAAAA": "DAAAAExTS1NOAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGhhbGY0IGNvbG9yOwpsYXlvdXQobG9jYXRpb24gPSAwKSBvdXQgaGFsZjQgdmNvbG9yX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gUHJpbWl0aXZlIFByb2Nlc3NvciBRdWFkUGVyRWRnZUFBR2VvbWV0cnlQcm9jZXNzb3IKCXZjb2xvcl9TMCA9IGNvbG9yOwoJc2tfUG9zaXRpb24gPSBwb3NpdGlvbi54eTAxOwp9CgAAAAAAAH0BAABsYXlvdXQgKG1ldGFsLCBiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGhhbGY0IHZjb2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzA7CglvdXRwdXRDb2xvcl9TMCA9IHZjb2xvcl9TMDsKCWNvbnN0IGhhbGY0IG91dHB1dENvdmVyYWdlX1MwID0gaGFsZjQoMSk7Cgl7CgkJLy8gWGZlciBQcm9jZXNzb3I6IFBvcnRlciBEdWZmCgkJc2tfRnJhZ0NvbG9yID0gb3V0cHV0Q29sb3JfUzAgKiBvdXRwdXRDb3ZlcmFnZV9TMDsKCX0KfQoAAAAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABUAAAAAQAAAAAAAAACAAAAHQAAAAAAAAABAAAACQAAAAgAAAABAAAADAAAAAAAAABQAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAD9AAAA", "HVIACAAAABQAAGAAAQ4AAAAAGQQAARC4GAAAIOCAAD6P7777777777YDAAAAAAAAAAAFIBUAFS7EOCAAAAAGZJY26AAQAAAA2S5KXK4QAAAAAMAAAAAAAAQAABQM74NUDECAAAAAQ5UOAMQAAAAAAAAAAEAAAAAZ3FHDLYADAAAABKDVK5LSCAIAABQAAAAAAACAAAGAT3RWSMYIAAAAADWRYBSQAAAAAAAQAAAAGJJOXLVOIIBAAAGAAAAAAAAIAAAIAPOH2NTBAAAAAAOKFAOLAAAAAAAEAAAAAMQQAIAAAYGP6G2BSBAAACAAAAAAAAM5S4Z4NUDACAAAAAAAAADAIUOKFAOLAAAAAAACAAAAAZGIEENLQDAAAAAAAAAAABQAKAIAAIAAAADIBCEAQAAAAAAAAAAAIADQAAAAIAAAAAAAIIDFAAAAAAAQAAAA7UAAAAAAAAAAAAAAAAAMGAIAAAAAAAAA": "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", "HUQAAAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAABAAIAAAAAAIIDAAAFAAAAAAAQAAAA7UAAAAABAAAAAIAAAAAAEAACAAAAAAAA": "DAAAAExTS1NTAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGhhbGY0IGNvbG9yOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IG91dCBoYWxmNCB2Y29sb3JfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmNvbG9yX1MwID0gY29sb3I7Cglza19Qb3NpdGlvbiA9IHBvc2l0aW9uLnh5MDE7Cn0KAAAAAABPAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzA7CglvdXRwdXRDb2xvcl9TMCA9IHZjb2xvcl9TMDsKCWNvbnN0IGhhbGY0IG91dHB1dENvdmVyYWdlX1MwID0gaGFsZjQoMSk7Cgl7CgkJLy8gWGZlciBQcm9jZXNzb3I6IERpc2FibGUgQ29sb3IKCX0KfQoAAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPAAAAAEAAAAAAAAAAgAAAB0AAAAAAAAAAQAAAAkAAAAIAAAAAQAAAAwAAAAAAAAAUAAAAAAAAAAAAAAA/QAAAA==", "CIAAAAAAQAARQAAYQAAAAGFYQAABRAAAAEEAAAAAAARAEAEABYAAAAEAAAAAAAEEBQAAAACGAAAAAAIAAAAP2AAAAAAQAAAACAAAAAGDAEBAAAAAAAAA": "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", "HUIAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAAAAAALUAQBAEAQAAAAGQCBAMQACAIAAAAAACQAGAAAAAQAAAAAAAQQGAAAABDAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAABQAAQAAAAAAAA": "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", "HUQACAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAADEAANAAAAA2RNJRSBIAAAAABQAAAABAAAAAAAZCEFUCYTDECQAAGAAAAAAAAAAAAAAOQAAAAAAAQAAAABAMQAABIAAAAAAEAAAAH5AAAAAAIAAAABAAAAADBQCAAAAAAAAAA": "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", "HVJAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAABAAAAAABBAMABAAOAAAABAAAAAAABBAMAAAIYAAAAABAAAAAAAAAAAAAAAAAAAAAAAAYMAQEAAAAAAAA": "DAAAAExTS1PMAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGhhbGY0IGNvbG9yOwpsYXlvdXQobG9jYXRpb24gPSAyKSBpbiBmbG9hdDIgbG9jYWxDb29yZDsKbGF5b3V0KGxvY2F0aW9uID0gMCkgZmxhdCBvdXQgaGFsZjQgdmNvbG9yX1MwOwpsYXlvdXQobG9jYXRpb24gPSAxKSBvdXQgZmxvYXQyIHZsb2NhbENvb3JkX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gUHJpbWl0aXZlIFByb2Nlc3NvciBRdWFkUGVyRWRnZUFBR2VvbWV0cnlQcm9jZXNzb3IKCXZjb2xvcl9TMCA9IGNvbG9yOwoJdmxvY2FsQ29vcmRfUzAgPSBsb2NhbENvb3JkOwoJc2tfUG9zaXRpb24gPSBwb3NpdGlvbi54eTAxOwp9CgAAAAB0AgAAbGF5b3V0KG1ldGFsLCBiaW5kaW5nPTApIHNhbXBsZXIyRCB1VGV4dHVyZVNhbXBsZXJfMF9TMDsKbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gZmxvYXQyIHZsb2NhbENvb3JkX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmNvbG9yX1MwOwoJZmxvYXQyIHRleENvb3JkOwoJdGV4Q29vcmQgPSB2bG9jYWxDb29yZF9TMDsKCW91dHB1dENvbG9yX1MwID0gKGJsZW5kX21vZHVsYXRlKHNhbXBsZSh1VGV4dHVyZVNhbXBsZXJfMF9TMCwgdGV4Q29vcmQpLCBvdXRwdXRDb2xvcl9TMCkpOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAABAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAGAAAAABAAAAAAAAAAMAAAAdAAAAAAAAAAEAAAAJAAAACAAAAAEAAAAdAAAADAAAAAEAAAAUAAAAAAAAAEYAAAABAAAAAQAAAAUAAAAAAAAAAQAAAAUAAAAAAAAAAQAAAAAAAAA=", "DYBQAAAAAEAAAAAQAABQAAIOAAABCFYIAAKAUDAAAAAAAAABAAAAAAAAAAANAAIAAAABAAAAACAJAAIAAAAFAAAAAAAQAAAA7UAAAAAAAAAAAAAAAAAMGAIAAAAAAAAA": "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", "HUQAAAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAAKAAYAAAACAAAAAAACCAYAABDAAAAAAEAAAAAAAAAAAAAAAAAAAAAAADBQCAQAAAAAAAA": "DAAAAExTS1NTAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGhhbGY0IGNvbG9yOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IG91dCBoYWxmNCB2Y29sb3JfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmNvbG9yX1MwID0gY29sb3I7Cglza19Qb3NpdGlvbiA9IHBvc2l0aW9uLnh5MDE7Cn0KAAAAAACCAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzA7CglvdXRwdXRDb2xvcl9TMCA9IHZjb2xvcl9TMDsKCWNvbnN0IGhhbGY0IG91dHB1dENvdmVyYWdlX1MwID0gaGFsZjQoMSk7Cgl7CgkJLy8gWGZlciBQcm9jZXNzb3I6IFBvcnRlciBEdWZmCgkJc2tfRnJhZ0NvbG9yID0gb3V0cHV0Q29sb3JfUzAgKiBvdXRwdXRDb3ZlcmFnZV9TMDsKCX0KfQoAAAAAAAABAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAFQAAAABAAAAAAAAAAIAAAAdAAAAAAAAAAEAAAAJAAAACAAAAAEAAAAMAAAAAAAAAEYAAAABAAAAAQAAAAUAAAAAAAAAAQAAAAUAAAAAAAAAAQAAAAAAAAA=", "HUQAAAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAABAAIAAAAAAIIDAAAFAAAAAAAQAAAA7UAAAAABAAAAAEAAAAAAEAACAAAAAAAA": "DAAAAExTS1NTAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGhhbGY0IGNvbG9yOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IG91dCBoYWxmNCB2Y29sb3JfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmNvbG9yX1MwID0gY29sb3I7Cglza19Qb3NpdGlvbiA9IHBvc2l0aW9uLnh5MDE7Cn0KAAAAAABPAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzA7CglvdXRwdXRDb2xvcl9TMCA9IHZjb2xvcl9TMDsKCWNvbnN0IGhhbGY0IG91dHB1dENvdmVyYWdlX1MwID0gaGFsZjQoMSk7Cgl7CgkJLy8gWGZlciBQcm9jZXNzb3I6IERpc2FibGUgQ29sb3IKCX0KfQoAAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPAAAAAEAAAAAAAAAAgAAAB0AAAAAAAAAAQAAAAkAAAAIAAAAAQAAAAwAAAAAAAAAUAAAAAAAAAAAAAAA/QAAAA==", "IYBAAAAABAAACAABBYAAAKAAAMAAGEAAAABRAEAAAEHCAAAAAAAABCAAAAAABAEQAEAAAACGAAAAAAIAAAAP2AAAAAAQAAAACAAAAAACAAAAAAAAAAAA": "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", "DAQAAAAAAAAAAAAAAJQAAIGAAEACBYQCAGAEFAIBAAAAAABAAAAAAAAAAAAAAOQAAAAAAAQAAAABAMQAAAAFAAAAAAAQAAAA7UAAAAABAAAAAEAAAAAMGAIAAAAAAAAA": "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", "HUQAAAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAADEAANAAAAA2RNJRSBIAAAAABQAAAABAAAAAAAZCEFUCYTDECQAAGAAAAAAAAAAAAAAOQAAAAAAAQAAAABAMQAABIAAAAAAEAAAAH5AAAAAAIAAAABAAAAADBQCAQAAAAAAAA": "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", "HVIAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAAAAAAAAFIBUAFS7EOCAAAAAGZJY26AAQAAAA2S5KXK4QAAAAAMAAAAAAAAQAABQM74NUDECAAAEA6TBTKEYAAAAAAAAAAEAAAAAZ3FHDLYADAAAABKDVK5LSCAIAABQAAAAAAACAAAGAT3RWSMYIAAAAB2MHNMTAAAAAAAAQAAAAGJJOXLVOIIBAAAGAAAAAAAAIAAAIAPOH2NTBAAAAADJA7V2MAAAAAAAEAAAAAMQQAIAAAYGP6G2BSBAAACAAAAAAAAM5S4Z4NUDACAAAAAAAAADAIXJA7V2MAAAAAAACAAAAAZBI7VYM2BAAAAAAAAAAABQAKAIAAIAAAADIBCEAQAAAAAAAAAAAIADQAAAAIAAAAAAAIIDEMAAAAAAQAAAAAAAAAAAAAAAAAAAAAAAMGAICAAAAAAAA": "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", "HVIAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAAAAAAAAFIBUAFS7EOCAAAAAGZJY26AAQAAAA2S5KXK4QAAAAAMAAAAAAAAQAABQM74NUDECAAAEA6TBTKEYAAAAAAAAAAEAAAAAZ3FHDLYADAAAABKDVK5LSCAIAABQAAAAAAACAAAGAT3RWSMYIAAAAB2MHNMTAAAAAAAAQAAAAGJJOXLVOIIBAAAGAAAAAAAAIAAAIAPOH2NTBAAAAADJA7V2MAAAAAAAEAAAAAMQQAIAAAYGP6G2BSBAAACAAAAAAAAM5S4Z4NUDACAAAAAAAAADAIXJA7V2MAAAAAAACAAAAAZBI7VYM2BAAAAAAAAAAABQAKAIAAIAAAADIBCEAQAAAAAAAAAAAIADQAAAAIAAAAAAAIIDEMAAAAAAQAAAAAAAAAAAAAAAAAIAAAAAMGAICAAAAAAAA": "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", "DAQAAAAAAAAAAAAAAJQAAIGAAEACBYQCAGAEFAIBAAAAAABAAAAAAAAAAAAAAOQAAAAAAAQAAAABAMQAAAAEMAAAAAAQAAAA7UAAAAABAAAAAEAAAAAMGAIAAAAAAAAA": "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", "HUJAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAQAAAAAAQQGACQAGAAAAAQAAAAAAAQQGAAAKAAAAAABAAAAB7IAAAAAAAAAAAQAAAAAYMAQEAAAAAAAA": "DAAAAExTS1NkAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGZsb2F0MiBsb2NhbENvb3JkOwpsYXlvdXQobG9jYXRpb24gPSAwKSBvdXQgZmxvYXQyIHZsb2NhbENvb3JkX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gUHJpbWl0aXZlIFByb2Nlc3NvciBRdWFkUGVyRWRnZUFBR2VvbWV0cnlQcm9jZXNzb3IKCXZsb2NhbENvb3JkX1MwID0gbG9jYWxDb29yZDsKCXNrX1Bvc2l0aW9uID0gcG9zaXRpb24ueHkwMTsKfQoAAAAALgIAAGxheW91dChtZXRhbCwgYmluZGluZz0wKSBzYW1wbGVyMkQgdVRleHR1cmVTYW1wbGVyXzBfUzA7CmxheW91dCAobWV0YWwsIGJpbmRpbmc9MCkgdW5pZm9ybSB1bmlmb3JtQnVmZmVyCnsKCWxheW91dChvZmZzZXQ9MCkgZmxvYXQ0IHNrX1JUQWRqdXN0Owp9CjsKbGF5b3V0KGxvY2F0aW9uID0gMCkgaW4gZmxvYXQyIHZsb2NhbENvb3JkX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMCA9IGhhbGY0KDEpOwoJZmxvYXQyIHRleENvb3JkOwoJdGV4Q29vcmQgPSB2bG9jYWxDb29yZF9TMDsKCW91dHB1dENvbG9yX1MwID0gKGJsZW5kX21vZHVsYXRlKHNhbXBsZSh1VGV4dHVyZVNhbXBsZXJfMF9TMCwgdGV4Q29vcmQpLCBoYWxmNCgxKSkpOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAVAAAAAEAAAAAAAAAAgAAAB0AAAAAAAAAAQAAAB0AAAAIAAAAAQAAABAAAAAAAAAAUAAAAAEAAAABAAAABQAAAAAAAAABAAAABQAAAAAAAAABAAAA/QAAAA==", "HVJAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAABAAAAAABBAMADSAB4QAAAAAEAAAAAAHIAAAAAAAIAAAAAQGIAAAAAAEMAAAAAAQAAAAAAAAAAAAAAAAAAAAAAAMGAICAAAAAAAA": "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", "IYAAAAAABAAACAABBYAAAIAAAIAAGEAAAABRAEAAAAAAAAEIAAAAAAEASAAQAAAAKAAAAAABAAAAB7IAAAAACAAAAAQAAAAAAIAAAAAAAAAAA": "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", "CIAAAAAAQAARQAAYQAAAAGFYQAABRAAAAEEAAAAAAARAEAEABYAAAAEAAAAAAAEEBQAAAACGAAAAAAIAAAAAAAAAAAAAAAAAAAAAAAGDAEBAAAAAAAAA": "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", "HUQACAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAAHEADZAAAAAAIAAAAAAOQAAAAAAAQAAAABAMQAAAAAAUAAAAAACAAAAAAAAAAAAAAAAAAAAAAABQYBAAAAAAAAAA": "DAAAAExTS1OkAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7CglsYXlvdXQob2Zmc2V0PTE2KSBmbG9hdDQgdWlubmVyUmVjdF9TMTsKCWxheW91dChvZmZzZXQ9MzIpIGhhbGYyIHVyYWRpdXNQbHVzSGFsZl9TMTsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDApIG91dCBoYWxmNCB2Y29sb3JfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmNvbG9yX1MwID0gY29sb3I7Cglza19Qb3NpdGlvbiA9IHBvc2l0aW9uLnh5MDE7Cn0KAAAAACIDAABsYXlvdXQgKG1ldGFsLCBiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKCWxheW91dChvZmZzZXQ9MTYpIGZsb2F0NCB1aW5uZXJSZWN0X1MxOwoJbGF5b3V0KG9mZnNldD0zMikgaGFsZjIgdXJhZGl1c1BsdXNIYWxmX1MxOwp9CjsKbGF5b3V0KGxvY2F0aW9uID0gMCkgaW4gaGFsZjQgdmNvbG9yX1MwOwpoYWxmNCBDaXJjdWxhclJSZWN0X1MxKGhhbGY0IF9pbnB1dCkgCnsKCWZsb2F0MiBkeHkwID0gdWlubmVyUmVjdF9TMS5MVCAtIHNrX0ZyYWdDb29yZC54eTsKCWZsb2F0MiBkeHkxID0gc2tfRnJhZ0Nvb3JkLnh5IC0gdWlubmVyUmVjdF9TMS5SQjsKCWZsb2F0MiBkeHkgPSBtYXgobWF4KGR4eTAsIGR4eTEpLCAwLjApOwoJaGFsZiBhbHBoYSA9IGhhbGYoc2F0dXJhdGUodXJhZGl1c1BsdXNIYWxmX1MxLnggLSBsZW5ndGgoZHh5KSkpOwoJcmV0dXJuIF9pbnB1dCAqIGFscGhhOwp9CnZvaWQgbWFpbigpIAp7CgkvLyBTdGFnZSAwLCBRdWFkUGVyRWRnZUFBR2VvbWV0cnlQcm9jZXNzb3IKCWhhbGY0IG91dHB1dENvbG9yX1MwOwoJb3V0cHV0Q29sb3JfUzAgPSB2Y29sb3JfUzA7Cgljb25zdCBoYWxmNCBvdXRwdXRDb3ZlcmFnZV9TMCA9IGhhbGY0KDEpOwoJaGFsZjQgb3V0cHV0X1MxOwoJb3V0cHV0X1MxID0gQ2lyY3VsYXJSUmVjdF9TMShvdXRwdXRDb3ZlcmFnZV9TMCk7Cgl7CgkJLy8gWGZlciBQcm9jZXNzb3I6IFBvcnRlciBEdWZmCgkJc2tfRnJhZ0NvbG9yID0gb3V0cHV0Q29sb3JfUzAgKiBvdXRwdXRfUzE7Cgl9Cn0KAAABAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABUAAAAAQAAAAAAAAACAAAAHQAAAAAAAAABAAAACQAAAAgAAAABAAAADAAAAAAAAABQAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAAAAAAA", "HUJAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAQAAAAAAQQGAAZAADIAAAAGULKMMQKAAAAAAMAAAAAIAAAAAAGIRBNAWEYZAUAABQAAAAAAAAAAAAADUAAAAAAAEAAAAAIDEAAAKAAAAAABAAAAB7IAAAAACAAAAAIAAAAAYMAQEAAAAAAAA": "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", "HUIAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAAAAAALUASJ3EZYN2AAAAACAAAEAAAABSCQKL3IYIJ2AAAAACAAAEAAAABLRAABAAAAABAEGABBAMAAQAAAAAAAAOQAAAAAAAQAAAABAMQAAAAAARQAAAAACAAAAAAAAAAAAAAAAAAAAAAAAAYAAIAAAAAAAA": "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", "HVJAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAABAAAAAABBAMABSAAGQAAAANIWUYZAUAAAAAAYAAAAAQAAAAAAEQCG2RMJRSBIAABAABAAAAAAAAAAAHIAAAAAAAIAAAAAQGIAAAKAAAAAABAAAAB7IAAAAAAAAAAAAAAAAAYMAQEAAAAAAAA": "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", "HUQAAAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAAHEADZAAAAAAIAAAAAAOQAAAAAAAQAAAABAMQAAAAAARQAAAAACAAAAAAAAAAAAAAAAAAAAAAABQYBAIAAAAAAAA": "DAAAAExTS1OpAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7CglsYXlvdXQob2Zmc2V0PTE2KSBmbG9hdDQgdWlubmVyUmVjdF9TMTsKCWxheW91dChvZmZzZXQ9MzIpIGhhbGYyIHVyYWRpdXNQbHVzSGFsZl9TMTsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gaGFsZjQgY29sb3I7CmxheW91dChsb2NhdGlvbiA9IDApIGZsYXQgb3V0IGhhbGY0IHZjb2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFByaW1pdGl2ZSBQcm9jZXNzb3IgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgl2Y29sb3JfUzAgPSBjb2xvcjsKCXNrX1Bvc2l0aW9uID0gcG9zaXRpb24ueHkwMTsKfQoAAAAAAAAAJwMAAGxheW91dCAobWV0YWwsIGJpbmRpbmc9MCkgdW5pZm9ybSB1bmlmb3JtQnVmZmVyCnsKCWxheW91dChvZmZzZXQ9MCkgZmxvYXQ0IHNrX1JUQWRqdXN0OwoJbGF5b3V0KG9mZnNldD0xNikgZmxvYXQ0IHVpbm5lclJlY3RfUzE7CglsYXlvdXQob2Zmc2V0PTMyKSBoYWxmMiB1cmFkaXVzUGx1c0hhbGZfUzE7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKaGFsZjQgQ2lyY3VsYXJSUmVjdF9TMShoYWxmNCBfaW5wdXQpIAp7CglmbG9hdDIgZHh5MCA9IHVpbm5lclJlY3RfUzEuTFQgLSBza19GcmFnQ29vcmQueHk7CglmbG9hdDIgZHh5MSA9IHNrX0ZyYWdDb29yZC54eSAtIHVpbm5lclJlY3RfUzEuUkI7CglmbG9hdDIgZHh5ID0gbWF4KG1heChkeHkwLCBkeHkxKSwgMC4wKTsKCWhhbGYgYWxwaGEgPSBoYWxmKHNhdHVyYXRlKHVyYWRpdXNQbHVzSGFsZl9TMS54IC0gbGVuZ3RoKGR4eSkpKTsKCXJldHVybiBfaW5wdXQgKiBhbHBoYTsKfQp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMDsKCW91dHB1dENvbG9yX1MwID0gdmNvbG9yX1MwOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCWhhbGY0IG91dHB1dF9TMTsKCW91dHB1dF9TMSA9IENpcmN1bGFyUlJlY3RfUzEob3V0cHV0Q292ZXJhZ2VfUzApOwoJewoJCS8vIFhmZXIgUHJvY2Vzc29yOiBQb3J0ZXIgRHVmZgoJCXNrX0ZyYWdDb2xvciA9IG91dHB1dENvbG9yX1MwICogb3V0cHV0X1MxOwoJfQp9CgABAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABUAAAAAQAAAAAAAAACAAAAHQAAAAAAAAABAAAACQAAAAgAAAABAAAADAAAAAAAAABGAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAAAAAAA", "HUJAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAQAAAAAAQQGACQAGAAAAAQAAAAAAAQQGAAAKAAAAAABAAAAB7IAAAAAAAAAAAAAAAAAYMAQEAAAAAAAA": "DAAAAExTS1NkAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGZsb2F0MiBsb2NhbENvb3JkOwpsYXlvdXQobG9jYXRpb24gPSAwKSBvdXQgZmxvYXQyIHZsb2NhbENvb3JkX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gUHJpbWl0aXZlIFByb2Nlc3NvciBRdWFkUGVyRWRnZUFBR2VvbWV0cnlQcm9jZXNzb3IKCXZsb2NhbENvb3JkX1MwID0gbG9jYWxDb29yZDsKCXNrX1Bvc2l0aW9uID0gcG9zaXRpb24ueHkwMTsKfQoAAAAALgIAAGxheW91dChtZXRhbCwgYmluZGluZz0wKSBzYW1wbGVyMkQgdVRleHR1cmVTYW1wbGVyXzBfUzA7CmxheW91dCAobWV0YWwsIGJpbmRpbmc9MCkgdW5pZm9ybSB1bmlmb3JtQnVmZmVyCnsKCWxheW91dChvZmZzZXQ9MCkgZmxvYXQ0IHNrX1JUQWRqdXN0Owp9CjsKbGF5b3V0KGxvY2F0aW9uID0gMCkgaW4gZmxvYXQyIHZsb2NhbENvb3JkX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gU3RhZ2UgMCwgUXVhZFBlckVkZ2VBQUdlb21ldHJ5UHJvY2Vzc29yCgloYWxmNCBvdXRwdXRDb2xvcl9TMCA9IGhhbGY0KDEpOwoJZmxvYXQyIHRleENvb3JkOwoJdGV4Q29vcmQgPSB2bG9jYWxDb29yZF9TMDsKCW91dHB1dENvbG9yX1MwID0gKGJsZW5kX21vZHVsYXRlKHNhbXBsZSh1VGV4dHVyZVNhbXBsZXJfMF9TMCwgdGV4Q29vcmQpLCBoYWxmNCgxKSkpOwoJY29uc3QgaGFsZjQgb3V0cHV0Q292ZXJhZ2VfUzAgPSBoYWxmNCgxKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRDb2xvcl9TMCAqIG91dHB1dENvdmVyYWdlX1MwOwoJfQp9CgAAAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAVAAAAAEAAAAAAAAAAgAAAB0AAAAAAAAAAQAAAB0AAAAIAAAAAQAAABAAAAAAAAAAUAAAAAEAAAABAAAABQAAAAAAAAABAAAABQAAAAAAAAABAAAA/QAAAA==", "AYQA5AADQAAAOAEARAFQJAABBADIB7777777777777777777777767YAAAAAAAAAACAMWAEQYX3QQAIAACAO2VADHYAAAAEALJ3XKFISAAAAABQAAAAEAAAAAA2JTW5YCMAQAAGAEENLQDAAAAAAAACAAAAAAQCGWZJQ36AAAAAAA2W52VKUQAAAAAMAAAAAAAAQAAGQMRXOGTQEAAAABB3I4AZAAAAAQAAAAAAADGUXKV2XEEAQAADAAAAAAAAEAAAEBE5ZRU5RCAAAAAOKFAOLAAAAAAAEAAAAAMQQAIAABIGJ3TDJ2CAAAAAACAAAAAZNEZDO4NHAIAAAAAAAAAEACVYYQBROAMAAAAAIAAAABEBBQ5UOAMQAAAAAAAAAACABKBAABAAAAAFAEEQCEAAAAAAAAAAAQAB5QAYAAAEAAAAAAA4IAPAAACAAAAAAAAAB2AAAAAAACAAAAAEBSAAAABIAAAAAAEAAAAAAAAAAAAAAAAAAAAAAADBQCAAAAAAAAAA": "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", "DAQAAAAAAAAAAAAAAJQAAIGAAEACBYQCAGAEFAIBAAAAAABAAAAAAAAAAAAAAOQAAAAAAAQAAAABAMQAAAAFAAAAAAAQAAAAAAAAAAAAAAAAAAAAAAAMGAIAAAAAAAAA": "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", "HVIAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAAAAAAAAFIBVWKMG7QAAAAAAGVXOVKVEAAAAADAAAAAAAAEAAAACEC4AA4JAAAAAIO2HAGIAAAAEAAAAAAAAZVF2VOVZBAEAAAYAAAAAAABAAAAABAXIAHCIAAAAADSRIDSYAAAAAABAAAAADEEACAAAABCBOAAOEQAAAAAAQAAAAGIBEIFYABYSAAAAAAAAABAAVOGEAMLQDAAAAACAAAAAJAIMHNDQDEAAAAAAAAAAAAAOQAAAAAAAQAAAABAMQAAAAKAAAAAABAAAAAAAAAAAAAAAAAAQAAAAAQEAQEAAAAAAAA": "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", "IYBAAAAABAAACAABBYAAAKAAAMAAGEAAAABRAEAAAEHCAAAAAAAABCAAAAAABAEQAEAAAACQAAAAAAIAAAAP2AAAAAAQAAAACAAAAAACAAAAAAAAAAAA": "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", "AYQQ5AADQAAAOAEARAFQJAABBADIB7777777777777777777777767YAAAAAAAAAAAAOQAAAAAAAQAAAABAMQAAAABIAAAAAAEAAAAH5AAAAAAAAAAAAAAAAADBQCAAAAAAAAAA": "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", "HUIAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAAAAAALUASJ3EZYN2AAAAAAAAAEAAAABSCQKL3IYIJ2AAAAAAAAAEAAAABLBAABAAAAABAEGABBAMAAAAAAAAAAAOQAAAAAAAQAAAABAMQAAAAAAUAAAAAACAAAAAAAAAAAAAAAAAAAAAAAAAYAAIAAAAAAAA": "DAAAAExTS1NLAgAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7CglsYXlvdXQob2Zmc2V0PTE2KSBmbG9hdDN4MyB1bWF0cml4X1MxX2MwX2MwOwoJbGF5b3V0KG9mZnNldD02NCkgaGFsZjQgdW9mZnNldHNBbmRLZXJuZWxfUzFfYzBbMTRdOwoJbGF5b3V0KG9mZnNldD0xNzYpIGhhbGYyIHVkaXJfUzFfYzA7CglsYXlvdXQob2Zmc2V0PTE5MikgZmxvYXQzeDMgdW1hdHJpeF9TMTsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGZsb2F0MiBwb3NpdGlvbjsKbGF5b3V0KGxvY2F0aW9uID0gMSkgaW4gZmxvYXQyIGxvY2FsQ29vcmQ7CmxheW91dChsb2NhdGlvbiA9IDApIG91dCBmbG9hdDIgdlRyYW5zZm9ybWVkQ29vcmRzXzJfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJc2tfUG9zaXRpb24gPSBwb3NpdGlvbi54eTAxOwoJewoJCXZUcmFuc2Zvcm1lZENvb3Jkc18yX1MwID0gZmxvYXQzeDIodW1hdHJpeF9TMSkgKiBsb2NhbENvb3JkLnh5MTsKCX0KfQoAAAAAAOIFAABjb25zdCBpbnQga01heExvb3BMaW1pdF9TMV9jMCA9IDg7CmxheW91dChtZXRhbCwgYmluZGluZz0wKSBzYW1wbGVyMkQgdVRleHR1cmVTYW1wbGVyXzBfUzE7CmxheW91dCAobWV0YWwsIGJpbmRpbmc9MCkgdW5pZm9ybSB1bmlmb3JtQnVmZmVyCnsKCWxheW91dChvZmZzZXQ9MCkgZmxvYXQ0IHNrX1JUQWRqdXN0OwoJbGF5b3V0KG9mZnNldD0xNikgZmxvYXQzeDMgdW1hdHJpeF9TMV9jMF9jMDsKCWxheW91dChvZmZzZXQ9NjQpIGhhbGY0IHVvZmZzZXRzQW5kS2VybmVsX1MxX2MwWzE0XTsKCWxheW91dChvZmZzZXQ9MTc2KSBoYWxmMiB1ZGlyX1MxX2MwOwoJbGF5b3V0KG9mZnNldD0xOTIpIGZsb2F0M3gzIHVtYXRyaXhfUzE7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgdlRyYW5zZm9ybWVkQ29vcmRzXzJfUzA7CmhhbGY0IFRleHR1cmVFZmZlY3RfUzFfYzBfYzBfYzAoaGFsZjQgX2lucHV0LCBmbG9hdDIgX2Nvb3JkcykgCnsKCXJldHVybiBzYW1wbGUodVRleHR1cmVTYW1wbGVyXzBfUzEsIF9jb29yZHMpOwp9CmhhbGY0IE1hdHJpeEVmZmVjdF9TMV9jMF9jMChoYWxmNCBfaW5wdXQsIGZsb2F0MiBfY29vcmRzKSAKewoJcmV0dXJuIFRleHR1cmVFZmZlY3RfUzFfYzBfYzBfYzAoX2lucHV0LCBmbG9hdDN4Mih1bWF0cml4X1MxX2MwX2MwKSAqIF9jb29yZHMueHkxKTsKfQpoYWxmNCBHYXVzc2lhbkJsdXIxRF9TMV9jMChoYWxmNCBfaW5wdXQpIAp7CgloYWxmNCBfdG1wXzBfaW5Db2xvciA9IF9pbnB1dDsKCWZsb2F0MiBfdG1wXzFfY29vcmRzID0gdlRyYW5zZm9ybWVkQ29vcmRzXzJfUzA7CgloYWxmNCBzdW0gPSBoYWxmNCgwLjApOwoJZm9yIChpbnQgaSA9IDA7aSA8IGtNYXhMb29wTGltaXRfUzFfYzA7ICsraSkgCgl7CgkJaGFsZjQgcyA9IHVvZmZzZXRzQW5kS2VybmVsX1MxX2MwW2ldOwoJCXN1bSArPSBzLnkgKiBNYXRyaXhFZmZlY3RfUzFfYzBfYzAoX3RtcF8wX2luQ29sb3IsIF90bXBfMV9jb29yZHMgKyBmbG9hdDIocy54ICogdWRpcl9TMV9jMCkpOwoJCXN1bSArPSBzLncgKiBNYXRyaXhFZmZlY3RfUzFfYzBfYzAoX3RtcF8wX2luQ29sb3IsIF90bXBfMV9jb29yZHMgKyBmbG9hdDIocy56ICogdWRpcl9TMV9jMCkpOwoJfQoJcmV0dXJuIGhhbGY0KHN1bSk7Cn0KaGFsZjQgTWF0cml4RWZmZWN0X1MxKGhhbGY0IF9pbnB1dCkgCnsKCXJldHVybiBHYXVzc2lhbkJsdXIxRF9TMV9jMChfaW5wdXQpOwp9CnZvaWQgbWFpbigpIAp7CgkvLyBTdGFnZSAwLCBRdWFkUGVyRWRnZUFBR2VvbWV0cnlQcm9jZXNzb3IKCWhhbGY0IG91dHB1dENvbG9yX1MwID0gaGFsZjQoMSk7Cgljb25zdCBoYWxmNCBvdXRwdXRDb3ZlcmFnZV9TMCA9IGhhbGY0KDEpOwoJaGFsZjQgb3V0cHV0X1MxOwoJb3V0cHV0X1MxID0gTWF0cml4RWZmZWN0X1MxKG91dHB1dENvbG9yX1MwKTsKCXsKCQkvLyBYZmVyIFByb2Nlc3NvcjogUG9ydGVyIER1ZmYKCQlza19GcmFnQ29sb3IgPSBvdXRwdXRfUzEgKiBvdXRwdXRDb3ZlcmFnZV9TMDsKCX0KfQoAAAAAAAABAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwAAAABAAAAAAAAAAIAAAAdAAAAAAAAAAEAAAAdAAAACAAAAAEAAAAQAAAAAAAAAFAAAAAAAAAAAQAAAAAAAAA=", "HUIAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAAAAAALUASJ3EZYN2AAAAACAAAEAAAABSCQKL3IYIJ2AAAAACAAAEAAAABLRAABAAAAABAEGABBAMAAQAAAAAAAAOQAAAAAAAQAAAABAMQAAAAAAUAAAAAACAAAAAAAAAAAAAAAAAAAAAAAAAYAAAAAAAAAAA": "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", "HVJAAAAAABIAAGAAAQ4AAAH477776R24EAAAIOBQAD6P7777777777YDAAAAABAAAAAABBAMABSAAGQAAAANIWUYZAUAAAAAAYAAAAAQAAAAAAEQCG2RMJRSBIAABAABAAAAAAAAAAAHIAAAAAAAIAAAAAQGIAAAKAAAAAABAAAAB7IAAAAACAAAAAIAAAAAYMAQEAAAAAAAA": "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", "IYAAAAAABAAACAABBYAAAIAAAIAAGEAAAABRAEAAAAAAAAEIAAAAAAEASAAQAAAAIYAAAAABAAAAB7IAAAAACAAAAAQAAAAAAIAAAAAAAAAAA": "DAAAAExTS1OMDwAAY29uc3QgZmxvYXQgUFJFQ0lTSU9OID0gNC4wMDAwMDA7CmNvbnN0IGZsb2F0IE1BWF9GSVhFRF9SRVNPTFZFX0xFVkVMID0gNS4wMDAwMDA7CmNvbnN0IGZsb2F0IE1BWF9GSVhFRF9TRUdNRU5UUyA9IDMyLjAwMDAwMDsKbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7CglsYXlvdXQob2Zmc2V0PTE2KSBmbG9hdDQgdWFmZmluZU1hdHJpeF9TMDsKCWxheW91dChvZmZzZXQ9MzIpIGZsb2F0MiB1dHJhbnNsYXRlX1MwOwoJbGF5b3V0KG9mZnNldD00MCkgaGFsZjQgdWNvbG9yX1MwOwp9CjsKbGF5b3V0KGxvY2F0aW9uID0gMCkgaW4gZmxvYXQyIHJlc29sdmVMZXZlbF9hbmRfaWR4OwpsYXlvdXQobG9jYXRpb24gPSAxKSBpbiBmbG9hdDQgcDAxOwpsYXlvdXQobG9jYXRpb24gPSAyKSBpbiBmbG9hdDQgcDIzOwpmbG9hdCB3YW5nc19mb3JtdWxhX21heF9mZGlmZl9wMihmbG9hdDIgcDAsIGZsb2F0MiBwMSwgZmxvYXQyIHAyLCBmbG9hdDIgcDMsZmxvYXQyeDIgbWF0cml4KSAKewoJZmxvYXQyIGQwID0gbWF0cml4ICogKGZtYShmbG9hdDIoLTIpLCBwMSwgcDIpICsgcDApOwoJZmxvYXQyIGQxID0gbWF0cml4ICogKGZtYShmbG9hdDIoLTIpLCBwMiwgcDMpICsgcDEpOwoJcmV0dXJuIG1heChkb3QoZDAsZDApLCBkb3QoZDEsZDEpKTsKfQpmbG9hdCB3YW5nc19mb3JtdWxhX2N1YmljKGZsb2F0IF9wcmVjaXNpb25fLCBmbG9hdDIgcDAsIGZsb2F0MiBwMSwgZmxvYXQyIHAyLCBmbG9hdDIgcDMsZmxvYXQyeDIgbWF0cml4KSAKewoJZmxvYXQgbSA9IHdhbmdzX2Zvcm11bGFfbWF4X2ZkaWZmX3AyKHAwLCBwMSwgcDIsIHAzLCBtYXRyaXgpOwoJcmV0dXJuIG1heChjZWlsKHNxcnQoMC43NSAqIF9wcmVjaXNpb25fICogc3FydChtKSkpLCAxLjApOwp9CmZsb2F0IHdhbmdzX2Zvcm11bGFfY3ViaWNfbG9nMihmbG9hdCBfcHJlY2lzaW9uXywgZmxvYXQyIHAwLCBmbG9hdDIgcDEsIGZsb2F0MiBwMiwgZmxvYXQyIHAzLGZsb2F0MngyIG1hdHJpeCkgCnsKCWZsb2F0IG0gPSB3YW5nc19mb3JtdWxhX21heF9mZGlmZl9wMihwMCwgcDEsIHAyLCBwMywgbWF0cml4KTsKCXJldHVybiBjZWlsKGxvZzIobWF4KDAuNTYyNSAqIF9wcmVjaXNpb25fICogX3ByZWNpc2lvbl8gKiBtLCAxLjApKSAqIC4yNSk7Cn0KZmxvYXQgd2FuZ3NfZm9ybXVsYV9jb25pY19wMihmbG9hdCBfcHJlY2lzaW9uXywgZmxvYXQyIHAwLCBmbG9hdDIgcDEsIGZsb2F0MiBwMiwgZmxvYXQgdykgCnsKCWZsb2F0MiBDID0gKG1pbihtaW4ocDAsIHAxKSwgcDIpICsgbWF4KG1heChwMCwgcDEpLCBwMikpICogMC41OwoJcDAgLT0gQzsKCXAxIC09IEM7CglwMiAtPSBDOwoJZmxvYXQgbSA9IHNxcnQobWF4KG1heChkb3QocDAscDApLCBkb3QocDEscDEpKSwgZG90KHAyLHAyKSkpOwoJZmxvYXQyIGRwID0gZm1hKGZsb2F0MigtMi4wICogdyksIHAxLCBwMCkgKyBwMjsKCWZsb2F0IGR3ID0gYWJzKGZtYSgtMi4wLCB3LCAyLjApKTsKCWZsb2F0IHJwX21pbnVzXzEgPSBtYXgoMC4wLCBmbWEobSwgX3ByZWNpc2lvbl8sIC0xLjApKTsKCWZsb2F0IG51bWVyID0gbGVuZ3RoKGRwKSAqIF9wcmVjaXNpb25fICsgcnBfbWludXNfMSAqIGR3OwoJZmxvYXQgZGVub20gPSA0ICogbWluKHcsIDEuMCk7CglyZXR1cm4gbnVtZXIvZGVub207Cn0KZmxvYXQgd2FuZ3NfZm9ybXVsYV9jb25pYyhmbG9hdCBfcHJlY2lzaW9uXywgZmxvYXQyIHAwLCBmbG9hdDIgcDEsIGZsb2F0MiBwMiwgZmxvYXQgdykgCnsKCWZsb2F0IG4yID0gd2FuZ3NfZm9ybXVsYV9jb25pY19wMihfcHJlY2lzaW9uXywgcDAsIHAxLCBwMiwgdyk7CglyZXR1cm4gbWF4KGNlaWwoc3FydChuMikpLCAxLjApOwp9CmZsb2F0IHdhbmdzX2Zvcm11bGFfY29uaWNfbG9nMihmbG9hdCBfcHJlY2lzaW9uXywgZmxvYXQyIHAwLCBmbG9hdDIgcDEsIGZsb2F0MiBwMiwgZmxvYXQgdykgCnsKCWZsb2F0IG4yID0gd2FuZ3NfZm9ybXVsYV9jb25pY19wMihfcHJlY2lzaW9uXywgcDAsIHAxLCBwMiwgdyk7CglyZXR1cm4gY2VpbChsb2cyKG1heChuMiwgMS4wKSkgKiAuNSk7Cn0KYm9vbCBpc19jb25pY19jdXJ2ZSgpIAp7CglyZXR1cm4gaXNpbmYocDIzLncpOwp9CmJvb2wgaXNfdHJpYW5ndWxhcl9jb25pY19jdXJ2ZSgpIAp7CglyZXR1cm4gaXNpbmYocDIzLnopOwp9CmZsb2F0IGxkZXhwX3BvcnRhYmxlKGZsb2F0IHgsIGZsb2F0IHApIAp7CglyZXR1cm4geCAqIGV4cDIocCk7Cn0Kdm9pZCBtYWluKCkgCnsKCS8vIFByaW1pdGl2ZSBQcm9jZXNzb3IgdGVzc2VsbGF0ZV9NaWRkbGVPdXRTaGFkZXIKCWZsb2F0MngyIEFGRklORV9NQVRSSVggPSBmbG9hdDJ4Mih1YWZmaW5lTWF0cml4X1MwLnh5LCB1YWZmaW5lTWF0cml4X1MwLnp3KTsKCWZsb2F0MiBUUkFOU0xBVEUgPSB1dHJhbnNsYXRlX1MwOwoJZmxvYXQgcmVzb2x2ZUxldmVsID0gcmVzb2x2ZUxldmVsX2FuZF9pZHgueDsKCWZsb2F0IGlkeEluUmVzb2x2ZUxldmVsID0gcmVzb2x2ZUxldmVsX2FuZF9pZHgueTsKCWZsb2F0MiBsb2NhbGNvb3JkOwoJaWYgKGlzX3RyaWFuZ3VsYXJfY29uaWNfY3VydmUoKSkgCgl7CgkJbG9jYWxjb29yZCA9IChyZXNvbHZlTGV2ZWwgIT0gMCkgPyBwMDEuenc6IChpZHhJblJlc29sdmVMZXZlbCAhPSAwKSA/IHAyMy54eTogcDAxLnh5OwoJfQoJZWxzZSAKCXsKCQlmbG9hdDIgcDA9cDAxLnh5LCBwMT1wMDEuencsIHAyPXAyMy54eSwgcDM9cDIzLnp3OwoJCWZsb2F0IHcgPSAtMTsKCQlmbG9hdCBtYXhSZXNvbHZlTGV2ZWw7CgkJaWYgKGlzX2NvbmljX2N1cnZlKCkpIAoJCXsKCQkJdyA9IHAzLng7CgkJCW1heFJlc29sdmVMZXZlbCA9IHdhbmdzX2Zvcm11bGFfY29uaWNfbG9nMihQUkVDSVNJT04sIEFGRklORV9NQVRSSVggKiBwMCxBRkZJTkVfTUFUUklYICogcDEsQUZGSU5FX01BVFJJWCAqIHAyLCB3KTsKCQkJcDEgKj0gdzsKCQkJcDMgPSBwMjsKCQl9CgkJZWxzZSAKCQl7CgkJCW1heFJlc29sdmVMZXZlbCA9IHdhbmdzX2Zvcm11bGFfY3ViaWNfbG9nMihQUkVDSVNJT04sIHAwLCBwMSwgcDIsIHAzLEFGRklORV9NQVRSSVgpOwoJCX0KCQlpZiAocmVzb2x2ZUxldmVsID4gbWF4UmVzb2x2ZUxldmVsKSAKCQl7CgkJCWlkeEluUmVzb2x2ZUxldmVsID0gZmxvb3IobGRleHBfcG9ydGFibGUoaWR4SW5SZXNvbHZlTGV2ZWwsbWF4UmVzb2x2ZUxldmVsIC0gcmVzb2x2ZUxldmVsKSk7CgkJCXJlc29sdmVMZXZlbCA9IG1heFJlc29sdmVMZXZlbDsKCQl9CgkJZmxvYXQgZml4ZWRWZXJ0ZXhJRCA9IGZsb29yKC41ICsgbGRleHBfcG9ydGFibGUoaWR4SW5SZXNvbHZlTGV2ZWwsIE1BWF9GSVhFRF9SRVNPTFZFX0xFVkVMIC0gcmVzb2x2ZUxldmVsKSk7CgkJaWYgKDAgPCBmaXhlZFZlcnRleElEICYmIGZpeGVkVmVydGV4SUQgPCBNQVhfRklYRURfU0VHTUVOVFMpIAoJCXsKCQkJZmxvYXQgVCA9IGZpeGVkVmVydGV4SUQgKiAoMSAvIE1BWF9GSVhFRF9TRUdNRU5UUyk7CgkJCWZsb2F0MiBhYiA9IG1peChwMCwgcDEsIFQpOwoJCQlmbG9hdDIgYmMgPSBtaXgocDEsIHAyLCBUKTsKCQkJZmxvYXQyIGNkID0gbWl4KHAyLCBwMywgVCk7CgkJCWZsb2F0MiBhYmMgPSBtaXgoYWIsIGJjLCBUKTsKCQkJZmxvYXQyIGJjZCA9IG1peChiYywgY2QsIFQpOwoJCQlmbG9hdDIgYWJjZCA9IG1peChhYmMsIGJjZCwgVCk7CgkJCWZsb2F0IHUgPSBtaXgoMS4wLCB3LCBUKTsKCQkJZmxvYXQgdiA9IHcgKyAxIC0gdTsKCQkJZmxvYXQgdXYgPSBtaXgodSwgdiwgVCk7CgkJCWxvY2FsY29vcmQgPSAodyA8IDApID9hYmNkOmFiYy91djsKCQl9CgkJZWxzZSAKCQl7CgkJCWxvY2FsY29vcmQgPSAoZml4ZWRWZXJ0ZXhJRCA9PSAwKSA/IHAwLnh5IDogcDMueHk7CgkJfQoJfQoJZmxvYXQyIHZlcnRleHBvcyA9IEFGRklORV9NQVRSSVggKiBsb2NhbGNvb3JkICsgVFJBTlNMQVRFOwoJc2tfUG9zaXRpb24gPSB2ZXJ0ZXhwb3MueHkwMTsKfQoAAAAAhQEAAGxheW91dCAobWV0YWwsIGJpbmRpbmc9MCkgdW5pZm9ybSB1bmlmb3JtQnVmZmVyCnsKCWxheW91dChvZmZzZXQ9MCkgZmxvYXQ0IHNrX1JUQWRqdXN0OwoJbGF5b3V0KG9mZnNldD0xNikgZmxvYXQ0IHVhZmZpbmVNYXRyaXhfUzA7CglsYXlvdXQob2Zmc2V0PTMyKSBmbG9hdDIgdXRyYW5zbGF0ZV9TMDsKCWxheW91dChvZmZzZXQ9NDApIGhhbGY0IHVjb2xvcl9TMDsKfQo7CnZvaWQgbWFpbigpIAp7CgkvLyBTdGFnZSAwLCB0ZXNzZWxsYXRlX01pZGRsZU91dFNoYWRlcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzAgPSB1Y29sb3JfUzA7Cgljb25zdCBoYWxmNCBvdXRwdXRDb3ZlcmFnZV9TMCA9IGhhbGY0KDEpOwoJewoJCS8vIFhmZXIgUHJvY2Vzc29yOiBEaXNhYmxlIENvbG9yCgl9Cn0KAAAAAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAATAAAAAEAAAACAAAAAQAAAB0AAAAAAAAAAQAAAAgAAAACAAAAHwAAAAAAAAACAAAAHwAAABAAAAACAAAAIAAAAEYAAAAAAAAAAAAAAP0AAAA=", "HWQACAAAABAAADAAAIOAAAAADIIAAIRODAAP577774DSAIAA737777YBAAAAAAAAAAAKAAYAAAACAAAAAAACCAYAABDAAAAAAEAAAAH5AAAAAAAAAAAAAAAAADBQCAAAAAAAAAA": "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", "FAIQMYAAMAAAEADAAABAEYAAAICIAB5AABQAAAQAMAAAEATAAABAIIGAAEDCBYQCA4AAAAAAAA5AAAAAAABAAAAACAZAAAAAIYAAAAABAAAAB7IAAAAAAAAAAAAAAAAAYMAQAAAAAAAAA": "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", "HUQACAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAAKAAYAAAACAAAAAAACCAYAABIAAAAAAEAAAAAAAAAAAAAAAAAAAAAAADBQCAAAAAAAAAA": "DAAAAExTS1NOAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGhhbGY0IGNvbG9yOwpsYXlvdXQobG9jYXRpb24gPSAwKSBvdXQgaGFsZjQgdmNvbG9yX1MwOwp2b2lkIG1haW4oKSAKewoJLy8gUHJpbWl0aXZlIFByb2Nlc3NvciBRdWFkUGVyRWRnZUFBR2VvbWV0cnlQcm9jZXNzb3IKCXZjb2xvcl9TMCA9IGNvbG9yOwoJc2tfUG9zaXRpb24gPSBwb3NpdGlvbi54eTAxOwp9CgAAAAAAAH0BAABsYXlvdXQgKG1ldGFsLCBiaW5kaW5nPTApIHVuaWZvcm0gdW5pZm9ybUJ1ZmZlcgp7CglsYXlvdXQob2Zmc2V0PTApIGZsb2F0NCBza19SVEFkanVzdDsKfQo7CmxheW91dChsb2NhdGlvbiA9IDApIGluIGhhbGY0IHZjb2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzA7CglvdXRwdXRDb2xvcl9TMCA9IHZjb2xvcl9TMDsKCWNvbnN0IGhhbGY0IG91dHB1dENvdmVyYWdlX1MwID0gaGFsZjQoMSk7Cgl7CgkJLy8gWGZlciBQcm9jZXNzb3I6IFBvcnRlciBEdWZmCgkJc2tfRnJhZ0NvbG9yID0gb3V0cHV0Q29sb3JfUzAgKiBvdXRwdXRDb3ZlcmFnZV9TMDsKCX0KfQoAAAAAAAAAAQAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAABUAAAAAQAAAAAAAAACAAAAHQAAAAAAAAABAAAACQAAAAgAAAABAAAADAAAAAAAAABQAAAAAQAAAAEAAAAFAAAAAAAAAAEAAAAFAAAAAAAAAAEAAAAAAAAA", "DAQAAAAAAAAAAAAAAJQAAIGAAEACBYQCAGAEFAIBAAAAAABAAAAAAAAAAAAAAOQAAAAAAAQAAAABAMQAAAAFAAAAAAAQAAAA7UAAAAAAAAAAAIAAAAAMGAIAAAAAAAAA": "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", "HUQAAAAAAAMAADAAAIOAAAH677776IZOCAAP577777777777777777YBAAAAAAAAAAABAAIAAAAAAIIDAAAFAAAAAAAQAAAA7UAAAAABAAAAAAAAAAAAEAACAAAAAAAA": "DAAAAExTS1NTAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBpbiBmbG9hdDIgcG9zaXRpb247CmxheW91dChsb2NhdGlvbiA9IDEpIGluIGhhbGY0IGNvbG9yOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IG91dCBoYWxmNCB2Y29sb3JfUzA7CnZvaWQgbWFpbigpIAp7CgkvLyBQcmltaXRpdmUgUHJvY2Vzc29yIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJdmNvbG9yX1MwID0gY29sb3I7Cglza19Qb3NpdGlvbiA9IHBvc2l0aW9uLnh5MDE7Cn0KAAAAAABPAQAAbGF5b3V0IChtZXRhbCwgYmluZGluZz0wKSB1bmlmb3JtIHVuaWZvcm1CdWZmZXIKewoJbGF5b3V0KG9mZnNldD0wKSBmbG9hdDQgc2tfUlRBZGp1c3Q7Cn0KOwpsYXlvdXQobG9jYXRpb24gPSAwKSBmbGF0IGluIGhhbGY0IHZjb2xvcl9TMDsKdm9pZCBtYWluKCkgCnsKCS8vIFN0YWdlIDAsIFF1YWRQZXJFZGdlQUFHZW9tZXRyeVByb2Nlc3NvcgoJaGFsZjQgb3V0cHV0Q29sb3JfUzA7CglvdXRwdXRDb2xvcl9TMCA9IHZjb2xvcl9TMDsKCWNvbnN0IGhhbGY0IG91dHB1dENvdmVyYWdlX1MwID0gaGFsZjQoMSk7Cgl7CgkJLy8gWGZlciBQcm9jZXNzb3I6IERpc2FibGUgQ29sb3IKCX0KfQoAAAAAAAEAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPAAAAAEAAAAAAAAAAgAAAB0AAAAAAAAAAQAAAAkAAAAIAAAAAQAAAAwAAAAAAAAAUAAAAAAAAAAAAAAA/QAAAA==", "CIAAAAAAQAARQAAYQAAAAGFYQAABRAAAAEEAAAAAAARAEAEABYAAAAEAAAAAAAEEBQAAAACQAAAAAAIAAAAP2AAAAAAQAAAACAAAAAGDAEBAAAAAAAAA": "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", "HUIAAAAAAAQAADAAAIOAAAH677777777777QGHAQAD7P7777777777YBAAAAAAAAAAALUASJ3EZYN2AAAAAAAIAEAAAABSCQKL3IYIJ2AAAAAAAIAEAAAABLBCABAAAAABAEGABBAMAAACAAAAAAAOQAAAAAAAQAAAABAMQAAAAAAUAAAAAACAAAAAAAAAAAAAAAAAAAAAAAAAYAAIAAAAAAAA": "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"}}
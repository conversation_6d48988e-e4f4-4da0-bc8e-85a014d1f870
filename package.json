{"name": "curefit-mobile", "version": "1.0.0", "private": true, "scripts": {"start": "npx react-native start --reset-cache", "android": "npx react-native run-android --appId=fit.cure.android.debug", "ios": "npx react-native run-ios", "doctor": "npx react-native doctor", "tunnel": "adb reverse tcp:8081 tcp:8081", "postinstall": "sh ./scripts/post-install.sh", "test": "jest", "lint": "eslint app", "bugsnag:create-build": "bugsnag-cli create-build", "bugsnag:upload-rn-android": "bugsnag-cli upload react-native-android --variant release --api-key=7cb621a57ee6286a4f4a15ece2ede1b5", "bugsnag:upload-android-proguard": "bugsnag-cli upload android-proguard --api-key=7cb621a57ee6286a4f4a15ece2ede1b5 android/", "bugsnag:upload-dsym": "bugsnag-cli upload dsym --api-key=7cb621a57ee6286a4f4a15ece2ede1b5 ios/", "bugsnag:upload-rn-ios": "bugsnag-cli upload react-native-ios --api-key=7cb621a57ee6286a4f4a15ece2ede1b5"}, "dependencies": {"@bugsnag/react-native": "^8.2.0", "@curefit/apps-common": "3.0.0", "@curefit/base-common": "2.53.0", "@curefit/cult-common": "3.9.1", "@curefit/datalake": "1.78.0", "@curefit/diy-common": "4.67.2", "@curefit/eat-common": "7.49.0", "@curefit/intervention-common": "1.8.0", "@curefit/notifee": "5.7.4", "@curefit/product-common": "5.59.0", "@curefit/react-native-cf-contacts": "git+ssh://**************:curefit/RNContactSync.git#abf9e91cd4d036ad972a0249bc96c4987c108bca", "@curefit/react-native-datalake": "git+ssh://**************/curefit/react-native-datalake.git#c78c2bbf3655bf03ceea5521256aafef2f8a92ca", "@curefit/react-native-iap": "git+ssh://**************:curefit/react-native-iap.git#40b966774672465b8cda36e7f5fb7d6ea3cb2a0b", "@curefit/react-native-pose-estimation": "git+ssh://**************:curefit/react-native-pose-estimation.git#fe5e63c6797c8e28c973c468a34b63b0aab433e0", "@curefit/react-native-video": "https://github.com/curefit/react-native-video.git#cfa9a061e601a3d950ce3c794a8f631055ae2eff", "@curefit/ui-common": "0.5.95", "@curefit/ui-cult": "0.2.18", "@curefit/ui-cult-pass": "0.0.4", "@curefit/ui-wellness": "0.0.11", "@curefit/uikit": "git+ssh://**************/curefit/curefit-UIkit.git#3de0a6d77fd2a51b206281fb0b600b6de0276f07", "@curefit/vortex": "0.1.5", "@emotion/core": "^10.0.21", "@emotion/native": "^10.0.14", "@invertase/react-native-apple-authentication": "^1.0.0", "@juspay/blaze-sdk-react-native": "^0.1.0", "@ptomasroos/react-native-multi-slider": "^2.2.2", "@react-native-async-storage/async-storage": "^1.15.8", "@react-native-camera-roll/camera-roll": "^6.0.0", "@react-native-clipboard/clipboard": "^1.14.1", "@react-native-community/blur": "4.4.1", "@react-native-community/cli-platform-android": "^11.3.7", "@react-native-community/cookies": "https://github.com/react-native-cookies/cookies.git#69fe6557b0f2c286e6df6fa9100391ad2d163860", "@react-native-community/datetimepicker": "^3.5.2", "@react-native-community/netinfo": "^6.1.0", "@react-native-community/progress-bar-android": "^1.0.4", "@react-native-community/progress-view": "^1.2.3", "@react-native-firebase/analytics": "^17.5.0", "@react-native-firebase/app": "^17.5.0", "@react-native-firebase/auth": "^17.5.0", "@react-native-firebase/crashlytics": "^17.5.0", "@react-native-firebase/dynamic-links": "^17.5.0", "@react-native-firebase/firestore": "^17.5.0", "@react-native-firebase/messaging": "^17.5.0", "@react-native-firebase/perf": "^17.5.0", "@react-native-firebase/remote-config": "^17.5.0", "@react-native-google-signin/google-signin": "^10.0.1", "@react-native-masked-view/masked-view": "^0.2.6", "@react-native-picker/picker": "2.2.0", "@react-navigation/bottom-tabs": "^5.3.19", "@react-navigation/material-top-tabs": "^5.3.14", "@react-navigation/native": "^5.9.8", "@reduxjs/toolkit": "^1.5.0", "@twotalltotems/react-native-otp-input": "^1.3.11", "babel-plugin-transform-remove-console": "6.9.4", "crypto-js": "^4.2.0", "curefit-react-native-multi-slider": "git+ssh://**************:curefit/react-native-multi-slider#f45b99f87eed6b8287bf99a52f148e0b9005cb43", "d3-interpolate": "1.3.2", "d3-scale": "^2.2.2", "deprecated-react-native-prop-types": "^5.0.0", "emotion-theming": "^10.0.19", "hyper-sdk-react": "3.0.16", "immer": "^2.1.5", "libphonenumber-js": "^1.9.13", "lodash": "4.17.21", "lodash.range": "3.2.0", "lottie-react-native": "6.7.2", "moment": "2.24.0", "moment-range": "4.0.2", "moment-timezone": "^0.5.10", "no-try": "3.0.0", "patch-package": "^6.4.7", "path-to-regexp": "^6.2.0", "prop-types": "^15.7.2", "react": "18.2.0", "react-content-loader": "^4.3.4", "react-countdown": "^2.2.1", "react-freeze": "^1.0.0", "react-native": "0.73.3", "react-native-actionsheet": "2.4.2", "react-native-advertising-id": "^1.0.11", "react-native-android-keyboard-adjust": "https://github.com/curefit/react-native-android-keyboard-adjust.git#d6d4f3ae341c7b73dbf4e013b5c9a6308e1dcbb5", "react-native-animatable": "1.3.3", "react-native-aws3": "0.0.8", "react-native-background-timer": "^2.1.1", "react-native-blob-util": "^0.19.11", "react-native-branch": "^6.2.2", "react-native-calendar-events": "2.2.0", "react-native-camera": "4.2.1", "react-native-chart-kit": "^6.12.0", "react-native-color-matrix-image-filters": "^5.2.2", "react-native-custom-tabs": "https://github.com/curefit/react-native-custom-tabs.git#86b1f7e12892fb0eab88982593681192aac1c2b2", "react-native-device-info": "^8.7.0", "react-native-document-picker": "6.1.0", "react-native-fast-image": "^8.6.3", "react-native-geolocation-service": "^3.1.0", "react-native-gesture-handler": "^1.10.3", "react-native-get-random-values": "^1.7.1", "react-native-gifted-chat": "0.16.3", "react-native-google-fit": "0.18.3", "react-native-hyperlink": "0.0.22", "react-native-image-crop-picker": "0.41.2", "react-native-image-pan-zoom": "^2.1.11", "react-native-image-picker": "^7.0.0", "react-native-image-resizer": "^1.4.5", "react-native-inappbrowser-reborn": "^3.7.0", "react-native-interactions": "0.4.0", "react-native-keep-awake": "4.0.0", "react-native-keyboard-accessory": "^0.1.5", "react-native-keyboard-aware-scroll-view": "0.9.5", "react-native-linear-gradient": "^2.5.6", "react-native-localize": "^1.4.0", "react-native-maps": "1.8.0", "react-native-mime-types": "2.2.1", "react-native-modal": "13.0.1", "react-native-modal-datetime-picker": "https://github.com/curefit/react-native-modal-datetime-picker.git#5123b319333e848d84708f062b0996ed3e232946", "react-native-modal-popover": "2.1.3", "react-native-modalbox": "2.0.2", "react-native-open-settings": "1.0.1", "react-native-orientation": "https://github.com/curefit/react-native-orientation.git#944498c3e5933cb329e07afe69575e6a75de3e28", "react-native-otp-verify": "^1.1.8", "react-native-pager-view": "^6.4.0", "react-native-pdf": "6.7.5", "react-native-permissions": "4.1.5", "react-native-picker": "https://github.com/beefe/react-native-picker.git#6fa89e921c93279e8c0ef6dc871bd13aeddad158", "react-native-picker-select": "^8.0.4", "react-native-progress": "5.0.1", "react-native-pulse": "^1.0.6", "react-native-qrcode-svg": "5.2.0", "react-native-quick-actions": "^0.3.9", "react-native-radial-gradient": "^1.1.5", "react-native-razorpay": "2.3.0", "react-native-restart": "^0.0.20", "react-native-rsa-native": "^2.0.5", "react-native-safari-view": "^2.1.0", "react-native-safe-area-context": "^3.2.0", "react-native-screens": "2.15.2", "react-native-sensors": "^7.3.3", "react-native-share": "7.2.1", "react-native-shimmer": "^0.6.0", "react-native-shimmer-placeholder": "^2.0.9", "react-native-snap-carousel": "^4.0.0-beta.6", "react-native-splash-screen": "^3.3.0", "react-native-status-bar-height": "https://github.com/ovr/react-native-status-bar-height.git#a6f566d7b831d69a5e45f09aef2766d653ce8f43", "react-native-store-review": "0.1.5", "react-native-svg": "15.8.0", "react-native-swipe-gestures": "1.0.4", "react-native-swipe-list-view": "^3.2.5", "react-native-system-setting": "^1.7.4", "react-native-tab-view": "^3.5.2", "react-native-tcp-socket": "^6.0.6", "react-native-toast-message": "^2.3.0", "react-native-url-polyfill": "^1.3.0", "react-native-user-inactivity": "^1.1.0", "react-native-uuid": "1.4.9", "react-native-vector-icons": "^7.0.0", "react-native-view-overflow": "0.0.4", "react-native-view-shot": "^3.1.2", "react-native-watch-connectivity": "^1.0.2", "react-native-webview": "^13.13.1", "react-native-zeroconf": "^0.12.2", "react-query": "^3.16.0", "react-redux": "^7.1.3", "recyclerlistview": "^3.0.5", "redux": "4.0.4", "redux-logger": "3.0.6", "redux-persist": "6.0.0", "redux-thunk": "2.3.0", "reselect": "4.0.0", "rn-apple-healthkit": "0.8.0", "url-parse": "1.5.2", "use-debounce": "^5.2.0", "util": "^0.12.3", "vwo-insights-react-native-sdk": "1.0.3"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/plugin-proposal-decorators": "^7.8.3", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@bugsnag/cli": "^2.9.2", "@react-native-community/eslint-config": "^2.0.0", "@react-native/babel-preset": "0.73.20", "@react-native/eslint-config": "0.73.2", "@react-native/metro-config": "0.73.4", "@react-native/typescript-config": "0.73.1", "@testing-library/jest-native": "^3.4.3", "@testing-library/react-native": "^7.1.0", "@tsconfig/react-native": "^3.0.0", "@types/lodash": "^4.14.144", "@types/metro-config": "^0.76.3", "@types/prop-types": "^15.7.3", "@types/react": "^18.2.6", "@types/react-native": "0.65.15", "@types/react-native-custom-tabs": "^0.1.1", "@types/react-native-modalbox": "^1.4.8", "@types/react-native-orientation": "^5.0.1", "@types/react-native-uuid": "^1.4.0", "@types/react-native-zeroconf": "^0.11.0", "@types/react-redux": "^7.1.4", "@types/react-test-renderer": "^18.0.0", "@types/redux-persist": "^4.3.1", "@types/url-parse": "^1.4.3", "@typescript-eslint/eslint-plugin": "^2.8.0", "@typescript-eslint/parser": "^2.8.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "eslint-config-airbnb": "^18.0.1", "eslint-config-prettier": "^6.7.0", "eslint-plugin-import": "^2.18.2", "eslint-plugin-jest": "^23.0.4", "eslint-plugin-jsx-a11y": "^6.2.3", "eslint-plugin-prettier": "^3.1.1", "eslint-plugin-react": "^7.16.0", "eslint-plugin-react-hooks": "^4.0.2", "eslint-plugin-react-native": "^3.8.1", "jest": "^29.6.3", "jetifier": "^2.0.0", "metro-react-native-babel-preset": "0.76.5", "prettier": "2.8.8", "pretty-quick": "^2.0.1", "react-native-gradle-plugin": "0.71.19", "react-native-svg-transformer": "^1.5.0", "react-native-typescript-transformer": "^1.2.13", "react-test-renderer": "18.2.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}, "resolutions": {"react-native": "0.73.3", "@react-native-async-storage/async-storage": "^1.15.8", "lodash": "^4.17.15", "set-value": "^2.0.1", "mpath": "^0.5.1", "npm": "^6.14.6", "mongoose": "^5.7.5", "acorn": "^7.1.1", "logkitty": "^0.7.1", "querystringify": "^2.0.0", "cryptiles": "^4.1.2", "npm-registry-fetch": "^4.0.5"}, "hyperSdkIOSVersion": "2.1.31", "description": "iOS and Android mobile app in React Native.", "main": "index.js", "repository": "**************:curefit/curefit-mobile.git", "author": "", "license": "MIT"}
# OSX
#
.DS_Store

# Xcode
#
build/
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata/
*.xccheckout
*.moved-aside
DerivedData
Pods/
typings/
*.hmap
*.ipa
*.xcuserstate
# VScode
#
.idea/
.deco
.classpath
.project
.settings

# Android/IJ
#
*.iml
*.hprof
.idea
.gradle
local.properties
crashlytics-build.properties
com_crashlytics_export_strings.xml
android/app/src/main/assets/index.android.*
android/app/release/*
*.bundle.map
android/app/bin/*
android/bin/*

# VSCode
.vscode/.react/

# node.js
#
node_modules/
npm-debug.log
yarn-error.log

# BUCK
buck-out/
\.buckd/
android/keystores/debug.keystore

main.jsbundle
main.jsbundle.meta

# Bundle artifact
*.jsbundle

#script report
scripts/report.txt

# npmrc
.npmrc

# fetched during install
ios/Zoom/MobileRTC.framework/
ios/getsocial-sdk*installer-script*/

gen/

# tests
*/__pycache__/*
appium-screenshot-*
log.html
output.xml
report.html
tests/Library/__pycache__/Utils.cpython-37.pyc
curefit_x/lib/main-dev-ios.dart
ios/.xcode.env.local
ios/.xcode.env

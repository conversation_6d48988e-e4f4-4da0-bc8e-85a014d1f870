#!/bin/bash

# Set up environment for React Native Android development
export JAVA_HOME="/opt/homebrew/opt/openjdk@17"
export PATH="/opt/homebrew/opt/openjdk@17/bin:$PATH"
export PATH="/opt/homebrew/opt/node@18/bin:$PATH"

# Android SDK setup
export ANDROID_HOME=$HOME/Library/Android/sdk
export PATH=$PATH:$ANDROID_HOME/emulator
export PATH=$PATH:$ANDROID_HOME/platform-tools
export PATH=$PATH:$ANDROID_HOME/cmdline-tools/latest/bin

echo "🔧 Environment Setup:"
echo "Java version: $(java -version 2>&1 | head -n 1)"
echo "Node version: $(node --version)"
echo "JAVA_HOME: $JAVA_HOME"
echo "ANDROID_HOME: $ANDROID_HOME"
echo ""

# Check if node_modules exists, if not install dependencies
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    yarn install
fi

echo "🧹 Cleaning Android build..."
cd android
./gradlew clean
cd ..

echo "🚀 Starting Metro bundler..."
npx react-native start --reset-cache &
METRO_PID=$!

echo "Metro bundler started with PID: $METRO_PID"
echo "Waiting 10 seconds for Metro to start..."
sleep 10

echo "🤖 Building and running Android app..."
npx react-native run-android --appId=fit.cure.android.debug

echo "✅ Done! If there were errors, check the logs above."
echo "To stop Metro bundler, run: kill $METRO_PID"

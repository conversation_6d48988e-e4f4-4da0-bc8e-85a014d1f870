/**
 * Integration test for Firebase Instance ID functionality
 * This test runs against the actual Firebase implementation
 */

import { initializeFirebaseInstanceId } from '../app/actions/FetchUserStatus';
import { testFirebaseInstanceId, testApiHeaders } from '../app/actions/FirebaseInstanceIdTest';
import SessionInfo from '../app/apis/SessionInfo';
import ApiHeaders from '../app/apis/ApiHeaders';

describe('Firebase Instance ID Integration Tests', () => {
  beforeEach(() => {
    // Clear SessionInfo before each test
    const sessionInfo = SessionInfo.getInstance();
    sessionInfo.firebaseInstanceId = null;
    sessionInfo.devHeaders = {};
  });

  it('should initialize Firebase instance ID and make it available in API headers', async () => {
    // Test the initialization process
    const firebaseInstanceId = await initializeFirebaseInstanceId();
    
    // Verify Firebase instance ID was obtained
    expect(firebaseInstanceId).toBeTruthy();
    expect(typeof firebaseInstanceId).toBe('string');
    
    // Verify it's stored in SessionInfo
    const sessionInfo = SessionInfo.getInstance();
    expect(sessionInfo.firebaseInstanceId).toBe(firebaseInstanceId);
    
    // Verify it's included in API headers
    const headers = ApiHeaders.SessionHeaders();
    expect(headers.firebaseInstanceId).toBe(firebaseInstanceId);
  });

  it('should handle Firebase instance ID in session headers', async () => {
    // Initialize Firebase instance ID
    await initializeFirebaseInstanceId();
    
    // Test that our test functions work with real Firebase
    const firebaseTest = await testFirebaseInstanceId();
    expect(firebaseTest.success).toBe(true);
    expect(firebaseTest.directAppInstanceId).toBeTruthy();
    
    const headersTest = testApiHeaders();
    expect(headersTest.success).toBe(true);
    expect(headersTest.firebaseInstanceIdInHeaders).toBeTruthy();
  });

  it('should set Firebase instance ID using ApiHeaders.setFirebaseInstanceId', () => {
    const testFirebaseId = 'test-firebase-instance-id-12345';
    
    // Use the static method to set Firebase instance ID
    ApiHeaders.setFirebaseInstanceId(testFirebaseId);
    
    // Verify it's stored in SessionInfo
    const sessionInfo = SessionInfo.getInstance();
    expect(sessionInfo.firebaseInstanceId).toBe(testFirebaseId);
    
    // Verify it's in devHeaders for backward compatibility
    expect(sessionInfo.devHeaders.firebaseInstanceId).toBe(testFirebaseId);
    
    // Verify it's included in session headers
    const headers = ApiHeaders.SessionHeaders();
    expect(headers.firebaseInstanceId).toBe(testFirebaseId);
  });

  it('should include Firebase instance ID in both base and session headers', () => {
    const testFirebaseId = 'test-firebase-instance-id-67890';
    
    // Set Firebase instance ID
    ApiHeaders.setFirebaseInstanceId(testFirebaseId);
    
    // Test SessionHeaders
    const sessionHeaders = ApiHeaders.SessionHeaders();
    expect(sessionHeaders.firebaseInstanceId).toBe(testFirebaseId);
    
    // Test SessionHeaders with additional headers
    const additionalHeaders = { customHeader: 'customValue' };
    const combinedHeaders = ApiHeaders.SessionHeaders(additionalHeaders);
    expect(combinedHeaders.firebaseInstanceId).toBe(testFirebaseId);
    expect(combinedHeaders.customHeader).toBe('customValue');
  });
});

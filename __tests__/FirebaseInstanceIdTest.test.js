import { testFirebaseInstanceId, testApiHeaders, runAllFirebaseTests } from '../app/actions/FirebaseInstanceIdTest';
import SessionInfo from '../app/apis/SessionInfo';

// Mock Firebase
jest.mock('@react-native-firebase/app', () => ({
  app: jest.fn(() => ({
    appInstanceId: Promise.resolve('mock-firebase-instance-id-12345')
  }))
}));

// Mock SessionInfo
jest.mock('../app/apis/SessionInfo');

// Mock ApiHeaders
jest.mock('../app/apis/ApiHeaders', () => ({
  getSessionHeaders: jest.fn(() => ({
    st: 'mock-session-token',
    at: 'mock-auth-token',
    firebaseInstanceId: 'mock-firebase-instance-id-12345'
  }))
}));

describe('Firebase Instance ID Tests', () => {
  let mockSessionInfo;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Mock SessionInfo instance
    mockSessionInfo = {
      firebaseInstanceId: null,
      devHeaders: {}
    };
    
    SessionInfo.getInstance = jest.fn(() => mockSessionInfo);
  });

  describe('testFirebaseInstanceId', () => {
    it('should successfully get Firebase instance ID and store it in SessionInfo', async () => {
      const result = await testFirebaseInstanceId();
      
      expect(result.success).toBe(true);
      expect(result.directAppInstanceId).toBe('mock-firebase-instance-id-12345');
      expect(result.sessionInfoFirebaseInstanceId).toBe('mock-firebase-instance-id-12345');
      expect(mockSessionInfo.firebaseInstanceId).toBe('mock-firebase-instance-id-12345');
    });

    it('should handle Firebase errors gracefully', async () => {
      // Mock Firebase to throw an error
      const firebase = require('@react-native-firebase/app');
      firebase.app.mockImplementation(() => ({
        appInstanceId: Promise.reject(new Error('Firebase not initialized'))
      }));

      const result = await testFirebaseInstanceId();
      
      expect(result.success).toBe(false);
      expect(result.error).toBe('Firebase not initialized');
    });
  });

  describe('testApiHeaders', () => {
    it('should include Firebase instance ID in API headers', () => {
      // Set up SessionInfo with Firebase instance ID
      mockSessionInfo.firebaseInstanceId = 'test-firebase-id';
      
      const result = testApiHeaders();
      
      expect(result.success).toBe(true);
      expect(result.firebaseInstanceIdInHeaders).toBe('mock-firebase-instance-id-12345');
      expect(result.headers).toHaveProperty('firebaseInstanceId');
    });

    it('should handle missing Firebase instance ID', () => {
      // SessionInfo without Firebase instance ID
      mockSessionInfo.firebaseInstanceId = null;
      
      const { getSessionHeaders } = require('../app/apis/ApiHeaders');
      getSessionHeaders.mockReturnValue({
        st: 'mock-session-token',
        at: 'mock-auth-token'
        // No firebaseInstanceId
      });
      
      const result = testApiHeaders();
      
      expect(result.success).toBe(true);
      expect(result.firebaseInstanceIdInHeaders).toBeUndefined();
    });
  });

  describe('runAllFirebaseTests', () => {
    it('should run all tests and return combined results', async () => {
      const result = await runAllFirebaseTests();
      
      expect(result).toHaveProperty('firebaseInstanceIdTest');
      expect(result).toHaveProperty('apiHeadersTest');
      expect(result).toHaveProperty('timestamp');
      expect(result.firebaseInstanceIdTest.success).toBe(true);
      expect(result.apiHeadersTest.success).toBe(true);
    });
  });
});

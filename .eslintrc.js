/**
 * Top or root level ESLint configurations
 * Don't be tempted to move this to JSON format as `.eslintrc.json` or an RC file as `.eslintrc`
 * This file being in JS, would benefit from Prettier formatting
 * And we can add detailed comments to explain our rationale behind each decisions
 */
module.exports = {
  /**
   * Specifies the ESLint Parser
   * We want to Lint both JS and TS files
   * Hence we are using TS ESLint Parser
   */
  parser: "@typescript-eslint/parser",
  plugins: [
    "@typescript-eslint/eslint-plugin",
    "react",
    "react-native",
    "react-hooks",
    "jest",
    "prettier",
  ],
  env: {
    /**
     * ES6 and future EcmaScript versions are enabled by default
     */
    es6: true,
    /**
     * We want RN specific global variables like __DEV__ to be accepted as well
     */
    "react-native/react-native": true,
  },
  extends: [
    /**
     * Generally speaking, there are three popular ESLint standards - Airbnb, Google, Standard
     * In React community, Airbnb has been prevalent for more than 3 years now
     * Airbnb linting rules also has open-source explanation for each rule, as well as great community support
     * For now, we would stick to Airbnb recommendations
     */
    "@react-native-community",
    "airbnb",
    "plugin:@typescript-eslint/recommended",
    "plugin:import/typescript",
    "plugin:prettier/recommended",
    "prettier/@typescript-eslint",
  ],
  parserOptions: {
    ecmaVersion: 2018, // Allows for the parsing of modern ECMAScript features
    sourceType: "module", // Allows for the use of imports
    ecmaFeatures: {
      jsx: true, // Allows for the parsing of JSX
    },
    tsconfigRootDir: ".",
  },
  rules: {
    // Place to specify ESLint rules. Can be used to overwrite rules specified from the extended configs
    /**
     * Since ours is a mixed JS and TS codebase, we are enabling return type check only for TS / TSX files
     * Rule is turned off by default
     * Overriden below (check "overrides" section)
     * This is as per official TS-ESLint recommendations
     * https://github.com/typescript-eslint/typescript-eslint/blob/master/packages/eslint-plugin/docs/rules/explicit-function-return-type.md#configuring-in-a-mixed-jsts-codebase
     *
     * Actually, this became a nuisance, so we removed the override for TypeScript files
     */
    "@typescript-eslint/explicit-function-return-type": "off",
    /**
     * This is enabled to allow using style objects
     * before defining them at the bottom with StyleSheet.create()
     * disable the rule for variables, but enable it for functions and classes
     */
    "@typescript-eslint/no-use-before-define": [
      "error",
      { functions: true, classes: true, variables: false },
    ],
    /**
     * We don't want ESLint to raise errors where these extensions are not specified
     * TypeScript is to be treated as first-class cititzens, and compatible with VSCode intellisense
     */
    "import/extensions": [
      "error",
      "ignorePackages",
      {
        js: "never",
        mjs: "never",
        jsx: "never",
        ts: "never",
        tsx: "never",
      },
    ],
    /**
     * File paths should be resolved properly
     * For image file extensions, this doesn't work, so we can suppress those errors
     */
    "import/no-unresolved": [2, { ignore: ["\\.(png|jpg|svg)\\?(lqip)$"] }],
    /**
     * Lot of exports in our codebase is not default export
     * So for the time being, we can turn it off
     */
    "import/prefer-default-export": "off",
    /**
     * We use require heavily to import assets
     */
    "global-require": 0,
    /**
     * Plenty of functions / class properties carry underscore in the beginning of their name
     * Hence it is turned off for now
     */
    "no-underscore-dangle": 0,
    /**
     * We want to disable this rule for immer
     * Because immer uses JS Proxies under the hood to intercept assignments
     * And perform a structurally-shared update
     * So it would allow assignments to "draft"
     */
    "no-param-reassign": [
      "error",
      { props: true, ignorePropertyModificationsFor: ["draft"] },
    ],
    /**
     * All style objects must be used
     */
    "react-native/no-unused-styles": 2,
    /**
     * https://github.com/Intellicode/eslint-plugin-react-native/blob/master/docs/rules/split-platform-components.md
     */
    "react-native/split-platform-components": 2,
    /**
     * Don't allow using inline styles in RN view JSX
     */
    "react-native/no-inline-styles": 2,
    /**
     * No color hardcoding in style objects
     */
    "react-native/no-color-literals": 2,
    /**
     * Don't allow style arrays unless there are more than one elements
     */
    "react-native/no-single-element-style-arrays": 2,
    /**
     * We want to include CFText as a valid raw text container
     */
    "react-native/no-raw-text": ["error", { skip: ["CFText"] }],
    /**
     * We want to gradually move to TypeScript
     * So, we'd prefer file names be `.tsx` whenever possible, for React components
     * But at the same time, we're keeping it as warning instead of error for now
     */
    "react/jsx-filename-extension": [
      "warn",
      {
        extensions: [".tsx", ".js"],
      },
    ],
    /**
     * We don't need prop type validation
     * Since we'll use React.FC<P> and validate prop types in a more robust manner through TypeScript
     */
    "react/prop-types": "off",
    /**
     * We want rule of hooks, as defined in React documentation, to be enforced
     * This rule throws error in development time,
     * and informs developer that they have potentially violated a rule of hooks
     * Rule of hooks: https://reactjs.org/docs/hooks-rules.html
     */
    "react-hooks/rules-of-hooks": "error",
    "react-hooks/exhaustive-deps": "warn",
    /**
     * We are enforcing Prettier, the AST based formatting tool, through ESLint
     * A formatting violation would be considered an ESLint violation
     * If someone has Prettier plugin installed in their IDE / editor, with auto-formatting on
     * They don't need to worry about this
     * We would also enforce this through Git pre-commit hook
     * However, CI Build would fail if someone has bypassed both these checks
     * Effectively, this makes it near impossible to check in code to main branches which isn't consistently formatted
     */
    "prettier/prettier": ["error"],
    /**
     * We allow prop spreading because we are already using TypeScript to validate the types
     * We also wrap over common Native elements, like Button / Image etc.
     * It's important that consumer of these components be able to pass any prop that's specific to that component
     * But not touched by the wrapper itself
     */
    "react/jsx-props-no-spreading": "off",
    "no-console": "off",
    "react/state-in-constructor": "off",
    "react/static-property-placement": "off",
    "no-unused-expressions": "off",
    "@typescript-eslint/no-unused-expressions": "error",
    "class-methods-use-this": "off",
    "react/jsx-uses-react": "off",
    "react/react-in-jsx-scope": "off",
    "react/require-default-props": [
      1,
      { forbidDefaultForRequired: false, ignoreFunctionalComponents: true },
    ],
  },
  settings: {
    react: {
      /**
       * Tells eslint-plugin-react to automatically detect the version of React to use
       */
      version: "detect",
    },
  },
};

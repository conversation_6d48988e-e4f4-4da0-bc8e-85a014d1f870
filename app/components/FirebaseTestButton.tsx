import React from 'react';
import { TouchableOpacity, Text, StyleSheet, Alert } from 'react-native';
import { runAllFirebaseTests } from '../actions/FirebaseInstanceIdTest';

interface FirebaseTestButtonProps {
  style?: any;
}

const FirebaseTestButton: React.FC<FirebaseTestButtonProps> = ({ style }) => {
  const handleTestPress = async () => {
    try {
      console.log('🔥 Running Firebase Instance ID Tests...');
      const results = await runAllFirebaseTests();
      
      // Show results in an alert
      const message = `
Firebase Instance ID Test: ${results.firebaseInstanceIdTest.success ? '✅ PASS' : '❌ FAIL'}
API Headers Test: ${results.apiHeadersTest.success ? '✅ PASS' : '❌ FAIL'}

Check console for detailed logs.
      `;
      
      Alert.alert('Firebase Test Results', message.trim());
    } catch (error) {
      console.error('Error running Firebase tests:', error);
      Alert.alert('Test Error', `Failed to run tests: ${error.message}`);
    }
  };

  return (
    <TouchableOpacity style={[styles.button, style]} onPress={handleTestPress}>
      <Text style={styles.buttonText}>🔥 Test Firebase Instance ID</Text>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  button: {
    backgroundColor: '#FF6B35',
    padding: 12,
    borderRadius: 8,
    margin: 10,
    alignItems: 'center',
  },
  buttonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default FirebaseTestButton;

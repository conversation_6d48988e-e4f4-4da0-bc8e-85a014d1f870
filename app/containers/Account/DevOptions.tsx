import React from "react";
import { StyleSheet } from "react-native";
import { Box } from "@curefit/uikit";
import { KeyboardAwareFlatList } from "react-native-keyboard-aware-scroll-view";
import _ from "lodash";
import { useDispatch, useSelector } from "react-redux";
import { RouteProp } from "@react-navigation/native";
import { NativeStackNavigationProp } from "react-native-screens/native-stack";
import { getNavigationRoute } from "../../lib/navigation/NavigationService";
import AccountCell from "../../components/Account/AccountCell";
import PlatformTouchable from "../../components/PlatformTouchable";
import { getTestIdValue, VM_WIDGETS_SIDE_SPACING } from "../../lib/constants";
import { sendPageClickEvent } from "../../actions/Analytics/PageEvents";
import { triggerWidgetAction } from "../../lib/WidgetActionHandler";
import { APP_MODES, MODES } from "../../lib/appModes";
import {
  toggleCodePush,
  toggleDevMode,
  updateDevCityId,
} from "../../actions/DevModal";
import { getUserStatus } from "../../actions/FetchUserStatus";
import {
  showInspector,
  showThemeToggleOption,
} from "../../actions/AppStateActions";

type navigationProps = {
  route: RouteProp<any, any>;
  navigation: NativeStackNavigationProp<any>;
};

const DevOptions = (props: navigationProps) => {
  const { params } = getNavigationRoute(props);
  const dispatch = useDispatch();
  const { devOptions } = params;
  const appState = useSelector((store: any) => {
    return store.appState;
  });

  const rowClicked = (cellData: { title: any; action: any }) => {
    sendPageClickEvent({
      buttonType: "Menu",
      actionText: cellData.title,
      actionTarget: _.get(cellData, "action.url"),
    });
    dispatch(triggerWidgetAction(cellData.action));
  };

  const renderRow = ({ item, index }: { item: any; index: any }) => {
    const {
      devMode = {},
      customURL = APP_MODES[MODES.CUSTOM].url,
      devCityId,
    } = appState;
    let cellData = item;
    if (item.type === "SERVER_SWITCH") {
      cellData = Object.keys(APP_MODES).map(mode => ({
        title: APP_MODES[mode].title,
        toggleState: devMode.mode === APP_MODES[mode].mode,
        toggleAction: data => {
          dispatch(toggleDevMode(APP_MODES[mode].mode, data));
        },
        editable: APP_MODES[mode].editable,
        placeholder:
          customURL && customURL.length ? customURL : APP_MODES[mode].url,
        isVirtualClusterPicker:
          devMode.mode === MODES.VIRTUAL_CLUSTER &&
          mode === MODES.VIRTUAL_CLUSTER,
      }));
    } else if (item.type === "INTERNAL_USER_SWITCH") {
      const isDf =
        appState.codePushDf === undefined || appState.codePushDf === true;
      cellData = {
        title: isDf ? "Internal build" : "External build",
        toggleState: true,
        toggleAction: () => dispatch(toggleCodePush),
      };
    } else if (item.type === "COUNTRY_SWITCH" && item.meta.length) {
      cellData = {
        title: item.title,
        isSelect: true,
        items: item.meta.map((city: { cityName: any; cityId: any }) => ({
          label: city.cityName,
          value: city.cityId,
        })),
        value: devCityId,
        onValueChange: (value: any) => {
          dispatch(updateDevCityId(value));
          dispatch(getUserStatus());
        },
      };
    } else if (item.type === "APP_INSPECTOR_SWITCH") {
      let toggleState = appState.showInspector;
      if (toggleState === undefined) {
        toggleState = false;
      }
      cellData = {
        title: item.title,
        toggleState,
        toggleAction: () => dispatch(showInspector(toggleState)),
      };
    } else if (item.type === "THEME_SWITCH") {
      let toggleState = appState.themeSwitchEnabled;
      if (toggleState === undefined) {
        toggleState = false;
      }
      cellData = {
        title: item.title,
        toggleState,
        toggleAction: () => dispatch(showThemeToggleOption(toggleState)),
      };
    }
    const testID = getTestIdValue(index);
    return (
      <PlatformTouchable
        testID={`ACCOUNT_VIEW_ROW_${testID}`}
        accessibilityLabel={`ACCOUNT_VIEW_ROW_${testID}`}
        onPress={() => rowClicked(cellData)}
      >
        <Box>
          {cellData && cellData.length ? (
            cellData.map((row: any) => <AccountCell {...row} />)
          ) : (
            <AccountCell {...cellData} />
          )}
        </Box>
      </PlatformTouchable>
    );
  };

  return (
    <KeyboardAwareFlatList
      data={devOptions}
      renderItem={renderRow}
      keyExtractor={(item, index) => `${index}-${item.type}`}
      extraScrollHeight={100}
      style={styles.scene}
    />
  );
};

export default DevOptions;

const styles = StyleSheet.create({
  scene: { paddingHorizontal: VM_WIDGETS_SIDE_SPACING },
});

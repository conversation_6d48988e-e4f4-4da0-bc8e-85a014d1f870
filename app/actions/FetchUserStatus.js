import { batch } from "react-redux";
import _ from "lodash";
import { ReactNativeDatalake } from "@curefit/react-native-datalake";
import branch from "react-native-branch";
// eslint-disable-next-line import/no-cycle
import NotificationUtil from "../lib/NotificationUtil";
// eslint-disable-next-line import/no-cycle
import { showIntervention } from "./Intervention";
// eslint-disable-next-line import/no-cycle
import { showChangeCity, setUpdatedCity, showAreaSelector } from "./Location";
import { setAppLayout } from "./AppLayout";
import { addAnnouncementInQueue } from "./AnnouncementActions";
import { setContactsSyncState } from "./ContactsSync";
import { trainerLedBottomTabs, PageTypes } from "../lib/constants";
import CFAnalytics from "../lib/CFAnalytics";
// eslint-disable-next-line import/no-cycle
import API from "../apis/Api";
import LocalStorage from "../lib/LocalStorage";
import * as types from "./types";
import SessionInfo from "../apis/SessionInfo";
// eslint-disable-next-line import/no-cycle
import { setUserProfileInfo } from "./UserProfile";
import { setAppTabs } from "./AppTabs";
import { triggerWidgetAction } from "../lib/WidgetActionHandler";
// eslint-disable-next-line import/no-cycle
import {
  updateAppStartViaDeeplink,
  updateParq,
  updateCreateProfilePending,
} from "./AppStateActions";
import { convertUrlToAppAction } from "../lib/CurefitUtils";
import * as NavigationService from "../lib/navigation/NavigationService";
import { sendScreenMessage } from "../lib/flutter/FlutterUtil";
import { sendWidgetViewEvent } from "./Analytics/WidgetEvents";
import firebase from "@react-native-firebase/app";

let selectIndexFlag = false;
let homeThemeFlag = false;

// Function to get Firebase instance ID
async function getFirebaseInstanceId() {
  try {
    const appInstanceId = await firebase.app().appInstanceId;
    return appInstanceId;
  } catch (error) {
    console.log('Error getting Firebase App Instance ID:', error);
    return null;
  }
}

function convertToVerticalsFormat(appTabs) {
  return appTabs.map(tabId => ({
    verticalType: tabId,
    displayText: tabId,
    pageId: trainerLedBottomTabs[tabId].pageId,
  }));
}

function setUserStatus(response) {
  return { type: types.SET_USER_STATUS, response };
}

export function setShowFlutterSupportSection(response) {
  return { type: types.SET_SHOW_FLUTTER_SUPPORT_SECTION, payload: response };
}

function updateSessionInfoFromUserStatus(response) {
  const sessionInfo = SessionInfo.getInstance();
  sessionInfo.cityId = response.cityId;
  sessionInfo.countryId = response.countryId;
}

const debouncedGetUserStatus = _.debounce(async (dispatch, getState, additionalHeaders, requestBody = {}) => {
  console.log("samarth 1")
  let body = CFAnalytics.insertAdvertisingIdentifier(requestBody);
  const branchParams = CFAnalytics.branchLatestParams
    ? CFAnalytics.branchLatestParams
    : await CFAnalytics.getBranchLatestReferringParams();
  const {
    appState: { devCityId },
    deepLinkReducer: { appStartedByDeepLink },
  } = getState();
  if (devCityId) {
    body.cityId = devCityId;
  }
  
  // Add Firebase instance ID to request body
  const sessionInfo = SessionInfo.getInstance();
  if (sessionInfo.firebaseInstanceId) {
    body.firebaseInstanceId = sessionInfo.firebaseInstanceId;
  } else {
    // Try to get Firebase instance ID if not already set
    try {
      const firebaseInstanceId = await getFirebaseInstanceId();
      if (firebaseInstanceId) {
        body.firebaseInstanceId = firebaseInstanceId;
        // Also set it in SessionInfo for future use
        sessionInfo.firebaseInstanceId = firebaseInstanceId;
      }
    } catch (error) {
      console.log('Error getting Firebase instance ID for user status:', error);
    }
  }
  
  body = { ...body, branchParams };
  LocalStorage.getValueFromPreference("SESSION_TOKEN", sessionToken => {
    LocalStorage.getValueFromPreference("ACCESS_TOKEN", accessToken => {
      console.log("samarth 2")
      if (sessionToken && accessToken) {
        const sessionInfo = SessionInfo.getInstance();
        sessionInfo.at = accessToken;
        sessionInfo.st = sessionToken;
      }
      if (accessToken == null) {
        return
      }
      console.log("samarth 3")
      console.log("User Status API Request Body:", body);
      return API.post(
        "/user/status",
        { ...API.headers.SessionHeaders(), ...additionalHeaders },
        body
      )
        .then(response => {
          const { mediaPlayerState, appRunStatus } = getState();
          if (!_.isNil(response.showFlutterSupportSection)) {
            dispatch(
              setShowFlutterSupportSection(response.showFlutterSupportSection)
            );
          }
          if (appRunStatus !== "active") return;
          const { isPlayerActive } = mediaPlayerState;
          batch(() => {
            response.displayChatbot = response.displayChatbot ?? false;
            updateSessionInfoFromUserStatus(response);
            dispatch(setUserStatus(response));
            if (!_.isEmpty(response.appLayout)) {
              dispatch(setAppLayout(response.appLayout));
            }
            if (response && response.user) {
              CFAnalytics.isInternalUser = response.user.isInternalUser;
              ReactNativeDatalake.setUser(response.user.userId, {
                email: response.user.email,
              });
              CFAnalytics.setBugsnagUser(response.user);
            }
            if (response.verticals) {
              dispatch(
                setAppTabs(response.verticals, response.isMoreToBeHighlighted)
              );
            }
            dispatch(setUserProfileInfo(response, true));

            if (response.intervention && !isPlayerActive) {
              dispatch(showIntervention(response));
            }
            if (response.parqNeeded !== undefined) {
              dispatch(updateParq(response.parqNeeded));
            }
            if (response.createProfilePending !== undefined) {
              dispatch(updateCreateProfilePending(response.createProfilePending));
            }
            if (response.showCitySelection) {
              const showDarkThemeModal =
                NavigationService.getNavigationRoute().name === PageTypes.AppTabs;
              if (!isPlayerActive) {
                dispatch(
                  showChangeCity(
                    true,
                    true,
                    false,
                    showDarkThemeModal ? "DARK" : "LIGHT"
                  )
                );
                sendWidgetViewEvent({
                  widgetType: "CITY_MODAL",
                  extraParams: {
                    showCitySelection: true,
                    city: response.cityId,
                  },
                });
              }
            } else if (response.cityId && response.cityName) {
              // update city selection on login
              dispatch(
                setUpdatedCity({
                  cityId: response.cityId,
                  name: response.cityName,
                  countryId: response.countryId,
                })
              );
            }
            if (response.areaSelectionData && response.cityId) {
              const {
                showAreaSelection = false,
                dismissAction,
              } = response.areaSelectionData;

              if (showAreaSelection) {
                const showDarkThemeModal =
                  NavigationService.getNavigationRoute().name ===
                  PageTypes.AppTabs;
                if (!isPlayerActive) {
                  dispatch(
                    showAreaSelector(
                      { cityId: response.cityId, userId: response.user.userId },
                      true,
                      true,
                      showDarkThemeModal ? "DARK" : "LIGHT",
                      null,
                      null,
                      () => {
                        if (dismissAction) {
                          dispatch(triggerWidgetAction(dismissAction));
                        }
                      }
                    )
                  );
                }
              }
            }
          });

          if (response.diySubscriptions) {
            // ask notification manager to schedule notifications
            NotificationUtil.scheduleNotificationsIfNotScheduled(
              response.diySubscriptions
            );
          }

          if (response.cityName) {
            CFAnalytics.setUserCityName(response.cityName);
          }

          if (response.detectedCity && response.detectedCity.city) {
            CFAnalytics.setDetectedCityName(response.detectedCity.city);
          }
          // triggering redirect action if present
          // in that case, don't show announcements.
          // prioritize redirection over announcements. Announcement can be shown later.
          // Remove the else block since it's a temporary fallback
          // else logic has already been moved to cfapi java
          if (response.branchRedirectAction) {
            LocalStorage.getValueForKey("first_open_branch", result => {
              if (!result && !appStartedByDeepLink) {
                const updatedRedirectAction = {
                  ...response.branchRedirectAction,
                  meta: {
                    ...response.branchRedirectAction.meta,
                    viaDeeplink: true,
                  },
                };
                dispatch(triggerWidgetAction(updatedRedirectAction));
                dispatch(updateAppStartViaDeeplink(true));
                CFAnalytics.logEvent("app_deeplink_action", {
                  sourceType: "branch",
                  utm_campaign: branchParams["~campaign"], // eslint-disable-line @typescript-eslint/camelcase
                  marketing_title: branchParams.$marketing_title, // eslint-disable-line @typescript-eslint/camelcase
                  url: response.branchRedirectAction.url,
                });
                LocalStorage.storeValue("first_open_branch", "true");
              } else if (
                !appStartedByDeepLink &&
                response.announcementData &&
                !isPlayerActive
              ) {
                addAnnouncementInQueue(response.announcementData);
              }
            });
          } else {
            LocalStorage.getValueForKey("first_open_branch", (result) => {
              if (!result && !appStartedByDeepLink) {
                CFAnalytics.getBranchLatestReferringParams().then(
                  (firstInstallParams) => {
                    if (firstInstallParams) {
                      const campaignName = firstInstallParams["~campaign"];
                      let deeplinkUrl;
                      if (firstInstallParams.$deeplink_path) {
                        deeplinkUrl = firstInstallParams.$deeplink_path;
                      } else if (campaignName) {
                        const urls = campaignName.match(/\bcurefit?:\/\/\S+/gi);
                        const campaignCheck = campaignName.toLowerCase();
                        if (campaignCheck.includes("live_pt")) {
                          deeplinkUrl =
                            "curefit://cultfitclp?selectedTab=LivePT&pageId=cult";
                        } else if (campaignCheck.includes("transform")) {
                          deeplinkUrl = "curefit://tf_clp";
                        } else if (campaignCheck.includes("unified")) {
                          deeplinkUrl =
                            "curefit://listpage?pageId=fitnesshub_nux";
                        } else if (
                          campaignCheck.includes("black") ||
                          campaignCheck.includes("elite")
                        ) {
                          deeplinkUrl =
                            "curefit://listpage?pageId=CultPassBlackSKU";
                        } else if (campaignCheck.includes("live")) {
                          deeplinkUrl =
                            "curefit://listpage?pageId=CultPassLiveSKU";
                        } else if (
                          campaignCheck.includes("gold") ||
                          campaignCheck.includes("pro")
                        ) {
                          deeplinkUrl =
                            "curefit://listpage?pageId=CultPassGoldSKU";
                        } else if (Array.isArray(urls) && urls.length > 0) {
                          // eslint-disable-next-line prefer-destructuring
                          deeplinkUrl = urls[0];
                        }
                      }
                      if (deeplinkUrl) {
                        dispatch(
                          triggerWidgetAction(convertUrlToAppAction(deeplinkUrl))
                        );
                        dispatch(updateAppStartViaDeeplink(true));
                        CFAnalytics.logEvent("app_deeplink_action", {
                          sourceType: "branch",
                          utm_campaign: campaignName, // eslint-disable-line @typescript-eslint/camelcase
                          marketing_title: firstInstallParams.$marketing_title, // eslint-disable-line @typescript-eslint/camelcase
                          url: firstInstallParams.$deeplink_path,
                        });
                      } else {
                        // No deeplink url found in branchd data
                        CFAnalytics.logEvent("app_error", {
                          errorType: "branch_deeplink_url_not_found",
                          sourceType: "branch",
                            utm_campaign: campaignName, // eslint-disable-line @typescript-eslint/camelcase
                            marketing_title: firstInstallParams.$marketing_title, // eslint-disable-line @typescript-eslint/camelcase
                            url: "EMPTY",
                          });
                          if (
                            !appStartedByDeepLink &&
                            response.announcementData &&
                            !isPlayerActive
                          ) {
                            addAnnouncementInQueue(response.announcementData);
                          }
                        }
                      } else {
                        // No branch data
                        CFAnalytics.logEvent("app_error", {
                          sourceType: "branch_params_empty",
                          utm_campaign: "EMPTY", // eslint-disable-line @typescript-eslint/camelcase
                          marketing_title: "EMPTY", // eslint-disable-line @typescript-eslint/camelcase
                          url: "EMPTY",
                        });
                        if (
                          !appStartedByDeepLink &&
                          response.announcementData &&
                          !isPlayerActive
                        ) {
                          addAnnouncementInQueue(response.announcementData);
                        }
                      }
                      LocalStorage.storeValue("first_open_branch", "true");
                    }
                  );
                } else if (
                  !appStartedByDeepLink &&
                  response.announcementData &&
                  !isPlayerActive
                ) {
                  addAnnouncementInQueue(response.announcementData);
                }
              });
            }

            if (response.datalakeConfig) {
              ReactNativeDatalake.configure(response.datalakeConfig);
            }
            if (response.contactSyncConfig) {
              dispatch(setContactsSyncState(response.contactSyncConfig));
            }
            if (response.showcfsform === true && response.formAction) {
              dispatch(triggerWidgetAction(response.formAction));
            }
            if (response.flutterAction) {
              sendScreenMessage({
                action: response.flutterAction,
                key: "apptabs",
              });
            }

            if (!homeThemeFlag && response.homeTheme !== undefined) {
              homeThemeFlag = true;
              sendScreenMessage({
                homeTheme: response.homeTheme,
                key: "apptabs",
              });
            }

            if (!selectIndexFlag) {
              selectIndexFlag = true;
              if (
                response.selectedAppTabId !== undefined &&
                response.tabPageSelectedPageId !== undefined
              ) {
                sendScreenMessage({
                  selectTab: response.selectedAppTabId,
                  params: {
                    queryParams: {
                      selectedTab: response.tabPageSelectedPageId,
                    },
                  },
                  key: "apptabs",
                });
              } else {
                sendScreenMessage({
                  selectIndex: response.tabScreenSelectedIndex ?? null,
                  key: "apptabs",
                });
              }
            }
            if (
              response.showRegisterDeviceForm === true &&
              response.deviceRegistrationFormAction
            ) {
              dispatch(triggerWidgetAction(response.deviceRegistrationFormAction));
            }
          })
          .catch(ex => {
            if (__DEV__) {
              console.warn(ex);
            }
          });
      });
    });
}, 1000)

export function getUserStatus(additionalHeaders, requestBody = {}) {
  return (dispatch, getState) => {
    debouncedGetUserStatus(dispatch, getState, additionalHeaders, requestBody);
  };
}

export function updateSelectedCity(city, onCityUpdated, isAreaSelector) {
  return (dispatch, getState) => {
    return API.post(
      "/v2/user/updateCityPreference",
      API.headers.SessionHeaders(),
      { cityId: city.cityId }
    )
      .then(response => {
        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
        !isAreaSelector && dispatch(setUpdatedCity(city));
        // eslint-disable-next-line @typescript-eslint/no-unused-expressions
        onCityUpdated && onCityUpdated();
        if (response.verticals) {
          dispatch(
            setAppTabs(response.verticals, response.isMoreToBeHighlighted)
          );
        }
        if (!_.isEmpty(response.appLayout)) {
          dispatch(setAppLayout(response.appLayout));
        }
        dispatch(getUserStatus({}, undefined));
        CFAnalytics.logEvent("city_updated", {
          cityName: city.name,
          isPopularCity: city.isPopular || false,
        });
      })
      .catch(ex => {
        if (__DEV__) {
          console.log(ex);
        }
      });
  };
}

// Function to initialize Firebase instance ID
export async function initializeFirebaseInstanceId() {
  try {
    const firebaseInstanceId = await getFirebaseInstanceId();
    if (firebaseInstanceId) {
      const sessionInfo = SessionInfo.getInstance();
      sessionInfo.firebaseInstanceId = firebaseInstanceId;
      console.log('Firebase Instance ID initialized:', firebaseInstanceId);
      return firebaseInstanceId;
    }
  } catch (error) {
    console.log('Error initializing Firebase Instance ID:', error);
  }
  return null;
}

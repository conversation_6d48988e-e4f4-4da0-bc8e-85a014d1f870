import firebase from "@react-native-firebase/app";
import SessionInfo from "../apis/SessionInfo";

// Test function to verify Firebase instance ID setup
export async function testFirebaseInstanceId() {
  try {
    // Test 1: Get Firebase instance ID directly
    const appInstanceId = await firebase.app().appInstanceId;
    console.log('Test 1 - Direct Firebase App Instance ID:', appInstanceId);
    
    // Test 2: Check if it's stored in SessionInfo
    const sessionInfo = SessionInfo.getInstance();
    console.log('Test 2 - Firebase Instance ID in SessionInfo:', sessionInfo.firebaseInstanceId);
    
    // Test 3: Set it in SessionInfo and verify
    if (appInstanceId) {
      sessionInfo.firebaseInstanceId = appInstanceId;
      console.log('Test 3 - Firebase Instance ID set in SessionInfo:', sessionInfo.firebaseInstanceId);
    }
    
    return {
      directAppInstanceId: appInstanceId,
      sessionInfoFirebaseInstanceId: sessionInfo.firebaseInstanceId,
      success: true
    };
  } catch (error) {
    console.log('Test Firebase Instance ID Error:', error);
    return {
      error: error.message,
      success: false
    };
  }
}

// Test function to verify API headers include Firebase instance ID
export function testApiHeaders() {
  try {
    const sessionInfo = SessionInfo.getInstance();
    const { getSessionHeaders } = require("../apis/ApiHeaders");
    const headers = getSessionHeaders();
    
    console.log('Test 4 - API Headers:', headers);
    console.log('Test 4 - Firebase Instance ID in headers:', headers.firebaseInstanceId);
    
    return {
      headers,
      firebaseInstanceIdInHeaders: headers.firebaseInstanceId,
      success: true
    };
  } catch (error) {
    console.log('Test API Headers Error:', error);
    return {
      error: error.message,
      success: false
    };
  }
}

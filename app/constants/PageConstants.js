export const CurePageTypes = {
  RootView: "root",
  AppTabs: "apptabs",
  TodayTab: "today",
  ExploreTab: "browse",
  MeTab: "account",
  LaunchScreen: "launchscreen",
  AfterLogin: "afterlogin",
  Onboarding: "onboarding",

  WebPage: "webview",
  InfoPage: "infopage",
  CarouselContentPage: "carouselpage",

  // SignUp
  SignUpView: "signupview",
  LoginView: "loginview",
  OTPVerifyView: "otpverifyview",

  // Feedback
  FeedBack: "feedback",

  // Checkout
  CheckoutPage: "checkout",
  CheckoutPageV1: "checkoutv1",
  PaymentPage: "payment",
  PaytmSignIn: "paytmsignin",
  PaytmVerifyOtp: "paytmverifyotp",
  PaytmWebView: "paytmwebview",
  MyOrders: "order",
  MyOrderDetail: "myorderdetail",
  SubscriptionCheckoutPage: "subscriptioncheckout",
  // Today
  TodayView: "todayview",
  Dashboard: "dashboard",
  StepsInfoPage: "stepdetails",
  SleepDetails: "sleepdetails",

  // Me
  AccountView: "accountview",
  ActivePacks: "activepacks",
  Policy: "policy",
  PolicyFromLogin: "policyfromlogin",
  AccountSettings: "accountsettings",
  Support: "support",
  PulseReportList: "pulsereportlist",
  ReportIssue: "reportissue",
  UpdatePhone: "updatephone",
  OTPVerifyUpdate: "otpverifyupdate",
  OrderConfirmation: "orderconfirmation",
  OrderConfirmationV1: "orderconfirmationv1",
  ConnectTVApp: "connecttvapp",

  // Address
  NeighbourhoodGate: "neighbourhoodgate",
  NotServing: "notserving",
  MarkerPage: "getmarkerresult",
  SelectAddress: "selectaddress",
  MapLocationSelection: "maplocationselection",
  AddAddress: "addaddress",
  SelectArea: "selectarea",
  SetDeliveryGate: "setgate",

  // Media
  VideoPlayer: "videoplayer",
  AudioPlayer: "audioplayer",

  CancelSubscriptionPage: "cancelsubscriptionpage",

  // Referral
  ReferralPage: "referfriends",
  ReferralTnCPage: "referraltncpage",

  // Permission
  GoogleFitPermission: "googlefitpermission",

  // Shareing
  ImageSharingPage: "imagesharingpage",

  IconFeedList: "iconfeedlist",
  CultIconList: "culticonlist",
  WellnessSearchPage: "wellnesssearchpage",
};

export const CultPageTypes = {
  HelpSelectCultCenter: "helpSelectCultCenter",
  SelectCultCenter: "selectcultcenter",
  DiyCultSingles: "diycultsingles",
  CultSingles: "cultsingles",
  CultPack: "cultpack",
  CultDIYPack: "cultdiypack",
  CultMindPack: "cultmindpack",
  CultBrowsePage: "cultbrowsepage",
  CultDIYBrowsePageV2: "diybrowsepagev2",
  TwelveFitBrowse: "twelvefitbrowse",
  CultOfferBrowse: "cultofferbrowse",
  Trainers: "trainers",
  CultClassBooking: "classbooking",
  MindClassBooking: "mindclassbooking",
  LiveClassBooking: "liveclassbooking",
  DIYSchedulePage: "diyschedule",
  AddSubUser: "addsubuser",
  ClassRecommendationGenerator: "classrecommendationgenerator",
  ClassRecommendation: "classrecommendation",
  NoCostEMIPage: "nocostemipage",
  SGTPreBookingPage: "sgtprebookingpage",
  FitternityIntermediatePage: "fitternityintermediate",
};

export const MindPageTypes = {
  SelectMindCenter: "selectmindcenter",
  MindDetail: "mindclass",
  MindSingles: "mindsingles",
  MindPack: "mindpack",
  MindBrowsePage: "mindbrowsepage",
  MindOfferBrowse: "mindofferbrowse",
  MindCenterPackBrowse: "mindcenterpackbrowse",
  SelectSpecialist: "selectspecialist",
  DoctorBooking: "doctorbooking",
  DoctorListing: "doctorlisting",
  MindTherapyCLP: "MindTherapy",
  MindTherapyCLPWeb: "therapy",
  TherapyCLPWeb: "therapyclp",
};

export const CarePageTypes = {
  VideoChat: "videochat",
  CarefitHCU: "carefithcu",
  CarefitTC: "carefittc",
  CarefitMP: "carefitmp",
  CareCartProduct: "carecartproduct",
  CareHcuCLP: "clphcu",
  CareConsultationCLP: "clpconsultation",
  CareSkinHairCLP: "clpskinhair",
  CareHealthPack: "clphealthpack",
  CarefitDiagnostics: "carefitdiagnostics",
  SelectCareDate: "selectcaredate",
  MultiConsultationSlots: "multiconsultationslots",
  CarePackDetail: "carepackdetail",
  SpecialistSelection: "specialistselection",
  CarefitPrescription: "carefitprescription",
  EndCall: "endcall",
  CareCheckoutPage: "carecheckout",
  LivePTFitnessReport: "liveptfitnessreport",
  DiagnosticsCheckoutPage: "diagnosticscheckout",
  CareOrderConfirmation: "careorderconfirmation",
  CareCartCheckout: "carecartcheckout",
  DiagnosticReportPage: "diagnostictestreport",
  TestsListingPage: "testslisting",
  DiagnosticCartListingPage: "diagnosticcartlisting",
  SelectCareDateV1: "selectcaredatev1",
  TwilioMessageChat: "twiliomessagechat",
  ChatHistory: "chathistory",
  CareCenterBrowse: "carecenterbrowse",
  CareDoctorBookingWebUrl: "doctor-booking",
  CareDoctorListingWebUrl: "speciality",
  CareDoctorListingWebUrlV2: "doctor-consultation",
  ConsultationListing: "consultations",
};

export const EatPageTypes = {
  EatFitCLPV2: "eatfitclp",
  OrderStatusPage: "cartdetails",
  FoodBrowsePage: "foodbrowsepage",
  CartCheckoutPage: "cartcheckout",
  MealPack: "foodpack",
  RecipeSingles: "recipesingles",
  MealSingles: "mealsingles",
  MealBrowsePage: "mealbrowsepage",
  MealChangePage: "changemeal",
  OrderTrackingPage: "ordertracking",
  CancelMealSubscriptionPage: "cancelmealsubscriptionpage",
  IngredientsPage: "ingredientspage",
  RecipeBrowsePage: "recipebrowsepage",
  RecipeStepsPage: "recipestepspage",
  ClpAddonModal: "addonmodal",
};
export const WholeFitPageTypes = {
  WholefitV2: "wholefitv2",
  WholeFitCLP: "wholefitclp",
  WholefitProductListingPage: "wholefitproductlp",
  WholefitBrandListingPage: "wholefitbrandlp",
  WholeFitBrowseModal: "wholefitbrowsemodal",
};

export const GoalPageTypes = {
  AddLogActivity: "addlogactivity",
  SearchActivity: "searchactivity",
  GoalSelectionPage: "goalselectionpage",
};
export const GearPageTypes = {
  GearHome: "gearhome",
  GearList: "gearlist",
  DetailsPage: "detailspage",
  GearCartCheckout: "gearcartcheckout",
  GearOrderDetails: "gearorder",
  ProductDetailsPage: "detailspage",
  ModifyGearOrder: "modifygearorder",
  GearOrderTracking: "gearordertracking",
  GearLandingPage: "cultsportlp",
  GearCultSportLandingPageSpecific: "cultsportlpspecific",
  // statically defining gear microapp screens
  GearCart: "gear-micro-cart",
  GearCanvas: "gear-micro-view",
  GearHomePage: "gear-micro-home",
  GearProductPage: "gear-micro-products",
  GearProductList: "gear-micro-collections",
};
export const LiveFitnessPageTypes = {
  LiveFitnessBrowsePage: "livefitnessbrowsepage",
  LiveMembershipPage: "livemembershippage",
  FindYourFirstClass: "findyourfirstclass",
  DIYBrowsePageV3: "diybrowsepagev3",
  MindDIYBrowsePageV3: "minddiybrowsepagev3",
  DIYSeriesBrowsePage: "diyseriesbrowsepage",
  LIVESearchPage: "livesearchpage",
  LiveSearchFiltersModal: "livesearchfiltersmodal",
  DIYSessionListingPage: "diysessionlistingpage",
  LiveTrainerPage: "trainerpage",
  LiveFormatPage: "formatpage",
  TrialCalibrationPage: "trialcalibrationpage",
  CoachCalibrationPage: "coachcalibrationpage",
  CoachExploreProgram: "coachexploreprogram",
};

export const IntlPageTypes = {
  NotServiceable: "notserviceable",
  TL_HOME: "tl-home",
  TL_TRAINERS: "tl-trainers",
  TL_WORKOUTS_TAB: "tl-workouts-tab",
  TL_WORKOUTS: "tl-workouts",
  TL_COLLECTIONS: "tl-collections",
  IntlOnboardingCompletion: "intl-onboarding-completion",
};

export const YearEndTypes = {
  YearEndQuiz: "yearendquiz",
};

export const Payment = {
  JuspayPaymentPage: "juspaypayment",
  JuspayManagementPage: "juspaymanagement",
};

export const CoachMarkTypes = {
  CoachMarkPage: "coachmarkpage",
};

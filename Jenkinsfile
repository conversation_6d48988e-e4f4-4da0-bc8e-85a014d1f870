pipeline {
    agent any

  tools {
        nodejs "Node 18"
        jdk 'JDK17'
    }
     options {
        ansiColor('xterm') // Enables ANSI color support
    }

    parameters {
        choice(
            name: 'PLATFORM_TYPE', 
            choices: ['ios', 'android'], 
            description: 'Select the platform'
        )
        choice(
            name: 'BUILD_TYPE', 
            choices: ['beta', 'publish'], 
            description: 'Select the build type'
        )
        string(name: 'APP_VERSION', defaultValue: '1.0', description: 'App Version')
        string(name: 'BRANCH_TO_BUILD', defaultValue: 'release', description: 'Branch to build')

    }
    environment {
        NPM_TOKEN = credentials('NPM_TOKEN')
        NODE_BINARY="which node"
        MATCH_PASSWORD = "apple"
  	    LANG = "en_US.UTF-8"
        LC_ALL = "en_US.UTF-8"
    	PATH = "$HOME/fvm/default/bin:$HOME/.pub-cache/bin:/usr/local/bin:$HOME/.rbenv/shims:$PATH"
    }

    stages {
    stage('Checkout Code') {
            steps {
                sh """
                git branch --list ${params.BRANCH_TO_BUILD} | grep -q ${params.BRANCH_TO_BUILD} && git branch -D ${params.BRANCH_TO_BUILD}
                git fetch origin ${params.BRANCH_TO_BUILD}:${params.BRANCH_TO_BUILD}
                git checkout ${params.BRANCH_TO_BUILD}
                git pull origin ${params.BRANCH_TO_BUILD}
                """
            }
        }
    stage('Check Java Version') {
        steps {
            sh 'java -version'
        }
    }
	stage('Load .zshrc') {
            steps {
                script {
                    sh '''
                        export ZSHRC_PATH="$HOME/.zshrc"
                        if [ -f "$ZSHRC_PATH" ]; then
                            source "$ZSHRC_PATH"
                        fi
                        echo "Zsh Config Loaded"
                    '''
                }
            }
        }
  	stage('Install cocoapods') {
         when {
                expression { params.PLATFORM_TYPE == 'ios' }
            }
            steps {
                sh '''
                    pod setup
                '''
            }
        }
        stage('Install Flutter via FVM') {
            steps {
                sh '''
                # Install a specific Flutter version (e.g., stable)
                # Verify Flutter installation
                fvm use 3.22.3
                flutter --version
                '''
            }
        }
	stage('Configure yarn') {
            steps {
                sh '''
                echo "//npm.pkg.github.com/:_authToken=$NPM_TOKEN" >> .npmrc
		        echo "@curefit:registry=https://npm.pkg.github.com/" >> .npmrc
		        echo "registry=https://registry.npmjs.org/" >> .npmrc
		        npm install -g yarn
		        yarn install
                '''
            }
        }
stage('Run fastlane iOS Beta') {
    when {
        expression { params.PLATFORM_TYPE == 'ios' && params.BUILD_TYPE == 'beta' }
    }
    steps {
        sh '''
            cd ios 
            gem install bundler
            bundle install
            pod install
            rm -rf .xcode.env .xcode.env.local
            bundle exec fastlane beta --verbose
        '''
    }
}

stage('Run fastlane iOS Publish') {
    when {
        expression { params.PLATFORM_TYPE == 'ios' && params.BUILD_TYPE == 'publish' }
    }
    steps {
        withCredentials([file(credentialsId: 'APP_STORE_CONNECT_API_KEY', variable: 'APP_STORE_CONNECT_API_KEY')]) {
            sh '''
                cd ios 
                gem install bundler
                bundle install
                pod install
                rm -rf .xcode.env .xcode.env.local
                bundle exec fastlane app_store api_key_path:$APP_STORE_CONNECT_API_KEY --verbose
            '''
        }
    }
}

stage('Run fastlane Android Publish') {
    when {
        expression { params.PLATFORM_TYPE == 'android' && params.BUILD_TYPE == 'publish' }
    }
    steps {
        withCredentials([file(credentialsId: 'GOOGLE_PLAY_API_KEY', variable: 'GOOGLE_PLAY_API_KEY')]) {
            sh '''
                cd android 
                gem install bundler
                bundle install
                bundle exec fastlane play_store api_key_path:$GOOGLE_PLAY_API_KEY --verbose
            '''
        }
    }
}

stage('Run fastlane android') {
    when {
          expression { params.PLATFORM_TYPE == 'android'  && params.BUILD_TYPE == 'beta' }
            }
            steps {
                sh '''
                    cd android 
                    gem install bundler
                    bundle install
                    bundle exec fastlane beta --verbose
                '''
            }
}
    }
}
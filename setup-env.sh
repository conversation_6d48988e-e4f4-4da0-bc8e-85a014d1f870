#!/bin/bash

echo "🔧 Setting up React Native development environment..."

# Set up environment variables
export JAVA_HOME="/opt/homebrew/opt/openjdk@17"
export PATH="/opt/homebrew/opt/openjdk@17/bin:$PATH"
export PATH="/opt/homebrew/opt/node@18/bin:$PATH"

# Android SDK setup
export ANDROID_HOME=$HOME/Library/Android/sdk
export PATH=$PATH:$ANDROID_HOME/emulator
export PATH=$PATH:$ANDROID_HOME/platform-tools
export PATH=$PATH:$ANDROID_HOME/cmdline-tools/latest/bin

echo "Environment variables set:"
echo "JAVA_HOME: $JAVA_HOME"
echo "Node version: $(node --version)"
echo "Java version: $(java -version 2>&1 | head -n 1)"
echo ""

# Clean previous builds
echo "🧹 Cleaning previous builds..."
rm -rf android/app/build
rm -rf node_modules

echo "📦 Installing dependencies..."
yarn install

echo "✅ Setup complete!"
echo ""
echo "Next steps:"
echo "1. Open Android Studio"
echo "2. Open the 'android' folder from this project"
echo "3. Wait for <PERSON><PERSON><PERSON> sync to complete"
echo "4. Run: ./run-android.sh"

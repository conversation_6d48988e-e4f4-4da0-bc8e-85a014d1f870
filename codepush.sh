export $(cat ios/CureFit.xcconfig)

echo "Enter iOS target binary version (Enter to skip)";
read iosVersion;
echo "Enter android target binary version (Enter to skip)";
read androidVersion;
androidProject="curefit/cure.fit-android-debug";
iosProject="curefit/cure.fit-ios-debug";

echo "Delete modules? (n/y)";
read deleteModules;

if [[ $deleteModules = "y" ]]
  then
  echo "Deleting node_modules, yarn install";
  watchman watch-del-all && rm -rf node_modules && yarn install
  echo "node_modules installed"
fi

sed -i '' 's/ALWAYS_STAGE\ =\ false/ALWAYS_STAGE = true/1' app/lib/ConfigUtil.ts

if [[ $androidVersion = "" ]] && [[ $iosVersion = "" ]];
    then echo "no platform selected";
  else
    if [[ $androidVersion != "" ]];
      then

        echo "********Preparing to code push android to "$androidProject" v"$androidVersion" segment "$pushSegment"**************"
        rm -rf android/app/build
        appcenter codepush release-react -a $androidProject -d Production -t $androidVersion -m -o android/app/build/ -b index.android.bundle -s android/app/build/index.android.bundle.map

        echo "Fetching last codepush version"
        cpVersion=$(appcenter codepush deployment history -a $androidProject Production | tail -2 | grep -G [v][0-9]* -o | cut -c 2-5)
        echo $cpVersion;
        codeBundleId="${androidVersion}.${cpVersion}";
        echo $codeBundleId;
      
    fi
    if [[ $iosVersion != "" ]];
      then
        echo "*********Preparing to code push iOS to "$iosProject" v"$iosVersion" segment "$pushSegment"**************"
        rm -rf ios/build
        appcenter codepush release-react -a $iosProject -d Production -t $iosVersion -m -o ios/build/ -b main.jsbundle -s ios/build/main.jsbundle.map

        echo "Fetching last codepush version"
        cpVersion=$(appcenter codepush deployment history -a $iosProject Production | tail -2 | grep -G [v][0-9]* -o | cut -c 2-5)
        echo $cpVersion;
        codeBundleId="${iosVersion}.${cpVersion}";
        echo $codeBundleId;
    fi
fi

  echo "Codepush completed"

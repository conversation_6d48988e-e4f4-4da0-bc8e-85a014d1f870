module.exports = {
  preset: "react-native",
  setupFilesAfterEnv: ["@testing-library/jest-native/extend-expect"],
  moduleNameMapper: {
    "^.+\\.(css|scss|less|sass)$": "<rootDir>/__mocks__/styleMock.js",
    "\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$":
      "<rootDir>/__mocks__/fileMock.js",
    "react-native-device-info":
      "react-native-device-info/jest/react-native-device-info-mock",
    "react-native-localize": "<rootDir>/__mocks__/react-native-localize.js",
  },
};

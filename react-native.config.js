module.exports = {
  dependencies: {
    'react-native-iap': {
      platforms: {
        android: null,
      },
    },
    "react-native-custom-tabs": {
      platforms: {
        ios: null,
      },
    },
    "react-native-vector-icons": {
      platforms: {
        android: null, // we don't need its native methods
        ios: null, // prevent unused font files bundling
      },
    },
    "@curefit/react-native-pose-estimation": {
      platforms: {
        android: null,
        ios: null,
      },
    },
    "@curefit/react-native-video": {
      platforms: {
        android: {
          sourceDir:
            "../node_modules/@curefit/react-native-video/android-exoplayer",
        },
      },
    },
    "react-native-geolocation-service": {
      platforms: {
        android: null,
      },
    },
  },
};

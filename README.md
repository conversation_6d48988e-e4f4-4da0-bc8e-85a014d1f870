# cult.fit Mobile App

iOS and Android mobile app 

### Setup

- Install [Homebrew](https://brew.sh)
- Xcode from Mac App Store
- Android Studio: `brew install android-studio`
- CocoaPods: `brew install cocoapods node watchman yarn`
- Install FVM: `brew tap leoafarias/fvm && brew install fvm`
- Use FVM: `echo 'export PATH="~/fvm/default/bin:$PATH"' >> ~/.zshrc`
- Flutter: `fvm install 3.22.3 && fvm global 3.22.3`
- Configure `npm` with [these steps](#github-packages)
- Run `yarn` to install dependencies

### Run

- Packager: `react-native start`
- On iOS: `react-native run-ios`
- On Android: `react-native run-android`

### Debug

Run `brew doctor` and `yarn doctor` to verify dev environment. Follow the docs for [Debugging](https://reactnative.dev/docs/debugging) and [Running on Device](https://reactnative.dev/docs/running-on-device). If issues occur, check the [Environment Setup](https://reactnative.dev/docs/environment-setup) docs.

<details>
  <summary>How to debug on iOS</summary>
  
- Open `Curefit.workspace` in Xcode IDE
- Select iPhone Simulator and click Run (play) button on top-left corner
- To clean and build in Xcode, the shortcut is `option + shift + cmd + k`
- Start the react-native packager using `react-native start --reset-cache`
- Once Simulator starts, focus the simulator window and open react-native menu using `cmd + d`
- Click `Debug` option to open the Chrome debugger
- In Chrome, right click > Inspect and debugger window with console, source, network etc. will open
</details>

<details>
  <summary>How to debug on Android</summary>
  
- Open Terminal and go to `cd android`
- Run `./gradlew clean && ./gradlew assembleDebug`
- Connect your android device, to port forward the device `adb reverse tcp:8081 tcp:8081` (shortcut: `yarn tunnel`)
- Start the react-native packager using `react-native start --reset-cache`
- Install the build apk from `adb install <workspace>/curefit-mobile/android/app/build/outputs/apk/debug/cure.fit-x.xx-debug.apk`
- Once app installed on the phone, launch the app and shake the phone to open the react-native debug menu
- Click `Debug` option to open the Chrome debugger
- In Chrome, right click > Inspect and debugger window with console, source, network etc. will open
</details>

<details>
  <summary>How to debug with Flipper</summary>
  
- [Flipper](https://fbflipper.com) for debugging: `brew --cask install flipper`
- [Layout Inspector](https://fbflipper.com/docs/setup/layout-plugin)
- [Network](https://fbflipper.com/docs/setup/network-plugin)
- [Redux](https://github.com/jk-gan/flipper-plugin-redux-debugger)
</details>
  
<details>
  <summary>How to reset environment</summary>
  
- `rm -rf android/app/build` - clear android build cache
- `rm -rf ios/Pods` - clear iOS CocoaPods deps
- `rm -rf ~/Library/Developer/Xcode/DerivedData` - to clear iOS build cache
- `rm -rf ~/Library/Developer/CoreSimulator/` - to clear iOS Simulator cache
- `rm -rf node_modules` - clear npm js deps
- `pod deintegrate` - remove CocoaPods from project
- `yarn --force --check-files` - force install js deps
- `cd ios && pod install --repo-update` - rerun CocoaPods
- `react-native start --reset-cache` - start packager after resetting cache
</details>

### Testing

- [Master Test Plan](https://docs.google.com/spreadsheets/d/17xHy46zjXa3WPIGfOTU7PRtNTcWlz3XJ_vlBiTALkjY/edit)
- [Regression Tests](https://github.com/curefit/curefit-mobile/blob/release/tests/README.md)
- [Unit Test Example](https://github.com/curefit/curefit-mobile/blob/master/app/components/VideoPlayer/Gamification/__tests__/ScoreText.test.tsx)
- [GitHub Wiki](https://github.com/curefit/curefit-mobile/wiki)

### Tools

- [Analytics Inspector](https://github.com/curefit/curefit-mobile/wiki/Analytics-Inspector)
- [Bitrise](https://app.bitrise.io/app/7351d298a609e549)
- [Crashlytics](https://console.firebase.google.com/project/cult-155917/crashlytics/app/android:fit.cure.android/issues)
- [Live Class Debugger](https://cf-debugger.web.app/)
- [Sentry](https://sentry.io/organizations/curefit/issues/)
- [Bugsnag](https://app.bugsnag.com/organizations/curefit-healthcare-private-ltd/stability-center)

### GitHub Packages

- Go here: https://github.com/settings/tokens/new
- Create token with permissions `repo`, `read:packages`, `write:packages`
- Modify `~/.npmrc` by replacing XXXX with your token:

```
//npm.pkg.github.com/:_authToken=XXXX
@curefit:registry=https://npm.pkg.github.com/
registry=https://registry.npmjs.org/
```

### Play Feature Delivery

Google Play’s app serving model uses Android App Bundles to generate and serve optimized APKs for each user’s device configuration, so users download only the code and resources they need to run your app. [Play Feature Delivery](https://developer.android.com/guide/app-bundle/play-feature-delivery#blogs) uses advanced capabilities of app bundles, allowing certain features of your app to be delivered conditionally or downloaded on demand.

<details>
  <summary>How to convert AAB to APKS</summary>
  
- Install bundletool: `brew install bundletool`
- Create single app that has dynamic modules for debugging: `bundletool build-apks --overwrite --mode=universal --bundle=./android/app/build/intermediates/intermediary_bundle/debug/cure.fit-8.14-debug.aab --output=my_app.apks`
- Install the app on device: `bundletool install-apks --apks=my_app.apks`
</details>

<details>
  <summary>How to convert APKS to individual split APKs</summary>
  
- Create bundles: `bundletool build-apks --bundle=./android/app/build/intermediates/intermediary_bundle/debug/cure.fit-8.14-debug.aab --output=my_app.apks`
- Unzip the apks: `unzip my_app.apks -d apks`
- Install selected apks from the bundle: `cd apks/splits/; adb install-multiple base-master.apk base-arm64_v8a.apk base-hdpi.apk poseEstimation-master.apk poseEstimation-arm64_v8a.apk poseEstimation-hdpi.apk`
</details>

<br>

### Setting up flutter-packages repository for local development 
See [here](https://github.com/curefit/flutter-packages/blob/master/README.md#setting-up-flutter-packages-with-your-parent-repository)


### Generating method channel through Piegon
- cd curefit_x
- dart run pigeon --input lib/message.dart  
